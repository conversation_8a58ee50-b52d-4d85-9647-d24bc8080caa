	apply plugin: 'java'
	apply plugin: 'eclipse'
	
	sourceCompatibility=1.8
	group = 'kd.bos.service'
	version = '1.0'
	
	// 获取当前日期和时间并格式化
	def date = new Date()
	def formattedDateTime = new java.text.SimpleDateFormat("yyyyMMddHHmm").format(date)
		tasks.withType(JavaCompile) {  
		options.encoding = "UTF-8" 
	} 
	
	def trd= '../../../mservice-cosmic/lib/trd'
	def bos= '../../../mservice-cosmic/lib/bos'
	def biz= '../../../mservice-cosmic/lib/biz'
	def cus= '../../../mservice-cosmic/lib/cus'
	// C:\nextcloudlib\node-debug-mservice\bin
	
	//def biz = 'F:/debug/bos-dev-env/mservice-cosmic/lib/biz'
	//def trd = 'F:/debug/bos-dev-env/mservice-cosmic/lib/trd'
	//def bos = 'F:/debug/bos-dev-env/mservice-cosmic/lib/bos'
	
	dependencies {
		compile fileTree(dir: bos, include: '*.jar')
		compile fileTree(dir: trd, include: '*.jar')
		compile fileTree(dir: biz, include: '*.jar')
		compile fileTree(dir: cus, include: '*.jar')
	}

	jar {
	    // 设置 JAR 包的文件名
	    archiveName = "${project.name}-${formattedDateTime}.jar"
		// 清除默认包含的内容
		includeEmptyDirs = false
		// 显式指定要包含的编译输出
		from(sourceSets.main.output) {
			include 'kd/bos/tcbj/**'
		}
		// 确保不包含其他任何内容，除非明确指定
		eachFile { file ->
			if (!file.path.startsWith('kd/bos/tcbj/')) {
				file.exclude()
			}
		}

    	duplicatesStrategy = DuplicatesStrategy.EXCLUDE
	}
	
	task sourcesJar(type: Jar, dependsOn: build) {
	    classifier = 'sources'
	    from sourceSets.main.allSource
	 }
	 
		
	 task copyJarTodir(type: Copy){
		    from 'build/libs'
			into '/home/<USER>/bos'
			exclude '**/*.class'
	 }
	test.ignoreFailures true
	
