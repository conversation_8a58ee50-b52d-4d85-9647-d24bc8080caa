<?xml version="1.0" encoding="UTF-8"?>

<DeployScript>
  <Scripts>
    <KScript>
      <id>w37fqrIB</id>
      <number>orgcuspricebillop</number>
      <cmb_ftype></cmb_ftype>
      <scripttype>4</scripttype>
      <context><![CDATA[// 正确引入苍穹脚本环境中的类
var QFilter = kd.bos.orm.query.QFilter;
var QCP = kd.bos.orm.query.QCP;
var OperateOption = kd.bos.dataentity.OperateOption;
var KDException = kd.bos.exception.KDException;
var BusinessDataServiceHelper = kd.bos.servicehelper.BusinessDataServiceHelper;
var OperationServiceHelper = kd.bos.servicehelper.operation.OperationServiceHelper;
var BillTypeHelper = kd.bos.tcbj.im.helper.BillTypeHelper;

var plugin = new OperationPlugin({
    // 保存操作
    SAVE_OP: "save",
    endOperationTransaction: function(e) {
        var key = e.getOperationKey();
        var bills = e.getDataEntities();
        if (this.SAVE_OP === key) {
            for (var i = 0; i < bills.length; i++) {
                var bill = bills[i];
                var id = bill.getLong("id");
                var pk = String(id);
                var qFilter1 = new QFilter("id", QCP.equals, pk);
                var billNo = BusinessDataServiceHelper.loadSingle("yd_orgcuspricebill", "id,billno", qFilter1.toArray()).get("billno");
                var submitResult = OperationServiceHelper.executeOperate("submit", "yd_orgcuspricebill", [pk], OperateOption.create());
                if (!submitResult.isSuccess()) {
                    throw new KDException(billNo + "提交失败，请手动提交，查看提交失败原因：" + submitResult.getMessage());
                } else {
                    var auditResult = OperationServiceHelper.executeOperate("audit", "yd_orgcuspricebill", [pk], OperateOption.create());
                    if (!auditResult.isSuccess()) {
                        throw new KDException(billNo + "提交成功，但审核失败，请手动审核，查看审核失败原因：" + auditResult.getMessage());
                    }
                }
            }
        }
    }
});]]></context>
      <bizappid>2/6622RVX+25</bizappid>
      <bizunitid>/KPQHMXQD66W</bizunitid>
      <scriptname>经销商供货价格表操作插件</scriptname>
      <creater_id>1966784705198060544</creater_id>
      <createdate>1742802098000</createdate>
      <modifydate>1742806774000</modifydate>
      <modifier_id>1966784705198060544</modifier_id>
      <isv>yd</isv>
      <classname>yd.scmc.yd_im_ext.setup.orgcuspricebillop</classname>
    </KScript>
  </Scripts>
  <BOSVersion>1.0</BOSVersion>
</DeployScript>
