<?xml version="1.0" encoding="UTF-8"?>

<DeployScript>
  <Scripts>
    <KScript>
      <id>9j4KVWsT</id>
      <number>testsaleout</number>
      <cmb_ftype></cmb_ftype>
      <scripttype>2</scripttype>
      <context><![CDATA[/**
* <AUTHOR>
* @date 2023-01-09
*/
var plugin = new BillPlugin({
	afterBindData : function(e){
	
	},
	afterCreateNewData : function(e){
	
	},
	click : function(e){
	
	},
	closedCallBack : function(e){
	
	},
	confirmCallBack : function(e){
	
	},
	itemClick : function(e){
	
	},
	registerListener : function(e){
	
	}
});]]></context>
      <bizappid>2/6622RVX+25</bizappid>
      <bizunitid>=Y70GHKBPJV</bizunitid>
      <scriptname>测试</scriptname>
      <creater_id>1338628323399434240</creater_id>
      <createdate>1673236547000</createdate>
      <modifydate>1673238130000</modifydate>
      <modifier_id>1338628323399434240</modifier_id>
      <isv>yd</isv>
      <classname>yd.scmc.yd_im_ext.outbill.testsaleout</classname>
    </KScript>
  </Scripts>
  <BOSVersion>1.0</BOSVersion>
</DeployScript>
