<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>1X53V9O8G9=A</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>1X53V9O8G9=A</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1666671216000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>1X53V9O8G9=A</Id>
          <Key>yd_ckdygx</Key>
          <EntityId>1X53V9O8G9=A</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>仓库对应关系</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>1X53V9O8G9=A</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>1X53V9O8G9=A</Id>
              <Name>仓库对应关系</Name>
              <Key>yd_ckdygx</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>仓库对应关系列表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Name>仓库对应关系列表</Name>
                    </FormAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>1X53V9O8G9=A</EntityId>
                    </BillListAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1Y0SL5W9LJVB</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>2</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_combofield_pt</ListFieldId>
                      <Name>平台</Name>
                    </ComboListColumnAp>
                    <ComboListColumnAp action="edit" oid="f87b7292000031ac">
                      <Index>3</Index>
                    </ComboListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1XSZASF9/3SD</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_textfield</ListFieldId>
                      <Name>业务平台对应仓库</Name>
                      <Width>300px</Width>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1XSZB+SD81EQ</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_basedatafield.number</ListFieldId>
                      <Name>苍穹（EAS)仓库.编码</Name>
                      <Width>300px</Width>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1XSZBG6BF7N+</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_basedatafield.name</ListFieldId>
                      <Name>苍穹（EAS)仓库.名称</Name>
                      <Width>300px</Width>
                    </ListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <EntryAp>
              <EntryId>qDwBtsCNLn</EntryId>
              <Id>qDwBtsCNLn</Id>
              <Name>单据体</Name>
              <PageType/>
              <Key>yd_entryentity</Key>
              <ParentId>4HVRka2lun</ParentId>
            </EntryAp>
            <AdvConSummaryPanelAp>
              <Id>oxz0h86Gon</Id>
              <Name>高级面板摘要容器</Name>
              <Key>yd_yd_yd_advconsummarypanelap</Key>
              <ParentId>9mdSz42Hab</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>Fc01wHHqk4</Id>
              <Key>yd_yd_yd_advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>9mdSz42Hab</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>4HVRka2lun</Id>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>yd_yd_yd_advconchildpanelap</Key>
              <ParentId>9mdSz42Hab</ParentId>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>KfoHg4j7Di</Id>
              <Key>yd_yd_yd_advconbaritemap_xz</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>Fc01wHHqk4</ParentId>
              <Name>新增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>vNtvUQyAyJ</Id>
              <Key>yd_yd_yd_advconbaritemap_sc</Key>
              <OperationStyle>0</OperationStyle>
              <Index>1</Index>
              <ParentId>Fc01wHHqk4</ParentId>
              <Name>删除行</Name>
            </AdvConBarItemAp>
            <FieldAp>
              <Id>Y6Dg0ViL2D</Id>
              <FieldId>Y6Dg0ViL2D</FieldId>
              <Index>4</Index>
              <Name>平台</Name>
              <Key>yd_combofield_pt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>I314HAbQ2B</Id>
              <FieldId>I314HAbQ2B</FieldId>
              <Name>业务平台对应仓库</Name>
              <Key>yd_textfield</Key>
              <ParentId>qDwBtsCNLn</ParentId>
              <Width>600px</Width>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>kPXGOUKYtC</Id>
              <FieldId>kPXGOUKYtC</FieldId>
              <Index>1</Index>
              <Name>苍穹（EAS)仓库</Name>
              <Key>yd_basedatafield</Key>
              <ParentId>qDwBtsCNLn</ParentId>
              <Width>600px</Width>
            </EntryFieldAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>9mdSz42Hab</Id>
              <Key>yd_advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>3</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <Name>高级面板</Name>
            </AdvConAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338628323399434240</ModifierId>
      <EntityId>1X53V9O8G9=A</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1666671215945</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ckdygx</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>1X53V9O8G9=A</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1666671216000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>1X53V9O8G9=A</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>仓库对应关系</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_ckdygx</TableName>
              <Operations>
                <Operation action="edit" oid="c91d5125000033ac">
                  <Parameter>
                    <SaveParameter>
                      <StatusFieldId>6q69iznvHX</StatusFieldId>
                    </SaveParameter>
                  </Parameter>
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Description>对应仓库不能重复</Description>
                      <Id>1X9+QGCLOYQR</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_combofield_pt</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_textfield</Id>
                        </FieldId>
                      </Fields>
                      <Enabled>true</Enabled>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation action="edit" oid="c91d5125000034ac">
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Description>平台仓库不允许重复</Description>
                      <Id>26NX/LU3/73H</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_combofield_pt</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_textfield</Id>
                        </FieldId>
                      </Fields>
                      <Enabled>true</Enabled>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <NewEntryParameter>
                      <EntryId>qDwBtsCNLn</EntryId>
                    </NewEntryParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>1X5RUDT38BCS</Id>
                  <SuccessMsg/>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>qDwBtsCNLn</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>1X5RV5YHI9C3</Id>
                  <SuccessMsg/>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>1X53V9O8G9=A</Id>
              <Key>yd_ckdygx</Key>
              <Name>仓库对应关系</Name>
            </BillEntity>
            <ComboField>
              <FieldName>fk_yd_combofield_pt</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Y6Dg0ViL2D</Id>
              <Key>yd_combofield_pt</Key>
              <MustInput>true</MustInput>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>万里牛</Caption>
                  <Value>4</Value>
                  <Seq>3</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <EntryEntity>
              <TableName>tk_yd_entryentitydyck</TableName>
              <Id>qDwBtsCNLn</Id>
              <Key>yd_entryentity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kPXGOUKYtC</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>苍穹（EAS)仓库</Name>
              <FieldName>fk_yd_basedatafield</FieldName>
              <Key>yd_basedatafield</Key>
              <MustInput>true</MustInput>
              <DisplayProp>number</DisplayProp>
              <RefLayout/>
              <ParentId>qDwBtsCNLn</ParentId>
              <BaseEntityId>14G1K2YGTJGQ</BaseEntityId>
            </BasedataField>
            <TextField>
              <FieldName>fk_yd_textfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>I314HAbQ2B</Id>
              <Key>yd_textfield</Key>
              <MustInput>true</MustInput>
              <ParentId>qDwBtsCNLn</ParentId>
              <Name>业务平台对应仓库</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv></Isv>
      <Version>1666671215986</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ckdygx</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>1X53V9O8G9=A</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1666671215945</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
