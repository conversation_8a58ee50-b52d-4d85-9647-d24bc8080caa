<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>1X53V9O8G9=A</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4YA/OOPEJZ2P</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>2</DevType>
      <ModifyDate>1750055860000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <DevType>2</DevType>
          <Id>4YA/OOPEJZ2P</Id>
          <Key>yd_ckdygx_ext_ec</Key>
          <EntityId>4YA/OOPEJZ2P</EntityId>
          <ParentId>1X53V9O8G9=A</ParentId>
          <MasterId>1X53V9O8G9=A</MasterId>
          <InheritPath>00305e8b000006ac,1X53V9O8G9=A</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4YA/OOPEJZ2P</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>4YA/OOPEJZ2P</Id>
              <Key>yd_ckdygx_ext_ec</Key>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4YA/OOPEJZ2P</EntityId>
                    </BillListAp>
                    <ComboListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YA1S5N6QDJ+</Id>
                      <Key>yd_combolistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_basedatafield.status</ListFieldId>
                      <Name>仓库数据状态</Name>
                    </ComboListColumnAp>
                    <ComboListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YA1S7ZD+51O</Id>
                      <Key>yd_combolistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>8</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_basedatafield.enable</ListFieldId>
                      <Name>仓库使用状态</Name>
                    </ComboListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <EntryFieldAp>
              <Id>h9DowWcCk3</Id>
              <FieldId>h9DowWcCk3</FieldId>
              <Index>2</Index>
              <Name>仓库数据状态</Name>
              <Key>yd_warehousestatus</Key>
              <ParentId>qDwBtsCNLn</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>9EdjveTvt6</Id>
              <FieldId>9EdjveTvt6</FieldId>
              <Index>3</Index>
              <Name>仓库使用状态</Name>
              <Key>yd_warehousestatenable</Key>
              <ParentId>qDwBtsCNLn</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>FEMEd8p5Cx</Id>
              <FieldId>FEMEd8p5Cx</FieldId>
              <Index>4</Index>
              <Name>备注</Name>
              <Key>yd_remark</Key>
              <ParentId>qDwBtsCNLn</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>2218211534222423040</ModifierId>
      <EntityId>4YA/OOPEJZ2P</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750055859627</Version>
      <ParentId>1X53V9O8G9=A</ParentId>
      <MasterId>1X53V9O8G9=A</MasterId>
      <Number>yd_ckdygx_ext_ec</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4YA/OOPEJZ2P</Id>
      <InheritPath>00305e8b000006ac,1X53V9O8G9=A</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>2</DevType>
      <ModifyDate>1750055860000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <DevType>2</DevType>
          <Isv>yd</Isv>
          <Id>4YA/OOPEJZ2P</Id>
          <ParentId>1X53V9O8G9=A</ParentId>
          <MasterId>1X53V9O8G9=A</MasterId>
          <InheritPath>00305e8b000006ac,1X53V9O8G9=A</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Id>4YA/OOPEJZ2P</Id>
              <Key>yd_ckdygx_ext_ec</Key>
            </BillEntity>
            <ComboField action="edit" oid="Y6Dg0ViL2D">
              <Items>
                <ComboItem>
                  <Caption>新E3</Caption>
                  <Value>5</Value>
                  <Seq>4</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <BasedataField action="edit" oid="kPXGOUKYtC">
              <RefProps>
                <RefProp>
                  <Name>status</Name>
                </RefProp>
                <RefProp>
                  <Name>enable</Name>
                </RefProp>
              </RefProps>
            </BasedataField>
            <TextField>
              <FieldName>fk_yd_remark</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>FEMEd8p5Cx</Id>
              <Key>yd_remark</Key>
              <ParentId>qDwBtsCNLn</ParentId>
              <Name>备注</Name>
            </TextField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>h9DowWcCk3</Id>
              <Key>yd_warehousestatus</Key>
              <RefDisplayProp>status</RefDisplayProp>
              <ParentId>qDwBtsCNLn</ParentId>
              <RefBaseFieldId>kPXGOUKYtC</RefBaseFieldId>
              <Name>仓库数据状态</Name>
            </BasedataPropField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>9EdjveTvt6</Id>
              <Key>yd_warehousestatenable</Key>
              <RefDisplayProp>enable</RefDisplayProp>
              <ParentId>qDwBtsCNLn</ParentId>
              <RefBaseFieldId>kPXGOUKYtC</RefBaseFieldId>
              <Name>仓库使用状态</Name>
            </BasedataPropField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750055859654</Version>
      <ParentId>1X53V9O8G9=A</ParentId>
      <MasterId>1X53V9O8G9=A</MasterId>
      <Number>yd_ckdygx_ext_ec</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4YA/OOPEJZ2P</Id>
      <InheritPath>00305e8b000006ac,1X53V9O8G9=A</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1750055859627</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
