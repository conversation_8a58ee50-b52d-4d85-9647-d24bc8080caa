<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>3EXN3/TKBDUV</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>3EXN3/TKBDUV</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1689841084000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <DevType>0</DevType>
          <Isv>yd</Isv>
          <Id>3EXN3/TKBDUV</Id>
          <Key>yd_customersign</Key>
          <EntityId>3EXN3/TKBDUV</EntityId>
          <ModelType>BaseFormModel</ModelType>
          <ParentId/>
          <MasterId/>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>客户公司标签</Name>
          <SrcBizObjId/>
          <InheritPath/>
          <Items>
            <BasedataFormAp>
              <Language>zh_CN</Language>
              <ShowType>MainNewTabPage</ShowType>
              <Id>3EXN3/TKBDUV</Id>
              <LockStyle>2</LockStyle>
              <Name>客户公司标签</Name>
              <PkId>3EXN3/T36Z1N</PkId>
              <Key>yd_customersign</Key>
              <ListMeta>
                <FormMetadata>
                  <Id>3EXOA42CRWH6</Id>
                  <Key>subformdesigner</Key>
                  <ModelType>BillListCardViewModel</ModelType>
                  <ParentId/>
                  <Name>客户公司标签列表</Name>
                  <SrcBizObjId/>
                  <InheritPath/>
                  <Items>
                    <ListFormAp>
                      <Language>zh_CN</Language>
                      <ShowType>MainNewTabPage</ShowType>
                      <Id>3EXOA42CRWH6</Id>
                      <AlignItems>stretch</AlignItems>
                      <LockStyle>2</LockStyle>
                      <Name>客户公司标签列表</Name>
                      <PkId>3EXOA43DNFLU</PkId>
                      <Key>subformdesigner</Key>
                      <Direction>column</Direction>
                    </ListFormAp>
                    <FilterContainerAp>
                      <Id>_FilterContainer_</Id>
                      <Key>_filtercontainer_</Key>
                      <Grow>0</Grow>
                      <Name>客户公司标签列表</Name>
                    </FilterContainerAp>
                    <ToolbarAp>
                      <Id>_toolbar_</Id>
                      <Key>_toolbar_</Key>
                      <Name>列表工具栏</Name>
                    </ToolbarAp>
                    <BillListAp>
                      <Id>_BillList_</Id>
                      <Name>客户公司标签列表</Name>
                      <Key>_billlist_</Key>
                      <EntityId>3EXN3/TKBDUV</EntityId>
                      <QueryType>1</QueryType>
                    </BillListAp>
                    <ListGridViewAp>
                      <Id>gridview</Id>
                      <Name>表格视图</Name>
                      <PageType/>
                      <Key>gridview</Key>
                      <ParentId>_BillList_</ParentId>
                    </ListGridViewAp>
                    <ListCardViewAp>
                      <AlignContent>flex-start</AlignContent>
                      <LayoutStyle>2</LayoutStyle>
                      <Id>cardview</Id>
                      <Name>卡片视图</Name>
                      <PageType/>
                      <Key>cardview</Key>
                      <ParentId>_BillList_</ParentId>
                    </ListCardViewAp>
                    <FastSearchGridViewAp>
                      <Id>fastsearchgridview</Id>
                      <Key>fastsearchgridview</Key>
                      <ParentId>_FilterContainer_</ParentId>
                      <Name>快捷搜索视图</Name>
                    </FastSearchGridViewAp>
                    <FilterGridViewAp>
                      <NewFilter>true</NewFilter>
                      <Id>filtergridview</Id>
                      <Key>filtergridview</Key>
                      <ParentId>_FilterContainer_</ParentId>
                      <Name>常用过滤视图</Name>
                    </FilterGridViewAp>
                    <SchemeFilterViewAp>
                      <Id>schemefilterview</Id>
                      <Key>schemefilterview</Key>
                      <ParentId>_FilterContainer_</ParentId>
                      <Name>方案过滤视图</Name>
                    </SchemeFilterViewAp>
                    <BarItemAp>
                      <OperationKey>new</OperationKey>
                      <Id>QHjumoqUMS</Id>
                      <Key>yd_baritemap</Key>
                      <OperationStyle>0</OperationStyle>
                      <ParentId>_toolbar_</ParentId>
                      <Name>新增</Name>
                    </BarItemAp>
                    <BarItemAp>
                      <OperationKey>delete</OperationKey>
                      <Id>ezsJfapxFd</Id>
                      <Key>yd_baritemap1</Key>
                      <OperationStyle>0</OperationStyle>
                      <Index>1</Index>
                      <ParentId>_toolbar_</ParentId>
                      <Name>删除</Name>
                    </BarItemAp>
                    <CardRowPanelAp>
                      <Id>3EXOA42GDOHB</Id>
                      <Name>卡片</Name>
                      <Key>cardviewrow</Key>
                      <ParentId>cardview</ParentId>
                    </CardRowPanelAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>EntitySeq</SeqColumnType>
                      <Id>3EXOAI48=+7R</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>fseq</ListFieldId>
                      <Name>序号</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3EXOBGM+QA0O</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>1</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_saleorg.name</ListFieldId>
                      <Name>销售组织</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3EXOC/L+JKO3</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>2</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_customer.name</ListFieldId>
                      <Name>客户名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3EXOC1PUB4HD</Id>
                      <Key>yd_listcolumnap3</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_customer.number</ListFieldId>
                      <Name>客户编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3EXOC89AOGO9</Id>
                      <Key>yd_listcolumnap4</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_company.name</ListFieldId>
                      <Name>标签公司</Name>
                    </ListColumnAp>
                    <ListOperationColumnAp>
                      <Id>3EXOD/XPJ9KZ</Id>
                      <Key>yd_listoperationcolumnap</Key>
                      <OperationColItems>
                        <OperationColItem>
                          <operationKey>modify</operationKey>
                          <permissionId/>
                          <icon/>
                          <fontSize/>
                          <operationName>修改</operationName>
                          <Id>3EXOFBC2ID/V</Id>
                          <foreColor/>
                        </OperationColItem>
                        <OperationColItem>
                          <operationKey>delete</operationKey>
                          <permissionId/>
                          <icon/>
                          <fontSize/>
                          <operationName>删除</operationName>
                          <Id>3EXOFBC9SL7I</Id>
                          <foreColor/>
                        </OperationColItem>
                      </OperationColItems>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <Name>操作列</Name>
                    </ListOperationColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <FlexPanelAp>
              <Id>m4UD61n7sK</Id>
              <Name>Flex容器</Name>
              <Key>yd_flexpanelap</Key>
              <Direction>column</Direction>
            </FlexPanelAp>
            <FieldAp>
              <Id>6wyIp5N158</Id>
              <FieldId>6wyIp5N158</FieldId>
              <Name>销售组织</Name>
              <Key>yd_saleorg</Key>
              <ParentId>LF5N5umJYU</ParentId>
              <FullLine>true</FullLine>
            </FieldAp>
            <FieldAp>
              <Lock>submit,audit,edit</Lock>
              <Id>W324occyBN</Id>
              <FieldId>W324occyBN</FieldId>
              <Index>1</Index>
              <Name>客户</Name>
              <Key>yd_customer</Key>
              <ParentId>LF5N5umJYU</ParentId>
              <FullLine>true</FullLine>
            </FieldAp>
            <FieldAp>
              <Id>YsR7gMWT1i</Id>
              <FieldId>YsR7gMWT1i</FieldId>
              <Index>2</Index>
              <Name>标签公司</Name>
              <Key>yd_company</Key>
              <ParentId>LF5N5umJYU</ParentId>
              <FullLine>true</FullLine>
            </FieldAp>
            <ToolbarAp>
              <Id>ofRaI52PXD</Id>
              <Key>yd_toolbarap</Key>
              <Shrink>0</Shrink>
              <ParentId>m4UD61n7sK</ParentId>
              <Name>工具栏</Name>
            </ToolbarAp>
            <FlexPanelAp>
              <Collapsible>true</Collapsible>
              <Id>LF5N5umJYU</Id>
              <Index>1</Index>
              <Name>基础信息</Name>
              <Key>yd_flexpanelap1</Key>
              <ParentId>m4UD61n7sK</ParentId>
            </FlexPanelAp>
            <BarItemAp>
              <OperationKey>save</OperationKey>
              <Id>3bn9rRsxKZ</Id>
              <Key>yd_baritemap2</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>ofRaI52PXD</ParentId>
              <Name>保存</Name>
            </BarItemAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338628323399434240</ModifierId>
      <EntityId>3EXN3/TKBDUV</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1689841084145</Version>
      <ParentId></ParentId>
      <MasterId></MasterId>
      <Number>yd_customersign</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>3EXN3/TKBDUV</Id>
      <InheritPath></InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1689841084000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <DevType>0</DevType>
          <Isv>yd</Isv>
          <Id>3EXN3/TKBDUV</Id>
          <ModelType>BaseFormModel</ModelType>
          <ParentId/>
          <MasterId/>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>客户公司标签</Name>
          <InheritPath/>
          <Items>
            <BaseEntity>
              <dbRoute>scm</dbRoute>
              <NumberFieldId>number</NumberFieldId>
              <TableName>tk_yd_customersign</TableName>
              <Id>3EXN3/TKBDUV</Id>
              <Name>客户公司标签</Name>
              <PkId>3EXN30KANWOA</PkId>
              <Operations>
                <Operation>
                  <ConfirmMsg/>
                  <Name>新增</Name>
                  <OperationType>new</OperationType>
                  <Id>3EXNK1TOZ7IU</Id>
                  <SuccessMsg/>
                  <Key>new</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Name>修改</Name>
                  <OperationType>modify</OperationType>
                  <Id>3EXNLDBTQRB/</Id>
                  <SuccessMsg/>
                  <Key>modify</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <DelParameter>
                      <OperationKey>next</OperationKey>
                      <ListDelScope>seldata</ListDelScope>
                    </DelParameter>
                  </Parameter>
                  <Name>删除</Name>
                  <OperationType>delete</OperationType>
                  <Id>3EXNOPP2DDTY</Id>
                  <SuccessMsg/>
                  <Key>delete</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <SaveParameter>
                      <AfterOperation>close</AfterOperation>
                    </SaveParameter>
                  </Parameter>
                  <Name>保存</Name>
                  <OperationType>save</OperationType>
                  <Id>3EXNT7Z=LPDG</Id>
                  <SuccessMsg/>
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <CustomPromp/>
                      <Description>组合字段唯一性校验</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>3EXNSVT0+1AO</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_customer.number</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                  <Key>save</Key>
                </Operation>
              </Operations>
              <Key>yd_customersign</Key>
              <NameFieldId>name</NameFieldId>
              <PkFieldName>FID</PkFieldName>
            </BaseEntity>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>6wyIp5N158</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>销售组织</Name>
              <FieldName>fk_yd_saleorg</FieldName>
              <Key>yd_saleorg</Key>
              <RefLayout/>
              <BaseEntityId>PKJP6FA0=V1</BaseEntityId>
            </BasedataField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>W324occyBN</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>客户</Name>
              <FieldName>fk_yd_customer</FieldName>
              <Key>yd_customer</Key>
              <DisplayProp>name(number)</DisplayProp>
              <RefLayout/>
              <BaseEntityId>a86c9c130002f7ac</BaseEntityId>
            </BasedataField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>YsR7gMWT1i</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>标签公司</Name>
              <FieldName>fk_yd_company</FieldName>
              <Key>yd_company</Key>
              <RefLayout/>
              <BaseEntityId>PKJP6FA0=V1</BaseEntityId>
            </BasedataField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1689841084172</Version>
      <ParentId></ParentId>
      <MasterId></MasterId>
      <Number>yd_customersign</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>3EXN3/TKBDUV</Id>
      <InheritPath></InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1689841084145</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
