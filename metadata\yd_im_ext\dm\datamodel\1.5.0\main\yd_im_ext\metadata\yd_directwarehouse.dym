<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>2KWZT6K7CK0V</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>2KWZT6K7CK0V</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1663061647000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>2KWZT6K7CK0V</Id>
          <Key>yd_directwarehouse</Key>
          <EntityId>2KWZT6K7CK0V</EntityId>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>直营店仓库表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BasedataFormAp action="edit" oid="1942c188000064ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>2KWZT6K7CK0V</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>2KWZT6K7CK0V</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>直营店仓库表</Name>
              <Key>yd_directwarehouse</Key>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>2KWZT6K7CK0V</EntityId>
                    </BillListAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2QAFP24NFDH2</Id>
                      <Key>yd_combolistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>status</ListFieldId>
                      <Name>数据状态</Name>
                    </ComboListColumnAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2MR8FWWXS=F0</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_platform</ListFieldId>
                      <Name>平台</Name>
                    </ComboListColumnAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2MR8FZ40CA9=</Id>
                      <Key>yd_combolistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_type</ListFieldId>
                      <Name>仓库类型</Name>
                    </ComboListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2MR8G2OGRXDD</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_warehouse.number</ListFieldId>
                      <Name>仓库.编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2MR8G8BYZX8Y</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_warehouse.name</ListFieldId>
                      <Name>仓库.名称</Name>
                    </ListColumnAp>
                    <DateListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2QA/C5EBU==C</Id>
                      <Key>yd_datelistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>8</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>createtime</ListFieldId>
                      <Name>创建时间</Name>
                    </DateListColumnAp>
                    <ComboListColumnAp action="remove" oid="UGA0V8HS=6S"/>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <EntryAp>
              <EntryId>i330TStaFX</EntryId>
              <Id>i330TStaFX</Id>
              <Name>单据体</Name>
              <PageType/>
              <Key>yd_entryentity</Key>
              <ParentId>0saK9smADt</ParentId>
            </EntryAp>
            <FlexPanelAp action="edit" oid="PxNfkkak4s">
              <Grow>0</Grow>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                  <Padding>
                    <Padding>
                      <Left>2px</Left>
                      <Right action="reset"/>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <AdvConSummaryPanelAp>
              <Id>xI6PAxoOO1</Id>
              <Name>高级面板摘要容器</Name>
              <Key>yd_advconsummarypanelap</Key>
              <ParentId>63e0ylOKwK</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>6fBLs5QUgb</Id>
              <Key>yd_advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>63e0ylOKwK</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>0saK9smADt</Id>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>yd_advconchildpanelap</Key>
              <ParentId>63e0ylOKwK</ParentId>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>X0Gc9nz9aX</Id>
              <Key>yd_newentry</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>6fBLs5QUgb</ParentId>
              <Name>新增分录</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>insertentry</OperationKey>
              <Id>bgCBtUbH78</Id>
              <Key>yd_insertentry</Key>
              <OperationStyle>0</OperationStyle>
              <Index>1</Index>
              <ParentId>6fBLs5QUgb</ParentId>
              <Name>插入分录</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>AI16Uewj7F</Id>
              <Key>yd_deleteentry</Key>
              <OperationStyle>0</OperationStyle>
              <Index>2</Index>
              <ParentId>6fBLs5QUgb</ParentId>
              <Name>删除分录</Name>
            </AdvConBarItemAp>
            <FieldAp>
              <Id>ZAwEULaF8X</Id>
              <FieldId>ZAwEULaF8X</FieldId>
              <Index>2</Index>
              <Name>平台</Name>
              <Key>yd_platform</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Ew34LdvV4J</Id>
              <FieldId>Ew34LdvV4J</FieldId>
              <Index>3</Index>
              <Name>仓库类型</Name>
              <Key>yd_type</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp action="edit" oid="AkId5S4yTs">
              <Index>4</Index>
            </FieldAp>
            <FieldAp action="edit" oid="jiRpZNc99A">
              <Index>5</Index>
            </FieldAp>
            <FieldAp action="edit" oid="ac5Y5Dax1q">
              <Index>6</Index>
            </FieldAp>
            <FieldAp action="edit" oid="O75mQrM58q">
              <Index>7</Index>
            </FieldAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>63e0ylOKwK</Id>
              <Key>yd_advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>PxNfkkak4s</ParentId>
              <BackColor>@container-bg-color</BackColor>
              <Name>高级容器</Name>
            </AdvConAp>
            <EntryFieldAp>
              <Id>tQ9Dn19NiF</Id>
              <FieldId>tQ9Dn19NiF</FieldId>
              <Name>仓库</Name>
              <Key>yd_warehouse</Key>
              <ParentId>i330TStaFX</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338616377744819200</ModifierId>
      <EntityId>2KWZT6K7CK0V</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1663061647162</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_directwarehouse</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>2KWZT6K7CK0V</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1663061647000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>2KWZT6K7CK0V</Id>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>直营店仓库表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BaseEntity action="edit" oid="1942c188000066ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_directwarehouse</TableName>
              <Operations>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <NewEntryParameter>
                      <EntryId>i330TStaFX</EntryId>
                    </NewEntryParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>2MNM5N2CPJQ/</Id>
                  <SuccessMsg/>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>i330TStaFX</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>插入分录</Name>
                  <OperationType>insertentry</OperationType>
                  <Id>2MNM7G2+N8CI</Id>
                  <SuccessMsg/>
                  <Key>insertentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>i330TStaFX</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>2MNM8YF1K=1R</Id>
                  <SuccessMsg/>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>2KWZT6K7CK0V</Id>
              <Key>yd_directwarehouse</Key>
              <NetworkControl>
                <NetCtrlOperation>
                  <OperationKey>submit</OperationKey>
                  <GroupId>default_netctrl</GroupId>
                  <Id>c91d5125000034ac</Id>
                  <Key>default_netctrl_submit</Key>
                </NetCtrlOperation>
              </NetworkControl>
              <Name>直营店仓库表</Name>
            </BaseEntity>
            <MuliLangTextField action="edit" oid="8oq6R4m9CF">
              <GL>true</GL>
            </MuliLangTextField>
            <ComboField>
              <FieldName>fk_yd_platform</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ZAwEULaF8X</Id>
              <Key>yd_platform</Key>
              <MustInput>true</MustInput>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <FieldName>fk_yd_type</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Ew34LdvV4J</Id>
              <Key>yd_type</Key>
              <MustInput>true</MustInput>
              <Name>仓库类型</Name>
              <Items>
                <ComboItem>
                  <Caption>共享仓</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>实体仓</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <EntryEntity>
              <TableName>tk_yd_directwarehouse_e</TableName>
              <Id>i330TStaFX</Id>
              <Key>yd_entryentity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>tQ9Dn19NiF</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>仓库</Name>
              <FieldName>fk_yd_warehouse</FieldName>
              <Key>yd_warehouse</Key>
              <MustInput>true</MustInput>
              <RefLayout/>
              <ParentId>i330TStaFX</ParentId>
              <BaseEntityId>14G1K2YGTJGQ</BaseEntityId>
            </BasedataField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv></Isv>
      <Version>1663061647354</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_directwarehouse</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>2KWZT6K7CK0V</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1663061647162</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
