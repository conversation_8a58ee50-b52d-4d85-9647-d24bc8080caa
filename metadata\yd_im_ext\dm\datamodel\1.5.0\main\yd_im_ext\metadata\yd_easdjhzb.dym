<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>25KB8XJMM6OQ</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>25KB8XJMM6OQ</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1646128628000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>25KB8XJMM6OQ</Id>
          <Key>yd_easdjhzb</Key>
          <EntityId>25KB8XJMM6OQ</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>汇总对账单</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Id>25KB8XJMM6OQ</Id>
              <Name>汇总对账单</Name>
              <Key>yd_easdjhzb</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ReportPlugin>kd.bos.yd.tcyp.dzRptCk</ReportPlugin>
            </ReportListAp>
            <FieldAp>
              <Id>w7lg3FGypX</Id>
              <FieldId>w7lg3FGypX</FieldId>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>开始日期</Name>
              <Key>yd_rqgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>WF4QfvMjGY</Id>
              <FieldId>WF4QfvMjGY</FieldId>
              <Index>1</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>结束日期</Name>
              <Key>yd_rqgljs</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>oApK832skL</Id>
              <FieldId>oApK832skL</FieldId>
              <Index>2</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>客户</Name>
              <Key>yd_khgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>gllGdkmzgq</Id>
              <FieldId>gllGdkmzgq</FieldId>
              <Index>3</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>退货</Name>
              <Key>yd_thgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Sezd0rt2Zv</Id>
              <FieldId>Sezd0rt2Zv</FieldId>
              <Index>4</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>金额判断</Name>
              <Key>yd_jegl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>bDTgl65bcT</Id>
              <FieldId>bDTgl65bcT</FieldId>
              <Index>5</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>数量判断</Name>
              <Key>yd_slgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>ps6BRkKOwc</Id>
              <FieldId>ps6BRkKOwc</FieldId>
              <Index>6</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>平台</Name>
              <Key>yd_pt</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Lock>new,edit</Lock>
              <Id>fRdj477J9e</Id>
              <FontWeight>bold</FontWeight>
              <FieldId>fRdj477J9e</FieldId>
              <Index>7</Index>
              <ForeColor>#666666</ForeColor>
              <Name>提示</Name>
              <Key>yd_textfield</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
              <Width>1000px</Width>
            </FieldAp>
            <EntryFieldAp>
              <Id>pguPNMiQ84</Id>
              <FieldId>pguPNMiQ84</FieldId>
              <Name>平台</Name>
              <Key>yd_mxpt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>AdsMPtNELD</Id>
              <FieldId>AdsMPtNELD</FieldId>
              <Index>1</Index>
              <Name>退货</Name>
              <Key>yd_th</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>qiR3Y2zuBp</Id>
              <FieldId>qiR3Y2zuBp</FieldId>
              <Index>2</Index>
              <Name>单据类型</Name>
              <Key>yd_djlx</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>cvDcZjmu16</Id>
              <FieldId>cvDcZjmu16</FieldId>
              <Index>3</Index>
              <Name>客户</Name>
              <Key>yd_kh</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>eYWyB2u19J</Id>
              <FieldId>eYWyB2u19J</FieldId>
              <Index>4</Index>
              <Name>客户编码</Name>
              <Key>yd_khbm</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>oYFjC4w1Ge</Id>
              <FieldId>oYFjC4w1Ge</FieldId>
              <Index>5</Index>
              <Name>组织</Name>
              <Hidden>true</Hidden>
              <Key>yd_zz</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>lJntGQ38xB</Id>
              <FieldId>lJntGQ38xB</FieldId>
              <Index>6</Index>
              <Name>店铺</Name>
              <Key>yd_dp</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>YdGyZH6KZr</Id>
              <FieldId>YdGyZH6KZr</FieldId>
              <Index>7</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>发货明细SKU总数量</Name>
              <Key>yd_cqzsl</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>5v7rRyvxAB</Id>
              <FieldId>5v7rRyvxAB</FieldId>
              <Index>8</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>出库单SKU总数量</Name>
              <Key>yd_cqckzsl</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>y2HGRIwwBn</Id>
              <FieldId>y2HGRIwwBn</FieldId>
              <Index>9</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>剔除SKU总数量</Name>
              <Key>yd_tczsl</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>54n6ZgwJr9</Id>
              <FieldId>54n6ZgwJr9</FieldId>
              <Index>10</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>数量数据错误</Name>
              <Key>yd_sjcw</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>vhw4qqLfQU</Id>
              <FieldId>vhw4qqLfQU</FieldId>
              <Index>11</Index>
              <BackColor>rgba(255,153,28,0.5)</BackColor>
              <Name>发货明细SKU总金额</Name>
              <Key>yd_cqzje</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>k06vA0HB9Q</Id>
              <FieldId>k06vA0HB9Q</FieldId>
              <Index>12</Index>
              <BackColor>rgba(255,153,28,0.5)</BackColor>
              <Name>出库单sku总金额</Name>
              <Key>yd_cqckzje</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>FnONL2oDYh</Id>
              <FieldId>FnONL2oDYh</FieldId>
              <Index>13</Index>
              <BackColor>rgba(255,153,28,0.5)</BackColor>
              <Name>金额数据错误</Name>
              <Key>yd_jesjcw</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1276622576449229824</ModifierId>
      <EntityId>25KB8XJMM6OQ</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1646128628195</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_easdjhzb</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>25KB8XJMM6OQ</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <DataXml>
        <EntityMetadata>
          <Id>25KB8XJMM6OQ</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>汇总对账单</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>25KB8XJMM6OQ</Id>
              <Key>yd_easdjhzb</Key>
              <Name>汇总对账单</Name>
            </ReportEntity>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>w7lg3FGypX</Id>
              <Key>yd_rqgl</Key>
              <MustInput>true</MustInput>
              <Name>开始日期</Name>
            </DateField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>oApK832skL</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>客户</Name>
              <Key>yd_khgl</Key>
              <RefLayout/>
              <BaseEntityId>a86c9c130002f7ac</BaseEntityId>
            </BasedataField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gllGdkmzgq</Id>
              <Key>yd_thgl</Key>
              <DefValue>2</DefValue>
              <Name>退货</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>退货</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>非退货</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pguPNMiQ84</Id>
              <Key>yd_mxpt</Key>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>e3</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <CheckBoxField>
              <ShowStyle>1</ShowStyle>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>AdsMPtNELD</Id>
              <Key>yd_th</Key>
              <Name>退货</Name>
            </CheckBoxField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>qiR3Y2zuBp</Id>
              <Key>yd_djlx</Key>
              <Name>单据类型</Name>
            </TextField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>cvDcZjmu16</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>客户</Name>
              <Key>yd_kh</Key>
              <RefLayout/>
              <BaseEntityId>a86c9c130002f7ac</BaseEntityId>
            </BasedataField>
            <DecimalField>
              <Scale>0</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>YdGyZH6KZr</Id>
              <Key>yd_cqzsl</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>发货明细SKU总数量</Name>
            </DecimalField>
            <DecimalField>
              <Scale>0</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>y2HGRIwwBn</Id>
              <Key>yd_tczsl</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>剔除SKU总数量</Name>
            </DecimalField>
            <DecimalField>
              <Scale>0</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5v7rRyvxAB</Id>
              <Key>yd_cqckzsl</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>出库单SKU总数量</Name>
            </DecimalField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>vhw4qqLfQU</Id>
              <Key>yd_cqzje</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>发货明细SKU总金额</Name>
            </AmountField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>54n6ZgwJr9</Id>
              <Key>yd_sjcw</Key>
              <Name>数量数据错误</Name>
            </CheckBoxField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Sezd0rt2Zv</Id>
              <Key>yd_jegl</Key>
              <DefValue>2</DefValue>
              <Name>金额判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>金额一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>金额不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>bDTgl65bcT</Id>
              <Key>yd_slgl</Key>
              <DefValue>2</DefValue>
              <Name>数量判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>数量一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>数量不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>FnONL2oDYh</Id>
              <Key>yd_jesjcw</Key>
              <Name>金额数据错误</Name>
            </CheckBoxField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>oYFjC4w1Ge</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>组织</Name>
              <Key>yd_zz</Key>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ps6BRkKOwc</Id>
              <Key>yd_pt</Key>
              <DefValue>0</DefValue>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>不过滤</Caption>
                  <Value>0</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>3</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>lJntGQ38xB</Id>
              <Key>yd_dp</Key>
              <Name>店铺</Name>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>WF4QfvMjGY</Id>
              <Key>yd_rqgljs</Key>
              <MustInput>true</MustInput>
              <Name>结束日期</Name>
            </DateField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>k06vA0HB9Q</Id>
              <Key>yd_cqckzje</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>出库单sku总金额</Name>
            </AmountField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>eYWyB2u19J</Id>
              <Key>yd_khbm</Key>
              <RefDisplayProp>number</RefDisplayProp>
              <RefBaseFieldId>cvDcZjmu16</RefBaseFieldId>
              <Name>客户编码</Name>
            </BasedataPropField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>fRdj477J9e</Id>
              <Key>yd_textfield</Key>
              <DefValue>备注：（1）可核对日期：2022年2月17日后 （2）南京佰健店铺不适用于金额核对</DefValue>
              <Name>提示</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>kingdee</Isv>
      <Version>1646128628215</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_easdjhzb</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>25KB8XJMM6OQ</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1646128628195</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
