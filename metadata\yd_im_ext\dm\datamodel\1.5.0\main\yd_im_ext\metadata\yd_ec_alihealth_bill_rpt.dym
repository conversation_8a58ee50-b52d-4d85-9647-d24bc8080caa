<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>4YKMMHMMKA20</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4YKMMHMMKA20</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1749536518000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>4YKMMHMMKA20</Id>
          <Key>yd_ec_alihealth_bill_rpt</Key>
          <EntityId>4YKMMHMMKA20</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>阿里健康对账单</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>阿里健康对账单来源表插件</Description>
                  <ClassName>kd.bos.tcbj.ec.report.formplugin.SourceSaleAlihealthRptPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>4YKMMHMMKA20</Id>
              <Name>阿里健康对账单</Name>
              <Key>yd_ec_alihealth_bill_rpt</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ReportPlugin>kd.bos.tcbj.ec.report.queryplugin.SourceSaleAlihealthQueryRptPlugin</ReportPlugin>
            </ReportListAp>
            <FieldAp>
              <Id>UMkdUGH4Et</Id>
              <FieldId>UMkdUGH4Et</FieldId>
              <Name>账单编码</Name>
              <Key>yd_import_bill_no_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>nQd5O1n69J</Id>
              <FieldId>nQd5O1n69J</FieldId>
              <Index>1</Index>
              <Name>对账单号</Name>
              <Key>yd_fbillno_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>PcejhbAkZX</Id>
              <FieldId>PcejhbAkZX</FieldId>
              <Index>2</Index>
              <Name>账单生成时间起</Name>
              <Key>yd_startdate_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>3PYZnmekyU</Id>
              <FieldId>3PYZnmekyU</FieldId>
              <Index>3</Index>
              <Name>账单生成时间止</Name>
              <Key>yd_enddate_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>XbNsYH8RaZ</Id>
              <FieldId>XbNsYH8RaZ</FieldId>
              <Name>账单编码</Name>
              <Key>yd_import_bill_no</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>EXtPfcFLqp</Id>
              <FieldId>EXtPfcFLqp</FieldId>
              <Index>1</Index>
              <Name>对账单号</Name>
              <Key>yd_bill_no</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>szzi6pZlGm</Id>
              <FieldId>szzi6pZlGm</FieldId>
              <Index>2</Index>
              <Name>账单生成时间</Name>
              <Key>yd_create_date</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>sHafSa1XNf</Id>
              <FieldId>sHafSa1XNf</FieldId>
              <Index>3</Index>
              <Name>业务主单据编码</Name>
              <Key>yd_master_bill_no</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>0tFwhYE4Bc</Id>
              <FieldId>0tFwhYE4Bc</FieldId>
              <Index>4</Index>
              <Name>业务子单据编码</Name>
              <Key>yd_son_bill_no</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>xA0H7Oi0Oy</Id>
              <FieldId>xA0H7Oi0Oy</FieldId>
              <Index>5</Index>
              <Name>业务时间</Name>
              <Key>yd_biz_date</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>1OFeh0UCbe</Id>
              <FieldId>1OFeh0UCbe</FieldId>
              <Index>6</Index>
              <Name>业务单据类型</Name>
              <Key>yd_bill_type</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>JVzBXvxFiB</Id>
              <FieldId>JVzBXvxFiB</FieldId>
              <Index>7</Index>
              <Name>费用类型</Name>
              <Key>yd_cost_type</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>89cKJyZ8yF</Id>
              <FieldId>89cKJyZ8yF</FieldId>
              <Index>8</Index>
              <Name>结算方式</Name>
              <Key>yd_settle_way</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>tTpzQwWItb</Id>
              <FieldId>tTpzQwWItb</FieldId>
              <Index>9</Index>
              <Name>供应商编码</Name>
              <Key>yd_supplier_code</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>2TsRyUKdny</Id>
              <FieldId>2TsRyUKdny</FieldId>
              <Index>10</Index>
              <Name>供应商名称</Name>
              <Key>yd_supplier_name</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>utZs6jADPM</Id>
              <FieldId>utZs6jADPM</FieldId>
              <Index>11</Index>
              <Name>结算币别</Name>
              <Key>yd_currency</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>cT0Wb4at9e</Id>
              <FieldId>cT0Wb4at9e</FieldId>
              <Index>12</Index>
              <Name>含税金额</Name>
              <Key>yd_tax_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>PBqnP4JtOK</Id>
              <FieldId>PBqnP4JtOK</FieldId>
              <Index>13</Index>
              <Name>未税金额</Name>
              <Key>yd_untax_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>zNV0E4Kj2C</Id>
              <FieldId>zNV0E4Kj2C</FieldId>
              <Index>14</Index>
              <Name>税额</Name>
              <Key>yd_tax</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>sfZckrLVvo</Id>
              <FieldId>sfZckrLVvo</FieldId>
              <Index>15</Index>
              <Name>税率</Name>
              <Key>yd_tax_rate</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>MxyWJ0kLMh</Id>
              <FieldId>MxyWJ0kLMh</FieldId>
              <Index>16</Index>
              <Name>货品编码</Name>
              <Key>yd_goods_num</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>087u1xY3FC</Id>
              <FieldId>087u1xY3FC</FieldId>
              <Index>17</Index>
              <Name>货品名称</Name>
              <Key>yd_goods_name</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>nk6jvwG8Os</Id>
              <FieldId>nk6jvwG8Os</FieldId>
              <Index>18</Index>
              <Name>存储单位</Name>
              <Key>yd_unit</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>gkfV8LnOBk</Id>
              <FieldId>gkfV8LnOBk</FieldId>
              <Index>19</Index>
              <Name>计费数量</Name>
              <Key>yd_quantity</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>AA7bDvmR9V</Id>
              <FieldId>AA7bDvmR9V</FieldId>
              <Index>20</Index>
              <Name>含税单价</Name>
              <Key>yd_tax_price</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>h12bhI4fkU</Id>
              <FieldId>h12bhI4fkU</FieldId>
              <Index>21</Index>
              <Name>未税单价</Name>
              <Key>yd_untax_price</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>0mrn9UtGnn</Id>
              <FieldId>0mrn9UtGnn</FieldId>
              <Index>22</Index>
              <Name>是否重算</Name>
              <Key>yd_recalculate</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>mdk9djN7Qe</Id>
              <FieldId>mdk9djN7Qe</FieldId>
              <Index>23</Index>
              <Name>参考</Name>
              <Key>yd_remark</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>1jrwObwSZZ</Id>
              <FieldId>1jrwObwSZZ</FieldId>
              <Index>24</Index>
              <Name>唯一ID</Name>
              <Key>yd_soleid</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>4O6AY8Lyh9</Id>
              <FieldId>4O6AY8Lyh9</FieldId>
              <Index>25</Index>
              <Name>结算模式</Name>
              <Key>yd_settle_type</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1966784705198060544</ModifierId>
      <EntityId>4YKMMHMMKA20</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749536518267</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_alihealth_bill_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4YKMMHMMKA20</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1749536518000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>4YKMMHMMKA20</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>阿里健康对账单</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>4YKMMHMMKA20</Id>
              <Key>yd_ec_alihealth_bill_rpt</Key>
              <Name>阿里健康对账单</Name>
            </ReportEntity>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>EXtPfcFLqp</Id>
              <Key>yd_bill_no</Key>
              <Name>对账单号</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <DateTimeField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>szzi6pZlGm</Id>
              <Key>yd_create_date</Key>
              <Name>账单生成时间</Name>
            </DateTimeField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>sHafSa1XNf</Id>
              <Key>yd_master_bill_no</Key>
              <Name>业务主单据编码</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>0tFwhYE4Bc</Id>
              <Key>yd_son_bill_no</Key>
              <Name>业务子单据编码</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <DateTimeField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xA0H7Oi0Oy</Id>
              <Key>yd_biz_date</Key>
              <Name>业务时间</Name>
            </DateTimeField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1OFeh0UCbe</Id>
              <Key>yd_bill_type</Key>
              <Name>业务单据类型</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>JVzBXvxFiB</Id>
              <Key>yd_cost_type</Key>
              <Name>费用类型</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>89cKJyZ8yF</Id>
              <Key>yd_settle_way</Key>
              <Name>结算方式</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>tTpzQwWItb</Id>
              <Key>yd_supplier_code</Key>
              <Name>供应商编码</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2TsRyUKdny</Id>
              <Key>yd_supplier_name</Key>
              <Name>供应商名称</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>cT0Wb4at9e</Id>
              <DefValue>0</DefValue>
              <Name>含税金额</Name>
              <CurrencyFieldId>j9oZumrQGY</CurrencyFieldId>
              <Key>yd_tax_amount</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>PBqnP4JtOK</Id>
              <DefValue>0</DefValue>
              <Name>未税金额</Name>
              <CurrencyFieldId>j9oZumrQGY</CurrencyFieldId>
              <Key>yd_untax_amount</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>zNV0E4Kj2C</Id>
              <DefValue>0</DefValue>
              <Name>税额</Name>
              <CurrencyFieldId>j9oZumrQGY</CurrencyFieldId>
              <Key>yd_tax</Key>
            </AmountField>
            <DecimalField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>sfZckrLVvo</Id>
              <Key>yd_tax_rate</Key>
              <DefValue>0</DefValue>
              <Name>税率</Name>
            </DecimalField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>MxyWJ0kLMh</Id>
              <Key>yd_goods_num</Key>
              <Name>货品编码</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>087u1xY3FC</Id>
              <Key>yd_goods_name</Key>
              <Name>货品名称</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>nk6jvwG8Os</Id>
              <Key>yd_unit</Key>
              <Name>存储单位</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>0mrn9UtGnn</Id>
              <Key>yd_recalculate</Key>
              <Name>是否重算</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>mdk9djN7Qe</Id>
              <Key>yd_remark</Key>
              <Name>参考</Name>
              <MaxLength>255</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1jrwObwSZZ</Id>
              <Key>yd_soleid</Key>
              <Name>唯一ID</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4O6AY8Lyh9</Id>
              <Key>yd_settle_type</Key>
              <Name>结算模式</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>nQd5O1n69J</Id>
              <Key>yd_fbillno_param</Key>
              <Name>对账单号</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>PcejhbAkZX</Id>
              <Key>yd_startdate_param</Key>
              <Name>账单生成时间起</Name>
            </DateField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>3PYZnmekyU</Id>
              <Key>yd_enddate_param</Key>
              <Name>账单生成时间止</Name>
            </DateField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gkfV8LnOBk</Id>
              <Key>yd_quantity</Key>
              <Name>计费数量</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>AA7bDvmR9V</Id>
              <Key>yd_tax_price</Key>
              <Name>含税单价</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>h12bhI4fkU</Id>
              <Key>yd_untax_price</Key>
              <Name>未税单价</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>utZs6jADPM</Id>
              <Key>yd_currency</Key>
              <Name>结算币别</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>XbNsYH8RaZ</Id>
              <Key>yd_import_bill_no</Key>
              <Name>账单编码</Name>
              <MaxLength>255</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>UMkdUGH4Et</Id>
              <Key>yd_import_bill_no_param</Key>
              <Name>账单编码</Name>
              <MaxLength>255</MaxLength>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749536518296</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_alihealth_bill_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4YKMMHMMKA20</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1749536518267</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
