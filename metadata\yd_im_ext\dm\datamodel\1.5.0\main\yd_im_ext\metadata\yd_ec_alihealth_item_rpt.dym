<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>4YKT27OKS+EK</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4YKT27OKS+EK</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1749536800000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>4YKT27OKS+EK</Id>
          <Key>yd_ec_alihealth_item_rpt</Key>
          <EntityId>4YKT27OKS+EK</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>阿里健康直发对账明细</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>阿里健康直发对账明细 插件</Description>
                  <ClassName>kd.bos.tcbj.ec.report.formplugin.SourceSaleAlihealthItemRptPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>4YKT27OKS+EK</Id>
              <Name>阿里健康直发对账明细</Name>
              <Key>yd_ec_alihealth_item_rpt</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ReportPlugin>kd.bos.tcbj.ec.report.queryplugin.SourceSaleAlihealthItemQueryRptPlugin</ReportPlugin>
            </ReportListAp>
            <FieldAp>
              <Id>x3LL6uQtl3</Id>
              <FieldId>x3LL6uQtl3</FieldId>
              <Name>账单编码</Name>
              <Key>yd_import_bill_no_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>OqvRuT2vzv</Id>
              <FieldId>OqvRuT2vzv</FieldId>
              <Index>1</Index>
              <Name>对账单编号</Name>
              <Key>yd_fbillno_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>Om5x1xrG7K</Id>
              <FieldId>Om5x1xrG7K</FieldId>
              <Name>账单编码</Name>
              <Key>yd_import_bill_no</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>42WEANzKUC</Id>
              <FieldId>42WEANzKUC</FieldId>
              <Index>1</Index>
              <Name>供应商ID</Name>
              <Key>yd_supplier</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>QJFGn3pyDL</Id>
              <FieldId>QJFGn3pyDL</FieldId>
              <Index>2</Index>
              <Name>供应商名称</Name>
              <Key>yd_supplier_name</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>WXZRFoNzWw</Id>
              <FieldId>WXZRFoNzWw</FieldId>
              <Index>3</Index>
              <Name>订单ID</Name>
              <Key>yd_orderno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>CFYIbsl1lG</Id>
              <FieldId>CFYIbsl1lG</FieldId>
              <Index>4</Index>
              <Name>子订单ID</Name>
              <Key>yd_son_orderno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>LiDn7pHK5d</Id>
              <FieldId>LiDn7pHK5d</FieldId>
              <Index>5</Index>
              <Name>货品ID</Name>
              <Key>yd_goodsid</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>jx2ZfTqWEW</Id>
              <FieldId>jx2ZfTqWEW</FieldId>
              <Index>6</Index>
              <Name>货品名称</Name>
              <Key>yd_goods_name</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>q9L3WXpoSR</Id>
              <FieldId>q9L3WXpoSR</FieldId>
              <Index>7</Index>
              <Name>仓库编码</Name>
              <Key>yd_stash_num</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>UC8Q0eE4FH</Id>
              <FieldId>UC8Q0eE4FH</FieldId>
              <Index>8</Index>
              <Name>仓库名称</Name>
              <Key>yd_stash_name</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>XsP1Dzs1f1</Id>
              <FieldId>XsP1Dzs1f1</FieldId>
              <Index>9</Index>
              <Name>数量</Name>
              <Key>yd_quantity</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>1OaZUvQNUA</Id>
              <FieldId>1OaZUvQNUA</FieldId>
              <Index>10</Index>
              <Name>订单成交金额</Name>
              <Key>yd_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>kRbRPP1qiv</Id>
              <FieldId>kRbRPP1qiv</FieldId>
              <Index>11</Index>
              <Name>扣点</Name>
              <Key>yd_point</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>77z4Iir0Eo</Id>
              <FieldId>77z4Iir0Eo</FieldId>
              <Index>12</Index>
              <Name>采购金额</Name>
              <Key>yd_settle_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>x2kGzcQlU9</Id>
              <FieldId>x2kGzcQlU9</FieldId>
              <Index>13</Index>
              <Name>累计金额</Name>
              <Key>yd_accruing_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>2lGHzhXOKo</Id>
              <FieldId>2lGHzhXOKo</FieldId>
              <Index>14</Index>
              <Name>业务日期</Name>
              <Key>yd_biz_date</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>xSW27LQlMv</Id>
              <FieldId>xSW27LQlMv</FieldId>
              <Index>15</Index>
              <Name>采购单号</Name>
              <Key>yd_purchase_order</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>TuUthjGnqy</Id>
              <FieldId>TuUthjGnqy</FieldId>
              <Index>16</Index>
              <Name>卖家ID</Name>
              <Key>yd_sellerid</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>JjR4jnTarL</Id>
              <FieldId>JjR4jnTarL</FieldId>
              <Index>17</Index>
              <Name>单据完结时间</Name>
              <Key>yd_bill_end_date</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ajD74wSl6e</Id>
              <FieldId>ajD74wSl6e</FieldId>
              <Index>18</Index>
              <Name>备注</Name>
              <Key>yd_remarks</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Q7YkBLXrbC</Id>
              <FieldId>Q7YkBLXrbC</FieldId>
              <Index>19</Index>
              <Name>行号</Name>
              <Key>yd_line_num</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>p4OrKojLKt</Id>
              <FieldId>p4OrKojLKt</FieldId>
              <Index>20</Index>
              <Name>类目</Name>
              <Key>yd_category</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>PFhOVPDOs4</Id>
              <FieldId>PFhOVPDOs4</FieldId>
              <Index>21</Index>
              <Name>对账单编号</Name>
              <Key>yd_bill_no</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1966784705198060544</ModifierId>
      <EntityId>4YKT27OKS+EK</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749536800151</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_alihealth_item_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4YKT27OKS+EK</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1749536800000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>4YKT27OKS+EK</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>阿里健康直发对账明细</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>4YKT27OKS+EK</Id>
              <Key>yd_ec_alihealth_item_rpt</Key>
              <Name>阿里健康直发对账明细</Name>
            </ReportEntity>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>42WEANzKUC</Id>
              <Key>yd_supplier</Key>
              <Name>供应商ID</Name>
              <MaxLength>120</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QJFGn3pyDL</Id>
              <Key>yd_supplier_name</Key>
              <Name>供应商名称</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>WXZRFoNzWw</Id>
              <Key>yd_orderno</Key>
              <Name>订单ID</Name>
              <MaxLength>120</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>CFYIbsl1lG</Id>
              <Key>yd_son_orderno</Key>
              <Name>子订单ID</Name>
              <MaxLength>120</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>LiDn7pHK5d</Id>
              <Key>yd_goodsid</Key>
              <Name>货品ID</Name>
              <MaxLength>120</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>jx2ZfTqWEW</Id>
              <Key>yd_goods_name</Key>
              <Name>货品名称</Name>
              <MaxLength>120</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>q9L3WXpoSR</Id>
              <Key>yd_stash_num</Key>
              <Name>仓库编码</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>UC8Q0eE4FH</Id>
              <Key>yd_stash_name</Key>
              <Name>仓库名称</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <IntegerField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>XsP1Dzs1f1</Id>
              <Key>yd_quantity</Key>
              <DefValue>0</DefValue>
              <Name>数量</Name>
            </IntegerField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1OaZUvQNUA</Id>
              <DefValue>0</DefValue>
              <Name>订单成交金额</Name>
              <Key>yd_amount</Key>
            </AmountField>
            <DecimalField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kRbRPP1qiv</Id>
              <Key>yd_point</Key>
              <DefValue>0</DefValue>
              <Name>扣点</Name>
            </DecimalField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>77z4Iir0Eo</Id>
              <DefValue>0</DefValue>
              <Name>采购金额</Name>
              <Key>yd_settle_amount</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>x2kGzcQlU9</Id>
              <DefValue>0</DefValue>
              <Name>累计金额</Name>
              <Key>yd_accruing_amount</Key>
            </AmountField>
            <DateTimeField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2lGHzhXOKo</Id>
              <Key>yd_biz_date</Key>
              <Name>业务日期</Name>
            </DateTimeField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xSW27LQlMv</Id>
              <Key>yd_purchase_order</Key>
              <Name>采购单号</Name>
              <MaxLength>300</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>TuUthjGnqy</Id>
              <Key>yd_sellerid</Key>
              <Name>卖家ID</Name>
              <MaxLength>300</MaxLength>
            </TextField>
            <DateTimeField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>JjR4jnTarL</Id>
              <Key>yd_bill_end_date</Key>
              <Name>单据完结时间</Name>
            </DateTimeField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ajD74wSl6e</Id>
              <Key>yd_remarks</Key>
              <Name>备注</Name>
              <MaxLength>255</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Q7YkBLXrbC</Id>
              <Key>yd_line_num</Key>
              <Name>行号</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>p4OrKojLKt</Id>
              <Key>yd_category</Key>
              <Name>类目</Name>
              <MaxLength>120</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>PFhOVPDOs4</Id>
              <Key>yd_bill_no</Key>
              <Name>对账单编号</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>OqvRuT2vzv</Id>
              <Key>yd_fbillno_param</Key>
              <Name>对账单编号</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Om5x1xrG7K</Id>
              <Key>yd_import_bill_no</Key>
              <Name>账单编码</Name>
              <MaxLength>255</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>x3LL6uQtl3</Id>
              <Key>yd_import_bill_no_param</Key>
              <Name>账单编码</Name>
              <MaxLength>255</MaxLength>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749536800183</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_alihealth_item_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4YKT27OKS+EK</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1749536800151</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
