<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>5/PFEW3X514M</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>5/PFEW3X514M</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1750389800000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>5/PFEW3X514M</Id>
          <Key>yd_ec_e3goods2</Key>
          <EntityId>5/PFEW3X514M</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>E3商品2</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>5/PFEW9O+5X=</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/PFEW9NZT2N</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>5/PFEW9O+5X=</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/PFEW9O+5XA</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>5/PFEW9O+5X=</ParentId>
                      <ListFieldId>creator.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>创建人.姓名</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/PFEW3X514M</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>5/PFEW9O+5X=</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Direction>column</Direction>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>5/PFEW9NZT2M</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>5/PFEW3X514M</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>E3商品2</Name>
              <Key>yd_ec_e3goods2</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>5/PFEW9NZT2J</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>5/PFEW9O+5X7</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>5/PFEW9NZT2J</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>5/PFEW9NZT2K</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>5/PFEW9NZT2J</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>5/PFEW9O+5X8</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>5/PFEW9O+5X7</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>5/PFEW9NZT2L</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>5/PFEW9O+5X7</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>5/PFEW9O+5X9</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/PFEW3X514M</EntityId>
                    </BillListAp>
                    <ComboListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J5=R1SXPS</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>2</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <Name>单据状态</Name>
                    </ComboListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J5HCVZS12</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_goods_sn</ListFieldId>
                      <Name>商品代码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J5KGC=WCZ</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_goods_name</ListFieldId>
                      <Name>商品名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J5MUSKK6F</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_brand_name</ListFieldId>
                      <Name>商品品牌</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J5PENU1W3</Id>
                      <Key>yd_listcolumnap3</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_is_combo</ListFieldId>
                      <Name>组装商品</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J5SFY4685</Id>
                      <Key>yd_listcolumnap4</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_is_erp</ListFieldId>
                      <Name>ERP商品</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J5W0KEU8O</Id>
                      <Key>yd_listcolumnap5</Key>
                      <Order>NotOrder</Order>
                      <Index>8</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_status</ListFieldId>
                      <Name>状态</Name>
                    </ListColumnAp>
                    <DateListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J80OCS37U</Id>
                      <Key>yd_datelistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>9</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>createtime</ListFieldId>
                      <Name>创建时间</Name>
                    </DateListColumnAp>
                    <DateListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>501J879XW652</Id>
                      <Key>yd_datelistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>10</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>modifytime</ListFieldId>
                      <Name>修改时间</Name>
                    </DateListColumnAp>
                    <ComboListColumnAp action="remove" oid="f87b7292000031ac"/>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp>
              <Id>MXDMu9WFgG</Id>
              <FieldId>MXDMu9WFgG</FieldId>
              <Index>2</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>商品代码</Name>
              <Key>yd_goods_sn</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>8z7xiaLEfZ</Id>
              <FieldId>8z7xiaLEfZ</FieldId>
              <Index>3</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>商品名称</Name>
              <Key>yd_goods_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>HDwZClPVqR</Id>
              <FieldId>HDwZClPVqR</FieldId>
              <Index>4</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>商品品牌</Name>
              <Key>yd_brand_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Oul6i1UgLt</Id>
              <FieldId>Oul6i1UgLt</FieldId>
              <Index>5</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>组装商品</Name>
              <Key>yd_is_combo</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>SMcRFYVFb0</Id>
              <FieldId>SMcRFYVFb0</FieldId>
              <Index>6</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>ERP商品</Name>
              <Key>yd_is_erp</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>yYIVg2w1oi</Id>
              <FieldId>yYIVg2w1oi</FieldId>
              <Index>7</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>状态</Name>
              <Key>yd_status</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp action="edit" oid="mGJPp5Qux7">
              <Index>8</Index>
            </FieldAp>
            <FieldAp action="edit" oid="GnxpBjqGJ5">
              <Index>9</Index>
            </FieldAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <FieldsetPanelAp action="edit" oid="xTQZ9d8tHa">
              <Index>1</Index>
            </FieldsetPanelAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Index>2</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338619120089825280</ModifierId>
      <EntityId>5/PFEW3X514M</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750389799636</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_e3goods2</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>5/PFEW3X514M</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1750389800000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>5/PFEW3X514M</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>E3商品2</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_ec_e3goods2</TableName>
              <Operations>
                <Operation action="edit" oid="c91d5125000033ac">
                  <Parameter>
                    <SaveParameter>
                      <StatusFieldId>6q69iznvHX</StatusFieldId>
                    </SaveParameter>
                  </Parameter>
                  <Validations>
                    <ConditionValidation>
                      <Expression>yd_brand_name = '汤臣倍健'</Expression>
                      <Description>品牌汤臣倍健合法性校验</Description>
                      <Message>品牌需要写汤臣倍健</Message>
                      <Enabled>true</Enabled>
                      <RuleType>FormValidate</RuleType>
                      <Id>50A1BP/SWG6O</Id>
                    </ConditionValidation>
                  </Validations>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>5/PFEW9O+5X4</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>5/PFEW9NZT2I</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>5/PFEW9O+5X4</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>5/PFEW9O+5X6</Id>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>5/PFEW3X514M</Id>
              <Key>yd_ec_e3goods2</Key>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
              <Name>E3商品2</Name>
            </BillEntity>
            <TextField>
              <FieldName>fk_yd_textfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>yYIVg2w1oi</Id>
              <Key>yd_status</Key>
              <Name>状态</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_textfield1</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>MXDMu9WFgG</Id>
              <Key>yd_goods_sn</Key>
              <Name>商品代码</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_textfield2</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>8z7xiaLEfZ</Id>
              <Key>yd_goods_name</Key>
              <Name>商品名称</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_textfield3</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>HDwZClPVqR</Id>
              <Key>yd_brand_name</Key>
              <Name>商品品牌</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_textfield4</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Oul6i1UgLt</Id>
              <Key>yd_is_combo</Key>
              <Name>组装商品</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_textfield5</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>SMcRFYVFb0</Id>
              <Key>yd_is_erp</Key>
              <Name>ERP商品</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750389799667</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_e3goods2</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>5/PFEW3X514M</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1750389799636</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>5/PCP+W5884U</BizunitId>
</DeployMetadata>
