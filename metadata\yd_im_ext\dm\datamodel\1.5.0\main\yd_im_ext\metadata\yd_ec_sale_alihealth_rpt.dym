<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>4YUJ5+R7L8NT</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4YUJ5+R7L8NT</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1750301807000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>4YUJ5+R7L8NT</Id>
          <Key>yd_ec_sale_alihealth_rpt</Key>
          <EntityId>4YUJ5+R7L8NT</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>阿里健康寄售平台账单（总表）</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>阿里健康寄售平台账单（总表）插件</Description>
                  <ClassName>kd.bos.tcbj.ec.report.formplugin.SaleAlihealthRptPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>4YUJ5+R7L8NT</Id>
              <Name>阿里健康寄售平台账单（总表）</Name>
              <Key>yd_ec_sale_alihealth_rpt</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <ReportPlugin>kd.bos.tcbj.ec.report.queryplugin.SaleAlihealthQueryRptPlugin</ReportPlugin>
            </ReportListAp>
            <BarItemAp>
              <Id>CXMCdicnA4</Id>
              <Key>yd_import</Key>
              <OperationStyle>0</OperationStyle>
              <Index>3</Index>
              <ParentId>FohRnuDXx2</ParentId>
              <Name>导入</Name>
            </BarItemAp>
            <BarItemAp>
              <Id>7ic26vwHoX</Id>
              <Key>yd_push_settlement_bill</Key>
              <OperationStyle>0</OperationStyle>
              <Index>4</Index>
              <ParentId>FohRnuDXx2</ParentId>
              <Name>下推结算中间表</Name>
            </BarItemAp>
            <BarItemAp>
              <Id>jE6nRUdtuL</Id>
              <Key>yd_delete</Key>
              <OperationStyle>0</OperationStyle>
              <Index>5</Index>
              <ParentId>FohRnuDXx2</ParentId>
              <Name>删除</Name>
            </BarItemAp>
            <FieldAp>
              <Id>6ZjnbUaUy2</Id>
              <FieldId>6ZjnbUaUy2</FieldId>
              <Name>账单编码</Name>
              <Key>yd_billno_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>dEfgWBDRlc</Id>
              <FieldId>dEfgWBDRlc</FieldId>
              <Index>1</Index>
              <Name>业务日期起</Name>
              <Key>yd_startdate_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>GOfGInKq9B</Id>
              <FieldId>GOfGInKq9B</FieldId>
              <Index>2</Index>
              <Name>业务日期止</Name>
              <Key>yd_enddate_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>hsHiXHTm0J</Id>
              <FieldId>hsHiXHTm0J</FieldId>
              <Index>3</Index>
              <Name>状态（中间表）</Name>
              <Key>yd_status_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>5u5hsgsO9F</Id>
              <FieldId>5u5hsgsO9F</FieldId>
              <Index>4</Index>
              <Name>是否异常</Name>
              <Key>yd_is_abnormal_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>aoSSN9BHUc</Id>
              <FieldId>aoSSN9BHUc</FieldId>
              <Index>5</Index>
              <Name>销售出库单</Name>
              <Key>yd_saleout_bill_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>xZYmPka491</Id>
              <FieldId>xZYmPka491</FieldId>
              <Name>平台</Name>
              <Key>yd_platform</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>JQDN4QTVAi</Id>
              <FieldId>JQDN4QTVAi</FieldId>
              <Index>1</Index>
              <Name>店铺</Name>
              <Key>yd_shop</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ZFGiHYU7Tg</Id>
              <FieldId>ZFGiHYU7Tg</FieldId>
              <Hyperlink>true</Hyperlink>
              <Index>2</Index>
              <Name>账单编码</Name>
              <Key>yd_billno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>1eBekY0yMK</Id>
              <FieldId>1eBekY0yMK</FieldId>
              <Index>3</Index>
              <Name>业务日期</Name>
              <Key>yd_bizdate</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>SbCryEGWNa</Id>
              <FieldId>SbCryEGWNa</FieldId>
              <Index>4</Index>
              <Name>统计时间</Name>
              <Key>yd_create_time</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>BPgzO7wecc</Id>
              <FieldId>BPgzO7wecc</FieldId>
              <Index>5</Index>
              <Name>状态（中间表）</Name>
              <Key>yd_status</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>BgcmGIWRRH</Id>
              <FieldId>BgcmGIWRRH</FieldId>
              <Hyperlink>true</Hyperlink>
              <Index>6</Index>
              <Name>中间表单号</Name>
              <Key>yd_settle_billno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>9G8zJmx59L</Id>
              <FieldId>9G8zJmx59L</FieldId>
              <Index>7</Index>
              <Name>是否异常</Name>
              <Key>yd_is_abnormal</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>pCeXKBrjho</Id>
              <FieldId>pCeXKBrjho</FieldId>
              <Index>8</Index>
              <Name>下推中间表异常原因</Name>
              <Key>yd_error_msg</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>u049leH9lY</Id>
              <FieldId>u049leH9lY</FieldId>
              <Hyperlink>true</Hyperlink>
              <Index>9</Index>
              <Name>销售出库单/退货单</Name>
              <Key>yd_saleout_bill</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>m61g11Mdr3</Id>
              <FieldId>m61g11Mdr3</FieldId>
              <Index>10</Index>
              <Name>下推销售出库单异常原因</Name>
              <Key>yd_push_error_msg</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>I85A4uM4Ps</Id>
              <FieldId>I85A4uM4Ps</FieldId>
              <Index>11</Index>
              <Name>平台金额</Name>
              <Key>yd_platform_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>XfxcqBemaT</Id>
              <FieldId>XfxcqBemaT</FieldId>
              <Index>12</Index>
              <Name>销售出库单金额</Name>
              <Key>yd_saleout_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>0sg1SRIBcH</Id>
              <FieldId>0sg1SRIBcH</FieldId>
              <Index>13</Index>
              <Name>差异核算结果</Name>
              <Key>yd_result_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1966784705198060544</ModifierId>
      <EntityId>4YUJ5+R7L8NT</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750301807121</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_sale_alihealth_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4YUJ5+R7L8NT</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1750301807000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>4YUJ5+R7L8NT</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>阿里健康寄售平台账单（总表）</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>4YUJ5+R7L8NT</Id>
              <Key>yd_ec_sale_alihealth_rpt</Key>
              <Name>阿里健康寄售平台账单（总表）</Name>
            </ReportEntity>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xZYmPka491</Id>
              <Key>yd_platform</Key>
              <DefValue>2</DefValue>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>猫超</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>阿里健康</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>中免</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>JQDN4QTVAi</Id>
              <Key>yd_shop</Key>
              <DefValue>2</DefValue>
              <Name>店铺</Name>
              <Items>
                <ComboItem>
                  <Caption>浙江昊超</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>广州麦优网络科技有限公司-寄售（阿里健康）</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>上海中免日上商业有限公司</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ZFGiHYU7Tg</Id>
              <Key>yd_billno</Key>
              <Name>账单编码</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>1eBekY0yMK</Id>
              <Key>yd_bizdate</Key>
              <Name>业务日期</Name>
            </DateField>
            <DateTimeField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>SbCryEGWNa</Id>
              <Key>yd_create_time</Key>
              <Name>统计时间</Name>
            </DateTimeField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>BPgzO7wecc</Id>
              <Key>yd_status</Key>
              <Name>状态（中间表）</Name>
              <Items>
                <ComboItem>
                  <Caption>待推送</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>下推成功</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>下推失败</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>9G8zJmx59L</Id>
              <Key>yd_is_abnormal</Key>
              <Name>是否异常</Name>
            </CheckBoxField>
            <LargeTextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pCeXKBrjho</Id>
              <Name>下推中间表异常原因</Name>
              <Key>yd_error_msg</Key>
            </LargeTextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>I85A4uM4Ps</Id>
              <DefValue>0</DefValue>
              <Name>平台金额</Name>
              <Key>yd_platform_amount</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>XfxcqBemaT</Id>
              <DefValue>0</DefValue>
              <Name>销售出库单金额</Name>
              <Key>yd_saleout_amount</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>0sg1SRIBcH</Id>
              <DefValue>0</DefValue>
              <Name>差异核算结果</Name>
              <Key>yd_result_amount</Key>
            </AmountField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>u049leH9lY</Id>
              <Key>yd_saleout_bill</Key>
              <Name>销售出库单/退货单</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>6ZjnbUaUy2</Id>
              <Key>yd_billno_param</Key>
              <Name>账单编码</Name>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>dEfgWBDRlc</Id>
              <Key>yd_startdate_param</Key>
              <Name>业务日期起</Name>
            </DateField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>GOfGInKq9B</Id>
              <Key>yd_enddate_param</Key>
              <Name>业务日期止</Name>
            </DateField>
            <MulComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>hsHiXHTm0J</Id>
              <Key>yd_status_param</Key>
              <Name>状态（中间表）</Name>
              <Items>
                <ComboItem>
                  <Caption>待推送</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>下推成功</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>下推失败</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </MulComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>aoSSN9BHUc</Id>
              <Key>yd_saleout_bill_param</Key>
              <Name>销售出库单</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <MulComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5u5hsgsO9F</Id>
              <Key>yd_is_abnormal_param</Key>
              <Name>是否异常</Name>
              <Items>
                <ComboItem>
                  <Caption>是</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>否</Caption>
                  <Value>0</Value>
                  <Seq>1</Seq>
                </ComboItem>
              </Items>
            </MulComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>BgcmGIWRRH</Id>
              <Key>yd_settle_billno</Key>
              <Name>中间表单号</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <LargeTextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>m61g11Mdr3</Id>
              <Name>下推销售出库单异常原因</Name>
              <Key>yd_push_error_msg</Key>
            </LargeTextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750301807157</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_sale_alihealth_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4YUJ5+R7L8NT</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1750301807121</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
