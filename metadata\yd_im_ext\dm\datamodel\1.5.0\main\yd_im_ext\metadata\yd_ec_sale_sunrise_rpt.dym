<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>5+TEEWR85JZK</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>5+TEEWR85JZK</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1750304997000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>5+TEEWR85JZK</Id>
          <Key>yd_ec_sale_sunrise_rpt</Key>
          <EntityId>5+TEEWR85JZK</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>中免日上寄售平台账单（总表）</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>中免日上寄售平台账单（总表）插件</Description>
                  <ClassName>kd.bos.tcbj.ec.report.formplugin.SaleSunriseRptPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>5+TEEWR85JZK</Id>
              <Name>中免日上寄售平台账单（总表）</Name>
              <Key>yd_ec_sale_sunrise_rpt</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <ReportPlugin>kd.bos.tcbj.ec.report.queryplugin.SaleSunriseQueryRptPlugin</ReportPlugin>
            </ReportListAp>
            <BarItemAp>
              <Id>q0GRtVEHux</Id>
              <Key>yd_import</Key>
              <OperationStyle>0</OperationStyle>
              <Index>3</Index>
              <ParentId>FohRnuDXx2</ParentId>
              <Name>导入</Name>
            </BarItemAp>
            <BarItemAp>
              <Id>gW4zkSO8L2</Id>
              <Key>yd_push_settlement_bill</Key>
              <OperationStyle>0</OperationStyle>
              <Index>4</Index>
              <ParentId>FohRnuDXx2</ParentId>
              <Name>下推结算中间表</Name>
            </BarItemAp>
            <BarItemAp>
              <Id>0Bk3Fxnwb9</Id>
              <Key>yd_delete</Key>
              <OperationStyle>0</OperationStyle>
              <Index>5</Index>
              <ParentId>FohRnuDXx2</ParentId>
              <Name>删除</Name>
            </BarItemAp>
            <FieldAp>
              <Id>IkLytOmAn7</Id>
              <FieldId>IkLytOmAn7</FieldId>
              <Name>账单编码</Name>
              <Key>yd_billno_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>OTZ4qfqSCp</Id>
              <FieldId>OTZ4qfqSCp</FieldId>
              <Index>1</Index>
              <Name>业务日期起</Name>
              <Key>yd_startdate_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>MR9K8uef1T</Id>
              <FieldId>MR9K8uef1T</FieldId>
              <Index>2</Index>
              <Name>业务日期止</Name>
              <Key>yd_enddate_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>KJJ3LaqlBk</Id>
              <FieldId>KJJ3LaqlBk</FieldId>
              <Index>3</Index>
              <Name>状态（中间表）</Name>
              <Key>yd_status_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>w8vWTBR2A4</Id>
              <FieldId>w8vWTBR2A4</FieldId>
              <Index>4</Index>
              <Name>是否异常</Name>
              <Key>yd_is_abnormal_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>he6J29IeS6</Id>
              <FieldId>he6J29IeS6</FieldId>
              <Index>5</Index>
              <Name>销售出库单</Name>
              <Key>yd_saleout_bill_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>L1WhRMB6r6</Id>
              <FieldId>L1WhRMB6r6</FieldId>
              <Name>平台</Name>
              <Key>yd_platform</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>osLpj74vjM</Id>
              <FieldId>osLpj74vjM</FieldId>
              <Index>1</Index>
              <Name>店铺</Name>
              <Key>yd_shop</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>1ro6K5rYTI</Id>
              <FieldId>1ro6K5rYTI</FieldId>
              <Hyperlink>true</Hyperlink>
              <Index>2</Index>
              <Name>账单编码</Name>
              <Key>yd_billno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>5VMXsmMvOk</Id>
              <FieldId>5VMXsmMvOk</FieldId>
              <Index>3</Index>
              <Name>业务日期</Name>
              <Key>yd_bizdate</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>wZGE03jbvy</Id>
              <FieldId>wZGE03jbvy</FieldId>
              <Index>4</Index>
              <Name>统计时间</Name>
              <Key>yd_create_time</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>2mTcV3FFLP</Id>
              <FieldId>2mTcV3FFLP</FieldId>
              <Index>5</Index>
              <Name>状态（中间表）</Name>
              <Key>yd_status</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>dcDhM0E4RJ</Id>
              <FieldId>dcDhM0E4RJ</FieldId>
              <Hyperlink>true</Hyperlink>
              <Index>6</Index>
              <Name>中间表单号</Name>
              <Key>yd_settle_billno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>XRyUYhmZAj</Id>
              <FieldId>XRyUYhmZAj</FieldId>
              <Index>7</Index>
              <Name>是否异常</Name>
              <Key>yd_is_abnormal</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>D4UY3MrbHR</Id>
              <FieldId>D4UY3MrbHR</FieldId>
              <Index>8</Index>
              <Name>下推中间表异常原因</Name>
              <Key>yd_error_msg</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>m80uuc2izh</Id>
              <FieldId>m80uuc2izh</FieldId>
              <Hyperlink>true</Hyperlink>
              <Index>9</Index>
              <Name>销售出库单/退货单</Name>
              <Key>yd_saleout_bill</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>0JDy7Xkilb</Id>
              <FieldId>0JDy7Xkilb</FieldId>
              <Index>10</Index>
              <Name>下推销售出库单异常原因</Name>
              <Key>yd_push_error_msg</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>kmvfL5FxJs</Id>
              <FieldId>kmvfL5FxJs</FieldId>
              <Index>11</Index>
              <Name>平台金额</Name>
              <Key>yd_platform_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Sc1JwMbdbU</Id>
              <FieldId>Sc1JwMbdbU</FieldId>
              <Index>12</Index>
              <Name>销售出库单金额</Name>
              <Key>yd_saleout_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>k8zou3Ut4G</Id>
              <FieldId>k8zou3Ut4G</FieldId>
              <Index>13</Index>
              <Name>差异核算结果</Name>
              <Key>yd_result_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1966784705198060544</ModifierId>
      <EntityId>5+TEEWR85JZK</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750304996961</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_sale_sunrise_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>5+TEEWR85JZK</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1750304997000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>5+TEEWR85JZK</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>中免日上寄售平台账单（总表）</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>5+TEEWR85JZK</Id>
              <Key>yd_ec_sale_sunrise_rpt</Key>
              <Name>中免日上寄售平台账单（总表）</Name>
            </ReportEntity>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>L1WhRMB6r6</Id>
              <Key>yd_platform</Key>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>猫超</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>阿里健康</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>中免</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>osLpj74vjM</Id>
              <Key>yd_shop</Key>
              <Name>店铺</Name>
              <Items>
                <ComboItem>
                  <Caption>浙江昊超</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>广州麦优网络科技有限公司-寄售（阿里健康）</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>上海中免日上商业有限公司</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1ro6K5rYTI</Id>
              <Key>yd_billno</Key>
              <Name>账单编码</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>5VMXsmMvOk</Id>
              <Key>yd_bizdate</Key>
              <Name>业务日期</Name>
            </DateField>
            <DateTimeField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>wZGE03jbvy</Id>
              <Key>yd_create_time</Key>
              <Name>统计时间</Name>
            </DateTimeField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2mTcV3FFLP</Id>
              <Key>yd_status</Key>
              <Name>状态（中间表）</Name>
              <Items>
                <ComboItem>
                  <Caption>待推送</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>下推成功</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>下推失败</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>dcDhM0E4RJ</Id>
              <Key>yd_settle_billno</Key>
              <Name>中间表单号</Name>
              <MaxLength>150</MaxLength>
            </TextField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>XRyUYhmZAj</Id>
              <Key>yd_is_abnormal</Key>
              <Name>是否异常</Name>
            </CheckBoxField>
            <LargeTextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>D4UY3MrbHR</Id>
              <Name>下推中间表异常原因</Name>
              <Key>yd_error_msg</Key>
            </LargeTextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>m80uuc2izh</Id>
              <Key>yd_saleout_bill</Key>
              <Name>销售出库单/退货单</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <LargeTextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>0JDy7Xkilb</Id>
              <Name>下推销售出库单异常原因</Name>
              <Key>yd_push_error_msg</Key>
            </LargeTextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kmvfL5FxJs</Id>
              <DefValue>0</DefValue>
              <Name>平台金额</Name>
              <Key>yd_platform_amount</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Sc1JwMbdbU</Id>
              <DefValue>0</DefValue>
              <Name>销售出库单金额</Name>
              <Key>yd_saleout_amount</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>k8zou3Ut4G</Id>
              <DefValue>0</DefValue>
              <Name>差异核算结果</Name>
              <Key>yd_result_amount</Key>
            </AmountField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>IkLytOmAn7</Id>
              <Key>yd_billno_param</Key>
              <Name>账单编码</Name>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>OTZ4qfqSCp</Id>
              <Key>yd_startdate_param</Key>
              <Name>业务日期起</Name>
            </DateField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>MR9K8uef1T</Id>
              <Key>yd_enddate_param</Key>
              <Name>业务日期止</Name>
            </DateField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>KJJ3LaqlBk</Id>
              <Key>yd_status_param</Key>
              <Name>状态（中间表）</Name>
              <Items>
                <ComboItem>
                  <Caption>待推送</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>下推成功</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>下推失败</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>w8vWTBR2A4</Id>
              <Key>yd_is_abnormal_param</Key>
              <Name>是否异常</Name>
              <Items>
                <ComboItem>
                  <Caption>是</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>否</Caption>
                  <Value>0</Value>
                  <Seq>1</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>he6J29IeS6</Id>
              <Key>yd_saleout_bill_param</Key>
              <Name>销售出库单</Name>
              <MaxLength>150</MaxLength>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750304997009</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_ec_sale_sunrise_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>5+TEEWR85JZK</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1750304996961</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
