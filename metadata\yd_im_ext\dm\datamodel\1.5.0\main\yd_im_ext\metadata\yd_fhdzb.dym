<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>1WCKAGBNM+JY</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>1WCKAGBNM+JY</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1666087249000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>1WCKAGBNM+JY</Id>
          <Key>yd_fhdzb</Key>
          <EntityId>1WCKAGBNM+JY</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>拉单对账单</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <BizAppId/>
                  <FPK/>
                  <Description/>
                  <ClassName>kd.bos.yd.tcyp.dzRptBd</ClassName>
                  <Enabled>true</Enabled>
                </Plugin>
              </Plugins>
              <Id>1WCKAGBNM+JY</Id>
              <Name>拉单对账单</Name>
              <Key>yd_fhdzb</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ClientRules>
                <ClientRule>
                  <TrueActions>
                    <SetRowStyleAction>
                      <RET>134</RET>
                      <ActionType>SetRowStyleAction</ActionType>
                      <RowStyles>
                        <RowStyle>
                          <BackgroundColor>#87CEEB</BackgroundColor>
                          <Degree>100</Degree>
                          <Id>201WI1LC8+AP</Id>
                          <ForeColor>#FFFFFF</ForeColor>
                        </RowStyle>
                      </RowStyles>
                      <Description>设置行样式</Description>
                      <Id>201WI5KXKLLN</Id>
                    </SetRowStyleAction>
                  </TrueActions>
                  <Id>201V1L48XS0U</Id>
                  <PreCondition>yd_th = true and yd_kh &lt;&gt; 0</PreCondition>
                  <PreDescription>111</PreDescription>
                </ClientRule>
              </ClientRules>
              <ReportPlugin>kd.bos.yd.tcyp.dzRpt</ReportPlugin>
            </ReportListAp>
            <FieldAp>
              <Id>xNYLaP61gk</Id>
              <FieldId>xNYLaP61gk</FieldId>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>开始日期</Name>
              <Key>yd_rqgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>6F2E71P9RP</Id>
              <FieldId>6F2E71P9RP</FieldId>
              <Index>1</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>结束日期</Name>
              <Key>yd_rqgljs</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>NIheetmm2x</Id>
              <FieldId>NIheetmm2x</FieldId>
              <Index>2</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>退货</Name>
              <Key>yd_thgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>KY4GLjds6P</Id>
              <FieldId>KY4GLjds6P</FieldId>
              <Index>3</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>金额判断</Name>
              <Key>yd_jegl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Si7AtbcIPc</Id>
              <FieldId>Si7AtbcIPc</FieldId>
              <Index>4</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>数量判断</Name>
              <Key>yd_slgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>gXZ4FCs0Tc</Id>
              <FieldId>gXZ4FCs0Tc</FieldId>
              <Index>5</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>店铺(支持模糊查询)</Name>
              <Key>yd_dpgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>kWNdw4nw6d</Id>
              <FieldId>kWNdw4nw6d</FieldId>
              <Index>6</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>单数判断</Name>
              <Key>yd_dsgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>PjXPtTK0kn</Id>
              <FieldId>PjXPtTK0kn</FieldId>
              <Index>7</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>平台</Name>
              <Key>yd_pt</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>LYtv0VfkGs</Id>
              <FieldId>LYtv0VfkGs</FieldId>
              <Index>8</Index>
              <Name>组织</Name>
              <Key>yd_zzgl</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>95BeSjkybX</Id>
              <FieldId>95BeSjkybX</FieldId>
              <Name>平台</Name>
              <Key>yd_mxpt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>opDZr7E3Er</Id>
              <FieldId>opDZr7E3Er</FieldId>
              <Index>1</Index>
              <Name>退货</Name>
              <Key>yd_th</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>gWeEKjzneH</Id>
              <FieldId>gWeEKjzneH</FieldId>
              <Index>2</Index>
              <Name>组织</Name>
              <Key>yd_zz</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>75AdVQstC0</Id>
              <FieldId>75AdVQstC0</FieldId>
              <Index>3</Index>
              <Name>店铺编号</Name>
              <Key>yd_dpbh</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Gaz16IC6Q5</Id>
              <FieldId>Gaz16IC6Q5</FieldId>
              <Index>4</Index>
              <Name>店铺名称</Name>
              <Key>yd_dpmc</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>flFr6sGA38</Id>
              <FieldId>flFr6sGA38</FieldId>
              <Index>5</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>平台订单总单数</Name>
              <Key>yd_ptzs</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>F7Ijmn4ets</Id>
              <FieldId>F7Ijmn4ets</FieldId>
              <Index>6</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>苍穹订单总单数</Name>
              <Key>yd_cqzs</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>s3k16LSw96</Id>
              <FieldId>s3k16LSw96</FieldId>
              <Index>7</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>单数数据错误</Name>
              <Key>yd_dssjcw</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>QCeHXbnv1Q</Id>
              <FieldId>QCeHXbnv1Q</FieldId>
              <Index>8</Index>
              <BackColor>rgba(119,196,4,0.5)</BackColor>
              <Name>平台订单物料总数量</Name>
              <Key>yd_ptzsl</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>gEEj77uPzn</Id>
              <FieldId>gEEj77uPzn</FieldId>
              <Index>9</Index>
              <BackColor>rgba(119,196,4,0.5)</BackColor>
              <Name>苍穹订单物料总数量</Name>
              <Key>yd_cqzsl</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>hKcNvgxQOz</Id>
              <FieldId>hKcNvgxQOz</FieldId>
              <Index>10</Index>
              <BackColor>rgba(119,196,4,0.5)</BackColor>
              <Name>数量数据错误</Name>
              <Key>yd_sjcw</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>SVUbTtwQzj</Id>
              <FieldId>SVUbTtwQzj</FieldId>
              <Index>11</Index>
              <BackColor>rgba(255,153,28,0.5)</BackColor>
              <Name>平台订单总金额</Name>
              <Key>yd_ptzje</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Cx8CML24yv</Id>
              <FieldId>Cx8CML24yv</FieldId>
              <Index>12</Index>
              <BackColor>rgba(255,153,28,0.5)</BackColor>
              <Name>苍穹订单总金额</Name>
              <Key>yd_cqzje</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>RB3VvrMxYE</Id>
              <FieldId>RB3VvrMxYE</FieldId>
              <Index>13</Index>
              <BackColor>rgba(255,153,28,0.5)</BackColor>
              <Name>金额数据错误</Name>
              <Key>yd_jesjcw</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1281724361673278464</ModifierId>
      <EntityId>1WCKAGBNM+JY</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1666087249099</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_fhdzb</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>1WCKAGBNM+JY</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <DataXml>
        <EntityMetadata>
          <Id>1WCKAGBNM+JY</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>拉单对账单</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>1WCKAGBNM+JY</Id>
              <Key>yd_fhdzb</Key>
              <Name>拉单对账单</Name>
            </ReportEntity>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>xNYLaP61gk</Id>
              <Key>yd_rqgl</Key>
              <MustInput>true</MustInput>
              <Name>开始日期</Name>
            </DateField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>NIheetmm2x</Id>
              <Key>yd_thgl</Key>
              <DefValue>2</DefValue>
              <Name>退货</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>退货</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>非退货</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>KY4GLjds6P</Id>
              <Key>yd_jegl</Key>
              <DefValue>2</DefValue>
              <Name>金额判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>金额一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>金额不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Si7AtbcIPc</Id>
              <Key>yd_slgl</Key>
              <DefValue>2</DefValue>
              <Name>数量判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>数量一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>数量不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gXZ4FCs0Tc</Id>
              <Key>yd_dpgl</Key>
              <Name>店铺(支持模糊查询)</Name>
            </TextField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kWNdw4nw6d</Id>
              <Key>yd_dsgl</Key>
              <DefValue>2</DefValue>
              <Name>单数判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>单数一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>单数不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>PjXPtTK0kn</Id>
              <Key>yd_pt</Key>
              <DefValue>0</DefValue>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>不过滤</Caption>
                  <Value>0</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>3</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>6F2E71P9RP</Id>
              <Key>yd_rqgljs</Key>
              <MustInput>true</MustInput>
              <Name>结束日期</Name>
            </DateField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>95BeSjkybX</Id>
              <Key>yd_mxpt</Key>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>e3</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <CheckBoxField>
              <ShowStyle>1</ShowStyle>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>opDZr7E3Er</Id>
              <Key>yd_th</Key>
              <Name>退货</Name>
            </CheckBoxField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>75AdVQstC0</Id>
              <Key>yd_dpbh</Key>
              <Name>店铺编号</Name>
            </TextField>
            <IntegerField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>flFr6sGA38</Id>
              <Key>yd_ptzs</Key>
              <DefValue>0</DefValue>
              <Name>平台订单总单数</Name>
            </IntegerField>
            <DecimalField>
              <Scale>0</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QCeHXbnv1Q</Id>
              <Key>yd_ptzsl</Key>
              <DefValue>0</DefValue>
              <Name>平台订单物料总数量</Name>
            </DecimalField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>SVUbTtwQzj</Id>
              <Key>yd_ptzje</Key>
              <DefValue>0</DefValue>
              <Name>平台订单总金额</Name>
            </AmountField>
            <IntegerField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>F7Ijmn4ets</Id>
              <Key>yd_cqzs</Key>
              <DefValue>0</DefValue>
              <Name>苍穹订单总单数</Name>
            </IntegerField>
            <DecimalField>
              <Scale>0</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gEEj77uPzn</Id>
              <Key>yd_cqzsl</Key>
              <DefValue>0</DefValue>
              <Name>苍穹订单物料总数量</Name>
            </DecimalField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Cx8CML24yv</Id>
              <Key>yd_cqzje</Key>
              <DefValue>0</DefValue>
              <Name>苍穹订单总金额</Name>
            </AmountField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>hKcNvgxQOz</Id>
              <Key>yd_sjcw</Key>
              <Name>数量数据错误</Name>
            </CheckBoxField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>RB3VvrMxYE</Id>
              <Key>yd_jesjcw</Key>
              <Name>金额数据错误</Name>
            </CheckBoxField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>s3k16LSw96</Id>
              <Key>yd_dssjcw</Key>
              <Name>单数数据错误</Name>
            </CheckBoxField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>LYtv0VfkGs</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>组织</Name>
              <Key>yd_zzgl</Key>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gWeEKjzneH</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>组织</Name>
              <Key>yd_zz</Key>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Gaz16IC6Q5</Id>
              <Key>yd_dpmc</Key>
              <Name>店铺名称</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>kingdee</Isv>
      <Version>1666087249347</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_fhdzb</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>1WCKAGBNM+JY</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1666087249099</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
