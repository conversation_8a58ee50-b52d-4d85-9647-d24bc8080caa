<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>2YDYIBPWFA8/</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>2YDYIBPWFA8/</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1679451073000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>2YDYIBPWFA8/</Id>
          <Key>yd_goodstartsettle</Key>
          <EntityId>2YDYIBPWFA8/</EntityId>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>开门红业务结算关系</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BasedataFormAp action="edit" oid="1942c188000064ac">
              <Rules>
                <FormRule>
                  <FalseActions>
                    <HideFieldAction>
                      <RET>251</RET>
                      <ActionType>HideFieldAction</ActionType>
                      <Description>隐藏字段</Description>
                      <Id>2YHG99LZW=8G</Id>
                      <Fields>
                        <FieldId>
                          <Id>zU4FLO1S4V</Id>
                        </FieldId>
                      </Fields>
                    </HideFieldAction>
                  </FalseActions>
                  <TrueActions>
                    <ShowFieldAction>
                      <RET>251</RET>
                      <ActionType>ShowFieldAction</ActionType>
                      <Description>显示字段</Description>
                      <Id>2YHG99LZWO13</Id>
                      <Fields>
                        <FieldId>
                          <Id>zU4FLO1S4V</Id>
                        </FieldId>
                      </Fields>
                    </ShowFieldAction>
                  </TrueActions>
                  <Description>是否显示品牌</Description>
                  <Id>2YHG+LO46//3</Id>
                  <PreCondition>yd_issplit = true</PreCondition>
                  <PreDescription>是否显示品牌</PreDescription>
                </FormRule>
              </Rules>
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>2YDYIBPWFA8/</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>2YDYIBPWFA8/</Id>
              <Grow action="reset"/>
              <BackColor>#E2E7EF</BackColor>
              <Name>开门红业务结算关系</Name>
              <Key>yd_goodstartsettle</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>开门红业务结算关系</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000024ac">
                      <Name>开门红业务结算关系</Name>
                    </FormAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>2YDYIBPWFA8/</EntityId>
                    </BillListAp>
                    <ComboListColumnAp>
                      <ColumnOrderAndFilter>false</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YE+1IPLVSRW</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>status</ListFieldId>
                      <Name>数据状态</Name>
                    </ComboListColumnAp>
                    <CheckBoxListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YHJ+P278D3G</Id>
                      <Key>yd_checkboxlistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_issplit</ListFieldId>
                      <Name>是否按品牌拆分链路</Name>
                    </CheckBoxListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>false</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YE+6XQF=9SK</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_brand.name</ListFieldId>
                      <Name>品牌.名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>false</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YE+A+IPF=OK</Id>
                      <Key>yd_listcolumnap3</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_settleorg.number</ListFieldId>
                      <Name>结算组织.编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>false</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YE+A0O3+R4U</Id>
                      <Key>yd_listcolumnap4</Key>
                      <Order>NotOrder</Order>
                      <Index>8</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_settleorg.name</ListFieldId>
                      <Name>结算组织.名称</Name>
                    </ListColumnAp>
                    <ComboListColumnAp>
                      <ColumnOrderAndFilter>false</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YE+8D0NP/MJ</Id>
                      <Key>yd_combolistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>9</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_orglevel</ListFieldId>
                      <Name>级次</Name>
                    </ComboListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>false</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YE+8X6/S552</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>10</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_org.number</ListFieldId>
                      <Name>组织.编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>false</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YE+9U6SGBP=</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>11</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_org.name</ListFieldId>
                      <Name>组织.名称</Name>
                    </ListColumnAp>
                    <DateListColumnAp>
                      <ColumnOrderAndFilter>false</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2YE+=6/KAEPB</Id>
                      <Key>yd_datelistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>12</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>createtime</ListFieldId>
                      <Name>创建时间</Name>
                    </DateListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <EntryFieldAp>
              <Id>jRyvxBlo8b</Id>
              <FieldId>jRyvxBlo8b</FieldId>
              <Name>级次</Name>
              <Key>yd_orglevel</Key>
              <ParentId>13zqsSmoP8</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>M22GU7XlrH</Id>
              <FieldId>M22GU7XlrH</FieldId>
              <Index>1</Index>
              <Name>组织</Name>
              <Key>yd_org</Key>
              <ParentId>13zqsSmoP8</ParentId>
            </EntryFieldAp>
            <FlexPanelAp action="edit" oid="LL4BVAnADa">
              <Index action="reset"/>
            </FlexPanelAp>
            <FlexPanelAp action="edit" oid="PxNfkkak4s">
              <Index>1</Index>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                  <Padding>
                    <Padding>
                      <Left>2px</Left>
                      <Right action="reset"/>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newlevel</OperationKey>
              <Id>H0Mh1SZNPt</Id>
              <Key>yd_newlevel</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>AlVSZoBV2x</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deletelevel</OperationKey>
              <Id>ArJFRhLroy</Id>
              <Key>yd_deletelevel</Key>
              <OperationStyle>0</OperationStyle>
              <Index>1</Index>
              <ParentId>AlVSZoBV2x</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <AdvConSummaryPanelAp>
              <Id>bMqFmKZY0L</Id>
              <Name>高级面板摘要容器</Name>
              <Key>yd_advconsummarypanelap1</Key>
              <ParentId>F3QDQcyYUQ</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>AlVSZoBV2x</Id>
              <Key>yd_advcontoolbarap1</Key>
              <Index>1</Index>
              <ParentId>F3QDQcyYUQ</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>IxPnLwIgGE</Id>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>yd_advconchildpanelap1</Key>
              <ParentId>F3QDQcyYUQ</ParentId>
            </AdvConChildPanelAp>
            <FieldAp>
              <Id>fMOYNGzsK1</Id>
              <FieldId>fMOYNGzsK1</FieldId>
              <Index>2</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>结算组织</Name>
              <Key>yd_settleorg</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <FireUpdEvt>true</FireUpdEvt>
              <Id>QGlblNkd3O</Id>
              <FieldId>QGlblNkd3O</FieldId>
              <Index>3</Index>
              <Name>是否按品牌拆分链路</Name>
              <Key>yd_issplit</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <FireUpdEvt>true</FireUpdEvt>
              <Id>zU4FLO1S4V</Id>
              <FieldId>zU4FLO1S4V</FieldId>
              <Index>4</Index>
              <Name>品牌</Name>
              <Key>yd_brand</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp action="edit" oid="AkId5S4yTs">
              <Index>5</Index>
            </FieldAp>
            <FieldAp action="edit" oid="jiRpZNc99A">
              <Index>6</Index>
            </FieldAp>
            <FieldAp action="edit" oid="CIs7Psqw3q">
              <Index>7</Index>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp action="edit" oid="ac5Y5Dax1q">
              <Index>8</Index>
            </FieldAp>
            <FieldAp action="edit" oid="O75mQrM58q">
              <Index>9</Index>
            </FieldAp>
            <EntryAp>
              <EntryId>13zqsSmoP8</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>13zqsSmoP8</Id>
              <Name>结算路径</Name>
              <PageType/>
              <Key>yd_levelentry</Key>
              <ParentId>IxPnLwIgGE</ParentId>
            </EntryAp>
            <FieldsetPanelAp action="edit" oid="I8M9X0FOPr">
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Left>10px</Left>
                      <Right>10px</Right>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>F3QDQcyYUQ</Id>
              <Key>yd_advconap1</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>PxNfkkak4s</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>结算路径</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                      <Left>10px</Left>
                      <Right>10px</Right>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338616377744819200</ModifierId>
      <EntityId>2YDYIBPWFA8/</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1679451072798</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_goodstartsettle</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>2YDYIBPWFA8/</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1679451073000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>2YDYIBPWFA8/</Id>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>开门红业务结算关系</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BaseEntity action="edit" oid="1942c188000066ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Rules>
                <BizRule>
                  <TrueActions>
                    <ClearValueAction>
                      <RET>253</RET>
                      <ActionType>ClearValueAction</ActionType>
                      <Expression>yd_brand</Expression>
                      <Description>清除指定字段值</Description>
                      <Id>2YHL75Z2TGIU</Id>
                    </ClearValueAction>
                  </TrueActions>
                  <Description>不是拆分清除品牌</Description>
                  <Id>2YHL70S520YM</Id>
                  <PreCondition>yd_issplit = false</PreCondition>
                  <PreDescription>不是拆分清除品牌</PreDescription>
                </BizRule>
              </Rules>
              <TableName>tk_yd_goodstartsettle</TableName>
              <Operations>
                <Operation action="edit" oid="b599405400001aac">
                  <Validations>
                    <ConditionValidation>
                      <Expression>yd_issplit = true AND yd_brand = null</Expression>
                      <TrueThrow>true</TrueThrow>
                      <LevelId>2</LevelId>
                      <RuleType>FormValidate</RuleType>
                      <Description>校验是否需要输入品牌</Description>
                      <Id>2YHG8UOV1SB2</Id>
                      <Message>请输入品牌</Message>
                      <Enabled>true</Enabled>
                    </ConditionValidation>
                  </Validations>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <NewEntryParameter>
                      <EntryId>xcq3vKhiyn</EntryId>
                    </NewEntryParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>2YDZ5K0M6D7W</Id>
                  <SuccessMsg/>
                  <Key>newsettle</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>xcq3vKhiyn</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>2YDZ7GP0PTS2</Id>
                  <SuccessMsg/>
                  <Key>deletesettle</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <NewEntryParameter>
                      <EntryId>13zqsSmoP8</EntryId>
                    </NewEntryParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>2YEFPZVU2BB/</Id>
                  <SuccessMsg/>
                  <Key>newlevel</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>13zqsSmoP8</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>2YEFTN6VJL22</Id>
                  <SuccessMsg/>
                  <Key>deletelevel</Key>
                </Operation>
              </Operations>
              <Id>2YDYIBPWFA8/</Id>
              <Key>yd_goodstartsettle</Key>
              <NetworkControl>
                <NetCtrlOperation>
                  <OperationKey>submit</OperationKey>
                  <GroupId>default_netctrl</GroupId>
                  <Id>c91d5125000034ac</Id>
                  <Key>default_netctrl_submit</Key>
                </NetCtrlOperation>
              </NetworkControl>
              <Name>开门红业务结算关系</Name>
            </BaseEntity>
            <MuliLangTextField action="edit" oid="8oq6R4m9CF">
              <GL>true</GL>
            </MuliLangTextField>
            <EntryEntity>
              <TableName>tk_yd_goodstartsettle_le</TableName>
              <Id>13zqsSmoP8</Id>
              <Key>yd_levelentry</Key>
              <Name>结算路径</Name>
            </EntryEntity>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>zU4FLO1S4V</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>品牌</Name>
              <FieldName>fk_yd_brandid</FieldName>
              <Key>yd_brand</Key>
              <RefLayout/>
              <BaseEntityId>2+/KBC98TKNE</BaseEntityId>
            </BasedataField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>fMOYNGzsK1</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>结算组织</Name>
              <FieldName>fk_yd_settleorgid</FieldName>
              <Key>yd_settleorg</Key>
              <MustInput>true</MustInput>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
            <CheckBoxField>
              <FieldName>fk_yd_issplit</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QGlblNkd3O</Id>
              <Key>yd_issplit</Key>
              <Name>是否按品牌拆分链路</Name>
            </CheckBoxField>
            <ComboField>
              <FieldName>fk_yd_orglevel</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>jRyvxBlo8b</Id>
              <Key>yd_orglevel</Key>
              <MustInput>true</MustInput>
              <ParentId>13zqsSmoP8</ParentId>
              <Name>级次</Name>
              <Items>
                <ComboItem>
                  <Caption>一级组织</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>二级组织</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>三级组织</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>四级组织</Caption>
                  <Value>4</Value>
                  <Seq>3</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>五级组织</Caption>
                  <Value>5</Value>
                  <Seq>4</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>M22GU7XlrH</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>组织</Name>
              <FieldName>fk_yd_orgid</FieldName>
              <Key>yd_org</Key>
              <MustInput>true</MustInput>
              <ParentId>13zqsSmoP8</ParentId>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv></Isv>
      <Version>1679451072842</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_goodstartsettle</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>2YDYIBPWFA8/</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1679451072798</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
