<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>223QH=22F32M</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4YBIQY1ZAH/M</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>2</DevType>
      <ModifyDate>1746543947000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <DevType>2</DevType>
          <Id>4YBIQY1ZAH/M</Id>
          <Key>yd_innerrelbill_ext_ec</Key>
          <EntityId>4YBIQY1ZAH/M</EntityId>
          <ParentId>223QH=22F32M</ParentId>
          <MasterId>223QH=22F32M</MasterId>
          <InheritPath>00305e8b000006ac,223QH=22F32M</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4YBIQY1ZAH/M</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>4YBIQY1ZAH/M</Id>
              <Key>yd_innerrelbill_ext_ec</Key>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4YBIQY1ZAH/M</EntityId>
                    </BillListAp>
                    <ListColumnAp action="edit" oid="37/P=L7Y1=P=">
                      <ColumnOrderAndFilter action="reset"/>
                    </ListColumnAp>
                    <ListColumnAp action="edit" oid="23TG794JT0/7">
                      <ColumnOrderAndFilter action="reset"/>
                    </ListColumnAp>
                    <ListColumnAp action="edit" oid="23TG7C3N/KAL">
                      <ColumnOrderAndFilter action="reset"/>
                    </ListColumnAp>
                    <ComboListColumnAp action="edit" oid="22MG/BLVLG+X">
                      <ColumnOrderAndFilter action="reset"/>
                    </ComboListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338618754925331456</ModifierId>
      <EntityId>4YBIQY1ZAH/M</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1746543946948</Version>
      <ParentId>223QH=22F32M</ParentId>
      <MasterId>223QH=22F32M</MasterId>
      <Number>yd_innerrelbill_ext_ec</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4YBIQY1ZAH/M</Id>
      <InheritPath>00305e8b000006ac,223QH=22F32M</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>2</DevType>
      <ModifyDate>1746543947000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <DevType>2</DevType>
          <Isv>yd</Isv>
          <Id>4YBIQY1ZAH/M</Id>
          <ParentId>223QH=22F32M</ParentId>
          <MasterId>223QH=22F32M</MasterId>
          <InheritPath>00305e8b000006ac,223QH=22F32M</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Id>4YBIQY1ZAH/M</Id>
              <Key>yd_innerrelbill_ext_ec</Key>
            </BillEntity>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1746543946981</Version>
      <ParentId>223QH=22F32M</ParentId>
      <MasterId>223QH=22F32M</MasterId>
      <Number>yd_innerrelbill_ext_ec</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4YBIQY1ZAH/M</Id>
      <InheritPath>00305e8b000006ac,223QH=22F32M</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1746543946948</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
