<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>1X52SLQ27+LO</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>1X52SLQ27+LO</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1744301648000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>1X52SLQ27+LO</Id>
          <Key>yd_khdygx</Key>
          <EntityId>1X52SLQ27+LO</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>客户对应关系</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>1X52SLQ27+LO</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>1X52SLQ27+LO</Id>
              <Name>客户对应关系</Name>
              <Key>yd_khdygx</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>客户对应关系列表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Name>客户对应关系列表</Name>
                    </FormAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>1X52SLQ27+LO</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <FlexPanelAp action="edit" oid="UFv60F0FiX">
              <Index>1</Index>
            </FlexPanelAp>
            <EntryAp>
              <EntryId>yTrnfGhPF7</EntryId>
              <Id>yTrnfGhPF7</Id>
              <Name>单据体</Name>
              <PageType/>
              <Key>yd_entryentity</Key>
              <SplitPage>true</SplitPage>
              <ParentId>HMJtOvGLTW</ParentId>
            </EntryAp>
            <AdvConSummaryPanelAp>
              <Id>oY3W1O62ty</Id>
              <Name>高级面板摘要容器</Name>
              <Key>yd_advconsummarypanelap</Key>
              <ParentId>K9o970wNFF</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>ObqSCjpabc</Id>
              <Key>yd_advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>K9o970wNFF</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>HMJtOvGLTW</Id>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>yd_advconchildpanelap</Key>
              <ParentId>K9o970wNFF</ParentId>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>88AOgqfTq7</Id>
              <Key>yd_advconbaritemap_xz</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>ObqSCjpabc</ParentId>
              <Name>新增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>5m2tK6jKX7</Id>
              <Key>yd_advconbaritemap_sc</Key>
              <OperationStyle>0</OperationStyle>
              <Index>1</Index>
              <ParentId>ObqSCjpabc</ParentId>
              <Name>删除行</Name>
            </AdvConBarItemAp>
            <FieldAp>
              <Id>pdBnwsmGJy</Id>
              <FieldId>pdBnwsmGJy</FieldId>
              <Index>4</Index>
              <Name>平台</Name>
              <Key>yd_combofield_pt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>fq88jRLK6v</Id>
              <FieldId>fq88jRLK6v</FieldId>
              <Index>5</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>后台计划拉取天数</Name>
              <Key>yd_integerfield_ts</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>6kFYCaifpj</Id>
              <FieldId>6kFYCaifpj</FieldId>
              <Index>6</Index>
              <Name>同步eas状态到发货明细天数</Name>
              <Key>yd_easqrts</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>K9o970wNFF</Id>
              <Key>yd_advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>3</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <Name>明细信息</Name>
            </AdvConAp>
            <EntryFieldAp>
              <Id>QVp5LpdTq8</Id>
              <FieldId>QVp5LpdTq8</FieldId>
              <Name>平台店铺</Name>
              <Key>yd_textfield</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>b6pLk1Ur3o</Id>
              <FieldId>b6pLk1Ur3o</FieldId>
              <Index>1</Index>
              <Name>平台店铺名称</Name>
              <Key>yd_textfield_dpmc</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>LH3qizoOPR</Id>
              <FieldId>LH3qizoOPR</FieldId>
              <Index>2</Index>
              <Name>苍穹客户</Name>
              <Key>yd_basedatafield</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>fK8PiuQiRl</Id>
              <FieldId>fK8PiuQiRl</FieldId>
              <Index>3</Index>
              <Name>苍穹客户编码</Name>
              <Key>yd_basedatapropfield_khbm</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>nlQ6P5lHYH</Id>
              <FieldId>nlQ6P5lHYH</FieldId>
              <Index>4</Index>
              <Name>对应组织</Name>
              <Key>yd_orgfield_dyzz</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>gFzmTpGInb</Id>
              <FieldId>gFzmTpGInb</FieldId>
              <Index>5</Index>
              <Name>汇总类型</Name>
              <Key>yd_combofield_hzcl</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>KOL0PjT128</Id>
              <FieldId>KOL0PjT128</FieldId>
              <Index>6</Index>
              <Name>库存事务</Name>
              <Key>yd_kcsw</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>QlarLWoFaI</Id>
              <FieldId>QlarLWoFaI</FieldId>
              <Index>7</Index>
              <Name>退货库存事务</Name>
              <Key>yd_thkcsw</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>8Ncl7uvk1X</Id>
              <FieldId>8Ncl7uvk1X</FieldId>
              <Index>8</Index>
              <Name>是否按照品牌分单</Name>
              <Key>yd_ppfd</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Cp2UWASZtL</Id>
              <FieldId>Cp2UWASZtL</FieldId>
              <Index>9</Index>
              <Name>是否分摊运费</Name>
              <Key>yd_isshare</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>UscVokhIps</Id>
              <FieldId>UscVokhIps</FieldId>
              <Index>10</Index>
              <Name>其他出库区域</Name>
              <Key>yd_quyu</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>NUi4pewSAm</Id>
              <FieldId>NUi4pewSAm</FieldId>
              <Index>11</Index>
              <Name>计算方式</Name>
              <Key>yd_count</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338618754925331456</ModifierId>
      <EntityId>1X52SLQ27+LO</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1744301648015</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_khdygx</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>1X52SLQ27+LO</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1744301648000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>1X52SLQ27+LO</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>客户对应关系</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_khdygx</TableName>
              <Operations>
                <Operation action="edit" oid="c91d5125000033ac">
                  <Parameter>
                    <SaveParameter>
                      <StatusFieldId>6q69iznvHX</StatusFieldId>
                    </SaveParameter>
                  </Parameter>
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <Description>对应店铺不能重复</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>1X9+UQO62ALT</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_combofield_pt</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_textfield</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation action="edit" oid="c91d5125000034ac">
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <Description>平台店铺不能重复</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>26NX44SQWL0E</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_combofield_pt</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_textfield</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <NewEntryParameter>
                      <EntryId>yTrnfGhPF7</EntryId>
                    </NewEntryParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>1X5393Z9P88Z</Id>
                  <SuccessMsg/>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>yTrnfGhPF7</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>1X53=/7845KE</Id>
                  <SuccessMsg/>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>1X52SLQ27+LO</Id>
              <Key>yd_khdygx</Key>
              <Name>客户对应关系</Name>
            </BillEntity>
            <ComboField>
              <FieldName>fk_yd_combofield_pt</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pdBnwsmGJy</Id>
              <Key>yd_combofield_pt</Key>
              <MustInput>true</MustInput>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>万里牛</Caption>
                  <Value>4</Value>
                  <Seq>3</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <EntryEntity>
              <TableName>tk_yd_entryentitydykh</TableName>
              <Id>yTrnfGhPF7</Id>
              <Key>yd_entryentity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <IntegerField>
              <FieldName>fk_yd_integerfield_ts</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>fq88jRLK6v</Id>
              <Key>yd_integerfield_ts</Key>
              <DefValue>0</DefValue>
              <Name>后台计划拉取天数</Name>
            </IntegerField>
            <IntegerField>
              <FieldName>fk_yd_integerfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>6kFYCaifpj</Id>
              <Key>yd_easqrts</Key>
              <DefValue>0</DefValue>
              <Name>同步eas状态到发货明细天数</Name>
            </IntegerField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>LH3qizoOPR</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>苍穹客户</Name>
              <FieldName>fk_yd_basedatafield</FieldName>
              <Key>yd_basedatafield</Key>
              <RefLayout/>
              <ParentId>yTrnfGhPF7</ParentId>
              <BaseEntityId>a86c9c130002f7ac</BaseEntityId>
            </BasedataField>
            <TextField>
              <FieldName>fk_yd_textfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QVp5LpdTq8</Id>
              <Key>yd_textfield</Key>
              <MustInput>true</MustInput>
              <ParentId>yTrnfGhPF7</ParentId>
              <Name>平台店铺</Name>
            </TextField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>nlQ6P5lHYH</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>对应组织</Name>
              <FieldName>fk_yd_orgfield_dyzz</FieldName>
              <Key>yd_orgfield_dyzz</Key>
              <MustInput>true</MustInput>
              <ParentId>yTrnfGhPF7</ParentId>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
            <ComboField>
              <FieldName>fk_yd_combofield_hzcl</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gFzmTpGInb</Id>
              <Key>yd_combofield_hzcl</Key>
              <MustInput>true</MustInput>
              <ParentId>yTrnfGhPF7</ParentId>
              <Name>汇总类型</Name>
              <Items>
                <ComboItem>
                  <Caption>正常发货汇总</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>正常发货不汇总</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>不转EAS</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>转其他出库汇总</Caption>
                  <Value>4</Value>
                  <Seq>3</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>转其他出库不汇总</Caption>
                  <Value>5</Value>
                  <Seq>4</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>fK8PiuQiRl</Id>
              <Key>yd_basedatapropfield_khbm</Key>
              <RefDisplayProp>number</RefDisplayProp>
              <ParentId>yTrnfGhPF7</ParentId>
              <RefBaseFieldId>LH3qizoOPR</RefBaseFieldId>
              <Name>苍穹客户编码</Name>
            </BasedataPropField>
            <TextField>
              <FieldName>fk_yd_textfield_dpmc</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>b6pLk1Ur3o</Id>
              <Key>yd_textfield_dpmc</Key>
              <ParentId>yTrnfGhPF7</ParentId>
              <Name>平台店铺名称</Name>
            </TextField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>KOL0PjT128</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>库存事务</Name>
              <FieldName>fk_yd_kcsw</FieldName>
              <Key>yd_kcsw</Key>
              <RefLayout/>
              <ParentId>yTrnfGhPF7</ParentId>
              <Filter>
                <FilterCondition>
                  <Filter>单据.编码 等于 im_otheroutbill</Filter>
                  <FilterRow>
                    <SimpleFilterRow>
                      <CompareType>67</CompareType>
                      <FieldName>billform.number</FieldName>
                      <RightBracket/>
                      <Id>20I23MO6FVP4</Id>
                      <LeftBracket/>
                      <Logic>0</Logic>
                      <Value>
                        <FilterValue>
                          <Id>20I23MO6FV/W</Id>
                          <Value>im_otheroutbill</Value>
                        </FilterValue>
                      </Value>
                      <BaseDataIds>
                        <FilterValue>
                          <Id>20I23MO6FVP5</Id>
                          <Value>im_otheroutbill</Value>
                        </FilterValue>
                      </BaseDataIds>
                    </SimpleFilterRow>
                  </FilterRow>
                </FilterCondition>
              </Filter>
              <BaseEntityId>139Y8ZXZEHMZ</BaseEntityId>
            </BasedataField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>UscVokhIps</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>其他出库区域</Name>
              <FieldName>fk_yd_quyu</FieldName>
              <Key>yd_quyu</Key>
              <RefLayout/>
              <ParentId>yTrnfGhPF7</ParentId>
              <BaseEntityId>2/SP/IXDJVAS</BaseEntityId>
            </BasedataField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QlarLWoFaI</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>退货库存事务</Name>
              <FieldName>fk_yd_thkcsw</FieldName>
              <Key>yd_thkcsw</Key>
              <RefLayout/>
              <ParentId>yTrnfGhPF7</ParentId>
              <Filter>
                <FilterCondition>
                  <Filter>单据.编码 等于 im_otheroutbill</Filter>
                  <FilterRow>
                    <SimpleFilterRow>
                      <CompareType>67</CompareType>
                      <FieldName>billform.number</FieldName>
                      <RightBracket/>
                      <Id>20I240OB/16F</Id>
                      <LeftBracket/>
                      <Logic>0</Logic>
                      <Value>
                        <FilterValue>
                          <Id>20I240OB/1VP</Id>
                          <Value>im_otheroutbill</Value>
                        </FilterValue>
                      </Value>
                      <BaseDataIds>
                        <FilterValue>
                          <Id>20I240OB/16G</Id>
                          <Value>im_otheroutbill</Value>
                        </FilterValue>
                      </BaseDataIds>
                    </SimpleFilterRow>
                  </FilterRow>
                </FilterCondition>
              </Filter>
              <BaseEntityId>139Y8ZXZEHMZ</BaseEntityId>
            </BasedataField>
            <CheckBoxField>
              <FieldName>fk_yd_ppfd</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>8Ncl7uvk1X</Id>
              <Key>yd_ppfd</Key>
              <ParentId>yTrnfGhPF7</ParentId>
              <Name>是否按照品牌分单</Name>
            </CheckBoxField>
            <ComboField>
              <FieldName>fk_yd_count</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>NUi4pewSAm</Id>
              <Key>yd_count</Key>
              <ParentId>yTrnfGhPF7</ParentId>
              <Name>计算方式</Name>
              <Items>
                <ComboItem>
                  <Caption>laz</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>虾皮</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>亚马逊</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <CheckBoxField>
              <FieldName>fk_yd_isshare</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Cp2UWASZtL</Id>
              <Key>yd_isshare</Key>
              <ParentId>yTrnfGhPF7</ParentId>
              <Name>是否分摊运费</Name>
            </CheckBoxField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv></Isv>
      <Version>1744301648049</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_khdygx</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>1X52SLQ27+LO</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1744301648015</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
