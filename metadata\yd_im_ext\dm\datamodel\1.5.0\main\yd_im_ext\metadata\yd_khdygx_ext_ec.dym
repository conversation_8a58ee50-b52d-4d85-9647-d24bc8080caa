<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>1X52SLQ27+LO</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4YA6C7L651ZR</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>2</DevType>
      <ModifyDate>1746677128000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <DevType>2</DevType>
          <Id>4YA6C7L651ZR</Id>
          <Key>yd_khdygx_ext_ec</Key>
          <EntityId>4YA6C7L651ZR</EntityId>
          <ParentId>1X52SLQ27+LO</ParentId>
          <MasterId>1X52SLQ27+LO</MasterId>
          <InheritPath>00305e8b000006ac,1X52SLQ27+LO</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4YA6C7L651ZR</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>4YA6C7L651ZR</Id>
              <Key>yd_khdygx_ext_ec</Key>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4YA6C7L651ZR</EntityId>
                    </BillListAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI7R5BLM/4</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_combofield_pt</ListFieldId>
                      <Name>平台</Name>
                    </ComboListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>EntryEntitySeq</SeqColumnType>
                      <Id>4YBI8/3EO/W=</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_entryentity.seq</ListFieldId>
                      <Name>分录行号</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI82OLU6FB</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_basedatafield.number</ListFieldId>
                      <Name>苍穹客户编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI84JHN2=0</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_basedatafield.name</ListFieldId>
                      <Name>苍穹客户.名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI8YMBAR34</Id>
                      <Key>yd_listcolumnap7</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_platformstock</ListFieldId>
                      <Name>平台仓库</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI8AAQTOYU</Id>
                      <Key>yd_listcolumnap3</Key>
                      <Order>NotOrder</Order>
                      <Index>8</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_textfield</ListFieldId>
                      <Name>平台店铺</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI8KCTSZ30</Id>
                      <Key>yd_listcolumnap6</Key>
                      <Order>NotOrder</Order>
                      <Index>9</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_textfield_dpmc</ListFieldId>
                      <Name>平台店铺名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI8CK7XS00</Id>
                      <Key>yd_listcolumnap4</Key>
                      <Order>NotOrder</Order>
                      <Index>10</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_orgfield_dyzz.number</ListFieldId>
                      <Name>对应组织.编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI8EQ2KWZZ</Id>
                      <Key>yd_listcolumnap5</Key>
                      <Order>NotOrder</Order>
                      <Index>11</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_orgfield_dyzz.name</ListFieldId>
                      <Name>对应组织.名称</Name>
                    </ListColumnAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YBI8SX37GS7</Id>
                      <Key>yd_combolistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>12</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_combofield_hzcl</ListFieldId>
                      <Name>汇总类型</Name>
                    </ComboListColumnAp>
                    <CheckBoxListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YGWHETZBMUH</Id>
                      <Key>yd_checkboxlistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>13</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_ppfd</ListFieldId>
                      <Name>是否按照品牌分单</Name>
                      <Width>120px</Width>
                    </CheckBoxListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <EntryFieldAp>
              <Id>GRDSe4kCeo</Id>
              <FieldId>GRDSe4kCeo</FieldId>
              <Index>4</Index>
              <Name>平台仓库</Name>
              <Key>yd_platformstock</Key>
              <ParentId>yTrnfGhPF7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp action="edit" oid="nlQ6P5lHYH">
              <Index>5</Index>
            </EntryFieldAp>
            <EntryFieldAp action="edit" oid="gFzmTpGInb">
              <Index>6</Index>
            </EntryFieldAp>
            <EntryFieldAp action="edit" oid="KOL0PjT128">
              <Index>7</Index>
            </EntryFieldAp>
            <EntryFieldAp action="edit" oid="QlarLWoFaI">
              <Index>8</Index>
            </EntryFieldAp>
            <EntryFieldAp action="edit" oid="8Ncl7uvk1X">
              <Index>9</Index>
            </EntryFieldAp>
            <EntryFieldAp action="edit" oid="Cp2UWASZtL">
              <Index>10</Index>
            </EntryFieldAp>
            <EntryFieldAp action="edit" oid="UscVokhIps">
              <Index>11</Index>
            </EntryFieldAp>
            <EntryFieldAp action="edit" oid="NUi4pewSAm">
              <Index>12</Index>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338618754925331456</ModifierId>
      <EntityId>4YA6C7L651ZR</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1746677128467</Version>
      <ParentId>1X52SLQ27+LO</ParentId>
      <MasterId>1X52SLQ27+LO</MasterId>
      <Number>yd_khdygx_ext_ec</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4YA6C7L651ZR</Id>
      <InheritPath>00305e8b000006ac,1X52SLQ27+LO</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>2</DevType>
      <ModifyDate>1746677128000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <DevType>2</DevType>
          <Isv>yd</Isv>
          <Id>4YA6C7L651ZR</Id>
          <ParentId>1X52SLQ27+LO</ParentId>
          <MasterId>1X52SLQ27+LO</MasterId>
          <InheritPath>00305e8b000006ac,1X52SLQ27+LO</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Operations>
                <Operation action="edit" oid="c91d5125000033ac">
                  <Validations>
                    <GrpfieldsuniqueValidation action="edit" oid="1X9+UQO62ALT">
                      <CustomPromp/>
                      <Fields>
                        <FieldId>
                          <Id>yd_platformstock</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation action="edit" oid="c91d5125000034ac">
                  <Validations>
                    <GrpfieldsuniqueValidation action="edit" oid="26NX44SQWL0E">
                      <CustomPromp/>
                      <Fields>
                        <FieldId>
                          <Id>yd_platformstock</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
              </Operations>
              <Id>4YA6C7L651ZR</Id>
              <Key>yd_khdygx_ext_ec</Key>
            </BillEntity>
            <TextField>
              <FieldName>fk_yd_platformstock</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>GRDSe4kCeo</Id>
              <Key>yd_platformstock</Key>
              <ParentId>yTrnfGhPF7</ParentId>
              <Name>平台仓库</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1746677128499</Version>
      <ParentId>1X52SLQ27+LO</ParentId>
      <MasterId>1X52SLQ27+LO</MasterId>
      <Number>yd_khdygx_ext_ec</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4YA6C7L651ZR</Id>
      <InheritPath>00305e8b000006ac,1X52SLQ27+LO</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1746677128467</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
