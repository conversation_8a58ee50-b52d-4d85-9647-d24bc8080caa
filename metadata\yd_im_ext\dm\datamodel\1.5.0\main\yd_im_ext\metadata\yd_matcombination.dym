<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>2EPZ+65CRH83</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>2EPZ+65CRH83</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1744701496000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>2EPZ+65CRH83</Id>
          <Key>yd_matcombination</Key>
          <EntityId>2EPZ+65CRH83</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>E3主商品拆分表</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <Plugins>
                <Plugin action="edit" oid="kd.bos.form.plugin.TemplateBillEdit">
                  <FPK/>
                  <Description/>
                  <BizAppId/>
                </Plugin>
                <Plugin action="edit" oid="kd.bos.form.plugin.CodeRulePlugin">
                  <FPK/>
                  <Description/>
                  <BizAppId/>
                  <RowKey>1</RowKey>
                </Plugin>
                <Plugin>
                  <FPK/>
                  <Description>主商品拆分编辑界面插件——校验比例和是否为100%</Description>
                  <ClassName>kd.bos.tcbj.im.price.plugin.MatCombinationEdit</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                  <RowKey>2</RowKey>
                </Plugin>
              </Plugins>
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>2EPZ+740=MKL</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2EPZ+740=N8W</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>2EPZ+740=MKL</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2EPZ+743Y0S1</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>2EPZ+740=MKL</ParentId>
                      <ListFieldId>creator.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>创建人.姓名</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>2EPZ+65CRH83</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>2EPZ+740=MKL</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Direction>column</Direction>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>2EPZ+740=N8V</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>2EPZ+65CRH83</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>E3主商品拆分表</Name>
              <Key>yd_matcombination</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>2EPZ+73YO81C</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>2EPZ+73YO7D3</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp>
                      <Id>2EPZ+740=MKJ</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>2EPZ+73YO7D3</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>2EPZ+740=N8U</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>2EPZ+73YO7D3</ParentId>
                    </FieldAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>2EPZ+73YO81C</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>2EPZ+740=N8T</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>2EPZ+73YO81C</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>2EPZ+740=MKK</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Name>E3主商品拆分表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Name>E3主商品拆分表</Name>
                    </FormAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <Name>E3主商品拆分表列表</Name>
                      <EntityId>2EPZ+65CRH83</EntityId>
                    </BillListAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2FWR+A2Z+OWT</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_mainmatnum</ListFieldId>
                      <Name>主商品编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2FWR+BA2D5WJ</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_mainmatname</ListFieldId>
                      <Name>主商品名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2FWR+IHDW/BZ</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_material.number</ListFieldId>
                      <Name>物料编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2FWR+K+T/QNR</Id>
                      <Key>yd_listcolumnap3</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_material.name</ListFieldId>
                      <Name>物料.名称</Name>
                    </ListColumnAp>
                    <DecimalListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2FWR/+K1JROC</Id>
                      <Key>yd_decimallistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_qty</ListFieldId>
                      <Name>组成数量</Name>
                    </DecimalListColumnAp>
                    <DecimalListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2FWR/1JK+/Q6</Id>
                      <Key>yd_decimallistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>8</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_price</ListFieldId>
                      <Name>单价</Name>
                    </DecimalListColumnAp>
                    <DecimalListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>2FWR/3OS9LFX</Id>
                      <Key>yd_decimallistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>9</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_rate</ListFieldId>
                      <Name>比例（%）</Name>
                    </DecimalListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <AdvConSummaryPanelAp>
              <Id>2EPZ+73V0TVS</Id>
              <Name>高级面板摘要容器</Name>
              <Key>advconsummarypanelap</Key>
              <ParentId>2EPZ+6Y42B6Z</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>2EPZ+73V0T6I</Id>
              <Key>advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>2EPZ+6Y42B6Z</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>2EPZ+73V0TVT</Id>
              <AlignItems>stretch</AlignItems>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>advconchildcanelap</Key>
              <Direction>column</Direction>
              <ParentId>2EPZ+6Y42B6Z</ParentId>
              <Wrap>false</Wrap>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>2EPZ+73V0T6J</Id>
              <Key>tb_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>2EPZ+73V0T6I</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>2EPZ+73YO81=</Id>
              <Key>tb_del</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>2EPZ+73V0T6I</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryAp>
              <EntryId>2EPZ+73YO7D0</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>2EPZ+73YO7D0</Id>
              <AutoRowHeight>true</AutoRowHeight>
              <Name>单据体</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>entryentity</Key>
              <ParentId>2EPZ+73V0TVT</ParentId>
            </EntryAp>
            <EntryFieldAp>
              <Id>2EPZ+73YO81A</Id>
              <FieldId>2EPZ+73YO81A</FieldId>
              <Name>修改人</Name>
              <Hidden>true</Hidden>
              <Key>modifierfield</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>2EPZ+73YO7D1</Id>
              <FieldId>2EPZ+73YO7D1</FieldId>
              <Index>1</Index>
              <Name>修改时间</Name>
              <Hidden>true</Hidden>
              <Key>modifydatefield</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>9pMwpyvgcV</Id>
              <FieldId>9pMwpyvgcV</FieldId>
              <Index>2</Index>
              <Name>主商品编码</Name>
              <Key>yd_mainmatnum</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>15AyGqdLci</Id>
              <FieldId>15AyGqdLci</FieldId>
              <Index>3</Index>
              <Name>主商品名称</Name>
              <Key>yd_mainmatname</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>mLcCG6uHWs</Id>
              <FieldId>mLcCG6uHWs</FieldId>
              <Index>4</Index>
              <Name>主商品金额</Name>
              <Key>yd_mainmatamt</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>BdRdizmRGZ</Id>
              <FieldId>BdRdizmRGZ</FieldId>
              <Index>5</Index>
              <Name>物料类型</Name>
              <Key>yd_mattype</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>tp5veqniGs</Id>
              <FieldId>tp5veqniGs</FieldId>
              <Index>6</Index>
              <Name>是否单品</Name>
              <Key>yd_issingle</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
            </EntryFieldAp>
            <EntryAp>
              <EntryId>i3UDhJH2si</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>i3UDhJH2si</Id>
              <Name>子单据体</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>yd_subentryentity</Key>
              <ParentId>8HdSQIb8f2</ParentId>
            </EntryAp>
            <AdvConSummaryPanelAp>
              <Id>7fQmmuO9c4</Id>
              <Name>高级面板摘要容器</Name>
              <Key>yd_advconsummarypanelap</Key>
              <ParentId>BKbrkp4EH3</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>cDwjAg6RT2</Id>
              <Key>yd_advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>BKbrkp4EH3</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>8HdSQIb8f2</Id>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>yd_advconchildpanelap</Key>
              <ParentId>BKbrkp4EH3</ParentId>
            </AdvConChildPanelAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <AdvConBarItemAp>
              <OperationKey>en_newentry</OperationKey>
              <Id>eyaKKi93Wo</Id>
              <Key>yd_advconbaritemap</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>cDwjAg6RT2</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>en_deleteentry</OperationKey>
              <Id>2ns3C9PsND</Id>
              <Key>yd_advconbaritemap1</Key>
              <OperationStyle>0</OperationStyle>
              <Index>1</Index>
              <ParentId>cDwjAg6RT2</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryFieldAp>
              <Id>zEAGojdYda</Id>
              <FieldId>zEAGojdYda</FieldId>
              <Name>物料编码</Name>
              <Key>yd_matnum</Key>
              <ParentId>i3UDhJH2si</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>6Q9ORRknc4</Id>
              <FieldId>6Q9ORRknc4</FieldId>
              <Index>1</Index>
              <Name>物料</Name>
              <Key>yd_material</Key>
              <ParentId>i3UDhJH2si</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ZfMWrMb3fY</Id>
              <FieldId>ZfMWrMb3fY</FieldId>
              <Index>2</Index>
              <Name>组成数量</Name>
              <Key>yd_qty</Key>
              <ParentId>i3UDhJH2si</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>WNG4p2CghV</Id>
              <FieldId>WNG4p2CghV</FieldId>
              <Index>3</Index>
              <Name>单价</Name>
              <Key>yd_price</Key>
              <ParentId>i3UDhJH2si</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Zv2uv5tgsZ</Id>
              <FieldId>Zv2uv5tgsZ</FieldId>
              <Index>4</Index>
              <DisplayFormatString/>
              <Name>比例（%）</Name>
              <Key>yd_rate</Key>
              <Summary>1</Summary>
              <ParentId>i3UDhJH2si</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Lock>submit,audit,new,edit</Lock>
              <Id>IHM74hkAdd</Id>
              <FieldId>IHM74hkAdd</FieldId>
              <Index>5</Index>
              <Name>金额</Name>
              <Key>yd_matamt</Key>
              <Summary>1</Summary>
              <ParentId>i3UDhJH2si</ParentId>
            </EntryFieldAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <FieldsetPanelAp action="edit" oid="xTQZ9d8tHa">
              <Index>1</Index>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>2EPZ+6Y42B6Z</Id>
              <Key>advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>主物料分录</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Index>3</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>BKbrkp4EH3</Id>
              <Key>yd_advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>4</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>@container-bg-color</BackColor>
              <Name>组成物料分录</Name>
            </AdvConAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338618754925331456</ModifierId>
      <EntityId>2EPZ+65CRH83</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1744701495655</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_matcombination</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>2EPZ+65CRH83</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1744701496000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>2EPZ+65CRH83</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>E3主商品拆分表</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_matcombination</TableName>
              <Operations>
                <Operation action="edit" oid="c91d5125000033ac">
                  <Parameter>
                    <SaveParameter>
                      <StatusFieldId>6q69iznvHX</StatusFieldId>
                    </SaveParameter>
                  </Parameter>
                  <Validations>
                    <MustInputValidation action="edit" oid="RS=8GBXGZY4">
                      <Enabled>true</Enabled>
                    </MustInputValidation>
                    <GrpfieldsuniqueValidation>
                      <Description>主商品字段唯一性校验</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>2G+=/IAJ=V0X</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_mainmatnum</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                    <ConditionValidation>
                      <Expression>yd_mainmatamt &lt;&gt; sum( yd_matamt )</Expression>
                      <EntityKey>entryentity</EntityKey>
                      <Description>校验合计金额</Description>
                      <Message>主商品金额与组装物料金额合计不一致，请检查！</Message>
                      <Enabled>true</Enabled>
                      <TrueThrow>true</TrueThrow>
                      <RuleType>FormValidate</RuleType>
                      <Id>2GIE6=OHMYJN</Id>
                    </ConditionValidation>
                  </Validations>
                </Operation>
                <Operation action="edit" oid="c91d5125000034ac">
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <Description>主商品编码字段唯一性校验</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>2G+=2FS=01QJ</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_mainmatnum</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                    <ConditionValidation>
                      <Expression>yd_mainmatamt &lt;&gt; sum( yd_matamt )</Expression>
                      <EntityKey>entryentity</EntityKey>
                      <Description>金额合计校验</Description>
                      <Message>主商品金额与组装物料金额合计不一致，请检查！</Message>
                      <Enabled>true</Enabled>
                      <TrueThrow>true</TrueThrow>
                      <RuleType>FormValidate</RuleType>
                      <Id>2GIEX7S5VKD1</Id>
                    </ConditionValidation>
                  </Validations>
                </Operation>
                <Operation action="edit" oid="c91d5125000035ac">
                  <Parameter>
                    <AuditParameter>
                      <CommentFieldId>9pMwpyvgcV</CommentFieldId>
                    </AuditParameter>
                  </Parameter>
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <Description>主商品字段唯一性校验</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>2G+=4/LXPFM7</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_mainmatnum</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                    <ConditionValidation>
                      <Expression>yd_mainmatamt &lt;&gt; sum( yd_matamt )</Expression>
                      <EntityKey>entryentity</EntityKey>
                      <Description>金额校验</Description>
                      <Message>主商品金额与组装物料金额合计不一致，请检查！</Message>
                      <Enabled>true</Enabled>
                      <TrueThrow>true</TrueThrow>
                      <RuleType>FormValidate</RuleType>
                      <Id>2GIEZ/COFJ82</Id>
                    </ConditionValidation>
                  </Validations>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>2EPZ+73YO7D0</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>2EPZ+73YO81B</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>2EPZ+73YO7D0</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>2EPZ+73YO7D2</Id>
                  <Key>deleteentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <NewEntryParameter>
                      <EntryId>i3UDhJH2si</EntryId>
                    </NewEntryParameter>
                  </Parameter>
                  <Name>子单据体_新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>2EPZK+NEOM/2</Id>
                  <SuccessMsg/>
                  <Key>en_newentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>i3UDhJH2si</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>子单据体_删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>2EPZQ15S1F0J</Id>
                  <SuccessMsg/>
                  <Key>en_deleteentry</Key>
                </Operation>
              </Operations>
              <Id>2EPZ+65CRH83</Id>
              <Key>yd_matcombination</Key>
              <Name>E3主商品拆分表</Name>
            </BillEntity>
            <EntryEntity>
              <TableName>tk_yd_matcombination_en</TableName>
              <Id>2EPZ+73YO7D0</Id>
              <Key>entryentity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <ModifierField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2EPZ+73YO81A</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>修改人</Name>
              <FieldName>fmodifierfield</FieldName>
              <Key>modifierfield</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
              <BaseEntityId>68bde9ca00000eac</BaseEntityId>
            </ModifierField>
            <ModifyDateField>
              <FieldName>fmodifydatefield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2EPZ+73YO7D1</Id>
              <Key>modifydatefield</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
              <Name>修改日期</Name>
            </ModifyDateField>
            <SubEntryEntity>
              <Rules>
                <BizRule>
                  <TrueActions>
                    <FormulaAction>
                      <RET>253</RET>
                      <ActionType>FormulaAction</ActionType>
                      <Expression>yd_matamt = yd_qty * yd_price</Expression>
                      <Description>计算金额</Description>
                      <Id>2GI=G=OVCPP7</Id>
                    </FormulaAction>
                  </TrueActions>
                  <Description>计算金额</Description>
                  <Id>2GI6+1UXE2J1</Id>
                  <PreCondition>yd_qty &lt;&gt; 0 or yd_price &lt;&gt; 0</PreCondition>
                  <PreDescription>单价数量不为空</PreDescription>
                </BizRule>
              </Rules>
              <TableName>tk_yd_matcomb_en_en</TableName>
              <Id>i3UDhJH2si</Id>
              <ParentEntryId>2EPZ+73YO7D0</ParentEntryId>
              <Key>yd_subentryentity</Key>
              <EntryMustInput>true</EntryMustInput>
              <ParentId>2EPZ+73YO7D0</ParentId>
              <Name>子单据体</Name>
            </SubEntryEntity>
            <TextField>
              <FieldName>fk_yd_mainmatnum</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>9pMwpyvgcV</Id>
              <Key>yd_mainmatnum</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
              <Name>主商品编码</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_mainmatname</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>15AyGqdLci</Id>
              <Key>yd_mainmatname</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
              <Name>主商品名称</Name>
            </TextField>
            <DecimalField>
              <FieldName>fk_yd_mainmatamt</FieldName>
              <Scale>4</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>mLcCG6uHWs</Id>
              <Key>yd_mainmatamt</Key>
              <DefValue>0</DefValue>
              <ParentId>2EPZ+73YO7D0</ParentId>
              <Name>主商品金额</Name>
            </DecimalField>
            <ComboField>
              <FieldName>fk_yd_mattype</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>BdRdizmRGZ</Id>
              <Key>yd_mattype</Key>
              <MustInput>true</MustInput>
              <ParentId>2EPZ+73YO7D0</ParentId>
              <Name>物料类型</Name>
              <Items>
                <ComboItem>
                  <Caption>ToB</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>ToC</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <MaterielField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>6Q9ORRknc4</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>物料</Name>
              <FieldName>fk_yd_materialid</FieldName>
              <Key>yd_material</Key>
              <ParentId>i3UDhJH2si</ParentId>
              <BaseEntityId>5fa3b2b40000a2ac</BaseEntityId>
            </MaterielField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>zEAGojdYda</Id>
              <Key>yd_matnum</Key>
              <RefDisplayProp>number</RefDisplayProp>
              <ParentId>i3UDhJH2si</ParentId>
              <RefBaseFieldId>6Q9ORRknc4</RefBaseFieldId>
              <Name>物料编码</Name>
            </BasedataPropField>
            <IntegerField>
              <FieldName>fk_yd_qty</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ZfMWrMb3fY</Id>
              <Key>yd_qty</Key>
              <MustInput>true</MustInput>
              <DataScope/>
              <DefValue>0</DefValue>
              <ParentId>i3UDhJH2si</ParentId>
              <Name>组成数量</Name>
            </IntegerField>
            <PriceField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>WNG4p2CghV</Id>
              <DefValue>0</DefValue>
              <Name>单价</Name>
              <FieldName>fk_yd_price</FieldName>
              <Key>yd_price</Key>
              <ParentId>i3UDhJH2si</ParentId>
            </PriceField>
            <DecimalField>
              <FieldName>fk_yd_rate</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Zv2uv5tgsZ</Id>
              <Key>yd_rate</Key>
              <DefValue>0</DefValue>
              <ParentId>i3UDhJH2si</ParentId>
              <Name>比例（%）</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_matamt</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>IHM74hkAdd</Id>
              <Key>yd_matamt</Key>
              <DefValue>0</DefValue>
              <ParentId>i3UDhJH2si</ParentId>
              <Name>金额</Name>
            </DecimalField>
            <CheckBoxField>
              <FieldName>fk_yd_issingle</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>tp5veqniGs</Id>
              <Key>yd_issingle</Key>
              <ParentId>2EPZ+73YO7D0</ParentId>
              <Name>是否单品</Name>
            </CheckBoxField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv></Isv>
      <Version>1744701495684</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_matcombination</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>2EPZ+65CRH83</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1744701495655</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
