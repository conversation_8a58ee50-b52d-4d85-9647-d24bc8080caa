<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>5/+B7OL4ZZQQ</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>5/+B7OL4ZZQQ</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1749794939000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>5/+B7OL4ZZQQ</Id>
          <Key>yd_materialprice</Key>
          <EntityId>5/+B7OL4ZZQQ</EntityId>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>价格</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BasedataFormAp action="edit" oid="1942c188000064ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/+B7OL4ZZQQ</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>5/+B7OL4ZZQQ</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>价格</Name>
              <Key>yd_materialprice</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>价格</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000024ac">
                      <Name>价格</Name>
                    </FormAp>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/+B7OL4ZZQQ</EntityId>
                    </BillListAp>
                    <ListColumnAp action="edit" oid="b5994054000028ac">
                      <Visible/>
                    </ListColumnAp>
                    <ListColumnAp action="edit" oid="b5994054000029ac">
                      <Visible/>
                    </ListColumnAp>
                    <ComboListColumnAp action="edit" oid="UGA0V8HS=6S">
                      <Visible/>
                    </ComboListColumnAp>
                    <ComboListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/+C6XK6YO3M</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_conditiontype</ListFieldId>
                      <Name>条件类型</Name>
                    </ComboListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/P33HB044WI</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_material.number</ListFieldId>
                      <Name>物料编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/P33M4CKSQ3</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_material.name</ListFieldId>
                      <Name>物料名称</Name>
                    </ListColumnAp>
                    <DecimalListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/+C9MH2GT=+</Id>
                      <Key>yd_decimallistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_price</ListFieldId>
                      <Name>价格</Name>
                    </DecimalListColumnAp>
                    <DateListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/+C9NVXZ7H5</Id>
                      <Key>yd_datelistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>8</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_begdate</ListFieldId>
                      <Name>开始日期</Name>
                    </DateListColumnAp>
                    <DateListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/+C9OVE42K7</Id>
                      <Key>yd_datelistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>9</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_enddate</ListFieldId>
                      <Name>结束日期</Name>
                    </DateListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <FlexPanelAp action="edit" oid="LL4BVAnADa">
              <Index action="reset"/>
            </FlexPanelAp>
            <FlexPanelAp action="edit" oid="PxNfkkak4s">
              <Grow>0</Grow>
              <Index>1</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                  <Padding>
                    <Padding>
                      <Left>2px</Left>
                      <Right action="reset"/>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp action="edit" oid="h736cFwZ6e">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="8oq6R4m9CF">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="AkId5S4yTs">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="jiRpZNc99A">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="ac5Y5Dax1q">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="O75mQrM58q">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp>
              <Id>LML09z07qw</Id>
              <FieldId>LML09z07qw</FieldId>
              <Index>6</Index>
              <Name>条件类型</Name>
              <Key>yd_conditiontype</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>2OEn4gRe4W</Id>
              <FieldId>2OEn4gRe4W</FieldId>
              <Index>7</Index>
              <Name>物料</Name>
              <Key>yd_material</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>temctcuH3q</Id>
              <FieldId>temctcuH3q</FieldId>
              <Index>8</Index>
              <Name>币别</Name>
              <Key>yd_currency</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>P9xh0AS6un</Id>
              <FieldId>P9xh0AS6un</FieldId>
              <Index>9</Index>
              <Name>价格</Name>
              <Key>yd_price</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>GXin5y9wtl</Id>
              <FieldId>GXin5y9wtl</FieldId>
              <Index>10</Index>
              <Name>开始日期</Name>
              <Key>yd_begdate</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>pTzTgO2Fzt</Id>
              <FieldId>pTzTgO2Fzt</FieldId>
              <Index>11</Index>
              <Name>结束日期</Name>
              <Key>yd_enddate</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>2218211534222423040</ModifierId>
      <EntityId>5/+B7OL4ZZQQ</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749794938889</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_materialprice</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>5/+B7OL4ZZQQ</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1749794939000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>5/+B7OL4ZZQQ</Id>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>价格</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BaseEntity action="edit" oid="1942c188000066ac">
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_materialprice</TableName>
              <Id>5/+B7OL4ZZQQ</Id>
              <Name>价格</Name>
              <Template action="reset"/>
              <Key>yd_materialprice</Key>
              <NetworkControl>
                <NetCtrlOperation>
                  <OperationKey>submit</OperationKey>
                  <GroupId>default_netctrl</GroupId>
                  <Id>c91d5125000034ac</Id>
                  <Key>default_netctrl_submit</Key>
                </NetCtrlOperation>
              </NetworkControl>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
            </BaseEntity>
            <TextField action="edit" oid="h736cFwZ6e">
              <MustInput action="reset"/>
            </TextField>
            <MuliLangTextField action="edit" oid="8oq6R4m9CF">
              <MustInput action="reset"/>
              <GL>true</GL>
            </MuliLangTextField>
            <ComboField>
              <FieldName>fk_yd_conditiontype</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>LML09z07qw</Id>
              <Key>yd_conditiontype</Key>
              <Name>条件类型</Name>
              <Items>
                <ComboItem>
                  <Caption>全国统一零售价</Caption>
                  <Value>ZRP1</Value>
                </ComboItem>
              </Items>
            </ComboField>
            <MaterielField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2OEn4gRe4W</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>物料</Name>
              <FieldName>fk_yd_materialid</FieldName>
              <Key>yd_material</Key>
              <BaseEntityId>5fa3b2b40000a2ac</BaseEntityId>
            </MaterielField>
            <PriceField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>P9xh0AS6un</Id>
              <DefValue>0</DefValue>
              <Name>价格</Name>
              <FieldName>fk_yd_price</FieldName>
              <Key>yd_price</Key>
            </PriceField>
            <DateField>
              <FieldName>fk_yd_begdate</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>GXin5y9wtl</Id>
              <Key>yd_begdate</Key>
              <Name>开始日期</Name>
            </DateField>
            <DateField>
              <FieldName>fk_yd_enddate</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>pTzTgO2Fzt</Id>
              <Key>yd_enddate</Key>
              <Name>结束日期</Name>
            </DateField>
            <CurrencyField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>temctcuH3q</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>币别</Name>
              <FieldName>fk_yd_currencyid</FieldName>
              <RefProps>
                <RefProp>
                  <Name>amtprecision</Name>
                </RefProp>
                <RefProp>
                  <Name>priceprecision</Name>
                </RefProp>
                <RefProp>
                  <Name>sign</Name>
                </RefProp>
                <RefProp>
                  <Name>isshowsign</Name>
                </RefProp>
              </RefProps>
              <Key>yd_currency</Key>
            </CurrencyField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749794938924</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_materialprice</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>5/+B7OL4ZZQQ</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1749794938889</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
