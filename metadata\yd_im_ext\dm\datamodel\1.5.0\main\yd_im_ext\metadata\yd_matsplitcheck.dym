<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>2W60N5=QF3VL</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>2W60N5=QF3VL</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1746598815000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>2W60N5=QF3VL</Id>
          <Key>yd_matsplitcheck</Key>
          <EntityId>2W60N5=QF3VL</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>主商品拆分核对</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>主商品拆分校验界面插件</Description>
                  <ClassName>kd.bos.yd.tcyp.report.formplugin.MatSplitCheckFormRpt</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>2W60N5=QF3VL</Id>
              <Name>主商品拆分核对</Name>
              <Key>yd_matsplitcheck</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ReportPlugin>kd.bos.yd.tcyp.report.queryplugin.MatSplitCheckQueryRpt</ReportPlugin>
            </ReportListAp>
            <FieldAp>
              <Id>Q4dqOy6QMg</Id>
              <FieldId>Q4dqOy6QMg</FieldId>
              <Name>开始日期</Name>
              <Key>yd_begindate</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>jhoA0P5LgX</Id>
              <FieldId>jhoA0P5LgX</FieldId>
              <Index>1</Index>
              <Name>到期日</Name>
              <Key>yd_enddate</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>G8ZckpPpgA</Id>
              <FieldId>G8ZckpPpgA</FieldId>
              <Name>组织名称</Name>
              <Key>yd_orgname</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>fyqc2vewpq</Id>
              <FieldId>fyqc2vewpq</FieldId>
              <Index>1</Index>
              <Name>店铺编码</Name>
              <Key>yd_storenumber</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>CCK7IOGnl7</Id>
              <FieldId>CCK7IOGnl7</FieldId>
              <Index>2</Index>
              <Name>单据编码</Name>
              <Key>yd_billnumber</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>cjTD3FGoUM</Id>
              <FieldId>cjTD3FGoUM</FieldId>
              <Index>3</Index>
              <Name>组装品编码</Name>
              <Key>yd_mainmatnum</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>mAIGQwrJ9O</Id>
              <FieldId>mAIGQwrJ9O</FieldId>
              <Index>4</Index>
              <Name>拆分单品编码</Name>
              <Key>yd_spmatnum</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338612817267262464</ModifierId>
      <EntityId>2W60N5=QF3VL</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1746598815373</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_matsplitcheck</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>2W60N5=QF3VL</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1746598815000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>2W60N5=QF3VL</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>主商品拆分核对</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>2W60N5=QF3VL</Id>
              <Key>yd_matsplitcheck</Key>
              <Name>主商品拆分核对</Name>
            </ReportEntity>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>G8ZckpPpgA</Id>
              <Key>yd_orgname</Key>
              <Name>组织名称</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>fyqc2vewpq</Id>
              <Key>yd_storenumber</Key>
              <Name>店铺编码</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>CCK7IOGnl7</Id>
              <Key>yd_billnumber</Key>
              <Name>单据编码</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>cjTD3FGoUM</Id>
              <Key>yd_mainmatnum</Key>
              <Name>组装品编码</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>mAIGQwrJ9O</Id>
              <Key>yd_spmatnum</Key>
              <Name>拆分单品编码</Name>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>Q4dqOy6QMg</Id>
              <Key>yd_begindate</Key>
              <MustInput>true</MustInput>
              <Name>开始日期</Name>
              <DefValueDesign>
                <DefValueDesign>
                  <FuncParameter>#CurrentDate#</FuncParameter>
                  <FuncType>getToday</FuncType>
                </DefValueDesign>
              </DefValueDesign>
            </DateField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>jhoA0P5LgX</Id>
              <Key>yd_enddate</Key>
              <MustInput>true</MustInput>
              <Name>到期日</Name>
              <DefValueDesign>
                <DefValueDesign>
                  <FuncParameter>#CurrentDate#</FuncParameter>
                  <FuncType>getToday</FuncType>
                </DefValueDesign>
              </DefValueDesign>
            </DateField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>kingdee</Isv>
      <Version>1746598815436</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_matsplitcheck</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>2W60N5=QF3VL</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1746598815373</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
