<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>4V9NTDV6I9T7</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4V9NTDV6I9T7</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1745217194000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>4V9NTDV6I9T7</Id>
          <Key>yd_mergeorderfields</Key>
          <EntityId>4V9NTDV6I9T7</EntityId>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>合单字段表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BasedataFormAp action="edit" oid="1942c188000064ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4V9NTDV6I9T7</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>4V9NTDV6I9T7</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>合单字段表</Name>
              <Key>yd_mergeorderfields</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>合单字段表列表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000024ac">
                      <Name>合单字段表列表</Name>
                    </FormAp>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <SchemeFilterViewAp action="edit" oid="schemefilterview">
                      <BatchSetFilter>[{"Value":"seniorfilter","TextField":"enable","Id":"enable","Name":"使用状态"},{"Value":"seniorfilter","TextField":"number","Id":"number","Name":"编码"},{"Value":"seniorfilter","TextField":"name","Id":"name","Name":"字段标识"},{"Value":"seniorfilter","TextField":"yd_fieldname","Id":"yd_fieldname","Name":"字段名称"},{"Value":"seniorfilter","TextField":"yd_tablename","Id":"yd_tablename","Name":"所属表名称"},{"Value":"seniorfilter","TextField":"yd_tableflag","Id":"yd_tableflag","Name":"所属表标识"}]</BatchSetFilter>
                    </SchemeFilterViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4V9NTDV6I9T7</EntityId>
                    </BillListAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4V=BUO96OYPI</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>2</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>name</ListFieldId>
                      <Name>字段标识</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4V=BUSKROOZ9</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_fieldname</ListFieldId>
                      <Name>字段名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4V9RNSJAR0L/</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_tablename</ListFieldId>
                      <Name>所属表名称</Name>
                    </ListColumnAp>
                    <ComboListColumnAp action="edit" oid="UGA0V8HS=6S">
                      <Index>5</Index>
                    </ComboListColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>enable</FieldName>
                      <Id>4WZMEM466LV+</Id>
                      <Key>yd_schemefiltercolumnap</Key>
                      <ParentId>schemefilterview</ParentId>
                      <Name>使用状态</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>number</FieldName>
                      <Id>4WZMEM466L5Q</Id>
                      <Key>yd_schemefiltercolumnap1</Key>
                      <Index>1</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>编码</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>name</FieldName>
                      <Id>4WZMEM466LV/</Id>
                      <Key>yd_schemefiltercolumnap2</Key>
                      <Index>2</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>字段标识</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>yd_fieldname</FieldName>
                      <Id>4WZMEM466L5R</Id>
                      <Key>yd_schemefiltercolumnap3</Key>
                      <Index>3</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>字段名称</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>yd_tablename</FieldName>
                      <Id>4WZMEM466LV0</Id>
                      <Key>yd_schemefiltercolumnap4</Key>
                      <Index>4</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>所属表名称</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>yd_tableflag</FieldName>
                      <Id>4WZMEM466L5S</Id>
                      <Key>yd_schemefiltercolumnap5</Key>
                      <Index>5</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>所属表标识</Name>
                    </SchemeFilterColumnAp>
                    <ListColumnAp action="remove" oid="b5994054000029ac"/>
                    <SchemeFilterColumnAp action="remove" oid="f31a499a0000d3ac"/>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <FlexPanelAp action="edit" oid="PxNfkkak4s">
              <Grow>0</Grow>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                  <Padding>
                    <Padding>
                      <Left>2px</Left>
                      <Right action="reset"/>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp action="edit" oid="8oq6R4m9CF">
              <Name>字段标识</Name>
            </FieldAp>
            <FieldAp>
              <Id>lg1rn2Bc6c</Id>
              <FieldId>lg1rn2Bc6c</FieldId>
              <Index>2</Index>
              <Name>字段名称</Name>
              <Key>yd_fieldname</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>xvKeDegq7z</Id>
              <FieldId>xvKeDegq7z</FieldId>
              <Index>3</Index>
              <Name>所属表标识</Name>
              <Key>yd_tableflag</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>UC8garNQiE</Id>
              <FieldId>UC8garNQiE</FieldId>
              <Index>4</Index>
              <Name>所属表名称</Name>
              <Key>yd_tablename</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp action="edit" oid="AkId5S4yTs">
              <Index>5</Index>
            </FieldAp>
            <FieldAp action="edit" oid="O75mQrM58q">
              <Index>6</Index>
            </FieldAp>
            <FieldAp action="edit" oid="jiRpZNc99A">
              <Index>7</Index>
            </FieldAp>
            <FieldAp action="edit" oid="ac5Y5Dax1q">
              <Index>8</Index>
            </FieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338618754925331456</ModifierId>
      <EntityId>4V9NTDV6I9T7</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1745217193816</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_mergeorderfields</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4V9NTDV6I9T7</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1745217194000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>4V9NTDV6I9T7</Id>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>合单字段表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BaseEntity action="edit" oid="1942c188000066ac">
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_mergeorderfields</TableName>
              <Id>4V9NTDV6I9T7</Id>
              <Name>合单字段表</Name>
              <Template action="reset"/>
              <Operations>
                <Operation action="edit" oid="b599405400001aac">
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <CustomPromp/>
                      <Description>编码唯一性校验</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>4V9S79PILLCP</Id>
                      <Fields>
                        <FieldId>
                          <Id>number</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                    <GrpfieldsuniqueValidation>
                      <CustomPromp/>
                      <Description>表标识+表名称+字段标识+字段名称唯一性校验</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>4V9SBQH77PV9</Id>
                      <Fields>
                        <FieldId>
                          <Id>name</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_tableflag</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_fieldname</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_tablename</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
              </Operations>
              <BusinessControl>
                <BusinessControl>
                  <CodeNumber>true</CodeNumber>
                  <WorkFlow>false</WorkFlow>
                </BusinessControl>
              </BusinessControl>
              <Key>yd_mergeorderfields</Key>
              <NetworkControl>
                <NetCtrlOperation>
                  <OperationKey>submit</OperationKey>
                  <GroupId>default_netctrl</GroupId>
                  <Id>c91d5125000034ac</Id>
                  <Key>default_netctrl_submit</Key>
                </NetCtrlOperation>
              </NetworkControl>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
            </BaseEntity>
            <MuliLangTextField action="edit" oid="8oq6R4m9CF">
              <Name>字段标识</Name>
              <GL>true</GL>
            </MuliLangTextField>
            <TextField>
              <FieldName>fk_yd_tableflag</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xvKeDegq7z</Id>
              <Key>yd_tableflag</Key>
              <MustInput>true</MustInput>
              <Name>所属表标识</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_fieldname</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>lg1rn2Bc6c</Id>
              <Key>yd_fieldname</Key>
              <MustInput>true</MustInput>
              <Name>字段名称</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_tablename</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>UC8garNQiE</Id>
              <Key>yd_tablename</Key>
              <MustInput>true</MustInput>
              <Name>所属表名称</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1745217193856</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_mergeorderfields</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4V9NTDV6I9T7</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1745217193816</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
