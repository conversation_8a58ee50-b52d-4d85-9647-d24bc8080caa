<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>4V9YXC3GR9A7</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4V9YXC3GR9A7</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1748532048000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>4V9YXC3GR9A7</Id>
          <Key>yd_mergeorderrules</Key>
          <EntityId>4V9YXC3GR9A7</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>合单规则表</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>4V9YXC8410M5</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4V9YXC841/YY</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>4V9YXC8410M5</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4V9YXC8410M6</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>4V9YXC8410M5</ParentId>
                      <ListFieldId>creator.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>创建人.姓名</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4V9YXC3GR9A7</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>4V9YXC8410M5</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Direction>column</Direction>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>4V9YXC841/YX</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>4V9YXC3GR9A7</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>合单规则表</Name>
              <Key>yd_mergeorderrules</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>4V9YXC80ELRG</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>4V9YXC80EMER</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>4V9YXC80ELRG</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4V9YXC841/YV</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>4V9YXC80ELRG</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4V9YXC8410M3</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>4V9YXC80EMER</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4V9YXC841/YW</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>4V9YXC80EMER</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>4V9YXC8410M4</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Name>合单规则表列表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Name>合单规则表列表</Name>
                    </FormAp>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4V9YXC3GR9A7</EntityId>
                    </BillListAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4V=4+Y+LJ4EJ</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>creator.name</ListFieldId>
                      <Name>创建人.姓名</Name>
                    </ListColumnAp>
                    <DateListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4V=4/0/C04IX</Id>
                      <Key>yd_datelistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>createtime</ListFieldId>
                      <Name>创建时间</Name>
                    </DateListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <EntryAp>
              <EntryId>4V9YXC80EMEO</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>4V9YXC80EMEO</Id>
              <Name>合单条件</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>yd_mergeconditionentry</Key>
              <ParentId>4V9YXC80ELRC</ParentId>
            </EntryAp>
            <AdvConSummaryPanelAp>
              <Id>4V9YXC80ELRB</Id>
              <Name>高级面板摘要容器</Name>
              <Key>advconsummarypanelap</Key>
              <ParentId>4V9YXC80EMEL</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>4V9YXC80EMEM</Id>
              <Key>advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>4V9YXC80EMEL</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>4V9YXC80ELRC</Id>
              <AlignItems>stretch</AlignItems>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>advconchildcanelap</Key>
              <Direction>column</Direction>
              <ParentId>4V9YXC80EMEL</ParentId>
              <Wrap>false</Wrap>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>4V9YXC80EMEN</Id>
              <Key>tb_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>4V9YXC80EMEM</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>4V9YXC80ELRD</Id>
              <Key>tb_del</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>4V9YXC80EMEM</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryFieldAp>
              <Id>4V9YXC80ELRE</Id>
              <FieldId>4V9YXC80ELRE</FieldId>
              <Name>修改人</Name>
              <Hidden>true</Hidden>
              <Key>modifierfield</Key>
              <ParentId>4V9YXC80EMEO</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>4V9YXC80EMEP</Id>
              <FieldId>4V9YXC80EMEP</FieldId>
              <Index>1</Index>
              <Name>修改时间</Name>
              <Hidden>true</Hidden>
              <Key>modifydatefield</Key>
              <ParentId>4V9YXC80EMEO</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>YCk6V6MOtN</Id>
              <FieldId>YCk6V6MOtN</FieldId>
              <Index>2</Index>
              <Name>字段标识</Name>
              <Key>yd_fieldflag</Key>
              <ParentId>4V9YXC80EMEO</ParentId>
              <Width>150px</Width>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>4D6yaQjk73</Id>
              <FieldId>4D6yaQjk73</FieldId>
              <Index>3</Index>
              <Name>字段名称</Name>
              <Key>yd_fieldname</Key>
              <ParentId>4V9YXC80EMEO</ParentId>
              <Width>200px</Width>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>bVyPS1nlBC</Id>
              <FieldId>bVyPS1nlBC</FieldId>
              <Index>4</Index>
              <Name>所属表标识</Name>
              <Key>yd_tableflag</Key>
              <ParentId>4V9YXC80EMEO</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>UBQ97ow60Q</Id>
              <FieldId>UBQ97ow60Q</FieldId>
              <Index>5</Index>
              <Name>所属表名称</Name>
              <Key>yd_tablename</Key>
              <ParentId>4V9YXC80EMEO</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>mZNFCDqHzU</Id>
              <FieldId>mZNFCDqHzU</FieldId>
              <Name>字段标识</Name>
              <Key>yd_detailfieldflag</Key>
              <ParentId>JoO7ri7t6U</ParentId>
              <Width>150px</Width>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>b8s8JuGnS3</Id>
              <FieldId>b8s8JuGnS3</FieldId>
              <Index>1</Index>
              <Name>字段名称</Name>
              <Key>yd_detailfieldname</Key>
              <ParentId>JoO7ri7t6U</ParentId>
              <Width>200px</Width>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>6hEhHkftsa</Id>
              <FieldId>6hEhHkftsa</FieldId>
              <Index>2</Index>
              <Name>所属表标识</Name>
              <Key>yd_detailtableflag</Key>
              <ParentId>JoO7ri7t6U</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>kmbNWfWZju</Id>
              <FieldId>kmbNWfWZju</FieldId>
              <Index>3</Index>
              <Name>所属表名称</Name>
              <Key>yd_detailtablename</Key>
              <ParentId>JoO7ri7t6U</ParentId>
            </EntryFieldAp>
            <AdvConBarItemAp>
              <OperationKey>newentry2</OperationKey>
              <Id>MqpKzQjOHu</Id>
              <Key>yd_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>LpN817RpX6</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry2</OperationKey>
              <Id>kh64QQOF6Y</Id>
              <Key>yd_del</Key>
              <OperationStyle>0</OperationStyle>
              <Index>1</Index>
              <ParentId>LpN817RpX6</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp>
              <Id>r3VnNDLa3i</Id>
              <FieldId>r3VnNDLa3i</FieldId>
              <Height>150px</Height>
              <ForeColor>#00FF00</ForeColor>
              <BackColor>#000000</BackColor>
              <ShowTitle>false</ShowTitle>
              <Name>单据范围</Name>
              <FontSize>15</FontSize>
              <Key>yd_billscope</Key>
              <ParentId>VZakX2g2eB</ParentId>
              <FullLine>true</FullLine>
            </FieldAp>
            <EntryAp>
              <EntryId>JoO7ri7t6U</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>JoO7ri7t6U</Id>
              <Name>明细行合并规则</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>yd_detialmergeentry</Key>
              <ParentId>hNRtB9AXoq</ParentId>
            </EntryAp>
            <FieldAp>
              <Id>pF1FsuEVdM</Id>
              <FieldId>pF1FsuEVdM</FieldId>
              <Index>4</Index>
              <Name>源单类型</Name>
              <Key>yd_source_billtype</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <AdvConSummaryPanelAp>
              <Id>FNMA1y4wQS</Id>
              <Name>高级面板摘要容器</Name>
              <Key>yd_advconsummarypanelap</Key>
              <ParentId>o7sECmnRFU</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>LpN817RpX6</Id>
              <Key>yd_advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>o7sECmnRFU</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>hNRtB9AXoq</Id>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>yd_advconchildpanelap</Key>
              <ParentId>o7sECmnRFU</ParentId>
            </AdvConChildPanelAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <FieldsetPanelAp action="edit" oid="xTQZ9d8tHa">
              <Index>1</Index>
            </FieldsetPanelAp>
            <FieldsetPanelAp>
              <Collapsible>true</Collapsible>
              <Id>VZakX2g2eB</Id>
              <Key>yd_fieldsetpanelap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>@container-bg-color</BackColor>
              <Name>单据范围</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>4V9YXC80EMEL</Id>
              <Key>advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>3</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>分单策略</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>o7sECmnRFU</Id>
              <Key>yd_advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>4</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>@container-bg-color</BackColor>
              <Name>单据体行合并策略</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Index>5</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338618754925331456</ModifierId>
      <EntityId>4V9YXC3GR9A7</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1748532047519</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_mergeorderrules</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4V9YXC3GR9A7</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1748532048000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>4V9YXC3GR9A7</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>合单规则表</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_mergeorderrules</TableName>
              <Operations>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>4V9YXC80EMEO</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>4V9YXC80ELRF</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>4V9YXC80EMEO</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>4V9YXC80EMEQ</Id>
                  <Key>deleteentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <NewEntryParameter>
                      <EntryId>JoO7ri7t6U</EntryId>
                    </NewEntryParameter>
                  </Parameter>
                  <Name>新增分录2</Name>
                  <OperationType>newentry</OperationType>
                  <Id>4VH93H5MSTRE</Id>
                  <SuccessMsg/>
                  <Key>newentry2</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>JoO7ri7t6U</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录2</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>4VH97K/RG4HO</Id>
                  <SuccessMsg/>
                  <Key>deleteentry2</Key>
                </Operation>
              </Operations>
              <BusinessControl>
                <BusinessControl>
                  <CodeNumber>true</CodeNumber>
                  <WorkFlow>false</WorkFlow>
                </BusinessControl>
              </BusinessControl>
              <Id>4V9YXC3GR9A7</Id>
              <Key>yd_mergeorderrules</Key>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
              <Name>合单规则表</Name>
            </BillEntity>
            <EntryEntity>
              <TableName>tk_yd_mergeconditionentry</TableName>
              <Id>4V9YXC80EMEO</Id>
              <Key>yd_mergeconditionentry</Key>
              <Name>合单条件</Name>
            </EntryEntity>
            <ModifierField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4V9YXC80ELRE</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>修改人</Name>
              <FieldName>fmodifierfield</FieldName>
              <Key>modifierfield</Key>
              <ParentId>4V9YXC80EMEO</ParentId>
              <BaseEntityId>68bde9ca00000eac</BaseEntityId>
            </ModifierField>
            <ModifyDateField>
              <FieldName>fmodifydatefield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4V9YXC80EMEP</Id>
              <Key>modifydatefield</Key>
              <ParentId>4V9YXC80EMEO</ParentId>
              <Name>修改日期</Name>
            </ModifyDateField>
            <LargeTextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>r3VnNDLa3i</Id>
              <EditingMode>largeText</EditingMode>
              <Name>单据范围</Name>
              <FieldName>fk_yd_billscope</FieldName>
              <Key>yd_billscope</Key>
            </LargeTextField>
            <EntryEntity>
              <TableName>tk_yd_detialmergeentry</TableName>
              <Id>JoO7ri7t6U</Id>
              <Key>yd_detialmergeentry</Key>
              <Name>明细行合并规则</Name>
            </EntryEntity>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>YCk6V6MOtN</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>字段标识</Name>
              <FieldName>fk_yd_fieldflagid</FieldName>
              <RefProps>
                <RefProp>
                  <Name>name</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_fieldname</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_tablename</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_tableflag</Name>
                </RefProp>
              </RefProps>
              <Key>yd_fieldflag</Key>
              <MustInput>true</MustInput>
              <RefLayout/>
              <ParentId>4V9YXC80EMEO</ParentId>
              <BaseEntityId>4V9NTDV6I9T7</BaseEntityId>
            </BasedataField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4D6yaQjk73</Id>
              <Key>yd_fieldname</Key>
              <RefDisplayProp>yd_fieldname</RefDisplayProp>
              <ParentId>4V9YXC80EMEO</ParentId>
              <RefBaseFieldId>YCk6V6MOtN</RefBaseFieldId>
              <Name>字段名称</Name>
            </BasedataPropField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>bVyPS1nlBC</Id>
              <Key>yd_tableflag</Key>
              <RefDisplayProp>yd_tableflag</RefDisplayProp>
              <ParentId>4V9YXC80EMEO</ParentId>
              <RefBaseFieldId>YCk6V6MOtN</RefBaseFieldId>
              <Name>所属表标识</Name>
            </BasedataPropField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>UBQ97ow60Q</Id>
              <Key>yd_tablename</Key>
              <RefDisplayProp>yd_tablename</RefDisplayProp>
              <ParentId>4V9YXC80EMEO</ParentId>
              <RefBaseFieldId>YCk6V6MOtN</RefBaseFieldId>
              <Name>所属表名称</Name>
            </BasedataPropField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>mZNFCDqHzU</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>字段标识</Name>
              <FieldName>fk_yd_detailfieldflag</FieldName>
              <RefProps>
                <RefProp>
                  <Name>number</Name>
                </RefProp>
                <RefProp>
                  <Name>name</Name>
                </RefProp>
                <RefProp>
                  <Name>status</Name>
                </RefProp>
                <RefProp>
                  <Name>creator.id</Name>
                </RefProp>
                <RefProp>
                  <Name>creator.number</Name>
                </RefProp>
                <RefProp>
                  <Name>creator.name</Name>
                </RefProp>
                <RefProp>
                  <Name>modifier.id</Name>
                </RefProp>
                <RefProp>
                  <Name>modifier.number</Name>
                </RefProp>
                <RefProp>
                  <Name>modifier.name</Name>
                </RefProp>
                <RefProp>
                  <Name>enable</Name>
                </RefProp>
                <RefProp>
                  <Name>createtime</Name>
                </RefProp>
                <RefProp>
                  <Name>modifytime</Name>
                </RefProp>
                <RefProp>
                  <Name>masterid</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_fieldflag</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_fieldname</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_tablename</Name>
                </RefProp>
              </RefProps>
              <Key>yd_detailfieldflag</Key>
              <MustInput>true</MustInput>
              <RefLayout/>
              <ParentId>JoO7ri7t6U</ParentId>
              <BaseEntityId>4V9NTDV6I9T7</BaseEntityId>
            </BasedataField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>b8s8JuGnS3</Id>
              <Key>yd_detailfieldname</Key>
              <RefDisplayProp>yd_fieldname</RefDisplayProp>
              <ParentId>JoO7ri7t6U</ParentId>
              <RefBaseFieldId>mZNFCDqHzU</RefBaseFieldId>
              <Name>字段名称</Name>
            </BasedataPropField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>6hEhHkftsa</Id>
              <Key>yd_detailtableflag</Key>
              <RefDisplayProp>name</RefDisplayProp>
              <ParentId>JoO7ri7t6U</ParentId>
              <RefBaseFieldId>mZNFCDqHzU</RefBaseFieldId>
              <Name>所属表标识</Name>
            </BasedataPropField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kmbNWfWZju</Id>
              <Key>yd_detailtablename</Key>
              <RefDisplayProp>yd_tablename</RefDisplayProp>
              <ParentId>JoO7ri7t6U</ParentId>
              <RefBaseFieldId>mZNFCDqHzU</RefBaseFieldId>
              <Name>所属表名称</Name>
            </BasedataPropField>
            <ComboField>
              <FieldName>fk_yd_source_billtype</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pF1FsuEVdM</Id>
              <Key>yd_source_billtype</Key>
              <MustInput>true</MustInput>
              <Name>源单类型</Name>
              <Items>
                <ComboItem>
                  <Caption>批发退货单</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>批发通知单</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>销售出库单</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>库存调拨单</Caption>
                  <Value>4</Value>
                  <Seq>3</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>其他出库单</Caption>
                  <Value>5</Value>
                  <Seq>4</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>发货明细</Caption>
                  <Value>6</Value>
                  <Seq>5</Seq>
                </ComboItem>
              </Items>
            </ComboField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1748532047553</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_mergeorderrules</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4V9YXC3GR9A7</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1748532047519</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
