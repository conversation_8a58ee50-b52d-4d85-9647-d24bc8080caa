<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>5/I9HU004ZEP</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>5/I9HU004ZEP</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1749623710000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>5/I9HU004ZEP</Id>
          <Key>yd_midwarehouse</Key>
          <EntityId>5/I9HU004ZEP</EntityId>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>中转仓配置表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BasedataFormAp action="edit" oid="1942c188000064ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/I9HU004ZEP</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>5/I9HU004ZEP</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>中转仓配置表</Name>
              <Key>yd_midwarehouse</Key>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/I9HU004ZEP</EntityId>
                    </BillListAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/I=QD0YR51G</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>1</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_warehouse.number</ListFieldId>
                      <Name>仓库编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/I=QE66QSRK</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>2</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_warehouse.name</ListFieldId>
                      <Name>仓库名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/I=QI4IZ/IV</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_org.number</ListFieldId>
                      <Name>组织编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/I=QO2AN2SA</Id>
                      <Key>yd_listcolumnap3</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_org.name</ListFieldId>
                      <Name>组织名称</Name>
                    </ListColumnAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/I=YWQ/MVE3</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>enable</ListFieldId>
                      <Name>使用状态</Name>
                    </ComboListColumnAp>
                    <ListColumnAp action="remove" oid="b5994054000028ac"/>
                    <ListColumnAp action="remove" oid="b5994054000029ac"/>
                    <ComboListColumnAp action="remove" oid="UGA0V8HS=6S"/>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <FlexPanelAp action="edit" oid="PxNfkkak4s">
              <Grow>0</Grow>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                  <Padding>
                    <Padding>
                      <Left>2px</Left>
                      <Right action="reset"/>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp action="edit" oid="h736cFwZ6e">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="8oq6R4m9CF">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="AkId5S4yTs">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp>
              <Id>w4OLXpa2YA</Id>
              <FieldId>w4OLXpa2YA</FieldId>
              <Index>3</Index>
              <Name>仓库</Name>
              <Key>yd_warehouse</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp action="edit" oid="ac5Y5Dax1q">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp>
              <Id>DknfnAdQyN</Id>
              <FieldId>DknfnAdQyN</FieldId>
              <Index>5</Index>
              <Name>组织</Name>
              <Key>yd_org</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp action="edit" oid="jiRpZNc99A">
              <Index>6</Index>
            </FieldAp>
            <FieldAp action="edit" oid="O75mQrM58q">
              <Index>7</Index>
            </FieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>2218211534222423040</ModifierId>
      <EntityId>5/I9HU004ZEP</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749623709824</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_midwarehouse</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>5/I9HU004ZEP</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1749623710000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>5/I9HU004ZEP</Id>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>中转仓配置表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BaseEntity action="edit" oid="1942c188000066ac">
              <dbRoute>scm</dbRoute>
              <Rules>
                <BizRule>
                  <FalseActions>
                    <ClearValueAction>
                      <RET>253</RET>
                      <ActionType>ClearValueAction</ActionType>
                      <Expression>yd_org</Expression>
                      <Description>清除指定字段值</Description>
                      <Id>5/I=ZEF8QITP</Id>
                    </ClearValueAction>
                  </FalseActions>
                  <TrueActions>
                    <TakeBasePropertyAction>
                      <RET>253</RET>
                      <ActionType>TakeBaseProAction</ActionType>
                      <Expression>
                        <TakeValueId>
                          <TargetField>yd_org</TargetField>
                          <Id>5/I=ZEF8QITQ</Id>
                          <SrcField>yd_warehouse.createorg.id</SrcField>
                        </TakeValueId>
                      </Expression>
                      <FieldName>
                        <TakeValueId>
                          <SrcFieldName>仓库.创建组织.内码(yd_warehouse.createorg.id)</SrcFieldName>
                          <TargetFieldName>组织(yd_org)</TargetFieldName>
                          <Id>5/I=ZEF8Q5/2</Id>
                        </TakeValueId>
                      </FieldName>
                      <Description>携带基础资料属性到指定列</Description>
                      <Id>5/I=ZEF8Q5/1</Id>
                    </TakeBasePropertyAction>
                  </TrueActions>
                  <Description>仓库不为空</Description>
                  <Id>5/I=L/TI0T5V</Id>
                  <PreCondition>yd_warehouse.id!=null</PreCondition>
                  <PreDescription>仓库不为空</PreDescription>
                </BizRule>
              </Rules>
              <TableName>tk_yd_midwarehouse</TableName>
              <Id>5/I9HU004ZEP</Id>
              <Name>中转仓配置表</Name>
              <Template action="reset"/>
              <Key>yd_midwarehouse</Key>
              <NetworkControl>
                <NetCtrlOperation>
                  <OperationKey>submit</OperationKey>
                  <GroupId>default_netctrl</GroupId>
                  <Id>c91d5125000034ac</Id>
                  <Key>default_netctrl_submit</Key>
                </NetCtrlOperation>
              </NetworkControl>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
            </BaseEntity>
            <TextField action="edit" oid="h736cFwZ6e">
              <MustInput action="reset"/>
            </TextField>
            <MuliLangTextField action="edit" oid="8oq6R4m9CF">
              <MustInput action="reset"/>
              <GL>true</GL>
            </MuliLangTextField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>DknfnAdQyN</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>组织</Name>
              <FieldName>fk_yd_orgid</FieldName>
              <Key>yd_org</Key>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>w4OLXpa2YA</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>仓库</Name>
              <FieldName>fk_yd_warehouseid</FieldName>
              <RefProps>
                <RefProp>
                  <Name>createorg.id</Name>
                </RefProp>
                <RefProp>
                  <Name>createorg.number</Name>
                </RefProp>
                <RefProp>
                  <Name>createorg.name</Name>
                </RefProp>
              </RefProps>
              <Key>yd_warehouse</Key>
              <RefLayout/>
              <BaseEntityId>14G1K2YGTJGQ</BaseEntityId>
            </BasedataField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749623709849</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_midwarehouse</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>5/I9HU004ZEP</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1749623709824</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
