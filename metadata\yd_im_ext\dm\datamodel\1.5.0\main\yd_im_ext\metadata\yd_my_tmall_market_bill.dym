<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>4XSYA9HPDKSJ</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4XSYA9HPDKSJ</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1747278604000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>4XSYA9HPDKSJ</Id>
          <Key>yd_my_tmall_market_bill</Key>
          <EntityId>4XSYA9HPDKSJ</EntityId>
          <ParentId>ab7efc31000010ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>猫超平台账单表</Name>
          <InheritPath>00305e8b000006ac,ab7efc31000010ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>4XSYA9K3OC3W</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4XSYA9K3OCT4</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>4XSYA9K3OC3W</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4XSYA9K3OC3X</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>4XSYA9K3OC3W</ParentId>
                      <ListFieldId>org.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>组织.名称</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4XSYA9HPDKSJ</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>4XSYA9K3OC3W</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Direction>column</Direction>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>4XSYA9K3OCT3</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>4XSYA9HPDKSJ</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>猫超平台账单表</Name>
              <Key>yd_my_tmall_market_bill</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>4XSYA9K3OCT/</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>4XSYA9K3OCT2</Id>
                      <Key>layoutflexap2</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <LayoutFlexAp>
                      <Id>4XSYA9K3OC3S</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp>
                      <Id>4XSYA9K3OC3T</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>4XSYA9K3OC3S</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4XSYA9K3OCT1</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>4XSYA9K3OC3S</ParentId>
                    </FieldAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>4XSYA9K3OCT/</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4XSYA9K3OCT0</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>4XSYA9K3OCT/</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4XSYA9K3OC3V</Id>
                      <FieldId>MwLyMGIJQa</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>组织</Name>
                      <Key>org</Key>
                      <ParentId>4XSYA9K3OCT2</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>4XSYA9K3OC3U</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4XSYA9HPDKSJ</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <AdvConSummaryPanelAp>
              <Id>4XSYA9K3OCSW</Id>
              <Name>高级面板摘要容器</Name>
              <Key>advconsummarypanelap</Key>
              <ParentId>4XSYA9K3OC3M</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>4XSYA9K3OC3N</Id>
              <Key>advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>4XSYA9K3OC3M</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>4XSYA9K3OCSX</Id>
              <AlignItems>stretch</AlignItems>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>advconchildcanelap</Key>
              <Direction>column</Direction>
              <ParentId>4XSYA9K3OC3M</ParentId>
              <Wrap>false</Wrap>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>4XSYA9K3OC3O</Id>
              <Key>tb_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>4XSYA9K3OC3N</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>4XSYA9K3OCSY</Id>
              <Key>tb_del</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>4XSYA9K3OC3N</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryFieldAp>
              <Id>4XSYA9K3OCSZ</Id>
              <FieldId>4XSYA9K3OCSZ</FieldId>
              <Name>修改人</Name>
              <Hidden>true</Hidden>
              <Key>modifierfield</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>4XSYA9K3OC3Q</Id>
              <FieldId>4XSYA9K3OC3Q</FieldId>
              <Index>1</Index>
              <Name>修改时间</Name>
              <Hidden>true</Hidden>
              <Key>modifydatefield</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>04t8EoxaPR</Id>
              <FieldId>04t8EoxaPR</FieldId>
              <Index>2</Index>
              <Name>对账单编号</Name>
              <Key>yd_fbillno</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>I4fd6rniB1</Id>
              <FieldId>I4fd6rniB1</FieldId>
              <Index>3</Index>
              <Name>业务主单据编码</Name>
              <Key>yd_business_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>YLAXOm6Nnd</Id>
              <FieldId>YLAXOm6Nnd</FieldId>
              <Index>4</Index>
              <Name>业务子单据编码</Name>
              <Key>yd_sub_business_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>xNq1th3rbr</Id>
              <FieldId>xNq1th3rbr</FieldId>
              <Index>5</Index>
              <Name>交易主单</Name>
              <Key>yd_deal_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Y2hBEvfeEw</Id>
              <FieldId>Y2hBEvfeEw</FieldId>
              <Index>6</Index>
              <Name>交易子单</Name>
              <Key>yd_sub_deal_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>HaUwBBpSQC</Id>
              <FieldId>HaUwBBpSQC</FieldId>
              <Index>7</Index>
              <Name>支付时间</Name>
              <Key>yd_pay_time</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>jdEQ6oboaZ</Id>
              <FieldId>jdEQ6oboaZ</FieldId>
              <Index>8</Index>
              <Name>业务时间</Name>
              <Key>yd_business_time</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>9cK5LKC9AF</Id>
              <FieldId>9cK5LKC9AF</FieldId>
              <Index>9</Index>
              <Name>业务单据类型</Name>
              <Key>yd_bill_type</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>HpKVSosDMb</Id>
              <FieldId>HpKVSosDMb</FieldId>
              <Index>10</Index>
              <Name>费用类型</Name>
              <Key>yd_fee_type</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>pqUG0lil6F</Id>
              <FieldId>pqUG0lil6F</FieldId>
              <Index>11</Index>
              <Name>发票方向</Name>
              <Key>yd_ticket_object</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Ar2QHjVcUi</Id>
              <FieldId>Ar2QHjVcUi</FieldId>
              <Index>12</Index>
              <Name>结算方式</Name>
              <Key>yd_settlement_type</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ffmp7Va3id</Id>
              <FieldId>ffmp7Va3id</FieldId>
              <Index>13</Index>
              <Name>含税金额</Name>
              <Key>yd_receive_amt</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Qvad190iHu</Id>
              <FieldId>Qvad190iHu</FieldId>
              <Index>14</Index>
              <Name>未税金额</Name>
              <Key>yd_excluding_tax_amt</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>7tkzf6EvD1</Id>
              <FieldId>7tkzf6EvD1</FieldId>
              <Index>15</Index>
              <Name>税额</Name>
              <Key>yd_tax_amt</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>U195kYfGgn</Id>
              <FieldId>U195kYfGgn</FieldId>
              <Index>16</Index>
              <Name>税率</Name>
              <Key>yd_tax_rate</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>pO2Svq3qaQ</Id>
              <FieldId>pO2Svq3qaQ</FieldId>
              <Index>17</Index>
              <Name>货品编码</Name>
              <Key>yd_goods_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>HhPDm4xxIm</Id>
              <FieldId>HhPDm4xxIm</FieldId>
              <Index>18</Index>
              <Name>货品名称</Name>
              <Key>yd_goods_name</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>oo8mWu6iC0</Id>
              <FieldId>oo8mWu6iC0</FieldId>
              <Index>19</Index>
              <Name>存储单位</Name>
              <Key>yd_goods_unit</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>a0QhG15lHf</Id>
              <FieldId>a0QhG15lHf</FieldId>
              <Index>20</Index>
              <Name>计费数量</Name>
              <Key>yd_goods_num</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>7wGKXDdFyc</Id>
              <FieldId>7wGKXDdFyc</FieldId>
              <Index>21</Index>
              <Name>含税单价</Name>
              <Key>yd_tax_unit_price</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>kVVtbkOWxg</Id>
              <FieldId>kVVtbkOWxg</FieldId>
              <Index>22</Index>
              <Name>未税单价</Name>
              <Key>yd_no_tax_unit_price</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Fa2vL7rFl6</Id>
              <FieldId>Fa2vL7rFl6</FieldId>
              <Index>23</Index>
              <Name>是否重算</Name>
              <Key>yd_is_recalculate</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>DKfRltapB2</Id>
              <FieldId>DKfRltapB2</FieldId>
              <Index>24</Index>
              <Name>参考</Name>
              <Key>yd_reference</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>AxA7aalGfq</Id>
              <FieldId>AxA7aalGfq</FieldId>
              <Index>25</Index>
              <Name>唯一ID</Name>
              <Key>yd_union_id</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>O6NwcPNliA</Id>
              <FieldId>O6NwcPNliA</FieldId>
              <Index>26</Index>
              <Name>结算流水编号</Name>
              <Key>yd_check_bill_no</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Emp4FQDfsE</Id>
              <FieldId>Emp4FQDfsE</FieldId>
              <Index>27</Index>
              <Name>门店名称</Name>
              <Key>yd_shop_name</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>fz7IAAZtF2</Id>
              <FieldId>fz7IAAZtF2</FieldId>
              <Index>28</Index>
              <Name>前端商品编码</Name>
              <Key>yd_front_goods_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>uVm0CdgZgz</Id>
              <FieldId>uVm0CdgZgz</FieldId>
              <Index>29</Index>
              <Name>前端商品名称</Name>
              <Key>yd_front_goods_name</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>3ArN5ibS0u</Id>
              <FieldId>3ArN5ibS0u</FieldId>
              <Index>30</Index>
              <Name>费用说明</Name>
              <Key>yd_fee_description</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </EntryFieldAp>
            <EntryAp>
              <EntryId>4XSYA9K3OC3P</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>4XSYA9K3OC3P</Id>
              <Name>单据体</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>yd_bill_entity</Key>
              <ParentId>4XSYA9K3OCSX</ParentId>
            </EntryAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp action="edit" oid="bdJvQ9RWla">
              <Name>对账单编号</Name>
            </FieldAp>
            <FieldAp>
              <Id>393YWviDWH</Id>
              <FieldId>393YWviDWH</FieldId>
              <Index>5</Index>
              <Name>结算公司主体</Name>
              <Key>yd_company_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>535gKIs99s</Id>
              <FieldId>535gKIs99s</FieldId>
              <Index>6</Index>
              <Name>账单周期</Name>
              <Key>yd_bill_cycle</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>8BixfUZAL2</Id>
              <FieldId>8BixfUZAL2</FieldId>
              <Index>7</Index>
              <Name>账单生成时间</Name>
              <Key>yd_bill_create_time</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>hPOxvC63I5</Id>
              <FieldId>hPOxvC63I5</FieldId>
              <Index>8</Index>
              <Name>供应商编码</Name>
              <Key>yd_supplier_code</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>eh44QbFQhL</Id>
              <FieldId>eh44QbFQhL</FieldId>
              <Index>9</Index>
              <Name>供应商名称</Name>
              <Key>yd_supplier_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>dvPVr8AxGL</Id>
              <FieldId>dvPVr8AxGL</FieldId>
              <Index>10</Index>
              <Name>经营类型</Name>
              <Key>yd_operate_type</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>x4CFRtKzKg</Id>
              <FieldId>x4CFRtKzKg</FieldId>
              <Index>11</Index>
              <Name>结算币种</Name>
              <Key>yd_currency</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>QfzXtKyI25</Id>
              <FieldId>QfzXtKyI25</FieldId>
              <Index>12</Index>
              <Name>结算含税总额</Name>
              <Key>yd_receive_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>e7XZ6veP1m</Id>
              <FieldId>e7XZ6veP1m</FieldId>
              <Index>13</Index>
              <Name>货款含税总额</Name>
              <Key>yd_goods_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>ygJIU15cS6</Id>
              <FieldId>ygJIU15cS6</FieldId>
              <Index>14</Index>
              <Name>票扣含税总额</Name>
              <Key>yd_receipt_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>yoCLy5q2ON</Id>
              <FieldId>yoCLy5q2ON</FieldId>
              <Index>15</Index>
              <Name>应开票含税总额</Name>
              <Key>yd_invoice_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>qcp9btxFHV</Id>
              <FieldId>qcp9btxFHV</FieldId>
              <Index>16</Index>
              <Name>账扣含税总额</Name>
              <Key>yd_account_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>n74SDXVyWi</Id>
              <FieldId>n74SDXVyWi</FieldId>
              <Index>17</Index>
              <Name>应转账含税总额</Name>
              <Key>yd_payment_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>MhzpWBh2BN</Id>
              <FieldId>MhzpWBh2BN</FieldId>
              <Index>18</Index>
              <Name>对账状态</Name>
              <Key>yd_recon_status_desc</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>hGbRkHBno5</Id>
              <FieldId>hGbRkHBno5</FieldId>
              <Index>19</Index>
              <Name>发票状态</Name>
              <Key>yd_invoice_status_desc</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>tjX3NeD2Sm</Id>
              <FieldId>tjX3NeD2Sm</FieldId>
              <Index>20</Index>
              <Name>付款状态</Name>
              <Key>yd_payment_status_desc</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>2l62Rb7JkP</Id>
              <FieldId>2l62Rb7JkP</FieldId>
              <Index>21</Index>
              <Name>下推状态</Name>
              <Key>yd_send_status</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>8z7MRQtBV9</Id>
              <FieldId>8z7MRQtBV9</FieldId>
              <Index>22</Index>
              <Name>是否异常</Name>
              <Key>yd_is_exception</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>dJKvirQL9w</Id>
              <FieldId>dJKvirQL9w</FieldId>
              <Index>23</Index>
              <Name>异常原因</Name>
              <Key>yd_exception_content</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>ayaSniCecv</Id>
              <FieldId>ayaSniCecv</FieldId>
              <Index>24</Index>
              <Name>差异核算结果</Name>
              <Key>yd_difference</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>x6l7jj2roH</Id>
              <FieldId>x6l7jj2roH</FieldId>
              <Index>25</Index>
              <Name>销售出库单号</Name>
              <Key>yd_sap_sale_no</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>020XGExCdm</Id>
              <FieldId>020XGExCdm</FieldId>
              <Index>26</Index>
              <Name>平台金额</Name>
              <Key>yd_platform_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>VfUkW3xv4K</Id>
              <FieldId>VfUkW3xv4K</FieldId>
              <Index>27</Index>
              <Name>销售出库单金额</Name>
              <Key>yd_sale_bill_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>4XSYA9K3OC3M</Id>
              <Key>advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>分录</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Index>3</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border action="setnull"/>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338612817267262464</ModifierId>
      <EntityId>4XSYA9HPDKSJ</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1747278603761</Version>
      <ParentId>ab7efc31000010ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_my_tmall_market_bill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4XSYA9HPDKSJ</Id>
      <InheritPath>00305e8b000006ac,ab7efc31000010ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1747278604000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>4XSYA9HPDKSJ</Id>
          <ParentId>ab7efc31000010ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>猫超平台账单表</Name>
          <InheritPath>00305e8b000006ac,ab7efc31000010ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <PermissionDimension>
                <PermissionDimension>
                  <DataDimensionField>org</DataDimensionField>
                  <DataDimension>MwLyMGIJQa</DataDimension>
                </PermissionDimension>
              </PermissionDimension>
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_tmall_market_bill</TableName>
              <Operations>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>4XSYA9K3OC3P</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>4XSYA9K3OCT+</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>4XSYA9K3OC3P</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>4XSYA9K3OC3R</Id>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>4XSYA9HPDKSJ</Id>
              <Key>yd_my_tmall_market_bill</Key>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
              <Name>猫超平台账单表</Name>
            </BillEntity>
            <BillNoField action="edit" oid="bdJvQ9RWla">
              <Name>对账单编号</Name>
            </BillNoField>
            <BillStatusField action="edit" oid="6q69iznvHX">
              <DefValue>C</DefValue>
            </BillStatusField>
            <EntryEntity>
              <TableName>tk_yd_tmall_market_entry</TableName>
              <Id>4XSYA9K3OC3P</Id>
              <Key>yd_bill_entity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <ModifierField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4XSYA9K3OCSZ</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>修改人</Name>
              <FieldName>fmodifierfield</FieldName>
              <Key>modifierfield</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <BaseEntityId>68bde9ca00000eac</BaseEntityId>
            </ModifierField>
            <ModifyDateField>
              <FieldName>fmodifydatefield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4XSYA9K3OC3Q</Id>
              <Key>modifydatefield</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>修改日期</Name>
            </ModifyDateField>
            <TextField>
              <FieldName>fk_yd_company_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>393YWviDWH</Id>
              <Key>yd_company_name</Key>
              <MustInput>true</MustInput>
              <Name>结算公司主体</Name>
              <MaxLength>60</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_bill_cycle</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>535gKIs99s</Id>
              <Key>yd_bill_cycle</Key>
              <MustInput>true</MustInput>
              <Name>账单周期</Name>
            </TextField>
            <DateTimeField>
              <FieldName>fk_yd_bill_create_time</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>8BixfUZAL2</Id>
              <Key>yd_bill_create_time</Key>
              <Name>账单生成时间</Name>
            </DateTimeField>
            <TextField>
              <FieldName>fk_yd_supplier_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>hPOxvC63I5</Id>
              <Key>yd_supplier_code</Key>
              <Name>供应商编码</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_supplier_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>eh44QbFQhL</Id>
              <Key>yd_supplier_name</Key>
              <Name>供应商名称</Name>
              <MaxLength>60</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_operate_type</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>dvPVr8AxGL</Id>
              <Key>yd_operate_type</Key>
              <MustInput>true</MustInput>
              <Name>经营类型</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_currency</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>x4CFRtKzKg</Id>
              <Key>yd_currency</Key>
              <Name>结算币种</Name>
              <MaxLength>20</MaxLength>
            </TextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QfzXtKyI25</Id>
              <DefValue>0</DefValue>
              <Name>结算含税总额</Name>
              <FieldName>fk_yd_receive_total_amt</FieldName>
              <Key>yd_receive_total_amt</Key>
              <MustInput>true</MustInput>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>e7XZ6veP1m</Id>
              <DefValue>0</DefValue>
              <Name>货款含税总额</Name>
              <FieldName>fk_yd_goods_total_amt</FieldName>
              <Key>yd_goods_total_amt</Key>
              <MustInput>true</MustInput>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ygJIU15cS6</Id>
              <DefValue>0</DefValue>
              <Name>票扣含税总额</Name>
              <FieldName>fk_yd_receipt_total_amt</FieldName>
              <Key>yd_receipt_total_amt</Key>
              <MustInput>true</MustInput>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>yoCLy5q2ON</Id>
              <DefValue>0</DefValue>
              <Name>应开票含税总额</Name>
              <FieldName>fk_yd_invoice_total_amt</FieldName>
              <Key>yd_invoice_total_amt</Key>
              <MustInput>true</MustInput>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>qcp9btxFHV</Id>
              <DefValue>0</DefValue>
              <Name>账扣含税总额</Name>
              <FieldName>fk_yd_account_total_amt</FieldName>
              <Key>yd_account_total_amt</Key>
              <MustInput>true</MustInput>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>n74SDXVyWi</Id>
              <DefValue>0</DefValue>
              <Name>应转账含税总额</Name>
              <FieldName>fk_yd_payment_total_amt</FieldName>
              <Key>yd_payment_total_amt</Key>
              <MustInput>true</MustInput>
            </AmountField>
            <TextField>
              <FieldName>fk_yd_recon_status_desc</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>MhzpWBh2BN</Id>
              <Key>yd_recon_status_desc</Key>
              <MustInput>true</MustInput>
              <Name>对账状态</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_invoice_status_desc</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>hGbRkHBno5</Id>
              <Key>yd_invoice_status_desc</Key>
              <MustInput>true</MustInput>
              <Name>发票状态</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_payment_status_desc</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>tjX3NeD2Sm</Id>
              <Key>yd_payment_status_desc</Key>
              <MustInput>true</MustInput>
              <Name>付款状态</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_fbillno</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>04t8EoxaPR</Id>
              <Key>yd_fbillno</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>对账单编号</Name>
              <MaxLength>60</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_business_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>I4fd6rniB1</Id>
              <Key>yd_business_code</Key>
              <MustInput>true</MustInput>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>业务主单据编码</Name>
              <MaxLength>60</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_sub_business_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>YLAXOm6Nnd</Id>
              <Key>yd_sub_business_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>业务子单据编码</Name>
              <MaxLength>60</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_deal_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xNq1th3rbr</Id>
              <Key>yd_deal_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>交易主单</Name>
              <MaxLength>80</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_sub_deal_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Y2hBEvfeEw</Id>
              <Key>yd_sub_deal_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>交易子单</Name>
              <MaxLength>80</MaxLength>
            </TextField>
            <DateTimeField>
              <FieldName>fk_yd_pay_time</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>HaUwBBpSQC</Id>
              <Key>yd_pay_time</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>支付时间</Name>
            </DateTimeField>
            <DateTimeField>
              <FieldName>fk_yd_business_time</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>jdEQ6oboaZ</Id>
              <Key>yd_business_time</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>业务时间</Name>
            </DateTimeField>
            <TextField>
              <FieldName>fk_yd_bill_type</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>9cK5LKC9AF</Id>
              <Key>yd_bill_type</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>业务单据类型</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_fee_type</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>HpKVSosDMb</Id>
              <Key>yd_fee_type</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>费用类型</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_ticket_object</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pqUG0lil6F</Id>
              <Key>yd_ticket_object</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>发票方向</Name>
              <MaxLength>30</MaxLength>
            </TextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ffmp7Va3id</Id>
              <DefValue>0</DefValue>
              <Name>含税金额</Name>
              <FieldName>fk_yd_receive_amt</FieldName>
              <Key>yd_receive_amt</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Qvad190iHu</Id>
              <DefValue>0</DefValue>
              <Name>未税金额</Name>
              <FieldName>fk_yd_excluding_tax_amt</FieldName>
              <Key>yd_excluding_tax_amt</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>7tkzf6EvD1</Id>
              <DefValue>0</DefValue>
              <Name>税额</Name>
              <FieldName>fk_yd_tax_amt</FieldName>
              <Key>yd_tax_amt</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>U195kYfGgn</Id>
              <DefValue>0</DefValue>
              <Name>税率</Name>
              <FieldName>fk_yd_tax_rate</FieldName>
              <Key>yd_tax_rate</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </AmountField>
            <TextField>
              <FieldName>fk_yd_goods_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pO2Svq3qaQ</Id>
              <Key>yd_goods_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>货品编码</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_goods_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>HhPDm4xxIm</Id>
              <Key>yd_goods_name</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>货品名称</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_goods_unit</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>oo8mWu6iC0</Id>
              <Key>yd_goods_unit</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>存储单位</Name>
            </TextField>
            <IntegerField>
              <FieldName>fk_yd_goods_num</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>a0QhG15lHf</Id>
              <Key>yd_goods_num</Key>
              <DefValue>0</DefValue>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>计费数量</Name>
            </IntegerField>
            <PriceField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>7wGKXDdFyc</Id>
              <DefValue>0</DefValue>
              <Name>含税单价</Name>
              <FieldName>fk_yd_tax_unit_price</FieldName>
              <Key>yd_tax_unit_price</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </PriceField>
            <PriceField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kVVtbkOWxg</Id>
              <DefValue>0</DefValue>
              <Name>未税单价</Name>
              <FieldName>fk_yd_no_tax_unit_price</FieldName>
              <Key>yd_no_tax_unit_price</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
            </PriceField>
            <TextField>
              <FieldName>fk_yd_is_recalculate</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Fa2vL7rFl6</Id>
              <Key>yd_is_recalculate</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>是否重算</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_reference</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>DKfRltapB2</Id>
              <Key>yd_reference</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>参考</Name>
              <MaxLength>512</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_union_id</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>AxA7aalGfq</Id>
              <Key>yd_union_id</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>唯一ID</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_shop_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Emp4FQDfsE</Id>
              <Key>yd_shop_name</Key>
              <MustInput>true</MustInput>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>门店名称</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_front_goods_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>fz7IAAZtF2</Id>
              <Key>yd_front_goods_code</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>前端商品编码</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_fee_description</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3ArN5ibS0u</Id>
              <Key>yd_fee_description</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>费用说明</Name>
              <MaxLength>512</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_check_bill_no</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>O6NwcPNliA</Id>
              <Key>yd_check_bill_no</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>结算流水编号</Name>
              <MaxLength>60</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_front_goods_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>uVm0CdgZgz</Id>
              <Key>yd_front_goods_name</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>前端商品名称</Name>
              <MaxLength>80</MaxLength>
            </TextField>
            <ComboField>
              <FieldName>fk_yd_send_status</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2l62Rb7JkP</Id>
              <Key>yd_send_status</Key>
              <MustInput>true</MustInput>
              <Name>下推状态</Name>
              <Items>
                <ComboItem>
                  <Caption>待推送</Caption>
                  <Value>待推送</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>下推成功</Caption>
                  <Value>下推成功</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>下推失败</Caption>
                  <Value>下推失败</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <FieldName>fk_yd_is_exception</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>8z7MRQtBV9</Id>
              <Key>yd_is_exception</Key>
              <DefValue/>
              <Name>是否异常</Name>
              <MaxLength>10</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_exception_content</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>dJKvirQL9w</Id>
              <Key>yd_exception_content</Key>
              <Name>异常原因</Name>
              <MaxLength>500</MaxLength>
            </TextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ayaSniCecv</Id>
              <DefValue>0</DefValue>
              <Name>差异核算结果</Name>
              <FieldName>fk_yd_difference</FieldName>
              <Key>yd_difference</Key>
            </AmountField>
            <TextField>
              <FieldName>fk_yd_sap_sale_no</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>x6l7jj2roH</Id>
              <Key>yd_sap_sale_no</Key>
              <Name>销售出库单号</Name>
              <MaxLength>80</MaxLength>
            </TextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>020XGExCdm</Id>
              <DefValue>0</DefValue>
              <Name>平台金额</Name>
              <FieldName>fk_yd_platform_amt</FieldName>
              <Key>yd_platform_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>VfUkW3xv4K</Id>
              <DefValue>0</DefValue>
              <Name>销售出库单金额</Name>
              <FieldName>fk_yd_sale_bill_amt</FieldName>
              <Key>yd_sale_bill_amt</Key>
            </AmountField>
            <TextField>
              <FieldName>fk_yd_settlement_type</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Ar2QHjVcUi</Id>
              <Key>yd_settlement_type</Key>
              <ParentId>4XSYA9K3OC3P</ParentId>
              <Name>结算方式</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1747278603840</Version>
      <ParentId>ab7efc31000010ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_my_tmall_market_bill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4XSYA9HPDKSJ</Id>
      <InheritPath>00305e8b000006ac,ab7efc31000010ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1747278603761</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
