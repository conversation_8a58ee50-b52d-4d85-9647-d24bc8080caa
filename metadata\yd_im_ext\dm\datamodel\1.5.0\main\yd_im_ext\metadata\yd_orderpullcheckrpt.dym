<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>5/FE2BZ9QK+R</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>5/FE2BZ9QK+R</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1749718265000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>5/FE2BZ9QK+R</Id>
          <Key>yd_orderpullcheckrpt</Key>
          <EntityId>5/FE2BZ9QK+R</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>拉单核对表</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>拉单核对报表表单插件</Description>
                  <ClassName>kd.bos.tcbj.ec.report.OrderPullCheckRptFormPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>5/FE2BZ9QK+R</Id>
              <Name>拉单核对表</Name>
              <Key>yd_orderpullcheckrpt</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ReportPlugin>kd.bos.tcbj.ec.report.OrderPullCheckRptListDataPlugin</ReportPlugin>
            </ReportListAp>
            <FieldAp>
              <Id>HNBVeqCTeP</Id>
              <FieldId>HNBVeqCTeP</FieldId>
              <Name>开始日期</Name>
              <Key>yd_startdate</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>OHoqrlaV0t</Id>
              <FieldId>OHoqrlaV0t</FieldId>
              <Index>1</Index>
              <Name>结束日期</Name>
              <Key>yd_enddate</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>jfk6RKNNKF</Id>
              <FieldId>jfk6RKNNKF</FieldId>
              <Index>2</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>退货</Name>
              <Key>yd_ordertypeitem</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>C1WDstGrEK</Id>
              <FieldId>C1WDstGrEK</FieldId>
              <Index>3</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>均摊金额判断</Name>
              <Key>yd_shareamountitem</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Wn9as0f7L6</Id>
              <FieldId>Wn9as0f7L6</FieldId>
              <Index>4</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>均摊运费判断</Name>
              <Key>yd_shareshippingfeeitem</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>1FywjBJvGb</Id>
              <FieldId>1FywjBJvGb</FieldId>
              <Index>5</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>数量判断</Name>
              <Key>yd_qtyitem</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Uae1USE9i5</Id>
              <FieldId>Uae1USE9i5</FieldId>
              <Index>6</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>单数判断</Name>
              <Key>yd_ordernumitem</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>VgxJV2Wune</Id>
              <FieldId>VgxJV2Wune</FieldId>
              <Index>7</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>店铺编码(支持模糊查询)</Name>
              <Key>yd_shopcodeitem</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Xz5k2po67D</Id>
              <FieldId>Xz5k2po67D</FieldId>
              <Index>8</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>平台</Name>
              <Key>yd_platformitem</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>hSW4v13p0b</Id>
              <FieldId>hSW4v13p0b</FieldId>
              <Index>9</Index>
              <Name>组织</Name>
              <Key>yd_orgitemid</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>HL2tUxFj1o</Id>
              <FieldId>HL2tUxFj1o</FieldId>
              <Name>平台</Name>
              <Key>yd_platform</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ZJhDfhWlR9</Id>
              <FieldId>ZJhDfhWlR9</FieldId>
              <Index>1</Index>
              <Name>退货</Name>
              <Key>yd_ordertype</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>pIUdWxHwQQ</Id>
              <FieldId>pIUdWxHwQQ</FieldId>
              <Index>2</Index>
              <Name>组织</Name>
              <Key>yd_orgid</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>dHdoeqVBgc</Id>
              <FieldId>dHdoeqVBgc</FieldId>
              <Index>3</Index>
              <Name>店铺编码</Name>
              <Key>yd_shopcode</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>7DyBROQoc6</Id>
              <FieldId>7DyBROQoc6</FieldId>
              <Index>4</Index>
              <Name>店铺名称</Name>
              <Key>yd_shopname</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Yrcx40Bhge</Id>
              <FieldId>Yrcx40Bhge</FieldId>
              <Index>5</Index>
              <Name>平台订单总单数</Name>
              <Key>yd_omstotalordernum</Key>
              <Summary>1</Summary>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>5Zn3NsiV8I</Id>
              <FieldId>5Zn3NsiV8I</FieldId>
              <Index>6</Index>
              <Name>苍穹订单总单数</Name>
              <Key>yd_cqtotalordernum</Key>
              <Summary>1</Summary>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>mQx4O7cHJi</Id>
              <FieldId>mQx4O7cHJi</FieldId>
              <Index>7</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>单数数据错误</Name>
              <Key>yd_istotalordernumerror</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>HFT5bEAGmX</Id>
              <FieldId>HFT5bEAGmX</FieldId>
              <Index>8</Index>
              <Name>平台订单物料总数量</Name>
              <Key>yd_omsmattotalqty</Key>
              <Summary>1</Summary>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>MHsgH5KKaJ</Id>
              <FieldId>MHsgH5KKaJ</FieldId>
              <Index>9</Index>
              <Name>苍穹订单物料总数量</Name>
              <Key>yd_cqmattotalqty</Key>
              <Summary>1</Summary>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>kwcut92hXR</Id>
              <FieldId>kwcut92hXR</FieldId>
              <Index>10</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>数量数据错误</Name>
              <Key>yd_ismatqtyerror</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>dQP4lkRKWz</Id>
              <FieldId>dQP4lkRKWz</FieldId>
              <Index>11</Index>
              <Name>平台订单均摊金额</Name>
              <Key>yd_omsshareamount</Key>
              <Summary>1</Summary>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Wo47JdlOrJ</Id>
              <FieldId>Wo47JdlOrJ</FieldId>
              <Index>12</Index>
              <Name>苍穹订单均摊金额</Name>
              <Key>yd_cqshareamount</Key>
              <Summary>1</Summary>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>TVGQRkWPoO</Id>
              <FieldId>TVGQRkWPoO</FieldId>
              <Index>13</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>均摊金额数据错误</Name>
              <Key>yd_isshareamount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>jh7V8xNAuu</Id>
              <FieldId>jh7V8xNAuu</FieldId>
              <Index>14</Index>
              <Name>平台订单均摊运费</Name>
              <Key>yd_omssharefreight</Key>
              <Summary>1</Summary>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>JZS9TG3vi3</Id>
              <FieldId>JZS9TG3vi3</FieldId>
              <Index>15</Index>
              <Name>苍穹订单均摊运费</Name>
              <Key>yd_cqsharefreight</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>VsUnOhRCrr</Id>
              <FieldId>VsUnOhRCrr</FieldId>
              <Index>16</Index>
              <BackColor>rgba(39,111,245,0.5)</BackColor>
              <Name>均摊运费数据错误</Name>
              <Key>yd_issharefreighterror</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338618754925331456</ModifierId>
      <EntityId>5/FE2BZ9QK+R</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749718264911</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_orderpullcheckrpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>5/FE2BZ9QK+R</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1749718265000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>5/FE2BZ9QK+R</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>拉单核对表</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>5/FE2BZ9QK+R</Id>
              <Key>yd_orderpullcheckrpt</Key>
              <Name>拉单核对表</Name>
            </ReportEntity>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>HNBVeqCTeP</Id>
              <Key>yd_startdate</Key>
              <MustInput>true</MustInput>
              <Name>开始日期</Name>
              <DefValueDesign>
                <DefValueDesign>
                  <FuncParameter>#CurrentDate#</FuncParameter>
                  <FuncType>getToday</FuncType>
                </DefValueDesign>
              </DefValueDesign>
            </DateField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>OHoqrlaV0t</Id>
              <Key>yd_enddate</Key>
              <MustInput>true</MustInput>
              <Name>结束日期</Name>
              <DefValueDesign>
                <DefValueDesign>
                  <FuncParameter>#CurrentDate#</FuncParameter>
                  <FuncType>getToday</FuncType>
                </DefValueDesign>
              </DefValueDesign>
            </DateField>
            <ComboField>
              <FieldName>fk_yd_thgl</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>jfk6RKNNKF</Id>
              <Key>yd_ordertypeitem</Key>
              <MustInput>true</MustInput>
              <DefValue>2</DefValue>
              <Name>退货</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>退货</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>非退货</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <FieldName>fk_yd_jegl</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>C1WDstGrEK</Id>
              <Key>yd_shareamountitem</Key>
              <MustInput>true</MustInput>
              <DefValue>2</DefValue>
              <Name>均摊金额判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>金额一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>金额不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <FieldName>fk_yd_slgl</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1FywjBJvGb</Id>
              <Key>yd_qtyitem</Key>
              <MustInput>true</MustInput>
              <DefValue>2</DefValue>
              <Name>数量判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>数量一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>数量不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <FieldName>fk_yd_dpgl</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>VgxJV2Wune</Id>
              <Key>yd_shopcodeitem</Key>
              <Name>店铺编码(支持模糊查询)</Name>
            </TextField>
            <ComboField>
              <FieldName>fk_yd_dsgl</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Uae1USE9i5</Id>
              <Key>yd_ordernumitem</Key>
              <MustInput>true</MustInput>
              <DefValue>2</DefValue>
              <Name>单数判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>单数一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>单数不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <FieldName>fk_yd_pt</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Xz5k2po67D</Id>
              <Key>yd_platformitem</Key>
              <DefValue>0</DefValue>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>不过滤</Caption>
                  <Value>0</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>3</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>万里牛</Caption>
                  <Value>4</Value>
                  <Seq>4</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>新E3</Caption>
                  <Value>5</Value>
                  <Seq>5</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>hSW4v13p0b</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>组织</Name>
              <FieldName>fk_yd_zzgl</FieldName>
              <Key>yd_orgitemid</Key>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
            <ComboField>
              <FieldName>fk_yd_mxpt</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>HL2tUxFj1o</Id>
              <Key>yd_platform</Key>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>万里牛</Caption>
                  <Value>4</Value>
                  <Seq>3</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>新E3</Caption>
                  <Value>5</Value>
                  <Seq>4</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <CheckBoxField>
              <FieldName>fk_yd_th</FieldName>
              <ShowStyle>1</ShowStyle>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ZJhDfhWlR9</Id>
              <Key>yd_ordertype</Key>
              <Name>退货</Name>
            </CheckBoxField>
            <OrgField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pIUdWxHwQQ</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>组织</Name>
              <FieldName>fk_yd_zz</FieldName>
              <Key>yd_orgid</Key>
              <BaseEntityId>73f9bf0200001dac</BaseEntityId>
            </OrgField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>dHdoeqVBgc</Id>
              <Key>yd_shopcode</Key>
              <Name>店铺编码</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_textfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>7DyBROQoc6</Id>
              <Key>yd_shopname</Key>
              <Name>店铺名称</Name>
            </TextField>
            <IntegerField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Yrcx40Bhge</Id>
              <Key>yd_omstotalordernum</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>平台订单总单数</Name>
            </IntegerField>
            <IntegerField>
              <FieldName>fk_yd_integerfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5Zn3NsiV8I</Id>
              <Key>yd_cqtotalordernum</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>苍穹订单总单数</Name>
            </IntegerField>
            <CheckBoxField>
              <FieldName>fk_yd_dssjcw</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kwcut92hXR</Id>
              <Key>yd_ismatqtyerror</Key>
              <Name>数量数据错误</Name>
            </CheckBoxField>
            <DecimalField>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>HFT5bEAGmX</Id>
              <Key>yd_omsmattotalqty</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>平台订单物料总数量</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_decimalfield</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>MHsgH5KKaJ</Id>
              <Key>yd_cqmattotalqty</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>苍穹订单物料总数量</Name>
            </DecimalField>
            <CheckBoxField>
              <FieldName>fk_yd_checkboxfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>mQx4O7cHJi</Id>
              <Key>yd_istotalordernumerror</Key>
              <Name>单数数据错误</Name>
            </CheckBoxField>
            <DecimalField>
              <FieldName>fk_yd_omsmattotalqty1</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>dQP4lkRKWz</Id>
              <Key>yd_omsshareamount</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>平台订单均摊金额</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_omsshareamount1</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Wo47JdlOrJ</Id>
              <Key>yd_cqshareamount</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>苍穹订单均摊金额</Name>
            </DecimalField>
            <CheckBoxField>
              <FieldName>fk_yd_ismatqtyerror1</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>TVGQRkWPoO</Id>
              <Key>yd_isshareamount</Key>
              <Name>均摊金额数据错误</Name>
            </CheckBoxField>
            <DecimalField>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>jh7V8xNAuu</Id>
              <Key>yd_omssharefreight</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>平台订单均摊运费</Name>
            </DecimalField>
            <DecimalField>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>JZS9TG3vi3</Id>
              <Key>yd_cqsharefreight</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>苍穹订单均摊运费</Name>
            </DecimalField>
            <CheckBoxField>
              <FieldName>fk_yd_isshareamount1</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>VsUnOhRCrr</Id>
              <Key>yd_issharefreighterror</Key>
              <Name>均摊运费数据错误</Name>
            </CheckBoxField>
            <ComboField>
              <FieldName>fk_yd_combofield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Wn9as0f7L6</Id>
              <Key>yd_shareshippingfeeitem</Key>
              <MustInput>true</MustInput>
              <DefValue>2</DefValue>
              <Name>均摊运费判断</Name>
              <Items>
                <ComboItem>
                  <Caption>全部</Caption>
                  <Value>2</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>金额一致</Caption>
                  <Value>1</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>金额不一致</Caption>
                  <Value>0</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749718264979</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_orderpullcheckrpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>5/FE2BZ9QK+R</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1749718264911</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
