<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>2QV6P08IGQ+R</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>2QV6P08IGQ+R</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1663766424000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>2QV6P08IGQ+R</Id>
          <Key>yd_pcpparamfilter</Key>
          <EntityId>2QV6P08IGQ+R</EntityId>
          <ParentId>62b02ccc00005fac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>参数过滤</Name>
          <InheritPath>62b02ccc00005fac</InheritPath>
          <Items>
            <FormAp action="edit" oid="62b02ccc00005eac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>PCP参数过滤插件</Description>
                  <ClassName>kd.bos.tcbj.im.outbill.plugin.PcpParamFilterPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>2QV6P08IGQ+R</Id>
              <Name>参数过滤</Name>
              <Key>yd_pcpparamfilter</Key>
            </FormAp>
            <FlexPanelAp action="edit" oid="vXXb0qB9sk">
              <AlignItems>center</AlignItems>
              <JustifyContent>center</JustifyContent>
              <Style>
                <Style>
                  <Padding>
                    <Padding>
                      <Left>30px</Left>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp>
              <Id>d7YqclrICR</Id>
              <FieldId>d7YqclrICR</FieldId>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>开始日期</Name>
              <Key>yd_begindate</Key>
              <ParentId>vXXb0qB9sk</ParentId>
              <Width>80%</Width>
            </FieldAp>
            <FieldAp>
              <Id>8RWxKlApAG</Id>
              <FieldId>8RWxKlApAG</FieldId>
              <Index>1</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>结束日期</Name>
              <Key>yd_enddate</Key>
              <ParentId>vXXb0qB9sk</ParentId>
              <Width>80%</Width>
            </FieldAp>
            <FieldAp>
              <Id>MZKJ29FJPh</Id>
              <FieldId>MZKJ29FJPh</FieldId>
              <Index>2</Index>
              <Name>PCP出库结果单号（选填）</Name>
              <Key>yd_billno</Key>
              <ParentId>vXXb0qB9sk</ParentId>
              <Width>80%</Width>
            </FieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1308009068819907584</ModifierId>
      <EntityId>2QV6P08IGQ+R</EntityId>
      <ModelType>DynamicFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1663766424049</Version>
      <ParentId>62b02ccc00005fac</ParentId>
      <MasterId></MasterId>
      <Number>yd_pcpparamfilter</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>2QV6P08IGQ+R</Id>
      <InheritPath>62b02ccc00005fac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1663766424000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>2QV6P08IGQ+R</Id>
          <ParentId>62b02ccc00005fac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>参数过滤</Name>
          <InheritPath>62b02ccc00005fac</InheritPath>
          <Items>
            <MainEntity action="edit" oid="62b02ccc000060ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>2QV6P08IGQ+R</Id>
              <Key>yd_pcpparamfilter</Key>
              <Name>参数过滤</Name>
            </MainEntity>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>MZKJ29FJPh</Id>
              <Key>yd_billno</Key>
              <Name>PCP出库结果单号（选填）</Name>
            </TextField>
            <DateTimeField>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>d7YqclrICR</Id>
              <Key>yd_begindate</Key>
              <MustInput>true</MustInput>
              <Name>开始日期</Name>
            </DateTimeField>
            <DateTimeField>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>8RWxKlApAG</Id>
              <Key>yd_enddate</Key>
              <Name>结束日期</Name>
            </DateTimeField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>DynamicFormModel</ModelType>
      <Isv>kingdee</Isv>
      <Version>1663766424090</Version>
      <ParentId>62b02ccc00005fac</ParentId>
      <MasterId></MasterId>
      <Number>yd_pcpparamfilter</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>2QV6P08IGQ+R</Id>
      <InheritPath>62b02ccc00005fac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1663766424049</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
