<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>3KVR+GM/9KB/</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>3KVR+GM/9KB/</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1695635721000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>3KVR+GM/9KB/</Id>
          <Key>yd_purinlotbill</Key>
          <EntityId>3KVR+GM/9KB/</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>采购入库批次结果单</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>3KVR+GZRY/V/</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3KVR+GZRY/5S</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>3KVR+GZRY/V/</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3KVR+GZRY/V0</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>3KVR+GZRY/V/</ParentId>
                      <ListFieldId>creator.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>创建人.姓名</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>3KVR+GM/9KB/</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>3KVR+GZRY/V/</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Direction>column</Direction>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>3KVR+GZRY/5R</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>3KVR+GM/9KB/</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>采购入库批次结果单</Name>
              <Key>yd_purinlotbill</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>3KVR+GZRY/5O</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>3KVR+GZRY/UY</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>3KVR+GZRY/5O</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>3KVR+GZRY/5P</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>3KVR+GZRY/5O</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>3KVR+GZRY/UZ</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>3KVR+GZRY/UY</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>3KVR+GZRY/5Q</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>3KVR+GZRY/UY</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>3KVR+GZRY/V+</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Name>采购入库批次结果单</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Name>采购入库批次结果单</Name>
                    </FormAp>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>3KVR+GM/9KB/</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <EntryAp>
              <EntryId>3KVR+GZO=LNG</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>3KVR+GZO=LNG</Id>
              <Name>单据体</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>entryentity</Key>
              <ParentId>3KVR+GZO=L+5</ParentId>
            </EntryAp>
            <AdvConSummaryPanelAp>
              <Id>3KVR+GZO=L+4</Id>
              <Name>高级面板摘要容器</Name>
              <Key>advconsummarypanelap</Key>
              <ParentId>3KVR+GZO=LND</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>3KVR+GZO=LNE</Id>
              <Key>advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>3KVR+GZO=LND</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>3KVR+GZO=L+5</Id>
              <AlignItems>stretch</AlignItems>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>advconchildcanelap</Key>
              <Direction>column</Direction>
              <ParentId>3KVR+GZO=LND</ParentId>
              <Wrap>false</Wrap>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>3KVR+GZO=LNF</Id>
              <Key>tb_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>3KVR+GZO=LNE</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>3KVR+GZO=L+6</Id>
              <Key>tb_del</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>3KVR+GZO=LNE</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryFieldAp>
              <Id>3KVR+GZO=L+7</Id>
              <FieldId>3KVR+GZO=L+7</FieldId>
              <Name>修改人</Name>
              <Hidden>true</Hidden>
              <Key>modifierfield</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>3KVR+GZO=LNH</Id>
              <FieldId>3KVR+GZO=LNH</FieldId>
              <Index>1</Index>
              <Name>修改时间</Name>
              <Hidden>true</Hidden>
              <Key>modifydatefield</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>mIRKM4uxX8</Id>
              <FieldId>mIRKM4uxX8</FieldId>
              <Index>2</Index>
              <Name>物料</Name>
              <Key>yd_material</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>hyNJUWspHO</Id>
              <FieldId>hyNJUWspHO</FieldId>
              <Index>3</Index>
              <Name>仓库</Name>
              <Key>yd_warehouse</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>qxrM0Gkhaz</Id>
              <FieldId>qxrM0Gkhaz</FieldId>
              <Index>4</Index>
              <Name>批次</Name>
              <Key>yd_lot</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Akz09mUENW</Id>
              <FieldId>Akz09mUENW</FieldId>
              <Index>5</Index>
              <Name>生产日期</Name>
              <Key>yd_begindate</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>RS0hPQxEb5</Id>
              <FieldId>RS0hPQxEb5</FieldId>
              <Index>6</Index>
              <Name>到期日</Name>
              <Key>yd_enddate</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>vvj0cjmugK</Id>
              <FieldId>vvj0cjmugK</FieldId>
              <Index>7</Index>
              <Name>数量</Name>
              <Key>yd_qty</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>30bP1jMwTv</Id>
              <FieldId>30bP1jMwTv</FieldId>
              <Index>8</Index>
              <Name>源E3仓库</Name>
              <Key>yd_oriwarehousename</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
            </EntryFieldAp>
            <BarItemAp>
              <OperationKey>resetsettle</OperationKey>
              <Id>yhil60BPOH</Id>
              <Key>yd_resetsettle</Key>
              <OperationStyle>0</OperationStyle>
              <Index>8</Index>
              <ParentId>KEFmJcZ1zW</ParentId>
              <Name>重置批次</Name>
            </BarItemAp>
            <BarItemAp action="edit" oid="h3UcTwsBYy">
              <Index>9</Index>
            </BarItemAp>
            <BarItemAp action="edit" oid="D82caHpEvG">
              <Index>10</Index>
            </BarItemAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp>
              <Id>7mf92yfHV3</Id>
              <FieldId>7mf92yfHV3</FieldId>
              <Index>4</Index>
              <Name>采购入库单号</Name>
              <Key>yd_purinbillno</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>8phH7DLejQ</Id>
              <FieldId>8phH7DLejQ</FieldId>
              <Index>5</Index>
              <Name>采购入库单ID</Name>
              <Key>yd_purinbillid</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>ncZHiFNAMz</Id>
              <FieldId>ncZHiFNAMz</FieldId>
              <Index>6</Index>
              <Name>错误信息</Name>
              <Key>yd_errmsg</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>3KVR+GZO=LND</Id>
              <Key>advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>分录</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Index>3</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338616377744819200</ModifierId>
      <EntityId>3KVR+GM/9KB/</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1695635721006</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_purinlotbill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>3KVR+GM/9KB/</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1695635721000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>3KVR+GM/9KB/</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>采购入库批次结果单</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_purinlotbill</TableName>
              <Operations>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>3KVR+GZO=LNG</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>3KVR+GZRY/5N</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>3KVR+GZO=LNG</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>3KVR+GZRY/UX</Id>
                  <Key>deleteentry</Key>
                </Operation>
                <Operation>
                  <Plugins>
                    <Plugin>
                      <FPK/>
                      <Description>重置批次</Description>
                      <ClassName>kd.bos.tcbj.im.outbill.oplugin.PurInLotBillOp</ClassName>
                      <Enabled>true</Enabled>
                      <BizAppId/>
                    </Plugin>
                  </Plugins>
                  <ConfirmMsg/>
                  <Name>重置批次</Name>
                  <OperationType>donothing</OperationType>
                  <Id>3KW=UXX3DTNW</Id>
                  <SuccessMsg/>
                  <Key>resetsettle</Key>
                </Operation>
              </Operations>
              <Id>3KVR+GM/9KB/</Id>
              <Key>yd_purinlotbill</Key>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
              <Name>采购入库批次结果单</Name>
            </BillEntity>
            <EntryEntity>
              <TableName>tk_yd_purinlotbill_e</TableName>
              <Id>3KVR+GZO=LNG</Id>
              <Key>entryentity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <ModifierField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3KVR+GZO=L+7</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>修改人</Name>
              <FieldName>fmodifierfield</FieldName>
              <Key>modifierfield</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <BaseEntityId>68bde9ca00000eac</BaseEntityId>
            </ModifierField>
            <ModifyDateField>
              <FieldName>fmodifydatefield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3KVR+GZO=LNH</Id>
              <Key>modifydatefield</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <Name>修改日期</Name>
            </ModifyDateField>
            <TextField>
              <FieldName>fk_yd_purinbillno</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>7mf92yfHV3</Id>
              <Key>yd_purinbillno</Key>
              <Name>采购入库单号</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_purinbillid</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>8phH7DLejQ</Id>
              <Key>yd_purinbillid</Key>
              <Name>采购入库单ID</Name>
            </TextField>
            <MaterielField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>mIRKM4uxX8</Id>
              <BizBasedata>true</BizBasedata>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>物料</Name>
              <FieldName>fk_yd_materialid</FieldName>
              <Key>yd_material</Key>
              <RefLayout/>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <BaseEntityId>17ASU9RBN7SH</BaseEntityId>
            </MaterielField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>hyNJUWspHO</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>仓库</Name>
              <FieldName>fk_yd_warehouseid</FieldName>
              <Key>yd_warehouse</Key>
              <RefLayout/>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <BaseEntityId>14G1K2YGTJGQ</BaseEntityId>
            </BasedataField>
            <TextField>
              <FieldName>fk_yd_lot</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>qxrM0Gkhaz</Id>
              <Key>yd_lot</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <Name>批次</Name>
            </TextField>
            <DateField>
              <FieldName>fk_yd_begindate</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>Akz09mUENW</Id>
              <Key>yd_begindate</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <Name>生产日期</Name>
            </DateField>
            <DateField>
              <FieldName>fk_yd_enddate</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>RS0hPQxEb5</Id>
              <Key>yd_enddate</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <Name>到期日</Name>
            </DateField>
            <TextField>
              <FieldName>fk_yd_oriwarehousename</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>30bP1jMwTv</Id>
              <Key>yd_oriwarehousename</Key>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <Name>源E3仓库</Name>
            </TextField>
            <DecimalField>
              <FieldName>fk_yd_qty</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>vvj0cjmugK</Id>
              <Key>yd_qty</Key>
              <DefValue>0</DefValue>
              <ParentId>3KVR+GZO=LNG</ParentId>
              <Name>数量</Name>
            </DecimalField>
            <TextField>
              <FieldName>fk_yd_errmsg</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ncZHiFNAMz</Id>
              <Key>yd_errmsg</Key>
              <Name>错误信息</Name>
              <MaxLength>2000</MaxLength>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1695635721360</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_purinlotbill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>3KVR+GM/9KB/</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1695635721006</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y6/MJH2P8O</BizunitId>
</DeployMetadata>
