<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>5/L/IY6NBQT=</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>5/L/IY6NBQT=</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1749882236000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>5/L/IY6NBQT=</Id>
          <Key>yd_purwarehousemapping</Key>
          <EntityId>5/L/IY6NBQT=</EntityId>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>采购仓库映射表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BasedataFormAp action="edit" oid="1942c188000064ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/L/IY6NBQT=</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>5/L/IY6NBQT=</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>采购仓库映射表</Name>
              <Key>yd_purwarehousemapping</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>采购仓库映射表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000024ac">
                      <Name>采购仓库映射表</Name>
                    </FormAp>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/L/IY6NBQT=</EntityId>
                    </BillListAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/L/XP7Z98ZK</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Hyperlink>true</Hyperlink>
                      <Index>1</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_warehouse.number</ListFieldId>
                      <Name>SAP仓库编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/L/XQ4R=2FR</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>2</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_warehouse.name</ListFieldId>
                      <Name>SAP仓库名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/L/XRKIX70F</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_e3warehouseno</ListFieldId>
                      <Name>E3入库仓库编码</Name>
                    </ListColumnAp>
                    <ComboListColumnAp action="edit" oid="UGA0V8HS=6S">
                      <Index>4</Index>
                    </ComboListColumnAp>
                    <ListColumnAp action="remove" oid="b5994054000028ac"/>
                    <ListColumnAp action="remove" oid="b5994054000029ac"/>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <FlexPanelAp action="edit" oid="LL4BVAnADa">
              <Index action="reset"/>
            </FlexPanelAp>
            <FlexPanelAp action="edit" oid="PxNfkkak4s">
              <Grow>0</Grow>
              <Index>1</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                  <Padding>
                    <Padding>
                      <Left>2px</Left>
                      <Right action="reset"/>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp action="edit" oid="h736cFwZ6e">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="8oq6R4m9CF">
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp>
              <Id>bukg7StpV3</Id>
              <FieldId>bukg7StpV3</FieldId>
              <Index>2</Index>
              <Name>SAP仓库</Name>
              <Key>yd_warehouse</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>vACqHxOmhc</Id>
              <FieldId>vACqHxOmhc</FieldId>
              <Index>3</Index>
              <Name>E3入库仓库编码</Name>
              <Key>yd_e3warehouseno</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp action="edit" oid="AkId5S4yTs">
              <Index>4</Index>
            </FieldAp>
            <FieldAp action="edit" oid="jiRpZNc99A">
              <Index>5</Index>
            </FieldAp>
            <FieldAp action="edit" oid="ac5Y5Dax1q">
              <Index>6</Index>
            </FieldAp>
            <FieldAp action="edit" oid="O75mQrM58q">
              <Index>7</Index>
            </FieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>2218211534222423040</ModifierId>
      <EntityId>5/L/IY6NBQT=</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749882235562</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_purwarehousemapping</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>5/L/IY6NBQT=</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1749882236000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>5/L/IY6NBQT=</Id>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>采购仓库映射表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BaseEntity action="edit" oid="1942c188000066ac">
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_purwarehousemapping</TableName>
              <Id>5/L/IY6NBQT=</Id>
              <Name>采购仓库映射表</Name>
              <Template action="reset"/>
              <Key>yd_purwarehousemapping</Key>
              <NetworkControl>
                <NetCtrlOperation>
                  <OperationKey>submit</OperationKey>
                  <GroupId>default_netctrl</GroupId>
                  <Id>c91d5125000034ac</Id>
                  <Key>default_netctrl_submit</Key>
                </NetCtrlOperation>
              </NetworkControl>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
            </BaseEntity>
            <TextField action="edit" oid="h736cFwZ6e">
              <MustInput action="reset"/>
            </TextField>
            <MuliLangTextField action="edit" oid="8oq6R4m9CF">
              <MustInput action="reset"/>
              <GL>true</GL>
            </MuliLangTextField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>bukg7StpV3</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>SAP仓库</Name>
              <FieldName>fk_yd_warehouseid</FieldName>
              <Key>yd_warehouse</Key>
              <RefLayout/>
              <BaseEntityId>14G1K2YGTJGQ</BaseEntityId>
            </BasedataField>
            <TextField>
              <FieldName>fk_yd_e3warehouseno</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>vACqHxOmhc</Id>
              <Key>yd_e3warehouseno</Key>
              <Name>E3入库仓库编码</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749882235590</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_purwarehousemapping</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>5/L/IY6NBQT=</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1749882235562</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
