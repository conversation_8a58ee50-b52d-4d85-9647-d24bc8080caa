<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>2UR7CI04X=3O</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>2UR7CI04X=3O</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1667375759000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>2UR7CI04X=3O</Id>
          <Key>yd_resultreconciliate</Key>
          <EntityId>2UR7CI04X=3O</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>销售出库结果对账表</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>控制排序字段、搜索字段，超链接跳转</Description>
                  <ClassName>kd.bos.tcbj.im.report.form.ResultRecondRptFormPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>2UR7CI04X=3O</Id>
              <Name>销售出库结果对账表</Name>
              <Key>yd_resultreconciliate</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ReportPlugin>kd.bos.tcbj.im.report.data.resultRecondRptListDataPlugin</ReportPlugin>
            </ReportListAp>
            <FieldAp>
              <Id>SOH3p1r1Uu</Id>
              <FieldId>SOH3p1r1Uu</FieldId>
              <Name>开始日期</Name>
              <Key>yd_begindate</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>LdXqKR8p9d</Id>
              <FieldId>LdXqKR8p9d</FieldId>
              <Index>1</Index>
              <Name>结束日期</Name>
              <Key>yd_enddate</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>3FgVz2zVY6</Id>
              <FieldId>3FgVz2zVY6</FieldId>
              <Index>2</Index>
              <Name>客户</Name>
              <Key>yd_customer</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>vtW3J4wyg1</Id>
              <FieldId>vtW3J4wyg1</FieldId>
              <Index>3</Index>
              <Name>是否开门红</Name>
              <Key>yd_begin</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>6AufKor4xp</Id>
              <FieldId>6AufKor4xp</FieldId>
              <Index>4</Index>
              <Name>事务类型</Name>
              <Key>yd_type</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <Id>QrHTLX2GOo</Id>
              <FieldId>QrHTLX2GOo</FieldId>
              <Hyperlink>true</Hyperlink>
              <Name>PCP销售出库结果单号</Name>
              <Key>yd_resultno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_resultno</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>X38d47bN94</Id>
              <FieldId>X38d47bN94</FieldId>
              <Index>1</Index>
              <Name>客户编码</Name>
              <Key>yd_cusnumber</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_cusnumber</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>972Z1LK2xD</Id>
              <FieldId>972Z1LK2xD</FieldId>
              <Index>2</Index>
              <Name>客户名称</Name>
              <Key>yd_cusname</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_cusname</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>xEASsD0yTz</Id>
              <FieldId>xEASsD0yTz</FieldId>
              <Index>3</Index>
              <Name>是否开门红</Name>
              <Key>yd_isbegin</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_isbegin</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>lySzoDofvt</Id>
              <FieldId>lySzoDofvt</FieldId>
              <Index>4</Index>
              <Name>事务类型</Name>
              <Key>yd_billtype</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_billtype</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ciHdvkVu4a</Id>
              <FieldId>ciHdvkVu4a</FieldId>
              <Index>5</Index>
              <Name>物料总数</Name>
              <Key>yd_materialnum</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_materialnum</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>9osdAyIAO1</Id>
              <FieldId>9osdAyIAO1</FieldId>
              <Index>6</Index>
              <Name>单据金额</Name>
              <Key>yd_amount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_amount</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>n7PIWMJbhe</Id>
              <FieldId>n7PIWMJbhe</FieldId>
              <Hyperlink>true</Hyperlink>
              <Index>7</Index>
              <Name>中台销售出库中间表单号</Name>
              <Key>yd_billno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_billno</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>QMLCzKtWEZ</Id>
              <FieldId>QMLCzKtWEZ</FieldId>
              <Index>8</Index>
              <Name>客户编码</Name>
              <Key>yd_customerno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_customerno</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>jqOz05XTVz</Id>
              <FieldId>jqOz05XTVz</FieldId>
              <Index>9</Index>
              <Name>物料总数</Name>
              <Key>yd_totalmaterial</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_totalmaterial</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Tu5wbqk8ck</Id>
              <FieldId>Tu5wbqk8ck</FieldId>
              <Index>10</Index>
              <Name>是否数量异常</Name>
              <Key>yd_numerror</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_iserror</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>dv76s5KFxi</Id>
              <FieldId>dv76s5KFxi</FieldId>
              <Index>11</Index>
              <Name>总金额</Name>
              <Key>yd_totalamount</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_totalamount</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>oTQuNpbHfR</Id>
              <FieldId>oTQuNpbHfR</FieldId>
              <Index>12</Index>
              <Name>是否金额异常</Name>
              <Key>yd_amounterror</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_amounterror</DataSourceField>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>je5TLb7cw2</Id>
              <FieldId>je5TLb7cw2</FieldId>
              <Index>13</Index>
              <Name>下游销售出库单号</Name>
              <Key>yd_salebillnumber</Key>
              <ParentId>Tm10PEdaRq</ParentId>
              <DataSourceField>yd_salebillnumber</DataSourceField>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338616377744819200</ModifierId>
      <EntityId>2UR7CI04X=3O</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1667375759066</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_resultreconciliate</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>2UR7CI04X=3O</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1667375759000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>2UR7CI04X=3O</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>销售出库结果对账表</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>2UR7CI04X=3O</Id>
              <Key>yd_resultreconciliate</Key>
              <Name>销售出库结果对账表</Name>
            </ReportEntity>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QrHTLX2GOo</Id>
              <Key>yd_resultno</Key>
              <Name>PCP销售出库结果单号</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>X38d47bN94</Id>
              <Key>yd_cusnumber</Key>
              <Name>客户编码</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>972Z1LK2xD</Id>
              <Key>yd_cusname</Key>
              <Name>客户名称</Name>
            </TextField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xEASsD0yTz</Id>
              <Key>yd_isbegin</Key>
              <Name>是否开门红</Name>
            </CheckBoxField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>lySzoDofvt</Id>
              <Key>yd_billtype</Key>
              <Name>事务类型</Name>
            </TextField>
            <IntegerField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ciHdvkVu4a</Id>
              <Key>yd_materialnum</Key>
              <DefValue>0</DefValue>
              <Name>物料总数</Name>
            </IntegerField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>9osdAyIAO1</Id>
              <Key>yd_amount</Key>
              <DefValue>0</DefValue>
              <Name>单据金额</Name>
            </AmountField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>n7PIWMJbhe</Id>
              <Key>yd_billno</Key>
              <Name>中台销售出库中间表单号</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QMLCzKtWEZ</Id>
              <Key>yd_customerno</Key>
              <Name>客户编码</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>jqOz05XTVz</Id>
              <Key>yd_totalmaterial</Key>
              <Name>物料总数</Name>
            </TextField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Tu5wbqk8ck</Id>
              <Key>yd_numerror</Key>
              <Name>是否数量异常</Name>
            </CheckBoxField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>dv76s5KFxi</Id>
              <Key>yd_totalamount</Key>
              <DefValue>0</DefValue>
              <Name>总金额</Name>
            </AmountField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>oTQuNpbHfR</Id>
              <Key>yd_amounterror</Key>
              <Name>是否金额异常</Name>
            </CheckBoxField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>je5TLb7cw2</Id>
              <Key>yd_salebillnumber</Key>
              <Name>下游销售出库单号</Name>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>SOH3p1r1Uu</Id>
              <Key>yd_begindate</Key>
              <Name>开始日期</Name>
            </DateField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>LdXqKR8p9d</Id>
              <Key>yd_enddate</Key>
              <Name>结束日期</Name>
            </DateField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3FgVz2zVY6</Id>
              <Key>yd_customer</Key>
              <Name>客户</Name>
            </TextField>
            <CheckBoxField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>vtW3J4wyg1</Id>
              <Key>yd_begin</Key>
              <Name>是否开门红</Name>
            </CheckBoxField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>6AufKor4xp</Id>
              <Key>yd_type</Key>
              <Name>事务类型</Name>
              <Items>
                <ComboItem>
                  <Caption>销售</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>退货</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
              </Items>
            </ComboField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>kingdee</Isv>
      <Version>1667375759431</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_resultreconciliate</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>2UR7CI04X=3O</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1667375759066</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
