<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>1WC9TSAJ=5C+</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>1WC9TSAJ=5C+</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1635301999000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>1WC9TSAJ=5C+</Id>
          <Key>yd_rhzfh</Key>
          <EntityId>1WC9TSAJ=5C+</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2+3BZ1=UB+SZ</BizappId>
          <IsvSign/>
          <Name>日汇总发货</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>1WC9TSAJ=5C+</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>1WC9TSAJ=5C+</Id>
              <Name>日汇总发货</Name>
              <Key>yd_rhzfh</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>日汇总发货列表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Name>日汇总发货列表</Name>
                    </FormAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>1WC9TSAJ=5C+</EntityId>
                    </BillListAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1X+UTIP+N7JX</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_combofield_pt</ListFieldId>
                      <Name>平台</Name>
                    </ComboListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1X+UTPRK6W75</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_textfield_dpbh</ListFieldId>
                      <Name>店铺编号</Name>
                    </ListColumnAp>
                    <DecimalListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1X+UTU4+PXMX</Id>
                      <Key>yd_decimallistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_integerfield_zs</ListFieldId>
                      <Name>张数</Name>
                    </DecimalListColumnAp>
                    <CheckBoxListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1X+X3+4ZJT=V</Id>
                      <Key>yd_checkboxlistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_checkboxfield_th</ListFieldId>
                      <Name>是否退货</Name>
                    </CheckBoxListColumnAp>
                    <DecimalListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1X+UTYZMYC5O</Id>
                      <Key>yd_decimallistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_decimalfield_sl</ListFieldId>
                      <Name>数量</Name>
                    </DecimalListColumnAp>
                    <DecimalListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1X+UU1XGAG0/</Id>
                      <Key>yd_decimallistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>8</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_decimalfield_zje</ListFieldId>
                      <Name>总金额</Name>
                    </DecimalListColumnAp>
                    <DateListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1X+UU6P+4C4+</Id>
                      <Key>yd_datelistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>9</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_datefield_fhrq</ListFieldId>
                      <Name>发货日期</Name>
                    </DateListColumnAp>
                    <DateListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1X1H7YT7/KTW</Id>
                      <Key>yd_datelistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>10</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>createtime</ListFieldId>
                      <Name>创建时间</Name>
                    </DateListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <FieldAp>
              <Id>eFIS4NJehe</Id>
              <FieldId>eFIS4NJehe</FieldId>
              <Index>4</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>发货日期</Name>
              <Key>yd_datefield_fhrq</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>lhYAcl7EF7</Id>
              <FieldId>lhYAcl7EF7</FieldId>
              <Index>5</Index>
              <Name>平台</Name>
              <Key>yd_combofield_pt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>WX7KhmrXca</Id>
              <FieldId>WX7KhmrXca</FieldId>
              <Index>6</Index>
              <Name>是否退货</Name>
              <Key>yd_checkboxfield_th</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Xn8ANouCGB</Id>
              <FieldId>Xn8ANouCGB</FieldId>
              <Index>7</Index>
              <Name>店铺编号</Name>
              <Key>yd_textfield_dpbh</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>E3Xfr963mV</Id>
              <FieldId>E3Xfr963mV</FieldId>
              <Index>8</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>张数</Name>
              <Key>yd_integerfield_zs</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>MGe2nj14pE</Id>
              <FieldId>MGe2nj14pE</FieldId>
              <Index>9</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>数量</Name>
              <Key>yd_decimalfield_sl</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>cmQVpdiHXC</Id>
              <FieldId>cmQVpdiHXC</FieldId>
              <Index>10</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>总金额</Name>
              <Key>yd_decimalfield_zje</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>sbF6TahEcl</Id>
              <FieldId>sbF6TahEcl</FieldId>
              <Index>11</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>日期</Name>
              <Key>yd_datefield</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1269426207447845888</ModifierId>
      <EntityId>1WC9TSAJ=5C+</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1635301998730</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_rhzfh</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>1WC9TSAJ=5C+</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <DataXml>
        <EntityMetadata>
          <Id>1WC9TSAJ=5C+</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2+3BZ1=UB+SZ</BizappId>
          <IsvSign/>
          <Name>日汇总发货</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_rhzfh</TableName>
              <BusinessControl>
                <BusinessControl>
                  <CodeNumber>true</CodeNumber>
                </BusinessControl>
              </BusinessControl>
              <Id>1WC9TSAJ=5C+</Id>
              <Key>yd_rhzfh</Key>
              <Name>日汇总发货</Name>
            </BillEntity>
            <BillNoField action="edit" oid="bdJvQ9RWla">
              <MaxLength>80</MaxLength>
            </BillNoField>
            <ComboField>
              <FieldName>fk_yd_combofield_pt</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>lhYAcl7EF7</Id>
              <Key>yd_combofield_pt</Key>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <CheckBoxField>
              <FieldName>fk_yd_checkboxfield_th</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>WX7KhmrXca</Id>
              <Key>yd_checkboxfield_th</Key>
              <Name>是否退货</Name>
            </CheckBoxField>
            <TextField>
              <FieldName>fk_yd_textfield_dpbh</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Xn8ANouCGB</Id>
              <Key>yd_textfield_dpbh</Key>
              <Name>店铺编号</Name>
            </TextField>
            <IntegerField>
              <FieldName>fk_yd_integerfield_zs</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>E3Xfr963mV</Id>
              <Key>yd_integerfield_zs</Key>
              <DefValue>0</DefValue>
              <Name>张数</Name>
            </IntegerField>
            <DecimalField>
              <FieldName>fk_yd_decimalfield_sl</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>MGe2nj14pE</Id>
              <Key>yd_decimalfield_sl</Key>
              <DefValue>0</DefValue>
              <Name>数量</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_decimalfield_zje</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>cmQVpdiHXC</Id>
              <Key>yd_decimalfield_zje</Key>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>总金额</Name>
            </DecimalField>
            <DateField>
              <FieldName>fk_yd_datefield</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>sbF6TahEcl</Id>
              <Key>yd_datefield</Key>
              <Name>日期</Name>
              <DefValueDesign>
                <DefValueDesign>
                  <FuncParameter>#CurrentDate#</FuncParameter>
                  <FuncType>getToday</FuncType>
                </DefValueDesign>
              </DefValueDesign>
            </DateField>
            <DateField>
              <FieldName>fk_yd_datefield_fhrq</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>eFIS4NJehe</Id>
              <Key>yd_datefield_fhrq</Key>
              <Name>发货日期</Name>
            </DateField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv></Isv>
      <Version>1635301998758</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_rhzfh</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>1WC9TSAJ=5C+</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1635301998730</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
