<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>5+XF8I6SBTH/</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>5+XF8I6SBTH/</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1749092266000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>5+XF8I6SBTH/</Id>
          <Key>yd_rpa_alihealth_bill</Key>
          <EntityId>5+XF8I6SBTH/</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>阿里健康对账单来源表（rpa）</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>5+XF8I9NRNSN</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5+XF8I9NROFY</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>5+XF8I9NRNSN</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5+XF8I9NRNSO</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>5+XF8I9NRNSN</ParentId>
                      <ListFieldId>creator.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>创建人.姓名</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5+XF8I6SBTH/</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>5+XF8I9NRNSN</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Direction>column</Direction>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>5+XF8I9NROFX</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>5+XF8I6SBTH/</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>阿里健康对账单来源表（rpa）</Name>
              <Key>yd_rpa_alihealth_bill</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>5+XF8I9K499I</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>5+XF8I9K48L=</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp>
                      <Id>5+XF8I9K48LA</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>5+XF8I9K48L=</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>5+XF8I9K499K</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>5+XF8I9K48L=</ParentId>
                    </FieldAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>5+XF8I9K499I</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>5+XF8I9K499J</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>5+XF8I9K499I</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>5+XF8I9K48LB</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <AdvConSummaryPanelAp>
              <Id>5+XF8I9K499D</Id>
              <Name>高级面板摘要容器</Name>
              <Key>advconsummarypanelap</Key>
              <ParentId>5+XF8I9K48L4</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>5+XF8I9K48L5</Id>
              <Key>advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>5+XF8I9K48L4</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>5+XF8I9K499E</Id>
              <AlignItems>stretch</AlignItems>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>advconchildcanelap</Key>
              <Direction>column</Direction>
              <ParentId>5+XF8I9K48L4</ParentId>
              <Wrap>false</Wrap>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>5+XF8I9K48L6</Id>
              <Key>tb_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>5+XF8I9K48L5</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>5+XF8I9K499F</Id>
              <Key>tb_del</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>5+XF8I9K48L5</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryFieldAp>
              <Id>5+XF8I9K499G</Id>
              <FieldId>5+XF8I9K499G</FieldId>
              <Name>修改人</Name>
              <Hidden>true</Hidden>
              <Key>modifierfield</Key>
              <ParentId>5+XF8I9K48L7</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>5+XF8I9K48L8</Id>
              <FieldId>5+XF8I9K48L8</FieldId>
              <Index>1</Index>
              <Name>修改时间</Name>
              <Hidden>true</Hidden>
              <Key>modifydatefield</Key>
              <ParentId>5+XF8I9K48L7</ParentId>
            </EntryFieldAp>
            <EntryAp>
              <EntryId>5+XF8I9K48L7</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>5+XF8I9K48L7</Id>
              <Name>单据体</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>entryentity</Key>
              <ParentId>5+XF8I9K499E</ParentId>
            </EntryAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp>
              <Id>uD7HrsTXn5</Id>
              <FieldId>uD7HrsTXn5</FieldId>
              <Index>4</Index>
              <Name>行号</Name>
              <Key>yd_row_num</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Hq3VUOoUSX</Id>
              <FieldId>Hq3VUOoUSX</FieldId>
              <Index>5</Index>
              <Name>账单编号</Name>
              <Key>yd_bill_code</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>PI7K7YD5kJ</Id>
              <FieldId>PI7K7YD5kJ</FieldId>
              <Index>6</Index>
              <Name>商户编码</Name>
              <Key>yd_merchant_code</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>KG5gbH2O58</Id>
              <FieldId>KG5gbH2O58</FieldId>
              <Index>7</Index>
              <Name>账单周期</Name>
              <Key>yd_bill_cycle</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>ms3LwGGf5o</Id>
              <FieldId>ms3LwGGf5o</FieldId>
              <Index>8</Index>
              <Name>账单创建时间</Name>
              <Key>yd_bill_create_time</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>eCeoCkXzHo</Id>
              <FieldId>eCeoCkXzHo</FieldId>
              <Index>9</Index>
              <Name>账单生成时间</Name>
              <Key>yd_bill_create_day</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>JG0rb2i5lk</Id>
              <FieldId>JG0rb2i5lk</FieldId>
              <Index>10</Index>
              <Name>账单状态码</Name>
              <Key>yd_bill_status</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>QesLHd1Brg</Id>
              <FieldId>QesLHd1Brg</FieldId>
              <Index>11</Index>
              <Name>对账单状态</Name>
              <Key>yd_bill_status_desc</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>r6EGupmIdx</Id>
              <FieldId>r6EGupmIdx</FieldId>
              <Index>12</Index>
              <Name>废止阶段描述</Name>
              <Key>yd_abolish_phase_desc</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>OavT4VL5nS</Id>
              <FieldId>OavT4VL5nS</FieldId>
              <Index>13</Index>
              <Name>公司编码</Name>
              <Key>yd_company_code</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>5Jsr74Auwe</Id>
              <FieldId>5Jsr74Auwe</FieldId>
              <Index>14</Index>
              <Name>公司主体</Name>
              <Key>yd_company_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>bAu67Bo2Jc</Id>
              <FieldId>bAu67Bo2Jc</FieldId>
              <Index>15</Index>
              <Name>供应商编码</Name>
              <Key>yd_supplier_code</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Lvi21jqL9T</Id>
              <FieldId>Lvi21jqL9T</FieldId>
              <Index>16</Index>
              <Name>供应商名称</Name>
              <Key>yd_supplier_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>JizWMym54H</Id>
              <FieldId>JizWMym54H</FieldId>
              <Index>17</Index>
              <Name>开票含税总额</Name>
              <Key>yd_invoice_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>onFmrNmxoA</Id>
              <FieldId>onFmrNmxoA</FieldId>
              <Index>18</Index>
              <Name>发票未税总金额</Name>
              <Key>yd_invoice_untax_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>TI4Vn9gCDR</Id>
              <FieldId>TI4Vn9gCDR</FieldId>
              <Index>19</Index>
              <Name>发票税额</Name>
              <Key>yd_invoice_tax_amount</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>FWMDRW1euF</Id>
              <FieldId>FWMDRW1euF</FieldId>
              <Index>20</Index>
              <Name>结算含税总额</Name>
              <Key>yd_receive_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>26OvMqn55w</Id>
              <FieldId>26OvMqn55w</FieldId>
              <Index>21</Index>
              <Name>支付总金额</Name>
              <Key>yd_payment_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>ZKLDzExI8D</Id>
              <FieldId>ZKLDzExI8D</FieldId>
              <Index>22</Index>
              <Name>账扣含税总额</Name>
              <Key>yd_account_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>VOvlH8omTq</Id>
              <FieldId>VOvlH8omTq</FieldId>
              <Index>23</Index>
              <Name>票扣含税总额</Name>
              <Key>yd_receipt_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>9244gkUPIv</Id>
              <FieldId>9244gkUPIv</FieldId>
              <Index>24</Index>
              <Name>货款含税净额</Name>
              <Key>yd_goods_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>1Ch8YlS05D</Id>
              <FieldId>1Ch8YlS05D</FieldId>
              <Index>25</Index>
              <Name>操作类型枚举</Name>
              <Key>yd_operate_type_enum</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>RM6t0EQJBM</Id>
              <FieldId>RM6t0EQJBM</FieldId>
              <Index>26</Index>
              <Name>操作类型</Name>
              <Key>yd_operate_type</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>3ItzB2dkJ4</Id>
              <FieldId>3ItzB2dkJ4</FieldId>
              <Index>27</Index>
              <Name>对账单类型</Name>
              <Key>yd_statement_bill_type</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>ZLHcPQrqyA</Id>
              <FieldId>ZLHcPQrqyA</FieldId>
              <Index>28</Index>
              <Name>对账单类型描述</Name>
              <Key>yd_statement_bill_desc</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>h9xU4Pi1L4</Id>
              <FieldId>h9xU4Pi1L4</FieldId>
              <Index>29</Index>
              <Name>作业实例ID</Name>
              <Key>yd_job_instance</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>zI5wGRrV9H</Id>
              <FieldId>zI5wGRrV9H</FieldId>
              <Index>30</Index>
              <Name>票据编号</Name>
              <Key>yd_ticket_no</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>H3a14EVQH6</Id>
              <FieldId>H3a14EVQH6</FieldId>
              <Index>31</Index>
              <Name>套件ID</Name>
              <Key>yd_suite</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>uhAjbm21QE</Id>
              <FieldId>uhAjbm21QE</FieldId>
              <Index>32</Index>
              <Name>行业编码</Name>
              <Key>yd_industry_code</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>h0Hs2zmeDl</Id>
              <FieldId>h0Hs2zmeDl</FieldId>
              <Index>33</Index>
              <Name>行业名称</Name>
              <Key>yd_industry_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>rYj6PC0YJT</Id>
              <FieldId>rYj6PC0YJT</FieldId>
              <Index>34</Index>
              <Name>参考信息</Name>
              <Key>yd_reference</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>dO0oHNuIPo</Id>
              <FieldId>dO0oHNuIPo</FieldId>
              <Index>35</Index>
              <Name>货币</Name>
              <Key>yd_currency</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Oz6z1IGB4O</Id>
              <FieldId>Oz6z1IGB4O</FieldId>
              <Index>36</Index>
              <Name>结算模式</Name>
              <Key>yd_settlement_mode</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>ovflVR8aXP</Id>
              <FieldId>ovflVR8aXP</FieldId>
              <Index>37</Index>
              <Name>结算模式描述</Name>
              <Key>yd_settlement_mode_desc</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>WfgRKRLTEH</Id>
              <FieldId>WfgRKRLTEH</FieldId>
              <Index>38</Index>
              <Name>合同编码</Name>
              <Key>yd_contract_code</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>TdjpFnw2KN</Id>
              <FieldId>TdjpFnw2KN</FieldId>
              <Index>39</Index>
              <Name>附件OSS链接</Name>
              <Key>yd_attachment_oss_url</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>mnyjptm3LY</Id>
              <FieldId>mnyjptm3LY</FieldId>
              <Index>40</Index>
              <Name>附件文件名</Name>
              <Key>yd_attach_file_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>s1WTgEWiyL</Id>
              <FieldId>s1WTgEWiyL</FieldId>
              <Index>41</Index>
              <Name>批次号</Name>
              <Key>yd_batch_no</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>0PAPbFDkEG</Id>
              <FieldId>0PAPbFDkEG</FieldId>
              <Index>42</Index>
              <Name>按钮显示值对象</Name>
              <Key>yd_button_show_vo</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>39KFr3wIYW</Id>
              <FieldId>39KFr3wIYW</FieldId>
              <Index>43</Index>
              <Name>对账状态</Name>
              <Key>yd_recon_status</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>5YaRA5ckc4</Id>
              <FieldId>5YaRA5ckc4</FieldId>
              <Index>44</Index>
              <Name>发票状态</Name>
              <Key>yd_invoice_status</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>rk8kxXO2rw</Id>
              <FieldId>rk8kxXO2rw</FieldId>
              <Index>45</Index>
              <Name>支付状态</Name>
              <Key>yd_payment_status</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>4Hf259xzWI</Id>
              <FieldId>4Hf259xzWI</FieldId>
              <Index>46</Index>
              <Name>供应商公司编码</Name>
              <Key>yd_supplier_org_no</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>g4cPChfNxp</Id>
              <FieldId>g4cPChfNxp</FieldId>
              <Index>47</Index>
              <Name>供应商公司名称</Name>
              <Key>yd_supplier_org_name</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>5+XF8I9K48L4</Id>
              <Key>advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>分录</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Index>3</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1966784705198060544</ModifierId>
      <EntityId>5+XF8I6SBTH/</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749092265927</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_rpa_alihealth_bill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>5+XF8I6SBTH/</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1749092266000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>5+XF8I6SBTH/</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>阿里健康对账单来源表（rpa）</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_rpa_alihealth_table</TableName>
              <Operations>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>5+XF8I9K48L7</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>5+XF8I9K499H</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>5+XF8I9K48L7</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>5+XF8I9K48L9</Id>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>5+XF8I6SBTH/</Id>
              <Key>yd_rpa_alihealth_bill</Key>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
              <Name>阿里健康对账单来源表（rpa）</Name>
            </BillEntity>
            <EntryEntity>
              <Id>5+XF8I9K48L7</Id>
              <Key>entryentity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <ModifierField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5+XF8I9K499G</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>修改人</Name>
              <FieldName>fmodifierfield</FieldName>
              <Key>modifierfield</Key>
              <ParentId>5+XF8I9K48L7</ParentId>
              <BaseEntityId>68bde9ca00000eac</BaseEntityId>
            </ModifierField>
            <ModifyDateField>
              <FieldName>fmodifydatefield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5+XF8I9K48L8</Id>
              <Key>modifydatefield</Key>
              <ParentId>5+XF8I9K48L7</ParentId>
              <Name>修改日期</Name>
            </ModifyDateField>
            <IntegerField>
              <FieldName>fk_yd_row_num</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>uD7HrsTXn5</Id>
              <Key>yd_row_num</Key>
              <DefValue>0</DefValue>
              <Name>行号</Name>
            </IntegerField>
            <TextField>
              <FieldName>fk_yd_bill_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Hq3VUOoUSX</Id>
              <Key>yd_bill_code</Key>
              <Name>账单编号</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_merchant_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>PI7K7YD5kJ</Id>
              <Key>yd_merchant_code</Key>
              <Name>商户编码</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_bill_cycle</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>KG5gbH2O58</Id>
              <Key>yd_bill_cycle</Key>
              <Name>账单周期</Name>
            </TextField>
            <DateTimeField>
              <FieldName>fk_yd_bill_create_time</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ms3LwGGf5o</Id>
              <Key>yd_bill_create_time</Key>
              <Name>账单创建时间</Name>
            </DateTimeField>
            <DateField>
              <FieldName>fk_yd_bill_create_day</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>eCeoCkXzHo</Id>
              <Key>yd_bill_create_day</Key>
              <Name>账单生成时间</Name>
            </DateField>
            <IntegerField>
              <FieldName>fk_yd_bill_status</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>JG0rb2i5lk</Id>
              <Key>yd_bill_status</Key>
              <DefValue>0</DefValue>
              <Name>账单状态码</Name>
            </IntegerField>
            <TextField>
              <FieldName>fk_yd_bill_status_desc</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>QesLHd1Brg</Id>
              <Key>yd_bill_status_desc</Key>
              <Name>对账单状态</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_abolish_phase_desc</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>r6EGupmIdx</Id>
              <Key>yd_abolish_phase_desc</Key>
              <Name>废止阶段描述</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_company_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>OavT4VL5nS</Id>
              <Key>yd_company_code</Key>
              <Name>公司编码</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_company_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5Jsr74Auwe</Id>
              <Key>yd_company_name</Key>
              <Name>公司主体</Name>
              <MaxLength>128</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_supplier_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>bAu67Bo2Jc</Id>
              <Key>yd_supplier_code</Key>
              <Name>供应商编码</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_supplier_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Lvi21jqL9T</Id>
              <Key>yd_supplier_name</Key>
              <Name>供应商名称</Name>
              <MaxLength>128</MaxLength>
            </TextField>
            <DecimalField>
              <FieldName>fk_yd_invoice_total_amt</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>JizWMym54H</Id>
              <Key>yd_invoice_total_amt</Key>
              <DefValue>0</DefValue>
              <Name>开票含税总额</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_invoice_untax_amt</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>onFmrNmxoA</Id>
              <Key>yd_invoice_untax_amt</Key>
              <DefValue>0</DefValue>
              <Name>发票未税总金额</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_invoice_tax_amount</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>TI4Vn9gCDR</Id>
              <Key>yd_invoice_tax_amount</Key>
              <DefValue>0</DefValue>
              <Name>发票税额</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_receive_total_amt</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>FWMDRW1euF</Id>
              <Key>yd_receive_total_amt</Key>
              <DefValue>0</DefValue>
              <Name>结算含税总额</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_payment_total_amt</FieldName>
              <Scale>2</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>26OvMqn55w</Id>
              <Key>yd_payment_total_amt</Key>
              <DefValue>0</DefValue>
              <Name>支付总金额</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_account_total_amt</FieldName>
              <Scale>6</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ZKLDzExI8D</Id>
              <Key>yd_account_total_amt</Key>
              <DefValue>0</DefValue>
              <Name>账扣含税总额</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_receipt_total_amt</FieldName>
              <Scale>6</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>VOvlH8omTq</Id>
              <Key>yd_receipt_total_amt</Key>
              <DefValue>0</DefValue>
              <Name>票扣含税总额</Name>
            </DecimalField>
            <DecimalField>
              <FieldName>fk_yd_goods_total_amt</FieldName>
              <Scale>6</Scale>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>9244gkUPIv</Id>
              <Key>yd_goods_total_amt</Key>
              <DefValue>0</DefValue>
              <Name>货款含税净额</Name>
            </DecimalField>
            <IntegerField>
              <FieldName>fk_yd_operate_type_enum</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1Ch8YlS05D</Id>
              <Key>yd_operate_type_enum</Key>
              <DefValue>0</DefValue>
              <Name>操作类型枚举</Name>
            </IntegerField>
            <TextField>
              <FieldName>fk_yd_operate_type</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>RM6t0EQJBM</Id>
              <Key>yd_operate_type</Key>
              <Name>操作类型</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_statement_bill_type</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3ItzB2dkJ4</Id>
              <Key>yd_statement_bill_type</Key>
              <Name>对账单类型</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_statement_type_desc</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ZLHcPQrqyA</Id>
              <Key>yd_statement_bill_desc</Key>
              <Name>对账单类型描述</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_job_instance</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>h9xU4Pi1L4</Id>
              <Key>yd_job_instance</Key>
              <Name>作业实例ID</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_ticket_no</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>zI5wGRrV9H</Id>
              <Key>yd_ticket_no</Key>
              <Name>票据编号</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_suite</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>H3a14EVQH6</Id>
              <Key>yd_suite</Key>
              <Name>套件ID</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_industry_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>uhAjbm21QE</Id>
              <Key>yd_industry_code</Key>
              <Name>行业编码</Name>
              <MaxLength>64</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_industry_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>h0Hs2zmeDl</Id>
              <Key>yd_industry_name</Key>
              <Name>行业名称</Name>
              <MaxLength>128</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_reference</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>rYj6PC0YJT</Id>
              <Key>yd_reference</Key>
              <Name>参考信息</Name>
              <MaxLength>255</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_currency</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>dO0oHNuIPo</Id>
              <Key>yd_currency</Key>
              <Name>货币</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_settlement_mode</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Oz6z1IGB4O</Id>
              <Key>yd_settlement_mode</Key>
              <Name>结算模式</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_settlement_mode_des</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ovflVR8aXP</Id>
              <Key>yd_settlement_mode_desc</Key>
              <Name>结算模式描述</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_contract_code</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>WfgRKRLTEH</Id>
              <Key>yd_contract_code</Key>
              <Name>合同编码</Name>
              <MaxLength>255</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_attachment_oss_url</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>TdjpFnw2KN</Id>
              <Key>yd_attachment_oss_url</Key>
              <Name>附件OSS链接</Name>
              <MaxLength>255</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_attach_file_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>mnyjptm3LY</Id>
              <Key>yd_attach_file_name</Key>
              <Name>附件文件名</Name>
              <MaxLength>128</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_batch_no</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>s1WTgEWiyL</Id>
              <Key>yd_batch_no</Key>
              <Name>批次号</Name>
              <MaxLength>128</MaxLength>
            </TextField>
            <LargeTextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>0PAPbFDkEG</Id>
              <Name>按钮显示值对象</Name>
              <FieldName>fk_yd_button_show_vo</FieldName>
              <Key>yd_button_show_vo</Key>
            </LargeTextField>
            <IntegerField>
              <FieldName>fk_yd_recon_status</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>39KFr3wIYW</Id>
              <Key>yd_recon_status</Key>
              <DefValue>0</DefValue>
              <Name>对账状态</Name>
            </IntegerField>
            <IntegerField>
              <FieldName>fk_yd_invoice_status</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5YaRA5ckc4</Id>
              <Key>yd_invoice_status</Key>
              <DefValue>0</DefValue>
              <Name>发票状态</Name>
            </IntegerField>
            <IntegerField>
              <FieldName>fk_yd_payment_status</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>rk8kxXO2rw</Id>
              <Key>yd_payment_status</Key>
              <DefValue>0</DefValue>
              <Name>支付状态</Name>
            </IntegerField>
            <TextField>
              <FieldName>fk_yd_supplier_org_no</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4Hf259xzWI</Id>
              <Key>yd_supplier_org_no</Key>
              <Name>供应商公司编码</Name>
              <MaxLength>128</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_supplier_org_name</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>g4cPChfNxp</Id>
              <Key>yd_supplier_org_name</Key>
              <Name>供应商公司名称</Name>
              <MaxLength>128</MaxLength>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749092265958</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_rpa_alihealth_bill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>5+XF8I6SBTH/</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1749092265927</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
