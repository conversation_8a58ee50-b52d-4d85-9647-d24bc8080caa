<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>3=/KIW505//U</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>3=/KIW505//U</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1719831228000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>3=/KIW505//U</Id>
          <Key>yd_settleconfig</Key>
          <EntityId>3=/KIW505//U</EntityId>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>结算配置表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BasedataFormAp action="edit" oid="1942c188000064ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>3=/KIW505//U</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>3=/KIW505//U</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>结算配置表</Name>
              <Key>yd_settleconfig</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>结算配置表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000024ac">
                      <Name>结算配置表</Name>
                    </FormAp>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>3=/KIW505//U</EntityId>
                    </BillListAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3JLZDR8T9U3X</Id>
                      <Key>yd_listcolumnap3</Key>
                      <Order>NotOrder</Order>
                      <Index>2</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>name</ListFieldId>
                      <Name>名称</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3=/SUHU0C1KN</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_type</ListFieldId>
                      <Name>类型</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3=/T6Y6HH21P</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_filter</ListFieldId>
                      <Name>过滤条件</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3=/SUKXPZ+M=</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_prompt</ListFieldId>
                      <Name>提示</Name>
                    </ListColumnAp>
                    <ComboListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>3=/U8XFPPU+T</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>status</ListFieldId>
                      <Name>数据状态</Name>
                    </ComboListColumnAp>
                    <ListColumnAp action="remove" oid="b5994054000029ac"/>
                    <ComboListColumnAp action="remove" oid="UGA0V8HS=6S"/>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <FlexPanelAp action="edit" oid="LL4BVAnADa">
              <Index action="reset"/>
            </FlexPanelAp>
            <FlexPanelAp action="edit" oid="PxNfkkak4s">
              <Grow>0</Grow>
              <Index>1</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                  <Padding>
                    <Padding>
                      <Left>2px</Left>
                      <Right action="reset"/>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp>
              <Id>5toxZyDYxP</Id>
              <FieldId>5toxZyDYxP</FieldId>
              <Index>2</Index>
              <Name>类型</Name>
              <Key>yd_type</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>pnQ0oMf3Xd</Id>
              <FieldId>pnQ0oMf3Xd</FieldId>
              <Index>3</Index>
              <Name>结算单据过滤条件</Name>
              <Key>yd_filter</Key>
              <ParentId>I8M9X0FOPr</ParentId>
              <FullLine>true</FullLine>
            </FieldAp>
            <FieldAp action="edit" oid="AkId5S4yTs">
              <Index>4</Index>
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="jiRpZNc99A">
              <Index>5</Index>
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="ac5Y5Dax1q">
              <Index>6</Index>
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp action="edit" oid="O75mQrM58q">
              <Index>7</Index>
              <Hidden>true</Hidden>
            </FieldAp>
            <FieldAp>
              <Id>1SRy8D0DQ6</Id>
              <FieldId>1SRy8D0DQ6</FieldId>
              <Index>8</Index>
              <Name>无满足单据提示</Name>
              <Key>yd_prompt</Key>
              <ParentId>I8M9X0FOPr</ParentId>
              <FullLine>true</FullLine>
            </FieldAp>
            <FieldAp>
              <Id>cYMmqBjfUF</Id>
              <FieldId>cYMmqBjfUF</FieldId>
              <Index>9</Index>
              <Name>源单校验类名</Name>
              <Key>yd_vailclass</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>41pOvhSqDx</Id>
              <FieldId>41pOvhSqDx</FieldId>
              <Index>10</Index>
              <Name>源单校验方法名</Name>
              <Key>yd_vailmethod</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>kY3vPERYk3</Id>
              <FieldId>kY3vPERYk3</FieldId>
              <Index>11</Index>
              <Name>结算路径获取类名</Name>
              <Key>yd_classname</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>e57igwSA3O</Id>
              <FieldId>e57igwSA3O</FieldId>
              <Index>12</Index>
              <Name>结算路径获取方法名</Name>
              <Key>yd_method</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>F3kKEMvJaE</Id>
              <FieldId>F3kKEMvJaE</FieldId>
              <Index>13</Index>
              <Name>退货路径获取方法名</Name>
              <Key>yd_return_method</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>0qvF6Fi2O7</Id>
              <FieldId>0qvF6Fi2O7</FieldId>
              <Index>14</Index>
              <Name>生成结算单据类名</Name>
              <Key>yd_settleclass</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>3BNxUQ7kr7</Id>
              <FieldId>3BNxUQ7kr7</FieldId>
              <Index>15</Index>
              <Name>生成结算单据方法名</Name>
              <Key>yd_settlemethod</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Gm5Im2N25o</Id>
              <FieldId>Gm5Im2N25o</FieldId>
              <Index>16</Index>
              <Name>生成直接调拨类名</Name>
              <Key>yd_transclass</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Llo6NYaKwO</Id>
              <FieldId>Llo6NYaKwO</FieldId>
              <Index>17</Index>
              <Name>生成直接调拨方法名</Name>
              <Key>yd_transmethod</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>M1drX4TjZX</Id>
              <FieldId>M1drX4TjZX</FieldId>
              <Index>18</Index>
              <Name>源单是否需要重置组织</Name>
              <Key>yd_resetorg</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>KQz3eXxl4g</Id>
              <FieldId>KQz3eXxl4g</FieldId>
              <Index>19</Index>
              <Name>内部交易关系自定义校验类名</Name>
              <Key>yd_cusclass</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>cwN0qDARok</Id>
              <FieldId>cwN0qDARok</FieldId>
              <Index>20</Index>
              <Name>内部交易关系自定义校验方法名</Name>
              <Key>yd_cusmethod</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>I0XTfZxiyW</Id>
              <FieldId>I0XTfZxiyW</FieldId>
              <Index>21</Index>
              <Name>正向出库单to正向出库单转换规则</Name>
              <Key>yd_oriouttoout</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>VyfKXorEk8</Id>
              <FieldId>VyfKXorEk8</FieldId>
              <Index>22</Index>
              <Name>正向出库单to正向入库单转换规则</Name>
              <Key>yd_outtoin</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>U08BH7xQ4V</Id>
              <FieldId>U08BH7xQ4V</FieldId>
              <Index>23</Index>
              <Name>正向入库单to正向出库单转换规则</Name>
              <Key>yd_intoout</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>8h2NbwrXD1</Id>
              <FieldId>8h2NbwrXD1</FieldId>
              <Index>24</Index>
              <Name>退货出库单to正向出库单转换规则</Name>
              <Key>yd_reouttoout</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>2GYDoMRjJu</Id>
              <FieldId>2GYDoMRjJu</FieldId>
              <Index>25</Index>
              <Name>退货出库单to退货入库单转换规则</Name>
              <Key>yd_oriouttoin_re</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>uREDd3461L</Id>
              <FieldId>uREDd3461L</FieldId>
              <Index>26</Index>
              <Name>退货入库单to退货出库单转换规则</Name>
              <Key>yd_intoout_re</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>WUqO6TEoUJ</Id>
              <FieldId>WUqO6TEoUJ</FieldId>
              <Index>27</Index>
              <Name>库存中间表生成调拨单转换规则标识</Name>
              <Key>yd_todirbill</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>v2iJtsActa</Id>
              <FieldId>v2iJtsActa</FieldId>
              <Index>28</Index>
              <Name>接口结算调度执行用户ID</Name>
              <Key>yd_userid</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Nc3w23Bkaq</Id>
              <FieldId>Nc3w23Bkaq</FieldId>
              <Index>29</Index>
              <Name>集成EAS服务流程</Name>
              <Key>yd_flow</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>xRLO9AFI2T</Id>
              <FieldId>xRLO9AFI2T</FieldId>
              <Index>30</Index>
              <Name>是否同步执行</Name>
              <Key>yd_flowtype</Key>
              <ParentId>I8M9X0FOPr</ParentId>
            </FieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1308009068819907584</ModifierId>
      <EntityId>3=/KIW505//U</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1719831227906</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_settleconfig</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>3=/KIW505//U</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1719831228000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>3=/KIW505//U</Id>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>结算配置表</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BaseEntity action="edit" oid="1942c188000066ac">
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_settleconfig</TableName>
              <Id>3=/KIW505//U</Id>
              <Name>结算配置表</Name>
              <Template action="reset"/>
              <Key>yd_settleconfig</Key>
              <NetworkControl>
                <NetCtrlOperation>
                  <OperationKey>submit</OperationKey>
                  <GroupId>default_netctrl</GroupId>
                  <Id>c91d5125000034ac</Id>
                  <Key>default_netctrl_submit</Key>
                </NetCtrlOperation>
              </NetworkControl>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
            </BaseEntity>
            <MuliLangTextField action="edit" oid="8oq6R4m9CF">
              <GL>true</GL>
            </MuliLangTextField>
            <TextField>
              <FieldName>fk_yd_yd_type</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5toxZyDYxP</Id>
              <Key>yd_type</Key>
              <MustInput>true</MustInput>
              <Name>类型</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_prompt</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1SRy8D0DQ6</Id>
              <Key>yd_prompt</Key>
              <Name>无满足单据提示</Name>
              <MaxLength>255</MaxLength>
            </TextField>
            <TextAreaField>
              <FieldName>fk_yd_filter</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pnQ0oMf3Xd</Id>
              <Key>yd_filter</Key>
              <MustInput>true</MustInput>
              <Name>结算单据过滤条件</Name>
              <MaxLength>500</MaxLength>
            </TextAreaField>
            <TextField>
              <FieldName>fk_yd_classname</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kY3vPERYk3</Id>
              <Key>yd_classname</Key>
              <MustInput>true</MustInput>
              <Name>结算路径获取类名</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_method</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>e57igwSA3O</Id>
              <Key>yd_method</Key>
              <MustInput>true</MustInput>
              <Name>结算路径获取方法名</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_cusclass</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>KQz3eXxl4g</Id>
              <Key>yd_cusclass</Key>
              <Name>内部交易关系自定义校验类名</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_cusmethod</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>cwN0qDARok</Id>
              <Key>yd_cusmethod</Key>
              <Name>内部交易关系自定义校验方法名</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_oriouttoout</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>I0XTfZxiyW</Id>
              <Key>yd_oriouttoout</Key>
              <Name>正向出库单to正向出库单转换规则</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_outtoin</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>VyfKXorEk8</Id>
              <Key>yd_outtoin</Key>
              <Name>正向出库单to正向入库单转换规则</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_intoout</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>U08BH7xQ4V</Id>
              <Key>yd_intoout</Key>
              <Name>正向入库单to正向出库单转换规则</Name>
            </TextField>
            <CheckBoxField>
              <FieldName>fk_yd_resetorg</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>M1drX4TjZX</Id>
              <Key>yd_resetorg</Key>
              <Name>源单是否需要重置组织</Name>
            </CheckBoxField>
            <TextField>
              <FieldName>fk_yd_return_method</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>F3kKEMvJaE</Id>
              <Key>yd_return_method</Key>
              <Name>退货路径获取方法名</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_oriouttoin</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2GYDoMRjJu</Id>
              <Key>yd_oriouttoin_re</Key>
              <Name>退货出库单to退货入库单转换规则</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_intoout_re</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>uREDd3461L</Id>
              <Key>yd_intoout_re</Key>
              <Name>退货入库单to退货出库单转换规则</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_reouttoout</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>8h2NbwrXD1</Id>
              <Key>yd_reouttoout</Key>
              <Name>退货出库单to正向出库单转换规则</Name>
              <SupportQingAnalysis>false</SupportQingAnalysis>
            </TextField>
            <TextField>
              <FieldName>fk_yd_vailclass</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>cYMmqBjfUF</Id>
              <Key>yd_vailclass</Key>
              <Name>源单校验类名</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_vailmethod</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>41pOvhSqDx</Id>
              <Key>yd_vailmethod</Key>
              <Name>源单校验方法名</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_settleclass</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>0qvF6Fi2O7</Id>
              <Key>yd_settleclass</Key>
              <Name>生成结算单据类名</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_settlemethod</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3BNxUQ7kr7</Id>
              <Key>yd_settlemethod</Key>
              <Name>生成结算单据方法名</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_transclass</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Gm5Im2N25o</Id>
              <Key>yd_transclass</Key>
              <Name>生成直接调拨类名</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_transmethod</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Llo6NYaKwO</Id>
              <Key>yd_transmethod</Key>
              <Name>生成直接调拨方法名</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_todirbill</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>WUqO6TEoUJ</Id>
              <Key>yd_todirbill</Key>
              <Name>库存中间表生成调拨单转换规则标识</Name>
            </TextField>
            <TextField>
              <FieldName>fk_yd_flow</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Nc3w23Bkaq</Id>
              <Key>yd_flow</Key>
              <Name>集成EAS服务流程</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <CheckBoxField>
              <FieldName>fk_yd_checkboxfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xRLO9AFI2T</Id>
              <Key>yd_flowtype</Key>
              <Name>是否同步执行</Name>
            </CheckBoxField>
            <TextField>
              <FieldName>fk_yd_userid</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>v2iJtsActa</Id>
              <Key>yd_userid</Key>
              <Name>接口结算调度执行用户ID</Name>
              <MaxLength>100</MaxLength>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1719831228017</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_settleconfig</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>3=/KIW505//U</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1719831227906</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
