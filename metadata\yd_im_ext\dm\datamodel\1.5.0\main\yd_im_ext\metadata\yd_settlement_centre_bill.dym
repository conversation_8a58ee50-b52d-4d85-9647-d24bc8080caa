<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>4YKZNZ6V2JHZ</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4YKZNZ6V2JHZ</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1750320683000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>4YKZNZ6V2JHZ</Id>
          <Key>yd_settlement_centre_bill</Key>
          <EntityId>4YKZNZ6V2JHZ</EntityId>
          <ParentId>ab7efc31000010ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>账单结算中间表</Name>
          <InheritPath>00305e8b000006ac,ab7efc31000010ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>4YKZNZ9C+SD=</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YKZNZ9C+RQ1</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>4YKZNZ9C+SD=</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YKZNZ9C+SDA</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>4YKZNZ9C+SD=</ParentId>
                      <ListFieldId>org.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>组织.名称</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4YKZNZ6V2JHZ</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>4YKZNZ9C+SD=</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Direction>column</Direction>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>4YKZNZ9C+RQ0</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>4YKZNZ6V2JHZ</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>账单结算中间表</Name>
              <Key>yd_settlement_centre_bill</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>4YKZNZ9C+RPY</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>4YKZNZ9C+RQ/</Id>
                      <Key>layoutflexap2</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <LayoutFlexAp>
                      <Id>4YKZNZ9C+SD6</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>4YKZNZ9C+RPY</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4YKZNZ9C+RPZ</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>4YKZNZ9C+RPY</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4YKZNZ9C+SD9</Id>
                      <FieldId>MwLyMGIJQa</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>组织</Name>
                      <Key>org</Key>
                      <ParentId>4YKZNZ9C+RQ/</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4YKZNZ9C+SD7</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>4YKZNZ9C+SD6</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>4YKZNZ9C+RQ+</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>4YKZNZ9C+SD6</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>4YKZNZ9C+SD8</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Name>账单结算中间表列表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Plugins>
                        <Plugin>
                          <FPK/>
                          <Description>列表插件</Description>
                          <ClassName>kd.bos.tcbj.ec.formplugin.SettlementCentreListPlugin</ClassName>
                          <Enabled>true</Enabled>
                          <BizAppId/>
                        </Plugin>
                      </Plugins>
                      <Name>账单结算中间表列表</Name>
                    </FormAp>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <SchemeFilterViewAp action="edit" oid="schemefilterview">
                      <BatchSetFilter>[{"Value":"seniorfilter","TextField":"billno","Id":"billno","Name":"单据编号"},{"Value":"seniorfilter","TextField":"billstatus","Id":"billstatus","Name":"单据状态"},{"Value":"seniorfilter","TextField":"creator.number","Id":"creator.number","Name":"创建人.工号"},{"Value":"seniorfilter","TextField":"creator.name","Id":"creator.name","Name":"创建人.姓名"},{"Value":"seniorfilter","TextField":"createtime","Id":"createtime","Name":"创建时间"},{"Value":"seniorfilter","TextField":"modifier.number","Id":"modifier.number","Name":"修改人.工号"},{"Value":"seniorfilter","TextField":"modifier.name","Id":"modifier.name","Name":"修改人.姓名"},{"Value":"seniorfilter","TextField":"modifytime","Id":"modifytime","Name":"修改时间"},{"Value":"seniorfilter","TextField":"auditor.number","Id":"auditor.number","Name":"审核人.工号"},{"Value":"seniorfilter","TextField":"auditor.name","Id":"auditor.name","Name":"审核人.姓名"},{"Value":"seniorfilter","TextField":"auditdate","Id":"auditdate","Name":"审核日期"},{"Value":"seniorfilter","TextField":"org.number","Id":"org.number","Name":"组织.编码"}]</BatchSetFilter>
                    </SchemeFilterViewAp>
                    <BarItemAp>
                      <Id>Fj2A3LJg4X</Id>
                      <Key>yd_push_saleout</Key>
                      <OperationStyle>0</OperationStyle>
                      <Index>8</Index>
                      <ParentId>_toolbar_</ParentId>
                      <Name>下推销售出库单</Name>
                    </BarItemAp>
                    <BarItemAp>
                      <OperationKey>trackdown</OperationKey>
                      <Id>3Uys0OhHVM</Id>
                      <Key>yd_baritemap</Key>
                      <OperationStyle>0</OperationStyle>
                      <Index>9</Index>
                      <ParentId>_toolbar_</ParentId>
                      <Name>下查</Name>
                    </BarItemAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>4YKZNZ6V2JHZ</EntityId>
                    </BillListAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YL7/B5GN7/E</Id>
                      <Key>yd_combolistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_platform</ListFieldId>
                      <Name>平台</Name>
                    </ComboListColumnAp>
                    <ComboListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YL7/H/9EFN2</Id>
                      <Key>yd_combolistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_shop</ListFieldId>
                      <Name>店铺</Name>
                    </ComboListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4YL7/KPCS/7Z</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_source_bill_no</ListFieldId>
                      <Name>关联账单编码</Name>
                    </ListColumnAp>
                    <CheckBoxListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4ZTWN9EF6JZO</Id>
                      <Key>yd_checkboxlistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_issalesreturn</ListFieldId>
                      <Name>是否退货</Name>
                    </CheckBoxListColumnAp>
                    <CheckBoxListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>4ZTWNXXMB8U9</Id>
                      <Key>yd_checkboxlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>7</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_ispush</ListFieldId>
                      <Name>是否下推成功</Name>
                    </CheckBoxListColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>billno</FieldName>
                      <Id>5/ECG=WK1DDJ</Id>
                      <Key>yd_schemefiltercolumnap</Key>
                      <ParentId>schemefilterview</ParentId>
                      <Name>单据编号</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>billstatus</FieldName>
                      <Id>5/ECG=WK1E1T</Id>
                      <Key>yd_schemefiltercolumnap1</Key>
                      <Index>1</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>单据状态</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>creator.number</FieldName>
                      <Id>5/ECG=WK1DDK</Id>
                      <Key>yd_schemefiltercolumnap2</Key>
                      <Index>2</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>创建人.工号</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>creator.name</FieldName>
                      <Id>5/ECG=WK1E1U</Id>
                      <Key>yd_schemefiltercolumnap3</Key>
                      <Index>3</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>创建人.姓名</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>createtime</FieldName>
                      <Id>5/ECG=WK1DDL</Id>
                      <Key>yd_schemefiltercolumnap4</Key>
                      <Index>4</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>创建时间</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>modifier.number</FieldName>
                      <Id>5/ECG=WK1E1V</Id>
                      <Key>yd_schemefiltercolumnap5</Key>
                      <Index>5</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>修改人.工号</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>modifier.name</FieldName>
                      <Id>5/ECG=WK1DDM</Id>
                      <Key>yd_schemefiltercolumnap6</Key>
                      <Index>6</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>修改人.姓名</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>modifytime</FieldName>
                      <Id>5/ECG=WK1E1W</Id>
                      <Key>yd_schemefiltercolumnap7</Key>
                      <Index>7</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>修改时间</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>auditor.number</FieldName>
                      <Id>5/ECG=WK1DDN</Id>
                      <Key>yd_schemefiltercolumnap8</Key>
                      <Index>8</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>审核人.工号</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>auditor.name</FieldName>
                      <Id>5/ECG=WK1E1X</Id>
                      <Key>yd_schemefiltercolumnap9</Key>
                      <Index>9</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>审核人.姓名</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>auditdate</FieldName>
                      <Id>5/ECG=WK1DDO</Id>
                      <Key>yd_schemefiltercolumnap10</Key>
                      <Index>10</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>审核日期</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp>
                      <FieldName>org.number</FieldName>
                      <Id>5/ECG=WK1E1Y</Id>
                      <Key>yd_schemefiltercolumnap11</Key>
                      <Index>11</Index>
                      <ParentId>schemefilterview</ParentId>
                      <Name>组织编码</Name>
                    </SchemeFilterColumnAp>
                    <SchemeFilterColumnAp action="remove" oid="3c48f498000001ac"/>
                    <SchemeFilterColumnAp action="remove" oid="3c48f498000002ac"/>
                    <SchemeFilterColumnAp action="remove" oid="0c354733000029ac"/>
                    <SchemeFilterColumnAp action="remove" oid="3c48f498000004ac"/>
                    <SchemeFilterColumnAp action="remove" oid="3c48f498000005ac"/>
                    <SchemeFilterColumnAp action="remove" oid="0c35473300002aac"/>
                    <SchemeFilterColumnAp action="remove" oid="3c48f498000007ac"/>
                    <SchemeFilterColumnAp action="remove" oid="3c48f498000008ac"/>
                    <SchemeFilterColumnAp action="remove" oid="0c354733000089ac"/>
                    <SchemeFilterColumnAp action="remove" oid="0c35473300008aac"/>
                    <SchemeFilterColumnAp action="remove" oid="0c35473300008bac"/>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <AdvConSummaryPanelAp>
              <Id>4YKZNZ9C+RPT</Id>
              <Name>高级面板摘要容器</Name>
              <Key>advconsummarypanelap</Key>
              <ParentId>4YKZNZ99CC6J</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>4YKZNZ9C+SD1</Id>
              <Key>advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>4YKZNZ99CC6J</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>4YKZNZ9C+RPU</Id>
              <AlignItems>stretch</AlignItems>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>advconchildcanelap</Key>
              <Direction>column</Direction>
              <ParentId>4YKZNZ99CC6J</ParentId>
              <Wrap>false</Wrap>
            </AdvConChildPanelAp>
            <EntryAp>
              <EntryId>4YKZNZ9C+SD3</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>4YKZNZ9C+SD3</Id>
              <Name>单据体</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageRow>20</PageRow>
              <PageType/>
              <Key>entryentity</Key>
              <SplitPage>true</SplitPage>
              <ParentId>4YKZNZ9C+RPU</ParentId>
            </EntryAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>4YKZNZ9C+SD2</Id>
              <Key>tb_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>4YKZNZ9C+SD1</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>4YKZNZ9C+RPV</Id>
              <Key>tb_del</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>4YKZNZ9C+SD1</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryFieldAp>
              <Id>4YKZNZ9C+RPW</Id>
              <FieldId>4YKZNZ9C+RPW</FieldId>
              <Name>修改人</Name>
              <Hidden>true</Hidden>
              <Key>modifierfield</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>4YKZNZ9C+SD4</Id>
              <FieldId>4YKZNZ9C+SD4</FieldId>
              <Index>1</Index>
              <Name>修改时间</Name>
              <Hidden>true</Hidden>
              <Key>modifydatefield</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>NvIT4EX1DY</Id>
              <FieldId>NvIT4EX1DY</FieldId>
              <Index>2</Index>
              <Name>业务平台对应货品编号</Name>
              <Key>yd_productcode</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>1unwV3YByZ</Id>
              <FieldId>1unwV3YByZ</FieldId>
              <Index>3</Index>
              <Name>业务平台对应商品编号</Name>
              <Key>yd_goodscode</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>8vMFcz0dMO</Id>
              <FieldId>8vMFcz0dMO</FieldId>
              <Index>4</Index>
              <Name>物料编码</Name>
              <Key>yd_material</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>uuruvLP4xq</Id>
              <FieldId>uuruvLP4xq</FieldId>
              <Index>5</Index>
              <Name>物料名称</Name>
              <Key>yd_materialname</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>gYZqOszqxE</Id>
              <FieldId>gYZqOszqxE</FieldId>
              <Index>6</Index>
              <Name>含税单价</Name>
              <Key>yd_priceandtax</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <NoDisplayScaleZero>true</NoDisplayScaleZero>
              <Id>DV1CmSzMTJ</Id>
              <FieldId>DV1CmSzMTJ</FieldId>
              <Index>7</Index>
              <Name>数量</Name>
              <Key>yd_qty</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>9g4LYWE8Hk</Id>
              <FieldId>9g4LYWE8Hk</FieldId>
              <Index>8</Index>
              <Name>是否赠品</Name>
              <Key>yd_ispresent</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>TXAcrEV5W6</Id>
              <FieldId>TXAcrEV5W6</FieldId>
              <Index>9</Index>
              <Name>仓库</Name>
              <Key>yd_warehouse</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>3QoiubK6OI</Id>
              <FieldId>3QoiubK6OI</FieldId>
              <Index>10</Index>
              <Name>折前价税合计</Name>
              <Key>yd_totaleasamount</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>DyrmtzZK7G</Id>
              <FieldId>DyrmtzZK7G</FieldId>
              <Index>11</Index>
              <Name>折扣额</Name>
              <Key>yd_discountamount</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>w8eXMaIEBC</Id>
              <FieldId>w8eXMaIEBC</FieldId>
              <Index>12</Index>
              <Name>价税合计</Name>
              <Key>yd_amountandtax</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </EntryFieldAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldAp>
              <Id>9EwoCfQ2ny</Id>
              <FieldId>9EwoCfQ2ny</FieldId>
              <Index>5</Index>
              <Name>平台</Name>
              <Key>yd_platform</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>kbShfLij3Z</Id>
              <FieldId>kbShfLij3Z</FieldId>
              <Index>6</Index>
              <Name>店铺</Name>
              <Key>yd_shop</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>PQ0Pb6j5m3</Id>
              <FieldId>PQ0Pb6j5m3</FieldId>
              <Index>7</Index>
              <Name>关联账单编码</Name>
              <Key>yd_source_bill_no</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>u7FPyifA6j</Id>
              <FieldId>u7FPyifA6j</FieldId>
              <Index>8</Index>
              <Name>是否退货</Name>
              <Key>yd_issalesreturn</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>PBQqKINUIA</Id>
              <FieldId>PBQqKINUIA</FieldId>
              <Index>9</Index>
              <Name>收货客户</Name>
              <Key>yd_customer</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>GxrZ8lMxvp</Id>
              <FieldId>GxrZ8lMxvp</FieldId>
              <Index>10</Index>
              <Name>业务日期</Name>
              <Key>yd_bizdate</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>0cLUl5uOiQ</Id>
              <FieldId>0cLUl5uOiQ</FieldId>
              <Index>11</Index>
              <Name>下游单号</Name>
              <Key>yd_targetbillno</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>nkhdYsZFTb</Id>
              <FieldId>nkhdYsZFTb</FieldId>
              <Index>12</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>是否下推成功</Name>
              <Key>yd_ispush</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>nObh9pnaLg</Id>
              <FieldId>nObh9pnaLg</FieldId>
              <Index>13</Index>
              <MobFieldPattern>1</MobFieldPattern>
              <Name>下推销售出库单失败原因</Name>
              <Key>yd_push_error_msg</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>4YKZNZ99CC6J</Id>
              <Key>advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>分录</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Index>3</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border action="setnull"/>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1966784705198060544</ModifierId>
      <EntityId>4YKZNZ6V2JHZ</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750320682565</Version>
      <ParentId>ab7efc31000010ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_settlement_centre_bill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4YKZNZ6V2JHZ</Id>
      <InheritPath>00305e8b000006ac,ab7efc31000010ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1750320683000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>4YKZNZ6V2JHZ</Id>
          <ParentId>ab7efc31000010ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>账单结算中间表</Name>
          <InheritPath>00305e8b000006ac,ab7efc31000010ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <PermissionDimension>
                <PermissionDimension>
                  <DataDimensionField>org</DataDimensionField>
                  <DataDimension>MwLyMGIJQa</DataDimension>
                </PermissionDimension>
              </PermissionDimension>
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_settlement_centre</TableName>
              <Operations>
                <Operation action="edit" oid="b599405400000eac">
                  <Plugins>
                    <Plugin action="edit" oid="kd.bos.coderule.CodeRuleDeleteOp">
                      <FPK/>
                      <Description/>
                      <BizAppId/>
                      <RowKey action="reset"/>
                    </Plugin>
                    <Plugin>
                      <FPK/>
                      <Description>操作插件</Description>
                      <ClassName>kd.bos.tcbj.ec.opplugin.SettlementCentreBillOpPlugin</ClassName>
                      <Enabled>true</Enabled>
                      <BizAppId/>
                      <RowKey>1</RowKey>
                    </Plugin>
                  </Plugins>
                </Operation>
                <Operation action="edit" oid="c91d5125000033ac">
                  <Parameter>
                    <SaveParameter>
                      <StatusFieldId>6q69iznvHX</StatusFieldId>
                    </SaveParameter>
                  </Parameter>
                  <Validations>
                    <MustInputValidation action="edit" oid="RS=8GBXGZY4">
                      <Enabled>true</Enabled>
                    </MustInputValidation>
                  </Validations>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>4YKZNZ9C+SD3</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>4YKZNZ9C+RPX</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>4YKZNZ9C+SD3</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>4YKZNZ9C+SD5</Id>
                  <Key>deleteentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <TrackDownParameter>
                      <CheckRightBill>,im_saloutbill,</CheckRightBill>
                    </TrackDownParameter>
                  </Parameter>
                  <Name>下查</Name>
                  <OperationType>trackdown</OperationType>
                  <Id>4ZUFRXR0XNON</Id>
                  <SuccessMsg/>
                  <Key>trackdown</Key>
                </Operation>
              </Operations>
              <BusinessControl>
                <BusinessControl>
                  <BOTP>true</BOTP>
                  <WorkFlow>false</WorkFlow>
                </BusinessControl>
              </BusinessControl>
              <Id>4YKZNZ6V2JHZ</Id>
              <Key>yd_settlement_centre_bill</Key>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
              <Name>账单结算中间表</Name>
            </BillEntity>
            <EntryEntity>
              <TableName>tk_yd_settlement_bill_en</TableName>
              <Id>4YKZNZ9C+SD3</Id>
              <Key>entryentity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <ModifierField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4YKZNZ9C+RPW</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>修改人</Name>
              <FieldName>fmodifierfield</FieldName>
              <Key>modifierfield</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
              <BaseEntityId>68bde9ca00000eac</BaseEntityId>
            </ModifierField>
            <ModifyDateField>
              <FieldName>fmodifydatefield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4YKZNZ9C+SD4</Id>
              <Key>modifydatefield</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
              <Name>修改日期</Name>
            </ModifyDateField>
            <ComboField>
              <FieldName>fk_yd_platform</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>9EwoCfQ2ny</Id>
              <Key>yd_platform</Key>
              <MustInput>true</MustInput>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>猫超</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>阿里健康</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>中免</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <ComboField>
              <FieldName>fk_yd_shop</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>kbShfLij3Z</Id>
              <Key>yd_shop</Key>
              <MustInput>true</MustInput>
              <Name>店铺</Name>
              <Items>
                <ComboItem>
                  <Caption>浙江昊超</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>广州麦优网络科技有限公司-寄售（阿里健康）</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>上海中免日上商业有限公司</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>8vMFcz0dMO</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>物料编码</Name>
              <FieldName>fk_yd_materialid</FieldName>
              <RefProps>
                <RefProp>
                  <Name>yd_basedatafield.id</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_basedatafield.number</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_basedatafield.name</Name>
                </RefProp>
              </RefProps>
              <Key>yd_material</Key>
              <MustInput>true</MustInput>
              <DisplayProp>number</DisplayProp>
              <RefLayout/>
              <ParentId>4YKZNZ9C+SD3</ParentId>
              <BaseEntityId>5fa3b2b40000a2ac</BaseEntityId>
            </BasedataField>
            <BasedataPropField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>uuruvLP4xq</Id>
              <Key>yd_materialname</Key>
              <RefDisplayProp>name</RefDisplayProp>
              <ParentId>4YKZNZ9C+SD3</ParentId>
              <RefBaseFieldId>8vMFcz0dMO</RefBaseFieldId>
              <Name>物料名称</Name>
            </BasedataPropField>
            <PriceField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gYZqOszqxE</Id>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>含税单价</Name>
              <FieldName>fk_yd_priceandtax</FieldName>
              <Key>yd_priceandtax</Key>
              <DataScope>[0,9999999999999]</DataScope>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </PriceField>
            <QtyField>
              <UnitFieldId>KzLSbS9uo9</UnitFieldId>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>DV1CmSzMTJ</Id>
              <DefValue>0</DefValue>
              <Name>数量</Name>
              <FieldName>fk_yd_qty</FieldName>
              <Key>yd_qty</Key>
              <MustInput>true</MustInput>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </QtyField>
            <CheckBoxField>
              <FieldName>fk_yd_ispresent</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>9g4LYWE8Hk</Id>
              <Key>yd_ispresent</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
              <Name>是否赠品</Name>
            </CheckBoxField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3QoiubK6OI</Id>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>折前价税合计</Name>
              <FieldName>fk_yd_totaleasamount</FieldName>
              <Key>yd_totaleasamount</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>DyrmtzZK7G</Id>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>折扣额</Name>
              <FieldName>fk_yd_discountamount</FieldName>
              <Key>yd_discountamount</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>w8eXMaIEBC</Id>
              <DefValue>0</DefValue>
              <ZeroShow>true</ZeroShow>
              <Name>价税合计</Name>
              <FieldName>fk_yd_amountandtax</FieldName>
              <Key>yd_amountandtax</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
            </AmountField>
            <CheckBoxField>
              <FieldName>fk_yd_issalesreturn</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>u7FPyifA6j</Id>
              <Key>yd_issalesreturn</Key>
              <Name>是否退货</Name>
            </CheckBoxField>
            <TextField>
              <FieldName>fk_yd_productcode</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>NvIT4EX1DY</Id>
              <Key>yd_productcode</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
              <Name>业务平台对应货品编号</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_goodscode</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1unwV3YByZ</Id>
              <Key>yd_goodscode</Key>
              <ParentId>4YKZNZ9C+SD3</ParentId>
              <Name>业务平台对应商品编号</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <LargeTextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>PQ0Pb6j5m3</Id>
              <Name>关联账单编码</Name>
              <FieldName>fk_yd_source_bill_no</FieldName>
              <Key>yd_source_bill_no</Key>
              <MustInput>true</MustInput>
            </LargeTextField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>PBQqKINUIA</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>收货客户</Name>
              <FieldName>fk_yd_customerid</FieldName>
              <Key>yd_customer</Key>
              <MustInput>true</MustInput>
              <RefLayout/>
              <BaseEntityId>a86c9c130002f7ac</BaseEntityId>
            </BasedataField>
            <TextField>
              <FieldName>fk_yd_targetbillno</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>0cLUl5uOiQ</Id>
              <Key>yd_targetbillno</Key>
              <Name>下游单号</Name>
              <MaxLength>200</MaxLength>
            </TextField>
            <TextField>
              <FieldName>fk_yd_warehouse</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>TXAcrEV5W6</Id>
              <Key>yd_warehouse</Key>
              <MustInput>true</MustInput>
              <ParentId>4YKZNZ9C+SD3</ParentId>
              <Name>仓库</Name>
            </TextField>
            <TextAreaField>
              <FieldName>fk_yd_push_error_msg</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>nObh9pnaLg</Id>
              <Key>yd_push_error_msg</Key>
              <Name>下推销售出库单失败原因</Name>
              <MaxLength>2000</MaxLength>
            </TextAreaField>
            <CheckBoxField>
              <FieldName>fk_yd_ispush</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>nkhdYsZFTb</Id>
              <Key>yd_ispush</Key>
              <Name>是否下推成功</Name>
            </CheckBoxField>
            <DateField>
              <FieldName>fk_yd_bizdate</FieldName>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>GxrZ8lMxvp</Id>
              <Key>yd_bizdate</Key>
              <MustInput>true</MustInput>
              <Name>业务日期</Name>
            </DateField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750320682602</Version>
      <ParentId>ab7efc31000010ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_settlement_centre_bill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4YKZNZ6V2JHZ</Id>
      <InheritPath>00305e8b000006ac,ab7efc31000010ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1750320682565</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
