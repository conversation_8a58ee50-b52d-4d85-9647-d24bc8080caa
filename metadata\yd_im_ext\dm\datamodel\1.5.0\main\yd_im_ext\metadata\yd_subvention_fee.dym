<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>5/ARVLIV4W2/</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>5/ARVLIV4W2/</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1749611782000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>5/ARVLIV4W2/</Id>
          <Key>yd_subvention_fee</Key>
          <EntityId>5/ARVLIV4W2/</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>寄售费补分摊</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>5/ARVLL+JTAO</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/ARVLL+JEI0</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>5/ARVLL+JTAO</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/ARVLL+JTAP</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>5/ARVLL+JTAO</ParentId>
                      <ListFieldId>creator.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>创建人.姓名</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/ARVLIV4W2/</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>5/ARVLL+JTAO</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Direction>column</Direction>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>5/ARVLL+JEI/</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>5/ARVLIV4W2/</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>寄售费补分摊</Name>
              <Key>yd_subvention_fee</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>5/ARVLL+JEHY</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>5/ARVLL+JTAL</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>5/ARVLL+JEHY</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>5/ARVLL+JEHZ</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>5/ARVLL+JEHY</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>5/ARVLL+JTAM</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>5/ARVLL+JTAL</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>5/ARVLL+JEI+</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>5/ARVLL+JTAL</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>5/ARVLL+JTAN</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>5/ARVLIV4W2/</EntityId>
                    </BillListAp>
                    <CommonDateFilterColumnAp action="edit" oid="3c48f498000000ac">
                      <DefValue>24</DefValue>
                    </CommonDateFilterColumnAp>
                    <DecimalListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/AXLKIOT3UJ</Id>
                      <Key>yd_decimallistcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>2</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_settle_month</ListFieldId>
                      <DisplayFormatString>#</DisplayFormatString>
                      <Name>结算月份</Name>
                    </DecimalListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/AXM38QM77U</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_platform</ListFieldId>
                      <Name>平台</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/AXM=GFHMQA</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_material.number</ListFieldId>
                      <Name>物料编码</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/AXMMX+OLG/</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_material.name</ListFieldId>
                      <Name>物料名称</Name>
                    </ListColumnAp>
                    <DecimalListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>5/AXN8EK+5L0</Id>
                      <Key>yd_decimallistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_amount</ListFieldId>
                      <Name>费补金额</Name>
                    </DecimalListColumnAp>
                    <ComboListColumnAp action="remove" oid="f87b7292000031ac"/>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <AdvConBarItemAp>
              <Id>9fp8Bz3N3q</Id>
              <Key>yd_advconbaritemap</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>3hzwT26p7D</ParentId>
              <Name>工具栏项</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <Id>OUIx1kEwGX</Id>
              <Key>yd_advconbaritemap1</Key>
              <OperationStyle>0</OperationStyle>
              <Index>1</Index>
              <ParentId>3hzwT26p7D</ParentId>
              <Name>工具栏项</Name>
            </AdvConBarItemAp>
            <EntryAp>
              <EntryId>5/ARVLL+JTAI</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>5/ARVLL+JTAI</Id>
              <Name>费补明细单据体</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>yd_subvention_entity</Key>
              <SplitPage>true</SplitPage>
              <ParentId>5/ARVLL+JEHU</ParentId>
            </EntryAp>
            <AdvConSummaryPanelAp>
              <Id>5/ARVLL+JEHT</Id>
              <Name>高级面板摘要容器</Name>
              <Key>advconsummarypanelap</Key>
              <ParentId>5/ARVLL+JTAF</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>5/ARVLL+JTAG</Id>
              <Key>advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>5/ARVLL+JTAF</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>5/ARVLL+JEHU</Id>
              <AlignItems>stretch</AlignItems>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>advconchildcanelap</Key>
              <Direction>column</Direction>
              <ParentId>5/ARVLL+JTAF</ParentId>
              <Wrap>false</Wrap>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>5/ARVLL+JTAH</Id>
              <Key>tb_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>5/ARVLL+JTAG</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>5/ARVLL+JEHV</Id>
              <Key>tb_del</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>5/ARVLL+JTAG</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryFieldAp>
              <Id>5/ARVLL+JEHW</Id>
              <FieldId>5/ARVLL+JEHW</FieldId>
              <Name>修改人</Name>
              <Hidden>true</Hidden>
              <Key>modifierfield</Key>
              <ParentId>5/ARVLL+JTAI</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>5/ARVLL+JTAJ</Id>
              <FieldId>5/ARVLL+JTAJ</FieldId>
              <Index>1</Index>
              <Name>修改时间</Name>
              <Hidden>true</Hidden>
              <Key>modifydatefield</Key>
              <ParentId>5/ARVLL+JTAI</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>3bJoiy77W6</Id>
              <FieldId>3bJoiy77W6</FieldId>
              <Index>2</Index>
              <Name>结算月份</Name>
              <Key>yd_settle_month</Key>
              <ParentId>5/ARVLL+JTAI</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>af878q3nS9</Id>
              <FieldId>af878q3nS9</FieldId>
              <Index>3</Index>
              <Name>平台</Name>
              <Key>yd_platform</Key>
              <ParentId>5/ARVLL+JTAI</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>O9G7HYIiqf</Id>
              <FieldId>O9G7HYIiqf</FieldId>
              <Index>4</Index>
              <Name>物料编码</Name>
              <Key>yd_material</Key>
              <ParentId>5/ARVLL+JTAI</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>3jHl4fBcax</Id>
              <FieldId>3jHl4fBcax</FieldId>
              <Index>5</Index>
              <Name>费补金额</Name>
              <Key>yd_amount</Key>
              <ParentId>5/ARVLL+JTAI</ParentId>
            </EntryFieldAp>
            <AdvConSummaryPanelAp>
              <Id>OB1HAroRDB</Id>
              <Name>高级面板摘要容器</Name>
              <Key>yd_advconsummarypanelap</Key>
              <ParentId>IpUMYK4wmw</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>3hzwT26p7D</Id>
              <Key>yd_advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>IpUMYK4wmw</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>b0GouHmKX4</Id>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>yd_advconchildpanelap</Key>
              <ParentId>IpUMYK4wmw</ParentId>
            </AdvConChildPanelAp>
            <EntryFieldAp>
              <Id>f64kXX64iu</Id>
              <FieldId>f64kXX64iu</FieldId>
              <Name>物料编码</Name>
              <Key>yd_item_material</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>uCL2RKa9Bn</Id>
              <FieldId>uCL2RKa9Bn</FieldId>
              <Index>1</Index>
              <Name>导入费补</Name>
              <Key>yd_item_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>qgkO2WCAa4</Id>
              <FieldId>qgkO2WCAa4</FieldId>
              <Index>2</Index>
              <Name>导入费补取正数</Name>
              <Key>yd_item_positive_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>2jUv3mb4zT</Id>
              <FieldId>2jUv3mb4zT</FieldId>
              <Index>3</Index>
              <Name>含税单价</Name>
              <Key>yd_item_unit_price</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>pxUq13Xogr</Id>
              <FieldId>pxUq13Xogr</FieldId>
              <Index>4</Index>
              <Name>原折前价税合计</Name>
              <Key>yd_item_before_dic_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>cA2cJ6LX7e</Id>
              <FieldId>cA2cJ6LX7e</FieldId>
              <Index>5</Index>
              <Name>原促销折扣额</Name>
              <Key>yd_item_before_dic</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>R5KFWtwVdQ</Id>
              <FieldId>R5KFWtwVdQ</FieldId>
              <Index>6</Index>
              <Name>原价税合计</Name>
              <Key>yd_item_org_amt_tax</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>98OirYLlri</Id>
              <FieldId>98OirYLlri</FieldId>
              <Index>7</Index>
              <Name>第一轮调整</Name>
              <Key>yd_item_1st_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>6k0eY84qqm</Id>
              <FieldId>6k0eY84qqm</FieldId>
              <Index>8</Index>
              <Name>第一轮价税剩余可调整</Name>
              <Key>yd_item_1st_modulate</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>0W3eje8Rg6</Id>
              <FieldId>0W3eje8Rg6</FieldId>
              <Index>9</Index>
              <Name>第一轮调整后剩余费补</Name>
              <Key>yd_item_1st_remnant</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>VJEYE5ZdOZ</Id>
              <FieldId>VJEYE5ZdOZ</FieldId>
              <Index>10</Index>
              <Name>权重占比</Name>
              <Key>yd_item_proportion</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>4ouGOIHgBR</Id>
              <FieldId>4ouGOIHgBR</FieldId>
              <Index>11</Index>
              <Name>第二轮按销售占比前十分摊</Name>
              <Key>yd_item_2nd_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>T4gf2yPkv4</Id>
              <FieldId>T4gf2yPkv4</FieldId>
              <Index>12</Index>
              <Name>第二轮剩余继续分摊</Name>
              <Key>yd_item_2nd_co_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>IqgEdu2AhO</Id>
              <FieldId>IqgEdu2AhO</FieldId>
              <Index>13</Index>
              <Name>实际第二轮分摊金额</Name>
              <Key>yd_item_2nd_a_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Fxve5jkCLc</Id>
              <FieldId>Fxve5jkCLc</FieldId>
              <Index>14</Index>
              <Name>第三轮分摊金额</Name>
              <Key>yd_item_3th_a_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>YHwJsFWP8E</Id>
              <FieldId>YHwJsFWP8E</FieldId>
              <Index>15</Index>
              <Name>总调整额</Name>
              <Key>yd_item_total_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>oA8rxidYz0</Id>
              <FieldId>oA8rxidYz0</FieldId>
              <Index>16</Index>
              <Name>价税合计</Name>
              <Key>yd_item_amt_and_tax</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>xUUwC1Qfiz</Id>
              <FieldId>xUUwC1Qfiz</FieldId>
              <Index>17</Index>
              <Name>折扣额</Name>
              <Key>yd_item_dic</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </EntryFieldAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <EntryAp>
              <EntryId>NtpdluOcLB</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>NtpdluOcLB</Id>
              <Name>费补分摊明细单据体</Name>
              <PageType/>
              <Key>yd_material_entity</Key>
              <SplitPage>true</SplitPage>
              <ParentId>b0GouHmKX4</ParentId>
            </EntryAp>
            <FieldAp>
              <Id>VfY5hVwsKV</Id>
              <FieldId>VfY5hVwsKV</FieldId>
              <Index>4</Index>
              <Name>导入费补合计</Name>
              <Key>yd_total_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>gfvSx9KadG</Id>
              <FieldId>gfvSx9KadG</FieldId>
              <Index>5</Index>
              <Name>平台费补金额</Name>
              <Key>yd_platform_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>j3wqELp625</Id>
              <FieldId>j3wqELp625</FieldId>
              <Index>6</Index>
              <Name>费补差异</Name>
              <Key>yd_diff_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>4jT7ZqfBC9</Id>
              <FieldId>4jT7ZqfBC9</FieldId>
              <Index>7</Index>
              <Name>导入费补负数</Name>
              <Key>yd_negative_diff_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>5kKOUL4Idv</Id>
              <FieldId>5kKOUL4Idv</FieldId>
              <Index>8</Index>
              <Name>第一轮调整后剩余费补</Name>
              <Key>yd_1st_remnant_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>7S4YxMkgHd</Id>
              <FieldId>7S4YxMkgHd</FieldId>
              <Index>9</Index>
              <Name>总调整额</Name>
              <Key>yd_total_modulate_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>3SKkILzpFS</Id>
              <FieldId>3SKkILzpFS</FieldId>
              <Index>10</Index>
              <Name>调整后差异</Name>
              <Key>yd_after_diff_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>sWeuXnigmt</Id>
              <FieldId>sWeuXnigmt</FieldId>
              <Index>11</Index>
              <Name>开票金额</Name>
              <Key>yd_invoice_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>r1yCK4iPFn</Id>
              <FieldId>r1yCK4iPFn</FieldId>
              <Index>12</Index>
              <Name>价税合计</Name>
              <Key>yd_amount_and_tax</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Pj4SXI3CrU</Id>
              <FieldId>Pj4SXI3CrU</FieldId>
              <Index>13</Index>
              <Name>本期退货</Name>
              <Key>yd_cur_return_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>abaKyUaB9N</Id>
              <FieldId>abaKyUaB9N</FieldId>
              <Index>14</Index>
              <Name>本期差异</Name>
              <Key>yd_cur_diff_amt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>O090afKgbM</Id>
              <FieldId>O090afKgbM</FieldId>
              <Index>15</Index>
              <Name>账单结算中间表</Name>
              <Key>yd_center_bill_no</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <FieldsetPanelAp action="edit" oid="xTQZ9d8tHa">
              <Index>1</Index>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>5/ARVLL+JTAF</Id>
              <Key>advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>分录</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>IpUMYK4wmw</Id>
              <Key>yd_advconap_item</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>3</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>@container-bg-color</BackColor>
              <Name>费补分摊明细</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Index>4</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338612817267262464</ModifierId>
      <EntityId>5/ARVLIV4W2/</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749611781697</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_subvention_fee</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>5/ARVLIV4W2/</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1749611782000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>5/ARVLIV4W2/</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>寄售费补分摊</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_subvention_fee</TableName>
              <Operations>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>5/ARVLL+JTAI</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>5/ARVLL+JEHX</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>5/ARVLL+JTAI</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>5/ARVLL+JTAK</Id>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>5/ARVLIV4W2/</Id>
              <Key>yd_subvention_fee</Key>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
              <Name>寄售费补分摊</Name>
            </BillEntity>
            <BillStatusField action="edit" oid="6q69iznvHX">
              <DefValue>C</DefValue>
            </BillStatusField>
            <EntryEntity>
              <TableName>tk_yd_subvention_entity</TableName>
              <Id>5/ARVLL+JTAI</Id>
              <Key>yd_subvention_entity</Key>
              <Name>费补明细单据体</Name>
            </EntryEntity>
            <ModifierField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5/ARVLL+JEHW</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>修改人</Name>
              <FieldName>fmodifierfield</FieldName>
              <Key>modifierfield</Key>
              <ParentId>5/ARVLL+JTAI</ParentId>
              <BaseEntityId>68bde9ca00000eac</BaseEntityId>
            </ModifierField>
            <ModifyDateField>
              <FieldName>fmodifydatefield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5/ARVLL+JTAJ</Id>
              <Key>modifydatefield</Key>
              <ParentId>5/ARVLL+JTAI</ParentId>
              <Name>修改日期</Name>
            </ModifyDateField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>VfY5hVwsKV</Id>
              <DefValue>0</DefValue>
              <Name>导入费补合计</Name>
              <FieldName>fk_yd_total_amt</FieldName>
              <Key>yd_total_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gfvSx9KadG</Id>
              <DefValue>0</DefValue>
              <Name>平台费补金额</Name>
              <FieldName>fk_yd_platform_amt</FieldName>
              <Key>yd_platform_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>j3wqELp625</Id>
              <DefValue>0</DefValue>
              <Name>费补差异</Name>
              <FieldName>fk_yd_diff_amt</FieldName>
              <Key>yd_diff_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4jT7ZqfBC9</Id>
              <DefValue>0</DefValue>
              <Name>导入费补负数</Name>
              <FieldName>fk_yd_amountfield</FieldName>
              <Key>yd_negative_diff_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>5kKOUL4Idv</Id>
              <DefValue>0</DefValue>
              <Name>第一轮调整后剩余费补</Name>
              <FieldName>fk_yd_first_remnant_amt</FieldName>
              <Key>yd_1st_remnant_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>7S4YxMkgHd</Id>
              <DefValue>0</DefValue>
              <Name>总调整额</Name>
              <FieldName>fk_yd_total_modulate_amt</FieldName>
              <Key>yd_total_modulate_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3SKkILzpFS</Id>
              <DefValue>0</DefValue>
              <Name>调整后差异</Name>
              <FieldName>fk_yd_after_diff_amt</FieldName>
              <Key>yd_after_diff_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>sWeuXnigmt</Id>
              <DefValue>0</DefValue>
              <Name>开票金额</Name>
              <FieldName>fk_yd_invoice_amt</FieldName>
              <Key>yd_invoice_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>r1yCK4iPFn</Id>
              <DefValue>0</DefValue>
              <Name>价税合计</Name>
              <FieldName>fk_yd_amount_and_tax</FieldName>
              <Key>yd_amount_and_tax</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Pj4SXI3CrU</Id>
              <DefValue>0</DefValue>
              <Name>本期退货</Name>
              <FieldName>fk_yd_cur_return_amt</FieldName>
              <Key>yd_cur_return_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>abaKyUaB9N</Id>
              <DefValue>0</DefValue>
              <Name>本期差异</Name>
              <FieldName>fk_yd_cur_diff_amt</FieldName>
              <Key>yd_cur_diff_amt</Key>
            </AmountField>
            <TextField>
              <FieldName>fk_yd_center_bill_no</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>O090afKgbM</Id>
              <Key>yd_center_bill_no</Key>
              <Name>账单结算中间表</Name>
              <MaxLength>100</MaxLength>
            </TextField>
            <IntegerField>
              <FieldName>fk_yd_settle_month</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3bJoiy77W6</Id>
              <Key>yd_settle_month</Key>
              <MustInput>true</MustInput>
              <DefValue>0</DefValue>
              <ParentId>5/ARVLL+JTAI</ParentId>
              <Name>结算月份</Name>
            </IntegerField>
            <TextField>
              <FieldName>fk_yd_platform</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>af878q3nS9</Id>
              <Key>yd_platform</Key>
              <MustInput>true</MustInput>
              <ParentId>5/ARVLL+JTAI</ParentId>
              <Name>平台</Name>
            </TextField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>O9G7HYIiqf</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>物料编码</Name>
              <FieldName>fk_yd_material</FieldName>
              <Key>yd_material</Key>
              <MustInput>true</MustInput>
              <RefLayout/>
              <ParentId>5/ARVLL+JTAI</ParentId>
              <BaseEntityId>5fa3b2b40000a2ac</BaseEntityId>
            </BasedataField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3jHl4fBcax</Id>
              <DefValue>0</DefValue>
              <Name>费补金额</Name>
              <FieldName>fk_yd_amount</FieldName>
              <Key>yd_amount</Key>
              <MustInput>true</MustInput>
              <ParentId>5/ARVLL+JTAI</ParentId>
            </AmountField>
            <EntryEntity>
              <TableName>tk_yd_subvention_material</TableName>
              <Id>NtpdluOcLB</Id>
              <Key>yd_material_entity</Key>
              <Name>费补分摊明细单据体</Name>
            </EntryEntity>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>f64kXX64iu</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>物料编码</Name>
              <FieldName>fk_yd_item_materialid</FieldName>
              <Key>yd_item_material</Key>
              <RefLayout/>
              <ParentId>NtpdluOcLB</ParentId>
              <BaseEntityId>5fa3b2b40000a2ac</BaseEntityId>
            </BasedataField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>uCL2RKa9Bn</Id>
              <DefValue>0</DefValue>
              <Name>导入费补</Name>
              <FieldName>fk_yd_item_amt</FieldName>
              <Key>yd_item_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>qgkO2WCAa4</Id>
              <DefValue>0</DefValue>
              <Name>导入费补取正数</Name>
              <FieldName>fk_yd_item_positive_amt</FieldName>
              <Key>yd_item_positive_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <PriceField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>2jUv3mb4zT</Id>
              <DefValue>0</DefValue>
              <Name>含税单价</Name>
              <FieldName>fk_yd_item_unit_price</FieldName>
              <Key>yd_item_unit_price</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </PriceField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pxUq13Xogr</Id>
              <DefValue>0</DefValue>
              <Name>原折前价税合计</Name>
              <FieldName>fk_yd_item_before_dic_amt</FieldName>
              <Key>yd_item_before_dic_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>cA2cJ6LX7e</Id>
              <DefValue>0</DefValue>
              <Name>原促销折扣额</Name>
              <FieldName>fk_yd_item_before_dic</FieldName>
              <Key>yd_item_before_dic</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>R5KFWtwVdQ</Id>
              <DefValue>0</DefValue>
              <Name>原价税合计</Name>
              <FieldName>fk_yd_item_org_amt_tax</FieldName>
              <Key>yd_item_org_amt_tax</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>98OirYLlri</Id>
              <DefValue>0</DefValue>
              <Name>第一轮调整</Name>
              <FieldName>fk_yd_item_1st_amt</FieldName>
              <Key>yd_item_1st_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>6k0eY84qqm</Id>
              <DefValue>0</DefValue>
              <Name>第一轮价税剩余可调整</Name>
              <FieldName>fk_yd_item_1st_modulate</FieldName>
              <Key>yd_item_1st_modulate</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>0W3eje8Rg6</Id>
              <DefValue>0</DefValue>
              <Name>第一轮调整后剩余费补</Name>
              <FieldName>fk_yd_item_1st_remnant</FieldName>
              <Key>yd_item_1st_remnant</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <DecimalField>
              <FieldName>fk_yd_item_proportion</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>VJEYE5ZdOZ</Id>
              <Key>yd_item_proportion</Key>
              <DefValue>0</DefValue>
              <ParentId>NtpdluOcLB</ParentId>
              <Name>权重占比</Name>
            </DecimalField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4ouGOIHgBR</Id>
              <DefValue>0</DefValue>
              <Name>第二轮按销售占比前十分摊</Name>
              <FieldName>fk_yd_item_2nd_amt</FieldName>
              <Key>yd_item_2nd_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>T4gf2yPkv4</Id>
              <DefValue>0</DefValue>
              <Name>第二轮剩余继续分摊</Name>
              <FieldName>fk_yd_item_2nd_co_amt</FieldName>
              <Key>yd_item_2nd_co_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>IqgEdu2AhO</Id>
              <DefValue>0</DefValue>
              <Name>实际第二轮分摊金额</Name>
              <FieldName>fk_yd_item_2nd_a_amt</FieldName>
              <Key>yd_item_2nd_a_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Fxve5jkCLc</Id>
              <DefValue>0</DefValue>
              <Name>第三轮分摊金额</Name>
              <FieldName>fk_yd_item_3th_a_amt</FieldName>
              <Key>yd_item_3th_a_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>YHwJsFWP8E</Id>
              <DefValue>0</DefValue>
              <Name>总调整额</Name>
              <FieldName>fk_yd_item_total_amt</FieldName>
              <Key>yd_item_total_amt</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>oA8rxidYz0</Id>
              <DefValue>0</DefValue>
              <Name>价税合计</Name>
              <FieldName>fk_yd_item_amt_and_tax</FieldName>
              <Key>yd_item_amt_and_tax</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xUUwC1Qfiz</Id>
              <DefValue>0</DefValue>
              <Name>折扣额</Name>
              <FieldName>fk_yd_item_dic</FieldName>
              <Key>yd_item_dic</Key>
              <ParentId>NtpdluOcLB</ParentId>
            </AmountField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1749611781733</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_subvention_fee</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>5/ARVLIV4W2/</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1749611781697</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
