<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>50++GXYEH5JG</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>50++GXYEH5JG</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1750064683000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>50++GXYEH5JG</Id>
          <Key>yd_test_task</Key>
          <EntityId>50++GXYEH5JG</EntityId>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>测试专用</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BasedataFormAp action="edit" oid="1942c188000064ac">
              <Plugins>
                <Plugin action="edit" oid="kd.bos.form.plugin.CodeRulePlugin">
                  <FPK/>
                  <Description/>
                  <BizAppId/>
                </Plugin>
                <Plugin action="edit" oid="dev.tpl.base.kd.bos.form.plugin.templatebaseedit">
                  <Description/>
                  <RowKey>1</RowKey>
                </Plugin>
                <Plugin>
                  <FPK/>
                  <Description>测试插件</Description>
                  <ClassName>kd.bos.tcbj.ec.formplugin.TestTaskEditPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                  <RowKey>2</RowKey>
                </Plugin>
              </Plugins>
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>50++GXYEH5JG</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>50++GXYEH5JG</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>测试专用</Name>
              <Key>yd_test_task</Key>
              <ListMeta>
                <FormMetadata>
                  <Items>
                    <FilterGridViewAp action="edit" oid="filtergridview">
                      <NewFilter>true</NewFilter>
                    </FilterGridViewAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BasedataFormAp>
            <FlexPanelAp action="edit" oid="PxNfkkak4s">
              <Grow>0</Grow>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                  <Padding>
                    <Padding>
                      <Left>2px</Left>
                      <Right action="reset"/>
                    </Padding>
                  </Padding>
                </Style>
              </Style>
            </FlexPanelAp>
            <BarItemAp>
              <Id>Kj60vCWYv2</Id>
              <Key>yd_btntest</Key>
              <OperationStyle>0</OperationStyle>
              <Index>9</Index>
              <ParentId>IDHr4o2NNH</ParentId>
              <Name>测试</Name>
            </BarItemAp>
            <BarItemAp action="edit" oid="evV9jbiOYW">
              <Index>10</Index>
            </BarItemAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338618754925331456</ModifierId>
      <EntityId>50++GXYEH5JG</EntityId>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750064682597</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_test_task</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>50++GXYEH5JG</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1750064683000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>50++GXYEH5JG</Id>
          <ParentId>1942c188000065ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>测试专用</Name>
          <InheritPath>1942c188000065ac</InheritPath>
          <Items>
            <BaseEntity action="edit" oid="1942c188000066ac">
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_test_task</TableName>
              <Id>50++GXYEH5JG</Id>
              <Name>测试专用</Name>
              <Template action="reset"/>
              <Key>yd_test_task</Key>
              <NetworkControl>
                <NetCtrlOperation>
                  <OperationKey>submit</OperationKey>
                  <GroupId>default_netctrl</GroupId>
                  <Id>c91d5125000034ac</Id>
                  <Key>default_netctrl_submit</Key>
                </NetCtrlOperation>
              </NetworkControl>
              <DefaultPageSetting>{"mblist":"","pclist":"","pcbill":"","mbbill":""}</DefaultPageSetting>
            </BaseEntity>
            <MuliLangTextField action="edit" oid="8oq6R4m9CF">
              <GL>true</GL>
            </MuliLangTextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BaseFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750064682672</Version>
      <ParentId>1942c188000065ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_test_task</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>50++GXYEH5JG</Id>
      <InheritPath>1942c188000065ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1750064682597</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
