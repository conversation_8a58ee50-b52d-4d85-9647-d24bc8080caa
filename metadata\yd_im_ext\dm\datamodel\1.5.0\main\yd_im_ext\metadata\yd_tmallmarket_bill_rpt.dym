<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>4YE/J/MLE1EZ</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>4YE/J/MLE1EZ</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1748399053000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>4YE/J/MLE1EZ</Id>
          <Key>yd_tmallmarket_bill_rpt</Key>
          <EntityId>4YE/J/MLE1EZ</EntityId>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>猫超平台账单报表</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportFormAp action="edit" oid="1b760aa100003aac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>猫超平台账单报表生成销售出库单插件</Description>
                  <ClassName>kd.bos.tcbj.ec.report.formplugin.TMallMarketBillFormRptPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>4YE/J/MLE1EZ</Id>
              <Name>猫超平台账单报表</Name>
              <Key>yd_tmallmarket_bill_rpt</Key>
            </ReportFormAp>
            <ReportListAp action="edit" oid="Tm10PEdaRq">
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <ReportPlugin>kd.bos.tcbj.ec.report.queryplugin.TMallMarketBillQueryRptPlugin</ReportPlugin>
            </ReportListAp>
            <BarItemAp>
              <Id>SMnF19hHTA</Id>
              <Key>yd_create_sale_bill</Key>
              <OperationStyle>0</OperationStyle>
              <Index>3</Index>
              <ParentId>FohRnuDXx2</ParentId>
              <Name>下推</Name>
            </BarItemAp>
            <FieldAp>
              <Id>LueirMTKvV</Id>
              <FieldId>LueirMTKvV</FieldId>
              <Name>对账单编号</Name>
              <Key>yd_fbillno_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>a4HWfsBxeo</Id>
              <FieldId>a4HWfsBxeo</FieldId>
              <Index>1</Index>
              <Name>开始业务日期</Name>
              <Key>yd_start_bill_time_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>vo8Ypn8AJo</Id>
              <FieldId>vo8Ypn8AJo</FieldId>
              <Index>2</Index>
              <Name>结束业务日期</Name>
              <Key>yd_end_bill_time_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>eTSY8bT7bv</Id>
              <FieldId>eTSY8bT7bv</FieldId>
              <Index>3</Index>
              <Name>状态</Name>
              <Key>yd_send_status_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>PcV3GwZswZ</Id>
              <FieldId>PcV3GwZswZ</FieldId>
              <Index>4</Index>
              <Name>是否异常</Name>
              <Key>yd_is_exception_param</Key>
              <ParentId>M4PHQ9C8Ss</ParentId>
            </FieldAp>
            <EntryFieldAp>
              <FireUpdEvt>false</FireUpdEvt>
              <Id>O0YkyFapvX</Id>
              <FieldId>O0YkyFapvX</FieldId>
              <Name>FID</Name>
              <Key>yd_fid</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>J5uKXWruXZ</Id>
              <FieldId>J5uKXWruXZ</FieldId>
              <Index>1</Index>
              <Name>平台</Name>
              <Key>yd_platform</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>IQgMANpEhY</Id>
              <FieldId>IQgMANpEhY</FieldId>
              <Index>2</Index>
              <Name>店铺</Name>
              <Key>yd_shop</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ACvYojEue9</Id>
              <FieldId>ACvYojEue9</FieldId>
              <Hyperlink>true</Hyperlink>
              <Index>3</Index>
              <Name>账单编号</Name>
              <Key>yd_fbillno</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>e23gsuQdWw</Id>
              <FieldId>e23gsuQdWw</FieldId>
              <Index>4</Index>
              <Name>业务日期</Name>
              <Key>yd_bill_create_time</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>OoxKEDH6Md</Id>
              <FieldId>OoxKEDH6Md</FieldId>
              <Index>5</Index>
              <Name>统计时间</Name>
              <Key>yd_fcreatetime</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>fL6EYOB351</Id>
              <FieldId>fL6EYOB351</FieldId>
              <Index>6</Index>
              <Name>中间表单号</Name>
              <Key>yd_middle_bill_no</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>AelYKmoO2v</Id>
              <FieldId>AelYKmoO2v</FieldId>
              <Index>7</Index>
              <Name>状态（中间表）</Name>
              <Key>yd_middle_bill_status</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>N98UnCg7sb</Id>
              <FieldId>N98UnCg7sb</FieldId>
              <Index>8</Index>
              <Name>是否异常</Name>
              <Key>yd_is_exception</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>hydBPLjh2K</Id>
              <FieldId>hydBPLjh2K</FieldId>
              <Index>9</Index>
              <Name>账单周期</Name>
              <Hidden>true</Hidden>
              <Key>yd_bill_cycle</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>dz0j943iNz</Id>
              <FieldId>dz0j943iNz</FieldId>
              <Index>10</Index>
              <Name>对账状态</Name>
              <Hidden>true</Hidden>
              <Key>yd_recon_status_desc</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ShGhBnQIdg</Id>
              <FieldId>ShGhBnQIdg</FieldId>
              <Index>11</Index>
              <Name>发票状态</Name>
              <Hidden>true</Hidden>
              <Key>yd_invoice_status_desc</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>ZYa5HSxha6</Id>
              <FieldId>ZYa5HSxha6</FieldId>
              <Index>12</Index>
              <Name>异常原因</Name>
              <Key>yd_exception_content</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>FomsYrDbSA</Id>
              <FieldId>FomsYrDbSA</FieldId>
              <Index>13</Index>
              <Name>销售出库单/退货单</Name>
              <Key>yd_target_bill_no</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>3eUzpHXVv1</Id>
              <FieldId>3eUzpHXVv1</FieldId>
              <Index>14</Index>
              <Name>状态（销售出库单）</Name>
              <Key>yd_ispush</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>Nlmm7US0e5</Id>
              <FieldId>Nlmm7US0e5</FieldId>
              <Index>15</Index>
              <Name>下推销售出库单异常原因</Name>
              <Key>yd_push_error_msg</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>YcI2tU2bz2</Id>
              <FieldId>YcI2tU2bz2</FieldId>
              <Index>16</Index>
              <Name>平台金额</Name>
              <Key>yd_platform_amt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>eaZ3JmSgSM</Id>
              <FieldId>eaZ3JmSgSM</FieldId>
              <Index>17</Index>
              <Name>销售出库单金额</Name>
              <Key>yd_sale_bill_amt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>60pIlwPlFd</Id>
              <FieldId>60pIlwPlFd</FieldId>
              <Index>18</Index>
              <Name>差异核算结果</Name>
              <Key>yd_difference</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>E4qemc12pl</Id>
              <FieldId>E4qemc12pl</FieldId>
              <Index>19</Index>
              <Name>付款状态</Name>
              <Hidden>true</Hidden>
              <Key>yd_payment_status_desc</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>F8ffQAM3Lo</Id>
              <FieldId>F8ffQAM3Lo</FieldId>
              <Index>20</Index>
              <Name>结算含税总额</Name>
              <Hidden>true</Hidden>
              <Key>yd_receive_total_amt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>KUMyBQz4Ys</Id>
              <FieldId>KUMyBQz4Ys</FieldId>
              <Index>21</Index>
              <Name>货款含税总额</Name>
              <Hidden>true</Hidden>
              <Key>yd_goods_total_amt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>1kuemCVlJS</Id>
              <FieldId>1kuemCVlJS</FieldId>
              <Index>22</Index>
              <Name>票扣含税总额</Name>
              <Hidden>true</Hidden>
              <Key>yd_receipt_total_amt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>szCr57GJKH</Id>
              <FieldId>szCr57GJKH</FieldId>
              <Index>23</Index>
              <Name>应转账含税总额</Name>
              <Hidden>true</Hidden>
              <Key>yd_payment_total_amt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>xtvGcwP2aW</Id>
              <FieldId>xtvGcwP2aW</FieldId>
              <Index>24</Index>
              <Name>参考</Name>
              <Hidden>true</Hidden>
              <Key>yd_reference</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>TDn2ZpMspN</Id>
              <FieldId>TDn2ZpMspN</FieldId>
              <Index>25</Index>
              <Name>应开票含税总额</Name>
              <Hidden>true</Hidden>
              <Key>yd_invoice_total_amt</Key>
              <ParentId>Tm10PEdaRq</ParentId>
            </EntryFieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1338612817267262464</ModifierId>
      <EntityId>4YE/J/MLE1EZ</EntityId>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1748399053426</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_tmallmarket_bill_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>4YE/J/MLE1EZ</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1748399053000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Isv>yd</Isv>
          <Id>4YE/J/MLE1EZ</Id>
          <ParentId>1b760aa100003bac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>猫超平台账单报表</Name>
          <InheritPath>1b760aa100003bac</InheritPath>
          <Items>
            <ReportEntity action="edit" oid="1b760aa100003cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Id>4YE/J/MLE1EZ</Id>
              <Key>yd_tmallmarket_bill_rpt</Key>
              <Name>猫超平台账单报表</Name>
            </ReportEntity>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>LueirMTKvV</Id>
              <Key>yd_fbillno_param</Key>
              <Name>对账单编号</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ACvYojEue9</Id>
              <Key>yd_fbillno</Key>
              <Name>账单编号</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>hydBPLjh2K</Id>
              <Key>yd_bill_cycle</Key>
              <Name>账单周期</Name>
            </TextField>
            <DateTimeField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>e23gsuQdWw</Id>
              <Key>yd_bill_create_time</Key>
              <Name>业务日期</Name>
            </DateTimeField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>F8ffQAM3Lo</Id>
              <DefValue>0</DefValue>
              <Name>结算含税总额</Name>
              <Key>yd_receive_total_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>KUMyBQz4Ys</Id>
              <DefValue>0</DefValue>
              <Name>货款含税总额</Name>
              <Key>yd_goods_total_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>1kuemCVlJS</Id>
              <DefValue>0</DefValue>
              <Name>票扣含税总额</Name>
              <Key>yd_receipt_total_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>YcI2tU2bz2</Id>
              <DefValue>0</DefValue>
              <Name>平台金额</Name>
              <Key>yd_platform_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>eaZ3JmSgSM</Id>
              <DefValue>0</DefValue>
              <Name>销售出库单金额</Name>
              <Key>yd_sale_bill_amt</Key>
            </AmountField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>szCr57GJKH</Id>
              <DefValue>0</DefValue>
              <Name>应转账含税总额</Name>
              <Key>yd_payment_total_amt</Key>
            </AmountField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>dz0j943iNz</Id>
              <Key>yd_recon_status_desc</Key>
              <Name>对账状态</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ShGhBnQIdg</Id>
              <Key>yd_invoice_status_desc</Key>
              <Name>发票状态</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>E4qemc12pl</Id>
              <Key>yd_payment_status_desc</Key>
              <Name>付款状态</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>xtvGcwP2aW</Id>
              <Key>yd_reference</Key>
              <Name>参考</Name>
            </TextField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>a4HWfsBxeo</Id>
              <Key>yd_start_bill_time_param</Key>
              <Name>开始业务日期</Name>
            </DateField>
            <DateField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>vo8Ypn8AJo</Id>
              <Key>yd_end_bill_time_param</Key>
              <Name>结束业务日期</Name>
            </DateField>
            <DateTimeField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>OoxKEDH6Md</Id>
              <Key>yd_fcreatetime</Key>
              <Name>统计时间</Name>
            </DateTimeField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>3eUzpHXVv1</Id>
              <Key>yd_ispush</Key>
              <Name>状态（销售出库单）</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>N98UnCg7sb</Id>
              <Key>yd_is_exception</Key>
              <Name>是否异常</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>ZYa5HSxha6</Id>
              <Key>yd_exception_content</Key>
              <Name>异常原因</Name>
            </TextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>60pIlwPlFd</Id>
              <DefValue>0</DefValue>
              <Name>差异核算结果</Name>
              <Key>yd_difference</Key>
            </AmountField>
            <MulComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>eTSY8bT7bv</Id>
              <Key>yd_send_status_param</Key>
              <Name>状态</Name>
              <Items>
                <ComboItem>
                  <Caption>待推送</Caption>
                  <Value>待推送</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>下推成功</Caption>
                  <Value>下推成功</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>下推失败</Caption>
                  <Value>下推失败</Value>
                  <Seq>2</Seq>
                </ComboItem>
              </Items>
            </MulComboField>
            <ComboField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>PcV3GwZswZ</Id>
              <Key>yd_is_exception_param</Key>
              <Name>是否异常</Name>
              <Items>
                <ComboItem>
                  <Caption>否</Caption>
                  <Value>否</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>是</Caption>
                  <Value>是</Value>
                  <Seq>1</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>AelYKmoO2v</Id>
              <Key>yd_middle_bill_status</Key>
              <Name>状态（中间表）</Name>
            </TextField>
            <AmountField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>TDn2ZpMspN</Id>
              <DefValue>0</DefValue>
              <Name>应开票含税总额</Name>
              <Key>yd_invoice_total_amt</Key>
            </AmountField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>J5uKXWruXZ</Id>
              <Key>yd_platform</Key>
              <DefValue>猫超</DefValue>
              <Name>平台</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>IQgMANpEhY</Id>
              <Key>yd_shop</Key>
              <Name>店铺</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>fL6EYOB351</Id>
              <Key>yd_middle_bill_no</Key>
              <Name>中间表单号</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>FomsYrDbSA</Id>
              <Key>yd_target_bill_no</Key>
              <Name>销售出库单/退货单</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Nlmm7US0e5</Id>
              <Key>yd_push_error_msg</Key>
              <Name>下推销售出库单异常原因</Name>
            </TextField>
            <BigIntField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>O0YkyFapvX</Id>
              <Key>yd_fid</Key>
              <DefValue>0</DefValue>
              <Name>FID</Name>
            </BigIntField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>ReportFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1748399053456</Version>
      <ParentId>1b760aa100003bac</ParentId>
      <MasterId></MasterId>
      <Number>yd_tmallmarket_bill_rpt</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>4YE/J/MLE1EZ</Id>
      <InheritPath>1b760aa100003bac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1748399053426</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
