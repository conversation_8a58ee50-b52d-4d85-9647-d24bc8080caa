<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>223OXO4EAV3D</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>223OXO4EAV3D</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1640932740000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>223OXO4EAV3D</Id>
          <Key>yd_transferrelbill</Key>
          <EntityId>223OXO4EAV3D</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2+/LKXUKAW04</BizappId>
          <IsvSign/>
          <Name>仓库调拨关系表</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <CardListColumnAp action="edit" oid="b1fbc6f800000cac">
                      <FieldForeColor>#212121</FieldForeColor>
                      <Height action="setnull"/>
                      <FieldFontSize>16</FieldFontSize>
                      <Shrink>0</Shrink>
                      <ForeColor>#212121</ForeColor>
                      <ParentId>223OXO=N9NBK</ParentId>
                      <AutoTextWrap>true</AutoTextWrap>
                      <ShowTitle>false</ShowTitle>
                      <Width action="setnull"/>
                      <FontSize>16</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Bottom>5px</Bottom>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>223OXO=N9O/V</Id>
                      <Key>cardlistcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>1</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>223OXO=N9NBK</ParentId>
                      <ListFieldId>billstatus</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>单据状态</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <CardListColumnAp>
                      <FieldForeColor>#999999</FieldForeColor>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>223OXO=QX1J/</Id>
                      <Key>cardlistcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <FieldFontSize>14</FieldFontSize>
                      <Shrink>0</Shrink>
                      <Index>2</Index>
                      <ForeColor>#999999</ForeColor>
                      <ParentId>223OXO=N9NBK</ParentId>
                      <ListFieldId>creator.name</ListFieldId>
                      <ShowTitle>false</ShowTitle>
                      <Name>创建人.姓名</Name>
                      <FontSize>14</FontSize>
                    </CardListColumnAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>223OXO4EAV3D</EntityId>
                    </BillListAp>
                    <CardFlexPanelAp>
                      <Id>223OXO=N9NBK</Id>
                      <AlignItems>stretch</AlignItems>
                      <JustifyContent>flex-start</JustifyContent>
                      <Name>卡片布局容器</Name>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Bottom>1px_solid_#e5e5e5</Bottom>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Top>11px</Top>
                              <Bottom>13px</Bottom>
                              <Right>12</Right>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Key>cardflexpanelap</Key>
                      <Direction>column</Direction>
                      <ParentId>b1fbc6f800000bac</ParentId>
                      <Wrap>false</Wrap>
                    </CardFlexPanelAp>
                    <CardRowPanelAp action="edit" oid="b1fbc6f800000bac">
                      <Direction>column</Direction>
                      <Height action="setnull"/>
                      <AlignItems>stretch</AlignItems>
                      <Shrink>0</Shrink>
                      <JustifyContent>flex-start</JustifyContent>
                      <BackColor>#ffffff</BackColor>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0px_solid_#e5e5e5</Top>
                              <Left>0px_solid_#e5e5e5</Left>
                              <Bottom>0px_solid_#e5e5e5</Bottom>
                              <Right>0px_solid_#e5e5e5</Right>
                            </Border>
                          </Border>
                          <Padding>
                            <Padding>
                              <Left>12px</Left>
                            </Padding>
                          </Padding>
                        </Style>
                      </Style>
                      <Wrap>false</Wrap>
                    </CardRowPanelAp>
                    <MobSortPanelAp action="edit" oid="mobsortpanel">
                      <Name>排序项1</Name>
                    </MobSortPanelAp>
                    <MobFilterPanelAp action="edit" oid="mobfilterpanel">
                      <Index>1</Index>
                      <Name>过滤项1</Name>
                    </MobFilterPanelAp>
                    <MobFilterPanelAp>
                      <Id>223OXO=N9O/U</Id>
                      <Key>mobfilterpanelap1</Key>
                      <Index>2</Index>
                      <ParentId>mobfiltersort</ParentId>
                      <Name>过滤项2</Name>
                    </MobFilterPanelAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>223OXO4EAV3D</Id>
              <BackColor>#E2E7EF</BackColor>
              <Name>仓库调拨关系表</Name>
              <Key>yd_transferrelbill</Key>
              <MobMeta>
                <FormMetadata>
                  <Items>
                    <LayoutFlexAp>
                      <Id>223OXO=N9O/R</Id>
                      <Key>layoutflexap</Key>
                      <Grow>0</Grow>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                    </LayoutFlexAp>
                    <MToolbarAp action="edit" oid="PbiHIUwCY6">
                      <Grow>0</Grow>
                      <Shrink>0</Shrink>
                      <TextStyle>0</TextStyle>
                    </MToolbarAp>
                    <LayoutFlexAp>
                      <Id>223OXO=N9NBH</Id>
                      <Key>layoutflexap1</Key>
                      <Grow>0</Grow>
                      <Index>1</Index>
                      <ParentId>00305e8b000006ac</ParentId>
                      <Name>字段布局容器</Name>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Top>10px</Top>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </LayoutFlexAp>
                    <FieldAp>
                      <Id>223OXO=N9NBI</Id>
                      <FieldId>GnxpBjqGJ5</FieldId>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>审核人</Name>
                      <Key>auditor</Key>
                      <ParentId>223OXO=N9NBH</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>223OXO=N9O/T</Id>
                      <FieldId>mGJPp5Qux7</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>创建人</Name>
                      <Key>creator</Key>
                      <ParentId>223OXO=N9NBH</ParentId>
                    </FieldAp>
                    <FieldAp action="edit" oid="l0yky0Xm63">
                      <MobFieldPattern>1</MobFieldPattern>
                      <ParentId>223OXO=N9O/R</ParentId>
                    </FieldAp>
                    <FieldAp>
                      <Id>223OXO=N9O/S</Id>
                      <FieldId>6q69iznvHX</FieldId>
                      <Index>1</Index>
                      <MobFieldPattern>1</MobFieldPattern>
                      <Name>单据状态</Name>
                      <Key>billstatus</Key>
                      <ParentId>223OXO=N9O/R</ParentId>
                    </FieldAp>
                    <MBarItemAp action="edit" oid="5HPUfXVVek">
                      <Radius>4px</Radius>
                      <ForeColor>#212121</ForeColor>
                      <BackColor>#ffffff</BackColor>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Border>
                            <Border>
                              <Top>0.5px_solid_#cccccc</Top>
                              <Left>0.5px_solid_#cccccc</Left>
                              <Bottom>0.5px_solid_#cccccc</Bottom>
                              <Right>0.5px_solid_#cccccc</Right>
                            </Border>
                          </Border>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                    <MBarItemAp>
                      <OperationKey>submit</OperationKey>
                      <Id>223OXO=N9NBJ</Id>
                      <Radius>4px</Radius>
                      <Key>bar_submit</Key>
                      <Index>1</Index>
                      <ParentId>PbiHIUwCY6</ParentId>
                      <Name>提交</Name>
                      <FontSize>14</FontSize>
                      <Style>
                        <Style>
                          <Margin>
                            <Margin>
                              <Left>6px</Left>
                              <Right>6px</Right>
                            </Margin>
                          </Margin>
                        </Style>
                      </Style>
                    </MBarItemAp>
                  </Items>
                </FormMetadata>
              </MobMeta>
              <ListMeta>
                <FormMetadata>
                  <Name>仓库调拨关系表</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Name>仓库调拨关系表</Name>
                    </FormAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <Name>仓库调拨关系列表</Name>
                      <EntityId>223OXO4EAV3D</EntityId>
                    </BillListAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>223S11NEY=+T</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_outwarehouse.name</ListFieldId>
                      <Name>调出仓库</Name>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>223S15LVRXZL</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_inwarehouse.name</ListFieldId>
                      <Name>调入仓库</Name>
                    </ListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <AdvConSummaryPanelAp>
              <Id>223OXO=CDEF7</Id>
              <Name>高级面板摘要容器</Name>
              <Key>advconsummarypanelap</Key>
              <ParentId>223OXO=CDDRZ</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>223OXO=CDDS+</Id>
              <Key>advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>223OXO=CDDRZ</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>223OXO=G/UMQ</Id>
              <AlignItems>stretch</AlignItems>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>advconchildcanelap</Key>
              <Direction>column</Direction>
              <ParentId>223OXO=CDDRZ</ParentId>
              <Wrap>false</Wrap>
            </AdvConChildPanelAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>223OXO=G/TZG</Id>
              <Key>tb_new</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>223OXO=CDDS+</ParentId>
              <Name>增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>223OXO=G/UMR</Id>
              <Key>tb_del</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>223OXO=CDDS+</ParentId>
              <Name>删行</Name>
            </AdvConBarItemAp>
            <EntryFieldAp>
              <Id>223OXO=G/UMS</Id>
              <FieldId>223OXO=G/UMS</FieldId>
              <Name>修改人</Name>
              <Hidden>true</Hidden>
              <Key>modifierfield</Key>
              <ParentId>223OXO=G/TZH</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>223OXO=G/TZI</Id>
              <FieldId>223OXO=G/TZI</FieldId>
              <Index>1</Index>
              <Name>修改时间</Name>
              <Hidden>true</Hidden>
              <Key>modifydatefield</Key>
              <ParentId>223OXO=G/TZH</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>FHEyaUlc74</Id>
              <FieldId>FHEyaUlc74</FieldId>
              <Index>2</Index>
              <Name>调出仓库</Name>
              <Key>yd_outwarehouse</Key>
              <ParentId>223OXO=G/TZH</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>pEZlD8VScG</Id>
              <FieldId>pEZlD8VScG</FieldId>
              <Index>3</Index>
              <Name>调入仓库</Name>
              <Key>yd_inwarehouse</Key>
              <ParentId>223OXO=G/TZH</ParentId>
            </EntryFieldAp>
            <EntryAp>
              <EntryId>223OXO=G/TZH</EntryId>
              <ShowSeq>true</ShowSeq>
              <Id>223OXO=G/TZH</Id>
              <Name>单据体</Name>
              <ShowSelChexkbox>true</ShowSelChexkbox>
              <PageType/>
              <Key>entryentity</Key>
              <ParentId>223OXO=G/UMQ</ParentId>
            </EntryAp>
            <BarItemAp action="edit" oid="LTouS4Ri1Q">
              <OperationKey>audit</OperationKey>
              <Name>审核</Name>
            </BarItemAp>
            <FlexPanelAp action="edit" oid="sb5ReliwR1">
              <BackColor/>
              <Style>
                <Style>
                  <Border>
                    <Border>
                      <Top>10px_solid_#E2E7EF</Top>
                      <Left>10px_solid_#E2E7EF</Left>
                      <Bottom>10px_solid_#E2E7EF</Bottom>
                      <Right>10px_solid_#E2E7EF</Right>
                    </Border>
                  </Border>
                </Style>
              </Style>
            </FlexPanelAp>
            <FieldsetPanelAp action="edit" oid="mqK0FWAH2I">
              <BackColor>#ffffff</BackColor>
            </FieldsetPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>223OXO=CDDRZ</Id>
              <Key>advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>2</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <BackColor>#ffffff</BackColor>
              <Name>调拨关系分录</Name>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AdvConAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Hidden>true</Hidden>
              <Index>3</Index>
              <BackColor>#ffffff</BackColor>
              <Style>
                <Style>
                  <Margin>
                    <Margin>
                      <Top>10px</Top>
                    </Margin>
                  </Margin>
                </Style>
              </Style>
            </AttachmentPanelAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1305746860345920512</ModifierId>
      <EntityId>223OXO4EAV3D</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1640932740483</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_transferrelbill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>223OXO4EAV3D</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <DataXml>
        <EntityMetadata>
          <Id>223OXO4EAV3D</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2+/LKXUKAW04</BizappId>
          <IsvSign/>
          <Name>仓库调拨关系表</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_transferrelbill</TableName>
              <Operations>
                <Operation action="edit" oid="c91d5125000033ac">
                  <Parameter>
                    <SaveParameter>
                      <StatusFieldId>6q69iznvHX</StatusFieldId>
                    </SaveParameter>
                  </Parameter>
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Description>调出调入仓库唯一键校验</Description>
                      <Id>24BEJ7CL/MI2</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_outwarehouse.id</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_inwarehouse.id</Id>
                        </FieldId>
                      </Fields>
                      <Enabled>true</Enabled>
                      <IsCheckEmptyValue>true</IsCheckEmptyValue>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation action="edit" oid="c91d5125000034ac">
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Description>调出仓库唯一性校验</Description>
                      <Id>24F8AW=S9YJM</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_outwarehouse.id</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_inwarehouse.id</Id>
                        </FieldId>
                      </Fields>
                      <Enabled>true</Enabled>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation action="edit" oid="c91d5125000035ac">
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Description>调出仓库唯一性校验</Description>
                      <Id>24F8E17IQE9S</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_outwarehouse.id</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_inwarehouse.id</Id>
                        </FieldId>
                      </Fields>
                      <Enabled>true</Enabled>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>223OXO=G/TZH</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>223OXO=G/UMT</Id>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>223OXO=G/TZH</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>223OXO=G/TZJ</Id>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>223OXO4EAV3D</Id>
              <Key>yd_transferrelbill</Key>
              <Name>仓库调拨关系表</Name>
            </BillEntity>
            <EntryEntity>
              <TableName>tk_yd_transferrel_entry</TableName>
              <Name>单据体</Name>
              <Id>223OXO=G/TZH</Id>
              <Key>entryentity</Key>
            </EntryEntity>
            <ModifierField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>223OXO=G/UMS</Id>
              <LableHyperlink>false</LableHyperlink>
              <Name>修改人</Name>
              <FieldName>fmodifierfield</FieldName>
              <Key>modifierfield</Key>
              <ParentId>223OXO=G/TZH</ParentId>
              <BaseEntityId>68bde9ca00000eac</BaseEntityId>
            </ModifierField>
            <ModifyDateField>
              <FieldName>fmodifydatefield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>223OXO=G/TZI</Id>
              <Key>modifydatefield</Key>
              <ParentId>223OXO=G/TZH</ParentId>
              <Name>修改日期</Name>
            </ModifyDateField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>FHEyaUlc74</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>调出仓库</Name>
              <FieldName>fk_yd_outwarehouseid</FieldName>
              <Key>yd_outwarehouse</Key>
              <MustInput>true</MustInput>
              <RefLayout/>
              <ParentId>223OXO=G/TZH</ParentId>
              <BaseEntityId>14G1K2YGTJGQ</BaseEntityId>
            </BasedataField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>pEZlD8VScG</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>调入仓库</Name>
              <FieldName>fk_yd_inwarehouseid</FieldName>
              <Key>yd_inwarehouse</Key>
              <MustInput>true</MustInput>
              <RefLayout/>
              <ParentId>223OXO=G/TZH</ParentId>
              <BaseEntityId>14G1K2YGTJGQ</BaseEntityId>
            </BasedataField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv></Isv>
      <Version>1640932740577</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_transferrelbill</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>223OXO4EAV3D</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1640932740483</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>/KPQHMXQD66W</BizunitId>
</DeployMetadata>
