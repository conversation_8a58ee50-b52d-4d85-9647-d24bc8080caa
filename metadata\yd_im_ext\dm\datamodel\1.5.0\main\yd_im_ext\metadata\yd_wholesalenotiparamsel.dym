<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>2EUN3DNDUWCZ</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>2EUN3DNDUWCZ</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1657006193000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>2EUN3DNDUWCZ</Id>
          <Key>yd_wholesalenotiparamsel</Key>
          <EntityId>2EUN3DNDUWCZ</EntityId>
          <ParentId>6fb46130000000ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>批发通知单参数选择界面</Name>
          <InheritPath>6fb46130000000ac</InheritPath>
          <Items>
            <FormAp action="edit" oid="db6d1d5500007bac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>批发通知单参数选择插件</Description>
                  <ClassName>kd.bos.tcbj.im.outbill.plugin.WholeSaleNoticeParamsPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>2EUN3DNDUWCZ</Id>
              <Height>300px</Height>
              <Name>批发通知单参数选择界面</Name>
              <Key>yd_wholesalenotiparamsel</Key>
              <Width>500px</Width>
            </FormAp>
            <FlexPanelAp action="edit" oid="pvZxev7ECP">
              <AlignItems>center</AlignItems>
              <JustifyContent>center</JustifyContent>
            </FlexPanelAp>
            <ButtonAp action="edit" oid="3Y1T8YDayb">
              <OperationKey>ok</OperationKey>
            </ButtonAp>
            <FieldAp>
              <Id>pjdXvM1hlX</Id>
              <FieldId>pjdXvM1hlX</FieldId>
              <Name>查询日期</Name>
              <Key>yd_searchdate</Key>
              <ParentId>pvZxev7ECP</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>A4xcL2e576</Id>
              <FieldId>A4xcL2e576</FieldId>
              <Index>1</Index>
              <Name>仓库编码</Name>
              <Key>yd_warehouseno</Key>
              <ParentId>pvZxev7ECP</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>tBKLG735c6</Id>
              <FieldId>tBKLG735c6</FieldId>
              <Index>2</Index>
              <Name>客户编码</Name>
              <Key>yd_customerno</Key>
              <ParentId>pvZxev7ECP</ParentId>
            </FieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1308009068819907584</ModifierId>
      <EntityId>2EUN3DNDUWCZ</EntityId>
      <ModelType>DynamicFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1657006193045</Version>
      <ParentId>6fb46130000000ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_wholesalenotiparamsel</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>2EUN3DNDUWCZ</Id>
      <InheritPath>6fb46130000000ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1657006193000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>2EUN3DNDUWCZ</Id>
          <ParentId>6fb46130000000ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <Name>批发通知单参数选择界面</Name>
          <InheritPath>6fb46130000000ac</InheritPath>
          <Items>
            <MainEntity action="edit" oid="db6d1d5500007cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Operations>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <DoNothingParameter>
                      <AfterOperation>close</AfterOperation>
                    </DoNothingParameter>
                  </Parameter>
                  <Name>确认</Name>
                  <OperationType>donothing</OperationType>
                  <Id>2F=1P2P/HI5=</Id>
                  <SuccessMsg/>
                  <Key>ok</Key>
                </Operation>
              </Operations>
              <Id>2EUN3DNDUWCZ</Id>
              <Key>yd_wholesalenotiparamsel</Key>
              <Name>批发通知单参数选择界面</Name>
            </MainEntity>
            <DateField>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>pjdXvM1hlX</Id>
              <Key>yd_searchdate</Key>
              <Name>查询日期</Name>
              <DefValueDesign>
                <DefValueDesign>
                  <FuncParameter>#CurrentDate#</FuncParameter>
                  <FuncType>getToday</FuncType>
                </DefValueDesign>
              </DefValueDesign>
            </DateField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>A4xcL2e576</Id>
              <Key>yd_warehouseno</Key>
              <Name>仓库编码</Name>
            </TextField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>tBKLG735c6</Id>
              <Key>yd_customerno</Key>
              <Name>客户编码</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>DynamicFormModel</ModelType>
      <Isv>kingdee</Isv>
      <Version>1657006193067</Version>
      <ParentId>6fb46130000000ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_wholesalenotiparamsel</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>2EUN3DNDUWCZ</Id>
      <InheritPath>6fb46130000000ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1657006193045</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
