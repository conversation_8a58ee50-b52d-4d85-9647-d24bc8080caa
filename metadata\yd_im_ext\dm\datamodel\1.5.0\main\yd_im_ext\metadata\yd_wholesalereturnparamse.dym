<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>25DD++YHP1+S</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>25DD++YHP1+S</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1641803009000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>25DD++YHP1+S</Id>
          <Key>yd_wholesalereturnparamse</Key>
          <EntityId>25DD++YHP1+S</EntityId>
          <ParentId>6fb46130000000ac</ParentId>
          <BizappId>2+/LKXUKAW04</BizappId>
          <Name>批发退货通知单参数界面</Name>
          <InheritPath>6fb46130000000ac</InheritPath>
          <Items>
            <FormAp action="edit" oid="db6d1d5500007bac">
              <Plugins>
                <Plugin>
                  <FPK/>
                  <Description>批发退货单参数选择界面插件</Description>
                  <ClassName>kd.bos.tcbj.im.outbill.plugin.WholeSaleReturnParamsPlugin</ClassName>
                  <Enabled>true</Enabled>
                  <BizAppId/>
                </Plugin>
              </Plugins>
              <Id>25DD++YHP1+S</Id>
              <Height>300px</Height>
              <Name>批发退货通知单参数界面</Name>
              <Key>yd_wholesalereturnparamse</Key>
              <Width>500px</Width>
            </FormAp>
            <FlexPanelAp action="edit" oid="pvZxev7ECP">
              <AlignItems>center</AlignItems>
              <JustifyContent>center</JustifyContent>
            </FlexPanelAp>
            <ButtonAp action="edit" oid="3Y1T8YDayb">
              <OperationKey>ok</OperationKey>
              <Name>确认</Name>
            </ButtonAp>
            <FieldAp>
              <Id>N5IR9FkAEq</Id>
              <FieldId>N5IR9FkAEq</FieldId>
              <Name>查询日期</Name>
              <Key>yd_searchdate</Key>
              <ParentId>pvZxev7ECP</ParentId>
            </FieldAp>
            <FieldAp>
              <Id>Wrw1ZpiHlY</Id>
              <FieldId>Wrw1ZpiHlY</FieldId>
              <Index>1</Index>
              <Name>客户编码</Name>
              <Key>yd_customerno</Key>
              <ParentId>pvZxev7ECP</ParentId>
            </FieldAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>1305746860345920512</ModifierId>
      <EntityId>25DD++YHP1+S</EntityId>
      <ModelType>DynamicFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1641803009234</Version>
      <ParentId>6fb46130000000ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_wholesalereturnparamse</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>25DD++YHP1+S</Id>
      <InheritPath>6fb46130000000ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <DataXml>
        <EntityMetadata>
          <Id>25DD++YHP1+S</Id>
          <ParentId>6fb46130000000ac</ParentId>
          <BizappId>2+/LKXUKAW04</BizappId>
          <Name>批发退货通知单参数界面</Name>
          <InheritPath>6fb46130000000ac</InheritPath>
          <Items>
            <MainEntity action="edit" oid="db6d1d5500007cac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <Operations>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <DoNothingParameter>
                      <AfterOperation>close</AfterOperation>
                    </DoNothingParameter>
                  </Parameter>
                  <Name>确认</Name>
                  <OperationType>donothing</OperationType>
                  <Id>25E2EXBE1PWR</Id>
                  <SuccessMsg/>
                  <Key>ok</Key>
                </Operation>
              </Operations>
              <Id>25DD++YHP1+S</Id>
              <Key>yd_wholesalereturnparamse</Key>
              <Name>批发退货通知单参数界面</Name>
            </MainEntity>
            <DateField>
              <Features>
                <Features>
                  <Copyable>false</Copyable>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <RegionType>1</RegionType>
              <Id>N5IR9FkAEq</Id>
              <Key>yd_searchdate</Key>
              <MustInput>true</MustInput>
              <Name>查询日期</Name>
              <DefValueDesign>
                <DefValueDesign>
                  <FuncParameter>#CurrentDate#</FuncParameter>
                  <FuncType>getToday</FuncType>
                </DefValueDesign>
              </DefValueDesign>
            </DateField>
            <TextField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>Wrw1ZpiHlY</Id>
              <Key>yd_customerno</Key>
              <Name>客户编码</Name>
            </TextField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>DynamicFormModel</ModelType>
      <Isv>kingdee</Isv>
      <Version>1641803009256</Version>
      <ParentId>6fb46130000000ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_wholesalereturnparamse</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>25DD++YHP1+S</Id>
      <InheritPath>6fb46130000000ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1641803009234</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=Y70GHKBPJV</BizunitId>
</DeployMetadata>
