<?xml version="1.0" encoding="UTF-8"?>

<DeployMetadata>
  <Multilanguage>false</Multilanguage>
  <MasterId>1X53EITF7EAL</MasterId>
  <BizappId>2/6622RVX+25</BizappId>
  <Id>1X53EITF7EAL</Id>
  <DesignMetas>
    <DesignFormMeta>
      <DevType>0</DevType>
      <ModifyDate>1750327663000</ModifyDate>
      <DataXml>
        <FormMetadata>
          <Isv>yd</Isv>
          <Id>1X53EITF7EAL</Id>
          <Key>yd_wldygx</Key>
          <EntityId>1X53EITF7EAL</EntityId>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>物料对应关系</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillFormAp action="edit" oid="00305e8b000005ac">
              <MobListMeta>
                <FormMetadata>
                  <Items>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>1X53EITF7EAL</EntityId>
                    </BillListAp>
                  </Items>
                </FormMetadata>
              </MobListMeta>
              <Id>1X53EITF7EAL</Id>
              <Name>物料对应关系</Name>
              <Key>yd_wldygx</Key>
              <ListMeta>
                <FormMetadata>
                  <Name>物料对应关系</Name>
                  <Items>
                    <FormAp action="edit" oid="b5994054000008ac">
                      <Name>物料对应关系</Name>
                    </FormAp>
                    <BillListAp action="edit" oid="_BillList_">
                      <EntityId>1X53EITF7EAL</EntityId>
                    </BillListAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1XSZYSCN92H4</Id>
                      <Key>yd_listcolumnap2</Key>
                      <Order>NotOrder</Order>
                      <Index>3</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_textfield</ListFieldId>
                      <Name>业务平台对应货品编号</Name>
                      <Width>300px</Width>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1XSZYPV/0K30</Id>
                      <Key>yd_listcolumnap</Key>
                      <Order>NotOrder</Order>
                      <Index>4</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_basedatafield.number</ListFieldId>
                      <Name>苍穹（EAS))物料.编码</Name>
                      <Width>300px</Width>
                    </ListColumnAp>
                    <ListColumnAp>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>1XSZYR+F=0VE</Id>
                      <Key>yd_listcolumnap1</Key>
                      <Order>NotOrder</Order>
                      <Index>5</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_basedatafield.name</ListFieldId>
                      <Name>苍穹（EAS))物料.名称</Name>
                      <Width>300px</Width>
                    </ListColumnAp>
                    <ListColumnAp>
                      <ColumnOrderAndFilter>1</ColumnOrderAndFilter>
                      <SeqColumnType>NotSeq</SeqColumnType>
                      <Id>23UN8UVD4QJ6</Id>
                      <Key>yd_listcolumnap3</Key>
                      <Order>NotOrder</Order>
                      <Index>6</Index>
                      <ParentId>gridview</ParentId>
                      <ListFieldId>yd_pp.name</ListFieldId>
                      <Name>品牌名称</Name>
                    </ListColumnAp>
                  </Items>
                </FormMetadata>
              </ListMeta>
            </BillFormAp>
            <AdvConBarItemAp>
              <OperationKey>newentry</OperationKey>
              <Id>UlBdTGSbjm</Id>
              <Key>yd_yd_advconbaritemap_xz</Key>
              <OperationStyle>0</OperationStyle>
              <ParentId>AaQqNd3uDq</ParentId>
              <Name>新增行</Name>
            </AdvConBarItemAp>
            <AdvConBarItemAp>
              <OperationKey>deleteentry</OperationKey>
              <Id>V3E5G578SO</Id>
              <Key>yd_yd_advconbaritemap_sc</Key>
              <OperationStyle>0</OperationStyle>
              <Index>1</Index>
              <ParentId>AaQqNd3uDq</ParentId>
              <Name>删除行</Name>
            </AdvConBarItemAp>
            <AdvConSummaryPanelAp>
              <Id>20F9p3ueSm</Id>
              <Name>高级面板摘要容器</Name>
              <Key>yd_yd_advconsummarypanelap</Key>
              <ParentId>H0CzuA3N1i</ParentId>
            </AdvConSummaryPanelAp>
            <AdvConToolbarAp>
              <Id>AaQqNd3uDq</Id>
              <Key>yd_yd_advcontoolbarap</Key>
              <Index>1</Index>
              <ParentId>H0CzuA3N1i</ParentId>
              <Name>高级面板工具栏</Name>
            </AdvConToolbarAp>
            <AdvConChildPanelAp>
              <Id>OoPJ15ej3g</Id>
              <Index>2</Index>
              <Name>高级面板子容器</Name>
              <Key>yd_yd_advconchildpanelap</Key>
              <ParentId>H0CzuA3N1i</ParentId>
            </AdvConChildPanelAp>
            <EntryAp>
              <EntryId>hJsD6wmu3f</EntryId>
              <Id>hJsD6wmu3f</Id>
              <Name>单据体</Name>
              <PageType/>
              <Key>yd_entryentity</Key>
              <ParentId>OoPJ15ej3g</ParentId>
            </EntryAp>
            <EntryFieldAp>
              <Id>4kg3syFwAh</Id>
              <FieldId>4kg3syFwAh</FieldId>
              <Name>苍穹（EAS))物料</Name>
              <Key>yd_basedatafield</Key>
              <ParentId>hJsD6wmu3f</ParentId>
              <Width>300px</Width>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>giw4H8yaQO</Id>
              <FieldId>giw4H8yaQO</FieldId>
              <Index>1</Index>
              <Name>业务平台对应货品编号</Name>
              <Key>yd_textfield</Key>
              <ParentId>hJsD6wmu3f</ParentId>
              <Width>300px</Width>
            </EntryFieldAp>
            <EntryFieldAp>
              <Lock>submit,audit,new,edit</Lock>
              <Id>gwTrmPtoW0</Id>
              <FieldId>gwTrmPtoW0</FieldId>
              <Index>2</Index>
              <Name>品牌</Name>
              <Key>yd_pp</Key>
              <ParentId>hJsD6wmu3f</ParentId>
            </EntryFieldAp>
            <EntryFieldAp>
              <Id>RvpsGlIGj6</Id>
              <FieldId>RvpsGlIGj6</FieldId>
              <Index>3</Index>
              <Name>是否启用</Name>
              <Key>yd_isdefault</Key>
              <ParentId>hJsD6wmu3f</ParentId>
            </EntryFieldAp>
            <FieldAp>
              <Id>6HyF4J9ee5</Id>
              <FieldId>6HyF4J9ee5</FieldId>
              <Index>4</Index>
              <Name>平台</Name>
              <Key>yd_combofield_pt</Key>
              <ParentId>mqK0FWAH2I</ParentId>
            </FieldAp>
            <AttachmentPanelAp action="edit" oid="rrz4n5821A">
              <Hidden>true</Hidden>
            </AttachmentPanelAp>
            <AdvConAp>
              <Collapsible>true</Collapsible>
              <Id>H0CzuA3N1i</Id>
              <Key>yd_advconap</Key>
              <Grow>0</Grow>
              <Shrink>0</Shrink>
              <Index>3</Index>
              <ParentId>sb5ReliwR1</ParentId>
              <Name>映射关系</Name>
            </AdvConAp>
          </Items>
        </FormMetadata>
      </DataXml>
      <ModifierId>2218211534222423040</ModifierId>
      <EntityId>1X53EITF7EAL</EntityId>
      <ModelType>BillFormModel</ModelType>
      <Isv>yd</Isv>
      <Version>1750327662996</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_wldygx</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <IsvSign></IsvSign>
      <Id>1X53EITF7EAL</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignFormMeta>
    <DesignEntityMeta>
      <DevType>0</DevType>
      <ModifyDate>1750327663000</ModifyDate>
      <DataXml>
        <EntityMetadata>
          <Id>1X53EITF7EAL</Id>
          <ParentId>00305e8b000006ac</ParentId>
          <BizappId>2/6622RVX+25</BizappId>
          <IsvSign/>
          <Name>物料对应关系</Name>
          <InheritPath>00305e8b000006ac</InheritPath>
          <Items>
            <BillEntity action="edit" oid="00305e8b000007ac">
              <Template action="reset"/>
              <dbRoute>scm</dbRoute>
              <TableName>tk_yd_wldygx</TableName>
              <Operations>
                <Operation action="edit" oid="c91d5125000033ac">
                  <Parameter>
                    <SaveParameter>
                      <StatusFieldId>6q69iznvHX</StatusFieldId>
                    </SaveParameter>
                  </Parameter>
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <CustomPromp/>
                      <Description>货品编号不能重复</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>1X9/1H0/IZN0</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_combofield_pt</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_basedatafield.number</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_textfield</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_isdefault</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation action="edit" oid="c91d5125000034ac">
                  <Validations>
                    <GrpfieldsuniqueValidation>
                      <Description>货品编号不能重复</Description>
                      <Enabled>true</Enabled>
                      <RuleType>GroupFieldUnique</RuleType>
                      <Id>25GSPU=Q3R/9</Id>
                      <Fields>
                        <FieldId>
                          <Id>yd_combofield_pt</Id>
                        </FieldId>
                        <FieldId>
                          <Id>yd_textfield</Id>
                        </FieldId>
                      </Fields>
                    </GrpfieldsuniqueValidation>
                  </Validations>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <NewEntryParameter>
                      <EntryId>hJsD6wmu3f</EntryId>
                    </NewEntryParameter>
                  </Parameter>
                  <Name>新增分录</Name>
                  <OperationType>newentry</OperationType>
                  <Id>1X5RPF42D25S</Id>
                  <SuccessMsg/>
                  <Key>newentry</Key>
                </Operation>
                <Operation>
                  <ConfirmMsg/>
                  <Parameter>
                    <EntryOpParameter>
                      <EntryId>hJsD6wmu3f</EntryId>
                    </EntryOpParameter>
                  </Parameter>
                  <Name>删除分录</Name>
                  <OperationType>deleteentry</OperationType>
                  <Id>1X5RQ85M=YZZ</Id>
                  <SuccessMsg/>
                  <Key>deleteentry</Key>
                </Operation>
              </Operations>
              <Id>1X53EITF7EAL</Id>
              <Key>yd_wldygx</Key>
              <Name>物料对应关系</Name>
            </BillEntity>
            <ComboField>
              <FieldName>fk_yd_combofield_pt</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>6HyF4J9ee5</Id>
              <Key>yd_combofield_pt</Key>
              <MustInput>true</MustInput>
              <Name>平台</Name>
              <Items>
                <ComboItem>
                  <Caption>E3</Caption>
                  <Value>1</Value>
                </ComboItem>
                <ComboItem>
                  <Caption>旺店通</Caption>
                  <Value>2</Value>
                  <Seq>1</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>吉客云</Caption>
                  <Value>3</Value>
                  <Seq>2</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>万里牛</Caption>
                  <Value>4</Value>
                  <Seq>3</Seq>
                </ComboItem>
                <ComboItem>
                  <Caption>新E3</Caption>
                  <Value>5</Value>
                  <Seq>4</Seq>
                </ComboItem>
              </Items>
            </ComboField>
            <EntryEntity>
              <Rules>
                <BizRule>
                  <TrueActions>
                    <TakeBasePropertyAction>
                      <RET>253</RET>
                      <ActionType>TakeBaseProAction</ActionType>
                      <Expression>
                        <TakeValueId>
                          <TargetField>yd_pp</TargetField>
                          <Id>23P3NP97N8A1</Id>
                          <SrcField>yd_basedatafield.yd_basedatafield</SrcField>
                        </TakeValueId>
                      </Expression>
                      <FieldName>
                        <TakeValueId>
                          <SrcFieldName>苍穹（EAS))物料.品牌</SrcFieldName>
                          <TargetFieldName>品牌(yd_pp)</TargetFieldName>
                          <Id>23P3NP97N9+B</Id>
                        </TakeValueId>
                      </FieldName>
                      <Description>携带基础资料属性到指定列</Description>
                      <Id>23P3NP97N9+A</Id>
                    </TakeBasePropertyAction>
                  </TrueActions>
                  <Description>物料不为空</Description>
                  <Id>23P3LJ1TCLBT</Id>
                  <PreCondition>yd_basedatafield.id &lt;&gt; null</PreCondition>
                  <PreDescription>物料不为空</PreDescription>
                </BizRule>
              </Rules>
              <TableName>tk_yd_entryentitydywl</TableName>
              <Id>hJsD6wmu3f</Id>
              <Key>yd_entryentity</Key>
              <Name>单据体</Name>
            </EntryEntity>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>4kg3syFwAh</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>苍穹（EAS))物料</Name>
              <FieldName>fk_yd_basedatafield</FieldName>
              <RefProps>
                <RefProp>
                  <Name>yd_basedatafield.id</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_basedatafield.number</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_basedatafield.name</Name>
                </RefProp>
                <RefProp>
                  <Name>yd_basedatafield.status</Name>
                </RefProp>
              </RefProps>
              <Key>yd_basedatafield</Key>
              <MustInput>true</MustInput>
              <DisplayProp>number(name)</DisplayProp>
              <RefLayout/>
              <ParentId>hJsD6wmu3f</ParentId>
              <BaseEntityId>5fa3b2b40000a2ac</BaseEntityId>
            </BasedataField>
            <TextField>
              <FieldName>fk_yd_textfield</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>giw4H8yaQO</Id>
              <Key>yd_textfield</Key>
              <MustInput>true</MustInput>
              <ParentId>hJsD6wmu3f</ParentId>
              <Name>业务平台对应货品编号</Name>
            </TextField>
            <BasedataField>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>gwTrmPtoW0</Id>
              <LableHyperlink>false</LableHyperlink>
              <DefValue/>
              <Name>品牌</Name>
              <FieldName>fk_yd_pp</FieldName>
              <Key>yd_pp</Key>
              <RefLayout/>
              <ParentId>hJsD6wmu3f</ParentId>
              <BaseEntityId>2+/KBC98TKNE</BaseEntityId>
            </BasedataField>
            <CheckBoxField>
              <FieldName>fk_yd_default</FieldName>
              <Features>
                <Features>
                  <Ver>1</Ver>
                </Features>
              </Features>
              <Id>RvpsGlIGj6</Id>
              <Key>yd_isdefault</Key>
              <ParentId>hJsD6wmu3f</ParentId>
              <Name>是否启用</Name>
            </CheckBoxField>
          </Items>
        </EntityMetadata>
      </DataXml>
      <ModelType>BillFormModel</ModelType>
      <Isv></Isv>
      <Version>1750327663028</Version>
      <ParentId>00305e8b000006ac</ParentId>
      <MasterId></MasterId>
      <Number>yd_wldygx</Number>
      <BizappId>2/6622RVX+25</BizappId>
      <Id>1X53EITF7EAL</Id>
      <InheritPath>00305e8b000006ac</InheritPath>
    </DesignEntityMeta>
  </DesignMetas>
  <Version>1750327662996</Version>
  <BOSVersion>1.0</BOSVersion>
  <BizunitId>=9Q874A5R7T</BizunitId>
</DeployMetadata>
