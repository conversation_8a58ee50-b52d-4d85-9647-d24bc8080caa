DELETE from t_bas_billtype where fid = 1204336694934195200;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON><PERSON><PERSON>DERULEID,FC<PERSON><PERSON>OR<PERSON>,FCREATETIME,<PERSON>ODIFIERID,<PERSON>ODIFYTIME,FC<PERSON><PERSON><PERSON><PERSON>INTCOUNT,FMA<PERSON>PRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (1204336694934195200,'im_mdc_backdifshare_BT_S','im_mdc_backdifshare','1',' ',1158703565431226368,{ts '2021-07-20 00:00:00' },1158703565431226368,{ts '2021-08-13 00:00:00' },'0',1,'1','0',' ','0',null,'1P+CID8Q/9JB',null,'C','1',' ',1204336694934195200);
DELETE from t_bas_billtype_l where fid = 1204336694934195200;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('1RXV3WHROX6Z',1204336694934195200,'zh_CN','标准差异分摊',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('1RXV3WHROX7+',1204336694934195200,'zh_TW','標準差異分攤',' ');
