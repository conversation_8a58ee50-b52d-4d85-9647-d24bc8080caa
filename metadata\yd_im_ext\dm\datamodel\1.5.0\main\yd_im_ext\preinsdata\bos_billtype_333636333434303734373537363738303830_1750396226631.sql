DELETE from t_bas_billtype where fid = 366344074757678080;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON>LL<PERSON>DERULEID,FCREATORID,FCREATETIME,FMODIFIERID,FMODIFYTIME,FCON<PERSON>OLPRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (366344074757678080,'im_OtherOutBill_STD_BT_S','im_otheroutbill','1',' ',1,{ts '2018-08-08 18:08:00' },1,{ts '2018-08-08 18:08:00' },'0',1,'1','0',' ','1',null,null,null,'C','1',' ',0);
DELETE from t_bas_billtype_l where fid = 366344074757678080;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('1604W0GM5D2R',366344074757678080,'zh_CN','标准其他出库单',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('1604W0GM5D2S',366344074757678080,'zh_TW','標準其他出庫單',' ');
