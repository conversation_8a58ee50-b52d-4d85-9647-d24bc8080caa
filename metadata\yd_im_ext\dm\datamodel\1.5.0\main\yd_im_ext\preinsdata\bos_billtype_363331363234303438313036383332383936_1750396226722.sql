DELETE from t_bas_billtype where fid = 631624048106832896;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON>LL<PERSON>DERULEID,FC<PERSON>ATORID,FCREATETIME,FMODIFIERID,FMODIFYTIME,FCONTROLPRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (631624048106832896,'im_purreceivebill_STD_BT_S','im_purreceivebill','1',' ',1,{ts '2018-08-08 18:08:00' },1,{ts '2018-08-08 18:08:00' },'0',1,'1','0',' ','1',null,null,null,'C','1',' ',631624048106832896);
DELETE from t_bas_billtype_l where fid = 631624048106832896;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('/ZK07M/X=MTT',631624048106832896,'zh_CN','标准采购收货单',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('/ZK07M/X=MTU',631624048106832896,'zh_TW','標準采購收貨單',' ');
