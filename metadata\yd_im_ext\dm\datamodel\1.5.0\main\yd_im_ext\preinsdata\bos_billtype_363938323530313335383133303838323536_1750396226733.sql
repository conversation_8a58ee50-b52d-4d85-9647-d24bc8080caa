DELETE from t_bas_billtype where fid = 698250135813088256;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON>LL<PERSON>DERULEID,FCREATORID,FCREATETIME,<PERSON>ODIFIERID,<PERSON>ODIFYTIME,FC<PERSON><PERSON><PERSON>PRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (698250135813088256,'im_MaterialReqOutBill_STD_BT_S','im_materialreqoutbill','1',' ',1,{ts '2019-08-22 18:08:00' },1,{ts '2019-08-22 18:08:00' },'0',1,'1','0',' ','0',' ','05NSWAY5DXRH',null,'C','1',' ',698250135813088256);
DELETE from t_bas_billtype_l where fid = 698250135813088256;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('05R8/SDZY4QS',698250135813088256,'zh_CN','标准领料出库单',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('05R8/SDZY4QT',698250135813088256,'zh_TW','標準領料出庫單',' ');
