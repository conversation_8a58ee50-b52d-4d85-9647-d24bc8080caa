DELETE from t_bas_billtype where fid = 721675267608688640;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON>LL<PERSON>DERULEID,FCREATORID,FCREATETIME,FMODIFIERID,<PERSON>ODIF<PERSON>TIME,FC<PERSON><PERSON><PERSON><PERSON>INTCOUNT,FMAX<PERSON>INTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEF<PERSON><PERSON>EPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (721675267608688640,'im_Adjustbill_STD_BT_S','im_adjustbill','1',' ',1,{ts '2019-09-27 10:08:00' },1,{ts '2019-09-27 10:08:00' },'0',1,'1','0',' ','0',' ','08MD5GHP0S3Q',null,'C','1',' ',721675267608688640);
DELETE from t_bas_billtype_l where fid = 721675267608688640;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('08MFXA=5A3V3',721675267608688640,'zh_CN','标准形态转换单',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('08MFXA=5A3V4',721675267608688640,'zh_TW','標準形態轉換單',' ');
