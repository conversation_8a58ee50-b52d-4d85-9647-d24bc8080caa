DELETE from t_bas_billtype where fid = 767306684862616576;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON><PERSON><PERSON>DERULEID,FC<PERSON><PERSON>OR<PERSON>,FCREATETIME,<PERSON>OD<PERSON>IERID,<PERSON>ODIFYTIME,FCON<PERSON><PERSON>PRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (767306684862616576,'im_mdc_mftreturnorder_BT_S_R','im_mdc_mftreturnorder','1',' ',1,{ts '2019-10-28 11:07:47' },1,{ts '2019-10-28 11:07:47' },'0',1,'1','0',' ','1',null,'0WIW186V8206',null,'C','1',' ',767306684862616576);
DELETE from t_bas_billtype_l where fid = 767306684862616576;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0D81YO5OI1NW',767306684862616576,'zh_CN','生产退料单','生产领料退回');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0D81YO5OI1NX',767306684862616576,'zh_TW','生產退料單','生產領料退回');
