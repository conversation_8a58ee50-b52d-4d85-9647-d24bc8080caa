DELETE from t_bas_billtype where fid = 767308634408491008;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON>LL<PERSON>DERULEID,FCREATORID,FCREATETIME,FMODIFIERID,<PERSON>ODIF<PERSON>TIME,FC<PERSON><PERSON><PERSON>PRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (767308634408491008,'im_mdc_mftfeedorder_BT_R','im_mdc_mftfeedorder','1',' ',1,{ts '2019-10-28 11:07:47' },1,{ts '2019-10-28 11:07:47' },'0',1,'1','0',' ','1',null,'0WIW5270B02W',null,'C','1',' ',767308634408491008);
DELETE from t_bas_billtype_l where fid = 767308634408491008;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0D82=W7NW2BB',767308634408491008,'zh_CN','生产补料单','生产补料单');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0D82=W7NW2BA',767308634408491008,'zh_TW','生產補料單','生產補料單');
