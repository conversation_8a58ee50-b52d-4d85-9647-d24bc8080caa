DELETE from t_bas_billtype where fid = 910175119043905536;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON><PERSON><PERSON>DERULEID,FCREATORID,FCREATETIME,FMODIFIERID,<PERSON><PERSON><PERSON><PERSON>TIME,FC<PERSON><PERSON><PERSON>PRINTCOUNT,FMA<PERSON><PERSON>INTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (910175119043905536,'im_disassemblebill_BT_S','im_disassemblebill','1',' ',454363406997129287,{ts '2020-06-09 19:15:11' },13466739,{ts '2020-07-21 10:11:10' },'0',1,'1','0',' ','0',null,'0URW0Z0=HSQL',null,'C','1',' ',910175119043905536);
DELETE from t_bas_billtype_l where fid = 910175119043905536;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0USJ=HV1+FYX',910175119043905536,'zh_CN','标准拆卸单',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0USJ=HV1+FYY',910175119043905536,'zh_TW','標準拆卸單',' ');
