DELETE from t_bas_billtype where fid = 920039909711047680;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON><PERSON><PERSON>DERULEID,FCREATORID,FCREATETIME,FMODIFIERID,FMODIFYTIME,FC<PERSON><PERSON><PERSON>PRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (920039909711047680,'im_mdc_mftreturnbill_BT_S','im_mdc_mftreturnbill','1',' ',1,{ts '2020-06-23 11:07:47' },1,{ts '2020-06-23 11:07:47' },'0',1,'1','0',' ','1',null,'0WJ9JPR+9QZR',null,'C','1',' ',920039909711047680);
DELETE from t_bas_billtype_l where fid = 920039909711047680;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0W+TH1T+JNCW',920039909711047680,'zh_CN','完工退库单','完工退库单');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0W+TH1T+JNCX',920039909711047680,'zh_TW','完工退庫單','完工退庫單');
