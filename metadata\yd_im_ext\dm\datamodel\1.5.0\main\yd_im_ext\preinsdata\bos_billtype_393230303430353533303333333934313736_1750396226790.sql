DELETE from t_bas_billtype where fid = 920040553033394176;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON><PERSON><PERSON>DERULEID,FCREATORID,FCREATETIME,FMODIFIERID,<PERSON>ODIF<PERSON>TIME,FC<PERSON><PERSON><PERSON>PRINTCOUNT,FMAX<PERSON>INTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (920040553033394176,'im_mdc_mftmanuinbill_BT_S','im_mdc_mftmanuinbill','1',' ',1,{ts '2020-06-23 11:07:47' },1,{ts '2020-06-23 11:07:47' },'0',1,'1','0',' ','1',null,'0VR3EBVIECSV',null,'C','1',' ',920040553033394176);
DELETE from t_bas_billtype_l where fid = 920040553033394176;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0W+TLRJ9TR5Y',920040553033394176,'zh_CN','完工入库单','完工入库单');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('0W+TLRJ9TR5Z',920040553033394176,'zh_TW','完工入庫單','完工入庫單');
