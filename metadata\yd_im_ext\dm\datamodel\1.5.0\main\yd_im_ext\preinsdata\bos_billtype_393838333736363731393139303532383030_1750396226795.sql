DELETE from t_bas_billtype where fid = 988376671919052800;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON><PERSON><PERSON>DERULEID,FCREATORID,FCREATETIME,<PERSON>OD<PERSON>IERID,<PERSON>ODIFYTIME,FC<PERSON><PERSON><PERSON>PRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (988376671919052800,'im_mdc_omproorder_BT_S','im_mdc_omproorder','1',' ',1,{ts '2020-09-25 00:00:00' },1,{ts '2020-09-25 00:00:00' },'0',1,'1','0',' ','0',null,'12CVY2NQ/PKB',null,'C','1',' ',988376671919052800);
DELETE from t_bas_billtype_l where fid = 988376671919052800;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('12D6XWMS6+OA',988376671919052800,'zh_CN','简单委外领料单',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('12D6XWMS6+OB',988376671919052800,'zh_TW','简单委外領料單',' ');
