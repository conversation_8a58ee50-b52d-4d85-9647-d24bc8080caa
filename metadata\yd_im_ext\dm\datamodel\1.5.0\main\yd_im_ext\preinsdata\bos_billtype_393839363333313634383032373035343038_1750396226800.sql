DELETE from t_bas_billtype where fid = 989633164802705408;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON><PERSON><PERSON>DERULEID,FC<PERSON>ATORID,FCREATETIME,<PERSON>ODIFIERID,<PERSON>ODIFYTIME,FCON<PERSON><PERSON>PRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (989633164802705408,'im_mdc_omreturnorder_BT_S','im_mdc_omreturnorder','1',' ',1,{ts '2020-09-25 00:00:00' },1,{ts '2020-09-25 00:00:00' },'0',1,'1','0',' ','0',null,'12D7+MWJ0U/2',null,'C','1',' ',989633164802705408);
DELETE from t_bas_billtype_l where fid = 989633164802705408;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('12J7OY9TXEG1',989633164802705408,'zh_CN','简单委外退料单',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('12J7OY9TXEG2',989633164802705408,'zh_TW','简单委外退料單',' ');
