DELETE from t_bas_billtype where fid = 989633630873884672;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON><PERSON><PERSON>DERULEID,FC<PERSON><PERSON>ORID,FCREATETIME,<PERSON>OD<PERSON>IERID,<PERSON>ODIFYTIME,FCON<PERSON><PERSON>PRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (989633630873884672,'im_mdc_omfeedorder_BT_S','im_mdc_omfeedorder','1',' ',1,{ts '2020-09-25 00:00:00' },1,{ts '2020-09-25 00:00:00' },'0',1,'1','0',' ','0',null,'12J4M6/JN3K5',null,'C','1',' ',989633630873884672);
DELETE from t_bas_billtype_l where fid = 989633630873884672;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('12J7SAQ88FO2',989633630873884672,'zh_CN','简单委外补料单',' ');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('12J7SAQ88FO3',989633630873884672,'zh_TW','简单委外補料單',' ');
