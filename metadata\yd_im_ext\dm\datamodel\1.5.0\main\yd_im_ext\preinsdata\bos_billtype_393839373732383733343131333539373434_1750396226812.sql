DELETE from t_bas_billtype where fid = 989772873411359744;
 INSERT INTO t_bas_billtype(FID,FNUMBER,FBILLFORMID,FISDEFAULT,<PERSON>LL<PERSON>DERULEID,FCREATORID,FCREATETIME,<PERSON>ODIFIERID,<PERSON>ODIFYTIME,FCON<PERSON><PERSON>PRINTCOUNT,FMAXPRINTCOUNT,FPRINTAFTERAUDIT,FDOCUMENTSTATUS,FDEFPRINTTEMPLATE,FISSYSPRESET,FPARASETTINGXML,FLAYOUTSOLUTION,FDEFWNREPORTTEMPLATE,FSTATUS,FENABLE,FBILLINITSETTING,FMASTERID) VALUES (989772873411359744,'im_mdc_ominbill_BT_S','im_mdc_ominbill','1',' ',1,{ts '2020-06-23 00:00:00' },1467255694653663232,{ts '2022-08-31 00:00:00' },'0',1,'1','0',' ','0',null,'129RQZ42Z6+9',null,'C','1',' ',989772873411359744);
DELETE from t_bas_billtype_l where fid = 989772873411359744;
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('12JWT+DE8AD4',989772873411359744,'zh_CN','标准委外入库单','标准委外入库单');
INSERT INTO t_bas_billtype_l(FPKID,FID,FLOCALEID,FNAME,FDESCRIPTION) VALUES ('12JWT+DE8AD5',989772873411359744,'zh_TW','標準委外入庫單','標準委外入庫單');
