 DELETE from t_cr_coderule where fid = '08MHOUOEE2NL';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, <PERSON><PERSON><PERSON>LE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMODIFIERID, <PERSON><PERSON><PERSON><PERSON>TI<PERSON>, FMASTERID, FISUPDATERECOVER, FISNONBREAK, F<PERSON><PERSON><PERSON>KCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '08MHOUOEE2NL','xtzh001','im_adjustbill','-','1',' ',' ',' ','1','C', 1,{ts'2019-09-27 10:08:00'},13466739,{ts'2020-11-09 19:52:57'},'08MHOUOEE2NL','1','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '08MHOUOEE2NL';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('08MHOUP91HN8','08MHOUOEE2NL','zh_CN','形态转换单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('08MHOUP91HN7','08MHOUOEE2NL','zh_TW','形態轉換單編碼規則');
DELETE from t_cr_coderuleentry where fid = '08MHOUOEE2NL';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DX9FX1KX+P','08MHOUOEE2NL',0,'1',' ',' ','XTZH',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DX9FX1KX+Q','08MHOUOEE2NL',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DX9FX1KX+R','08MHOUOEE2NL',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
