 DELETE from t_cr_coderule where fid = '0APYAVFC5DGP';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, <PERSON><PERSON>ABLE, FSTATUS, FCRE<PERSON><PERSON><PERSON>, <PERSON><PERSON>AT<PERSON>IME, FMODIFIERID, <PERSON><PERSON><PERSON><PERSON>TI<PERSON>, FMASTERID, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '0APYAVFC5DGP','zzd001','im_assembbill','-','1',' ',' ',' ','1','C', 1,{ts'2019-10-28 10:00:33'},13466739,{ts'2020-11-09 19:53:50'},'0APYAVFC5DGP','1','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '0APYAVFC5DGP';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('0APYAVFFV1VU','0APYAVFC5DGP','zh_CN','组装单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('0APYAVFFV1VT','0APYAVFC5DGP','zh_TW','組裝單編碼規則');
DELETE from t_cr_coderuleentry where fid = '0APYAVFC5DGP';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DXBR1AE7MB','0APYAVFC5DGP',0,'1',' ',' ','ZZD',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DXBR1AE7MC','0APYAVFC5DGP',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DXBR1AE7MD','0APYAVFC5DGP',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
