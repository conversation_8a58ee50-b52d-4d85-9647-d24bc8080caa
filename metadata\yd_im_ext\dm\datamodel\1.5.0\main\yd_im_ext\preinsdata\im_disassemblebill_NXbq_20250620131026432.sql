 DELETE from t_cr_coderule where fid = '0YEPTCSH8F+Z';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, <PERSON>EXAM<PERSON><PERSON><PERSON>G<PERSON>, FENABLE, FSTATUS, FCREAT<PERSON>ID, FCREATETIME, FMODIFIERID, FMODIF<PERSON>TIME, FMAS<PERSON><PERSON>D, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '0YEPTCSH8F+Z','cxd001','im_disassemblebill','-','1',' ',' ',' ','1','C', 13466739,{ts'2020-07-20 17:52:57'},13466739,{ts'2020-11-09 19:54:57'},'0YEPTCSH8F+Z','1','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '0YEPTCSH8F+Z';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('0YEPTCSH8BMP','0YEPTCSH8F+Z','zh_CN','拆卸单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('0YEPTCSH8BMO','0YEPTCSH8F+Z','zh_TW','拆卸單編碼規則');
DELETE from t_cr_coderuleentry where fid = '0YEPTCSH8F+Z';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DXFUFFIK7K','0YEPTCSH8F+Z',0,'1',' ',' ','CXD',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DXFUFFIK7L','0YEPTCSH8F+Z',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DXFUFFIK7M','0YEPTCSH8F+Z',2,'16','1',' ',' ',' ',8,1,1,' ','1','1','1','0','-','1');
