 DELETE from t_cr_coderule where fid = '/S9IBMTRY6=T';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, FENABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON>ID, FCREATETIME, FMODIFIERID, <PERSON><PERSON><PERSON><PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, F<PERSON>CHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '/S9IBMTRY6=T',' ','im_initbill','-','1','2',' ',' ','1','C', 1,{ts'2018-08-08 18:08:00'},1,{ts'2018-08-08 18:08:00'},'/S9IBMTRY6=T','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '/S9IBMTRY6=T';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9IBMTRY9+V','/S9IBMTRY6=T','zh_CN','初始库存编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9IBMTRY9+U','/S9IBMTRY6=T','zh_TW','初始庫存編碼規則');
DELETE from t_cr_coderuleentry where fid = '/S9IBMTRY6=T';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9IBMU8199C','/S9IBMTRY6=T',0,'1',' ',' ','CSKC',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9IBMU8199D','/S9IBMTRY6=T',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9IBMU8199E','/S9IBMTRY6=T',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
