 DELETE from t_cr_coderule where fid = '/S9IFQ3FY15/';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>GTH, FENABLE, FSTATUS, FCRE<PERSON><PERSON>ID, FCREATETIME, FMODIFIERID, FMODIF<PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, F<PERSON>CH<PERSON>KCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '/S9IFQ3FY15/',' ','im_invcountbill','-','1','2',' ',' ','1','C', 1,{ts'2018-08-08 18:08:00'},1,{ts'2018-08-08 18:08:00'},'/S9IFQ3FY15/','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '/S9IFQ3FY15/';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9IFQ3FY+G/','/S9IFQ3FY15/','zh_CN','盘点表编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9IFQ3FY+G+','/S9IFQ3FY15/','zh_TW','盤點表編碼規則');
DELETE from t_cr_coderuleentry where fid = '/S9IFQ3FY15/';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9IFQ4D6724','/S9IFQ3FY15/',0,'1',' ',' ','PDB',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9IFQ4D6725','/S9IFQ3FY15/',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9IFQ4D6726','/S9IFQ3FY15/',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
