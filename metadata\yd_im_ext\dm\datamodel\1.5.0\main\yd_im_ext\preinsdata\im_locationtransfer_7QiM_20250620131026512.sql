 DELETE from t_cr_coderule where fid = '0C+88RINK2PS';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, F<PERSON>ABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMODIFIERID, FMOD<PERSON><PERSON>TIME, FMAS<PERSON>RID, FISUPDATERECOVER, FISNONBREAK, F<PERSON>CHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '0C+88RINK2PS','cwyd001','im_locationtransfer','-','1',' ',' ',' ','1','C', 275188253840125478,{ts'2019-11-11 14:47:54'},13466739,{ts'2020-11-09 19:46:40'},'0C+88<PERSON><PERSON>K2<PERSON>','1','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '0C+88RINK2PS';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('0C+88RIN8CIU','0C+88RINK2PS','zh_CN','仓位移动单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('16DWLYM1BF=7','0C+88RINK2PS','zh_TW','倉位移動單編碼規則');
DELETE from t_cr_coderuleentry where fid = '0C+88RINK2PS';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DWOF+91FRA','0C+88RINK2PS',0,'1',' ',' ','CWYD',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DWOF+91FRB','0C+88RINK2PS',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('16DWOF+91FRC','0C+88RINK2PS',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
