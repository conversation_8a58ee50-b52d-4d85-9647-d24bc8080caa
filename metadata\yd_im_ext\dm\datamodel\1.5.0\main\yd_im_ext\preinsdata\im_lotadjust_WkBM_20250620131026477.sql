 DELETE from t_cr_coderule where fid = '/S9I96E6U5Y1';
 INSERT INTO t_cr_coderule(FID, FNUMBER, <PERSON>ZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>G<PERSON>, <PERSON><PERSON><PERSON>LE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMOD<PERSON><PERSON><PERSON>D, <PERSON><PERSON><PERSON><PERSON>TI<PERSON>, FMASTERID, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON><PERSON>, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '/S9I96E6U5Y1',' ','im_lotadjust','-','1','2',' ',' ','1','C', 1,{ts'2018-08-08 18:08:00'},1,{ts'2018-08-08 18:08:00'},'/S9I96E6U5Y1','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '/S9I96E6U5Y1';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9I96E6U381','/S9I96E6U5Y1','zh_CN','批号调整编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9I96E6U380','/S9I96E6U5Y1','zh_TW','批號調整編碼規則');
DELETE from t_cr_coderuleentry where fid = '/S9I96E6U5Y1';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9I96FW2ND=','/S9I96E6U5Y1',0,'1',' ',' ','PHTZ',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9I96FW2NDA','/S9I96E6U5Y1',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9I96FW2NDB','/S9I96E6U5Y1',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
