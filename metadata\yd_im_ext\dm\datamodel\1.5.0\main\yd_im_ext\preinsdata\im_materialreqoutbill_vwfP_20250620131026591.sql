 DELETE from t_cr_coderule where fid = '0A37M=UOG786';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAMPL<PERSON><PERSON>GTH, FENABLE, FSTATUS, FCREAT<PERSON>ID, FCREATETIME, FMODIFIERID, FMODIF<PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '0A37M=UOG786',' ','im_materialreqoutbill','-','1','2',' ',' ','1','C', 1,{ts'2019-08-22 18:08:00'},1,{ts'2019-08-22 18:08:00'},'0A37M=UOG786','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '0A37M=UOG786';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('0A37M=UOEYE8','0A37M=UOG786','zh_CN','领料出库单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('0A37M=UOEYE7','0A37M=UOG786','zh_TW','領料出庫單編碼規則');
DELETE from t_cr_coderuleentry where fid = '0A37M=UOG786';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('0A38K8B922ME','0A37M=UOG786',0,'1',' ',' ','LLCK',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('0A38K8B922MF','0A37M=UOG786',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('0A38K8B922MG','0A37M=UOG786',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
