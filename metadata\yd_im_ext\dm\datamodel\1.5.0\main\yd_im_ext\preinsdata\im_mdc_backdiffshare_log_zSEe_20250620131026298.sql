 DELETE from t_cr_coderule where fid = '1S2050RO1WT4';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, <PERSON><PERSON>ABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMODIFIERID, <PERSON>OD<PERSON><PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, F<PERSON>CH<PERSON>KCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '1S2050RO1WT4','1S203PS+0=UY','im_mdc_backdiffshare_log','-','1','2',' ',' ','1','C', 0,{ts'2021-07-22 00:00:00'},0,{ts'2021-07-22 00:00:00'},'1S2050RO1WT4','0','0','0','0','0','0','0','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '1S2050RO1WT4';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('1S2050RO4A10','1S2050RO1WT4','zh_CN','领料差异分摊日志');
DELETE from t_cr_coderuleentry where fid = '1S2050RO1WT4';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('1S2050S46ZRP','1S2050RO1WT4',0,'1','1',' ','DCCYFTRZ',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('1S2050S46ZRQ','1S2050RO1WT4',1,'10',' ',' ',' ',' ',8,1,1,' ','1','1','1','0','-','1');
