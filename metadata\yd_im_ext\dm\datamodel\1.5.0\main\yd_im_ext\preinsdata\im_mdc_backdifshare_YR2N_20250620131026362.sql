 DELETE from t_cr_coderule where fid = '1UL3KD673I4M';
 INSERT INTO t_cr_coderule(FID, FNUMBER, <PERSON>ZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>G<PERSON>, <PERSON><PERSON><PERSON><PERSON>, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMOD<PERSON><PERSON><PERSON>D, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FMASTE<PERSON>D, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODI<PERSON><PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '1UL3KD673I4M','1UL3IXW5ZN06','im_mdc_backdifshare','-','1','2',' ',' ','1','C', 0,{ts'2021-08-19 00:00:00'},0,{ts'2021-08-19 00:00:00'},'1UL3KD673I4M','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '1UL3KD673I4M';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('1UL3KD673ITW','1UL3KD673I4M','zh_CN','领料差异分摊');
DELETE from t_cr_coderuleentry where fid = '1UL3KD673I4M';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('1UL3KD6Z3UOQ','1UL3KD673I4M',0,'1','1',' ','PDCYFT',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('1UL3KD6Z3UOR','1UL3KD673I4M',1,'16',' ',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
