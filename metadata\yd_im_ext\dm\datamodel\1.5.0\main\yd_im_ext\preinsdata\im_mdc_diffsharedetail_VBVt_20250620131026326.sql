 DELETE from t_cr_coderule where fid = '1S2/YP+4VA1N';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEX<PERSON><PERSON><PERSON><PERSON>GTH, <PERSON>ENABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMODIFIERID, <PERSON><PERSON><PERSON><PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, <PERSON>ISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODI<PERSON><PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '1S2/YP+4VA1N','1S2/W21G0OVK','im_mdc_diffsharedetail','-','1','2',' ',' ','1','C', 0,{ts'2021-07-22 00:00:00'},0,{ts'2021-07-22 00:00:00'},'1S2/YP+4VA1N','0','0','0','0','0','0','0','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '1S2/YP+4VA1N';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('1S2/YP+4XQAJ','1S2/YP+4VA1N','zh_CN','领料差异分摊明细');
DELETE from t_cr_coderuleentry where fid = '1S2/YP+4VA1N';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('1S2/YP/9C=CT','1S2/YP+4VA1N',0,'1','1',' ','DCCYFTMX',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('1S2/YP/9C=CU','1S2/YP+4VA1N',1,'9',' ',' ','yyyyMMdd',' ',8,1,1,' ','1','1','1','0','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('1S2/YP/9C=CV','1S2/YP+4VA1N',2,'10',' ',' ',' ',' ',8,1,1,' ','1','1','1','0','-','1');
