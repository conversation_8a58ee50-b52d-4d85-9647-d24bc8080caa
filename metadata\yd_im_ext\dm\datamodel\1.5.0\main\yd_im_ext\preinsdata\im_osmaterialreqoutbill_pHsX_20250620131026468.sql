 DELETE from t_cr_coderule where fid = '2IGCU7IB=FGC';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, <PERSON><PERSON>ABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ATETIME, FMODIFIERID, <PERSON><PERSON><PERSON><PERSON>TIME, FMAS<PERSON><PERSON>D, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '2IGCU7IB=FGC','2IFU+DBDKNDW','im_osmaterialreqoutbill','-','1','2',' ',' ','1','C', 0,{ts'2022-06-17 00:00:00'},1155582543786049536,{ts'2022-06-17 00:00:00'},'2IGCU7IB=FGC','0','0','0','0','0','0','0','0','0','0',' ',' ');
DELETE from t_cr_coderule_l where fid = '2IGCU7IB=FGC';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('2IGCU7IEXV+M','2IGCU7IB=FGC','zh_CN','委外采购领料单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('2IGD3PSXZSVK','2IGCU7IB=FGC','zh_TW','委外采購領料單編碼規則');
DELETE from t_cr_coderuleentry where fid = '2IGCU7IB=FGC';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('2IGDWY1WM=QV','2IGCU7IB=FGC',0,'1','1',' ','WWLLCK',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('2IGDWY1WM=QW','2IGCU7IB=FGC',1,'2',' ','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('2IGDWY1WM=QX','2IGCU7IB=FGC',2,'16',' ',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
