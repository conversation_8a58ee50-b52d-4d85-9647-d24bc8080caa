 DELETE from t_cr_coderule where fid = '/S9HMVBLRU=5';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAMPL<PERSON><PERSON>GTH, FENABLE, FSTATUS, FCRE<PERSON><PERSON>ID, FCREATETIME, FMODIFIERID, <PERSON>OD<PERSON><PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, F<PERSON>CHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '/S9HMVBLRU=5',' ','im_otherinbill','-','1','2',' ',' ','1','C', 1,{ts'2018-08-08 18:08:00'},1,{ts'2018-08-08 18:08:00'},'/S9HMVBLRU=5','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '/S9HMVBLRU=5';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9HMVBLRX+7','/S9HMVBLRU=5','zh_CN','其他入库单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9HMVBLRX+6','/S9HMVBLRU=5','zh_TW','其他入庫單編碼規則');
DELETE from t_cr_coderuleentry where fid = '/S9HMVBLRU=5';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9HMVCBS4U=','/S9HMVBLRU=5',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9HMVCBS4U9','/S9HMVBLRU=5',0,'1',' ',' ','QTRK',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9HMVCBS4UA','/S9HMVBLRU=5',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
