 DELETE from t_cr_coderule where fid = '2C5514N881C+';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, FENABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMODIFIERID, <PERSON>OD<PERSON><PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, <PERSON><PERSON>CH<PERSON><PERSON>CODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '2C5514N881C+','2C55+5YAAW+6','im_planorder','-','1','2',' ',' ','1','C', 0,{ts'2022-04-07 00:00:00'},1,{ts'2022-04-07 00:00:00'},'2C5514N881C+','0','0','0','0','0','0','0','0','0','0',' ',' ');
DELETE from t_cr_coderule_l where fid = '2C5514N881C+';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('2C5514N88+NZ','2C5514N881C+','zh_CN','外购计划建议');
DELETE from t_cr_coderuleentry where fid = '2C5514N881C+';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('2C5514OBR0O4','2C5514N881C+',0,'1','1',' ','JHJY',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('2C5514OBR0O5','2C5514N881C+',1,'2',' ','availabledate','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('2C5514OBR0O6','2C5514N881C+',2,'16',' ',' ',' ',' ',8,1,1,' ','1','1','1','0','-','1');
