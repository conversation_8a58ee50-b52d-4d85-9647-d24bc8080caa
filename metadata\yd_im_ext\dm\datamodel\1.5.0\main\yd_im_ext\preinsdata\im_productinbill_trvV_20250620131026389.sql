 DELETE from t_cr_coderule where fid = '05OVGZV439UL';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>GTH, FENABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON>ID, FCREATETIME, FMODIFIERID, <PERSON><PERSON><PERSON><PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, <PERSON><PERSON>CHEC<PERSON>CODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '05OVGZV439UL',' ','im_productinbill','-','1','2',' ',' ','1','C', 1,{ts'2019-08-21 17:14:25'},1,{ts'2019-08-21 17:14:25'},'05OVGZV439UL','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '05OVGZV439UL';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('05OVGZV43DJE','05OVGZV439UL','zh_CN','生产入库单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('05OVGZV43DJD','05OVGZV439UL','zh_TW','生產入庫單編碼規則');
DELETE from t_cr_coderuleentry where fid = '05OVGZV439UL';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('05OVGZXN/H0F','05OVGZV439UL',0,'1',' ',' ','SCRK',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('05OVGZXN/H0G','05OVGZV439UL',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('05OVGZXN/H0H','05OVGZV439UL',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
