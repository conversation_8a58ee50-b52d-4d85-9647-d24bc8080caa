 DELETE from t_cr_coderule where fid = '/S9B8W3DWA45';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAMPL<PERSON><PERSON>GTH, FENABLE, FSTATUS, FCREAT<PERSON>ID, FCREATETIME, FMODIFIERID, FMODIF<PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '/S9B8W3DWA45','采购入库默认编码规则','im_purinbill','-','1','2',' ',' ','1','C', 1,{ts'2018-08-08 18:08:00'},1338616377744819200,{ts'2023-04-19 10:27:40'},'/S9B8W3DWA45','0','0','0','0','0','0','1','0','1','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '/S9B8W3DWA45';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9B8W3DW8F5','/S9B8W3DWA45','zh_CN','采购入库单编码规则');
DELETE from t_cr_coderuleentry where fid = '/S9B8W3DWA45';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('37PDEU6TPS3N','/S9B8W3DWA45',0,'1',' ',' ','CGRK',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('37PDEU6TPS3O','/S9B8W3DWA45',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('37PDEU6TPS3P','/S9B8W3DWA45',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
