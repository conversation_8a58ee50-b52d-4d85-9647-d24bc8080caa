 DELETE from t_cr_coderule where fid = '/S9H9O6VR6EZ';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, <PERSON>EX<PERSON><PERSON><PERSON><PERSON>G<PERSON>, <PERSON>ENABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMOD<PERSON>IERID, <PERSON><PERSON><PERSON><PERSON>TI<PERSON>, FMASTE<PERSON>D, FISUPDATERECOVER, FISNONBREAK, <PERSON><PERSON>CHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '/S9H9O6VR6EZ',' ','im_purreturnbill','-','1','2',' ',' ','1','C', 1,{ts'2018-08-08 18:08:00'},1,{ts'2018-08-08 18:08:00'},'/S9H9O6VR6EZ','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '/S9H9O6VR6EZ';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9H9O6VR94/','/S9H9O6VR6EZ','zh_CN','采购退库单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9H9O6VR94+','/S9H9O6VR6EZ','zh_TW','采購退庫單編碼規則');
DELETE from t_cr_coderuleentry where fid = '/S9H9O6VR6EZ';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9H9O=VO0Q/','/S9H9O6VR6EZ',0,'1',' ',' ','CGTK',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9H9O=VO0Q0','/S9H9O6VR6EZ',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9H9O=VO0Q1','/S9H9O6VR6EZ',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
