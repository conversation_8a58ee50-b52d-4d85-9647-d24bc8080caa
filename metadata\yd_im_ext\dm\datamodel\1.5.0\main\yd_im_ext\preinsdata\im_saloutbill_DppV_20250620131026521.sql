 DELETE from t_cr_coderule where fid = '/S9HCNGWWFP1';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, FENABLE, FSTATUS, FCREAT<PERSON>ID, FCREATETIME, FMODIFIERID, <PERSON>OD<PERSON><PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, <PERSON><PERSON>CH<PERSON>KCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFIABLE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '/S9HCNGWWFP1','销售出库默认编码规则','im_saloutbill','-','1','2',' ',' ','1','C', 1,{ts'2018-08-08 18:08:00'},1338616377744819200,{ts'2023-04-19 10:17:01'},'/S9HCNGWWFP1','0','0','0','0','0','0','1','0','1','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '/S9HCNGWWFP1';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9HCNGWWD/1','/S9HCNGWWFP1','zh_CN','销售出库单编码规则');
DELETE from t_cr_coderuleentry where fid = '/S9HCNGWWFP1';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('37PCESWEAGGF','/S9HCNGWWFP1',0,'1',' ',' ','XSCK',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('37PCESWEAGGG','/S9HCNGWWFP1',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('37PCESWEAGGH','/S9HCNGWWFP1',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
