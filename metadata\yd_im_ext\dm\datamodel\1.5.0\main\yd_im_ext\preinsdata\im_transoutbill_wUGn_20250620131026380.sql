 DELETE from t_cr_coderule where fid = '/S9HQWM2CZ/F';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FC<PERSON>LMODE, FAPPMODE, FEXAMPLE, <PERSON>EXAM<PERSON><PERSON><PERSON><PERSON><PERSON>, FENABLE, FSTATUS, FCREATORID, FCREATETIME, FMODIF<PERSON>RID, <PERSON><PERSON><PERSON><PERSON>TIME, FMASTERID, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '/S9HQWM2CZ/F',' ','im_transoutbill','-','1','2',' ',' ','1','C', 1,{ts'2018-08-08 18:08:00'},1,{ts'2018-08-08 18:08:00'},'/S9HQWM2CZ/F','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '/S9HQWM2CZ/F';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9HQWM2CWAF','/S9HQWM2CZ/F','zh_CN','调拨出库单编码规则');
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('/S9HQWM2CWAE','/S9HQWM2CZ/F','zh_TW','調撥出庫單編碼規則');
DELETE from t_cr_coderuleentry where fid = '/S9HQWM2CZ/F';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9HQWN39I42','/S9HQWM2CZ/F',0,'1',' ',' ','DBCK',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9HQWN39I43','/S9HQWM2CZ/F',1,'2','1','biztime','yyMMdd',' ',8,1,1,' ','1','1','1','1','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('/S9HQWN39I44','/S9HQWM2CZ/F',2,'16','1',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
