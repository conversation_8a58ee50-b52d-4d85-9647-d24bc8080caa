 DELETE from t_cr_coderule where fid = '4V=6+08BXJ<PERSON>';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, <PERSON><PERSON>ABLE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FCREATETIME, FMODIFIERID, <PERSON>OD<PERSON><PERSON>TIME, FMAS<PERSON><PERSON>D, FISUPDATERECOVER, FISNONBREAK, FISCHECKCODE, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '4V=6+08BXJEM','4V=5X6F4WHYI','yd_mergeorderfields','-','1','2',' ',' ','1','C', 0,{ts'2025-04-02 14:08:36'},1338618754925331456,{ts'2025-04-02 14:08:36'},'4V=6+08BXJ<PERSON>','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '4V=6+08BXJEM';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('4V=6+08BXK2W','4V=6+08BXJEM','zh_CN','合单字段表');
DELETE from t_cr_coderuleentry where fid = '4V=6+08BXJEM';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('4V=6+09GEJE0','4V=6+08BXJEM',0,'1','1',' ','HDZD',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('4V=6+09GEJE1','4V=6+08BXJEM',1,'9',' ',' ','yyMMdd',' ',8,1,1,' ','1','1','1','0','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('4V=6+09GEJE2','4V=6+08BXJEM',2,'16',' ',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
