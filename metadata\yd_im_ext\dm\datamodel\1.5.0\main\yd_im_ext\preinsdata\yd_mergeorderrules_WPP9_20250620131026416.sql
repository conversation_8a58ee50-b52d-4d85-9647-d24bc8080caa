 DELETE from t_cr_coderule where fid = '4V=3QSUP25OR';
 INSERT INTO t_cr_coderule(FID, FNUMBER, FBIZOBJECTID, FSPLITSIGN, FCTRLMODE, FAPPMODE, FEXAMPLE, FEXAM<PERSON><PERSON><PERSON>G<PERSON>, <PERSON><PERSON><PERSON>LE, FSTATUS, <PERSON><PERSON><PERSON><PERSON><PERSON>, FC<PERSON>ATETIME, FMODIFIERID, <PERSON>OD<PERSON><PERSON>TI<PERSON>, FMASTERID, FISUPDATERECOVER, FISNONBREAK, F<PERSON><PERSON><PERSON>KCO<PERSON>, FISAPPCONDITION, FISAPPORG, FISLOG, FISSERIALNUMBER, FISUNIQUE, FISMODIFI<PERSON>LE, FISADDVIEW, FFILTERCONDITION, FCONDITIONDESC) VALUES ( '4V=3QSUP25OR','4V=3N3ZAJU7C','yd_mergeorderrules','-','1','2',' ',' ','1','C', 0,{ts'2025-04-02 13:44:49'},1338618754925331456,{ts'2025-04-02 13:44:49'},'4V=3QSUP25OR','0','0','0','0','0','0','1','0','0','1',' ',' ');
DELETE from t_cr_coderule_l where fid = '4V=3QSUP25OR';
INSERT INTO t_cr_coderule_l(FPKID, FID, FLOCALEID, FNAME) VALUES ('4V=3QSUP26C/','4V=3QSUP25OR','zh_CN','合单规则表');
DELETE from t_cr_coderuleentry where fid = '4V=3QSUP25OR';
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('4V=3QSVTL5+X','4V=3QSUP25OR',0,'1','1',' ','HDGZ',' ',8,1,1,' ','0','1','1','0',' ','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('4V=3QSVTL5+Y','4V=3QSUP25OR',1,'9',' ',' ','yyMMdd',' ',8,1,1,' ','1','1','1','0','-','1');
INSERT t_cr_coderuleentry(FENTRYID, FID, FSEQ, FATTRIBUTETYPE, FATTUSINGMODE, FVALUEATRIBUTE, FFORMAT, FSETTINGVALUE, FLENGTH, FINITIAL, FSTEP, FADDCHAR, FISSPLITSIGN, FADDSTYLE, FCUTSTYLE, FISSORTITEM, FSPLITSIGN, FISVISABLE) VALUES ('4V=3QSVTL5+Z','4V=3QSUP25OR',2,'16',' ',' ',' ',' ',6,1,1,' ','1','1','1','0','-','1');
