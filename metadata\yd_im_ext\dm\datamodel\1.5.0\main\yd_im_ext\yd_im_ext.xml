<?xml version="1.0" encoding="UTF-8"?>
<DataModel dympath="metadata" xmlpath="preinsdata/xml">
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_initbill_Ti41_20250620131026276.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_mdc_backdiffshare_log_zSEe_20250620131026298.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_transdirbill_6ppl_20250620131026308.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_transinbill_b7nF_20250620131026317.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_mdc_diffsharedetail_VBVt_20250620131026326.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_surplusbill_nXdU_20250620131026335.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_purreceivebill_l6o6_20250620131026345.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_salreturnbill_e0sL_20250620131026353.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_mdc_backdifshare_YR2N_20250620131026362.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_invcountbill_OB58_20250620131026371.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_transoutbill_wUGn_20250620131026380.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_productinbill_trvV_20250620131026389.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_deficitbill_JKlL_20250620131026398.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_materialreqbill_8olz_20250620131026407.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/yd_mergeorderrules_WPP9_20250620131026416.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_ospurinbill_8nZA_20250620131026424.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_disassemblebill_NXbq_20250620131026432.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_otherinbill_5TvH_20250620131026441.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/yd_dailyshipment_b5Tp_20250620131026450.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_purinbill_pvS3_20250620131026459.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_osmaterialreqoutbill_pHsX_20250620131026468.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_lotadjust_WkBM_20250620131026477.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_planorder_blfG_20250620131026486.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_otheroutbill_s8e7_20250620131026495.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_purreturnbill_PdKw_20250620131026504.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_locationtransfer_7QiM_20250620131026512.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_saloutbill_DppV_20250620131026521.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_transapply_d3cK_20250620131026529.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/yd_mergeorderfields_NzBa_20250620131026538.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_typeadjust_gTo1_20250620131026547.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_assembbill_3Jer_20250620131026556.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_adjustbill_TB4z_20250620131026564.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_statusadjust_tChw_20250620131026573.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_invcountscheme_PuYy_20250620131026582.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/im_materialreqoutbill_vwfP_20250620131026591.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_333636333431383839353637323336303936_1750396226613.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_333636333432343132323336323334373532_1750396226625.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_333636333434303734373537363738303830_1750396226631.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_333636333434363639373238303839303838_1750396226637.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_333636333438323438353933343734353630_1750396226642.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_343532363832383331383631313430343830_1750396226648.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_343533333933323338373632343733343732_1750396226654.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_343533353231393837343533363039393834_1750396226660.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_343639343535323738363934323432333034_1750396226665.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_343639343535373337353334333233373132_1750396226671.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_343734343436303637343039313037393638_1750396226678.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_353038333838373238313239393930363536_1750396226683.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_353038333930303831373734343937373932_1750396226689.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_353039313336383936313832343635353336_1750396226695.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_353333393639353637383930383934383438_1750396226700.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_353436343834303833333431393539313638_1750396226706.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_353436343834393733353439343239373630_1750396226711.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_353436343835323634353136363836383438_1750396226717.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_363331363234303438313036383332383936_1750396226722.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_363937363738383338323937333138343030_1750396226728.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_363938323530313335383133303838323536_1750396226733.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_373036333631353633333834323930333034_1750396226739.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_373231363735323637363038363838363430_1750396226744.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_373436383535333336363733323235373238_1750396226750.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_373537373237363938343930383639373630_1750396226755.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_373637313334353533333237353838333532_1750396226761.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_373637333036363834383632363136353736_1750396226768.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_373637333038363334343038343931303038_1750396226774.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_393130313735313139303433393035353336_1750396226779.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_393230303339393039373131303437363830_1750396226784.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_393230303430353533303333333934313736_1750396226790.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_393838333736363731393139303532383030_1750396226795.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_393839363333313634383032373035343038_1750396226800.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_393839363333363330383733383834363732_1750396226807.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_393839373732383733343131333539373434_1750396226812.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31323034333336363934393334313935323030_1750396226817.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31323233393431343630393935333637393336_1750396226822.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31323233393431373239393736303833343536_1750396226828.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31323233393432303134323135363736393238_1750396226833.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31323238383335393433323732373937313834_1750396226838.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31333233323032373834313635393337313532_1750396226851.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31333933353037373737383833383537393230_1750396226858.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31343135303535303134373639393933373238_1750396226864.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31343231373133333131313636313331323030_1750396226870.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31343333393235383332343030373831333132_1750396226876.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31343336393232373830363836383032393434_1750396226881.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31343434313830363034373536313634363038_1750396226887.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31343434313831323435373936383131373736_1750396226893.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31343434313831383335353234333431373630_1750396226898.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/bos_billtype_31343633313134363933313934333334323038_1750396226904.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/wb_portal_scheme_1297823959181427712_20250620131027.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/wb_portal_scheme_1338408474744193024_20250620131027.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/wb_portal_scheme_1301388174047577088_20250620131027.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/wb_portal_scheme_1286835015484702720_20250620131027.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/wb_portal_scheme_949992343916906496_20250620131027.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/wb_portal_scheme_1373172115301210112_20250620131027.sql</SqlScript>
    <SqlScript Type="KSQL" Separator=";" dbkey="basedata">preinsdata/wb_portal_scheme_519290390566704128_20250620131026.sql</SqlScript>
</DataModel>
