package kd.bos.service;

import kd.bos.config.client.util.ConfigUtils;
import kd.bos.service.webserver.JettyServer;

public class DebugServer242 {

	public static void main(String[] args) throws Exception
	{
		System.setProperty(ConfigUtils.APP_NAME_KEY, "mservice-biz1.5-cosmic");

		//设置集群环境名称和配置服务器地址
		System.setProperty(ConfigUtils.CLUSTER_NAME_KEY, "ierp");
		System.setProperty(ConfigUtils.CONFIG_URL_KEY, "***************:2181");
		System.setProperty("configAppName", "mservice,web");
		System.setProperty("webmserviceinone", "true");

		System.setProperty("file.encoding", "utf-8");
		System.setProperty("xdb.enable", "false");

		System.setProperty("mq.consumer.register", "true");
		System.setProperty("MONITOR_HTTP_PORT", "9998");
		System.setProperty("JMX_HTTP_PORT", "9091");
		System.setProperty("dubbo.protocol.port", "28888");
		System.setProperty("dubbo.consumer.url", "dubbo://localhost:28888");
		System.setProperty("dubbo.consumer.url.qing", "dubbo://localhost:30880");
		System.setProperty("dubbo.registry.register", "false");
//		System.setProperty("mq.consumer.register","true");//true..才能在本地时触发工作流
		System.setProperty("mq.debug.queue.tag", "whb1133");
		System.setProperty("dubbo.service.lookup.local", "true");
		System.setProperty("appSplit", "false");

		System.setProperty("lightweightdeploy","false");

		System.setProperty("db.sql.out", "true");
		System.setProperty("db.sql.out.withParameter", "true");


		System.setProperty("JETTY_WEB_PORT","8080");
		System.setProperty("JETTY_WEBAPP_PATH", "D:/tcbj_cosmic/mservice-cosmic/webapp");
		System.setProperty("JETTY_WEBRES_PATH", "D:/tcbj_cosmic/static-file-service");

		//http://localhost:8080/ierp/login.html
		System.setProperty("domain.contextUrl","http://localhost:8080/ierp");
		System.setProperty("domain.tenantCode","ierp");
		System.setProperty("tenant.code.type","config");

		//System.setProperty("fileserver","http://127.0.0.1:8100/fileserver/");
		//System.setProperty("imageServer.url","http://127.0.0.1:8100/fileserver/");
		System.setProperty("bos.app.special.deployalone.ids","");
		System.setProperty("mc.server.url","http://***************:8090/mc");
		System.setProperty("mq.debug.queue.tag", "test");
//		System.setProperty("actionConfigFiles.config","kd/bos/userInfoApi/api/actionconfig-kd.xml");
		JettyServer.main(null);
	}

}