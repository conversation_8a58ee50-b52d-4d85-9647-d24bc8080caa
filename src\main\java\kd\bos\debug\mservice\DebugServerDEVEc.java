package kd.bos.debug.mservice;

import kd.bos.config.client.util.ConfigUtils;
import kd.bos.db.DB;
import kd.bos.service.webserver.JettyServer;

import java.nio.file.Files;
import java.nio.file.Paths;

public class DebugServerDEVEc {

    public static void main(String[] args) throws Exception
    {
        //应用标识，每个端改成自己的名字命名
//		System.setProperty(ConfigUtils.APP_NAME_KEY, "mservice-biz1.5-cosmic");
        System.setProperty(ConfigUtils.APP_NAME_KEY, "my-cosmic");
        //设置集群环境名称和配置服务器地址
//		System.setProperty(ConfigUtils.CLUSTER_NAME_KEY, "cosmic");//集群编码，同服务器中MC配置一致
        System.setProperty(ConfigUtils.CLUSTER_NAME_KEY, "ierp");
//		System.setProperty(ConfigUtils.CONFIG_URL_KEY, "127.0.0.1:2181");
		System.setProperty(ConfigUtils.CONFIG_URL_KEY, "*************:2181");//dev zk路径
//        System.setProperty(ConfigUtils.CONFIG_URL_KEY, "*************:2181");//UAT zk路径
//        System.setProperty(ConfigUtils.CONFIG_URL_KEY, "172.16.13.75:2181");//正式环境 zk路径
        System.setProperty("configAppName", "mservice,web");
        System.setProperty("webmserviceinone", "true");

        System.setProperty("file.encoding", "utf-8");
        System.setProperty("xdb.enable", "false");//xdb需要垂直分库时使用

//		System.setProperty("mq.debug.queue.tag", "jimmy_unique_queue");//改成自己的队列名，全集群唯一性，对工作流、定时任务有影响

        System.setProperty("mq.consumer.register", "true");
        System.setProperty("MONITOR_HTTP_PORT", "9998");
        System.setProperty("JMX_HTTP_PORT", "9091");
//		System.setProperty("mqConfigFiles.config", "jimmymqconfig.xml");//mq配置文件
        System.setProperty("dubbo.registry.register", "false");
        System.setProperty("dubbo.protocol.port", "28888");
        System.setProperty("dubbo.consumer.url", "dubbo://localhost:28888");
        System.setProperty("dubbo.consumer.url.qing", "dubbo://localhost:30880");
        System.setProperty("dubbo.registry.register", "false");
        //System.setProperty("mq.debug.queue.tag", "whb1133");
        System.setProperty("dubbo.service.lookup.local", "true");
        System.setProperty("appSplit", "false");//是否分应用部署

        System.setProperty("lightweightdeploy","false");//是否轻量级环境部署，默认是否

        System.setProperty("db.sql.out", "false");//打印日志
        System.setProperty("db.sql.out.withParameter", "true");
// 运行期前开启DB日志(仅用于调试，不要在产品中使用，会导致日志配置失效)
        DB.setSqlOut(true);
//		DB.setSqlLogger(new SqlLogger() {
//			@Override
//			public void logSQL(String sql, Object... objects) {
//				System.out.println(sql);
//			}
//
//			@Override
//			public void logMessage(String s) {
//				System.out.println(s);
//			}
//		});

//		System.setProperty("script.debug.enable","true");//kde脚本调度
        System.setProperty("login.type","STANDALONE");//登录方式

        System.setProperty("JETTY_WEB_PORT","8080");
        System.setProperty("JETTY_WEBAPP_PATH", "../../../mservice-cosmic/webapp");//本地Jettywebapp路径
        System.setProperty("JETTY_WEBRES_PATH", "../../../static-file-service");//本地静态资源路径

//		System.setProperty("actionConfigFiles.config", "../../../qing-actionconfig.xml");//轻分析action本地配置文件
//		System.setProperty("ActionConfigFiles", "../../../actionconfig.xml");//微服务action本地配置文件路径

        System.setProperty("domain.contextUrl","http://localhost:8080/ierp");//影响登录路径入口地址
//	    System.setProperty("domain.tenantCode","cosmic-simple");
//		System.setProperty("domain.tenantCode","tcbj");//租户编码，同服务器中mc配置一致,DEV
        System.setProperty("domain.tenantCode","ierp");//租户编码，同服务器中mc配置一致,UAT
        System.setProperty("tenant.code.type","config");

        //System.setProperty("fileserver","http://127.0.0.1:8100/fileserver/");
        //System.setProperty("imageServer.url","http://127.0.0.1:8100/fileserver/");
        System.setProperty("bos.app.special.deployalone.ids","");
//		System.setProperty("mc.server.url","http://127.0.0.1:8090/");
		System.setProperty("mc.server.url","http://*************:8090/mc");//远程mc路径,DEV
//        System.setProperty("mc.server.url","http://*************:8090/mc");//远程mc路径,UAT
//        System.setProperty("mc.server.url","http://*************:8090/mc");//远程mc路径,正式环境
        System.setProperty("mq.debug.queue.tag", "test");
//		System.setProperty("org.eclipse.jetty.server.Request.maxFormContentSize","9000000");

        try {
            System.setProperty("log.config", org.apache.commons.lang3.StringUtils.join(Files.readAllLines(
                    Paths.get(DebugServerDEVEc.class.getClassLoader().getResource("logback.xml").toURI())), ""));
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        JettyServer.main(null);

    }

}