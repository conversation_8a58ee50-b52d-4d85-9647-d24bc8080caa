package kd.bos.debug.mservice;

import kd.bos.config.client.util.ConfigUtils;
import kd.bos.db.DB;
import kd.bos.db.SqlLogger;
import kd.bos.service.webserver.JettyServer;

public class DebugServerUAT {

	public static void main(String[] args) throws Exception
	{
		System.setProperty(ConfigUtils.APP_NAME_KEY, "mservice-biz1.5-cosmic");
//		System.setProperty(ConfigUtils.APP_NAME_KEY, "mservice");

		//设置集群环境名称和配置服务器地址
//		System.setProperty(ConfigUtils.CLUSTER_NAME_KEY, "cosmic");
		System.setProperty(ConfigUtils.CLUSTER_NAME_KEY, "ierp");
//		System.setProperty(ConfigUtils.CONFIG_URL_KEY, "127.0.0.1:2181");
		System.setProperty(ConfigUtils.CONFIG_URL_KEY, "***************:2181");
//		System.setProperty(ConfigUtils.CONFIG_URL_KEY, "***************:2181");
		System.setProperty("configAppName", "mservice,web");
		System.setProperty("webmserviceinone", "true");

		System.setProperty("file.encoding", "utf-8");
		System.setProperty("xdb.enable", "false");

		System.setProperty("mq.consumer.register", "false");
		System.setProperty("MONITOR_HTTP_PORT", "9998");
		System.setProperty("JMX_HTTP_PORT", "9091");
		System.setProperty("dubbo.protocol.port", "28888");
		System.setProperty("dubbo.consumer.url", "dubbo://localhost:28888");
		System.setProperty("dubbo.consumer.url.qing", "dubbo://localhost:30880");
		System.setProperty("dubbo.registry.register", "false");
		System.setProperty("mq.debug.queue.tag", "whb1133");
		System.setProperty("dubbo.service.lookup.local", "true");
		System.setProperty("appSplit", "false");

		System.setProperty("lightweightdeploy","false");

		System.setProperty("db.sql.out", "false");

		System.setProperty("JETTY_WEB_PORT","8080");
		System.setProperty("JETTY_WEBAPP_PATH", "D:/tcbj_cosmic/mservice-cosmic/webapp");
		System.setProperty("JETTY_WEBRES_PATH", "D:/tcbj_cosmic/static-file-service");
		System.setProperty("ActionConfigFile", "D:/tcbj_cosmic/mservice-cosmic/conf/actionconfig.xml");

		System.setProperty("domain.contextUrl","http://localhost:8080/ierp");
//	    System.setProperty("domain.tenantCode","cosmic-simple");
//	    System.setProperty("domain.tenantCode","tcbj");
		System.setProperty("domain.tenantCode","ierp");
		System.setProperty("tenant.code.type","config");

		System.setProperty("trace.enable","false");
		System.setProperty("trace.reporter.type","");


//		System.setProperty("fileserver","http://***************:8765/fileserver/");
//	    System.setProperty("imageServer.url","http://***************:8765/fileserver/");
//		System.setProperty("fileserver","http://127.0.0.1:8100/fileserver/");
//		System.setProperty("imageServer.url","http://127.0.0.1:8100/fileserver/");
		System.setProperty("bos.app.special.deployalone.ids","");
//		System.setProperty("mc.server.url","http://127.0.0.1:8090/");
//	    System.setProperty("mc.server.url", "http://***************:8090/mc");
		System.setProperty("mc.server.url", "http://***************:8090/mc");
//		System.setProperty("mc.server.url", "http://***************:8090/mc");

		http://***************/
		// 开启log
		/**
		 StringBuffer log = new StringBuffer();
		 Files.readAllLines(Paths.get(Debug85.class.getResource("/log.config.xml").toURI()))
		 .forEach(s -> log.append(s + "\n"));
		 System.setProperty("log.config", log.toString());
		 */
		DB.setSqlLogger(new SqlLogger() {

			@Override
			public void logSQL(String arg0, Object... arg1) {
				System.out.println(arg0);
			}

			@Override
			public void logMessage(String arg0) {

			}
		});


		JettyServer.main(null);
//		Booter.main(null);
	}
}