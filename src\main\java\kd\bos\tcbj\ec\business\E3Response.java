package kd.bos.tcbj.ec.business;

import com.fasterxml.jackson.annotation.JsonProperty;

public class E3Response {

    /**
     * 是否成功
     */
    @JsonProperty("success")
    Boolean success;

    /**
     * 操作状态码
     */
    @JsonProperty("status")
    String status;
    
    /**
     * 操作消息
     */
    @JsonProperty("message")
    String message;

    /**
     * 业务数据
     */
    @JsonProperty("data")
    String data;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
} 