package kd.bos.tcbj.ec.business;

import java.util.List;
import java.util.Map;

/**
 * SQL查询结果包装类
 * 用于封装数据库查询返回的结果，包含状态码、消息和数据列表
 * 
 * <AUTHOR>
 * @date 2024
 */
public class E3SqlResult {
    
    /**
     * 响应状态码
     * 通常用于标识请求是否成功，如200表示成功，其他值表示失败
     */
    private Integer success;
    
    /**
     * 响应消息
     * 用于描述请求结果的详细信息，如成功消息或错误描述
     */
    private String msg;
    
    /**
     * 查询结果数据列表
     * 存储SQL查询返回的数据，每个Map代表一行记录，key为字段名，value为字段值
     */
    private List<Map<String, Object>> data;

    /**
     * 获取响应状态码
     * 
     * @return 状态码
     */
    public Integer getSuccess() {
        return success;
    }

    /**
     * 设置响应状态码
     * 
     * @param code 状态码
     */
    public void setSuccess(Integer success) {
        this.success = success;
    }

    /**
     * 获取响应消息
     * 
     * @return 响应消息
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 设置响应消息
     * 
     * @param msg 响应消息
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * 获取查询结果数据列表
     * 
     * @return 数据列表，每个Map代表一行记录
     */
    public List<Map<String, Object>> getData() {
        return data;
    }

    /**
     * 设置查询结果数据列表
     * 
     * @param data 数据列表，每个Map代表一行记录
     */
    public void setData(List<Map<String, Object>> data) {
        this.data = data;
    }
} 
