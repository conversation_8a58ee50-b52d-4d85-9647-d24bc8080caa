package kd.bos.tcbj.ec.business.response;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * E3分页信息类
 * 用于映射E3接口响应中的分页相关信息
 */
public class E3PageInfo {

    /**
     * 当前页码
     */
    @JSONField(name = "pageNo")
    private Integer pageNo;

    /**
     * 每页数量
     */
    @JSONField(name = "pageSize")
    private Integer pageSize;

    /**
     * 合计结果数量
     */
    @JSONField(name = "totalResult")
    private Integer totalResult;

    /**
     * 合计页数
     */
    @JSONField(name = "pageTotal")
    private Integer pageTotal;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalResult() {
        return totalResult;
    }

    public void setTotalResult(Integer totalResult) {
        this.totalResult = totalResult;
    }

    public Integer getPageTotal() {
        return pageTotal;
    }

    public void setPageTotal(Integer pageTotal) {
        this.pageTotal = pageTotal;
    }
} 