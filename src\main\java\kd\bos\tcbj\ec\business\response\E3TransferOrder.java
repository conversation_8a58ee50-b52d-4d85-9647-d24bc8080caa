package kd.bos.tcbj.ec.business.response;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;

/**
 * E3移仓单据类
 * 用于映射E3接口响应中的ycList单据信息
 */
public class E3TransferOrder {

    /**
     * 单据ID
     */
    @JSONField(name = "id")
    private String id;

    /**
     * 单据编号
     */
    @JSONField(name = "djbh")
    private String djbh;

    /**
     * 日期
     */
    @JSONField(name = "rq")
    private String rq;

    /**
     * 原单据号
     */
    @JSONField(name = "lxdj")
    private String lxdj;

    /**
     * 备注
     */
    @JSONField(name = "bz")
    private String bz;

    /**
     * 制单人
     */
    @JSONField(name = "zdr")
    private String zdr;

    /**
     * 制单日期
     */
    @JSONField(name = "zdrq")
    private String zdrq;

    /**
     * 是否审批
     */
    @JSONField(name = "sp")
    private Integer sp;

    /**
     * 审批人
     */
    @JSONField(name = "spr")
    private String spr;

    /**
     * 审批日期
     */
    @JSONField(name = "sprq")
    private String sprq;

    /**
     * 作废
     */
    @JSONField(name = "zf")
    private Integer zf;

    /**
     * 作废人
     */
    @JSONField(name = "zfr")
    private String zfr;

    /**
     * 作废日期
     */
    @JSONField(name = "zfrq")
    private String zfrq;

    /**
     * 是否终止
     */
    @JSONField(name = "zz")
    private Integer zz;

    /**
     * 终止人
     */
    @JSONField(name = "zzr")
    private String zzr;

    /**
     * 终止日期
     */
    @JSONField(name = "zzrq")
    private String zzrq;

    /**
     * 单据状态
     */
    @JSONField(name = "status")
    private String status;

    /**
     * 实际数量
     */
    @JSONField(name = "sl")
    private Integer sl;

    /**
     * 通知数量
     */
    @JSONField(name = "sl1")
    private Integer sl1;

    /**
     * 执行数量
     */
    @JSONField(name = "sl2")
    private Integer sl2;

    /**
     * 金额
     */
    @JSONField(name = "je")
    private Double je;

    /**
     * 移入仓库编码
     */
    @JSONField(name = "yrck_code")
    private String yrckCode;

    /**
     * 移入仓库名称
     */
    @JSONField(name = "yrck_name")
    private String yrckName;

    /**
     * 移出仓库编码
     */
    @JSONField(name = "ycck_code")
    private String ycckCode;

    /**
     * 移出仓库名称
     */
    @JSONField(name = "ycck_name")
    private String ycckName;

    /**
     * 移出渠道
     */
    @JSONField(name = "ycqd_name")
    private String ycqdName;

    /**
     * 移入渠道
     */
    @JSONField(name = "yrqd_name")
    private String yrqdName;

    /**
     * 出库时间
     */
    @JSONField(name = "ckrq")
    private String ckrq;

    /**
     * 入库时间
     */
    @JSONField(name = "rkrq")
    private String rkrq;

    /**
     * 明细列表
     */
    @JSONField(name = "mx_list")
    private List<E3TransferOrderDetail> mxList;

    // Getter and Setter methods
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDjbh() {
        return djbh;
    }

    public void setDjbh(String djbh) {
        this.djbh = djbh;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public String getLxdj() {
        return lxdj;
    }

    public void setLxdj(String lxdj) {
        this.lxdj = lxdj;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getZdr() {
        return zdr;
    }

    public void setZdr(String zdr) {
        this.zdr = zdr;
    }

    public String getZdrq() {
        return zdrq;
    }

    public void setZdrq(String zdrq) {
        this.zdrq = zdrq;
    }

    public Integer getSp() {
        return sp;
    }

    public void setSp(Integer sp) {
        this.sp = sp;
    }

    public String getSpr() {
        return spr;
    }

    public void setSpr(String spr) {
        this.spr = spr;
    }

    public String getSprq() {
        return sprq;
    }

    public void setSprq(String sprq) {
        this.sprq = sprq;
    }

    public Integer getZf() {
        return zf;
    }

    public void setZf(Integer zf) {
        this.zf = zf;
    }

    public String getZfr() {
        return zfr;
    }

    public void setZfr(String zfr) {
        this.zfr = zfr;
    }

    public String getZfrq() {
        return zfrq;
    }

    public void setZfrq(String zfrq) {
        this.zfrq = zfrq;
    }

    public Integer getZz() {
        return zz;
    }

    public void setZz(Integer zz) {
        this.zz = zz;
    }

    public String getZzr() {
        return zzr;
    }

    public void setZzr(String zzr) {
        this.zzr = zzr;
    }

    public String getZzrq() {
        return zzrq;
    }

    public void setZzrq(String zzrq) {
        this.zzrq = zzrq;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSl() {
        return sl;
    }

    public void setSl(Integer sl) {
        this.sl = sl;
    }

    public Integer getSl1() {
        return sl1;
    }

    public void setSl1(Integer sl1) {
        this.sl1 = sl1;
    }

    public Integer getSl2() {
        return sl2;
    }

    public void setSl2(Integer sl2) {
        this.sl2 = sl2;
    }

    public Double getJe() {
        return je;
    }

    public void setJe(Double je) {
        this.je = je;
    }

    public String getYrckCode() {
        return yrckCode;
    }

    public void setYrckCode(String yrckCode) {
        this.yrckCode = yrckCode;
    }

    public String getYrckName() {
        return yrckName;
    }

    public void setYrckName(String yrckName) {
        this.yrckName = yrckName;
    }

    public String getYcckCode() {
        return ycckCode;
    }

    public void setYcckCode(String ycckCode) {
        this.ycckCode = ycckCode;
    }

    public String getYcckName() {
        return ycckName;
    }

    public void setYcckName(String ycckName) {
        this.ycckName = ycckName;
    }

    public String getYcqdName() {
        return ycqdName;
    }

    public void setYcqdName(String ycqdName) {
        this.ycqdName = ycqdName;
    }

    public String getYrqdName() {
        return yrqdName;
    }

    public void setYrqdName(String yrqdName) {
        this.yrqdName = yrqdName;
    }

    public String getCkrq() {
        return ckrq;
    }

    public void setCkrq(String ckrq) {
        this.ckrq = ckrq;
    }

    public String getRkrq() {
        return rkrq;
    }

    public void setRkrq(String rkrq) {
        this.rkrq = rkrq;
    }

    public List<E3TransferOrderDetail> getMxList() {
        return mxList;
    }

    public void setMxList(List<E3TransferOrderDetail> mxList) {
        this.mxList = mxList;
    }
} 