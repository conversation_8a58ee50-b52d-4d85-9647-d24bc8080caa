package kd.bos.tcbj.ec.business.response;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;

/**
 * E3移仓单据响应数据类
 * 用于映射E3接口响应中的data字段
 */
public class E3TransferOrderData {

    /**
     * 分页信息
     */
    @JSONField(name = "page")
    private E3PageInfo page;

    /**
     * 移仓单据列表
     */
    @JSONField(name = "ycList")
    private List<E3TransferOrder> ycList;

    public E3PageInfo getPage() {
        return page;
    }

    public void setPage(E3PageInfo page) {
        this.page = page;
    }

    public List<E3TransferOrder> getYcList() {
        return ycList;
    }

    public void setYcList(List<E3TransferOrder> ycList) {
        this.ycList = ycList;
    }
} 