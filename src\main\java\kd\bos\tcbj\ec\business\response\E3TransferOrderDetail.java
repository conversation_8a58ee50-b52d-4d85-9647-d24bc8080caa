package kd.bos.tcbj.ec.business.response;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * E3移仓单据明细类
 * 用于映射E3接口响应中的mx_list明细信息
 */
public class E3TransferOrderDetail {

    /**
     * 单据主键
     */
    @JSONField(name = "dj_id")
    private String djId;

    /**
     * 单据编号
     */
    @JSONField(name = "djbh")
    private String djbh;

    /**
     * SKU
     */
    @JSONField(name = "sku")
    private String sku;

    /**
     * SKU ID
     */
    @JSONField(name = "sku_id")
    private String skuId;

    /**
     * 商品ID
     */
    @JSONField(name = "goods_id")
    private String goodsId;

    /**
     * 商品编码
     */
    @JSONField(name = "goods_sn")
    private String goodsSn;

    /**
     * 颜色ID
     */
    @JSONField(name = "color_id")
    private String colorId;

    /**
     * 颜色代码
     */
    @JSONField(name = "color_code")
    private String colorCode;

    /**
     * 尺码ID
     */
    @JSONField(name = "size_id")
    private String sizeId;

    /**
     * 尺码代码
     */
    @JSONField(name = "size_code")
    private String sizeCode;

    /**
     * 实际数量
     */
    @JSONField(name = "sl")
    private Integer sl;

    /**
     * 通知数量
     */
    @JSONField(name = "sl1")
    private Integer sl1;

    /**
     * 执行数量
     */
    @JSONField(name = "sl2")
    private Integer sl2;

    /**
     * 成本价
     */
    @JSONField(name = "cbj")
    private Double cbj;

    /**
     * 折扣
     */
    @JSONField(name = "zk")
    private Double zk;

    /**
     * 单价
     */
    @JSONField(name = "dj")
    private Double dj;

    /**
     * 单价2
     */
    @JSONField(name = "dj2")
    private Double dj2;

    /**
     * 金额
     */
    @JSONField(name = "je")
    private Double je;

    /**
     * 金额2
     */
    @JSONField(name = "je2")
    private Double je2;

    /**
     * 金额3
     */
    @JSONField(name = "je3")
    private Double je3;

    /**
     * 金额4
     */
    @JSONField(name = "je4")
    private Double je4;

    /**
     * 最后变化时间
     */
    @JSONField(name = "lastchanged")
    private String lastchanged;

    /**
     * 批次号
     */
    @JSONField(name = "batch_no")
    private String batchNo;

    /**
     * 创建时间
     */
    @JSONField(name = "create_date")
    private String createDate;

    /**
     * 安全天数
     */
    @JSONField(name = "safedays")
    private Integer safedays;

    /**
     * 过期日期
     */
    @JSONField(name = "expiration_date")
    private String expirationDate;

    /**
     * 阈值日期
     */
    @JSONField(name = "threshold_date")
    private String thresholdDate;

    /**
     * 标准价
     */
    @JSONField(name = "bzj")
    private Double bzj;

    /**
     * 生产日期
     */
    @JSONField(name = "scrq")
    private String scrq;

    // Getter and Setter methods
    public String getDjId() {
        return djId;
    }

    public void setDjId(String djId) {
        this.djId = djId;
    }

    public String getDjbh() {
        return djbh;
    }

    public void setDjbh(String djbh) {
        this.djbh = djbh;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsSn() {
        return goodsSn;
    }

    public void setGoodsSn(String goodsSn) {
        this.goodsSn = goodsSn;
    }

    public String getColorId() {
        return colorId;
    }

    public void setColorId(String colorId) {
        this.colorId = colorId;
    }

    public String getColorCode() {
        return colorCode;
    }

    public void setColorCode(String colorCode) {
        this.colorCode = colorCode;
    }

    public String getSizeId() {
        return sizeId;
    }

    public void setSizeId(String sizeId) {
        this.sizeId = sizeId;
    }

    public String getSizeCode() {
        return sizeCode;
    }

    public void setSizeCode(String sizeCode) {
        this.sizeCode = sizeCode;
    }

    public Integer getSl() {
        return sl;
    }

    public void setSl(Integer sl) {
        this.sl = sl;
    }

    public Integer getSl1() {
        return sl1;
    }

    public void setSl1(Integer sl1) {
        this.sl1 = sl1;
    }

    public Integer getSl2() {
        return sl2;
    }

    public void setSl2(Integer sl2) {
        this.sl2 = sl2;
    }

    public Double getCbj() {
        return cbj;
    }

    public void setCbj(Double cbj) {
        this.cbj = cbj;
    }

    public Double getZk() {
        return zk;
    }

    public void setZk(Double zk) {
        this.zk = zk;
    }

    public Double getDj() {
        return dj;
    }

    public void setDj(Double dj) {
        this.dj = dj;
    }

    public Double getDj2() {
        return dj2;
    }

    public void setDj2(Double dj2) {
        this.dj2 = dj2;
    }

    public Double getJe() {
        return je;
    }

    public void setJe(Double je) {
        this.je = je;
    }

    public Double getJe2() {
        return je2;
    }

    public void setJe2(Double je2) {
        this.je2 = je2;
    }

    public Double getJe3() {
        return je3;
    }

    public void setJe3(Double je3) {
        this.je3 = je3;
    }

    public Double getJe4() {
        return je4;
    }

    public void setJe4(Double je4) {
        this.je4 = je4;
    }

    public String getLastchanged() {
        return lastchanged;
    }

    public void setLastchanged(String lastchanged) {
        this.lastchanged = lastchanged;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Integer getSafedays() {
        return safedays;
    }

    public void setSafedays(Integer safedays) {
        this.safedays = safedays;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getThresholdDate() {
        return thresholdDate;
    }

    public void setThresholdDate(String thresholdDate) {
        this.thresholdDate = thresholdDate;
    }

    public Double getBzj() {
        return bzj;
    }

    public void setBzj(Double bzj) {
        this.bzj = bzj;
    }

    public String getScrq() {
        return scrq;
    }

    public void setScrq(String scrq) {
        this.scrq = scrq;
    }
} 