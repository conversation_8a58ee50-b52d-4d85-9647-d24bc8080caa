package kd.bos.tcbj.ec.business.response;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * E3移仓单据完整响应类
 * 用于映射E3接口的完整响应结构
 */
public class E3TransferOrderResponse {

    /**
     * 响应状态
     */
    @JSONField(name = "status")
    private String status;

    /**
     * 响应消息
     */
    @JSONField(name = "message")
    private String message;

    /**
     * 响应数据
     */
    @JSONField(name = "data")
    private E3TransferOrderData data;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public E3TransferOrderData getData() {
        return data;
    }

    public void setData(E3TransferOrderData data) {
        this.data = data;
    }
} 