package kd.bos.tcbj.ec.common;


import kd.bos.tcbj.ec.business.E3ApiResponse;
import kd.bos.tcbj.ec.business.E3Response;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.stereotype.Service;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.dataentity.entity.DynamicObject;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.HashMap;
import java.util.Objects;
import org.codehaus.jackson.map.ObjectMapper;

public class E3ApiUtils {

    private static final Log log = LogFactory.getLog(E3ApiUtils.class);

    private static SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
    public static final String SIGN_TYPE_KEY="serviceType"; //签名type key
    public static final String SIGN_DATA_KEY="data";   //签名data key

    // E3配置参数常量
    private static final String E3_CONFIG_TABLE = "yd_e3paramsetting";
    private static final String E3_CONFIG_FIELD = "name";
    private static final String E3_IP_KEY = "EC_E3_IP";
    private static final String E3_KEY_KEY = "EC_E3_KEY";
    private static final String E3_SECRET_KEY = "EC_E3_SECRET";

    /**
     * 查询订单详情
     *
     * @param method 调用方法名称
     * @param jspnParam json查询参
     * @return List<BaiSonGetOrderDetailResponse>
     */
    public static String sendE3Api (String e3BaseUrl,String key,String secret,String method, JSONObject jspnParam) {
        Map<String, Object> param = new LinkedHashMap<>();
        try {
            Map<String, String> signMap = getSignMap(key, secret, "3.0");
            jspnParam.put("not_decrypt",1);
            String sign = generateSign(signMap, method, jspnParam);
            param.put("key", key);
            param.put("sign", sign);
            param.put("requestTime", signMap.get("requestTime"));
            param.put("version", signMap.get("version"));
            param.put("serviceType", method);
            param.put("data", jspnParam.toJSONString());
            String requestUrl = "&key=%s&sign=%s&requestTime=%s&version=%s&serviceType=%s&data=%s";
            log.info("E3接口请求:{}", e3BaseUrl + String.format(requestUrl, signMap.get("key"), sign, signMap.get("requestTime"), signMap.get("version"), method, jspnParam));
        } catch (JsonProcessingException e) {
            log.error("E3接口参数封装异常");
            throw new RuntimeException("参数封装异常");
        }

        // 发送请求
        String jsonResult = HttpClientUtils.doPost(e3BaseUrl,param);
        JSONObject jsonObject = JSONObject.parseObject(jsonResult);
        log.debug("E3接口调用结果:{}", jsonResult);
        String status = jsonObject.getString("status");
        String message = jsonObject.getString("message");
        if (status.equals("INVALID_REQUEST")) {
            //log.info("E3接口调用结果:{}", jsonResult);
            throw new RuntimeException(message);
        }
        return jsonResult;
    }

    /**
     * 查批发通知单
     *
     * @param method 调用方法名称
     * @param jspnParam json查询参
     * @return String
     */
    public static String getwholesaleOrder (String e3BaseUrl,String key,String secret,String method, JSONObject jspnParam) {
        Map<String, Object> param = new LinkedHashMap<>();
        try {
            Map<String, String> signMap = getSignMap(key, secret, "3.0");
            jspnParam.put("not_decrypt",1);
            String sign = generateSign(signMap, method, jspnParam);
            param.put("key", key);
            param.put("format", "json");
            param.put("sign", sign);
            param.put("requestTime", signMap.get("requestTime"));
            param.put("version", signMap.get("version"));
            param.put("serviceType", method);
            param.put("data", jspnParam.toJSONString());
            String requestUrl = "&key=%s&sign=%s&requestTime=%s&version=%s&serviceType=%s&data=%s";
            log.info("E3接口请求:{}", e3BaseUrl + String.format(requestUrl, signMap.get("key"), sign, signMap.get("requestTime"), signMap.get("version"), method, jspnParam));
        } catch (JsonProcessingException e) {
            log.error("E3接口参数封装异常");
            throw new RuntimeException("参数封装异常");
        }

        // 发送请求
        String jsonResult = HttpClientUtils.doPost(e3BaseUrl,param);
        JSONObject jsonObject = JSONObject.parseObject(jsonResult);
        log.info("E3接口调用结果:{}", jsonResult);
        String status = jsonObject.getString("status");
        String message = jsonObject.getString("message");
        if (status.equals("INVALID_REQUEST")) {
            throw new RuntimeException(message);
        }
        return jsonResult;
    }

    /**
     * 发送请求
     *
     * @param method    调用方法名称
     * @param jspnParam json查询参
     * @return List<BaiSonGetOrderDetailResponse>
     */
    public static  E3Response sendE3ApiReturnE3Response(String e3BaseUrl, String key, String secret, String method, JSONObject jspnParam) {
        Map<String, Object> param = new LinkedHashMap<>();
        try {
            Map<String, String> signMap = getSignMap(key, secret, "3.0");
            jspnParam.put("not_decrypt", 1);
            String sign = generateSign(signMap, method, jspnParam);
            param.put("key", key);
            param.put("sign", sign);
            param.put("requestTime", signMap.get("requestTime"));
            param.put("version", signMap.get("version"));
            param.put("serviceType", method);
            param.put("data", jspnParam.toJSONString());
            String requestUrl = "&key=%s&sign=%s&requestTime=%s&version=%s&serviceType=%s&data=%s";
            log.info("E3接口请求:{}", e3BaseUrl + String.format(requestUrl, signMap.get("key"), sign, signMap.get("requestTime"), signMap.get("version"), method, jspnParam));
        } catch (JsonProcessingException e) {
            log.error("E3接口参数封装异常");
            throw new RuntimeException("参数封装异常");
        }

        // 发送请求
        String jsonResult = HttpClientUtils.doPost(e3BaseUrl, param);
        E3Response e3Response = JSONObject.parseObject(jsonResult, E3Response.class);
        log.info("E3接口调用结果:{}", jsonResult);
        String status = e3Response.getStatus();
        String message = e3Response.getMessage();
        if (status.equals("INVALID_REQUEST")) {
            throw new RuntimeException(message);
        }
        return e3Response;
    }

    /**
     * @description 生成签名
     * @param map  参数
     * @param methodName 方法名
     * @param obj  data 数据
     * <AUTHOR>
     * @date 2019/7/2
     */
    public static String generateSign(Map<String,String> map, String methodName, JSONObject obj) throws JsonProcessingException {
        StringBuffer sb=new StringBuffer();
        if(map!=null){
            map.put(SIGN_TYPE_KEY,methodName);
            map.put(SIGN_DATA_KEY,obj.toJSONString());

            int i=1;

            for(String key : map.keySet()){
                sb.append(key).append("=").append(map.get(key));
                if (i < map.size()) {
                    sb.append("&");
                }
                i++;
            }
        }
        return MD5.getMD5Code(sb.toString());
    }

    public static Map<String, String> getSignMap(String key, String secret) {
        Map<String, String> signMap = new LinkedHashMap<>();
        signMap.put("key", key);
        signMap.put("requestTime", getNowStr());
        signMap.put("secret", secret);
        signMap.put("version", "2.0");
        return signMap;
    }

    public static Map<String, String> getSignMap(String key, String secret, String version) {
        Map<String, String> signMap = new LinkedHashMap<>();
        signMap.put("key", key);
        signMap.put("requestTime", getNowStr());
        signMap.put("secret", secret);
        signMap.put("version", version);
        return signMap;
    }

    public static String getNowStr() {
        return format.format(new Date());
    }

    /**
     * 创建E3单据
     *
     * @param method    调用方法名称
     * @param jspnParam json查询参
     * @return E3ApiResponse
     */
    public static E3ApiResponse creatE3Bill(String e3BaseUrl, String key, String secret, String method, JSONObject jspnParam) throws IOException {
        Map<String, Object> param = new LinkedHashMap<>();
        try {
            Map<String, String> signMap = getSignMap(key, secret, "3.0");
            jspnParam.put("not_decrypt", 1);
            String sign = generateSign(signMap, method, jspnParam);
            param.put("key", key);
            param.put("sign", sign);
            param.put("requestTime", signMap.get("requestTime"));
            param.put("version", signMap.get("version"));
            param.put("serviceType", method);
            param.put("data", jspnParam.toJSONString());
            String requestUrl = "&key=%s&sign=%s&requestTime=%s&version=%s&serviceType=%s&data=%s";
            log.info("[creatE3Bill][E3接口请求:{}]", e3BaseUrl + String.format(requestUrl, signMap.get("key"), sign, signMap.get("requestTime"), signMap.get("version"), method, jspnParam));
        } catch (JsonProcessingException e) {
            log.error("[creatE3Bill][E3接口参数封装异常]", e);
            throw new RuntimeException("参数封装异常");
        }

        // 发送请求
        String jsonResult = HttpClientUtils.doPost(e3BaseUrl, param);
        log.info("[creatE3Bill][E3接口调用结果:{}]", jsonResult);
        
        // 使用 FastJSON 来解析
        JSONObject jsonObject = JSONObject.parseObject(jsonResult);
        E3ApiResponse e3Response = new E3ApiResponse();
        e3Response.setStatus(jsonObject.getString("status"));
        e3Response.setMessage(jsonObject.getString("message"));
        
        // 修改点1：处理data字段可能为对象或数组的情况
        Object data = jsonObject.get("data");
        if (data instanceof JSONArray) {
            e3Response.setData((JSONArray) data);
        } else if (data instanceof JSONObject) {
            JSONArray dataArray = new JSONArray();
            dataArray.add(data);
            e3Response.setData(dataArray);
        } else {
            e3Response.setData(null);
        }

        // 处理特殊情况: 单据已存在
        if ("api-server-exception".equals(e3Response.getStatus()) && e3Response.getData() != null) {
            // 修改点2：兼容处理单对象的情况（添加类型转换）
            JSONObject firstData = null;
            JSONArray responseData = (JSONArray) e3Response.getData(); // 添加类型转换
            if (!responseData.isEmpty()) {
                Object firstItem = responseData.get(0); // 现在可以正确调用get方法
                if (firstItem instanceof JSONObject) {
                    firstData = (JSONObject) firstItem;
                }
            }
            
            if (firstData != null) {
                String dataMessage = firstData.getString("message");
                if (dataMessage != null && dataMessage.contains("已存在")) {
                    log.info("[creatE3Bill][检测到单据已存在,视为成功处理][原始响应:{}]", jsonResult);
                    e3Response.setStatus("api-success");
                    firstData.put("status", "api-success");
                }
            }
        }
        
        if ("INVALID_REQUEST".equals(e3Response.getStatus())) {
            throw new RuntimeException(e3Response.getMessage());
        }
        return e3Response;
    }

    /**
     * 获取E3系统配置信息
     * @return E3配置Map，包含baseUrl、key、secret
     * @throws RuntimeException 当配置获取失败时抛出异常
     */
    public static Map<String, String> getE3Config() {
        Map<String, String> config = new HashMap<>();
        
        try {
            // 一次查询获取所有E3配置
            QFilter configFilter = new QFilter("number", QCP.in, new String[]{E3_IP_KEY, E3_KEY_KEY, E3_SECRET_KEY});
            DynamicObject[] configObjs = BusinessDataServiceHelper.load(E3_CONFIG_TABLE, "number," + E3_CONFIG_FIELD, configFilter.toArray());
            
            // 遍历结果并映射到配置Map
            for (DynamicObject configObj : configObjs) {
                String number = configObj.getString("number");
                String value = configObj.getString(E3_CONFIG_FIELD);
                
                if (E3_IP_KEY.equals(number)) {
                    config.put("baseUrl", value);
                } else if (E3_KEY_KEY.equals(number)) {
                    config.put("key", value);
                } else if (E3_SECRET_KEY.equals(number)) {
                    config.put("secret", value);
                }
            }
            
            // 验证是否获取到所有必需的配置
            if (!config.containsKey("baseUrl") || !config.containsKey("key") || !config.containsKey("secret")) {
                throw new RuntimeException("E3配置不完整，缺少必要的配置项");
            }
            
        } catch (Exception e) {
            log.error("获取E3配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("E3配置获取失败", e);
        }
        
        return config;
    }

    /**
     * 根据配置键获取E3配置值
     * @param configKey 配置键（TCBJ_E3_IP、TCBJ_E3_KEY、TCBJ_E3_SECRET）
     * @return 配置值
     * @throws RuntimeException 当配置获取失败时抛出异常
     */
    public static String getE3ConfigValue(String configKey) {
        try {
            QFilter filter = new QFilter("number", QCP.equals, configKey);
            DynamicObject configObj = BusinessDataServiceHelper.loadSingle(E3_CONFIG_TABLE, E3_CONFIG_FIELD, filter.toArray());
            return configObj.getString(E3_CONFIG_FIELD);
        } catch (Exception e) {
            log.error("获取E3配置项 {} 失败: {}", configKey, e.getMessage(), e);
            throw new RuntimeException("E3配置项获取失败: " + configKey, e);
        }
    }

    /**
     * 验证E3配置是否完整
     * @param config E3配置Map
     * @return 是否有效
     */
    public static boolean validateE3Config(Map<String, String> config) {
        if (config == null) {
            return false;
        }
        
        String baseUrl = config.get("baseUrl");
        String key = config.get("key");
        String secret = config.get("secret");
        
        return Objects.nonNull(baseUrl) && !baseUrl.trim().isEmpty() &&
               Objects.nonNull(key) && !key.trim().isEmpty() &&
               Objects.nonNull(secret) && !secret.trim().isEmpty();
    }

}

