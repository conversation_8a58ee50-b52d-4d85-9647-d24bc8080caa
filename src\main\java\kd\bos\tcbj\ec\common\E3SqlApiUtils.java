package kd.bos.tcbj.ec.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class E3SqlApiUtils {
    private static final Log log = LogFactory.getLog(E3SqlApiUtils.class.getName());

    private static final String MAIYOU_API_URL = "http://39.100.118.17/myapi/storage/sqlhelper.php";
    private static final String BAIYUE_API_URL = "http://47.92.195.153/dbapi/sqlhelper.php";

    /**
     * E3数据库通用接口请求
     * @param sqlStr SQL查询字符串
     * @return 处理后的响应字符串
     */
    public static String sqlExec(String sqlStr) {
        return sqlExec(sqlStr, false); // 默认使用麦油API
    }

    /**
     * E3数据库通用接口请求
     * @param sqlStr SQL查询字符串
     * @param useBaiyue 是否使用百跃API
     * @return 处理后的响应字符串
     */
    public static String sqlExec(String sqlStr, boolean useBaiyue) {
        // 根据参数选择使用哪个API URL
        String apiUrl = useBaiyue ? BAIYUE_API_URL : MAIYOU_API_URL;
        log.info("E3数据库通用接口使用API: {}", apiUrl);
        
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("sql_query", sqlStr);
        log.info("E3数据库通用接口请求参数: {}", paramMap);
        
        String responseStr = "";
        try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl);
            
            List<NameValuePair> nameValuePairs = new ArrayList<>();
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                nameValuePairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
            }
            
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(nameValuePairs, "UTF-8");
            httpPost.setEntity(entity);
            
            HttpResponse response = httpclient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            responseStr = EntityUtils.toString(responseEntity, "UTF-8");
            
        } catch (Exception e) {
            log.error("E3数据库通用接口请求异常: {}", e.getMessage(), e);
            throw new RuntimeException("E3数据库通用接口请求失败", e);
        }
        
        log.info("E3数据库通用接口返回数据: {}", responseStr);
        return responseStr.substring(responseStr.indexOf("{"));
    }

    /**
     * E3数据库通用接口请求（新，用fastjson，才可以转LocalDateTime）
     * @param sqlStr sql查询语句
     * @param type 返回数据类型
     * @param <T> 泛型参数
     * @return 转换后的对象
     */
    public static <T> T sqlExec(String sqlStr, TypeReference<T> type) {
        return sqlExec(sqlStr, type, false); // 默认使用麦油API
    }

    /**
     * E3数据库通用接口请求（新，用fastjson，才可以转LocalDateTime）
     * @param sqlStr sql查询语句
     * @param type 返回数据类型
     * @param useBaiyue 是否使用百跃API
     * @param <T> 泛型参数
     * @return 转换后的对象
     */
    public static <T> T sqlExec(String sqlStr, TypeReference<T> type, boolean useBaiyue) {
        String responseStr = sqlExec(sqlStr, useBaiyue);
        T response = null;
        try {
            response = JSON.parseObject(responseStr, type);
        } catch (Exception e) {
            log.error("E3通用查询接口解析JSON异常: {}", e.getMessage(), e);
        }
        return response;
    }
}

