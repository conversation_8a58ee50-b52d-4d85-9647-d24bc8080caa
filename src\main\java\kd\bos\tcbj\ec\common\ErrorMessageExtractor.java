package kd.bos.tcbj.ec.common;

import kd.bos.entity.operate.result.IOperateInfo;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.operate.interaction.InteractionContext;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.tcbj.im.util.StringUtils;

import java.util.List;

/**
 * 智能错误消息提取器
 * 
 * 该工具类用于从多个错误源中智能提取最有用的错误信息，支持多层级错误信息提取。
 * 提取优先级：simpleMessage -> OperationResult.getMessage -> IOperateInfo -> Exception message
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class ErrorMessageExtractor {

    private static final Log log = LogFactory.getLog(ErrorMessageExtractor.class.getName());
    
    /**
     * 错误消息配置类
     */
    public static final class ErrorMessageConfig {
        /** 默认最大消息长度 */
        public static final int DEFAULT_MAX_LENGTH = 800;
        /** 消息截断后缀 */
        public static final String TRUNCATE_SUFFIX = "...";
        /** 默认错误消息 */
        public static final String DEFAULT_ERROR_MESSAGE = "操作失败，请联系管理员";
    }

    /**
     * 从多个错误源中提取最佳错误消息
     * 
     * @param operationResult 操作结果对象
     * @param fallbackException 备用异常对象
     * @param maxLength 最大消息长度
     * @return 提取的错误消息
     */
    public static String extractBestErrorMessage(OperationResult operationResult, Exception fallbackException, int maxLength) {
        log.debug("开始提取错误消息，maxLength: {}", maxLength);
        
        // 第一优先级：尝试从 InteractionContext 获取 simpleMessage
        String simpleMessage = extractSimpleMessage(operationResult);
        if (isValidMessage(simpleMessage)) {
            log.debug("使用 simpleMessage: {}", simpleMessage);
            return formatErrorMessage(simpleMessage, maxLength);
        }
        
        // 第二优先级：OperationResult.getMessage()
        String operationMessage = operationResult != null ? operationResult.getMessage() : null;
        if (isValidMessage(operationMessage)) {
            log.debug("使用 operationResult.getMessage(): {}", operationMessage);
            
            // 第三优先级：结合 IOperateInfo 集合中的消息
            String combinedMessage = combineWithOperateInfoMessages(operationMessage, operationResult);
            return formatErrorMessage(combinedMessage, maxLength);
        }
        
        // 第四优先级：Exception.getMessage()
        String exceptionMessage = fallbackException != null ? fallbackException.getMessage() : null;
        if (isValidMessage(exceptionMessage)) {
            log.debug("使用 Exception.getMessage(): {}", exceptionMessage);
            return formatErrorMessage("处理异常: " + exceptionMessage, maxLength);
        }
        
        // 兜底策略：默认错误消息
        log.warn("所有错误消息源都无效，使用默认错误消息");
        return ErrorMessageConfig.DEFAULT_ERROR_MESSAGE;
    }

    /**
     * 从 OperationResult 中提取 simpleMessage
     * 
     * @param operationResult 操作结果对象
     * @return simpleMessage 或 null
     */
    private static String extractSimpleMessage(OperationResult operationResult) {
        if (operationResult == null) {
            return null;
        }
        
        try {
            InteractionContext interactionContext = operationResult.getInteractionContext();
            if (interactionContext != null) {
                String simpleMessage = interactionContext.getSimpleMessage();
                log.debug("从 InteractionContext 提取到 simpleMessage: {}", simpleMessage);
                return simpleMessage;
            }
        } catch (Exception e) {
            log.warn("提取 simpleMessage 时发生异常: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 结合 OperationResult 消息和 IOperateInfo 消息
     * 
     * @param operationMessage 操作消息
     * @param operationResult 操作结果对象
     * @return 组合后的错误消息
     */
    private static String combineWithOperateInfoMessages(String operationMessage, OperationResult operationResult) {
        StringBuilder combinedMessage = new StringBuilder(operationMessage);
        
        try {
            List<IOperateInfo> operateInfos = operationResult.getAllErrorOrValidateInfo();
            if (operateInfos != null && !operateInfos.isEmpty()) {
                combinedMessage.append(",");
                for (IOperateInfo errInfo : operateInfos) {
                    String infoMessage = errInfo.getMessage();
                    if (isValidMessage(infoMessage)) {
                        combinedMessage.append(infoMessage).append(",");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("提取 IOperateInfo 消息时发生异常: {}", e.getMessage(), e);
        }
        
        return combinedMessage.toString();
    }

    /**
     * 格式化错误消息，处理长度限制
     * 
     * @param message 原始消息
     * @param maxLength 最大长度
     * @return 格式化后的消息
     */
    private static String formatErrorMessage(String message, int maxLength) {
        if (!isValidMessage(message)) {
            return ErrorMessageConfig.DEFAULT_ERROR_MESSAGE;
        }
        
        String trimmedMessage = message.trim();
        
        // 处理长度限制
        if (maxLength > 0 && trimmedMessage.length() > maxLength) {
            int truncateLength = maxLength - ErrorMessageConfig.TRUNCATE_SUFFIX.length();
            if (truncateLength > 0) {
                trimmedMessage = trimmedMessage.substring(0, truncateLength) + ErrorMessageConfig.TRUNCATE_SUFFIX;
            } else {
                trimmedMessage = trimmedMessage.substring(0, maxLength);
            }
            log.debug("消息已截断到 {} 字符", maxLength);
        }
        
        return trimmedMessage;
    }

    /**
     * 验证消息是否有效
     * 
     * @param message 待验证的消息
     * @return 是否有效
     */
    private static boolean isValidMessage(String message) {
        return StringUtils.isNotBlank(message);
    }
} 