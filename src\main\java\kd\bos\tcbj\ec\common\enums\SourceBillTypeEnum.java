package kd.bos.tcbj.ec.common.enums;

/**
 * 来源单据类型枚举
 * 用于标识销售出库单的来源单据类型
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public enum SourceBillTypeEnum {

    /**
     * 批发退货单
     */
    WHOLESALE_RETURN_BILL(1, "批发退货单"),
    
    /**
     * 批发通知单
     */
    WHOLESALE_NOTICE_BILL(2, "批发通知单"),
    
    /**
     * PCP销售出库单
     */
    PCP_SALES_OUTBOUND_BILL(3, "PCP销售出库单"),
    
    /**
     * PCP库存调拨单
     */
    PCP_INVENTORY_TRANSFER_BILL(4, "PCP库存调拨单"),
    
    /**
     * 其他出库单
     */
    OTHER_OUTBOUND_BILL(5, "其他出库单"),
    
    /**
     * 发货明细
     */
    DELIVERY_INSTRUCTION(6, "发货明细"),
    
    /**
     * 批发销货单
     */
    WHOLESALE_SALES_BILL(7, "批发销货单");
    
    private final int code;
    private final String description;
    
    /**
     * 构造方法
     * @param code 类型编码
     * @param description 类型描述
     */
    SourceBillTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 获取类型编码
     * @return 类型编码
     */
    public int getCode() {
        return code;
    }
    
    /**
     * 获取类型描述
     * @return 类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据编码获取枚举实例
     * @param code 类型编码
     * @return 枚举实例，如果找不到则返回null
     */
    public static SourceBillTypeEnum fromCode(int code) {
        for (SourceBillTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 验证编码是否有效
     * @param code 类型编码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        return fromCode(code) != null;
    }
} 