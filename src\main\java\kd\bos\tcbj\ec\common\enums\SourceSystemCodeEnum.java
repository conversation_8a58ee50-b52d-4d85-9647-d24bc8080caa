package kd.bos.tcbj.ec.common.enums;

/**
 * 源系统编码枚举
 * 专门用于管理 yd_sourcesys 字段的取值
 * 提供类型安全的源系统编码访问
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public enum SourceSystemCodeEnum {
    
    /**
     * E3系统
     */
    E3("1", "E3"),
    
    /**
     * 旺店通系统
     */
    WANGDIANTONG("2", "旺店通"),
    
    /**
     * 吉客云系统
     */
    JIKEYUN("3", "吉客云"),
    
    /**
     * 万里牛系统
     */
    WANLINIU("4", "万里牛"),
    
    /**
     * 新E3系统
     */
    NEW_E3("5", "新E3");
    
    private final String value;
    private final String description;
    
    /**
     * 构造方法
     * @param value 源系统编码值
     * @param description 源系统描述
     */
    SourceSystemCodeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
    
    /**
     * 获取源系统编码值
     * @return 编码值
     */
    public String getValue() {
        return value;
    }
    
    /**
     * 获取源系统描述
     * @return 系统描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据编码值获取枚举实例
     * @param value 编码值
     * @return 枚举实例，如果找不到则返回null
     */
    public static SourceSystemCodeEnum fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (SourceSystemCodeEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 验证编码值是否有效
     * @param value 编码值
     * @return 是否有效
     */
    public static boolean isValidValue(String value) {
        return fromValue(value) != null;
    }
} 