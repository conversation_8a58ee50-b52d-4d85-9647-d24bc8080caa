package kd.bos.tcbj.ec.common.enums;

/**
 * 来源系统枚举
 * 用于标识单据的来源系统
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public enum SourceSystemEnum {
    
    /**
     * E3系统
     */
    E3(1, "E3系统"),
    
    /**
     * 苍穹财务系统-旺店通
     */
    CANGQIONG_WDT(2, "苍穹财务系统-旺店通"),
    
    /**
     * 苍穹财务系统-旺店通
     */
    CANGQIONG_WDT_DUPLICATE(3, "苍穹财务系统-旺店通"),
    
    /**
     * PCP系统
     */
    PCP(4, "PCP"),
    
    /**
     * 万里牛系统
     */
    WANLINIU(5, "万里牛");
    
    private final int code;
    private final String description;
    
    /**
     * 构造方法
     * @param code 系统编码
     * @param description 系统描述
     */
    SourceSystemEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 获取系统编码
     * @return 系统编码
     */
    public int getCode() {
        return code;
    }
    
    /**
     * 获取系统描述
     * @return 系统描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据编码获取枚举实例
     * @param code 系统编码
     * @return 枚举实例，如果找不到则返回null
     */
    public static SourceSystemEnum fromCode(int code) {
        for (SourceSystemEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 验证编码是否有效
     * @param code 系统编码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        return fromCode(code) != null;
    }
} 