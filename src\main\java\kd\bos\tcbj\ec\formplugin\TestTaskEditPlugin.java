package kd.bos.tcbj.ec.formplugin;

import java.util.Date;
import java.util.EventObject;
import java.util.Map;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.IFormView;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.ec.servicehelper.PushWholesalenotice2SaloutbillService;
import kd.bos.tcbj.ec.servicehelper.SyncE3TransferOutOrderService;
import kd.bos.yd.tcyp.ABillServiceHelper;
import kd.bos.yd.tcyp.ApiFf;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

import java.util.Map;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import kd.bos.tcbj.ec.servicehelper.PushWholesalereturn2SaloutbillService;
import json.JSONArray;
import json.JSONObject;

public class TestTaskEditPlugin extends AbstractFormPlugin {
    
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.addItemClickListeners(new String[] { "tbmain" });// 菜单栏标识
    }

    @Override
	public void itemClick(ItemClickEvent evt) {
		super.itemClick(evt);
		String pname = evt.getItemKey();
		switch (pname) {
		    case "yd_btntest":
                Map<String, Object> map = new HashMap<>();
                // map.put("json", "{\"startTime\":\"2025-01-01 00:00:00\",\"endTime\":\"2025-01-31 23:59:59\",\"numberList\":[\"BJYCRKD2025012200001\"]}");
                // SyncE3TransferOutOrderService service = new SyncE3TransferOutOrderService();
                // service.syncTransferOutOrderData(map);

                // map.put("json", "{\"startBizDate\":\"2025-01-01\",\"endBizDate\":\"2025-12-31\",\"extraWhere\":\"fbillno = 'PFXHD2025051700017_66f6_test'\",\"autoAudit\":true}");
                // PushWholesalenotice2SaloutbillService service = new PushWholesalenotice2SaloutbillService();
                // service.pushWholesalenotice2Saloutbill(map);

                map.put("json", "{\"startBizDate\":\"2025-01-01\",\"endBizDate\":\"2025-12-31\",\"extraWhere\":\"fbillno = 'PFTHD2025032900027_66f6_test2'\",\"autoAudit\":true}");
                PushWholesalereturn2SaloutbillService service2 = new PushWholesalereturn2SaloutbillService();
                service2.pushWholesalereturn2Saloutbill(map);

            break;
        }
    }
    
}
