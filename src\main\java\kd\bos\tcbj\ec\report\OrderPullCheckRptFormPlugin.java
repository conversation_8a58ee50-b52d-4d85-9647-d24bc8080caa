package kd.bos.tcbj.ec.report;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.exception.KDBizException;
import kd.bos.report.events.SortAndFilterEvent;
import kd.bos.report.plugin.AbstractReportFormPlugin;
/**
 * 拉单核对报表插件
 * 用于处理订单拉取检查报表的相关功能，包括排序、过滤和查询前的数据校验
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class OrderPullCheckRptFormPlugin extends AbstractReportFormPlugin {

    @Override
    public void setSortAndFilter(List<SortAndFilterEvent> list) {
        super.setSortAndFilter(list);
        for (SortAndFilterEvent event : list) {
            event.setSort(true);
            event.setFilter(true);
        }
    }

    @Override
	public void beforeQuery(ReportQueryParam queryParam) {  
        super.beforeQuery(queryParam);
        Date beginDate = null;
        Date endDate = null;
        List<FilterItemInfo> filters = queryParam.getFilter().getFilterItems();
        for (FilterItemInfo filterItem : filters) {
            if ("yd_startdate".equals(filterItem.getPropName())) {
                beginDate = filterItem.getDate();
            } else if ("yd_enddate".equals(filterItem.getPropName())) {
                endDate = filterItem.getDate();
            } 
        }
        if (Objects.isNull(beginDate)) {
            throw new KDBizException("请选择开始日期！");
        }
        if (Objects.isNull(endDate)) {
            throw new KDBizException("请选择结束日期！");
        }
        if (endDate.compareTo(beginDate) < 0) {
            throw new KDBizException("结束日期不能小于开始日期！");
        }
        // 计算时间差，检查是否超过31天
        long beginTime = beginDate.getTime();
        long endTime = endDate.getTime();
        int days = (int) ((endTime - beginTime) / (1000 * 60 * 60 * 24));
        if (days >= 31) {
            throw new KDBizException("时间间隔不能超过31天，请缩小日期查询范围！");
        }
        queryParam.getFilter().addFilterItem("yd_startdate", beginDate);
        queryParam.getFilter().addFilterItem("yd_enddate", endDate);
    }
}
