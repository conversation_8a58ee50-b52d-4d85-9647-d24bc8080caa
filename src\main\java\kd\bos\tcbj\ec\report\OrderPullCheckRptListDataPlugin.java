package kd.bos.tcbj.ec.report;

import kd.bos.algo.DataSet;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.pur.constants.PurMatQuotaConstant;
import kd.bos.tcbj.srm.pur.helper.PurSDBalanceRptHelper;
import kd.bos.tcbj.srm.utils.RptUtil;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.tcbj.ec.common.E3SqlApiUtils;
import kd.bos.tcbj.ec.business.E3SqlResult;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import java.math.BigDecimal;
import java.util.Map;
import java.util.ArrayList;
import java.lang.StringBuilder;
import kd.bos.dataentity.entity.DynamicObject;
import java.util.Calendar;

/**
 * 拉单核对报表数据插件
 * 用于处理订单拉取检查报表的数据查询逻辑
 * 使用会话级别临时表存储E3查询结果数据
 *
 * <AUTHOR>
 * @version 1.0
 */
public class OrderPullCheckRptListDataPlugin extends AbstractReportListDataPlugin {

    private static final Log logger = LogFactory.getLog(OrderPullCheckRptListDataPlugin.class);

    private String temporaryTableName;

    @Override
    public DataSet query(ReportQueryParam queryParam, Object arg1) throws Throwable {
        try {
            List<FilterItemInfo> filterInfos = this.getQueryParam().getFilter().getFilterItems();
            List<QFilter> filters = this.getQueryParam().getCustomFilter();

            Date beginDate = null;
            Date endDate = null;
            String orderType = ""; // 全部：2；订单：0；退货：1
            String yd_shareamountitem = ""; //均摊金额判断-》  全部：2；金额一直：1；金额不一致：0；
            String yd_shareshippingfeeitem = ""; //均摊运费判断-》  全部：2；运费一致：1；运费不一致：0；
            String yd_qtyitem = ""; //数量判断-》 全部：2；数量一致：1；数量不一致：0；
            String yd_ordernumitem = ""; //单量判断-》 全部：2；单量一致：1；单量不一致：0；
            String yd_shopcodeitem = ""; //店铺编码（模糊查找）
            Long yd_orgitemid = null; //组织机构

            for (FilterItemInfo filterItem : filterInfos) {
                String itemName = filterItem.getPropName();
                if ("yd_startdate".equals(itemName)) {
                    beginDate = filterItem.getDate();
                } else if ("yd_enddate".equals(itemName)) {
                    endDate = filterItem.getDate();
                }else if ("yd_ordertypeitem".equals(itemName)) {
                    orderType = filterItem.getString();
                }else if ("yd_shareamountitem".equals(itemName)) {
                    yd_shareamountitem = filterItem.getString();
                }else if ("yd_shareshippingfeeitem".equals(itemName)) {
                    yd_shareshippingfeeitem = filterItem.getString();
                }else if ("yd_qtyitem".equals(itemName)) {
                    yd_qtyitem = filterItem.getString();
                }else if ("yd_ordernumitem".equals(itemName)) {
                    yd_ordernumitem = filterItem.getString();
                }else if ("yd_shopcodeitem".equals(itemName)) {
                    yd_shopcodeitem = filterItem.getString();
                }else if ("yd_orgitemid".equals(itemName)) {
                    if (filterItem.getValue() != null) {
                        DynamicObject org = (DynamicObject) filterItem.getValue();
                        if (org != null && org.getPkValue() != null) {
                            yd_orgitemid = Long.parseLong(org.getPkValue().toString());
                        }
                    }
                }
            }

            // 生成动态临时表名
            this.temporaryTableName = generateTemporaryTableName();

            // 创建临时表
            createTemporaryTable();

            String strSQL = buildOrderPullCheckSql(formatDateForBizQuery(beginDate), formatDateForBizQuery(endDate), orderType);

            // 第一次查询 - 使用麦油API (useBaiyue=false)
            E3SqlResult result1 = E3SqlApiUtils.sqlExec(
                    strSQL,
                    new com.alibaba.fastjson.TypeReference<E3SqlResult>() {},
                    false
            );

            // 将第一次查询结果写入汇总表
            writeE3DataToSummaryTable(result1);

            // 第二次查询 - 使用百跃API (useBaiyue=true)
            E3SqlResult result2 = E3SqlApiUtils.sqlExec(
                    strSQL,
                    new com.alibaba.fastjson.TypeReference<E3SqlResult>() {},
                    true
            );

            // 将第二次查询结果写入汇总表
            writeE3DataToSummaryTable(result2);

            // 从临时表查询汇总数据并与业务系统数据进行比较
            String querySQL = buildComparisonSQL(beginDate, endDate, yd_shareamountitem, yd_shareshippingfeeitem, yd_qtyitem, yd_ordernumitem);

            Object[] params = new Object[]{
                    formatDateForBizQuery(beginDate),
                    formatDateForBizQuery(endDate),
                    // 单量过滤条件参数（重复3次）
                    yd_ordernumitem, yd_ordernumitem, yd_ordernumitem,
                    // 均摊金额过滤条件参数（重复3次）
                    yd_shareamountitem, yd_shareamountitem, yd_shareamountitem,
                    // 均摊运费过滤条件参数（重复3次）
                    yd_shareshippingfeeitem, yd_shareshippingfeeitem, yd_shareshippingfeeitem,
                    // 商品数量过滤条件参数（重复3次）
                    yd_qtyitem, yd_qtyitem, yd_qtyitem
            };

            DataSet ds = DB.queryDataSet(this.getClass().toString(), DBRoute.of("scm"), querySQL, params);

            // 应用店铺编码模糊过滤
            if (yd_shopcodeitem != null && !yd_shopcodeitem.trim().isEmpty()) {
                String shopCodeFilter = "yd_shopcode like '%" + yd_shopcodeitem.trim().replace("'", "''") + "%'";
                ds = ds.where(shopCodeFilter);
            }

            // 应用组织机构过滤
            if (yd_orgitemid != null) {
                String orgIdFilter = "yd_orgid = " + yd_orgitemid;
                ds = ds.where(orgIdFilter);
            }

            return ds;
        } finally {
            cleanupTemporaryTable();
        }
    }

    /**
     * 将E3SqlResult数据写入动态命名的临时表
     * @param result E3查询结果
     * @return 受影响的行数
     */
    private int writeE3DataToSummaryTable(E3SqlResult result) {
        try {
            // 检查E3接口返回状态
            if (result.getSuccess() != 1) {
                logger.error("E3接口调用失败，状态码: {}, 消息: {}", result.getSuccess(), result.getMsg());
                return 0;
            }

            // 检查数据是否为空
            if (result.getData() == null || result.getData().isEmpty()) {
                logger.warn("E3接口返回数据为空");
                return 0;
            }

            // 创建批量INSERT SQL语句
            String insertSQL = "INSERT INTO " + temporaryTableName + " " +
                    "(id, khdm, khmc, ordertype, total_order_count, total_share_payment, " +
                    "total_share_shipping_fee, total_goods_number, create_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            // 创建批量参数列表
            List<Object[]> batchParams = new ArrayList<>();

            // 遍历数据并构建参数
            for (Map<String, Object> row : result.getData()) {
                try {
                    // 生成主键ID
                    Long id = DB.genLongId("");

                    // 获取字段值并进行类型转换
                    String khdm = (String) row.get("khdm");
                    String khmc = (String) row.get("khmc");

                    // 转换数值字段
                    Integer ordertype = Integer.valueOf(row.get("ordertype").toString());
                    Integer totalOrderCount = Integer.valueOf(row.get("total_order_count").toString());
                    BigDecimal totalSharePayment = new BigDecimal(row.get("total_share_payment").toString());
                    BigDecimal totalShareShippingFee = new BigDecimal(row.get("total_share_shipping_fee").toString());
                    Integer totalGoodsNumber = Integer.valueOf(row.get("total_goods_number").toString());

                    // 创建参数数组
                    Object[] params = new Object[]{
                            id, khdm, khmc, ordertype, totalOrderCount,
                            totalSharePayment, totalShareShippingFee, totalGoodsNumber
                    };

                    // 添加到批量参数列表
                    batchParams.add(params);

                } catch (NumberFormatException e) {
                    logger.error("数据类型转换异常: {}", e.getMessage(), e);
                    continue;
                }
            }

            // 执行批量插入
            if (!batchParams.isEmpty()) {
                int[] affectedRows = DB.executeBatch(DBRoute.of("scm"), insertSQL, batchParams);
                int totalAffectedRows = 0;
                for (int rows : affectedRows) {
                    totalAffectedRows += rows;
                }
                logger.info("成功写入E3订单退货汇总数据，影响行数: {}", totalAffectedRows);
                return totalAffectedRows;
            }

        } catch (Exception e) {
            logger.error("写入E3数据到汇总表异常: {}", e.getMessage(), e);
        }

        return 0;
    }

    /**
     * 生成动态临时表名，避免多线程冲突
     * @return 基于线程ID和时间戳的唯一表名
     */
    private String generateTemporaryTableName() {
        try {
            long threadId = Thread.currentThread().getId();
            long timestamp = System.currentTimeMillis();
            String tableName = "t_yd_e3summary_tid" + threadId + "_" + timestamp;
            logger.debug("生成动态临时表名: {}", tableName);
            return tableName;
        } catch (Exception e) {
            logger.error("生成临时表名异常: {}", e.getMessage(), e);
            throw new RuntimeException("生成临时表名失败", e);
        }
    }

    /**
     * 创建会话级别临时表
     * 每次查询时都会重新创建，确保数据环境干净
     */
    private void createTemporaryTable() {
        try {
            // 删除可能存在的临时表
            String dropSQL = "DROP TABLE " + temporaryTableName;
            try {
                DB.execute(DBRoute.of("scm"), dropSQL);
                logger.debug("成功删除已存在的临时表: {}", temporaryTableName);
            } catch (Exception dropEx) {
                // 表不存在时的正常情况，记录DEBUG级别日志
                logger.debug("临时表不存在，无需删除: {}", temporaryTableName);
            }

            // 创建新的临时表
            String createSQL = "/*dialect*/CREATE TABLE " + temporaryTableName + " (" +
                    "id BIGINT UNSIGNED NOT NULL COMMENT '主键', " +
                    "khdm VARCHAR(100) NOT NULL COMMENT '客户代码', " +
                    "khmc VARCHAR(200) NOT NULL COMMENT '客户名称', " +
                    "ordertype TINYINT UNSIGNED NOT NULL DEFAULT 2 COMMENT '订单类型：0订单，1退单，2全部', " +
                    "total_order_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '单量', " +
                    "total_share_payment DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '总均摊金额', " +
                    "total_share_shipping_fee DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '总均摊运费', " +
                    "total_goods_number INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品总数量', " +
                    "create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', " +
                    "PRIMARY KEY (id), " +
                    "UNIQUE KEY uk_khdm_khmc_ordertype (khdm, khmc, ordertype)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='e3订单及退货汇总表临时表'";

            DB.execute(DBRoute.of("scm"), createSQL);
            logger.info("成功创建E3订单退货汇总表: {}", temporaryTableName);

        } catch (Exception e) {
            logger.error("创建临时表异常: {}", e.getMessage(), e);
            throw new RuntimeException("创建临时表失败", e);
        }
    }

    /**
     * 构建订单拉单核对报表SQL查询语句
     * @param beginDate 查询开始日期，为null时使用默认值
     * @param endDate 查询结束日期，为null时使用默认值
     * @param orderType 订单类型
     * @return 完整的SQL查询字符串
     */
    private String buildOrderPullCheckSql(Date beginDate, Date endDate, String orderType) {
        // 将开始时间设置为当天0点0分0秒
        long beginTimestamp;
        if (beginDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(beginDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            beginTimestamp = calendar.getTimeInMillis() / 1000;
        } else {
            beginTimestamp = 1749432878L;
        }

        // 将结束时间设置为当天23点59分59秒
        long endTimestamp;
        if (endDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            endTimestamp = calendar.getTimeInMillis() / 1000;
        } else {
            endTimestamp = 1749605678L;
        }

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT ");
        sqlBuilder.append("  khdm, ");
        sqlBuilder.append("  khmc, ");
        sqlBuilder.append("  ordertype, ");
        sqlBuilder.append("  SUM(order_count) AS total_order_count, ");
        sqlBuilder.append("  SUM(total_share_payment) AS total_share_payment, ");
        sqlBuilder.append("  SUM(total_share_shipping_fee) AS total_share_shipping_fee, ");
        sqlBuilder.append("  SUM(total_goods_number) AS total_goods_number ");
        sqlBuilder.append("FROM ( ");

        // 根据orderType添加查询部分
        if ("2".equals(orderType)) {
            // 全部：订单 + 退单
            sqlBuilder.append(buildOrderInfoQuery(beginTimestamp, endTimestamp));
            sqlBuilder.append(" UNION ALL ");
            sqlBuilder.append(buildOrderReturnQuery(beginTimestamp, endTimestamp));
        } else if ("0".equals(orderType)) {
            // 只查询订单
            sqlBuilder.append(buildOrderInfoQuery(beginTimestamp, endTimestamp));
        } else if ("1".equals(orderType)) {
            // 只查询退单
            sqlBuilder.append(buildOrderReturnQuery(beginTimestamp, endTimestamp));
        } else {
            // 默认情况：全部
            sqlBuilder.append(buildOrderInfoQuery(beginTimestamp, endTimestamp));
            sqlBuilder.append(" UNION ALL ");
            sqlBuilder.append(buildOrderReturnQuery(beginTimestamp, endTimestamp));
        }

        sqlBuilder.append(") AS combined ");
        sqlBuilder.append("GROUP BY ");
        sqlBuilder.append("  khdm, ");
        sqlBuilder.append("  khmc, ");
        sqlBuilder.append("  ordertype ");
        sqlBuilder.append("ORDER BY ");
        sqlBuilder.append("  khdm, ");
        sqlBuilder.append("  khmc, ");
        sqlBuilder.append("  ordertype");

        return sqlBuilder.toString();
    }

    /**
     * 构建订单信息查询SQL
     * @param beginTimestamp 开始时间戳
     * @param endTimestamp 结束时间戳
     * @return 订单查询SQL字符串
     */
    private String buildOrderInfoQuery(long beginTimestamp, long endTimestamp) {
        return "  SELECT  " +
                "    t2.khdm, " +
                "    t2.khmc, " +
                "    0 as ordertype, " +
                "    COUNT(DISTINCT t1.order_id) AS order_count, " +
                "    SUM(t3.share_payment) AS total_share_payment, " +
                "    SUM(t3.share_shipping_fee) AS total_share_shipping_fee, " +
                "    SUM(t3.goods_number) AS total_goods_number " +
                "  FROM " +
                "    order_info t1 " +
                "  LEFT JOIN kehu t2 ON t1.sd_id = t2.id " +
                "  LEFT JOIN order_goods t3 ON t3.order_id = t1.order_id " +
                "  WHERE " +
                "    t1.shipping_status = '7' " +
                "    AND t1.shipping_time_ck >= " + beginTimestamp + " " +
                "    AND t1.shipping_time_ck <= " + endTimestamp + " " +
                "  GROUP BY " +
                "    t2.khdm, " +
                "    t2.khmc ";
    }

    /**
     * 构建退单信息查询SQL
     * @param beginTimestamp 开始时间戳
     * @param endTimestamp 结束时间戳
     * @return 退单查询SQL字符串
     */
    private String buildOrderReturnQuery(long beginTimestamp, long endTimestamp) {
        return "  SELECT  " +
                "    t2.khdm, " +
                "    t2.khmc, " +
                "    1 as ordertype, " +
                "    COUNT(DISTINCT t1.return_order_id) AS order_count, " +
                "    SUM(t3.share_payment) AS total_share_payment, " +
                "    0 AS total_share_shipping_fee, " +
                "    SUM(t3.goods_number) AS total_goods_number " +
                "  FROM " +
                "    order_return t1 " +
                "  LEFT JOIN kehu t2 ON t1.sd_id = t2.id " +
                "  LEFT JOIN order_return_goods t3 ON t3.return_order_id = t1.return_order_id " +
                "  WHERE " +
                "    t1.return_shipping_status = '2' " +
                "    AND t1.return_shipping_time_rk >= " + beginTimestamp + " " +
                "    AND t1.return_shipping_time_rk <= " + endTimestamp + " " +
                "  GROUP BY " +
                "    t2.khdm, " +
                "    t2.khmc ";
    }

    /**
     * 构建比较查询SQL语句
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @param yd_shareamountitem 均摊金额过滤条件
     * @param yd_shareshippingfeeitem 均摊运费过滤条件
     * @param yd_qtyitem 数量过滤条件
     * @param yd_ordernumitem 单量过滤条件
     * @return 完整的比较SQL
     */
    private String buildComparisonSQL(Date beginDate, Date endDate, String yd_shareamountitem, String yd_shareshippingfeeitem, String yd_qtyitem, String yd_ordernumitem) {
        try {
            // 参数验证，对于null或空字符串的过滤参数，默认设置为"2"（显示全部）
            if (yd_shareamountitem == null || yd_shareamountitem.trim().isEmpty()) {
                yd_shareamountitem = "2";
            }
            if (yd_shareshippingfeeitem == null || yd_shareshippingfeeitem.trim().isEmpty()) {
                yd_shareshippingfeeitem = "2";
            }
            if (yd_qtyitem == null || yd_qtyitem.trim().isEmpty()) {
                yd_qtyitem = "2";
            }
            if (yd_ordernumitem == null || yd_ordernumitem.trim().isEmpty()) {
                yd_ordernumitem = "2";
            }

            logger.debug("过滤参数 - 均摊金额: {}, 均摊运费: {}, 数量: {}, 单量: {}",
                    yd_shareamountitem, yd_shareshippingfeeitem, yd_qtyitem, yd_ordernumitem);

            String sql = "/*dialect*/SELECT " +
                    "5 as yd_platform, " +
                    "e3.khdm as yd_shopcode, " +
                    "e3.khmc as yd_shopname, " +
                    "e3.ordertype as yd_ordertype, " +
                    "e3.total_order_count as yd_omstotalordernum, " +
                    "COALESCE(biz.total_order_count, 0) as yd_cqtotalordernum, " +
                    "e3.total_share_payment as yd_omsshareamount, " +
                    "COALESCE(biz.total_share_payment, 0) as yd_cqshareamount, " +
                    "e3.total_share_shipping_fee as yd_omssharefreight, " +
                    "COALESCE(biz.total_share_shipping_fee, 0) as yd_cqsharefreight, " +
                    "e3.total_goods_number as yd_omsmattotalqty, " +
                    "COALESCE(biz.total_goods_number, 0) as yd_cqmattotalqty, " +
                    "COALESCE(biz.shoporgid, 0) as yd_orgid, " +
                    "CASE WHEN e3.total_order_count = COALESCE(biz.total_order_count, 0) THEN 0 ELSE 1 END as yd_istotalordernumerror, " +
                    "CASE WHEN e3.total_share_payment = COALESCE(biz.total_share_payment, 0) THEN 0 ELSE 1 END as yd_isshareamount, " +
                    "CASE WHEN e3.total_share_shipping_fee = COALESCE(biz.total_share_shipping_fee, 0) THEN 0 ELSE 1 END as yd_issharefreighterror, " +
                    "CASE WHEN e3.total_goods_number = COALESCE(biz.total_goods_number, 0) THEN 0 ELSE 1 END as yd_ismatqtyerror " +
                    "FROM " + temporaryTableName + " e3 " +
                    "LEFT JOIN ( " +
                    "  SELECT " +
                    "    t1.fk_yd_textfield_dpbh as khdm, " +
                    "    t1.fk_yd_textfield_dpbh as khmc, " +
                    "    t1.fk_yd_checkboxfield_th as ordertype, " +
                    "    MAX(t1.fk_yd_shoporgid) as shoporgid, " +
                    "    COUNT(DISTINCT t1.fid) AS total_order_count, " +
                    "    SUM(t2.fk_yd_shareamount) AS total_share_payment, " +
                    "    SUM(t2.fk_yd_avlogisticscost) AS total_share_shipping_fee, " +
                    "    SUM(t2.fk_yd_e3qty) AS total_goods_number " +
                    "  FROM tk_yd_fhmx t1 " +
                    "  LEFT JOIN tk_yd_fhmxentry t2 ON t1.fid = t2.fid " +
                    "  WHERE t1.fk_yd_combofield_pt = '5' " +
                    "    AND t1.fk_yd_datefield_fhrq >= ? " +
                    "    AND t1.fk_yd_datefield_fhrq <= ? " +
                    "    AND t1.fk_yd_textfield_ddbh != t1.fk_yd_dealcode " +
                    "  GROUP BY t1.fk_yd_textfield_dpbh, t1.fk_yd_checkboxfield_th " +
                    ") biz ON e3.khdm = biz.khdm AND e3.ordertype = biz.ordertype " +
                    "WHERE 1=1 " +
                    "AND (? = '2' OR (? = '1' AND e3.total_order_count = COALESCE(biz.total_order_count, 0)) OR (? = '0' AND e3.total_order_count != COALESCE(biz.total_order_count, 0))) " +
                    "AND (? = '2' OR (? = '1' AND e3.total_share_payment = COALESCE(biz.total_share_payment, 0)) OR (? = '0' AND e3.total_share_payment != COALESCE(biz.total_share_payment, 0))) " +
                    "AND (? = '2' OR (? = '1' AND e3.total_share_shipping_fee = COALESCE(biz.total_share_shipping_fee, 0)) OR (? = '0' AND e3.total_share_shipping_fee != COALESCE(biz.total_share_shipping_fee, 0))) " +
                    "AND (? = '2' OR (? = '1' AND e3.total_goods_number = COALESCE(biz.total_goods_number, 0)) OR (? = '0' AND e3.total_goods_number != COALESCE(biz.total_goods_number, 0))) " +
                    "ORDER BY e3.khdm, e3.khmc, e3.ordertype";

            logger.debug("构建比较查询SQL，包含ordertype关联逻辑和过滤条件: {}", sql);
            return sql;

        } catch (Exception e) {
            logger.error("构建比较SQL异常: {}", e.getMessage(), e);
            throw new RuntimeException("构建比较SQL失败", e);
        }
    }

    /**
     * 清理临时表
     * 确保资源正确释放，即使在异常情况下也不会抛出异常
     */
    private void cleanupTemporaryTable() {
        try {
            if (temporaryTableName != null) {
                String dropSQL = "DROP TABLE " + temporaryTableName;
                try {
                    DB.execute(DBRoute.of("scm"), dropSQL);
                    logger.debug("成功清理临时表: {}", temporaryTableName);
                } catch (Exception dropEx) {
                    // 表不存在时的正常情况，记录DEBUG级别日志
                    logger.debug("临时表不存在，无需清理: {}", temporaryTableName);
                }
            }
        } catch (Exception e) {
            logger.error("清理临时表异常，表名: {}, 错误: {}", temporaryTableName, e.getMessage(), e);
        }
    }

    /**
     * 将Date对象转换为适合业务查询的格式
     * @param date 原始日期
     * @return 转换后的日期，如果输入为null则返回默认值
     */
    private java.sql.Date formatDateForBizQuery(Date date) {
        try {
            if (date == null) {
                // 提供默认日期值
                return new java.sql.Date(1749432878L * 1000);
            }
            return new java.sql.Date(date.getTime());
        } catch (Exception e) {
            logger.error("日期格式转换异常: {}", e.getMessage(), e);
            return new java.sql.Date(1749432878L * 1000);
        }
    }

}
