package kd.bos.tcbj.ec.servicehelper;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.orm.query.QCP;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.im.outbill.helper.WholeSaleNoticeBillMserviceHelper;

/**
 * 批发通知单自动下推销售出库单服务类
 * 
 * 功能描述：
 * - 查询已审核且未生成下游单据的批发通知单
 * - 支持通过JSON参数过滤特定渠道和客户的数据
 * - 支持开始日期过滤，查询指定日期及之后的数据
 * - 自动下推生成销售出库单
 * 
 * @createDate  : 2025-06-09
 * @createAuthor: 黄志强
 * @updateDate  : 2025-06-09
 * @updateAuthor: 黄志强
 */
public class OldWholeSaleNotiToSaleoutService {

    private static final Log log = LogFactory.getLog(OldWholeSaleNotiToSaleoutService.class.getName());

    /**
     * 处理批发通知单自动下推销售出库单
     * 
     * @param params 参数Map，支持json参数进行数据过滤
     * @throws KDException 业务异常
     */
    public void processWholeSaleNotiToSaleout(Map<String, Object> params) throws KDException {
        log.info("开始执行批发通知单自动下推销售出库单服务");
        
        // 获取JSON参数
        // JSON参数示例：
        // yd_bizdate: 开始日期，过滤此日期及之后的数据
        // [
        //   {
        //     "yd_channelno": "CHANNEL001",
        //     "yd_bizdate":"2025-06-09",
        //     "yd_customerno": ["CUST001", "CUST002", "CUST003"]
        //   },
        //   {
        //     "yd_channelno": "CHANNEL002", 
        //     "yd_bizdate":"2025-06-09",
        //     "yd_customerno": ["CUST004", "CUST005"]
        //   }
        // ]
        String jsonParam = (String) params.get("json");
        QFilter billFilters = QFilter.of("yd_targetbillno=?", "");
        billFilters.and(QFilter.of("billstatus=?", "C"));

        // 解析JSON并添加过滤条件
        if (jsonParam != null && !jsonParam.trim().isEmpty()) {
            try {
                log.info("解析JSON参数: {}", jsonParam);
                JSONArray jsonArray = JSONArray.parseArray(jsonParam);
                if (jsonArray != null && jsonArray.size() > 0) {
                    List<QFilter> orConditions = new ArrayList<>();
                    
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObj = jsonArray.getJSONObject(i);
                        String channelNo = jsonObj.getString("yd_channelno");
                        String bizDate = jsonObj.getString("yd_bizdate");
                        JSONArray customerNos = jsonObj.getJSONArray("yd_customerno");
                        
                        // 构建单个JSON对象的条件组
                        QFilter channelFilter = null;
                        QFilter customerFilter = null;
                        QFilter bizdateFilter = null;
                        
                        if (channelNo != null && !channelNo.trim().isEmpty()) {
                            channelFilter = QFilter.of("yd_channelno=?", channelNo);
                        }
                        
                        if (bizDate != null && !bizDate.trim().isEmpty()) {
                            bizdateFilter = QFilter.of("yd_bizdate>=?", bizDate);
                        }
                        
                        if (customerNos != null && customerNos.size() > 0) {
                            List<String> customerList = new ArrayList<>();
                            for (int j = 0; j < customerNos.size(); j++) {
                                String customerNo = customerNos.getString(j);
                                if (customerNo != null && !customerNo.trim().isEmpty()) {
                                    customerList.add(customerNo);
                                }
                            }
                            if (!customerList.isEmpty()) {
                                customerFilter = new QFilter("yd_customerno", QCP.in, customerList);
                            }
                        }
                        
                        // 组合当前JSON对象的条件
                        QFilter currentCondition = null;
                        // 根据当前JSON对象中存在的过滤条件，组合成AND条件
                        // 优先级：三个条件都存在 > 两个条件存在 > 单个条件存在
                        if (channelFilter != null && customerFilter != null && bizdateFilter != null) {
                            // 渠道号、客户号、业务日期三个条件都存在时，使用AND逻辑组合
                            currentCondition = channelFilter.and(customerFilter).and(bizdateFilter);
                        } else if (channelFilter != null && customerFilter != null) {
                            // 渠道号和客户号都存在时，使用AND逻辑组合
                            currentCondition = channelFilter.and(customerFilter);
                        } else if (channelFilter != null && bizdateFilter != null) {
                            // 渠道号和业务日期都存在时，使用AND逻辑组合
                            currentCondition = channelFilter.and(bizdateFilter);
                        } else if (customerFilter != null && bizdateFilter != null) {
                            // 客户号和业务日期都存在时，使用AND逻辑组合
                            currentCondition = customerFilter.and(bizdateFilter);
                        } else if (channelFilter != null) {
                            // 只有渠道号条件存在
                            currentCondition = channelFilter;
                        } else if (customerFilter != null) {
                            // 只有客户号条件存在
                            currentCondition = customerFilter;
                        } else if (bizdateFilter != null) {
                            // 只有业务日期条件存在
                            currentCondition = bizdateFilter;
                        }
                        
                        // 如果当前JSON对象构建了有效的过滤条件，则添加到OR条件列表中
                        if (currentCondition != null) {
                            orConditions.add(currentCondition);
                        }
                    }
                    
                    // 使用OR逻辑组合所有条件
                    if (!orConditions.isEmpty()) {
                        QFilter jsonConditions = orConditions.get(0);
                        for (int i = 1; i < orConditions.size(); i++) {
                            // 基于项目现有模式，构建OR条件字符串
                            String orCondition = "(" + jsonConditions.toString() + ") or (" + orConditions.get(i).toString() + ")";
                            jsonConditions = QFilter.of(orCondition);
                        }
                        billFilters.and(jsonConditions);
                        log.info("已添加JSON过滤条件，共 {} 个OR条件组", orConditions.size());
                    }
                }
            } catch (Exception e) {
                log.error("解析JSON参数失败: {}", e.getMessage(), e);
                throw new KDException("解析JSON参数失败，请检查日期格式(YYYY-MM-DD)：" + e.getMessage());
            }
        } else {
            log.info("未提供JSON参数，使用默认查询条件");
        }

        log.info("开始查询批发通知单数据");
        DynamicObject[] wholeSaleReturnBills = BusinessDataServiceHelper.load("yd_wholesalenoticebill", "id", billFilters.toArray());
        if (wholeSaleReturnBills.length == 0) {
            log.warn("没有满足条件的批发通知单数据");
            throw new KDException("没有满足条件的批发通知单数据！");
        }

        log.info("查询到 {} 条批发通知单数据", wholeSaleReturnBills.length);
        Set<String> idSet = new HashSet<String>();
        for(DynamicObject tempObj : wholeSaleReturnBills) {
            idSet.add(tempObj.getPkValue().toString());
        }

        log.info("开始执行批发通知单下推销售出库单，单据数量: {}", idSet.size());
        WholeSaleNoticeBillMserviceHelper.pushToSaleoutBill(idSet);
        log.info("批发通知单自动下推销售出库单服务执行完成");
    }
} 