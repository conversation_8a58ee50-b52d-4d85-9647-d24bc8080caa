package kd.bos.tcbj.ec.servicehelper;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.IOperateInfo;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDException;
import kd.bos.form.IFormView;
import kd.bos.orm.query.QFilter;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.ABillServiceHelper;
import kd.bos.orm.query.QCP;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.tcbj.im.util.BotpUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.zo.initialvaluefiltration;
import kd.bos.db.tx.TX;
import kd.bos.db.tx.TXHandle;
import kd.bos.tcbj.ec.common.enums.SourceBillTypeEnum;
import kd.bos.tcbj.ec.common.enums.SourceSystemEnum;
import kd.bos.tcbj.ec.common.enums.SourceSystemCodeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;

import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.LinkedHashMap;

/**
 * 批发通知单下推销售出库单服务类
 * 
 * 该服务用于处理批发通知单下推销售出库单的业务逻辑，支持批量处理和JSON参数配置。
 * 支持通过autoAudit参数控制是否自动审核生成的销售出库单。
 * 支持按品牌分组下推功能，每个品牌分组独立生成销售出库单。
 * 参考SyncE3TransferOutOrderService的架构模式实现。
 * 
 * <AUTHOR>
 * @since 2025-05-20
 */
public class PushWholesalenotice2SaloutbillService {

    private static final Log log = LogFactory.getLog(PushWholesalenotice2SaloutbillService.class.getName());
    private static final String FORMID_WHOLESALENOTICEBILL = "yd_wholesalenoticebill";
    private static final String FORMID_SALOUTBILL = "im_saloutbill";

    /**
     * 品牌分组结果类
     * 用于存储每个品牌分组的处理结果
     */
    private static class BrandGroupResult {
        private String brandCode;
        private String brandName;
        private List<DynamicObject> splitEntries;
        private boolean processSuccess;
        private String saloutbillNo;
        private String errorMessage;
        private List<String> entryIds;

        public BrandGroupResult(String brandCode, String brandName, List<DynamicObject> splitEntries) {
            this.brandCode = brandCode;
            this.brandName = brandName;
            this.splitEntries = splitEntries;
            this.processSuccess = false;
            this.saloutbillNo = "";
            this.errorMessage = "";
            this.entryIds = splitEntries.stream()
                    .map(entry -> entry.getPkValue().toString())
                    .collect(Collectors.toList());
        }

        // Getters and setters
        public String getBrandCode() { return brandCode; }
        public String getBrandName() { return brandName; }
        public List<DynamicObject> getSplitEntries() { return splitEntries; }
        public boolean isProcessSuccess() { return processSuccess; }
        public void setProcessSuccess(boolean processSuccess) { this.processSuccess = processSuccess; }
        public String getSaloutbillNo() { return saloutbillNo; }
        public void setSaloutbillNo(String saloutbillNo) { this.saloutbillNo = saloutbillNo; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public List<String> getEntryIds() { return entryIds; }
    }

    /**
     * 品牌处理统计类
     * 用于汇总品牌分组的处理结果
     */
    private static class BrandProcessingSummary {
        private int totalBrandGroups;
        private int successBrandGroups;
        private int failBrandGroups;
        private Map<String, String> brandResults;

        public BrandProcessingSummary() {
            this.totalBrandGroups = 0;
            this.successBrandGroups = 0;
            this.failBrandGroups = 0;
            this.brandResults = new LinkedHashMap<>();
        }

        public void addResult(String brandCode, boolean success, String message) {
            totalBrandGroups++;
            if (success) {
                successBrandGroups++;
            } else {
                failBrandGroups++;
            }
            brandResults.put(brandCode, success ? "成功: " + message : "失败: " + message);
        }

        // Getters
        public int getTotalBrandGroups() { return totalBrandGroups; }
        public int getSuccessBrandGroups() { return successBrandGroups; }
        public int getFailBrandGroups() { return failBrandGroups; }
        public Map<String, String> getBrandResults() { return brandResults; }
    }

    /**
     * 常量定义类
     */
    private static final class Constants {
        // 单据类型常量
        static final String BILL_TYPE_SALOUTBILL = "im_SalOutBill_STD_BT_S";
        static final String BIZTYPE_NUMBER = "210";
        static final String INVSCHEME_NUMBER = "210";
        
        // 来源系统常量和来源单据类型现在使用枚举定义
        // 参见 SourceSystemEnum 和 SourceBillTypeEnum
        
        // 查询状态常量
        static final String BILL_STATUS_CONFIRMED = "C";
        static final String SETTLE_STATUS = "4";
        static final String PLATFORM_CODE = "5";
        static final String DEFAULT_START_DATE = "2025-01-01";
    }

    /**
     * 批发通知单下推销售出库单主方法
     * 支持JSON参数配置，可以指定查询条件、批量大小等参数
     * 
     * @param map 参数映射，支持JSON配置
     * @throws KDException 业务异常
     */
    public void pushWholesalenotice2Saloutbill(Map<String, Object> map) throws KDException {
        
        // JSON参数解析逻辑
        String startBizDate = Constants.DEFAULT_START_DATE;
        String extraWhere = null;
        List<String> filterWholesalenoticebillIds = null;
        boolean useIdFilter = false;
        int batchSize = 50; // 默认批量大小
        boolean autoAudit = true; // 默认自动审核
        
        try {
            String jsonStr = (String) map.get("json");
            log.info("接收到的JSON参数: {}", jsonStr);
            
            // JSON参数示例:
            // {
            //   "startBizDate": "2025-01-01",
            //   "endBizDate": "2025-12-31", 
            //   "extraWhere": "fk_yd_customer = '123' or fk_yd_org = '456'",
            //   "batchSize": 50,
            //   "wholesalenoticebillIds": ["id1", "id2", "id3"],
            //   "autoAudit": true
            // }
            if (jsonStr != null && !jsonStr.trim().isEmpty()) {
                JSONObject jsonObj = JSON.parseObject(jsonStr);
                
                // 解析开始业务日期
                String startBizDateStr = jsonObj.getString("startBizDate");
                if (startBizDateStr != null && !startBizDateStr.trim().isEmpty()) {
                    startBizDate = startBizDateStr.trim();
                }
                
                // 解析额外查询条件
                String extraWhereStr = jsonObj.getString("extraWhere");
                if (extraWhereStr != null && !extraWhereStr.trim().isEmpty()) {
                    extraWhere = extraWhereStr.trim();
                }
                
                // 解析批量大小
                Integer batchSizeInt = jsonObj.getInteger("batchSize");
                if (batchSizeInt != null && batchSizeInt > 0) {
                    batchSize = batchSizeInt;
                }
                
                // 解析是否自动审核
                Boolean autoAuditBool = jsonObj.getBoolean("autoAudit");
                if (autoAuditBool != null) {
                    autoAudit = autoAuditBool;
                }
                
                // 解析批发通知单ID列表
                JSONArray idArray = jsonObj.getJSONArray("wholesalenoticebillIds");
                if (idArray != null && !idArray.isEmpty()) {
                    filterWholesalenoticebillIds = new ArrayList<>();
                    for (int i = 0; i < idArray.size(); i++) {
                        String id = idArray.getString(i);
                        if (id != null && !id.trim().isEmpty()) {
                            filterWholesalenoticebillIds.add(id.trim());
                        }
                    }
                    if (!filterWholesalenoticebillIds.isEmpty()) {
                        useIdFilter = true;
                        log.info("解析到 {} 个批发通知单ID进行过滤", filterWholesalenoticebillIds.size());
                    }
                }
                
                log.info("解析后的参数 - 开始日期: {}, 额外条件: {}, 批量大小: {}, 启用ID过滤: {}, 自动审核: {}", 
                        startBizDate, extraWhere, batchSize, useIdFilter, autoAudit);
            } else {
                // 从map中获取传统参数（兼容性处理）
                if (map.containsKey("startBizDate")) {
                    startBizDate = (String) map.get("startBizDate");
                }
                if (map.containsKey("extraWhere")) {
                    extraWhere = (String) map.get("extraWhere");
                }
                log.warn("JSON参数为空，使用传统参数或默认值");
            }
        } catch (Exception e) {
            log.warn("解析JSON参数失败，使用默认参数: {}", e.getMessage(), e);
        }

        // 获取批发通知单ID列表
        List<String> wholesalenoticebillIds;
        if (useIdFilter && filterWholesalenoticebillIds != null && !filterWholesalenoticebillIds.isEmpty()) {
            wholesalenoticebillIds = filterWholesalenoticebillIds;
        } else {
            Map<String, Object> queryParams = map;
            if (extraWhere != null) {
                queryParams.put("extraWhere", extraWhere);
            }
            queryParams.put("startBizDate", startBizDate);
            wholesalenoticebillIds = queryWholesalenoticebillIds(queryParams);
        }
        
        if (wholesalenoticebillIds.isEmpty()) {
            log.info("未查询到符合条件的批发通知单，处理结束");
            return;
        }

        log.info("开始处理 {} 个批发通知单", wholesalenoticebillIds.size());

        // 分批处理
        List<List<String>> batches = partitionList(wholesalenoticebillIds, batchSize);
        int totalCount = wholesalenoticebillIds.size();
        int successCount = 0;
        int failCount = 0;

        for (int batchIndex = 0; batchIndex < batches.size(); batchIndex++) {
            List<String> batch = batches.get(batchIndex);
            log.info("处理第 {}/{} 批，包含 {} 个批发通知单", batchIndex + 1, batches.size(), batch.size());
            
            int[] result = processBatch(batch, autoAudit);
            successCount += result[0];
            failCount += result[1];
        }

        logProcessingSummary(totalCount, successCount, failCount);
    }

    /**
     * 查询符合条件的批发通知单ID列表
     * @param map 查询参数
     * @return 批发通知单ID列表
     */
    private List<String> queryWholesalenoticebillIds(Map<String, Object> map) {
        // 获取批发通知单
        String sql = "select fid from tk_yd_wholesalenoticebill where fbillstatus = ? "
                + "and fk_yd_settlestatus = ? and fk_yd_platform = ? and fk_yd_bizdate >= ?";
        
        // 从map中获取额外的查询条件
        if (map != null && map.containsKey("extraWhere")) {
            sql += " and (" + map.get("extraWhere") + ")";
        }
        
        List<String> wholesalenoticebillIds = new ArrayList<>();
        
        // 从map中获取日期，如果没有则使用默认值
        String startBizDate = Constants.DEFAULT_START_DATE;
        if (map != null && map.containsKey("startBizDate")) {
            startBizDate = (String) map.get("startBizDate");
        }
        
        try (DataSet ds = DB.queryDataSet(this.getClass().toString(), DBRoute.of("scm"), sql, 
                new Object[]{Constants.BILL_STATUS_CONFIRMED, Constants.SETTLE_STATUS, Constants.PLATFORM_CODE, startBizDate})) {
            while (ds != null && ds.hasNext()) {
                Row row = ds.next();
                wholesalenoticebillIds.add(row.getString("fid"));
            }
        }
        
        return wholesalenoticebillIds;
    }

    /**
     * 分批处理批发通知单列表
     * @param batch 批发通知单ID列表
     * @param autoAudit 是否自动审核
     * @return int数组，[0]成功数量，[1]失败数量
     */
    private int[] processBatch(List<String> batch, boolean autoAudit) {
        int successCount = 0;
        int failCount = 0;
        
        for (String wholesalenoticebillId : batch) {
            try {
                DynamicObject wholesalenoticebill = BusinessDataServiceHelper.loadSingle(wholesalenoticebillId, FORMID_WHOLESALENOTICEBILL);
                if (wholesalenoticebill != null) {
                    processSingleWholesalenotice(wholesalenoticebill, autoAudit);
                    successCount++;
                } else {
                    log.warn("未找到批发通知单，ID: {}", wholesalenoticebillId);
                    failCount++;
                }
            } catch (Exception e) {
                handleProcessException(wholesalenoticebillId, e);
                failCount++;
            }
        }
        
        return new int[]{successCount, failCount};
    }

    /**
     * 统一处理单个批发通知单的处理异常
     * @param wholesalenoticebillId 批发通知单ID
     * @param e 异常信息
     */
    private void handleProcessException(String wholesalenoticebillId, Exception e) {
        log.error("处理批发通知单ID: {} 时发生异常: {}", wholesalenoticebillId, e.getMessage(), e);
    }

    /**
     * 查询客户对应的税率
     * @param customer 客户对象
     * @return 税率编号
     */
    private String loadCustomerTaxRate(DynamicObject customer) {
        String customerRateNum = "";
        if (customer != null && StringUtils.isNotBlank(customer.getString("number"))) {
            QFilter cusFilter = new QFilter("number", QCP.equals, customer.getString("number"));
            DynamicObject customerObj = BusinessDataServiceHelper.loadSingle("bd_customer", "taxrate.number",
                    new QFilter[] { cusFilter });
            if (customerObj != null && customerObj.get("taxrate.number") != null) {
                customerRateNum = customerObj.getString("taxrate.number");
            }
        }
        return customerRateNum;
    }

    /**
     * 创建销售出库单视图并设置基本字段
     * @param wholesalenoticebill 批发通知单
     * @param customerTaxRate 客户税率
     * @return 配置好的视图对象
     */
    private IFormView createSaloutbillView(DynamicObject wholesalenoticebill, String customerTaxRate) {
        // 创建销售出库单视图并设置基本字段
        IFormView view = ABillServiceHelper.createAddView(FORMID_SALOUTBILL);
        
        setupBasicFields(view, wholesalenoticebill);
        setupSourceInfo(view, wholesalenoticebill);
        
        return view;
    }

    /**
     * 设置销售出库单的基本字段信息
     * @param view 销售出库单视图
     * @param wholesalenoticebill 批发通知单
     */
    private void setupBasicFields(IFormView view, DynamicObject wholesalenoticebill) {
        // 设置单据类型
        view.getModel().setItemValueByNumber("billtype", Constants.BILL_TYPE_SALOUTBILL);
        
        // 设置组织信息
        DynamicObject org = wholesalenoticebill.getDynamicObject("yd_org");
        view.getModel().setValue("org", org);
        view.getModel().setValue("bizorg", org);
        
        // 设置业务类型和库存方案
        view.getModel().setItemValueByNumber("biztype", Constants.BIZTYPE_NUMBER);
        view.getModel().setItemValueByNumber("invscheme", Constants.INVSCHEME_NUMBER);
        
        // 设置业务日期
        view.getModel().setValue("biztime", wholesalenoticebill.getDate("yd_bizdate"));
        
        // 设置客户
        DynamicObject customer = wholesalenoticebill.getDynamicObject("yd_cqcustomer");
        view.getModel().setValue("customer", customer);
        
        // 设置备注
        view.getModel().setValue("comment", wholesalenoticebill.getString("yd_description"));
        
        // 设置是否来源CSP
        String e3Creator = wholesalenoticebill.getString("yd_e3creator");
        boolean isFromCsp = "CSP".equalsIgnoreCase(e3Creator);
        view.getModel().setValue("yd_isfromcsp", isFromCsp);
        
        // 设置是否退货
        boolean isReturn = false;
        view.getModel().setValue("yd_isreturn", isReturn);
        
        // 设置SAP单据类型
        String sapBillType = isReturn ? "ZREC" : "ZORC";
        view.getModel().setValue("yd_sapbilltype", sapBillType);
    }

    /**
     * 设置来源系统相关信息
     * 使用统一的枚举定义来设置来源系统和来源单据类型，提高代码维护性
     * @param view 销售出库单视图
     * @param wholesalenoticebill 批发通知单
     */
    private void setupSourceInfo(IFormView view, DynamicObject wholesalenoticebill) {
        // 来源系统(E3) - 使用SourceSystemEnum枚举
        view.getModel().setValue("yd_tbly", SourceSystemEnum.E3.getCode());
        // 来源单据类型 - 使用SourceBillTypeEnum枚举
        view.getModel().setValue("yd_sourcebilltype", SourceBillTypeEnum.WHOLESALE_SALES_BILL.getCode());
        // 通知单号
        view.getModel().setValue("yd_dealcode", wholesalenoticebill.getString("yd_noticebillno"));
        // sap单号
        view.getModel().setValue("yd_sapbillno", wholesalenoticebill.getString("yd_sap_billlno"));
        // 来源系统
        view.getModel().setValue("yd_sourcesys", SourceSystemCodeEnum.NEW_E3.getValue());
    }

    /**
     * 清空现有分录数据
     * @param view 销售出库单视图
     */
    private void clearExistingEntries(IFormView view) {
        DynamicObject bill = view.getModel().getDataEntity();
        DynamicObjectCollection entries = bill.getDynamicObjectCollection("billentry");
        for (int i = entries.size() - 1; i >= 0; i--) {
            view.getModel().deleteEntryRow("billentry", i);
        }
    }

    /**
     * 验证拆单明细是否有效（非排除物料）
     * @param splitEntry 拆单明细
     * @return 是否有效
     */
    private boolean isValidSplitEntry(DynamicObject splitEntry) {
        return !splitEntry.getBoolean("yd_excludematsplit");
    }

    /**
     * 根据序号查找匹配的OMS明细
     * @param omsEntryCol OMS明细集合
     * @param omsSeq 序号
     * @return OMS明细对象
     */
    private DynamicObject findMatchingOmsEntry(DynamicObjectCollection omsEntryCol, Integer omsSeq) {
        List<DynamicObject> omsEntryList = omsEntryCol.stream().collect(Collectors.toList());
        return omsEntryList.stream().filter(x -> x.getInt("seq") == omsSeq).findFirst().orElse(null);
    }

    /**
     * 处理单个分录的数据设置
     * @param view 销售出库单视图
     * @param splitEntry 拆单明细
     * @param omsEntryCol OMS明细集合
     * @param wholesalenoticebill 批发通知单
     * @param customerTaxRate 客户税率
     * @param rowIndex 行索引
     */
    private void processSingleEntry(IFormView view, DynamicObject splitEntry, DynamicObjectCollection omsEntryCol, 
                                  DynamicObject wholesalenoticebill, String customerTaxRate, int rowIndex) {
        // 设置物料
        Object matId = splitEntry.getDynamicObject("yd_splitmaterial").getPkValue();
        DynamicObject materialObj = BusinessDataServiceHelper.loadSingle(matId, "bd_material");
        String matNumber = splitEntry.getDynamicObject("yd_splitmaterial").getString("number");
        view.getModel().setItemValueByNumber("material", matNumber, rowIndex);
        // 基本计量单位
        view.getModel().setValue("baseunit", materialObj.getDynamicObject("baseunit"), rowIndex);
        // 计量单位
        view.getModel().setValue("unit", materialObj.getDynamicObject("baseunit"), rowIndex);
        // 销售组织仓库编码
        view.getModel().setValue("warehouse", splitEntry.getDynamicObject("yd_splitsalorgstock"), rowIndex);
        // 库存组织仓库编码
        view.getModel().setValue("yd_invorgstock", splitEntry.getDynamicObject("yd_splitinvorgstock"), rowIndex);
        // 设置数量
        view.getModel().setValue("qty", splitEntry.getBigDecimal("yd_splitqty"), rowIndex);
        // 含税单价
        view.getModel().setValue("priceandtax", splitEntry.getBigDecimal("yd_splitprice"), rowIndex);
        // 价税合计
        view.getModel().setValue("amountandtax", splitEntry.getBigDecimal("yd_totaltaxamt"), rowIndex);
        // 是否赠品
        view.getModel().setValue("ispresent", splitEntry.getBoolean("yd_isgift"), rowIndex);
        // 设置税率
        if (customerTaxRate != null) {
            view.getModel().setItemValueByNumber("taxrateid", customerTaxRate, rowIndex);
        }

        // 序号(OMS明细)
        Integer omsSeq = splitEntry.getInt("yd_omsrownum");
        DynamicObject omsEntry = findMatchingOmsEntry(omsEntryCol, omsSeq);
        if (omsEntry != null) {
            // 是否组套
            view.getModel().setValue("yd_isbom", omsEntry.getBoolean("yd_isbom"), rowIndex);
            if(omsEntry.getBoolean("yd_isbom")){                
                // 组套编码
                view.getModel().setValue("yd_bomnum", omsEntry.getString("yd_goodsnum"), rowIndex);
            }
            // sap行号
            view.getModel().setValue("yd_saprowno", omsEntry.getString("yd_sap_rowindex"), rowIndex);
        }

        // 行类型
        // 是否退货=否且是否赠品=否，取 TAN：常规销售类别
        // 是否退货=否且是否赠品=是，取 TANN：免费行
        // 是否退货=是且是否赠品=否，取 REN：退货行
        // 是否退货=是且是否赠品=是，取 RENN：免费行退货
        boolean yd_isreturn = false;
        if (yd_isreturn == false && splitEntry.getBoolean("yd_isgift") == false) {
            view.getModel().setValue("yd_rowtype", "TAN", rowIndex);
        }else if (yd_isreturn == false && splitEntry.getBoolean("yd_isgift") == true) {
            view.getModel().setValue("yd_rowtype", "TANN", rowIndex);
        }else if (yd_isreturn == true && splitEntry.getBoolean("yd_isgift") == false) {
            view.getModel().setValue("yd_rowtype", "REN", rowIndex);
        }else if (yd_isreturn == true && splitEntry.getBoolean("yd_isgift") == true) {
            view.getModel().setValue("yd_rowtype", "RENN", rowIndex);
        }

        // 记录源单信息
        // 来源系统单据分录ID
        view.getModel().setValue("srcsysbillentryid", splitEntry.getPkValue(), rowIndex);
        // 来源系统单据编号
        view.getModel().setValue("srcsysbillno", wholesalenoticebill.getString("billno"), rowIndex);
        // 来源系统单据ID
        view.getModel().setValue("srcsysbillid", wholesalenoticebill.getPkValue(), rowIndex);
        // 来源单据ID
        view.getModel().setValue("srcbillid", wholesalenoticebill.getPkValue(), rowIndex);
        // 来源单据行ID
        view.getModel().setValue("srcbillentryid", splitEntry.getPkValue(), rowIndex);
        // 来源单据分录序号
        view.getModel().setValue("srcbillentryseq", splitEntry.get("seq"), rowIndex);
        // 来源单据编号
        view.getModel().setValue("srcbillnumber", wholesalenoticebill.getString("billno"), rowIndex);

        // 行ID
        String rowId = splitEntry.getPkValue().toString();

        // 品牌
        DynamicObject brand = splitEntry.getDynamicObject("yd_brand");
        if (brand != null) {
            String brandCode = brand.getString("number");
        }
    }

    /**
     * 根据批发通知单拆单明细创建销售出库单分录
     * @param view 销售出库单视图
     * @param wholesalenoticebill 批发通知单
     * @param customerTaxRate 客户税率
     * @return 创建的分录数量
     */
    private int createSaloutbillEntries(IFormView view, DynamicObject wholesalenoticebill, String customerTaxRate) {
        // OMS明细
        DynamicObjectCollection omsEntryCol = wholesalenoticebill.getDynamicObjectCollection("entryentity");
        // 拆单明细
        DynamicObjectCollection splitEntryCol = wholesalenoticebill.getDynamicObjectCollection("yd_splitentryentity");

        if (omsEntryCol.isEmpty() && splitEntryCol.isEmpty()) {
            return 0;
        }
        
        int entryCount = 0;
        for (int i = 0; i < splitEntryCol.size(); i++) {
            DynamicObject splitEntry = splitEntryCol.get(i);

            // 是否排除物料
            if (!isValidSplitEntry(splitEntry)) {
                continue;
            }

            // 其他的都是一对一生成，直接创建分录
            int rowIndex = view.getModel().createNewEntryRow("billentry");
            processSingleEntry(view, splitEntry, omsEntryCol, wholesalenoticebill, customerTaxRate, rowIndex);
            entryCount++;
        }
        
        return entryCount;
    }

    /**
     * 根据特定品牌的拆单明细创建销售出库单分录
     * @param view 销售出库单视图
     * @param wholesalenoticebill 批发通知单
     * @param brandSplitEntries 特定品牌的拆单明细列表
     * @param customerTaxRate 客户税率
     * @return 创建的分录数量
     */
    private int createSaloutbillEntriesForBrand(IFormView view, DynamicObject wholesalenoticebill, 
                                               List<DynamicObject> brandSplitEntries, String customerTaxRate) {
        // OMS明细
        DynamicObjectCollection omsEntryCol = wholesalenoticebill.getDynamicObjectCollection("entryentity");

        if (brandSplitEntries == null || brandSplitEntries.isEmpty()) {
            return 0;
        }
        
        int entryCount = 0;
        for (DynamicObject splitEntry : brandSplitEntries) {
            // 验证有效性（此处应该已经过滤过，但保险起见再次验证）
            if (!isValidSplitEntry(splitEntry)) {
                continue;
            }

            // 创建分录
            int rowIndex = view.getModel().createNewEntryRow("billentry");
            processSingleEntry(view, splitEntry, omsEntryCol, wholesalenoticebill, customerTaxRate, rowIndex);
            entryCount++;
        }
        
        return entryCount;
    }

    /**
     * 保存提交审核销售出库单
     * @param view 销售出库单视图
     * @param autoAudit 是否自动审核
     * @return 操作结果
     */
    private OperationResult saveSubmitAndAuditSaloutbill(IFormView view, boolean autoAudit) {
        return ABillServiceHelper.saveOperate(view, autoAudit);
    }

    /**
     * 从操作结果中提取单据编号
     * @param operationResult 操作结果
     * @return 单据编号
     */
    private String extractBillNumberFromResult(OperationResult operationResult) {
        String billNo = "";
        Map<Object, String> map = operationResult.getBillNos();
        for (Map.Entry<Object, String> entry : map.entrySet()) {
            billNo = entry.getValue();
            break;
        }
        return billNo;
    }

    /**
     * 创建单据间关联关系
     * @param wholesalenoticebill 批发通知单
     * @param saloutbillId 销售出库单ID
     */
    private void createBillRelation(DynamicObject wholesalenoticebill, String saloutbillId) {
        BotpUtils.createRelation(FORMID_WHOLESALENOTICEBILL, Long.parseLong(wholesalenoticebill.getPkValue().toString()), 
                               FORMID_SALOUTBILL, Long.parseLong(saloutbillId));
    }

    /**
     * 更新批发通知单的下游单据信息
     * 将销售出库单的处理结果反写到批发通知单的拆单明细中
     * 
     * @param wholesalenoticebill 批发通知单对象
     * @param isSuccess 处理是否成功
     * @param saloutbillNo 销售出库单号（成功时填入）
     * @param errorMessage 错误信息（失败时填入）
     */
    private void updateWholesalenoticebill(DynamicObject wholesalenoticebill, Boolean isSuccess, String saloutbillNo, String errorMessage) {
        // 结算状态 - 只有成功时才设置状态为6
        if (isSuccess) {
            wholesalenoticebill.set("yd_settlestatus", 6);
        }
        // 获取批发通知单的拆单明细集合
        DynamicObjectCollection splitEntryCol = wholesalenoticebill.getDynamicObjectCollection("yd_splitentryentity");
        
        // 遍历拆单明细，将下游单据号反写到每个明细行
        for (DynamicObject splitEntry : splitEntryCol) {
            // 只有成功时才赋值销售出库单号
            if (isSuccess) {
                splitEntry.set("yd_downstreambillno", saloutbillNo);
            }
            splitEntry.set("yd_pusherrorreason", errorMessage);
        }
        
        // 持久化保存批发通知单的更新
        SaveServiceHelper.save(new DynamicObject[] {wholesalenoticebill});
    }

    /**
     * 按分录ID精确更新批发通知单的下游单据信息
     * 只更新指定分录ID列表对应的拆单明细状态
     * 
     * @param wholesalenoticebill 批发通知单对象
     * @param entryIds 需要更新的分录ID列表
     * @param isSuccess 处理是否成功
     * @param saloutbillNo 销售出库单号（成功时填入）
     * @param errorMessage 错误信息（失败时填入）
     */
    private void updateWholesalenoticebillByEntries(DynamicObject wholesalenoticebill, List<String> entryIds, 
                                                   Boolean isSuccess, String saloutbillNo, String errorMessage) {
        if (entryIds == null || entryIds.isEmpty()) {
            log.warn("分录ID列表为空，跳过状态更新");
            return;
        }

        // 获取批发通知单的拆单明细集合
        DynamicObjectCollection splitEntryCol = wholesalenoticebill.getDynamicObjectCollection("yd_splitentryentity");
        
        int updatedCount = 0;
        // 遍历拆单明细，只更新匹配的分录
        for (DynamicObject splitEntry : splitEntryCol) {
            String entryId = splitEntry.getPkValue().toString();
            if (entryIds.contains(entryId)) {
                // 只有成功时才赋值销售出库单号
                if (isSuccess) {
                    splitEntry.set("yd_downstreambillno", saloutbillNo);
                }
                splitEntry.set("yd_pusherrorreason", errorMessage);
                updatedCount++;
            }
        }
        
        log.info("精确更新拆单明细状态，目标: {} 个，实际更新: {} 个", entryIds.size(), updatedCount);
        
        // 持久化保存批发通知单的更新
        SaveServiceHelper.save(new DynamicObject[] {wholesalenoticebill});
    }

    /**
     * 处理单个批发通知单下推销售出库单
     * 支持按品牌分组下推，每个品牌分组独立生成销售出库单
     * @param wholesalenoticebill 批发通知单
     * @param autoAudit 是否自动审核
     */
    private void processSingleWholesalenotice(DynamicObject wholesalenoticebill, boolean autoAudit) {
        log.info("开始处理批发通知单: {}", wholesalenoticebill.getString("yd_noticebillno"));

        // 获取拆单明细并按品牌分组
        DynamicObjectCollection splitEntryCol = wholesalenoticebill.getDynamicObjectCollection("yd_splitentryentity");
        if (splitEntryCol.isEmpty()) {
            log.info("批发通知单无拆单明细，跳过处理");
            return;
        }

        Map<String, List<DynamicObject>> brandGroups = groupSplitEntriesByBrand(splitEntryCol);
        if (brandGroups.isEmpty()) {
            log.info("按品牌分组后无有效明细，跳过处理");
            return;
        }

        // 查询客户对应税率
        String customerRateNum = loadCustomerTaxRate(wholesalenoticebill.getDynamicObject("yd_cqcustomer"));

        // 品牌处理统计
        BrandProcessingSummary summary = new BrandProcessingSummary();
        
        // 逐个品牌组处理
        for (Map.Entry<String, List<DynamicObject>> brandEntry : brandGroups.entrySet()) {
            String brandCode = brandEntry.getKey();
            List<DynamicObject> brandSplitEntries = brandEntry.getValue();
            
            log.info("开始处理品牌组: {} 包含 {} 个拆单明细", brandCode, brandSplitEntries.size());
            
            // 创建品牌分组结果对象
            String brandName = getBrandName(brandSplitEntries.get(0));
            BrandGroupResult brandResult = new BrandGroupResult(brandCode, brandName, brandSplitEntries);
            
            // 处理单个品牌组
            processSingleBrandGroup(wholesalenoticebill, brandResult, customerRateNum, autoAudit);
            
            // 记录处理结果
            summary.addResult(brandCode, brandResult.isProcessSuccess(), 
                            brandResult.isProcessSuccess() ? brandResult.getSaloutbillNo() : brandResult.getErrorMessage());
            
            // 精确回写品牌组的处理状态
            updateWholesalenoticebillByEntries(wholesalenoticebill, brandResult.getEntryIds(), 
                                             brandResult.isProcessSuccess(), brandResult.getSaloutbillNo(), 
                                             brandResult.getErrorMessage());
        }

        // 检查是否所有品牌组都处理成功，如果是则更新主单结算状态
        if (summary.getFailBrandGroups() == 0 && summary.getSuccessBrandGroups() > 0) {
            wholesalenoticebill.set("yd_settlestatus", 6);
            SaveServiceHelper.save(new DynamicObject[] {wholesalenoticebill});
            log.info("所有品牌组处理成功，更新主单结算状态为6");
        }

        // 输出处理汇总
        logBrandProcessingSummary(wholesalenoticebill.getString("yd_noticebillno"), summary);
    }

    /**
     * 处理单个品牌组
     * @param wholesalenoticebill 批发通知单
     * @param brandResult 品牌分组结果对象
     * @param customerRateNum 客户税率
     * @param autoAudit 是否自动审核
     */
    private void processSingleBrandGroup(DynamicObject wholesalenoticebill, BrandGroupResult brandResult, 
                                       String customerRateNum, boolean autoAudit) {
        try (TXHandle h = TX.requiresNew()) {
            try {
                // 创建销售出库单视图并设置基本字段
                IFormView view = createSaloutbillView(wholesalenoticebill, customerRateNum);
                
                // 清空现有分录数据
                clearExistingEntries(view);

                // 为当前品牌组创建分录
                int entryCount = createSaloutbillEntriesForBrand(view, wholesalenoticebill, 
                                                               brandResult.getSplitEntries(), customerRateNum);
                
                if (entryCount == 0) {
                    brandResult.setProcessSuccess(false);
                    brandResult.setErrorMessage("品牌组无有效分录");
                    return;
                }

                // 保存提交审核销售出库单
                OperationResult operationResult = saveSubmitAndAuditSaloutbill(view, autoAudit);
                
                if (operationResult.isSuccess()) {
                    log.info("品牌组 {} 销售出库单处理成功", brandResult.getBrandCode());
                    ABillServiceHelper.exitView(view);
                    
                    List<String> saloutbillIds = operationResult.getSuccessPkIds().stream()
                            .map(String::valueOf).collect(Collectors.toList());
                    
                    // 创建关联关系
                    createBillRelation(wholesalenoticebill, saloutbillIds.get(0));

                    // 获取销售出库单号
                    String saloutbillNo = extractBillNumberFromResult(operationResult);
                    
                    // 设置成功结果
                    brandResult.setProcessSuccess(true);
                    brandResult.setSaloutbillNo(saloutbillNo);
                    brandResult.setErrorMessage("");
                    
                } else {
                    String errMessage = operationResult.getMessage() + ",";
                    for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
                        errMessage += errInfo.getMessage() + ",";
                    }
                    if (errMessage.length() > 800) {
                        errMessage = errMessage.substring(0, 800);
                    }

                    // 设置失败结果
                    brandResult.setProcessSuccess(false);
                    brandResult.setErrorMessage(errMessage);
                    
                    log.error("品牌组 {} 销售出库单处理失败: {}", brandResult.getBrandCode(), errMessage);
                }

            } catch (Exception e) {
                h.markRollback();
                throw e;
            }
        } catch (Exception e) {
            // 设置异常结果
            brandResult.setProcessSuccess(false);
            brandResult.setErrorMessage("处理异常: " + e.getMessage());
            log.error("品牌组 {} 处理异常: {}", brandResult.getBrandCode(), e.getMessage(), e);
        }
    }

    /**
     * 从拆单明细中获取品牌名称
     * @param splitEntry 拆单明细
     * @return 品牌名称
     */
    private String getBrandName(DynamicObject splitEntry) {
        DynamicObject brand = splitEntry.getDynamicObject("yd_brand");
        if (brand != null) {
            String name = brand.getString("name");
            if (name != null && !name.trim().isEmpty()) {
                return name.trim();
            }
        }
        return "未知品牌";
    }

    /**
     * 输出品牌处理结果汇总日志
     * @param noticebillNo 批发通知单号
     * @param summary 处理统计
     */
    private void logBrandProcessingSummary(String noticebillNo, BrandProcessingSummary summary) {
        log.info("批发通知单 {} 按品牌分组处理完成 - 总品牌组: {}, 成功: {}, 失败: {}", 
                noticebillNo, summary.getTotalBrandGroups(), summary.getSuccessBrandGroups(), summary.getFailBrandGroups());
        
        for (Map.Entry<String, String> entry : summary.getBrandResults().entrySet()) {
            log.info("品牌 {}: {}", entry.getKey(), entry.getValue());
        }
    }

    /**
     * 将列表分割为指定大小的批次
     * @param list 原始列表
     * @param batchSize 批次大小
     * @return 分批后的列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }

    /**
     * 记录处理结果汇总
     * @param totalCount 总数量
     * @param successCount 成功数量
     * @param failCount 失败数量
     */
    private void logProcessingSummary(int totalCount, int successCount, int failCount) {
        log.info("批发通知单下推销售出库单处理完成 - 总数: {}, 成功: {}, 失败: {}", totalCount, successCount, failCount);
        log.info("注意: 每个批发通知单内部已按品牌分组独立下推，详细结果请查看各单据的品牌组处理日志");
    }

    /**
     * 按品牌对拆单明细进行分组
     * @param splitEntryCol 拆单明细集合
     * @return 品牌分组Map，key为品牌编号，value为该品牌的拆单明细列表
     */
    private Map<String, List<DynamicObject>> groupSplitEntriesByBrand(DynamicObjectCollection splitEntryCol) {
        Map<String, List<DynamicObject>> brandGroups = new LinkedHashMap<>();
        
        for (DynamicObject splitEntry : splitEntryCol) {
            // 验证拆单明细是否有效（非排除物料）
            if (!isValidSplitEntry(splitEntry)) {
                continue;
            }
            
            // 获取品牌信息，用于分组处理
            String brandCode = "UNKNOWN"; // 默认品牌编号，当品牌信息为空时使用
            String brandName = "未知品牌"; // 默认品牌名称，当品牌信息为空时使用
            
            // 从拆单明细中获取品牌对象
            DynamicObject brand = splitEntry.getDynamicObject("yd_brand");
            if (brand != null) {
                // 获取品牌编号
                String code = brand.getString("number");
                // 获取品牌名称
                String name = brand.getString("name");
                
                // 验证品牌编号是否有效，如果有效则使用实际品牌编号
                if (code != null && !code.trim().isEmpty()) {
                    brandCode = code.trim();
                }
                
                // 验证品牌名称是否有效，如果有效则使用实际品牌名称
                if (name != null && !name.trim().isEmpty()) {
                    brandName = name.trim();
                }
            }
            
            // 将拆单明细添加到对应品牌分组
            brandGroups.computeIfAbsent(brandCode, k -> new ArrayList<>()).add(splitEntry);
        }
        
        log.info("按品牌分组完成，共分为 {} 个品牌组", brandGroups.size());
        for (Map.Entry<String, List<DynamicObject>> entry : brandGroups.entrySet()) {
            log.info("品牌: {} 包含 {} 个拆单明细", entry.getKey(), entry.getValue().size());
        }
        
        return brandGroups;
    }
}