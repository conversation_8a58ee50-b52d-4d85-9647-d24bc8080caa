package kd.bos.tcbj.ec.servicehelper;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.IOperateInfo;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDException;
import kd.bos.form.IFormView;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.ec.common.enums.SourceBillTypeEnum;
import kd.bos.tcbj.ec.common.enums.SourceSystemEnum;
import kd.bos.tcbj.ec.common.enums.SourceSystemCodeEnum;
import kd.bos.tcbj.ec.common.ErrorMessageExtractor;
import kd.bos.tcbj.im.helper.ABillServiceHelper;
import kd.bos.orm.query.QCP;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.tcbj.im.util.BotpUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.db.tx.TX;
import kd.bos.db.tx.TXHandle;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import java.text.SimpleDateFormat;
import java.text.ParseException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.stream.Collectors;

/**
 * 批发退货单下推销售出库单服务类
 * 
 * 该服务用于处理批发退货单下推销售出库单的业务逻辑，支持批量处理和JSON参数配置。
 * 支持通过autoAudit参数控制是否自动审核生成的销售出库单。
 * 支持按品牌分组下推功能，每个品牌分组独立生成销售出库单。
 * 参考SyncE3TransferOutOrderService的架构模式实现。
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public class PushWholesalereturn2SaloutbillService {

    private static final Log log = LogFactory.getLog(PushWholesalereturn2SaloutbillService.class.getName());
    private static final String FORMID_WHOLESALERETURNBILL = "yd_wholesalereturnbill";
    private static final String FORMID_SALOUTBILL = "im_saloutbill";

    /**
     * 品牌分组结果类
     * 用于存储每个品牌分组的处理结果
     */
    private static class BrandGroupResult {
        private String brandCode;
        private String brandName;
        private List<DynamicObject> splitEntries;
        private boolean processSuccess;
        private String saloutbillNo;
        private String errorMessage;
        private List<String> entryIds;
        private int brandIndex;

        public BrandGroupResult(String brandCode, String brandName, List<DynamicObject> splitEntries, int brandIndex) {
            this.brandCode = brandCode;
            this.brandName = brandName;
            this.splitEntries = splitEntries;
            this.processSuccess = false;
            this.saloutbillNo = "";
            this.errorMessage = "";
            this.brandIndex = brandIndex;
            this.entryIds = splitEntries.stream()
                    .map(entry -> entry.getPkValue().toString())
                    .collect(Collectors.toList());
        }

        // Getters and setters
        public String getBrandCode() { return brandCode; }
        public String getBrandName() { return brandName; }
        public List<DynamicObject> getSplitEntries() { return splitEntries; }
        public boolean isProcessSuccess() { return processSuccess; }
        public void setProcessSuccess(boolean processSuccess) { this.processSuccess = processSuccess; }
        public String getSaloutbillNo() { return saloutbillNo; }
        public void setSaloutbillNo(String saloutbillNo) { this.saloutbillNo = saloutbillNo; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public List<String> getEntryIds() { return entryIds; }
        public int getBrandIndex() { return brandIndex; }
    }

    /**
     * 品牌处理统计类
     * 用于汇总品牌分组的处理结果
     */
    private static class BrandProcessingSummary {
        private int totalBrandGroups;
        private int successBrandGroups;
        private int failBrandGroups;
        private Map<String, String> brandResults;

        public BrandProcessingSummary() {
            this.totalBrandGroups = 0;
            this.successBrandGroups = 0;
            this.failBrandGroups = 0;
            this.brandResults = new LinkedHashMap<>();
        }

        public void addResult(String brandCode, boolean success, String message) {
            totalBrandGroups++;
            if (success) {
                successBrandGroups++;
            } else {
                failBrandGroups++;
            }
            brandResults.put(brandCode, success ? "成功: " + message : "失败: " + message);
        }

        // Getters
        public int getTotalBrandGroups() { return totalBrandGroups; }
        public int getSuccessBrandGroups() { return successBrandGroups; }
        public int getFailBrandGroups() { return failBrandGroups; }
        public Map<String, String> getBrandResults() { return brandResults; }
    }

    /**
     * 常量定义类
     */
    private static final class Constants {
        // 单据类型常量
        static final String BILL_TYPE_SALOUTBILL = "im_SalOutBill_STD_BT_S";
        static final String BIZTYPE_NUMBER = "2101";
        static final String INVSCHEME_NUMBER = "2101";

        // 来源系统常量
        static final int SOURCE_SYSTEM_E3 = 1;

        // 来源单据类型常量
        static final int SOURCE_BILLTYPE_WHOLESALERETURNBILL = 1;  // 批发退货单
        static final int SOURCE_BILLTYPE_WHOLESALENOTICEBILL = 2;  // 批发通知单
        static final int SOURCE_BILLTYPE_PCP_SALOUTBILL = 3;       // PCP销售出库单
        static final int SOURCE_BILLTYPE_PCP_INVTRANSFERBILL = 4;  // PCP库存调拨单
        static final int SOURCE_BILLTYPE_OTHER_OUTBILL = 5;        // 其他出库单
        static final int SOURCE_BILLTYPE_DELIVERY_DETAIL = 6;      // 发货明细
        static final int SOURCE_BILLTYPE_WHOLESALE_SALEBILL = 7;   // 批发销货单

        // 查询状态常量
        static final String BILL_STATUS_CONFIRMED = "C";
        static final String SETTLE_STATUS = "4";
        static final String PLATFORM_CODE = "5";
        static final String DEFAULT_START_DATE = "2025-01-01";
    }

    /**
     * 批发退货单下推销售出库单主方法
     * 支持JSON参数配置，可以指定查询条件、批量大小等参数
     * 
     * @param map 参数映射，支持JSON配置
     * @throws KDException 业务异常
     */
    public void pushWholesalereturn2Saloutbill(Map<String, Object> map) throws KDException {
        
        // JSON参数解析逻辑
        String startBizDate = Constants.DEFAULT_START_DATE;
        String extraWhere = null;
        List<String> filterWholesalereturnbillIds = null;
        boolean useIdFilter = false;
        int batchSize = 50; // 默认批量大小
        boolean autoAudit = true; // 默认自动审核
        
        try {
            String jsonStr = (String) map.get("json");
            log.info("接收到的JSON参数: {}", jsonStr);
            
            // JSON参数示例:
            // {
            //   "startBizDate": "2025-01-01",
            //   "endBizDate": "2025-12-31", 
            //   "extraWhere": "fk_yd_customer = '123' or fk_yd_org = '456'",
            //   "batchSize": 50,
            //   "wholesalereturnbillIds": ["id1", "id2", "id3"],
            //   "autoAudit": true
            // }
            if (jsonStr != null && !jsonStr.trim().isEmpty()) {
                JSONObject jsonObj = JSON.parseObject(jsonStr);
                
                // 解析开始业务日期
                String startBizDateStr = jsonObj.getString("startBizDate");
                if (startBizDateStr != null && !startBizDateStr.trim().isEmpty()) {
                    startBizDate = startBizDateStr.trim();
                }
                
                // 解析额外查询条件
                String extraWhereStr = jsonObj.getString("extraWhere");
                if (extraWhereStr != null && !extraWhereStr.trim().isEmpty()) {
                    extraWhere = extraWhereStr.trim();
                }
                
                // 解析批量大小
                Integer batchSizeInt = jsonObj.getInteger("batchSize");
                if (batchSizeInt != null && batchSizeInt > 0) {
                    batchSize = batchSizeInt;
                }
                
                // 解析是否自动审核
                Boolean autoAuditBool = jsonObj.getBoolean("autoAudit");
                if (autoAuditBool != null) {
                    autoAudit = autoAuditBool;
                }
                
                // 解析批发退货单ID列表
                JSONArray idArray = jsonObj.getJSONArray("wholesalereturnbillIds");
                if (idArray != null && !idArray.isEmpty()) {
                    filterWholesalereturnbillIds = new ArrayList<>();
                    for (int i = 0; i < idArray.size(); i++) {
                        String id = idArray.getString(i);
                        if (id != null && !id.trim().isEmpty()) {
                            filterWholesalereturnbillIds.add(id.trim());
                        }
                    }
                    if (!filterWholesalereturnbillIds.isEmpty()) {
                        useIdFilter = true;
                        log.info("解析到 {} 个批发退货单ID进行过滤", filterWholesalereturnbillIds.size());
                    }
                }
                
                log.info("解析后的参数 - 开始日期: {}, 额外条件: {}, 批量大小: {}, 启用ID过滤: {}, 自动审核: {}", 
                        startBizDate, extraWhere, batchSize, useIdFilter, autoAudit);
            } else {
                // 从map中获取传统参数（兼容性处理）
                if (map.containsKey("startBizDate")) {
                    startBizDate = (String) map.get("startBizDate");
                }
                if (map.containsKey("extraWhere")) {
                    extraWhere = (String) map.get("extraWhere");
                }
                log.warn("JSON参数为空，使用传统参数或默认值");
            }
        } catch (Exception e) {
            log.warn("解析JSON参数失败，使用默认参数: {}", e.getMessage(), e);
        }

        // 获取批发退货单ID列表
        List<String> wholesalereturnbillIds;
        if (useIdFilter && filterWholesalereturnbillIds != null && !filterWholesalereturnbillIds.isEmpty()) {
            wholesalereturnbillIds = filterWholesalereturnbillIds;
        } else {
            Map<String, Object> queryParams = map;
            if (extraWhere != null) {
                queryParams.put("extraWhere", extraWhere);
            }
            queryParams.put("startBizDate", startBizDate);
            wholesalereturnbillIds = queryWholesalereturnbillIds(queryParams);
        }
        
        if (wholesalereturnbillIds.isEmpty()) {
            log.info("未查询到符合条件的批发退货单，处理结束");
            return;
        }

        log.info("开始处理 {} 个批发退货单", wholesalereturnbillIds.size());

        // 分批处理
        List<List<String>> batches = partitionList(wholesalereturnbillIds, batchSize);
        int totalCount = wholesalereturnbillIds.size();
        int successCount = 0;
        int failCount = 0;

        for (int batchIndex = 0; batchIndex < batches.size(); batchIndex++) {
            List<String> batch = batches.get(batchIndex);
            log.info("处理第 {}/{} 批，包含 {} 个批发退货单", batchIndex + 1, batches.size(), batch.size());
            
            int[] result = processBatch(batch, autoAudit);
            successCount += result[0];
            failCount += result[1];
        }

        logProcessingSummary(totalCount, successCount, failCount);
    }

    /**
     * 查询符合条件的批发退货单ID列表
     * @param map 查询参数
     * @return 批发退货单ID列表
     */
    private List<String> queryWholesalereturnbillIds(Map<String, Object> map) {
        // 获取批发退货单
        String sql = "select fid from tk_yd_wholesalereturnbill where fbillstatus = ? "
                + "and fk_yd_settlestatus = ? and fk_yd_platform = ? and fk_yd_returndate >= ?";

        // 从map中获取额外的查询条件
        if (map != null && map.containsKey("extraWhere")) {
            sql += " and (" + map.get("extraWhere") + ")";
        }

        List<String> wholesalereturnbillIds = new ArrayList<>();

        // 从map中获取日期，如果没有则使用默认值
        String startBizDate = Constants.DEFAULT_START_DATE;
        if (map != null && map.containsKey("startBizDate")) {
            startBizDate = (String) map.get("startBizDate");
        }

        try (DataSet ds = DB.queryDataSet(this.getClass().toString(), DBRoute.of("scm"), sql,
                new Object[]{Constants.BILL_STATUS_CONFIRMED, Constants.SETTLE_STATUS, Constants.PLATFORM_CODE, startBizDate})) {
            while (ds != null && ds.hasNext()) {
                Row row = ds.next();
                wholesalereturnbillIds.add(row.getString("fid"));
            }
        }

        return wholesalereturnbillIds;
    }

    /**
     * 批量查询处理主方法
     */
    private int[] processBatch(List<String> batch, boolean autoAudit) {
        int successCount = 0;
        int failCount = 0;
        
        for (String wholesalereturnbillId : batch) {
            try {
                DynamicObject wholesalereturnbill = BusinessDataServiceHelper.loadSingle(wholesalereturnbillId, FORMID_WHOLESALERETURNBILL);
                if (wholesalereturnbill != null) {
                    processSingleWholesalereturn(wholesalereturnbill, autoAudit);
                    successCount++;
                } else {
                    log.warn("未找到批发退货单，ID: {}", wholesalereturnbillId);
                    failCount++;
                }
            } catch (Exception e) {
                handleProcessException(wholesalereturnbillId, e);
                failCount++;
            }
        }
        
        return new int[]{successCount, failCount};
    }

    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }

    /**
     * 记录处理统计信息
     */
    private void logProcessingSummary(int totalCount, int successCount, int failCount) {
        log.info("批发退货单下推销售出库单处理完成 - 总数: {}, 成功: {}, 失败: {}", totalCount, successCount, failCount);
        log.info("注意: 每个批发退货单内部已按品牌分组独立下推，详细结果请查看各单据的品牌组处理日志");
    }

    /**
     * 统一处理单个批发退货单的处理异常
     * @param wholesalereturnbillId 批发退货单ID
     * @param e 异常信息
     */
    private void handleProcessException(String wholesalereturnbillId, Exception e) {
        log.error("处理批发退货单ID: {} 时发生异常: {}", wholesalereturnbillId, e.getMessage(), e);
    }

    /**
     * 查询客户对应的税率
     * @param customer 客户对象
     * @return 税率编号
     */
    private String loadCustomerTaxRate(DynamicObject customer) {
        String customerRateNum = "";
        if (customer != null && StringUtils.isNotBlank(customer.getString("number"))) {
            QFilter cusFilter = new QFilter("number", QCP.equals, customer.getString("number"));
            DynamicObject customerObj = BusinessDataServiceHelper.loadSingle("bd_customer", "taxrate.number",
                    new QFilter[] { cusFilter });
            if (customerObj != null && customerObj.get("taxrate.number") != null) {
                customerRateNum = customerObj.getString("taxrate.number");
            }
        }
        return customerRateNum;
    }

    /**
     * 创建销售出库单视图并设置基本字段
     * @param wholesalereturnbill 批发退货单
     * @param customerTaxRate 客户税率
     * @param brandIndex 品牌序号，用于生成唯一单据编号
     * @return 配置好的视图对象
     */
    private IFormView createSaloutbillView(DynamicObject wholesalereturnbill, String customerTaxRate, int brandIndex) {
        // 创建销售出库单视图并设置基本字段
        IFormView view = ABillServiceHelper.createAddView(FORMID_SALOUTBILL);

        setupBasicFields(view, wholesalereturnbill, brandIndex);
        setupSourceInfo(view, wholesalereturnbill);

        return view;
    }

    /**
     * 设置销售出库单的基本字段信息
     * @param view 销售出库单视图
     * @param wholesalereturnbill 批发退货单
     * @param brandIndex 品牌序号，用于生成唯一单据编号
     */
    private void setupBasicFields(IFormView view, DynamicObject wholesalereturnbill, int brandIndex) {
        // 单据编号（使用原单单号下推到销售出库单单号）
        String billNo = wholesalereturnbill.getString("billno");
        if (brandIndex > 0) {
            billNo = billNo + "_" + brandIndex;
        }
        view.getModel().setValue("billno", billNo);
        
        // 设置单据类型
        view.getModel().setItemValueByNumber("billtype", Constants.BILL_TYPE_SALOUTBILL);

        // 设置组织信息
        DynamicObject org = wholesalereturnbill.getDynamicObject("yd_org");
        view.getModel().setValue("org", org);
        view.getModel().setValue("bizorg", org);

        // 设置业务类型和库存方案
        view.getModel().setItemValueByNumber("biztype", Constants.BIZTYPE_NUMBER);
        view.getModel().setItemValueByNumber("invscheme", Constants.INVSCHEME_NUMBER);

        // 设置业务日期
        view.getModel().setValue("biztime", wholesalereturnbill.getDate("yd_returndate"));

        // 设置客户
        DynamicObject customer = wholesalereturnbill.getDynamicObject("yd_cqcustomer");
        view.getModel().setValue("customer", customer);

        // 设置备注
        view.getModel().setValue("comment", wholesalereturnbill.getString("yd_description"));

        // 设置是否来源CSP
        String e3Creator = wholesalereturnbill.getString("yd_e3creator");
        boolean isFromCsp = "CSP".equalsIgnoreCase(e3Creator);
        view.getModel().setValue("yd_isfromcsp", isFromCsp);

        // 设置是否退货
        boolean isReturn = true;
        view.getModel().setValue("yd_isreturn", isReturn);

        // 设置SAP单据类型
        String sapBillType = isReturn ? "ZREC" : "ZORC";
        view.getModel().setValue("yd_sapbilltype", sapBillType);
    }

    /**
     * 设置来源系统相关信息
     * @param view 销售出库单视图
     * @param wholesalereturnbill 批发退货单
     */
    private void setupSourceInfo(IFormView view, DynamicObject wholesalereturnbill) {
        // 来源系统(E3)
        view.getModel().setValue("yd_tbly", SourceSystemEnum.E3.getCode());
        // 来源单据类型
        view.getModel().setValue("yd_sourcebilltype", SourceBillTypeEnum.WHOLESALE_RETURN_BILL.getCode());
        // 通知单号
        view.getModel().setValue("yd_dealcode", wholesalereturnbill.getString("yd_noticebillno"));
        // sap单号
        view.getModel().setValue("yd_sapbillno", wholesalereturnbill.getString("yd_sap_billlno"));
        // 来源系统
        view.getModel().setValue("yd_sourcesys", SourceSystemCodeEnum.NEW_E3.getValue());
    }

    /**
     * 清空现有分录数据
     * @param view 销售出库单视图
     */
    private void clearExistingEntries(IFormView view) {
        DynamicObject bill = view.getModel().getDataEntity();
        DynamicObjectCollection entries = bill.getDynamicObjectCollection("billentry");
        for (int i = entries.size() - 1; i >= 0; i--) {
            view.getModel().deleteEntryRow("billentry", i);
        }
    }

    /**
     * 验证拆单明细是否有效（非排除物料）
     * @param splitEntry 拆单明细
     * @return 是否有效
     */
    private boolean isValidSplitEntry(DynamicObject splitEntry) {
        return !splitEntry.getBoolean("yd_excludematsplit");
    }

    /**
     * 根据序号查找匹配的OMS明细
     * @param omsEntryCol OMS明细集合
     * @param omsSeq 序号
     * @return OMS明细对象
     */
    private DynamicObject findMatchingOmsEntry(DynamicObjectCollection omsEntryCol, Integer omsSeq) {
        List<DynamicObject> omsEntryList = omsEntryCol.stream().collect(Collectors.toList());
        return omsEntryList.stream().filter(x -> x.getInt("seq") == omsSeq).findFirst().orElse(null);
    }

    /**
     * 处理单个分录的数据设置
     * @param view 销售出库单视图
     * @param splitEntry 拆单明细
     * @param omsEntryCol OMS明细集合
     * @param wholesalereturnbill 批发退货单
     * @param customerTaxRate 客户税率
     * @param rowIndex 行索引
     */
    private void processSingleEntry(IFormView view, DynamicObject splitEntry, DynamicObjectCollection omsEntryCol,
                                    DynamicObject wholesalereturnbill, String customerTaxRate, int rowIndex) {
        // 设置物料
        Object matId = splitEntry.getDynamicObject("yd_splitmaterial").getPkValue();
        DynamicObject materialObj = BusinessDataServiceHelper.loadSingle(matId, "bd_material");
        String matNumber = splitEntry.getDynamicObject("yd_splitmaterial").getString("number");
        view.getModel().setItemValueByNumber("material", matNumber, rowIndex);

        // DynamicObject bill = view.getModel().getDataEntity();
        // DynamicObjectCollection billEntryCol = bill.getDynamicObjectCollection("billentry");
        // for (DynamicObject billEntry : billEntryCol) {
        //     DynamicObject material = billEntry.getDynamicObject("material");
        //     if (material != null) {
        //         System.out.println(material.getString("number"));
        //     }
        // }
         
        // 基本计量单位
        view.getModel().setValue("baseunit", materialObj.getDynamicObject("baseunit"), rowIndex);
        // 计量单位
        view.getModel().setValue("unit", materialObj.getDynamicObject("baseunit"), rowIndex);
        // 销售组织仓库编码
        view.getModel().setValue("warehouse", splitEntry.getDynamicObject("yd_splitsalorgstock"), rowIndex);
        // 库存组织仓库编码
        view.getModel().setValue("yd_invorgstock", splitEntry.getDynamicObject("yd_splitinvorgstock"), rowIndex);
        // 设置数量
        view.getModel().setValue("qty", splitEntry.getBigDecimal("yd_splitqty"), rowIndex);
        // 含税单价
        view.getModel().setValue("priceandtax", splitEntry.getBigDecimal("yd_splitprice"), rowIndex);
        // 价税合计
        view.getModel().setValue("amountandtax", splitEntry.getBigDecimal("yd_totaltaxamt"), rowIndex);
        // 是否赠品
        view.getModel().setValue("ispresent", splitEntry.getBoolean("yd_isgift"), rowIndex);
        // 设置税率
        if (customerTaxRate != null) {
            view.getModel().setItemValueByNumber("taxrateid", customerTaxRate, rowIndex);
        }

        // 序号(OMS明细)
        Integer omsSeq = splitEntry.getInt("yd_omsrownum");
        DynamicObject omsEntry = findMatchingOmsEntry(omsEntryCol, omsSeq);
        if (omsEntry != null) {
            // 是否组套
            view.getModel().setValue("yd_isbom", omsEntry.getBoolean("yd_isbom"), rowIndex);
            if (omsEntry.getBoolean("yd_isbom")) {
                // 组套编码
                view.getModel().setValue("yd_bomnum", omsEntry.getString("yd_goodsnum"), rowIndex);              
            }
            // sap行号
            view.getModel().setValue("yd_saprowno", omsEntry.getString("yd_sap_rowindex"), rowIndex);
        }

        // 行类型
        // 是否退货=否且是否赠品=否，取 TAN：常规销售类别
        // 是否退货=否且是否赠品=是，取 TANN：免费行
        // 是否退货=是且是否赠品=否，取 REN：退货行
        // 是否退货=是且是否赠品=是，取 RENN：免费行退货
        boolean yd_isreturn = true;
        if (yd_isreturn == false && splitEntry.getBoolean("yd_isgift") == false) {
            view.getModel().setValue("yd_rowtype", "TAN", rowIndex);
        }else if (yd_isreturn == false && splitEntry.getBoolean("yd_isgift") == true) {
            view.getModel().setValue("yd_rowtype", "TANN", rowIndex);
        }else if (yd_isreturn == true && splitEntry.getBoolean("yd_isgift") == false) {
            view.getModel().setValue("yd_rowtype", "REN", rowIndex);
        }else if (yd_isreturn == true && splitEntry.getBoolean("yd_isgift") == true) {
            view.getModel().setValue("yd_rowtype", "RENN", rowIndex);
        }

        // 记录源单信息
        // 来源系统单据分录ID
        view.getModel().setValue("srcsysbillentryid", splitEntry.getPkValue(), rowIndex);
        // 来源系统单据编号
        view.getModel().setValue("srcsysbillno", wholesalereturnbill.getString("billno"), rowIndex);
        // 来源系统单据ID
        view.getModel().setValue("srcsysbillid", wholesalereturnbill.getPkValue(), rowIndex);
        // 来源单据ID
        view.getModel().setValue("srcbillid", wholesalereturnbill.getPkValue(), rowIndex);
        // 来源单据行ID
        view.getModel().setValue("srcbillentryid", splitEntry.getPkValue(), rowIndex);
        // 来源单据分录序号
        view.getModel().setValue("srcbillentryseq", splitEntry.get("seq"), rowIndex);
        // 来源单据编号
        view.getModel().setValue("srcbillnumber", wholesalereturnbill.getString("billno"), rowIndex);
    }

    /**
     * 根据批发退货单拆单明细创建销售出库单分录
     * @param view 销售出库单视图
     * @param wholesalereturnbill 批发退货单
     * @param customerTaxRate 客户税率
     * @return 创建的分录数量
     */
    private int createSaloutbillEntries(IFormView view, DynamicObject wholesalereturnbill, String customerTaxRate) {
        // OMS明细
        DynamicObjectCollection omsEntryCol = wholesalereturnbill.getDynamicObjectCollection("entryentity");
        // 拆单明细
        DynamicObjectCollection splitEntryCol = wholesalereturnbill.getDynamicObjectCollection("yd_splitentryentity");

        if (omsEntryCol.isEmpty() && splitEntryCol.isEmpty()) {
            return 0;
        }

        int entryCount = 0;
        for (int i = 0; i < splitEntryCol.size(); i++) {
            DynamicObject splitEntry = splitEntryCol.get(i);

            // 是否排除物料
            if (!isValidSplitEntry(splitEntry)) {
                continue;
            }

            // 其他的都是一对一生成，直接创建分录
            int rowIndex = view.getModel().createNewEntryRow("billentry");
            processSingleEntry(view, splitEntry, omsEntryCol, wholesalereturnbill, customerTaxRate, rowIndex);
            entryCount++;
        }

        return entryCount;
    }

    /**
     * 根据特定品牌的拆单明细创建销售出库单分录
     * @param view 销售出库单视图
     * @param wholesalereturnbill 批发退货单
     * @param brandSplitEntries 特定品牌的拆单明细列表
     * @param customerTaxRate 客户税率
     * @return 创建的分录数量
     */
    private int createSaloutbillEntriesForBrand(IFormView view, DynamicObject wholesalereturnbill, 
                                               List<DynamicObject> brandSplitEntries, String customerTaxRate) {
        // OMS明细
        DynamicObjectCollection omsEntryCol = wholesalereturnbill.getDynamicObjectCollection("entryentity");

        if (brandSplitEntries == null || brandSplitEntries.isEmpty()) {
            return 0;
        }
        
        int entryCount = 0;
        for (DynamicObject splitEntry : brandSplitEntries) {
            // 验证有效性（此处应该已经过滤过，但保险起见再次验证）
            if (!isValidSplitEntry(splitEntry)) {
                continue;
            }

            // 创建分录
            int rowIndex = view.getModel().createNewEntryRow("billentry");
            processSingleEntry(view, splitEntry, omsEntryCol, wholesalereturnbill, customerTaxRate, rowIndex);
            entryCount++;
        }
        
        return entryCount;
    }

    /**
     * 保存提交审核销售出库单
     * @param view 销售出库单视图
     * @param autoAudit 是否自动审核
     * @return 操作结果
     */
    private OperationResult saveSubmitAndAuditSaloutbill(IFormView view, boolean autoAudit) {
        return ABillServiceHelper.saveOperate(view, autoAudit);
    }

    /**
     * 从操作结果中提取单据编号
     * @param operationResult 操作结果
     * @return 单据编号
     */
    private String extractBillNumberFromResult(OperationResult operationResult) {
        String billNo = "";
        Map<Object, String> map = operationResult.getBillNos();
        for (Map.Entry<Object, String> entry : map.entrySet()) {
            billNo = entry.getValue();
            break;
        }
        return billNo;
    }

    /**
     * 创建单据间关联关系
     * @param wholesalereturnbill 批发退货单
     * @param saloutbillId 销售出库单ID
     */
    private void createBillRelation(DynamicObject wholesalereturnbill, String saloutbillId) {
        BotpUtils.createRelation(FORMID_WHOLESALERETURNBILL, Long.parseLong(wholesalereturnbill.getPkValue().toString()),
                FORMID_SALOUTBILL, Long.parseLong(saloutbillId));
    }

    /**
     * 更新批发退货单的下游单据信息
     * 将销售出库单的处理结果反写到批发退货单的拆单明细中
     *
     * @param wholesalereturnbill 批发退货单对象
     * @param isSuccess 处理是否成功
     * @param saloutbillNo 销售出库单号（成功时填入）
     * @param errorMessage 错误信息（失败时填入）
     */
    private void updateWholesalereturnbill(DynamicObject wholesalereturnbill, Boolean isSuccess, String saloutbillNo, String errorMessage) {
        // 结算状态 - 只有成功时才设置状态为6
        if (isSuccess) {
            wholesalereturnbill.set("yd_settlestatus", 6);
        }
        // 获取批发退货单的拆单明细集合
        DynamicObjectCollection splitEntryCol = wholesalereturnbill.getDynamicObjectCollection("yd_splitentryentity");

        // 遍历拆单明细，将下游单据号反写到每个明细行
        for (DynamicObject splitEntry : splitEntryCol) {
            // 只有成功时才赋值销售出库单号
            if (isSuccess) {
                splitEntry.set("yd_downstreambillno", saloutbillNo);
            }
            splitEntry.set("yd_pusherrorreason", errorMessage);
        }

        // 持久化保存批发退货单的更新
        SaveServiceHelper.save(new DynamicObject[] {wholesalereturnbill});
    }

    /**
     * 按分录ID精确更新批发退货单的下游单据信息
     * 只更新指定分录ID列表对应的拆单明细状态
     * 
     * @param wholesalereturnbill 批发退货单对象
     * @param entryIds 需要更新的分录ID列表
     * @param isSuccess 处理是否成功
     * @param saloutbillNo 销售出库单号（成功时填入）
     * @param errorMessage 错误信息（失败时填入）
     */
    private void updateWholesalereturnbillByEntries(DynamicObject wholesalereturnbill, List<String> entryIds, 
                                                   Boolean isSuccess, String saloutbillNo, String errorMessage) {
        if (entryIds == null || entryIds.isEmpty()) {
            log.warn("分录ID列表为空，跳过状态更新");
            return;
        }

        // 获取批发退货单的拆单明细集合
        DynamicObjectCollection splitEntryCol = wholesalereturnbill.getDynamicObjectCollection("yd_splitentryentity");
        
        int updatedCount = 0;
        // 遍历拆单明细，只更新匹配的分录
        for (DynamicObject splitEntry : splitEntryCol) {
            String entryId = splitEntry.getPkValue().toString();
            if (entryIds.contains(entryId)) {
                // 只有成功时才赋值销售出库单号
                if (isSuccess) {
                    splitEntry.set("yd_downstreambillno", saloutbillNo);
                }
                splitEntry.set("yd_pusherrorreason", errorMessage);
                updatedCount++;
            }
        }
        
        log.info("精确更新拆单明细状态，目标: {} 个，实际更新: {} 个", entryIds.size(), updatedCount);
        
        // 持久化保存批发退货单的更新
        SaveServiceHelper.save(new DynamicObject[] {wholesalereturnbill});
    }

    /**
     * 批量更新所有品牌组的状态
     * 统一处理所有品牌组的状态回写，支持全部成功时更新主单结算状态
     * @param wholesalereturnbill 批发退货单对象
     * @param allBrandResults 所有品牌组的处理结果列表
     * @param allSuccess 是否全部成功
     */
    private void updateAllBrandGroupsStatus(DynamicObject wholesalereturnbill, 
                                           List<BrandGroupResult> allBrandResults, 
                                           boolean allSuccess) {
        log.info("开始批量更新品牌组状态，共 {} 个品牌组，全部成功: {}", allBrandResults.size(), allSuccess);
        
        for (BrandGroupResult brandResult : allBrandResults) {
            try {
                updateWholesalereturnbillByEntries(
                    wholesalereturnbill, 
                    brandResult.getEntryIds(),
                    brandResult.isProcessSuccess(),
                    brandResult.getSaloutbillNo(),
                    brandResult.getErrorMessage()
                );
                log.debug("品牌组 {} 状态更新完成", brandResult.getBrandCode());
            } catch (Exception updateException) {
                log.error("品牌组 {} 状态更新失败: {}", brandResult.getBrandCode(), updateException.getMessage(), updateException);
                // 状态更新失败不影响整体流程，只记录日志
            }
        }
        
        // 如果全部成功，更新主单结算状态
        if (allSuccess) {
            try {
                wholesalereturnbill.set("yd_settlestatus", 6);
                SaveServiceHelper.save(new DynamicObject[] {wholesalereturnbill});
                log.info("所有品牌组处理成功，主单结算状态已更新为6");
            } catch (Exception e) {
                log.error("更新主单结算状态失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 处理单个批发退货单下推销售出库单
     * 支持按品牌分组下推，每个品牌分组独立生成销售出库单
     * 采用统一事务处理模式，确保数据一致性
     * @param wholesalereturnbill 批发退货单
     * @param autoAudit 是否自动审核
     */
    private void processSingleWholesalereturn(DynamicObject wholesalereturnbill, boolean autoAudit) {
        log.info("开始处理批发退货单: {}", wholesalereturnbill.getString("yd_noticebillno"));

        // 获取拆单明细并按品牌分组
        DynamicObjectCollection splitEntryCol = wholesalereturnbill.getDynamicObjectCollection("yd_splitentryentity");
        if (splitEntryCol.isEmpty()) {
            log.info("批发退货单无拆单明细，跳过处理");
            return;
        }

        Map<String, List<DynamicObject>> brandGroups = groupSplitEntriesByBrand(splitEntryCol);
        if (brandGroups.isEmpty()) {
            log.info("按品牌分组后无有效明细，跳过处理");
            return;
        }

        // 查询客户对应税率
        String customerRateNum = loadCustomerTaxRate(wholesalereturnbill.getDynamicObject("yd_cqcustomer"));

        // 品牌处理统计
        BrandProcessingSummary summary = new BrandProcessingSummary();
        List<BrandGroupResult> allBrandResults = new ArrayList<>();
        boolean hasFailure = false;

        log.info("开始统一事务处理批发退货单品牌分组，共 {} 个品牌组", brandGroups.size());
        
        try (TXHandle h = TX.requiresNew()) {
            try {
                // 逐个品牌组处理
                int brandIndex = 0;
                for (Map.Entry<String, List<DynamicObject>> brandEntry : brandGroups.entrySet()) {
                    String brandCode = brandEntry.getKey();
                    List<DynamicObject> brandSplitEntries = brandEntry.getValue();
                    
                    log.info("开始处理品牌组: {} (序号: {}) 包含 {} 个拆单明细", brandCode, brandIndex, brandSplitEntries.size());
                    
                    // 创建品牌分组结果对象
                    String brandName = getBrandName(brandSplitEntries.get(0));
                    BrandGroupResult brandResult = new BrandGroupResult(brandCode, brandName, brandSplitEntries, brandIndex);
                    
                    // 处理单个品牌组
                    processSingleBrandGroup(wholesalereturnbill, brandResult, customerRateNum, autoAudit);
                    
                    // 收集结果
                    allBrandResults.add(brandResult);
                    
                    // 递增品牌序号
                    brandIndex++;
                    
                    // 记录处理结果
                    summary.addResult(brandCode, brandResult.isProcessSuccess(), 
                                    brandResult.isProcessSuccess() ? brandResult.getSaloutbillNo() : brandResult.getErrorMessage());
                    
                    // 检查是否有失败
                    if (!brandResult.isProcessSuccess()) {
                        hasFailure = true;
                        log.warn("品牌组 {} 处理失败: {}", brandCode, brandResult.getErrorMessage());
                    }
                }
                
                if (!hasFailure) {
                    // 成功时在事务内批量回写状态
                    log.info("所有品牌组处理成功，在事务内批量更新状态");
                    updateAllBrandGroupsStatus(wholesalereturnbill, allBrandResults, true);
                    log.info("统一事务处理完成，所有品牌组成功");
                } else {
                    // 有失败时回滚事务
                    log.warn("存在失败的品牌组，标记事务回滚");
                    h.markRollback();
                }
                
            } catch (Exception e) {
                log.error("品牌组处理过程中发生异常，标记事务回滚: {}", e.getMessage(), e);
                h.markRollback();
                throw e;
            }
        } catch (Exception e) {
            // 事务外处理失败情况
            log.warn("统一事务回滚，开始事务外状态回写");
            updateAllBrandGroupsStatus(wholesalereturnbill, allBrandResults, false);
        }

        // 输出处理汇总
        logBrandProcessingSummary(wholesalereturnbill.getString("yd_noticebillno"), summary);
    }

    /**
     * 处理单个品牌组
     * @param wholesalereturnbill 批发退货单
     * @param brandResult 品牌分组结果对象
     * @param customerRateNum 客户税率
     * @param autoAudit 是否自动审核
     */
    private void processSingleBrandGroup(DynamicObject wholesalereturnbill, BrandGroupResult brandResult, 
                                       String customerRateNum, boolean autoAudit) {
        IFormView view = null;
        try {
            // 创建销售出库单视图并设置基本字段
            view = createSaloutbillView(wholesalereturnbill, customerRateNum, brandResult.getBrandIndex());
            
            // 清空现有分录数据
            clearExistingEntries(view);

            // 为当前品牌组创建分录
            int entryCount = createSaloutbillEntriesForBrand(view, wholesalereturnbill, 
                                                           brandResult.getSplitEntries(), customerRateNum);
            
            if (entryCount == 0) {
                brandResult.setProcessSuccess(false);
                brandResult.setErrorMessage("品牌组无有效分录");
                return;
            }

            // 保存提交审核销售出库单
            OperationResult operationResult = saveSubmitAndAuditSaloutbill(view, autoAudit);
            
            if (operationResult.isSuccess()) {
                // 获取销售出库单号
                String saloutbillNo = extractBillNumberFromResult(operationResult);
                
                log.info("品牌组 {} (序号: {}) 销售出库单处理成功，单据编号: {}", brandResult.getBrandCode(), brandResult.getBrandIndex(), saloutbillNo);
                ABillServiceHelper.exitView(view);
                view = null; // 标记已释放
                
                List<String> saloutbillIds = operationResult.getSuccessPkIds().stream()
                        .map(String::valueOf).collect(Collectors.toList());
                
                // 创建关联关系
                createBillRelation(wholesalereturnbill, saloutbillIds.get(0));
                
                // 设置成功结果
                brandResult.setProcessSuccess(true);
                brandResult.setSaloutbillNo(saloutbillNo);
                brandResult.setErrorMessage("");
                
                log.info("品牌组 {} 销售出库单处理成功，等待统一事务提交后更新状态", brandResult.getBrandCode());
                
            } else {
                // 使用智能错误消息提取器获取最佳错误消息
                String errMessage = ErrorMessageExtractor.extractBestErrorMessage(
                    operationResult, null, 800);

                // 设置失败结果
                brandResult.setProcessSuccess(false);
                brandResult.setErrorMessage(errMessage);
                
                log.error("品牌组 {} 销售出库单处理失败: {}", brandResult.getBrandCode(), errMessage);
            }

        } catch (Exception e) {
            // 使用智能错误消息提取器处理异常消息
            String exceptionMessage = ErrorMessageExtractor.extractBestErrorMessage(
                null, e, 800);
            
            // 设置异常结果
            brandResult.setProcessSuccess(false);
            brandResult.setErrorMessage(exceptionMessage);
            log.error("品牌组 {} 处理异常: {}", brandResult.getBrandCode(), e.getMessage(), e);
        } finally {
            // 确保View资源正确释放
            if (view != null) {
                try {
                    ABillServiceHelper.exitView(view);
                } catch (Exception e) {
                    log.warn("释放View资源时发生异常: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 从拆单明细中获取品牌名称
     * @param splitEntry 拆单明细
     * @return 品牌名称
     */
    private String getBrandName(DynamicObject splitEntry) {
        DynamicObject brand = splitEntry.getDynamicObject("yd_brand");
        if (brand != null) {
            String name = brand.getString("name");
            if (name != null && !name.trim().isEmpty()) {
                return name.trim();
            }
        }
        return "未知品牌";
    }

    /**
     * 输出品牌处理结果汇总日志
     * @param noticebillNo 批发退货单号
     * @param summary 处理统计
     */
    private void logBrandProcessingSummary(String noticebillNo, BrandProcessingSummary summary) {
        log.info("批发退货单 {} 按品牌分组处理完成 - 总品牌组: {}, 成功: {}, 失败: {}", 
                noticebillNo, summary.getTotalBrandGroups(), summary.getSuccessBrandGroups(), summary.getFailBrandGroups());
        
        for (Map.Entry<String, String> entry : summary.getBrandResults().entrySet()) {
            log.info("品牌 {}: {}", entry.getKey(), entry.getValue());
        }
    }

    /**
     * 按品牌对拆单明细进行分组
     * @param splitEntryCol 拆单明细集合
     * @return 品牌分组Map，key为品牌编号，value为该品牌的拆单明细列表
     */
    private Map<String, List<DynamicObject>> groupSplitEntriesByBrand(DynamicObjectCollection splitEntryCol) {
        Map<String, List<DynamicObject>> brandGroups = new LinkedHashMap<>();
        
        for (DynamicObject splitEntry : splitEntryCol) {
            // 验证拆单明细是否有效（非排除物料）
            if (!isValidSplitEntry(splitEntry)) {
                continue;
            }
            
            // 获取品牌信息，用于分组处理
            String brandCode = "UNKNOWN"; // 默认品牌编号，当品牌信息为空时使用
            String brandName = "未知品牌"; // 默认品牌名称，当品牌信息为空时使用
            
            // 从拆单明细中获取品牌对象
            DynamicObject brand = splitEntry.getDynamicObject("yd_brand");
            if (brand != null) {
                // 获取品牌编号
                String code = brand.getString("number");
                // 获取品牌名称
                String name = brand.getString("name");
                
                // 验证品牌编号是否有效，如果有效则使用实际品牌编号
                if (code != null && !code.trim().isEmpty()) {
                    brandCode = code.trim();
                }
                
                // 验证品牌名称是否有效，如果有效则使用实际品牌名称
                if (name != null && !name.trim().isEmpty()) {
                    brandName = name.trim();
                }
            }
            
            // 将拆单明细添加到对应品牌分组
            brandGroups.computeIfAbsent(brandCode, k -> new ArrayList<>()).add(splitEntry);
        }
        
        log.info("按品牌分组完成，共分为 {} 个品牌组", brandGroups.size());
        for (Map.Entry<String, List<DynamicObject>> entry : brandGroups.entrySet()) {
            log.info("品牌: {} 包含 {} 个拆单明细", entry.getKey(), entry.getValue().size());
        }
        
        return brandGroups;
    }
}