package kd.bos.tcbj.ec.servicehelper;

import kd.bos.exception.KDException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.tcbj.ec.business.E3SqlResult;
import kd.bos.tcbj.ec.common.E3SqlApiUtils;
import kd.bos.tcbj.ec.common.E3ApiUtils;
import kd.bos.tcbj.ec.business.E3Response;
import kd.bos.tcbj.ec.business.response.E3TransferOrderResponse;
import kd.bos.tcbj.ec.business.response.E3TransferOrderData;
import kd.bos.tcbj.ec.business.response.E3TransferOrder;
import kd.bos.tcbj.ec.business.response.E3TransferOrderDetail;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import java.text.SimpleDateFormat;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;
import java.util.stream.Collectors;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.IFormView;
import kd.bos.tcbj.im.helper.ABillServiceHelper;

public class SyncE3TransferOutOrderService {

    private static final Log log = LogFactory.getLog(SyncE3TransferOutOrderService.class.getName());

    private static final String FORMID_FHMXB_STRING = "yd_fhmxb";

    /**
     * 移仓单据上下文数据包装类
     */
    private static class TransferOrderContext {
        private String djbh;  // 单据编号
        private String qdyr;  // 移入渠道客户代码
        private String qdyc;  // 移出渠道客户代码
        private String ckyr;  // 移入仓库代码
        private String ckyc;  // 移出仓库代码

        public TransferOrderContext(Map<String, Object> row) {
            this.djbh = (String) row.get("number");
            this.qdyr = (String) row.get("qdyr");
            this.qdyc = (String) row.get("qdyc");
            this.ckyr = (String) row.get("ckyr");
            this.ckyc = (String) row.get("ckyc");
        }

        public String getDjbh() { return djbh; }
        public String getQdyr() { return qdyr; }
        public String getQdyc() { return qdyc; }
        public String getCkyr() { return ckyr; }
        public String getCkyc() { return ckyc; }
    }

    public void syncTransferOutOrderData(Map<String, Object> map) throws KDException {

        // JSON参数解析逻辑
        long startTimestamp;
        long endTimestamp = System.currentTimeMillis() / 1000;
        List<String> filterNumberList = null;
        boolean useNumberFilter = false;
        
        try {
            String jsonStr = (String) map.get("json");
            log.info("接收到的JSON参数: {}", jsonStr);
            
            // JSON参数示例:
            // {
            //   "startTime": "2024-01-01 00:00:00",
            //   "endTime": "2024-01-31 23:59:59",
            //   "numberList": ["DJ001", "DJ002", "DJ003"]
            // }
            // 注意：时间格式必须为 yyyy-MM-dd HH:mm:ss
            if (jsonStr != null && !jsonStr.trim().isEmpty()) {
                JSONObject jsonObj = JSON.parseObject(jsonStr);
                String startTimeStr = jsonObj.getString("startTime");
                String endTimeStr = jsonObj.getString("endTime");
                
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date startDate = sdf.parse(startTimeStr);
                startTimestamp = startDate.getTime() / 1000;
                
                // 如果JSON中有endTime，则使用JSON中的值，否则使用当前时间
                if (endTimeStr != null && !endTimeStr.trim().isEmpty()) {
                    Date endDate = sdf.parse(endTimeStr);
                    endTimestamp = endDate.getTime() / 1000;
                }
                
                // 解析单据编号列表
                JSONArray numberArray = jsonObj.getJSONArray("numberList");
                if (numberArray != null && !numberArray.isEmpty()) {
                    filterNumberList = new ArrayList<>();
                    for (int i = 0; i < numberArray.size(); i++) {
                        String number = numberArray.getString(i);
                        if (number != null && !number.trim().isEmpty()) {
                            filterNumberList.add(number.trim());
                        }
                    }
                    if (!filterNumberList.isEmpty()) {
                        useNumberFilter = true;
                        log.info("解析到 {} 个单据编号进行过滤", filterNumberList.size());
                    }
                }
                
                log.info("解析后的开始时间戳: {}, 结束时间戳: {}, 启用单据编号过滤: {}", startTimestamp, endTimestamp, useNumberFilter);
            } else {
                // 默认查询最近30天
                startTimestamp = endTimestamp - (30 * 24 * 60 * 60);
                log.warn("JSON参数为空，使用默认时间范围（最近30天）");
            }
        } catch (Exception e) {
            log.warn("解析JSON参数失败，使用默认时间范围: {}", e.getMessage(), e);
            startTimestamp = endTimestamp - (30 * 24 * 60 * 60);
        }

        // 构建SQL查询语句 - 查询移仓入库单数据
        // 单据状态="已验收"且获取类型yc_type=入库且单据类型=yphth一盘货退货且上游移仓出库单创建人=CSP
        E3SqlResult result;
        if (useNumberFilter && filterNumberList != null && !filterNumberList.isEmpty()) {
            // 使用单据编号过滤
            result = executeBatchSqlQuery(filterNumberList, startTimestamp, endTimestamp);
        } else {
            // 使用时间范围过滤
            String strSQL = buildBaseSql(startTimestamp, endTimestamp, null);
            log.info("最终执行的SQL语句: {}", strSQL);
            result = E3SqlApiUtils.sqlExec(
                    strSQL,
                    new com.alibaba.fastjson.TypeReference<E3SqlResult>() {},
                    true
            );
        }

        // 验证SQL查询结果
        if (result.getSuccess() != 1 || result.getData() == null || result.getData().isEmpty()) {
            log.warn("SQL查询无数据或查询失败，状态码: {}, 消息: {}", result.getSuccess(), result.getMsg());
            return;
        }

        // 提取所有单据编号
        List<String> numberList = result.getData().stream()
            .map(row -> (String) row.get("number"))
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        log.info("提取到 {} 个唯一单据编号", numberList.size());

        // 创建上下文映射表
        Map<String, TransferOrderContext> contextMap = new HashMap<>();
        for (Map<String, Object> row : result.getData()) {
            TransferOrderContext context = new TransferOrderContext(row);
            if (context.getDjbh() != null) {
                contextMap.put(context.getDjbh(), context);
            }
        }
        log.info("创建单据上下文映射表，包含 {} 条记录", contextMap.size());

        // 分页处理
        int batchSize = 50; // 可配置的批量大小
        List<List<String>> batches = partitionList(numberList, batchSize);

        // 获取E3配置
        Map<String, String> e3Config = E3ApiUtils.getE3Config();

        // 批量查询处理
        processBatchQuery(batches, e3Config, contextMap);

    }

    /**
     * 构建基础SQL查询语句
     */
    private String buildBaseSql(long startTimestamp, long endTimestamp, List<String> numberList) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ")
                .append("    a.id, ")                          // 移仓入库单主键ID
                .append("    a.rq, ")                          // 日期（时间戳格式）
                .append("    a.djbh as number, ")              // 单据编号
                .append("    qd_yr.khdm as qdyr, ")            // 移入渠道客户代码
                .append("    qd_yc.khdm as qdyc, ")            // 移出渠道客户代码
                .append("    ck_yr.ckdm as ckyr, ")            // 移入仓库代码
                .append("    ck_yc.ckdm as ckyc, ")            // 移出仓库代码
                .append("    a.djxz, ")                        // 单据性质（1-宿主移仓 2-普通移仓 3-渠道移仓）
                .append("    a.fhlx ")                         // 发货类型
                .append("from drp_ycrkd as a ")                // 移仓入库单主表
                .append("INNER JOIN kehu as qd_yr on qd_yr.id = a.yrqd_id ")    // 关联移入渠道客户表
                .append("INNER JOIN kehu as qd_yc on qd_yc.id = a.qd_id ")      // 关联移出渠道客户表
                .append("INNER JOIN cangku as ck_yr on ck_yr.id = a.yrck_id ")  // 关联移入仓库表
                .append("INNER JOIN cangku as ck_yc on ck_yc.id = a.ycck_id ")  // 关联移出仓库表
                .append("INNER JOIN drp_yctzd as b on b.djbh = a.lxdj ")        // 关联移仓通知单（通过联关单据号）
                .append("where a.status = 4 and UPPER(b.zdr) like '%CSP%' ");   // 过滤条件：单据状态=4且制单人包含CSP
        
        // 添加时间范围过滤
        sqlBuilder.append("  and a.rq >= ").append(startTimestamp).append(" ")
                  .append("  and a.rq <= ").append(endTimestamp);
        
        // 添加单据编号过滤
        if (numberList != null && !numberList.isEmpty()) {
            sqlBuilder.append(" and a.djbh in (");
            for (int i = 0; i < numberList.size(); i++) {
                if (i > 0) {
                    sqlBuilder.append(", ");
                }
                sqlBuilder.append("'").append(numberList.get(i).replace("'", "''")).append("'");
            }
            sqlBuilder.append(")");
        }
        
        return sqlBuilder.toString();
    }

    /**
     * 分批执行SQL查询（处理IN子句限制）
     */
    private E3SqlResult executeBatchSqlQuery(List<String> filterNumberList, long startTimestamp, long endTimestamp) {
        final int MAX_IN_PARAMS = 1000; // SQL IN子句最大参数数量
        
        if (filterNumberList.size() <= MAX_IN_PARAMS) {
            // 单次查询
            String strSQL = buildBaseSql(startTimestamp, endTimestamp, filterNumberList);
            log.info("执行单次SQL查询，单据编号数量: {}", filterNumberList.size());
            log.info("最终执行的SQL语句: {}", strSQL);
            return E3SqlApiUtils.sqlExec(
                    strSQL,
                    new com.alibaba.fastjson.TypeReference<E3SqlResult>() {},
                    true
            );
        } else {
            // 分批查询
            List<List<String>> numberBatches = partitionNumberList(filterNumberList, MAX_IN_PARAMS);
            log.info("执行分批SQL查询，总单据编号数量: {}, 分为 {} 批", filterNumberList.size(), numberBatches.size());
            
            E3SqlResult mergedResult = new E3SqlResult();
            List<Map<String, Object>> allData = new ArrayList<>();
            
            for (int i = 0; i < numberBatches.size(); i++) {
                List<String> batch = numberBatches.get(i);
                String strSQL = buildBaseSql(startTimestamp, endTimestamp, batch);
                log.info("执行第 {}/{} 批SQL查询，单据编号数量: {}", i+1, numberBatches.size(), batch.size());
                
                E3SqlResult batchResult = E3SqlApiUtils.sqlExec(
                        strSQL,
                        new com.alibaba.fastjson.TypeReference<E3SqlResult>() {},
                        true
                );
                
                if (batchResult.getSuccess() == 1 && batchResult.getData() != null) {
                    allData.addAll(batchResult.getData());
                } else {
                    log.warn("第 {} 批SQL查询失败，状态码: {}, 消息: {}", i+1, batchResult.getSuccess(), batchResult.getMsg());
                }
            }
            
            mergedResult.setSuccess(1);
            mergedResult.setData(allData);
            mergedResult.setMsg("分批查询完成");
            log.info("分批SQL查询完成，合并结果数量: {}", allData.size());
            
            return mergedResult;
        }
    }

    /**
     * 将单据编号列表分割成指定大小的批次
     */
    private List<List<String>> partitionNumberList(List<String> numberList, int batchSize) {
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < numberList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, numberList.size());
            batches.add(numberList.subList(i, end));
        }
        return batches;
    }

    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }

    /**
     * 批量查询处理主方法
     */
    private void processBatchQuery(List<List<String>> batches, Map<String, String> e3Config, Map<String, TransferOrderContext> contextMap) {
        int totalCount = 0;
        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < batches.size(); i++) {
            List<String> batch = batches.get(i);
            log.info("处理第 {}/{} 批次，包含 {} 个单据", i+1, batches.size(), batch.size());
            
            try {
                int[] batchResult = processSingleBatch(batch, e3Config, contextMap);
                totalCount += batchResult[0];
                successCount += batchResult[1];
                failCount += batchResult[2];
            } catch (Exception e) {
                log.error("批次 {} 处理失败: {}", i+1, e.getMessage(), e);
                failCount += batch.size();
                totalCount += batch.size();
            }
        }

        logProcessingSummary(totalCount, successCount, failCount);
    }

    /**
     * 单批次处理方法
     */
    private int[] processSingleBatch(List<String> numberBatch, Map<String, String> e3Config, Map<String, TransferOrderContext> contextMap) {
        int total = numberBatch.size();
        int success = 0;
        int fail = 0;

        for (String djbh : numberBatch) {
            try {
                queryE3TransferOrder(djbh, e3Config, contextMap);
                success++;
            } catch (Exception e) {
                log.error("查询单据 {} 失败: {}", djbh, e.getMessage(), e);
                fail++;
            }
        }

        return new int[]{total, success, fail};
    }

    /**
     * 单据查询方法
     */
    private void queryE3TransferOrder(String djbh, Map<String, String> e3Config, Map<String, TransferOrderContext> contextMap) {
        // 构建查询参数
        JSONObject queryParam = new JSONObject();
        queryParam.put("djbh", djbh);
        queryParam.put("yc_type", "rk"); // 出库类型
        queryParam.put("pageNo", 1);
        queryParam.put("pageSize", 100);
        
        // 调用E3接口
        String method = "e3oms.stock.yc.ckrk.get";
        E3Response response = E3ApiUtils.sendE3ApiReturnE3Response(
            e3Config.get("baseUrl"),
            e3Config.get("key"), 
            e3Config.get("secret"),
            method,
            queryParam
        );
        
        // 处理响应结果
        processE3Response(djbh, response, contextMap);
    }

    /**
     * 响应处理方法
     */
    private void processE3Response(String djbh, E3Response response, Map<String, TransferOrderContext> contextMap) {
        if (!"api-success".equals(response.getStatus())) {
            log.warn("单据 {} E3接口查询失败: {}", djbh, response.getMessage());
            return;
        }
        
        try {
            // 解析E3Response.data字段为E3TransferOrderResponse对象
            String dataJson = response.getData();
            if (dataJson == null || dataJson.trim().isEmpty()) {
                log.warn("单据 {} 响应数据为空", djbh);
                return;
            }
            
            E3TransferOrderData transferOrderData = JSON.parseObject(dataJson, E3TransferOrderData.class);
            if (transferOrderData == null) {
                log.warn("单据 {} 响应数据解析失败或数据为空", djbh);
                return;
            }
            
            // 记录分页信息
            if (transferOrderData.getPage() != null) {
                log.info("单据 {} 分页信息 - 当前页: {}, 每页数量: {}, 总记录数: {}, 总页数: {}", 
                    djbh, 
                    transferOrderData.getPage().getPageNo(),
                    transferOrderData.getPage().getPageSize(),
                    transferOrderData.getPage().getTotalResult(),
                    transferOrderData.getPage().getPageTotal());
            }
            
            // 遍历处理单据列表
            if (transferOrderData.getYcList() != null && !transferOrderData.getYcList().isEmpty()) {
                log.info("单据 {} 查询到 {} 条移仓单据", djbh, transferOrderData.getYcList().size());
                
                for (E3TransferOrder transferOrder : transferOrderData.getYcList()) {
                    processTransferOrder(djbh, transferOrder, contextMap);
                }
            } else {
                log.info("单据 {} 未查询到移仓单据数据", djbh);
            }
            
        } catch (Exception e) {
            log.error("单据 {} 响应数据解析异常: {}", djbh, e.getMessage(), e);
        }
    }
    
    /**
     * 处理单个移仓单据
     */
    private void processTransferOrder(String queryDjbh, E3TransferOrder transferOrder, Map<String, TransferOrderContext> contextMap) {
        try {
            log.info("处理移仓单据 - 查询单号: {}, 单据ID: {}, 单据编号: {}, 状态: {}, 移出仓库: {} -> 移入仓库: {}", 
                queryDjbh,
                transferOrder.getId(),
                transferOrder.getDjbh(),
                transferOrder.getStatus(),
                transferOrder.getYcckName(),
                transferOrder.getYrckName());
            
            IFormView view = ABillServiceHelper.createAddView(FORMID_FHMXB_STRING);

            // 单据编号
            view.getModel().setValue("billno", "E3_2_" + transferOrder.getDjbh());
            // 出入库日期
            view.getModel().setValue("yd_datefield_fhrq", transferOrder.getRq());
            // 平台 (E3=1, 旺店通=2, 吉客云=3, 万里牛=4, 新E3=5)
            view.getModel().setValue("yd_combofield_pt", "5");
            // 订单编号
            view.getModel().setValue("yd_textfield_ddbh", transferOrder.getDjbh());
            // 交易号
            view.getModel().setValue("yd_dealcode", transferOrder.getLxdj());
            // 渠道代码
            TransferOrderContext context = contextMap.get(transferOrder.getDjbh());
            String qdyrValue = (context != null && context.getQdyr() != null) ? context.getQdyr() : "";
            view.getModel().setValue("yd_qdcode", qdyrValue);
            log.info("设置渠道代码 - 单据: {}, qdyr: {}", transferOrder.getDjbh(), qdyrValue);
            // 渠道名称
            view.getModel().setValue("yd_qdname", transferOrder.getYrqdName());
            // 店铺编码
            view.getModel().setValue("yd_textfield_dpbh", qdyrValue);
            // 店铺名称
            view.getModel().setValue("yd_sdname", transferOrder.getYrqdName());
            // 发货仓库编码
            view.getModel().setValue("yd_textfield_ck", transferOrder.getYrckCode());
            // 发货仓库名称
            view.getModel().setValue("yd_fhckmc", transferOrder.getYrckName());
            // 是否退货
            view.getModel().setValue("yd_checkboxfield_th", true);

            clearExistingEntries(view);

            // 处理明细数据
            if (transferOrder.getMxList() != null && !transferOrder.getMxList().isEmpty()) {
                log.info("单据 {} 包含 {} 条明细", transferOrder.getDjbh(), transferOrder.getMxList().size());
                
                for (E3TransferOrderDetail detail : transferOrder.getMxList()) {
                    int rowIndex = view.getModel().createNewEntryRow("yd_entryentity");
                    processTransferOrderDetail(view, transferOrder.getDjbh(), detail, rowIndex);
                }
            } else {
                log.warn("单据 {} 无明细数据", transferOrder.getDjbh());
            }
            
            OperationResult result = ABillServiceHelper.saveOperate(view, false);            
            if (result.isSuccess()) {
                log.info("单据 {} 保存成功", transferOrder.getDjbh());
            } else {
                log.error("单据 {} 保存失败: {}", transferOrder.getDjbh(), result.getMessage());
            }
        } catch (Exception e) {
            log.error("处理移仓单据异常 - 单据编号: {}, 错误: {}", transferOrder.getDjbh(), e.getMessage(), e);
        }
    }
    
    /**
     * 清空现有分录数据
     * @param view 销售出库单视图
     */
    private void clearExistingEntries(IFormView view) {
        DynamicObject bill = view.getModel().getDataEntity();
        DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_entryentity");
        for (int i = entries.size() - 1; i >= 0; i--) {
            view.getModel().deleteEntryRow("yd_entryentity", i);
        }
    }

    /**
     * 处理移仓单据明细
     * 
     * @param djbh 单据编号
     * @param detail 移仓单据明细对象，包含SKU、商品编码、数量、金额等信息
     */
    private void processTransferOrderDetail(IFormView view, String djbh, E3TransferOrderDetail detail, int rowIndex) {
        try {
            log.info("处理明细 - 单据: {}, SKU: {}, 商品编码: {}, 通知数量: {}, 实际数量: {}, 金额: {}", 
                djbh,
                detail.getSku(),
                detail.getGoodsSn(),
                detail.getSl1(),
                detail.getSl(),
                detail.getJe());
            // 货品编号
            view.getModel().setValue("yd_textfield_hpbh", detail.getGoodsSn(), rowIndex);
            // 数量(实际数量)
            view.getModel().setValue("yd_e3qty", detail.getSl(), rowIndex);
            // 均摊运费
            view.getModel().setValue("yd_avlogisticscost", BigDecimal.ZERO, rowIndex);
            // 均摊金额
            view.getModel().setValue("yd_shareamount", BigDecimal.ZERO, rowIndex);
            // 总金额
            view.getModel().setValue("yd_detailtotalamount", BigDecimal.ZERO, rowIndex);
            // 商品上架单价
            view.getModel().setValue("yd_shelves_price", BigDecimal.ZERO, rowIndex);
            // 商品总折扣
            view.getModel().setValue("yd_totaldiscountamt", BigDecimal.ZERO, rowIndex);            
            
        } catch (Exception e) {
            log.error("处理移仓单据明细异常 - 单据: {}, SKU: {}, 错误: {}", djbh, detail.getSku(), e.getMessage(), e);
        }
    }

    /**
     * 记录处理统计信息
     */
    private void logProcessingSummary(int totalCount, int successCount, int failCount) {
        log.info("批量处理完成 - 总数: {}, 成功: {}, 失败: {}", totalCount, successCount, failCount);
    }

} 