package kd.bos.tcbj.ec.task;

import java.util.Map;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.ec.servicehelper.OldWholeSaleNotiToSaleoutService;

public class OldWholeSaleNotiToSaleoutScheduleTask extends AbstractTask {

    /**
     * 定时任务执行方法：批发通知单自动下推销售出库单
     * 
     * 功能描述：
     * - 每30分钟执行一次
     * - 查询已审核且未生成下游单据的批发通知单
     * - 支持通过JSON参数过滤特定渠道和客户的数据
     * - 支持开始日期过滤，查询指定日期及之后的数据
     * - 自动下推生成销售出库单
     * 
     * @param ctx 请求上下文
     * @param params 任务参数，支持json参数进行数据过滤
     * @throws KDException 业务异常
     * 
     * @createDate  : 2025-06-09
     * @createAuthor: 黄志强
     * @updateDate  : 2025-06-09
     * @updateAuthor: 黄志强
     */
    @Override
    public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
        // 创建服务实例并调用业务处理方法
        OldWholeSaleNotiToSaleoutService service = new OldWholeSaleNotiToSaleoutService();
        service.processWholeSaleNotiToSaleout(params);
    }

}