package kd.bos.tcbj.ec.task;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.ec.servicehelper.PushWholesalenotice2SaloutbillService;

import java.util.Map;

/**
 * 批发通知单下推销售出库单定时任务
 * 
 * 该任务已重构为Service模式，业务逻辑已迁移到PushWholesalenotice2SaloutbillService中。
 * Task类现在只负责调用Service，符合单一职责原则。
 * 
 * <AUTHOR>
 * @since 2025-05-20
 */
public class PushWholesalenotice2SaloutbillTask extends AbstractTask {

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        PushWholesalenotice2SaloutbillService service = new PushWholesalenotice2SaloutbillService();
        service.pushWholesalenotice2Saloutbill(map);
    }
}
