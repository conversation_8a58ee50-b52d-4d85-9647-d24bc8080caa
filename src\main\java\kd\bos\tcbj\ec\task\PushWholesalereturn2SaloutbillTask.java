package kd.bos.tcbj.ec.task;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.ec.servicehelper.PushWholesalereturn2SaloutbillService;

import java.util.Map;

/**
 * ec_批发退货单下推销售出库单任务
 * 
 * 该任务用于处理批发退货单下推销售出库单的业务场景。
 * 通过调用PushWholesalereturn2SaloutbillService服务来处理具体的数据同步逻辑。
 */
public class PushWholesalereturn2SaloutbillTask extends AbstractTask {

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        PushWholesalereturn2SaloutbillService service = new PushWholesalereturn2SaloutbillService();
        service.pushWholesalereturn2Saloutbill(map);
    }

}
