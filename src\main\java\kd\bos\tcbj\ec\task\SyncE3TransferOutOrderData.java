package kd.bos.tcbj.ec.task;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.ec.servicehelper.SyncE3TransferOutOrderService;

import java.util.Map;

/**
 * ec_同步E3调拨入库单数据（经销商一盘货）
 * 
 * 该任务用于从E3系统同步调拨入库单数据，主要用于经销商一盘货业务场景。
 * 通过调用SyncE3TransferOutOrderService服务来处理具体的数据同步逻辑。
 */
public class SyncE3TransferOutOrderData extends AbstractTask {

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        SyncE3TransferOutOrderService service = new SyncE3TransferOutOrderService();
        service.syncTransferOutOrderData(map);
    }

}
