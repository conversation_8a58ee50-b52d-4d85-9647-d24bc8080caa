package kd.bos.tcbj.ec.task;

import kd.bos.context.RequestContext;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import json.JSONObject;
import java.text.SimpleDateFormat;
import java.util.Date;
import kd.bos.tcbj.im.outbill.helper.WholeSaleNoticeBillMserviceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;

import java.util.Map;

/**
 * 批发通知单同步任务类
 * 用于定时同步E3系统的批发通知单数据
 * <AUTHOR>
 * @date 2024
 */
public class SyncWholeSaleNoticeBillTask extends AbstractTask {

    /** 日期格式化器，用于格式化日期为yyyy-MM-dd格式 */
    private final static SimpleDateFormat DATE_SDF = new SimpleDateFormat("yyyy-MM-dd");
    
    /**
     * 执行同步批发通知单任务
     * @param requestContext 请求上下文
     * @param map 参数映射，支持startDate和endDate参数自定义时间范围
     *            示例：
     *            - 使用字符串日期：{"startDate": "2024-01-01", "endDate": "2024-01-31"}
     *            - 使用Date对象：{"startDate": new Date(), "endDate": new Date()}
     *            - 不传参数或传null：使用默认时间范围（过去60天到当前时间）
     * @throws KDException 业务异常
     */
    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        // 检查是否传入了自定义的开始和结束日期参数
        Date startDate;
        Date endDate;
        
        if (map != null && map.containsKey("startDate") && map.containsKey("endDate")) {
            // 使用传入的日期参数
            try {
                Object startDateParam = map.get("startDate");
                Object endDateParam = map.get("endDate");
                
                // 处理开始日期参数
                if (startDateParam instanceof Date) {
                    startDate = (Date) startDateParam;
                } else if (startDateParam instanceof String) {
                    startDate = DateTimeUtils.parse((String) startDateParam);
                } else {
                    throw new KDException("startDate参数类型不支持，请传入Date对象或日期字符串");
                }
                
                // 处理结束日期参数
                if (endDateParam instanceof Date) {
                    endDate = (Date) endDateParam;
                } else if (endDateParam instanceof String) {
                    endDate = DateTimeUtils.parse((String) endDateParam);
                } else {
                    throw new KDException("endDate参数类型不支持，请传入Date对象或日期字符串");
                }
                
                // 验证日期范围
                if (startDate.after(endDate)) {
                    throw new KDException("开始日期不能晚于结束日期");
                }
                
            } catch (Exception e) {
                if (e instanceof KDException) {
                    throw e;
                }
                throw new KDException("日期参数解析失败: " + e.getMessage());
            }
        } else {
            // 使用默认的时间范围：过去60天到当前时间
            Date currentDate = new Date();
            startDate = new Date(currentDate.getTime() - 60L * 24 * 60 * 60 * 1000);
            endDate = currentDate;
        }
        
        // 按照日期降序，每7天一组遍历
        Date batchEndDate = endDate;
        while (batchEndDate.after(startDate)) {
            // 计算当前批次的开始时间（7天前）
            Date batchStartDate = new Date(batchEndDate.getTime() - 7L * 24 * 60 * 60 * 1000);
            
            // 确保不超过总的开始时间
            if (batchStartDate.before(startDate)) {
                batchStartDate = startDate;
            }
            
            // 构建请求参数JSON对象
            JSONObject postJson = new JSONObject();
            postJson.put("rq_start", DATE_SDF.format(batchStartDate) + " 00:00:00");
            postJson.put("rq_end", DATE_SDF.format(batchEndDate) + " 23:59:59");
            
            // 调用批发通知单同步服务
            ApiResult apiResult = WholeSaleNoticeBillMserviceHelper.getWholeSaleNoticeBill(postJson);
            
            // 处理同步结果
            if (!apiResult.getSuccess()) {
                throw new KDException("批发通知单同步失败: " + apiResult.getMessage());
            }
            
            // 移动到下一个7天批次（向前推7天）
            batchEndDate = new Date(batchStartDate.getTime() - 1000); // 减去1秒避免重复
        }
    }
}
