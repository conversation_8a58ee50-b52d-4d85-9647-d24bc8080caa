package kd.bos.tcbj.fi.cas.helper;

import java.rmi.RemoteException;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONArray;
import com.kingdee.util.StringUtils;

import json.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.fi.cas.webservice.IntfsServiceWSProxy;

/**
 * 与主数据系统交互的工具类
 * @auditor yanzuliang
 * @date 2023年8月29日
 * 
 */
public class SynDataToMdmHelper {
	
	// 创建logger
    private final static Log logger = LogFactory.getLog("SynDataToMdmHelper");
    
    /**
     * 新增启用的数据同步到主数据系统，只有启用状态的数据才能传这个接口，禁用后启用的不能传这个接口
     * @param idSet 需要传递的数据ID集合
     * @param state 状态,0-停用，1-启用
     * <AUTHOR>
     * @date 2023-08-29
     */
    public static ApiResult importDataToMdm(Set<String> idSet, String state) {
    	ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		StringBuffer errorMsg = new StringBuffer();
		Set<String> successSet = new HashSet<String>();  // 同步成功ID
		Set<String> failSet = new HashSet<String>();  // 同步失败ID
    	
		Map<String,String> mdmServiceMap = getMdmServiceConfig();
		if (StringUtils.isEmpty(mdmServiceMap.get("url"))
				|| StringUtils.isEmpty(mdmServiceMap.get("user"))
				|| StringUtils.isEmpty(mdmServiceMap.get("pwd")) ) {
			return ApiResult.fail("MDM接口参数配置缺失，请检查参数配置！菜单路径：财务云-出纳-基础设置-参数设置。");
		}
        
        // 请求接口
        IntfsServiceWSProxy proxy = new IntfsServiceWSProxy();
        // 设置参数配置
        proxy.setEndpoint(mdmServiceMap.get("url"));
		
    	DynamicObjectCollection col = QueryServiceHelper.query("yd_yusandhe", "id", new QFilter("id", QCP.in, idSet).toArray());
		if (col.size() == 0) {
			return ApiResult.fail("无数据需要处理！");
		}
		
		JSONObject paramObj = new JSONObject();
		JSONArray list = new JSONArray();
		Set<String> tmpSet = new HashSet<String>();  // 临时中转变量
		for (DynamicObject tmpObj : col) {
			tmpSet.add(tmpObj.getString("id"));
			DynamicObject bill = BusinessDataServiceHelper.loadSingle(tmpObj.getString("id"), "yd_yusandhe");
			DynamicObjectCollection enCol = bill.getDynamicObjectCollection("yd_hslx");
			if(enCol.size() == 0) continue;
			// 封装报文
			JSONObject dataObj = new JSONObject();
			JSONObject budgetObj = new JSONObject();
			budgetObj.put("FID", bill.getString("id"));
			budgetObj.put("FNUMBER_CODE", bill.getString("number"));
			budgetObj.put("FNUMBER_NAME", bill.getString("name"));
			budgetObj.put("COMPANY_CODE", bill.getString("yd_companycode.number"));
			budgetObj.put("COMPANY_NAME", bill.getString("yd_companycode.name"));
			budgetObj.put("DEPARTMENT_CODE", bill.getString("yd_departmentcode.number"));
			budgetObj.put("DEPARTMENT_NAME", bill.getString("yd_departmentcode.name"));
			budgetObj.put("BUDGET_SHORT_FID", bill.getString("yd_budgetitems.id"));
			budgetObj.put("BUDGET_SHORT_CODE", bill.getString("yd_budgetitems.number"));
			budgetObj.put("BUDGET_SHORT_NAME", bill.getString("yd_budgetitems.name"));
			budgetObj.put("BUDGET_SHORT_LONGNAME", bill.getString("yd_budgetitems.fullname"));
			// 父级预算项目，默认放空值
			budgetObj.put("PARENT_BUDGET_SHORT_FID", "");
			budgetObj.put("PARENT_BUDGET_SHORT_CODE", "");
			budgetObj.put("PARENT_BUDGET_SHORT_NAME", "");
			DynamicObject budgetitemObj = bill.getDynamicObject("yd_budgetitems");
			if (budgetitemObj.getDynamicObject("parent") != null) {
				String parentid = budgetitemObj.getString("parent.id");
				DynamicObject parentObj = BusinessDataServiceHelper.loadSingle(parentid, "er_expenseitemedit", "id,name,number");
				budgetObj.put("PARENT_BUDGET_SHORT_FID", parentObj.getString("id"));
				budgetObj.put("PARENT_BUDGET_SHORT_CODE", parentObj.getString("number"));
				budgetObj.put("PARENT_BUDGET_SHORT_NAME", parentObj.getString("name"));
			}
			
			budgetObj.put("ONE_BUDGET_CODE", bill.getString("yd_firstlevelbudget.number"));
			budgetObj.put("ONE_BUDGET_NAME", bill.getString("yd_firstlevelbudget.name"));
			// 费用类型编码
			budgetObj.put("EXPENSETYPE_CODE", bill.getString("yd_expensetype"));
			
			DynamicObject tmpEnObj = enCol.get(0);
			String enId = tmpEnObj.getString("id");
			// 预算品牌
			DynamicObjectCollection brandCol = tmpEnObj.getDynamicObjectCollection("yd_brandchoice");
			JSONArray childList = new JSONArray();
			Set<String> brandSet = new HashSet<String>();  // 用于判断是否存在重复品牌数据
			for (DynamicObject tmpBrandObj : brandCol) {
				String brandId = tmpBrandObj.getDynamicObject("fbasedataid").getString("id");
				if (brandSet.contains(brandId)) {
					continue;
				}
				brandSet.add(brandId);
				JSONObject enObj = new JSONObject();
				JSONObject brandObj = new JSONObject();
				brandObj.put("ID", bill.getString("id"));
				brandObj.put("BRAND_FID", tmpBrandObj.getDynamicObject("fbasedataid").getString("id"));
				brandObj.put("BRAND_CODE", tmpBrandObj.getDynamicObject("fbasedataid").getString("number"));
				brandObj.put("BRAND_NAME", tmpBrandObj.getDynamicObject("fbasedataid").getString("name"));
				brandObj.put("SUBTABLE_ID", enId);
				enObj.put("BUDGET_BRAND", brandObj);
				childList.add(enObj);
			}
			budgetObj.put("CHILDMODELS_2", childList);
			
			// 会计科目
			DynamicObject accountViewObj = tmpEnObj.getDynamicObject("yd_kj_number");
			JSONArray acChildList = new JSONArray();
			JSONObject acEnObj = new JSONObject();
			if (accountViewObj != null) {
				JSONObject acObj = new JSONObject();
				acObj.put("ID", bill.getString("id"));
				acObj.put("ACCOUNTVIEW_FID", accountViewObj.getString("id"));
				acObj.put("ACCOUNTVIEW_CODE", accountViewObj.getString("number"));
				acObj.put("ACCOUNTVIEW_NAME", accountViewObj.getString("name"));
				acObj.put("SUBTABLE_ID", enId);
				acEnObj.put("BUDGET_ACCOUNTVIEW", acObj);
				acChildList.add(acEnObj);
			}
			budgetObj.put("CHILDMODELS_1", acChildList);
			
			// 预算渠道
			DynamicObjectCollection channelCol = tmpEnObj.getDynamicObjectCollection("yd_channelchoice");
			JSONArray channelChildList = new JSONArray();
			Set<String> channelSet = new HashSet<String>();  // 用于判断是否存在重复渠道数据
			for (DynamicObject tmpChannelObj : channelCol) {
				String channelId = tmpChannelObj.getDynamicObject("fbasedataid").getString("id");
				if (channelSet.contains(channelId)) {
					continue;
				}
				channelSet.add(channelId);
				JSONObject enObj = new JSONObject();
				JSONObject channelObj = new JSONObject();
				channelObj.put("ID", bill.getString("id"));
				channelObj.put("CHANNEL_FID", tmpChannelObj.getDynamicObject("fbasedataid").getString("id"));
				channelObj.put("CHANNEL_CODE", tmpChannelObj.getDynamicObject("fbasedataid").getString("number"));
				channelObj.put("CHANNEL_NAME", tmpChannelObj.getDynamicObject("fbasedataid").getString("name"));
				channelObj.put("SUBTABLE_ID", enId);
				enObj.put("BUDGET_CHANNEL", channelObj);
				channelChildList.add(enObj);
			}
			budgetObj.put("CHILDMODELS_4", channelChildList);
			
			// 预算店铺
			DynamicObjectCollection shopCol = tmpEnObj.getDynamicObjectCollection("yd_shopchoice");
			JSONArray shopChildList = new JSONArray();
			Set<String> shopSet = new HashSet<String>();  // 用于判断是否存在重复店铺数据
			for (DynamicObject tmpShopObj : shopCol) {
				String shopId = tmpShopObj.getDynamicObject("fbasedataid").getString("id");
				if (shopSet.contains(shopId)) {
					continue;
				}
				shopSet.add(shopId);
				JSONObject enObj = new JSONObject();
				JSONObject shopObj = new JSONObject();
				shopObj.put("ID", bill.getString("id"));
				shopObj.put("SHOP_FID", tmpShopObj.getDynamicObject("fbasedataid").getString("id"));
				shopObj.put("SHOP_CODE", tmpShopObj.getDynamicObject("fbasedataid").getString("number"));
				shopObj.put("SHOP_NAME", tmpShopObj.getDynamicObject("fbasedataid").getString("name"));
				shopObj.put("SUBTABLE_ID", enId);
				enObj.put("BUDGET_SHOP", shopObj);
				shopChildList.add(enObj);
			}
			budgetObj.put("CHILDMODELS_3", shopChildList);
			
			dataObj.put("BUDGET", budgetObj);
			list.add(dataObj);
			// 如果超过20就先调用接口并清空，最后不足20的就传递
			if (list.size() == 5) {
				paramObj.put("LIST", list);
				try {
					logger.info("请求主数据报文："+paramObj.toJSONString());
					String resp = proxy.importData("BUDGET", paramObj.toJSONString(), "JSON", state, mdmServiceMap.get("user"), mdmServiceMap.get("pwd"));
					logger.info("主数据返回报文："+resp);
					if (!StringUtils.isEmpty(resp) && !StringUtils.equalsIgnoreCase("1", resp)) {
						errorMsg.append("推送主数据接口失败，请联系主数据管理员，接口返回："+resp+"；");
						failSet.addAll(tmpSet);
					}
					if ("1".equalsIgnoreCase(resp)) {
						successSet.addAll(tmpSet);
					}
				} catch (RemoteException e) {
					errorMsg.append("主数据接口访问失败，请联系管理员！错误信息为："+e.getLocalizedMessage()+"；");
					failSet.addAll(tmpSet);
				}
				
				paramObj = new JSONObject();
				list = new JSONArray();
				tmpSet = new HashSet<String>();  // 临时中转变量
			}
		}
		paramObj.put("LIST", list);
		
		try {
			if (list.size() > 0) {
				logger.info("请求主数据报文："+paramObj.toJSONString());
				String resp = proxy.importData("BUDGET", paramObj.toJSONString(), "JSON", state, mdmServiceMap.get("user"), mdmServiceMap.get("pwd"));
				logger.info("主数据返回报文："+resp);
				if (!StringUtils.isEmpty(resp) && !StringUtils.equalsIgnoreCase("1", resp)) {
					errorMsg.append("推送主数据接口失败，请联系主数据管理员，接口返回："+resp+"；");
					failSet.addAll(tmpSet);
				}
				if ("1".equalsIgnoreCase(resp)) {
					successSet.addAll(tmpSet);
				}
			}
		} catch (RemoteException e) {
			errorMsg.append("主数据接口访问失败，请联系管理员！错误信息为："+e.getLocalizedMessage()+"；");
			failSet.addAll(tmpSet);
		}
		
		// 反写数据同步标识
		updateDataSynedMark(successSet, failSet, errorMsg);
    	
		if (errorMsg.length() > 0) {
			return ApiResult.fail(errorMsg.toString());
		}
		
		return apiResult;
    }
    
    /**
     * 启用或禁用数据时通过此接口传递给MDM系统
     * 仅适用场景：启用后禁用、禁用后启用、反审核（需要同时修改为禁用状态，这样用户需要操作启用，启用就要调用此接口）
     * @param idSet 需要传递的数据ID集合
     * @param state 状态,0-停用，1-启用
     * <AUTHOR>
     * @date 2023-08-29
     */
    public static ApiResult updateDataUseStatus(Set<String> idSet, String state) {
    	ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		StringBuffer errorMsg = new StringBuffer();
		Set<String> successSet = new HashSet<String>();  // 同步成功ID
		Set<String> failSet = new HashSet<String>();  // 同步失败ID
		
		Map<String,String> mdmServiceMap = getMdmServiceConfig();
		if (StringUtils.isEmpty(mdmServiceMap.get("url"))
				|| StringUtils.isEmpty(mdmServiceMap.get("user"))
				|| StringUtils.isEmpty(mdmServiceMap.get("pwd")) ) {
			return ApiResult.fail("MDM接口参数配置缺失，请检查参数配置！菜单路径：财务云-出纳-基础设置-参数设置。");
		}
		
		// 请求接口
        IntfsServiceWSProxy proxy = new IntfsServiceWSProxy();
        // 设置参数配置
        proxy.setEndpoint(mdmServiceMap.get("url"));
		
		DynamicObjectCollection col = QueryServiceHelper.query("yd_yusandhe", "id", new QFilter("id", QCP.in, idSet).toArray());
		if (col.size() == 0) {
			return ApiResult.fail("无数据需要处理！");
		}
		
		JSONObject paramObj = new JSONObject();
		JSONArray list = new JSONArray();
		Set<String> tmpSet = new HashSet<String>();  // 临时中转变量
		for (DynamicObject tmpObj : col) {
			tmpSet.add(tmpObj.getString("id"));
			// 封装报文
			JSONObject dataObj = new JSONObject();
			JSONObject budgetObj = new JSONObject();
			budgetObj.put("FID", tmpObj.getString("id"));
			dataObj.put("BUDGET", budgetObj);
			list.add(dataObj);
		}
		paramObj.put("LIST", list);
		
		try {
			logger.info("请求主数据报文："+paramObj.toJSONString());
			String resp = proxy.updateDataUseStatus("BUDGET", paramObj.toJSONString(), "JSON", state, mdmServiceMap.get("user"), mdmServiceMap.get("pwd"));
			logger.info("主数据返回报文："+resp);
			if (!StringUtils.isEmpty(resp) && !StringUtils.equalsIgnoreCase("1", resp)) {
				errorMsg.append("推送主数据接口失败，请联系主数据管理员，接口返回："+resp+"；");
				failSet.addAll(tmpSet);
			}
			if ("1".equalsIgnoreCase(resp)) {
				successSet.addAll(tmpSet);
			}
		} catch (RemoteException e) {
			errorMsg.append("主数据接口访问失败，请联系管理员！错误信息为："+e.getLocalizedMessage()+"；");
			failSet.addAll(tmpSet);
		}
		
		// 反写数据同步标识
		updateDataSynedMark(successSet, failSet, errorMsg);
    	
		if (errorMsg.length() > 0) {
			return ApiResult.fail(errorMsg.toString());
		}
		
		return apiResult;
    }

	private static Map<String, String> getMdmServiceConfig() {
		Map<String,String> mdmServiceMap = new HashMap<String,String>();
		
		// MDM接口地址
		DynamicObject[] url = BusinessDataServiceHelper.load("yd_paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"MDM_SERVICE_URL")});
		if (url.length > 0) {
			mdmServiceMap.put("url", url[0].getString("name"));
		}
        
        // MDM接口帐号
		DynamicObject[] user = BusinessDataServiceHelper.load("yd_paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"MDM_SERVICE_USER")});
		if (user.length > 0) {
			mdmServiceMap.put("user", user[0].getString("name"));
		}
        
        // MDM接口密码
		DynamicObject[] pwd = BusinessDataServiceHelper.load("yd_paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"MDM_SERVICE_PWD")});
		if (pwd.length > 0) {
			mdmServiceMap.put("pwd", pwd[0].getString("name"));
		}
		
		return mdmServiceMap;
	}

	/**
	 * 更新数据同步标识
	 * @param successSet 同步成功的ID
	 * @param failSet  同步失败的ID
	 * @param errorMsg 失败原因，截取300
	 */
	private static void updateDataSynedMark(Set<String> successSet, Set<String> failSet, StringBuffer errorMsg) {
		if (successSet.size() > 0) {
			DynamicObject[] objs = BusinessDataServiceHelper.load("yd_yusandhe", "id,yd_besynedmdm,yd_lastsynedtime,yd_synederror,yd_hadunaudit", new QFilter("id",QCP.in,successSet).toArray());
			for (DynamicObject obj : objs) {
				obj.set("yd_besynedmdm", true);
				obj.set("yd_lastsynedtime", new Date());
				obj.set("yd_synederror", "");
				obj.set("yd_hadunaudit", false);
			}
			SaveServiceHelper.save(objs);
		}
		
		if (failSet.size() > 0) {
			DynamicObject[] objs = BusinessDataServiceHelper.load("yd_yusandhe", "id,yd_besynedmdm,yd_lastsynedtime,yd_synederror", new QFilter("id",QCP.in,failSet).toArray());
			for (DynamicObject obj : objs) {
				obj.set("yd_besynedmdm", false);
				obj.set("yd_lastsynedtime", new Date());
				obj.set("yd_synederror", errorMsg.length() > 300 ? errorMsg.substring(0,300) : errorMsg.toString());
			}
			SaveServiceHelper.save(objs);
		}
	}
    
}
