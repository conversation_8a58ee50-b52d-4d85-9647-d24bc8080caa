package kd.bos.tcbj.fi.cas.helper;

import java.rmi.RemoteException;

import kd.bos.tcbj.fi.cas.webservice.IntfsServiceWSProxy;

/**
 * @auditor ya<PERSON><PERSON><PERSON>
 * @date 2023年8月23日
 * 
 */
public class TestWebServiceHelper {

	public static void main(String[] args) {
		IntfsServiceWSProxy proxy = new IntfsServiceWSProxy();
		try {
			String respStr = proxy.importData("BUDGET", "{\"LIST\":[]}", "JSON", "1", "ysfk", "8bd24fc87f330f63609c74dafc7436fc");
			System.out.println(respStr);
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

}
