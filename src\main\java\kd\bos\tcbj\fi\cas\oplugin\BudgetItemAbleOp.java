package kd.bos.tcbj.fi.cas.oplugin;

import java.util.HashSet;
import java.util.Set;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.exception.KDException;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.fi.cas.helper.SynDataToMdmHelper;

/**
 * 预算项目按部门绑定表操作
 * 1、启用操作：调用主数据启用接口以及数据推送接口，使得可以更新数据
 * 2、禁用操作：调用主数据禁用接口
 * 3、反审核操作：调用主数据禁用接口，同时反写数据为禁用状态，使得用户可以触发启用然后传给主数据
 * 4、同步主数据功能操作：只能获取未同步主数据的或者已同步主数据但是同步失败的数据
 * @auditor yanzuliang
 * @date 2023年8月29日
 * 
 */
public class BudgetItemAbleOp extends AbstractOperationServicePlugIn {
	
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("id");
		e.getFieldKeys().add("billno");
		e.getFieldKeys().add("yd_besynedmdm");
		e.getFieldKeys().add("enable");
		e.getFieldKeys().add("status");
		e.getFieldKeys().add("yd_hadunaudit");
	}
	
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		
		DynamicObject[] objs = e.getDataEntities();
		Set<String> idSet = new HashSet<String>();
		for (DynamicObject obj : objs) {
			if (obj.getBoolean("yd_besynedmdm")) {  // 同步过MDM才需要这么操作，没同步过的不需要，让调度计划定时跑，禁用的也不用同步到MDM
				idSet.add(obj.getString("id"));
			}
		}
		
		if ("unaudit".equalsIgnoreCase(e.getOperationKey())) {  // 反审核
			SynDataToMdmHelper.updateDataUseStatus(idSet, "0");
			// 反写使用状态为禁用状态，需要去掉审核操作里面对禁用状态的控制
			for (DynamicObject obj : objs) {
				obj.set("yd_hadunaudit", true);
			}
			SaveServiceHelper.save(objs);
		} else if ("disable".equalsIgnoreCase(e.getOperationKey())) {  // 禁用
			SynDataToMdmHelper.updateDataUseStatus(idSet, "0");
		} else if ("enable".equalsIgnoreCase(e.getOperationKey())) {  // 启用
			SynDataToMdmHelper.updateDataUseStatus(idSet, "1");
			SynDataToMdmHelper.importDataToMdm(idSet, "1");
		} else if ("synMdm".equalsIgnoreCase(e.getOperationKey())) {  // 同步主数据
			// 只处理未同步主数据的或者同步失败的数据，还需要处理重推的场景，失败的有：新增推送失败；禁用后启用推送失败；启用后禁用推送失败；反审核推送失败；
			// 未同步的已审核的启用的传导入接口；未同步的保存状态或禁用状态的数据传禁用接口；
			Set<String> unSynedSet = new HashSet<String>();
			Set<String> synedAbleSet = new HashSet<String>();
			Set<String> synedUnableSet = new HashSet<String>();
			for (DynamicObject obj : objs) {
				String ableState = obj.getString("enable");
				String billState = obj.getString("status");
				Boolean beSyned = obj.getBoolean("yd_besynedmdm");
				if (!beSyned && "1".equalsIgnoreCase(ableState) && "C".equalsIgnoreCase(billState)) {
					unSynedSet.add(obj.getString("id"));
				}
				if (beSyned && "1".equalsIgnoreCase(ableState) && "C".equalsIgnoreCase(billState)) {
					synedAbleSet.add(obj.getString("id"));
				}
				if (!beSyned && ("0".equalsIgnoreCase(ableState) || "A".equalsIgnoreCase(billState))) {
					synedUnableSet.add(obj.getString("id"));
				}
			}
			
			StringBuffer errorMsg = new StringBuffer();
			if (unSynedSet.size() > 0) {
				ApiResult result = SynDataToMdmHelper.importDataToMdm(unSynedSet, "1");
				if (!result.getSuccess()) {
					errorMsg.append(result.getMessage());
				}
			}
			if (synedAbleSet.size() > 0) {
				SynDataToMdmHelper.updateDataUseStatus(synedAbleSet, "1");
				ApiResult result = SynDataToMdmHelper.importDataToMdm(synedAbleSet, "1");
				if (!result.getSuccess()) {
					errorMsg.append(result.getMessage());
				}
			}
			if (synedUnableSet.size() > 0) {
				ApiResult result = SynDataToMdmHelper.updateDataUseStatus(synedUnableSet, "0");
				if (!result.getSuccess()) {
					errorMsg.append(result.getMessage());
				}
			}
			
			if (errorMsg.length() > 0) {
				throw new KDException(errorMsg.toString());
			}
			
		}
	}
}
