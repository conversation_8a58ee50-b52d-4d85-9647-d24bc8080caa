package kd.bos.tcbj.fi.cas.schedulejob;

import java.rmi.RemoteException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.fi.cas.helper.SynDataToMdmHelper;
import kd.bos.tcbj.fi.cas.webservice.IntfsServiceWSProxy;

/**
 * 同步主数据调度计划类
 * 1、同步《预算项目按部门设置绑定表》到MDM系统：获取最后修改时间在前半个小时内的已审核的数据，包含启用和禁用状态；
 * @auditor yanzuliang
 * @date 2023年8月29日
 * 
 */
public class SynDataToMDMTask extends AbstractTask {

	@Override
	public void execute(RequestContext ctx, Map<String, Object> paramMap) throws KDException {
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.add(11, -1);
		// 获取数据
		QFilter filter = new QFilter("modifytime", QCP.large_equals, cal.getTime());
		filter.and(new QFilter("status", QCP.equals, "C"));
		filter.and(new QFilter("enable", QCP.equals, "1"));
		DynamicObjectCollection col = QueryServiceHelper.query("yd_yusandhe", "id,enable,yd_hadunaudit", filter.toArray());
		if (col.size() == 0) return;
		
		Set<String> ableSet = new HashSet<String>();  // 启用状态数据
		Set<String> unauditSet = new HashSet<String>();  // 被反审过的数据
		for (DynamicObject obj : col) {
			if (obj.getBoolean("yd_hadunaudit")) {
				unauditSet.add(obj.getString("id"));
			} else {
				ableSet.add(obj.getString("id"));
			}
		}
		
		StringBuffer result = new StringBuffer();
		
		if (ableSet.size() > 0) {
			ApiResult ableResp = SynDataToMdmHelper.importDataToMdm(ableSet, "1");
			if (!ableResp.getSuccess()) {
				result.append("新增启用数据同步失败："+ableResp.getMessage());
			}
		}
		
		if (unauditSet.size() > 0) {
			SynDataToMdmHelper.updateDataUseStatus(unauditSet, "1");
			ApiResult ableResp = SynDataToMdmHelper.importDataToMdm(unauditSet, "1");
			if (!ableResp.getSuccess()) {
				result.append("新增启用数据同步失败："+ableResp.getMessage());
			}
		}
		
		if (result.length() > 0) {
			throw new KDBizException("同步失败！"+result.toString());
		}
	}

}
