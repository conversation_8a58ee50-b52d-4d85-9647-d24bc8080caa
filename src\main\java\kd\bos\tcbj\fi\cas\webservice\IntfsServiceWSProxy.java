package kd.bos.tcbj.fi.cas.webservice;

public class IntfsServiceWSProxy implements kd.bos.tcbj.fi.cas.webservice.IntfsServiceWS_PortType {
  private String _endpoint = null;
  private kd.bos.tcbj.fi.cas.webservice.IntfsServiceWS_PortType intfsServiceWS_PortType = null;
  
  public IntfsServiceWSProxy() {
    _initIntfsServiceWSProxy();
  }
  
  public IntfsServiceWSProxy(String endpoint) {
    _endpoint = endpoint;
    _initIntfsServiceWSProxy();
  }
  
  private void _initIntfsServiceWSProxy() {
    try {
      intfsServiceWS_PortType = (new kd.bos.tcbj.fi.cas.webservice.IntfsServiceWS_ServiceLocator()).getintfsServiceWSPort();
      if (intfsServiceWS_PortType != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)intfsServiceWS_PortType)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)intfsServiceWS_PortType)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (intfsServiceWS_PortType != null)
      ((javax.xml.rpc.Stub)intfsServiceWS_PortType)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public kd.bos.tcbj.fi.cas.webservice.IntfsServiceWS_PortType getIntfsServiceWS_PortType() {
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType;
  }
  
  public java.lang.String getModelInfo(java.lang.String modelCode, java.lang.String dataType, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.getModelInfo(modelCode, dataType, userName, password);
  }
  
  public java.lang.String importData(java.lang.String modelCode, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.importData(modelCode, dataStr, dataType, dataStatus, userName, password);
  }
  
  public java.lang.String updateDataUseStatus(java.lang.String modelCode, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.updateDataUseStatus(modelCode, dataStr, dataType, dataStatus, userName, password);
  }
  
  public java.lang.String importDataProCode(java.lang.String modelCode, java.lang.String codeField, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.importDataProCode(modelCode, codeField, dataStr, dataType, dataStatus, userName, password);
  }
  
  public java.lang.String produceCode(java.lang.String modelCode, java.lang.String codeField, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.produceCode(modelCode, codeField, dataStr, dataType, dataStatus, userName, password);
  }
  
  public java.lang.String importDicData(java.lang.String dicCode, java.lang.String dataStr, java.lang.String dataType, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.importDicData(dicCode, dataStr, dataType, userName, password);
  }
  
  public java.lang.String updateDicDataStatus(java.lang.String dicCode, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.updateDicDataStatus(dicCode, dataStr, dataType, dataStatus, userName, password);
  }
  
  public java.lang.String getModelDatas(java.lang.String modelCode, java.lang.String queryCondition, java.lang.String queryField, java.lang.String dataType, java.lang.String isVague, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.getModelDatas(modelCode, queryCondition, queryField, dataType, isVague, userName, password);
  }
  
  public java.lang.String getDicDatas(java.lang.String dicCode, java.lang.String queryStr, java.lang.String dataType, java.lang.String isVague, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.getDicDatas(dicCode, queryStr, dataType, isVague, userName, password);
  }
  
  public java.lang.String getCodeInfo(java.lang.String modelCode, java.lang.String codeField, java.lang.String queryStr, java.lang.String dataType, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException{
    if (intfsServiceWS_PortType == null)
      _initIntfsServiceWSProxy();
    return intfsServiceWS_PortType.getCodeInfo(modelCode, codeField, queryStr, dataType, userName, password);
  }
  
  
}