/**
 * IntfsServiceWS_PortType.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package kd.bos.tcbj.fi.cas.webservice;

public interface IntfsServiceWS_PortType extends java.rmi.Remote {
    public java.lang.String getModelInfo(java.lang.String modelCode, java.lang.String dataType, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String importData(java.lang.String modelCode, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String updateDataUseStatus(java.lang.String modelCode, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String importDataProCode(java.lang.String modelCode, java.lang.String codeField, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String produceCode(java.lang.String modelCode, java.lang.String codeField, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String importDicData(java.lang.String dicCode, java.lang.String dataStr, java.lang.String dataType, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String updateDicDataStatus(java.lang.String dicCode, java.lang.String dataStr, java.lang.String dataType, java.lang.String dataStatus, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String getModelDatas(java.lang.String modelCode, java.lang.String queryCondition, java.lang.String queryField, java.lang.String dataType, java.lang.String isVague, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String getDicDatas(java.lang.String dicCode, java.lang.String queryStr, java.lang.String dataType, java.lang.String isVague, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String getCodeInfo(java.lang.String modelCode, java.lang.String codeField, java.lang.String queryStr, java.lang.String dataType, java.lang.String userName, java.lang.String password) throws java.rmi.RemoteException;
}
