/**
 * IntfsServiceWS_ServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package kd.bos.tcbj.fi.cas.webservice;

public class IntfsServiceWS_ServiceLocator extends org.apache.axis.client.Service implements kd.bos.tcbj.fi.cas.webservice.IntfsServiceWS_Service {

    public IntfsServiceWS_ServiceLocator() {
    }


    public IntfsServiceWS_ServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public IntfsServiceWS_ServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for intfsServiceWSPort
    private java.lang.String intfsServiceWSPort_address = "http://***************:80/cidp/ws/intfsServiceWS";

    public java.lang.String getintfsServiceWSPortAddress() {
        return intfsServiceWSPort_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String intfsServiceWSPortWSDDServiceName = "intfsServiceWSPort";

    public java.lang.String getintfsServiceWSPortWSDDServiceName() {
        return intfsServiceWSPortWSDDServiceName;
    }

    public void setintfsServiceWSPortWSDDServiceName(java.lang.String name) {
        intfsServiceWSPortWSDDServiceName = name;
    }

    public kd.bos.tcbj.fi.cas.webservice.IntfsServiceWS_PortType getintfsServiceWSPort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(intfsServiceWSPort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getintfsServiceWSPort(endpoint);
    }

    public kd.bos.tcbj.fi.cas.webservice.IntfsServiceWS_PortType getintfsServiceWSPort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            kd.bos.tcbj.fi.cas.webservice.IntfsServiceWSPortBindingStub _stub = new kd.bos.tcbj.fi.cas.webservice.IntfsServiceWSPortBindingStub(portAddress, this);
            _stub.setPortName(getintfsServiceWSPortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setintfsServiceWSPortEndpointAddress(java.lang.String address) {
        intfsServiceWSPort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (kd.bos.tcbj.fi.cas.webservice.IntfsServiceWS_PortType.class.isAssignableFrom(serviceEndpointInterface)) {
                kd.bos.tcbj.fi.cas.webservice.IntfsServiceWSPortBindingStub _stub = new kd.bos.tcbj.fi.cas.webservice.IntfsServiceWSPortBindingStub(new java.net.URL(intfsServiceWSPort_address), this);
                _stub.setPortName(getintfsServiceWSPortWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("intfsServiceWSPort".equals(inputPortName)) {
            return getintfsServiceWSPort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://www.meritit.com/ws/IntfsServiceWS", "intfsServiceWS");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://www.meritit.com/ws/IntfsServiceWS", "intfsServiceWSPort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("intfsServiceWSPort".equals(portName)) {
            setintfsServiceWSPortEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
