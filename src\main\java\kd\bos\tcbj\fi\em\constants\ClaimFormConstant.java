package kd.bos.tcbj.fi.em.constants;

/**
 * @package: kd.bos.tcbj.fi.em.constants.ClaimFormConstant
 * @className ClaimFormConstant
 * @author: hst
 * @createDate: 2024/01/19
 * @description: EAS索赔单汇总中间表常量类
 * @version: v1.0
 */
public class ClaimFormConstant {
    /** 单据标识 **/
    public static String MAIN_ENTITY = "yd_claimform";
    /** 单据标识 **/
    public static String BILLNO_FIELD = "billno";
    /** 库存组织 **/
    public static String ORG_FIELD = "yd_org";
    /** 库存编码 **/
    public static String ORGNUM_FIELD = "yd_orgnum";
    /** 客户 **/
    public static String CUSTOMER_FIELD = "yd_customer";
    /** 客户编码 **/
    public static String CUSNUM_FIELD = "yd_cusnum";
    /** 年度 **/
    public static String YEAR_FIELD = "yd_year";
    /** 月份 **/
    public static String MONTH_FIELD = "yd_month";
    /** 总金额 **/
    public static String AMOUNT_FIELD = "yd_totalamount";
    /** 是否已生成预付款请款单 **/
    public static String ISCREATE_FIELD = "yd_iscreate";
    /** 预付款请款单号 **/
    public static String REQBILLNO_FIELD = "yd_reqbillno";
    /** 单据状态 **/
    public static String STATUS_FIELD = "billstatus";
    /** 处理状态 **/
    public static String BILLSTATUS_FIELD = "yd_billstatus";
    /** 下推错误信息 **/
    public static String ERRMSG_FIELD = "yd_errmsg";
    /** 业务日期 **/
    public static String BIZDATE_FIELD = "yd_bizdate";
    /** 汇总明细分录 **/
    public static String DETAIL_ENTRY = "yd_detatilentry";

    /**
     * 获取表头字段信息
     * @return
     * @author: hst
     * @createDate: 2024/02/27
     */
    public static String getHeadField () {
        return BILLNO_FIELD + "," + ORG_FIELD + "," + CUSTOMER_FIELD + ","
                + YEAR_FIELD + "," + MONTH_FIELD + "," + AMOUNT_FIELD + ","
                + ISCREATE_FIELD + "," + REQBILLNO_FIELD + "," + BILLSTATUS_FIELD + ","
                + ERRMSG_FIELD;
    }
}
