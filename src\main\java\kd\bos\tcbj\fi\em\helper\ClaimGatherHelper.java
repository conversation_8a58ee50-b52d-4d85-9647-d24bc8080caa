package kd.bos.tcbj.fi.em.helper;

import com.alibaba.fastjson.JSONObject;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.db.tx.TX;
import kd.bos.db.tx.TXHandle;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.fi.em.constants.ClaimFormConstant;
import kd.bos.tcbj.fi.em.utils.DynamicObjectUtil;
import kd.bos.tcbj.fi.em.utils.StringUtils;
import kd.isc.iscb.util.db.DataRow;
import kd.isc.iscb.util.dt.TypedArray;
import kd.bos.servicehelper.DispatchServiceHelper;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.fi.em.helper.ClaimGatherHelper
 * @className ClaimGatherHelper
 * @author: hst
 * @createDate: 2024/01/11
 * @description: 索赔单中间表业务类
 * @version: v1.0
 */
public class ClaimGatherHelper {

    /**
     * 获取EAS索赔单
     * @param inputs 服务流程输入参数
     * @param isAuto 是否自动生成请款单
     * @param qFilter 下推请款单条件
     * @return
     * @author: hst
     * @createDate: 2024/01/18
     */
    public ApiResult getEASClaimBills (Map<String,Object> inputs, Boolean isAuto, QFilter qFilter) {
        ApiResult result = new ApiResult();

        try {
            // 获取参数配置信息
            Map<String,Object> param = this.getConfigParam();

            // 构造服务流程参数
            inputs = this.buildInputParam(inputs,param);

            // 调用服务流程，获取EAS索赔单数据
            HashMap datas = this.executeIscFlowService("yd_fi_claimform",
                    Arrays.asList(inputs.values().toArray()), "execute");

            // 按指定维度汇总数据（单据编码）
            Map<String, List<Object>> resultDatas = this.summarizeDataBySpecifiedDimension(datas);

            // 将分组信息保存到中间表中
            Map<String, Integer> opResult = saveSummarizeData(resultDatas,param);

            // 是否自动下推生成预付款请款单
            if (Objects.isNull(isAuto)) {
                isAuto = ((Boolean) param.get("isauto")).booleanValue();
            }
            if (isAuto.booleanValue()) {
                if (Objects.isNull(qFilter)) {
                    this.summarizeToGenerateInvoice();
                } else {
                    this.summarizeToGenerateInvoiceByInfo(qFilter);
                }
            }

            // 构建执行结果信息
            result = buildReturnResutl(opResult);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("获取EAS索赔单失败，请联系管理员！失败原因：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取参数配置信息
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    public Map<String,Object> getConfigParam () {
        // 获取对应的节点信息
        DynamicObject node = BusinessDataServiceHelper.loadSingle("yd_param_tree",
                new QFilter("yd_model",QFilter.equals,"yd_claimform").toArray());

        if (Objects.nonNull(node)) {
            // 获取参数配置信息
            DynamicObject config = BusinessDataServiceHelper.loadSingle("yd_em_param_setting",
                    new QFilter("yd_nodeid",QFilter.equals,node.getString("yd_seq")).toArray());

            if (Objects.nonNull(config)) {
                return getConfigParam(config);
            } else {
                throw new KDBizException("获取不到参数配置，请联系管理员！");
            }
        } else {
            throw new KDBizException("获取不到参数配置节点信息，请联系管理员！");
        }
    }

    /**
     * 构造参数配置信息
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    private Map<String,Object> getConfigParam (DynamicObject config) {
        Map<String,Object> param = new HashMap<>();
        String paramStr = config.getString("yd_params_tag");

        JSONObject jsonParam = JSONObject.parseObject(paramStr);
        // 字段映射
        param.put("fieldMap",jsonParam.get("yd_fieldmap_tag"));
        // 基础资料映射
        param.put("baseMap",jsonParam.get("yd_basemap_tag"));
        // 涉及组织
        param.put("orgs","(" + jsonParam.getJSONArray("yd_org").stream()
                .map(org -> "'" + ((JSONObject) org).getJSONObject("fbasedataid").getString("yd_eas_number") + "'")
                .collect(Collectors.joining(",")) + ")");
        // 涉及客户
        param.put("customers","(" + jsonParam.getJSONArray("yd_custom").stream()
                .map(custom -> "'" + ((JSONObject) custom).getJSONObject("fbasedataid").getString("number") + "'")
                .collect(Collectors.joining(",")) + ")");
        // 转换规则ID
        param.put("isauto",jsonParam.getBoolean("yd_isauto"));
        // 转换规则ID
        param.put("botpid",jsonParam.getString("yd_botpid"));
        // 支付方式
        param.put("paymode",jsonParam.getJSONObject("yd_paymode").getString("id"));
        // 项目干系人
        param.put("holder",jsonParam.getJSONArray("yd_holder").stream()
                .map(holder -> ((JSONObject) holder).getJSONObject("fbasedataid").getString("id")).collect(Collectors.toList()));
        // update by hst 2024/03/25 费用承担部门
        param.put("costdepts",jsonParam.getJSONArray("yd_entryentity").stream()
                .collect(Collectors.toMap(costDept -> ((JSONObject) costDept).getJSONObject("yd_costorg").getString("id"),
                        costDept -> costDept)));
        // update by hst 2024/05/10 不下推请款单的客户
        param.put("unpushs",jsonParam.getJSONArray("yd_unpushcus").stream()
                .map(custom -> ((JSONObject) custom).getJSONObject("fbasedataid").getString("id"))
                .collect(Collectors.toList()));

        return param;
    }

    /**
     * 构造服务流程参数
     * @param input 输入参数
     * @param param 参数配置
     * @return
     * @author: hst
     * @createDate: 2024/02/27
     */
    private Map<String,Object> buildInputParam (Map<String,Object> input,Map<String,Object> param) {
        // 年度 + 月份
        if (!input.containsKey("month")) {
            input.put("month",new SimpleDateFormat("yyyyMM").format(new Date()));
        }
        // 库存组织
        if (!input.containsKey("orgs")) {
            input.put("orgs",param.containsKey("orgs") ? param.get("orgs") : "('')");
        }
        // 客户
        if (!input.containsKey("customers")) {
            input.put("customers",param.containsKey("customers") ? param.get("customers") : "('')");
        }
        return input;
    }

    /**
     * 启动服务流程
     * @param flowNumber 服务流程编码
     * @param inputs 入参
     * @param type 执行方式（execute：同步，start：异步）
     * @return
     */
    private HashMap executeIscFlowService (String flowNumber, List<Object> inputs, String type) {
        HashMap datas = DispatchServiceHelper.invokeBizService("isc","iscb",
                "IscFlowService",type,flowNumber,inputs);
        return datas;
    }

    /**
     * 按指定维度汇总数据（单据编码）
     * @param datas 待汇总的数据
     * @author: hst
     * @createDate: 2024/01/18
     */
    private Map<String,List<Object>> summarizeDataBySpecifiedDimension (HashMap datas) {
        Map<String,List<Object>> result = new HashMap<>();

        if (Objects.nonNull(datas) && datas.size() > 0) {
            // 获取输出参数集合
            LinkedHashMap output = (LinkedHashMap) datas.get("output");
            // 获取查询到的数据集合
            if (Objects.nonNull(output) && output.size() > 0) {
                TypedArray array = (TypedArray) output.get("datas");
                Object[] list = array.toArray();

                // 按维度分组
                if (list.length > 0) {
                    result = Arrays.asList(list).stream().collect(Collectors.groupingBy(data->
                            ((DataRow) data).get("fnumber").toString()
                    ));
                }
            }
        }

        return result;
    }

    /**
     * 保存汇总后的数据
     * @param resultDatas 汇总的数据
     * @param param 配置信息
     * @author: hst
     * @createDate: 2024/01/19
     */
    private Map<String,Integer> saveSummarizeData (Map<String,List<Object>> resultDatas, Map<String,Object> param) {
        // 记录操作条数
        Map<String,Integer> result = new HashMap<>();
        // 需要进行保存的数据
        List<DynamicObject> bills = new ArrayList<>();
        // 字段映射
        Map<String,String> fieldMap = param.containsKey("fieldMap") ?
                JSONObject.parseObject(param.get("fieldMap").toString(),Map.class) : new HashMap<>();
        // 基础资料映射
        Map<String,String> baseMap = param.containsKey("baseMap") ?
                JSONObject.parseObject(param.get("baseMap").toString(),Map.class) : new HashMap<>();
        if (Objects.isNull(fieldMap) || fieldMap.size() == 0) {
            throw new KDBizException("请在参数配置中维护字段映射!");
        }

        for (Map.Entry<String,List<Object>> keySet : resultDatas.entrySet()) {
            List<Object> entryDatas = keySet.getValue();
            if (entryDatas.size() > 0) {
                String key = keySet.getKey();

                // 通过特定维度获取单据
                DynamicObject bill = this.getBillBySpecifiedDimension(key);

                // 是否已生成下游单据
                boolean isCreate = bill.getBoolean(ClaimFormConstant.ISCREATE_FIELD);
                if (isCreate) {
                    result.put("notHandle", result.containsKey("notHandle") ? result.get("notHandle") + 1 : 1);
                    continue;
                }

                // 判断是新增还是更新
                String billNo = bill.getString(ClaimFormConstant.BILLNO_FIELD);
                if (StringUtils.isNotBlank(billNo)) {
                    result.put("update", result.containsKey("update") ? result.get("update") + 1 : 1);
                } else {
                    result.put("add", result.containsKey("add") ? result.get("add") + 1 : 1);
                }

                // 字段赋值
                this.fieldAssignment(bill, entryDatas, key, fieldMap, baseMap, param);
                bills.add(bill);
            }
        }

        if (bills.size() > 0) {
            OperationResult opResutl = SaveServiceHelper.saveOperate(ClaimFormConstant.MAIN_ENTITY
                    , bills.toArray(new DynamicObject[bills.size()]), OperateOption.create());
            // 保存失败时，返回失败信息
            if (!opResutl.isSuccess()) {
                throw new KDBizException(opResutl.getMessage());
            }
        }

        return result;
    }

    /**
     * 构造过滤条件
     * @param key 键
     * @author: hst
     * @createDate: 2024/01/19
     */
    private DynamicObject getBillBySpecifiedDimension (String key) {
        // 构造过滤条件，查询该维度下是否已有单据
        List<QFilter> qFilters = new ArrayList<>();
        // 单据编号
        qFilters.add(new QFilter(ClaimFormConstant.BILLNO_FIELD,QFilter.equals,key));

        // 通过过滤条件获取单据，没有则新增单据
        DynamicObject bill = this.getBillByQFilters(ClaimFormConstant.MAIN_ENTITY
                , qFilters.toArray(new QFilter[qFilters.size()]));

        return bill;
    }

    /**
     * 通过过滤条件获取单据，没有则新增单据
     * @param entityName 单据标识
     * @param qFilters 过滤条件
     * @author: hst
     * @createDate: 2024/01/19
     */
    private DynamicObject getBillByQFilters (String entityName, QFilter[] qFilters) {
        DynamicObject bill = BusinessDataServiceHelper.loadSingle(ClaimFormConstant.MAIN_ENTITY,qFilters);
        // 判断是否需要新增
        if (Objects.isNull(bill)) {
            bill = BusinessDataServiceHelper.newDynamicObject(entityName);
        }
        return bill;
    }

    /**
     * 字段赋值
     * @param bill 需赋值单据
     * @param datas 待赋值数据
     * @param key
     * @param fieldMap 字段映射
     * @param baseMap 基础资料映射
     * @author: hst
     * @createDate: 2024/01/19
     */
    private void fieldAssignment (DynamicObject bill, List<Object> datas, String key, Map<String,String> fieldMap,
                                  Map<String,String> baseMap, Map<String,Object> param) {
        // 单据编码
        bill.set(ClaimFormConstant.BILLNO_FIELD,key);
        // 单据状态
        bill.set(ClaimFormConstant.STATUS_FIELD,"C");

        // 通过映射对表头字段进行赋值
        DataRow headData = (DataRow)datas.get(0);
        // 获取表头所有字段信息
        List<String> headFields = DynamicObjectUtil.getAllField(bill.getDynamicObjectType());
        for (Map.Entry<String,String> map : fieldMap.entrySet()) {
            if (StringUtils.isNotBlank(map.getKey())
                    && headFields.indexOf(map.getKey()) > -1) {
                Object value = headData.get(map.getValue());
                // 如果是基础资料
                if (baseMap.containsKey(map.getKey()) && Objects.nonNull(value)) {
                    bill.set(map.getKey(),
                            this.getBaseDataByNumber(baseMap.get(map.getKey()), value.toString(), "number"));
                } else {
                    bill.set(map.getKey(), headData.get(map.getValue()));
                }
            }
        }

        // 通过映射对分录字段进行赋值
        // 无论新增或者更新，先清空分录
        DynamicObjectCollection billEntries = bill.getDynamicObjectCollection(ClaimFormConstant.DETAIL_ENTRY);
        billEntries.clear();
        // 获取分录所有字段信息
        List<String> entryFields = DynamicObjectUtil.getAllField(billEntries.getDynamicObjectType());
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Object data : datas) {
            DynamicObject entry = billEntries.addNew();
            DataRow dataRow = (DataRow) data;
            for (Map.Entry<String,String> map : fieldMap.entrySet()) {
                Object value = dataRow.get(map.getValue());
                // 分录中是否包含该字段
                if (StringUtils.isNotBlank(map.getKey())
                        && entryFields.indexOf(map.getKey()) > -1) {
                    // 是否是基础资料
                    if (baseMap.containsKey(map.getKey()) && Objects.nonNull(value)) {
                        entry.set(map.getKey(), this.getBaseDataByNumber(baseMap.get(map.getKey()), value.toString(), "number"));
                    } else {
                        entry.set(map.getKey(), value);
                    }
                }
            }
            // 金额合计
            totalAmount = totalAmount.add(new BigDecimal(dataRow.getValue("famount").toString()));
        }

        bill.set(ClaimFormConstant.AMOUNT_FIELD,totalAmount);

        // 处理状态为空则赋值
        if (StringUtils.isBlank(bill.getString(ClaimFormConstant.BILLSTATUS_FIELD))) {
            DynamicObject customer = bill.getDynamicObject(ClaimFormConstant.CUSTOMER_FIELD);
            if (Objects.nonNull(customer)) {
                String customerId = customer.getString("id");
                if (param.containsKey("unpushs") && ((List<String>) param.get("unpushs")).contains(customerId)) {
                        bill.set(ClaimFormConstant.BILLSTATUS_FIELD, "C");
                } else {
                    bill.set(ClaimFormConstant.BILLSTATUS_FIELD, "A");
                }
            } else {
                bill.set(ClaimFormConstant.BILLSTATUS_FIELD, "A");
            }
        }
    }

    /**
     * 字段赋值
     * @param entityName 基础资料单据标识
     * @param number 编码
     * @author: hst
     * @createDate: 2024/01/19
     */
    private DynamicObject getBaseDataByNumber (String entityName, String number, String field) {
        // 如果是组织，则通过eas编码进行查找
        if ("bos_org".equals(entityName)) {
            return BusinessDataServiceHelper.loadSingleFromCache(entityName, QFilter.of("yd_eas_number = ?", number).toArray());
        } else {
            return BusinessDataServiceHelper.loadSingleFromCache(entityName, QFilter.of(field + " = ?", number).toArray());
        }
    }

    /**
     * 构建返回结果内容
     * @param opResult 执行结果
     * @return
     * @author: hst
     * @createDate: 2024/02/22
     */
    private ApiResult buildReturnResutl (Map<String,Integer> opResult) {
        ApiResult result = new ApiResult();
        // 无需处理数量
        int notHandleNum = opResult.containsKey("notHandle") ? opResult.get("notHandle") : 0;
        // 更新数量
        int updateNum = opResult.containsKey("update") ? opResult.get("update") : 0;
        // 新增数量
        int addNum = opResult.containsKey("add") ? opResult.get("add") : 0;

        // 拼接返回信息
        String msg = "本次共获取到" + (notHandleNum + updateNum + addNum) + "条EAS索赔单数据。"
                + (addNum > 0 || updateNum > 0 || notHandleNum > 0 ? "其中" : "")
                + (addNum > 0 ? "新增：" + addNum + "条。" : "") + (updateNum > 0 ? "更新：" + updateNum + "条。" : "")
                + (notHandleNum > 0 ? "已生成下游单据无需处理" + notHandleNum + "条。" : "");

        result.setSuccess(true);
        result.setMessage(msg);

        return result;
    }

    /**
     * 按公司+客户维度汇总索赔单，下推生成请款单
     * @author: hst
     * @createDate: 2024/02/22
     */
    public ApiResult summarizeToGenerateInvoice () {
        ApiResult result = new ApiResult();

        try {
            // 获取参数配置信息
            Map<String, Object> param = getConfigParam();

            // 获取待处理的索赔单
            DynamicObject[] claims = getPendingClaimForms();

            // 按组织 + 客户维度分组
            Map<String, List<DynamicObject>> groups = groupClaimsByOrgAndCustom(claims);

            // 下推生成预付款请款单
            pushPaymentRequest(groups, param);

            result.setSuccess(true);
            result.setData(claims.length);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    /**
     * 获取待处理的索赔单
     * @return
     * @author: hst
     * @createDate: 2024/02/27
     */
    private DynamicObject[] getPendingClaimForms () {
        // 处理状态为待处理
        QFilter qFilter = QFilter.of(ClaimFormConstant.BILLSTATUS_FIELD + " = ?", "A");
        // 是否已生成预付款请款单为否
        qFilter = qFilter.and(QFilter.of(ClaimFormConstant.ISCREATE_FIELD + " = ?", false));
        // 库存组织不为空
        qFilter = qFilter.and(QFilter.of(ClaimFormConstant.ORG_FIELD + " is not null"));
        // 客户不为空
        qFilter = qFilter.and(QFilter.of(ClaimFormConstant.CUSTOMER_FIELD + " is not null"));
        // 获取的字段信息
        String fields = ClaimFormConstant.getHeadField();

        DynamicObject[] claims = BusinessDataServiceHelper.load(ClaimFormConstant.MAIN_ENTITY,
                fields,qFilter.toArray());

        return claims;
    }

    /**
     * 根据库存组织 + 客户进行分组
     * @return
     * @author: hst
     * @createDate: 2024/02/27
     */
    private Map<String,List<DynamicObject>> groupClaimsByOrgAndCustom (DynamicObject[] claims) {
        // 按库存组织 + 客户进行分组
        Map<String,List<DynamicObject>> groups = Arrays.asList(claims).stream().collect(Collectors.groupingBy(
                claim -> claim.getString(ClaimFormConstant.ORG_FIELD + ".id")
                        + "&" + claim.getString(ClaimFormConstant.CUSTOMER_FIELD + ".id")));

        return groups;
    }

    /**
     * 下推生成请款单
     * @param groups 按组织+客户分组的索赔单
     * @param param 配置信息
     * @author: hst
     * @createDate: 2024/02/27
     */
    private void pushPaymentRequest (Map<String,List<DynamicObject>> groups, Map<String,Object> param) {
        // 支付方式
        DynamicObject payMode = BusinessDataServiceHelper.loadSingle(param.get("paymode"),"bd_settlementtype");
        // 项目干系人
        DynamicObject[] holder = BusinessDataServiceHelper.load("bos_user","id,number,name," +
                        "multilanguagetext,masterid", new QFilter("id",QFilter.in,param.get("holder")).toArray());

        // 分组下推
        for (Map.Entry<String,List<DynamicObject>> group : groups.entrySet()) {
            // 维度
            String keys = group.getKey();
            // 待处理的索赔单
            List<DynamicObject> claims = group.getValue();
            try {

                // update by hst 2024/05/10 判断该客户是否需要下推
//                String customerId = keys.split("&")[1];
//                if (param.containsKey("unpushs") && ((List<String>) param.get("unpushs")).contains(customerId)) {
//                    claims.stream().forEach(claim -> {claim.set(ClaimFormConstant.BILLSTATUS_FIELD,"C");claim.set(ClaimFormConstant.ERRMSG_FIELD,"");});
//                    SaveServiceHelper.save(claims.toArray(new DynamicObject[claims.size()]));
//                    continue;
//                }

                // 合计金额
                BigDecimal totalAmount = claims.stream().map(claim -> claim.getBigDecimal(ClaimFormConstant.AMOUNT_FIELD))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 当汇总金额大于0时，才下推生成请款单
                if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                    // 需要下推的源单ID
                    List<String> oriBillIds = claims.stream().map(claim -> claim.getString("id"))
                            .collect(Collectors.toList());

                    DynamicObject targetBill = pushBill(oriBillIds, ClaimFormConstant.MAIN_ENTITY, "er_dailyloanbill",
                            param.get("botpid").toString());

                    // 对下推的数据进行最后调整
                    // update by hst 2024/03/27 增加参数
                    adjustDataAfterPush(keys, targetBill, payMode, holder, param);

                    try (TXHandle h = TX.requiresNew("fi_claim_push")) {
                        try {
                            OperationServiceHelper.executeOperate("save","er_dailyloanbill",new DynamicObject[]{targetBill},OperateOption.create());
                            // 保存成功反写源单
                            for (DynamicObject claim :claims) {
                                claim.set(ClaimFormConstant.ISCREATE_FIELD,true);
                                claim.set(ClaimFormConstant.REQBILLNO_FIELD,targetBill.getString("billno"));
                                claim.set(ClaimFormConstant.BILLSTATUS_FIELD,"B");
                                claim.set(ClaimFormConstant.ERRMSG_FIELD,"");
                            }
                            SaveServiceHelper.save(claims.toArray(new DynamicObject[claims.size()]));
                        } catch (Exception e) {
                            h.markRollback();
                            throw e;
                        }
                    }
                }
            } catch (Exception e) {
                String errMsg = e.getMessage().length() > 255 ? e.getMessage().substring(0,255) : e.getMessage();
                for (DynamicObject claim :claims) {
                    claim.set(ClaimFormConstant.ERRMSG_FIELD,errMsg);
                }
                SaveServiceHelper.save(claims.toArray(new DynamicObject[claims.size()]));
            }
        }
    }

    /**
     * 下推单据
     *
     * @param oriBillIds 源单ID集合
     * @param oriBillEntity
     * @param desBillEntity
     * @param ruleId
     * @return
     */
    private DynamicObject pushBill(List<String> oriBillIds, String oriBillEntity, String desBillEntity, String ruleId) {
        // 将源单ID，源单标识，目标单标识，目标单名称，下推规则作为参数
        // 返回目标单ID
        // 构建下推参数
        PushArgs pushArgs = new PushArgs();
        pushArgs.setSourceEntityNumber(oriBillEntity);  // 源单标志
        pushArgs.setTargetEntityNumber(desBillEntity);  // 目标单标志
        pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告
        pushArgs.setRuleId(ruleId);  // 固定下推规则

        //需要下推的单据
        List<ListSelectedRow> selectedRows = new ArrayList<>();
        oriBillIds.stream().forEach(oriBillId -> {
            ListSelectedRow srcBill = new ListSelectedRow(oriBillId);
            selectedRows.add(srcBill);
        });
        pushArgs.setSelectedRows(selectedRows);

        //调用下推引擎，下推目标单
        ConvertOperationResult pushResult = ConvertServiceHelper.push(pushArgs);

        //获取生成的目标单数据包
        MainEntityType targetMainType = EntityMetadataCache.getDataEntityType(desBillEntity);
        List<DynamicObject> targetBillObjs = pushResult.loadTargetDataObjects(new IRefrencedataProvider() {
            @Override
            public void fillReferenceData(Object[] objs, IDataEntityType dType) {
                BusinessDataReader.loadRefence(objs, dType);
            }
        }, targetMainType);

        return targetBillObjs.get(0);
    }

    /**
     * 对下推的数据进行调整
     * @param keys
     * @param targetBill
     * @param payModel
     * @return
     * @author: hst
     * @createDate: 2024/02/29
     */
    private void adjustDataAfterPush (String keys, DynamicObject targetBill, DynamicObject payModel, DynamicObject[] holders, Map<String,Object> param) {
        // 项目干系人（二开）
        DynamicObjectCollection extColl = targetBill.getDynamicObjectCollection("yd_projectower");
        // 项目干系人（标准）
        DynamicObjectCollection coll = targetBill.getDynamicObjectCollection("projectower");
        for (DynamicObject holder : holders) {
            DynamicObject newExtObj = new DynamicObject(extColl.getDynamicObjectType());
            newExtObj.set("fbasedataId", holder);
            extColl.add(newExtObj);

            DynamicObject newObj = new DynamicObject(coll.getDynamicObjectType());
            newObj.set("fbasedataId", holder);
            coll.add(newObj);
        }
        targetBill.set("yd_projectower",extColl);
        targetBill.set("projectower",coll);

        // update by hst 2024/05/10 获取申请配置信息
        HashMap costParams = (HashMap) param.get("costdepts");
        String costOrgId = targetBill.getString("costcompany.id");
        JSONObject costParam = (JSONObject) costParams.get(costOrgId);

        // 获取人员信息
        JSONObject user = costParam.getJSONObject("yd_applier");
        if (Objects.nonNull(user)) {
            DynamicObject applier = BusinessDataServiceHelper.loadSingle(user.getString("id"), "bos_user");
            targetBill.set("applier", applier);
        } else {
            throw new KDBizException("获取不到申请人配置信息");
        }

        // 申请公司
        JSONObject applyOrg = costParam.getJSONObject("yd_applyorg");
        if (Objects.nonNull(applyOrg)) {
            DynamicObject org = BusinessDataServiceHelper.loadSingle(applyOrg.getString("id"), "bos_adminorg");
            targetBill.set("company",org);

        } else {
            throw new KDBizException("获取不到申请公司配置信息");
        }

        // 申请部门
        JSONObject applyDept = costParam.getJSONObject("yd_applydept");
        if (Objects.nonNull(applyDept)) {
            DynamicObject dept = BusinessDataServiceHelper.loadSingle(applyDept.getString("id"), "bos_adminorg");
            targetBill.set("org",dept);
        } else {
            throw new KDBizException("获取不到申请部门信息");
        }

        // 获取申请人职位
        String position = costParam.getString("yd_position");
        if (StringUtils.isNotBlank(position)) {
            targetBill.set("applierpositionstr",position);
        } else {
            throw new KDBizException("获取不到申请人职位信息");
        }

        // update by hst 2024/03/27 获取费用承担公司下费用承担部门
        DynamicObject costDept = null;
        if (Objects.nonNull(costParam.getJSONObject("yd_costdepart"))) {
            costDept = BusinessDataServiceHelper.loadSingle(costParam.getJSONObject("yd_costdepart").getString("id"),"bos_org");
        }
        if (Objects.isNull(costDept)) {
            throw new KDBizException("获取不到费用承担公司下物流部门");
        }
        // 表头->费用承担部门
        targetBill.set("costdept",costDept);
        // 借款明细->费用承担部门、预算部门赋值
        DynamicObject expenseEntry = targetBill.getDynamicObjectCollection("expenseentryentity").get(0);
        expenseEntry.set("entrycostdept",costDept);
        expenseEntry.set("yd_budgetdepartment",costDept);

        // 调整收款信息
        // 获取客户信息
        DynamicObject customer = BusinessDataServiceHelper.loadSingle(keys.split("&")[1],"bd_customer");
        DynamicObjectCollection accountEntry = targetBill.getDynamicObjectCollection("accountentry");
        accountEntry.clear();
        DynamicObject accountInfo = accountEntry.addNew();
        accountInfo.set("payertype","other");
        accountInfo.set("payername",customer.getString("name"));
        accountInfo.set("orireceiveamount",targetBill.getBigDecimal("payamount"));
        accountInfo.set("receiveamount",targetBill.getBigDecimal("payamount"));
        accountInfo.set("yd_receiveamount",targetBill.getBigDecimal("payamount"));
        accountInfo.set("paymode",payModel);
        accountInfo.set("accountcurrency",expenseEntry.get("entrycurrency"));
        accountInfo.set("accexchangerate",expenseEntry.get("exchangerate"));
        accountInfo.set("yd_accexchangerate",expenseEntry.get("yd_exchangerate"));

        // 单据状态为已付款
        targetBill.set("billstatus","G");
    }

    /**
     * 按公司+客户维度汇总索赔单，下推生成请款单（根据填写的信息）
     * @author: hst
     * @createDate: 2024/03/12
     */
    public ApiResult summarizeToGenerateInvoiceByInfo (QFilter qFilter) {
        ApiResult result = new ApiResult();

        try {
            // 获取参数配置信息
            Map<String, Object> param = getConfigParam();

            // 获取待处理的索赔单
            DynamicObject[] claims = getPendingClaimFormsByQFilter(qFilter);

            // 按组织 + 客户维度分组
            Map<String, List<DynamicObject>> groups = groupClaimsByOrgAndCustom(claims);

            // 下推生成预付款请款单
            pushPaymentRequest(groups, param);

            result.setSuccess(true);
            result.setData(claims.length);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    /**
     * 获取待处理的索赔单
     * @return
     * @author: hst
     * @createDate: 2024/02/27
     */
    private DynamicObject[] getPendingClaimFormsByQFilter (QFilter qFilter) {
        // 处理状态为待处理
        qFilter = qFilter.and(QFilter.of(ClaimFormConstant.BILLSTATUS_FIELD + " = ?", "A"));
        // 是否已生成预付款请款单为否
        qFilter = qFilter.and(QFilter.of(ClaimFormConstant.ISCREATE_FIELD + " = ?", false));
        // 库存组织不为空
        qFilter = qFilter.and(QFilter.of(ClaimFormConstant.ORG_FIELD + " is not null"));
        // 客户不为空
        qFilter = qFilter.and(QFilter.of(ClaimFormConstant.CUSTOMER_FIELD + " is not null"));
        // 获取的字段信息
        String fields = ClaimFormConstant.getHeadField();

        DynamicObject[] claims = BusinessDataServiceHelper.load(ClaimFormConstant.MAIN_ENTITY,
                fields,qFilter.toArray());

        return claims;
    }
}
