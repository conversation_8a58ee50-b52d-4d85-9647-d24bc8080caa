package kd.bos.tcbj.fi.em.op;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.fi.em.constants.ClaimFormConstant;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.fi.em.op.DailyLoanBillDeleteOp
 * @className DailyLoanBillDeleteOp
 * @author: hst
 * @createDate: 2024/03/01
 * @description: 预付请款单删除插件
 * @version: v1.0
 */
public class DailyLoanBillDeleteOp extends AbstractOperationServicePlugIn {

    private static final String DELETE_OP = "delete";

    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        super.endOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case DELETE_OP : {
                this.reverseWriteClaimForm(bills);
                break;
            }
        }
    }

    /**
     * 反写索赔单中间表
     * @param bills 单据
     * @author: hst
     * @createDate: 2024/03/01
     */
    private void reverseWriteClaimForm (DynamicObject[] bills) {
        List<String> billNos = Arrays.asList(bills).stream().map(bill -> bill.getString("billno"))
                .collect(Collectors.toList());
        QFilter qFilter = new QFilter(ClaimFormConstant.REQBILLNO_FIELD,QFilter.in,billNos);

        DynamicObject[] claims = BusinessDataServiceHelper.load(ClaimFormConstant.MAIN_ENTITY,
                ClaimFormConstant.getHeadField(),qFilter.toArray());

        for (DynamicObject claim : claims) {
            claim.set(ClaimFormConstant.REQBILLNO_FIELD,"");
            claim.set(ClaimFormConstant.ISCREATE_FIELD,false);
            claim.set(ClaimFormConstant.BILLSTATUS_FIELD,"A");
        }

        SaveServiceHelper.save(claims);
    }
}
