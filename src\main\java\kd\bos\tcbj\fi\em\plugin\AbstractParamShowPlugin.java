package kd.bos.tcbj.fi.em.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.form.IFormView;
import kd.bos.form.IPageCache;
import kd.bos.form.plugin.AbstractFormPlugin;

/**
 * @package: kd.bos.tcbj.fi.em.plugin.AbstractParamShowPlugin
 * @className AbstractParamShowPlugin
 * @author: hst
 * @createDate: 2024/02/23
 * @description: 费用核算参数配置公共插件类
 * @version: v1.0
 */
public class AbstractParamShowPlugin extends AbstractFormPlugin {

    /**
     * 值变更事件
     * @param e
     * @author: hst
     * @createDate: 2024/02/23
     */
    @Override
    public void propertyChanged(PropertyChangedArgs e) {
        super.propertyChanged(e);
        this.getPageCache().put("dataChange", "true");
    }

    /**
     * 查询参数设置
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    protected String getParamSettings() {
        String nodeId = this.getCustomParam("nodeid");
        String paramJson = "";
        String sql = "select fk_yd_params_tag from tk_yd_em_param_setting where fk_yd_nodeid = ?";
        DataSet ds = DB.queryDataSet(this.getClass().toString(), DBRoute.of("em"), sql, new Object[]{nodeId});
        Throwable throwable = null;

        try {
            while(ds != null && ds.hasNext()) {
                Row row = ds.next();
                paramJson = row.getString("fk_yd_params_tag");
            }
        } catch (Throwable th) {
            throwable = th;
            throw th;
        } finally {
            if (ds != null) {
                if (throwable != null) {
                    try {
                        ds.close();
                    } catch (Throwable th) {
                        throwable.addSuppressed(th);
                    }
                } else {
                    ds.close();
                }
            }

        }

        return paramJson;
    }

    /**
     * 获取父页面传递参数
     * @param key
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    private String getCustomParam(String key) {
        String nodeId = "";
        IFormView parentView = this.getView().getParentView();
        if (parentView == null) {
            return nodeId;
        } else {
            IPageCache pageCache = parentView.getPageCache();
            IFormView parameterView = parentView.getViewNoPlugin(pageCache.get("settingPageCache"));
            if (parameterView != null) {
                nodeId = parameterView.getFormShowParameter().getCustomParam(key);
            }

            return nodeId;
        }
    }
}
