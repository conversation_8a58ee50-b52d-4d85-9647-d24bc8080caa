package kd.bos.tcbj.fi.em.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.serialization.SerializationUtils;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.form.field.BasedataEdit;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.epm.eb.common.utils.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.fi.em.plugin.ClaimFormParamShowPlugin
 * @className ClaimFormParamShowPlugin
 * @author: hst
 * @createDate: 2024/02/23
 * @description: 索赔单中间表参数配置数据加载插件
 * @version: v1.0
 */
public class ClaimFormParamShowPlugin extends AbstractParamShowPlugin {

    /**
     * 事件注册
     * @param e
     * @author: hst
     * @createDate: 2024/03/25
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.addBeforeF7SelectListener();
    }

    /**
     * 新建数据包完毕后，触发此事件
     * @param e
     * @author: hst
     * @createDate: 2024/02/22
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        this.setDefaultValue();
    }

    /**
     * 基础资料选择过滤条件
     * @author: hst
     * @createDate: 2024/03/25
     */
    private void addBeforeF7SelectListener () {
        // 费用承担公司
        BasedataEdit costOrgEdit = this.getView().getControl("yd_costorg");
        // 弹窗前过滤
        costOrgEdit.addBeforeF7SelectListener((evt) -> {
            List<String> orgIds = this.getModel().getDataEntity().getDynamicObjectCollection("yd_org")
                    .stream().map(org -> org.getDynamicObject("fbasedataid").getString("id"))
                    .collect(Collectors.toList());
            evt.getCustomQFilters().add(new QFilter("id",QFilter.in,orgIds));
        });

        // 费用承担部门
        BasedataEdit costDeptEdit = this.getView().getControl("yd_costdepart");
        // 弹窗前过滤
        costDeptEdit.addBeforeF7SelectListener((evt) -> {
            // 通过选中行的费用承担公司过滤费用承担部门
            int index = this.getModel().getEntryCurrentRowIndex("yd_entryentity");
            DynamicObject entry = this.getModel().getEntryRowEntity("yd_entryentity",index);
            DynamicObject costOrg = entry.getDynamicObject("yd_costorg");
            if (Objects.nonNull(costOrg)) {
                evt.getFormShowParameter().setCustomParam("rootId",costOrg.getPkValue());
            }
        });

        // update by hst 2024/05/10 不下推请款单的客户
        BasedataEdit unPushCusEdit = this.getView().getControl("yd_unpushcus");
        // 弹窗前过滤
        unPushCusEdit.addBeforeF7SelectListener((evt) -> {
            // 获取涉及客户
            DynamicObjectCollection customers = this.getModel().getDataEntity(true)
                    .getDynamicObjectCollection("yd_custom");
            List<String> cusIds = customers.stream().map(customer
                    -> customer.getDynamicObject("fbasedataid").getString("id")).collect(Collectors.toList());

            if (cusIds.size() > 0) {
                evt.getCustomQFilters().add(new QFilter("id",QFilter.in,cusIds));
            } else {
                evt.getCustomQFilters().add(QFilter.of("1 != 1"));
            }
        });

        // update by hst 2024/05/10 申请人
        BasedataEdit applierEdit = this.getView().getControl("yd_applier");
        // 弹窗前过滤
        applierEdit.addBeforeF7SelectListener((evt) -> {
            // 通过选中行的申请部门过滤申请人
            int index = this.getModel().getEntryCurrentRowIndex("yd_entryentity");
            DynamicObject entry = this.getModel().getEntryRowEntity("yd_entryentity",index);
            DynamicObject applyDept = entry.getDynamicObject("yd_applyorg");
            if (Objects.nonNull(applyDept)) {
                evt.getFormShowParameter().setCustomParam("initRootOrgId",applyDept.getPkValue());
            }
        });
        applierEdit.addAfterF7SelectListener((evt) -> {
            int index = evt.getCurrentRowIndex();
            ListSelectedRow row = evt.getListSelectedRow();
            Object userId = row.getPrimaryKeyValue();
            Object entryId = row.getEntryPrimaryKeyValue();

            if (Objects.nonNull(userId) && Objects.nonNull(entryId)) {
                // 查询用户信息
                DynamicObject user = BusinessDataServiceHelper.loadSingle(userId, "bos_user");
                if (Objects.nonNull(user)) {
                    DynamicObjectCollection deptEntries = user.getDynamicObjectCollection("entryentity");
                    for (DynamicObject deptEntry : deptEntries) {
                        if (entryId.equals(deptEntry.getPkValue())) {
                            this.getModel().setValue("yd_applydept",deptEntry.getDynamicObject("dpt"),index);
                            this.getModel().setValue("yd_position",deptEntry.getString("position"),index);
                            return;
                        }
                    }
                }
            }
        });
    }

    /**
     * 加载默认数据
     * @author: hst
     * @createDate: 2024/02/23
     */
    private void setDefaultValue() {
        String paramJson = super.getParamSettings();
        if (StringUtils.isNotEmpty(paramJson)) {
            Map paramMap = SerializationUtils.fromJsonString(paramJson, Map.class);
            this.getModel().setValue("yd_isauto", paramMap.containsKey("yd_isauto")
                    ? paramMap.get("yd_isauto") : "");
            this.getModel().setValue("yd_botpid", paramMap.containsKey("yd_botpid")
                    ? paramMap.get("yd_botpid") : "");
            this.getModel().setValue("yd_fieldmap_tag", paramMap.containsKey("yd_fieldmap_tag")
                    ? paramMap.get("yd_fieldmap_tag") : "");
            this.getModel().setValue("yd_basemap_tag", paramMap.containsKey("yd_basemap_tag")
                    ? paramMap.get("yd_basemap_tag") : "");
            if (paramMap.containsKey("yd_org")) {
                this.setMultipleBaseData("yd_org", (ArrayList) paramMap.get("yd_org"),"bos_org");
            }
            if (paramMap.containsKey("yd_custom")) {
                this.setMultipleBaseData("yd_custom", (ArrayList) paramMap.get("yd_custom"),"bd_customer");
            }
            if (paramMap.containsKey("yd_holder")) {
                this.setMultipleBaseData("yd_holder", (ArrayList) paramMap.get("yd_holder"),"bos_user");
            }
            if (paramMap.containsKey("yd_paymode")) {
                this.getModel().setValue("yd_paymode", ((Map) paramMap.get("yd_paymode")).get("id"));
            }
            // update by hst 2024/03/27 增加费用承担部门分录
            if (paramMap.containsKey("yd_entryentity")) {
                List<LinkedHashMap> deptMap = (List<LinkedHashMap>) paramMap.get("yd_entryentity");
                for (LinkedHashMap dept : deptMap) {
                    int index = this.getModel().createNewEntryRow("yd_entryentity");
                    this.getModel().setValue("yd_costorg",Objects.nonNull((dept.get("yd_costorg")))
                            ? ((LinkedHashMap) dept.get("yd_costorg")).get("id") : null,index);
                    this.getModel().setValue("yd_costdepart",Objects.nonNull((dept.get("yd_costdepart")))
                            ? ((LinkedHashMap) dept.get("yd_costdepart")).get("id") : null,index);
                    // update by hst 2024/05/10 增加申请组织、申请部门、申请人配置
                    this.getModel().setValue("yd_applyorg",Objects.nonNull((dept.get("yd_applyorg")))
                            ? ((LinkedHashMap) dept.get("yd_applyorg")).get("id") : null,index);
                    this.getModel().setValue("yd_applier",Objects.nonNull((dept.get("yd_applier")))
                            ? ((LinkedHashMap) dept.get("yd_applier")).get("id") : null,index);
                    this.getModel().setValue("yd_applydept",Objects.nonNull((dept.get("yd_applydept")))
                            ? ((LinkedHashMap) dept.get("yd_applydept")).get("id") : null,index);
                    this.getModel().setValue("yd_position",dept.get("yd_position"),index);
                }
            }
            // update by hst 2025/05/10 增加不需要下推的客户
            if (paramMap.containsKey("yd_unpushcus")) {
                this.setMultipleBaseData("yd_unpushcus", (ArrayList) paramMap.get("yd_unpushcus"),"bd_customer");
            }
        }
    }

    /**
     * 设置多选基础资料
     * @param field 字段名
     * @param datas
     */
    private void setMultipleBaseData (String field, ArrayList datas,String entityName) {
        if (StringUtils.isNotEmpty(entityName)) {
            Set<String> ids = this.getMultipleBaseDataInfo(datas);
            DynamicObject[] baseDatas = BusinessDataServiceHelper.load(entityName,"id,number,name",
                    new QFilter("id",QFilter.in,ids).toArray());
            DynamicObjectCollection collection = new DynamicObjectCollection();
            for (DynamicObject baseData : baseDatas) {
                collection.add(baseData);
            }
            if (collection.size() > 0) {
                this.getModel().setValue(field, collection);
            }
        }
    }

    /**
     * 获取已选的多选基础资料信息
     * @param datas
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    private Set<String> getMultipleBaseDataInfo (ArrayList datas) {
        Set<String> ids = new HashSet<>();
        for (Object data :datas) {
            ids.add(((LinkedHashMap) (((LinkedHashMap) data).get("fbasedataid"))).get("id").toString());
        }
        return ids;
    }
}
