package kd.bos.tcbj.fi.em.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.fi.em.constants.ClaimFormConstant;
import kd.bos.tcbj.fi.em.helper.ClaimGatherHelper;
import kd.bos.tcbj.im.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.fi.em.plugin.ClaimGatherListPlugin
 * @className ClaimGatherListPlugin
 * @author: hst
 * @createDate: 2024/01/11
 * @description: EAS索赔单汇总中间表列表插件
 * @version: v1.0
 */
public class ClaimGatherListPlugin extends AbstractListPlugin {

    /** 汇总EAS索赔单按钮标识 **/
    private final static String GATHER_BTN = "yd_gather";
    /** 下推请款单按钮标识 **/
    private final static String PUSH_BTN = "yd_push";
    /** 下推请款单信息填写按钮标识 **/
    private final static String PUSHINFO_BTN = "yd_pushinfo";
    /** 索赔单获取填写页面标识 **/
    private final static String GETCLAIMINFO_FORM = "yd_getclaiminfo";
    /** 索赔单汇总维度填写页面标识 **/
    private final static String GATHERINFO_FORM = "yd_gatherinfo";
    /** 索赔单获取维度填写回调标识 **/
    private final static String GET_CALLBACK = "getclaiminfo";
    /** 索赔单汇总维度填写回调标识 **/
    private final static String GATHER_CALLBACK = "pushinfo";

    /**
     * 按钮点击事件，根据标识执行相应事件
     * @param evt
     * @author: hst
     * @createDate: 2024/01/11
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case GATHER_BTN : {
                // 打开索赔单汇总维度填写页面
                this.openGatherInfoFillPage();
                break;
            }
            case PUSH_BTN : {
                // 按公司+客户维度汇总索赔单，下推生成请款单
                this.summarizeToGenerateInvoice();
                break;
            }
            case PUSHINFO_BTN : {
                // 打开索赔单汇总维度填写页面
                this.openPushInfoFillPage();
                break;
            }
        }
    }

    /**
     * 子页面关闭回调事件
     * @param closedCallBackEvent
     * @author: hst
     * @createDate: 2024/01/11
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        String actionId = closedCallBackEvent.getActionId();
        Object returndata = closedCallBackEvent.getReturnData();
        if (Objects.nonNull(returndata)) {
            switch (actionId) {
                case GET_CALLBACK : {
                    // 获取EAS索赔单
                    this.getEASClaimBills(((HashMap) returndata));
                    break;
                }
                case GATHER_CALLBACK : {
                    // update by hst 2024/03/12 按维度汇总EAS索赔单下推生成预付款请款单
                    this.summarizeToGenerateInvoiceByInfo(((HashMap) returndata));
                    break;
                }
            }
        }
    }

    /**
     * 打开索赔单汇总维度填写页面
     * @author: hst
     * @createDate: 2024/01/11
     */
    private void openGatherInfoFillPage () {
        FormShowParameter showParameter = new FormShowParameter();
        showParameter.setFormId(GETCLAIMINFO_FORM);
        showParameter.setCloseCallBack(new CloseCallBack(this, GET_CALLBACK));
        showParameter.getOpenStyle().setShowType(ShowType.Modal);
        this.getView().showForm(showParameter);
    }

    /**
     * 获取EAS索赔单
     * @author: hst
     * @createDate: 2024/02/22
     */
    private void getEASClaimBills (HashMap returndata) {
        // 构建服务流程参数
        Map<String,Object> inputs = new HashMap<>();
        String type = returndata.get("yd_type").toString();
        Boolean isAuto = false;
        QFilter qFilter = null;
        if ("1".equals(type)) {
            // 月份
            Date date = (Date) returndata.get("yd_month");
            String month = new SimpleDateFormat("yyyyMM").format(date);
            inputs.put("month", month);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
            qFilter = new QFilter(ClaimFormConstant.BIZDATE_FIELD,QFilter.less_than,calendar.getTime());

            // 库存组织
            if (returndata.containsKey("yd_org")) {
                inputs.put("orgs", "('" + ((DynamicObject) returndata.get("yd_org")).getString("yd_eas_number") + "')");
                qFilter = qFilter.and(new QFilter(ClaimFormConstant.ORGNUM_FIELD,QFilter.equals,
                        ((DynamicObject) returndata.get("yd_org")).getString("yd_eas_number")));
            }
            // 客户
            if (returndata.containsKey("yd_customer")) {
                inputs.put("customers", "('" + ((DynamicObject) returndata.get("yd_customer")).getString("number") + "')");
                qFilter = qFilter.and(new QFilter(ClaimFormConstant.CUSNUM_FIELD,QFilter.equals,
                        ((DynamicObject) returndata.get("yd_customer")).getString("number")));
            }
            // 出库单号
            inputs.put("billno", "");
            // 是否自动下推请款单
            isAuto = (Boolean) returndata.get("yd_isauto");
        } else {
            String billNo = returndata.get("yd_billno").toString();
            if (StringUtils.isNotBlank(billNo)) {
                if (billNo.contains("\n")) {
                    billNo = "('" + String.join("','",Arrays.asList(returndata.get("yd_billno").toString().split("\n"))
                            .stream().filter(bill -> !"".equals(bill)).collect(Collectors.toList())) + "')";
                } else {
                    billNo = "('" + billNo + "')";
                }
                inputs.put("month", "");
                inputs.put("orgs", "");
                inputs.put("customers", "");
                inputs.put("billno", billNo);
            }
        }

        // 获取EAS索赔单
        ApiResult result = new ClaimGatherHelper().getEASClaimBills(inputs, isAuto, qFilter);

        // 执行结果反馈
        if (result.getSuccess()) {
            this.getView().showMessage(result.getMessage());
        } else {
            this.getView().showErrorNotification(result.getMessage());
        }
        this.getView().invokeOperation("refresh");
    }

    /**
     * 按公司+客户维度汇总索赔单，下推生成请款单
     * @author: hst
     * @createDate: 2024/03/01
     */
    private void summarizeToGenerateInvoice() {
        ApiResult result = new ClaimGatherHelper().summarizeToGenerateInvoice();
        // 执行结果反馈
        if (result.getSuccess()) {
            this.getView().showMessage("操作成功。本次共执行" + result.getData() + "条数据。");
        } else {
            this.getView().showErrorNotification(result.getMessage());
        }
        this.getView().invokeOperation("refresh");
    }

    /**
     * 打开索赔单汇总维度填写页面
     * @author: hst
     * @createDate: 2024/03/12
     */
    private void openPushInfoFillPage () {
        FormShowParameter showParameter = new FormShowParameter();
        showParameter.setFormId(GATHERINFO_FORM);
        showParameter.setCaption("汇总信息填写");
        showParameter.setCloseCallBack(new CloseCallBack(this, GATHER_CALLBACK));
        showParameter.getOpenStyle().setShowType(ShowType.Modal);
        this.getView().showForm(showParameter);
    }

    /**
     * 按公司+客户维度汇总索赔单，下推生成请款单
     * @author: hst
     * @createDate: 2024/03/12
     */
    private void summarizeToGenerateInvoiceByInfo(HashMap returndata) {
        // 业务日期
        Date date = (Date) returndata.get("yd_month");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
        QFilter qFilter = new QFilter(ClaimFormConstant.BIZDATE_FIELD,QFilter.less_than,calendar.getTime());
        // 库存组织
        if (returndata.containsKey("yd_org")) {
            qFilter = qFilter.and(new QFilter(ClaimFormConstant.ORGNUM_FIELD,QFilter.equals,
                    ((DynamicObject) returndata.get("yd_org")).getString("yd_eas_number")));
        }
        // 客户
        if (returndata.containsKey("yd_customer")) {
            qFilter = qFilter.and(new QFilter(ClaimFormConstant.CUSNUM_FIELD,QFilter.equals,
                    ((DynamicObject) returndata.get("yd_customer")).getString("number")));
        }

        ApiResult result = new ClaimGatherHelper().summarizeToGenerateInvoiceByInfo(qFilter);
        // 执行结果反馈
        if (result.getSuccess()) {
            this.getView().showMessage("操作成功。本次共执行" + result.getData() + "条数据。");
        } else {
            this.getView().showErrorNotification(result.getMessage());
        }
        this.getView().invokeOperation("refresh");
    }
}
