package kd.bos.tcbj.fi.em.plugin;

import kd.bos.algo.DataSet;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.dataentity.serialization.SerializationUtils;
import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.property.BasedataProp;
import kd.bos.entity.property.BooleanProp;
import kd.bos.entity.property.FieldProp;
import kd.bos.entity.property.MulBasedataProp;
import kd.bos.entity.tree.TreeNode;
import kd.bos.form.*;
import kd.bos.form.control.TreeView;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.control.events.TreeNodeClickListener;
import kd.bos.form.control.events.TreeNodeEvent;
import kd.bos.form.events.BeforeClosedEvent;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.servicehelper.DBServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.epm.eb.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * @package: kd.bos.tcbj.fi.em.plugin.EmParamSettingPlugin
 * @className EmParamSettingPlugin
 * @author: hst
 * @createDate: 2024/02/23
 * @description: 费用核算参数配置动态表单插件
 * @version: v1.0
 */
public class EmParamSettingPlugin extends AbstractFormPlugin implements TreeNodeClickListener {

    /**
     * 控件注册
     * @param e
     * @author: hst
     * @createDate: 2024/02/23
     */
    public void registerListener(EventObject e) {
        super.registerListener(e);
        TreeView tree = this.getControl("yd_treeviewap");
        tree.addTreeNodeClickListener(this);
        this.addItemClickListeners(new String[]{"yd_toolbarap"});
    }

    /**
     * 加载树形控件数据
     * @param e
     * @author: hst
     * @createDate: 2024/02/23
     */
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        TreeNode root = this.buildRootNode();
        IPageCache pageCache = this.getPageCache();
        pageCache.put("treenodecache", SerializationUtils.toJsonString(root));
        TreeView tree = this.getView().getControl("yd_treeviewap");
        tree.addNode(root);
        this.getView().setEnable(false, new String[]{"yd_save", "yd_refresh"});
    }

    /**
     * 按钮点击事件
     * @param evt
     * @author: hst
     * @createDate: 2024/02/23
     */
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        switch (evt.getItemKey()) {
            case "yd_save":
                this.saveData();
            default:
        }
    }

    /**
     * 页面关闭前触发，校验是否有修改
     * @param e
     * @author: hst
     * @createDate: 2024/02/24
     */
    public void beforeClosed(BeforeClosedEvent e) {
        super.beforeClosed(e);
        if (!Boolean.parseBoolean(this.getPageCache().get("isClosed"))) {
            IPageCache pageCache = this.getPageCache();
            IFormView parameterView = this.getView().getViewNoPlugin(pageCache.get("settingPageCache"));
            if (parameterView != null && Boolean.parseBoolean(parameterView.getPageCache().get("dataChange"))) {
                this.getView().showConfirm("检测到您有更改内容，是否保存修改？若不保存，将丢失这些更改？",
                        MessageBoxOptions.YesNoCancel, new ConfirmCallBackListener("dataChange_comfirm", this));
                e.setCancel(true);
            }

        }
    }

    /**
     * 确认框回调事件
     * @param messageBoxClosedEvent
     * @author: hst
     * @createDate: 2024/02/23
     */
    public void confirmCallBack(MessageBoxClosedEvent messageBoxClosedEvent) {
        super.confirmCallBack(messageBoxClosedEvent);
        String confirmId = messageBoxClosedEvent.getCallBackId();
        if ("dataChange_comfirm".equals(confirmId)) {
            if (messageBoxClosedEvent.getResult().equals(MessageBoxResult.No)) {
                this.getPageCache().put("isClosed", "true");
                this.getView().close();
            } else if (messageBoxClosedEvent.getResult().equals(MessageBoxResult.Yes)) {
                this.saveData();
                this.getPageCache().put("isClosed", "true");
                this.getView().close();
            }
        } else if ("treeClick_confirm".equals(confirmId)) {
            String currentNodeId = this.getPageCache().get("currentNodeId");
            if (messageBoxClosedEvent.getResult().equals(MessageBoxResult.Yes)) {
                this.saveData();
                this.doTreeNodeClick(currentNodeId);
            } else if (messageBoxClosedEvent.getResult().equals(MessageBoxResult.No)) {
                this.doTreeNodeClick(currentNodeId);
            } else {
                String lastNodeId = this.getPageCache().get("focusedNodeId");
                TreeNode leftRoot = SerializationUtils.fromJsonString(this.getPageCache().get("treenodecache"), TreeNode.class);
                TreeView tree = this.getControl("treeviewap");
                TreeNode treeNode = leftRoot.getTreeNode(lastNodeId);
                tree.focusNode(treeNode);
            }
        }

    }

    /**
     * 树型控件点击事件
     * @param evt
     * @author: hst
     * @createDate: 2024/02/23
     */
    public void treeNodeClick(TreeNodeEvent evt) {
        String nodeId = (String)evt.getNodeId();
        String lastNodeId = this.getPageCache().get("focusedNodeId");
        if (!StringUtils.equals(nodeId, lastNodeId)) {
            IPageCache pageCache = this.getPageCache();
            IFormView parameterView = this.getView().getViewNoPlugin(pageCache.get("settingPageCache"));
            if (parameterView != null && Boolean.parseBoolean(parameterView.getPageCache().get("dataChange"))) {
                this.getView().showConfirm(ResManager.loadKDString("检测到您有更改内容，是否保存修改？若不保存，将丢失这些更改？", "EbParamSettingPlugin_31", "epm-eb-formplugin", new Object[0]), MessageBoxOptions.YesNoCancel, new ConfirmCallBackListener("treeClick_confirm", this));
                this.getPageCache().put("currentNodeId", nodeId);
                evt.setCancel(true);
            } else {
                this.doTreeNodeClick(nodeId);
                TreeNode root = SerializationUtils.fromJsonString(this.getPageCache().get("treenodecache"), TreeNode.class);
                TreeNode checkedNode = root.getTreeNode(nodeId, 10);
                List<TreeNode> children = checkedNode.getChildren();
                if (CollectionUtils.isNotEmpty(children)) {
                    this.getView().setEnable(false, new String[]{"btn_save", "btn_refresh"});
                }

            }
        }
    }

    /**
     * 获取树形控件数据
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    private TreeNode buildRootNode() {
        TreeNode treeNode = new TreeNode();
        treeNode.setParentid("");
        List<Map<String, String>> treeList = new ArrayList();
        String headId = "";
        DynamicObjectCollection categoryCollection = QueryServiceHelper.query("yd_param_tree",
                "id,yd_seq,yd_code,yd_name,yd_formid,yd_parentid",null);

        for (DynamicObject category : categoryCollection) {
            Map<String, String> memberMap = new HashMap();
            memberMap.put("id", category.getString("yd_seq"));
            memberMap.put("code", category.getString("yd_code"));
            memberMap.put("name", category.getString("yd_name"));
            memberMap.put("formid", category.getString("yd_formid"));
            memberMap.put("parentid", category.getString("yd_parentid"));
            treeList.add(memberMap);
            if ("0".equals(category.getString("yd_parentid"))) {
                headId = category.getString("yd_seq");
                treeNode.setId(headId);
                treeNode.setText(category.getString("yd_name"));
                treeNode.setIsOpened(true);
            }
        }

        this.setEntryNode(treeNode, treeList, headId);
        return treeNode;
    }

    /**
     *
     * @param root
     * @param rootNodeList
     * @param headId
     * @return
     */
    private TreeNode setEntryNode(TreeNode root, List<Map<String, String>> rootNodeList, String headId) {
        Map<String, List<Map<String, String>>> nodeMap = new HashMap();

        Map node;
        List<Map<String, String>> nodeList;
        for(Iterator rootNode = rootNodeList.iterator(); rootNode.hasNext(); nodeList.add(node)) {
            node = (Map)rootNode.next();
            String parentId = (String)node.get("parentid");
            nodeList = nodeMap.get(parentId);
            if (nodeList == null) {
                nodeList = new ArrayList();
                nodeMap.put(parentId, nodeList);
            }
        }

        return this.createNode(root, nodeMap, headId);
    }

    /**
     * 创建节点
     * @param root
     * @param nodeMap
     * @param headId
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    private TreeNode createNode(TreeNode root, Map<String, List<Map<String, String>>> nodeMap, String headId) {
        List<Map<String, String>> list = nodeMap.get(headId);
        if (list != null) {
            List<TreeNode> children = root.getChildren();
            if (children == null) {
                children = new ArrayList(list.size());
                root.setChildren(children);
            }

            for(Map<String, String> map : list) {
                TreeNode entry = new TreeNode();
                entry.setParentid(headId);
                entry.setId(map.get("id"));
                entry.setText(map.get("name"));
                entry.setData(map.get("formid"));
                String entryHeadId = map.get("id");
                this.createNode(entry, nodeMap, entryHeadId);
                children.add(entry);
            }
        }

        return root;
    }

    /**
     * 树型控件点击后执行
     * @param nodeId
     * @author: hst
     * @createDate: 2024/02/22
     */
    private void doTreeNodeClick(String nodeId) {
        this.closeParameterForm();
        TreeNode root = SerializationUtils.fromJsonString(this.getPageCache().get("treenodecache"), TreeNode.class);
        TreeNode checkedNode = root.getTreeNode(nodeId, 10);
        TreeView tree = this.getControl("yd_treeviewap");
        tree.focusNode(checkedNode);
        if (checkedNode == null) {
            this.getView().setEnable(false, new String[]{"yd_save", "yd_refresh"});
        } else {
            this.getPageCache().put("focusedNodeId", nodeId);
            String formId = (String)checkedNode.getData();
            if (!StringUtils.isEmpty(formId)) {
                FormShowParameter showParameter = this.getFormShowParameter(nodeId, formId);
                this.getView().showForm(showParameter);
                this.getView().setEnable(true, new String[]{"yd_save", "yd_refresh"});
                this.getPageCache().put("settingPageCache", showParameter.getPageId());
            }
        }
    }

    /**
     * 获取页面参数
     * @param nodeId
     * @param formId
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    private FormShowParameter getFormShowParameter(String nodeId, String formId) {
        FormShowParameter showParameter = new FormShowParameter();
        showParameter.getOpenStyle().setShowType(ShowType.InContainer);
        showParameter.getOpenStyle().setTargetKey("yd_settingspanel");
        showParameter.setFormId(formId);
        showParameter.setCustomParam("nodeid", nodeId);
        return showParameter;
    }

    /**
     * 关闭页面参数
     * @author: hst
     * @createDate: 2024/02/23
     */
    private void closeParameterForm() {
        String pageId = this.getPageCache().get("settingPageCache");
        if (pageId != null) {
            Map<String, Object> arg = new HashMap(16);
            arg.put("pageId", pageId);
            ((IClientViewProxy)this.getView().getService(IClientViewProxy.class)).addAction("closeWindow", arg);
            this.getPageCache().remove("settingPageCache");
        }
    }

    /**
     * 数据保存逻辑
     * @author: hst
     * @createDate: 2024/02/23
     */
    private void saveData() {
        IPageCache pageCache = this.getPageCache();
        IFormView parameterView = this.getView().getViewNoPlugin(pageCache.get("settingPageCache"));
        if (parameterView != null) {
            parameterView.getPageCache().put("dataChange", "false");
            IDataModel parameterModel = parameterView.getService(IDataModel.class);
            DynamicObject dataEntity = parameterModel.getDataEntity(true);
            List<FieldTip> fieldKeys = this.getMustInputKey(dataEntity);
            String paramKey;
            String jsonString;
            if (fieldKeys.size() != 0) {
                StringBuilder errorDesc = (new StringBuilder()).append("请按要求填写");

                for(FieldTip fieldKey : fieldKeys) {
                    paramKey = fieldKey.getFieldKey();
                    jsonString = (dataEntity.getDataEntityType().getProperties().get(paramKey)).getDisplayName().getLocaleValue();
                    errorDesc.append("“").append(jsonString).append("”,");
                }

                errorDesc.setCharAt(errorDesc.length() - 1, '。');
                this.getView().showTipNotification(errorDesc.toString());
            } else {
                String nodeId = parameterView.getFormShowParameter().getCustomParam("nodeid");
                boolean isExist = false;
                paramKey = "select fk_yd_params_tag from tk_yd_em_param_setting where fk_yd_nodeid = ?";
                DataSet ds = DB.queryDataSet(this.getClass().toString(), DBRoute.of("em"), paramKey, new Object[]{Long.valueOf(nodeId)});
                Throwable throwable = null;

                try {
                    if (ds != null && ds.hasNext()) {
                        isExist = true;
                    }
                } catch (Throwable th) {
                    throwable = th;
                    throw th;
                } finally {
                    if (ds != null) {
                        if (throwable != null) {
                            try {
                                ds.close();
                            } catch (Throwable th) {
                                throwable.addSuppressed(th);
                            }
                        } else {
                            ds.close();
                        }
                    }

                }

                jsonString = SerializationUtils.toJsonString(dataEntity);
                Date date = new Date();
                String sql;
                Object[] params;
                if (isExist) {
                    sql = "update tk_yd_em_param_setting set fk_yd_params_tag = ?, fk_yd_modifydate = ? " +
                            "where fk_yd_nodeid = ?";
                    params = new Object[]{jsonString, date, Long.valueOf(nodeId)};
                } else {
                    sql = "insert into tk_yd_em_param_setting (fid, fk_yd_nodeid, fk_yd_params_tag, " +
                            "fk_yd_createdate, fk_yd_modifydate) values (?,?,?,?,?)";
                    params = new Object[]{DBServiceHelper.genGlobalLongId(), Long.valueOf(nodeId), jsonString, date, date};
                }

                boolean result = DB.execute(DBRoute.of("em"), sql, params);
                if (result) {
                    this.getView().showSuccessNotification(ResManager.loadKDString("保存成功", "EbParamSettingPlugin_32", "epm-eb-formplugin", new Object[0]));
                }

            }
        }
    }

    /**
     * 获取必录字段
     * @param dataEntity
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    private List<FieldTip> getMustInputKey(DynamicObject dataEntity) {
        List<FieldTip> keys = new ArrayList();
        IDataEntityType type = dataEntity.getDataEntityType();
        DataEntityPropertyCollection props = type.getProperties();

        for (IDataEntityProperty prop : props) {
            if (!(prop instanceof BooleanProp)) {
                if (prop instanceof FieldProp) {
                    FieldProp fieldProp = (FieldProp)prop;
                    if (fieldProp.isMustInput() && kd.bos.dataentity.utils.StringUtils.isBlank(dataEntity.get(fieldProp.getName()))) {
                        keys.add(new FieldTip(FieldTip.FieldTipsLevel.Error, FieldTip.FieldTipsTypes.notNull, fieldProp.getName(), (new LocaleString(ResManager.loadKDString("值不能为空", "EbParamSettingPlugin_33", "epm-eb-formplugin", new Object[0]))).getLocaleValue()));
                    }
                } else if (prop instanceof BasedataProp) {
                    BasedataProp basedataProp = (BasedataProp)prop;
                    if (basedataProp.isMustInput() && kd.bos.dataentity.utils.StringUtils.isBlank(dataEntity.get(basedataProp.getName()))) {
                        keys.add(new FieldTip(FieldTip.FieldTipsLevel.Error, FieldTip.FieldTipsTypes.notNull, basedataProp.getName(), (new LocaleString(ResManager.loadKDString("值不能为空", "EbParamSettingPlugin_33", "epm-eb-formplugin", new Object[0]))).getLocaleValue()));
                    }
                } else if (prop instanceof MulBasedataProp) {
                    MulBasedataProp basedataProp = (MulBasedataProp)prop;
                    if (basedataProp.isMustInput() && dataEntity.getDynamicObjectCollection(basedataProp.getName()).size() == 0) {
                        keys.add(new FieldTip(FieldTip.FieldTipsLevel.Error, FieldTip.FieldTipsTypes.notNull, basedataProp.getName(), (new LocaleString(ResManager.loadKDString("值不能为空", "EbParamSettingPlugin_33", "epm-eb-formplugin", new Object[0]))).getLocaleValue()));
                    }
                }
            }
        }

        return keys;
    }
}
