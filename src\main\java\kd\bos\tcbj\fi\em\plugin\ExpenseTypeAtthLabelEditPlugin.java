package kd.bos.tcbj.fi.em.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.RefObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.form.*;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.operate.FormOperate;
import kd.bos.list.ListShowParameter;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.fi.em.constants.EntityNameConsts;
import org.apache.commons.lang.StringUtils;

import java.util.EventObject;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: hzc
 * @createDate: 2023/12/27
 * @description: 根据费用类型附件配置分录提示表单插件
 * @version: v1.0
 */
public class ExpenseTypeAtthLabelEditPlugin extends AbstractBillPlugIn {
    /** 提交 */
    private static final String OP_SUBMIT = "submit";
    /** 确认框参数 */
    private final static String OPPARAM_AFTEREXPENSETYPEATTACHMENTCONFIRM = "afterExpenseTypeAttachmentConfirm";
    private final static String CALLBACK_ATTACHMENTCONFIRM = "callBackAttachMentConfirm";
    /** 标识-费用类型 */
    private final static String KEY_EXPENSETYPE = "yd_expensetype";
    /** 标识-附件提示 */
    private final static String KEY_ATTACHMENTLABEL = "yd_attachmentlabel";

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        //费用类型
        BasedataEdit expenseTypeEdit = this.getControl(KEY_EXPENSETYPE);
        expenseTypeEdit.addBeforeF7SelectListener(listener -> {
            listener.getFormShowParameter().setCustomParam("openBillType", this.getView().getEntityId());
        });
    }

    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
//        //将附件提示写入标签
//        DynamicObject expenseTypeInfo = (DynamicObject)this.getModel().getValue(KEY_EXPENSETYPE);
//        setAttachmentLabel(expenseTypeInfo);
    }

    @Override
    public void afterLoadData(EventObject e) {
        super.afterLoadData(e);
//        //将附件提示写入标签
//        DynamicObject expenseTypeInfo = (DynamicObject)this.getModel().getValue(KEY_EXPENSETYPE);
//        setAttachmentLabel(expenseTypeInfo);
    }

    @Override
    public void beforeBindData(EventObject e) {
        //将附件提示写入标签
        DynamicObject expenseTypeInfo = (DynamicObject)this.getModel().getValue(KEY_EXPENSETYPE);
        setAttachmentLabel(expenseTypeInfo);
        super.beforeBindData(e);
    }

    @Override
    public void propertyChanged(PropertyChangedArgs e) {
        String key = e.getProperty().getName();
        Object newValue = e.getChangeSet()[0].getNewValue();
        if (KEY_EXPENSETYPE.equals(key)) {
            //将附件提示写入标签
            DynamicObject expenseTypeInfo = (DynamicObject) newValue;
            setAttachmentLabel(expenseTypeInfo);
        }
    }

    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs e) {
        super.beforeDoOperation(e);
        FormOperate operate = (FormOperate)e.getSource();
        // 提交操作
        if (StringUtils.equals(operate.getOperateKey(), OP_SUBMIT)){
            RefObject<String> afterConfirm = new RefObject<>();
            if (!operate.getOption().tryGetVariableValue(OPPARAM_AFTEREXPENSETYPEATTACHMENTCONFIRM, afterConfirm)){
                DynamicObject expenseTypeInfo = (DynamicObject)this.getModel().getValue(KEY_EXPENSETYPE);
                String label = getExpenseTypeLabel(expenseTypeInfo);
                if(StringUtils.isNotBlank(label)){
                    ConfirmCallBackListener confirmCallBackListener = new ConfirmCallBackListener(CALLBACK_ATTACHMENTCONFIRM, this);
                    this.getView().showConfirm("是否继续提交？",label, MessageBoxOptions.YesNo, ConfirmTypes.Default, confirmCallBackListener);
                    // 在没有确认之前，先取消本次操作
                    e.setCancel(true);
                }
            }
        }
    }

    @Override
    public void confirmCallBack(MessageBoxClosedEvent e) {
        super.confirmCallBack(e);
        if(StringUtils.equals(CALLBACK_ATTACHMENTCONFIRM, e.getCallBackId())){
            if(MessageBoxResult.Yes.equals(e.getResult())){//是
                OperateOption operateOption = OperateOption.create();
                operateOption.setVariableValue(OPPARAM_AFTEREXPENSETYPEATTACHMENTCONFIRM, "true");
                operateOption.setVariableValue("afterconfirm", "true");
                this.getView().invokeOperation(OP_SUBMIT, operateOption);
            }
        }
    }

    /**
     * 获取费用类型附件配置提示
     */
    private String getExpenseTypeLabel(DynamicObject expenseTypeInfo) {
        String label = "";//获取费用类型配置的附件提醒
        if(expenseTypeInfo != null){
            DynamicObjectCollection expenseTypeColl = QueryServiceHelper.query(EntityNameConsts.EXPENSE_TYPE
                    , "yd_attachconfigentry.yd_ae_billtype, yd_attachconfigentry.yd_ae_desc"
                    , QFilter.of("id=? and yd_attachconfigentry.yd_ae_billtype=?"
                            , expenseTypeInfo.getLong("id"), this.getView().getEntityId()).toArray());
            if(expenseTypeColl.size() > 0){
                label = expenseTypeColl.get(0).getString("yd_attachconfigentry.yd_ae_desc");
            }
        }
        return label;
    }

    /**
     * 将附件提示写入标签
     */
    private void setAttachmentLabel(DynamicObject expenseTypeInfo) {
        String expenseTypeLabel = getExpenseTypeLabel(expenseTypeInfo);
        this.getModel().setValue(KEY_ATTACHMENTLABEL, expenseTypeLabel);
    }

}
