package kd.bos.tcbj.fi.em.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.events.BeforeCreateListColumnsArgs;
import kd.bos.form.events.BeforeCreateListDataProviderArgs;
import kd.bos.list.IListColumn;
import kd.bos.list.ListColumn;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.mvc.list.ListDataProvider;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.fi.em.constants.EntityNameConsts;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: hzc
 * @createDate: 2023/12/29
 * @description: 费用类型列表
 * @version: v1.0
 */
public class ExpenseTypeListPlugin extends AbstractListPlugin {

    @Override
    public void beforeCreateListDataProvider(BeforeCreateListDataProviderArgs args) {
        String openBillType = (String)this.getView().getFormShowParameter().getCustomParam("openBillType");
        args.setListDataProvider(new MyListDataProvider(openBillType));
    }

    @Override
    public void beforeCreateListColumns(BeforeCreateListColumnsArgs e) {
        super.beforeCreateListColumns(e);
    }
}
class MyListDataProvider extends ListDataProvider {
    private String openBillType = "";

    public MyListDataProvider(String openBillType) {
        this.openBillType = openBillType;
    }

    /**
     * 加载列表数据
     * 获取系统自动加载的列表数据，然后对内容进行修正
     */
    @Override
    public DynamicObjectCollection getData(int arg0, int arg1) {
        DynamicObjectCollection rows = super.getData(arg0, arg1);
        if(StringUtils.isNotBlank(openBillType)){
            if (rows.isEmpty()){
                return rows;
            }
            if (!rows.get(0).getDataEntityType().getProperties().containsKey("yd_attachmentlabel")){
                return rows;
            }
            DynamicObjectCollection expenseTypeColl = QueryServiceHelper.query(EntityNameConsts.EXPENSE_TYPE
                    , "id, yd_attachconfigentry.yd_ae_billtype, yd_attachconfigentry.yd_ae_desc"
                    , QFilter.of("yd_attachconfigentry.yd_ae_billtype=?"
                            , openBillType).toArray());
            Map<Long, String> expenseAtthLabelMap = expenseTypeColl.stream().collect(Collectors.toMap(a -> a.getLong("id"), a -> a.getString("yd_attachconfigentry.yd_ae_desc")));
            for(DynamicObject row : rows){
                row.set("yd_attachmentlabel", expenseAtthLabelMap.get(row.getLong("id")));
            }
        }

        return rows;
    }
}