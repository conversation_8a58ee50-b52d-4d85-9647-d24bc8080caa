package kd.bos.tcbj.fi.em.plugin;

import com.alibaba.fastjson.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.entity.MainEntityType;
import kd.bos.exception.KDBizException;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.fi.em.helper.ClaimGatherHelper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.fi.em.plugin.GatherInfoFormPlugin
 * @className GatherInfoFormPlugin
 * @author: hst
 * @createDate: 2024/01/11
 * @description: 索赔单汇总维度填写动态表单插件
 * @version: v1.0
 */
public class GatherInfoFormPlugin extends AbstractFormPlugin {

    /**
     * 事件注册
     * @param e
     * @author: hst
     * @createDate: 2024/03/01
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        // 设置F7弹窗过滤条件
        this.setF7selectedListener();
    }

    /**
     * 操作执行后
     * @param e
     * @author: hst
     * @createDate: 2024/01/11
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs e) {
        super.afterDoOperation(e);
        if ("confirm".equals(e.getOperateKey()) && e.getOperationResult().isSuccess()) {
            this.packageReturnData();
        }
    }

    /**
     * 设置F7弹窗过滤条件
     * @author: hst
     * @createDate: 2024/03/01
     */
    private void setF7selectedListener() {
        Map<String,Set> params = this.getConfigParam();
        // 库存组织
        BasedataEdit orgEdit = this.getControl("yd_org");
        orgEdit.addBeforeF7SelectListener(listener -> {
            listener.getCustomQFilters().add(new QFilter("yd_eas_number",QFilter.in,params.get("orgs")));
        });
        // 客户
        BasedataEdit customerEdit = this.getControl("yd_customer");
        customerEdit.addBeforeF7SelectListener(listener -> {
            listener.getCustomQFilters().add(new QFilter("number",QFilter.in,params.get("customers")));
        });
    }

    /**
     * 封装回传数据
     * @author: hst
     * @createDate: 2024/01/11
     */
    private void packageReturnData () {
        Map<String, Object> returnData = new HashMap<>();
        DynamicObject dynamicObject = this.getModel().getDataEntity();
        MainEntityType entityType = (MainEntityType) dynamicObject.getDataEntityType();
        Map<String, IDataEntityProperty> fieldMap = entityType.getFields();
        for (String key : fieldMap.keySet()) {
            if (Objects.nonNull(dynamicObject.get(key))) {
                returnData.put(key, dynamicObject.get(key));
            }
        }
        this.getView().returnDataToParent(returnData);
        this.getView().close();
    }

    /**
     * 获取参数配置信息
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    public Map<String,Set> getConfigParam () {
        // 获取对应的节点信息
        DynamicObject node = BusinessDataServiceHelper.loadSingle("yd_param_tree",
                new QFilter("yd_model",QFilter.equals,"yd_claimform").toArray());

        if (Objects.nonNull(node)) {
            // 获取参数配置信息
            DynamicObject config = BusinessDataServiceHelper.loadSingle("yd_em_param_setting",
                    new QFilter("yd_nodeid",QFilter.equals,node.getString("yd_seq")).toArray());

            if (Objects.nonNull(config)) {
                Map<String,Set> params = new HashMap<>();
                String paramStr = config.getString("yd_params_tag");
                JSONObject jsonParam = JSONObject.parseObject(paramStr);

                // 涉及组织
                params.put("orgs",jsonParam.getJSONArray("yd_org").stream()
                        .map(org -> ((JSONObject) org).getJSONObject("fbasedataid").getString("yd_eas_number"))
                        .collect(Collectors.toSet()));
                // 涉及客户
                params.put("customers",jsonParam.getJSONArray("yd_custom").stream()
                        .map(custom -> ((JSONObject) custom).getJSONObject("fbasedataid").getString("number"))
                        .collect(Collectors.toSet()));
                return params;
            } else {
                throw new KDBizException("获取不到参数配置，请联系管理员！");
            }
        } else {
            throw new KDBizException("获取不到参数配置节点信息，请联系管理员！");
        }
    }
}
