package kd.bos.tcbj.fi.em.task;

import kd.bos.context.RequestContext;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.fi.em.helper.ClaimGatherHelper;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @package: kd.bos.schedule.executor.AbstractTask
 * @className: ClaimGatherTask
 * @author: hst
 * @createDate: 2024/03/01
 * @version: v1.0
 */
public class ClaimGatherTask extends AbstractTask {

    // 获取EAS索赔单生成请款单
    private final static String CLAIM_GATHER = "claim_gather";

    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        if (map.containsKey("key")) {
            String key = map.get("key").toString();
            switch (key) {
                case CLAIM_GATHER: {
                    // 获取EAS索赔单
                    this.getEASClaimBills();
                    break;
                }
            }
        }
    }

    /**
     * 获取EAS索赔单
     * @author: hst
     * @createDate: 2024/03/01
     */
    private void getEASClaimBills () {
        // 构建服务流程参数
        Map<String,Object> inputs = new HashMap<>();
        String month = this.getExecuteTime();
        inputs.put("month",month);
        inputs.put("billno","");

        // 获取EAS索赔单
        ApiResult result = new ClaimGatherHelper().getEASClaimBills(inputs, null, null);

        if (!result.getSuccess()) {
            throw new KDBizException(result.getMessage());
        }
    }

    /**
     * 获取同步日期
     * @return
     */
    private String getExecuteTime () {
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();

        // 将月份减去1
        int month = calendar.get(Calendar.MONTH);
        if (month == Calendar.JANUARY || month == Calendar.FEBRUARY) {
            // 如果当前为1或2月，则需要先设置到上年的最后一天再加1
            calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR)-1);
            calendar.set(Calendar.MONTH, Calendar.DECEMBER);
            calendar.add(Calendar.DAY_OF_MONTH, -calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

            // 然后再减去1个月
            calendar.add(Calendar.MONTH, -1);
        } else {
            // 其他情况直接减去1个月
            calendar.add(Calendar.MONTH, -1);
        }

        return new SimpleDateFormat("yyyyMM").format(calendar.getTime());
    }
}
