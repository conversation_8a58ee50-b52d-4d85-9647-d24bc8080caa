package kd.bos.tcbj.fi.em.utils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.EntityType;
import kd.bos.entity.property.BasedataProp;
import kd.bos.entity.property.EntryProp;
import kd.bos.entity.property.MulBasedataProp;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实体工具类
 */
public class DynamicObjectUtil {

    /**
     * 将实体转换为Map（字段值映射)
     * @author: hst
     * @createDate: 2024/01/19
     * @param dynamicObject
     * @return
     */
    public static Map<String, Object> objectMapByQuery(DynamicObject dynamicObject) {
        Map<String, Object> objectMap = new HashMap();
        if (dynamicObject != null) {
            DataEntityPropertyCollection dynamicObjects = dynamicObject.getDataEntityType().getProperties();
            Iterator iterator = dynamicObjects.iterator();

            while(iterator.hasNext()) {
                IDataEntityProperty iDataEntityProperty = (IDataEntityProperty)iterator.next();
                String name = iDataEntityProperty.getName();
                Object object = dynamicObject.get(name);
                if (iDataEntityProperty instanceof BasedataProp) {
                    if (object instanceof DynamicObject) {
                        objectMap.put(name.toLowerCase(), getBDIdOfValue((DynamicObject)object));
                    } else if (object instanceof Long) {
                        objectMap.put(name.toLowerCase(), object);
                    }
                } else if (!(iDataEntityProperty instanceof EntryProp)) {
                    if (iDataEntityProperty instanceof MulBasedataProp) {
                        if (object instanceof DynamicObjectCollection) {
                            objectMap.put(name.toLowerCase(), getMulBaseDataOfId(dynamicObject.getDynamicObjectCollection(name)));
                        }
                    } else {
                        objectMap.put(name.toLowerCase(), object);
                    }
                }
            }
        }
        return objectMap;
    }

    protected static Object getBDIdOfValue(DynamicObject dynamicObject) {
        return dynamicObject != null ? dynamicObject.get("id") : null;
    }

    protected static Object[] getMulBaseDataOfId(DynamicObjectCollection dynamicObjects) {
        List<Object> reList = new ArrayList();
        Iterator iterator = dynamicObjects.iterator();
        while(iterator.hasNext()) {
            DynamicObject dynamicObject = (DynamicObject)iterator.next();
            if (null != dynamicObject && dynamicObject.toString().length() != 0) {
                DynamicObject obj = dynamicObject.getDynamicObject(1);
                if (null != obj) {
                    reList.add(dynamicObject.getDynamicObject(1).getPkValue());
                }
            }
        }
        return reList.toArray();
    }

    /**
     * 获取实体所有字段
     * @author: hst
     * @createDate: 2024/01/19
     * @param dynamicObjectType
     * @return
     */
    public static List<String> getAllField (DynamicObjectType dynamicObjectType) {
        StringBuffer fieldStr = new StringBuffer();
        List<String> fieldList = new ArrayList<>();
        if (dynamicObjectType != null) {
            EntityType ObjectType = (EntityType) dynamicObjectType;
            Map<String, IDataEntityProperty> fields = ObjectType.getFields();
            for (Map.Entry<String, IDataEntityProperty> field : fields.entrySet()) {
                fieldStr.append(field.getValue().getName() + ",");
            }
            fieldList = Arrays.stream(fieldStr.deleteCharAt(fieldStr.length() - 1).toString().split(",")).collect(Collectors.toList());
        }
        return fieldList;
    }
}
