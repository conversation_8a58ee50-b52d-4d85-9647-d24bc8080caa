package kd.bos.tcbj.fi.em.utils;

import java.util.Collection;

/**
 * 字符串
 *
 * <AUTHOR>
 * @Description:
 * @date 2023/06/27
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

    private static final String SEPARATOR = ",";

    /**
     * 将集合转换成字符串
     * @param col 集合对象
     * @return 元素通过分隔符连接
     * <AUTHOR>
     * @date 2022-2-10
     */
    public static String toString(Collection<String> col) {
        if (col == null || col.size() == 0) return "";
        StringBuilder builder = new StringBuilder();
        for (String str : col) {
            builder.append(str).append(SEPARATOR);
        }
        return builder.substring(0, builder.length()-1);
    }
}
