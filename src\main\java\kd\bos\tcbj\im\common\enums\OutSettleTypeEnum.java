package kd.bos.tcbj.im.common.enums;

/**
 * 委外结算类型
 *
 * <AUTHOR>
 * @date 2022-9-1
 */
public enum OutSettleTypeEnum {

    /**值定义**/
    /**直营店一盘货委外结算*/
    DIRECTOUTSETTLE("directOutSettle","直营店一盘货委外结算"),
    /**经销商一盘货委外结算*/
    DISTRIBUTOROUTSETTLE("distributorOutSettle", "经销商一盘货委外结算");

    private String code;
    private String value;
    OutSettleTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
