package kd.bos.tcbj.im.common.enums;

/**
 * PCP业务类型
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-8-29
 */
public enum PcpBusinessTypeEnum {
    INTERNAL_DEAL("internal_deal", "内部交易"),
    INTERNAL_DEAL_RETURN("internal_deal_return", "内部交易退"),
    SALE_RETURN_INTERNAL_DEAL("sale_return_internal_deal", "销售退转内部交易"),
    CREDIT_CONTROL_PRE("credit_control_pre", "信控前置"),
    CREDIT_CONTROL_REAR("credit_control_rear", "信控后置");

    private final String code;
    private final String name;
    PcpBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {return this.code;}
    public String getName() {return this.name;}
}
