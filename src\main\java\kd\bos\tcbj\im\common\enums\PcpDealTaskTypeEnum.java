package kd.bos.tcbj.im.common.enums;

/**
 * PCP处理类型枚举
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-19
 */
public enum PcpDealTaskTypeEnum {
    /**获取PCP销售出库单（当天内的数据）*/
    GETPCPSALEOUTBILL("getPcpSaleOutBill", "获取PCP销售出库单（当天内的数据）"),
    /**获取PCP销售出库单（一周内未获取的数据）*/
    GETPCPSALEOUTBILLBYWEEK("getPcpSaleOutBillByWeek", "获取PCP销售出库单（一周内未获取的数据）"),
    /**下推转换生成销售出库单（当天内的数据）*/
    TRANSSALEOUTBILL("transSaleOutBill", "下推转换生成销售出库单（当天内的数据）"),
    /**下推转换生成销售出库单（还未转换生成的数据）*/
    TRANSSALEOUTBILLBYNOTRANS("transSaleOutBillByNoTrans", "下推转换生成销售出库单（还未转换生成的数据）"),
    /**PCP销售结果数据回传*/
    PUSHSALERESULTTOPCP("pushSaleResultToPcp", "PCP销售结果数据回传"),
    /**获取PCP调拨单*/
    GETPCPTRANSBILL("getPcpTransBill", "获取PCP调拨单"),
    /**PCP调拨单结算*/
    PCPTRANSSETTLE("pcpTransSettle", "PCP调拨单结算");

    private final String code;
    private final String name;
    PcpDealTaskTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {return this.code;}
    public String getName() {return this.name;}
}
