package kd.bos.tcbj.im.common.enums;

/**
 * PCP库存调拨业务类型
 *
 * <AUTHOR>
 * @Description:
 * @date 2024/06/01
 */
public enum PcpTransBizTypeEnum {
    INTERNAL_DEAL("internal_deal_ba", "内部交易"),
    INTERNAL_DEAL_RETURN("internal_deal_return_ba", "内部交易退"),
    SALE_RETURN_INTERNAL_DEAL("sale_return_internal_deal_ba", "销售退转内部交易"),
    ROUTE_INTERNAL_DEAL("route_internal_deal_ba", "在途内部交易"),
    CROSS_ORG_TRANSFER("cross_organization_transfer", "跨组织调拨"),
    SAME_ORG_TRANSFER("same_organization_transfer", "同组织调拨"),
    ORG_TRANSFER_JLD_GAIDA("org_transfer_jld-gaida", "子公司间调拨(健力多、钙达等)"),
    ORG_TRANSFER_TCBJ_YEP("org_transfer_tcbj-yep", "跨组织调拨（汤臣倍健、yep、舒百宁）"),
    ORG_TRANSFER_TYBS("org_transfer_tybs", "跨组织调拨（天然博士）"),
    ORG_TRANSFER_OUT("org_transfer_out", "跨组织调拨（外采）"),
    ORG_TRANSFER_BC_WL("org_transfer_bc-wl", "跨组织调拨（包材、物料）");

    private final String code;
    private final String name;
    PcpTransBizTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {return this.code;}
    public String getName() {return this.name;}
}
