package kd.bos.tcbj.im.helper;

import kd.bos.cache.CacheFactory;
import kd.bos.cache.TempFileCache;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.fileservice.FileService;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.tcbj.im.util.StringUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 附件辅助类
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-6
 */
public class AttachHelper {

    /**
     * 获取附件控件的值
     * @param billEntity 单据标识
     * @param pkValue 单据ID
     * @param entryEntity 分录标识
     * @param attachFieldKey 附件标识
     * <AUTHOR>
     * @date 2022-9-6
     */
    public static List<DynamicObject> getAttachFieldCollection(String billEntity, Object pkValue, String entryEntity, String attachFieldKey) {
        DynamicObject info = BizHelper.getDynamicObjectById(billEntity, pkValue);
        DynamicObjectCollection entryCol = info.getDynamicObjectCollection(entryEntity);
        return entryCol.stream().flatMap(item -> item.getDynamicObjectCollection(attachFieldKey).stream()).collect(Collectors.toList());
    }

    /**
     * 获取附件控件的值
     * @param billEntity 单据标识
     * @param pkValue 单据ID
     * @param attachFieldKeys 附件标识结合
     * <AUTHOR>
     * @date 2022-9-6
     */
    public static List<DynamicObject> getAttachFieldCollection(String billEntity, Object pkValue, String[] attachFieldKeys) {
        DynamicObject info = BizHelper.getDynamicObjectById(billEntity, pkValue);
        return Arrays.stream(attachFieldKeys).flatMap(attachFieldKey -> info.getDynamicObjectCollection(attachFieldKey).stream()).collect(Collectors.toList());
    }

    /**
     * 将url关联的文件服务器文件存储成临时文件
     * （未保存状态下单据的附件必须是临时文件）
     * @param url  文件地址
     * @param name 文件名
     * @return 临时文件地址
     */
    public static String saveAsTempFile(String url, String name) {

        FileService fileService = FileServiceFactory.getAttachmentFileService();
        TempFileCache tempFileCache = CacheFactory.getCommonCacheFactory().getTempFileCache();

        String path = url.split("download.do\\?path=")[1];
        //通过path参数获取文件流
        InputStream inputStream = fileService.getInputStream(path);
        //保存文件流返回
        String tempUrl = tempFileCache.saveAsUrl(name, inputStream, 2 * 60 * 60);
        //域名路径
        String address = RequestContext.get().getClientFullContextPath();
        if (!address.endsWith("/")) {
            address = address + "/";
        }
        tempUrl = address + tempUrl;
        return tempUrl;
    }

    /**
     * 获取附件uid
     */
    public static String getAttachUid() {
        StringBuffer uid = new StringBuffer("rc-upload-");
        uid.append((new Date()).getTime());
        uid.append("-");
        int index = (int)(1.0D + Math.random() * 10.0D);
        uid.append(index);
        return uid.toString();
    }
}
