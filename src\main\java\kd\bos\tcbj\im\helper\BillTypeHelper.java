package kd.bos.tcbj.im.helper;

/**
 * 上下文辅助类
 * <AUTHOR>
 * @Description:
 * @date 2021-12-13
 */
public class BillTypeHelper {

    // ************************* 标准产品 ******************************
    /**银行账户*/
    public static String BILLTYPE_ACCOUNTBANK = "bd_accountbanks";
    /**供应商*/
    public static String BILLTYPE_SUPPLIER = "bd_supplier";
    /**供应商库的供应商*/
    public static String BILLTYPE_SRM_SUPPLIER = "srm_supplier";
    /**客户*/
    public static String BILLTYPE_CUSTOMER = "bd_customer";
    /**业务组织*/
    public static String BILLTYPE_ORG = "bos_org";
    /**人员（用户）*/
    public static String BILLTYPE_USER = "bos_user";
    /**会计期间*/
    public static String BILLTYPE_PERIOD = "bd_period";
    /**行政组织*/
    public static String BILLTYPE_ADMINORG = "bos_adminorg";
    /**费用项目*/
    public static String BILLTYPE_EXPENSEITEMEDIT = "er_expenseitemedit";
    /**采购入库单**/
    public static String BILLTYPE_PURINBILL = "im_purinbill";
    /**销售出库单**/
    public static String BILLTYPE_SALEOUTBILL = "im_saloutbill";
    /**物料**/
    public static String BILLTYPE_MATERIAL = "bd_material";
    /**仓库**/
    public static String BILLTYPE_WAREHOUSE = "bd_warehouse";
    /**单据类型*/
    public static String BILLTYPE_BILLTYPE = "bos_billtype";
    /**行类型*/
    public static String BILLTYPE_LINETYPE = "bd_linetype";
    /**税率*/
    public static String BILLTYPE_TAXRATE = "bd_taxrate";
    /**附件字段*/
    public static String BILLTYPE_BD_ATTACHMENT = "bd_attachment";

    // ************************* 二开 ******************************
    /**发货明细表*/
    public static String BILLTYPE_FHMXB = "yd_fhmxb";
    /**客户对应关系（店铺对应关系）*/
    public static String BILLTYPE_KHDYGX = "yd_khdygx";
    /**内部仓库交易确认表（组织仓库对应关系表）*/
    public static String BILLTYPE_ORGWAREHOUSE = "yd_orgwarehouse";
    /**内部组织交易价格表*/
    public static String BILLTYPE_PRICERELBILL = "yd_pricerelbill";
    /**排除物料*/
    public static String BILLTYPE_PCWL = "yd_pcwl";
    /**仓库对应关系*/
    public static String BILLTYPE_WAREHOUSERELA = "yd_ckdygx";
    /**EAS即时库存*/
    public static String BILLTYPE_EASINVENTORY = "yd_easinventory";
    /**直营店客户结算关系表*/
    public static String BILLTYPE_DIRECTSETTLERELA = "yd_directsettlerela";
    /**内部交易关系表--一盘货/直营店结算*/
    public static String BILLTYPE_INNERRELBILL = "yd_innerrelbill";
    /**经销商供货价格表*/
    public static String BILLTYPE_ORGCUSPRICEBILL = "yd_orgcuspricebill";
    /**组织分类表*/
    public static String BILLTYPE_ORGCLASSIFY = "yd_orgclassify";
    /**PCP组织映射表*/
    public static String BILLTYPE_PCPORGRELATION = "yd_pcporgrelation";
    /**销售出库单中间表*/
    public static String BILLTYPE_MIDSALEOUTBILL = "yd_midsaleoutbill";
    /**参数配置表*/
    public static String BILLTYPE_INTERFACECONFIG = "yd_e3paramsetting";
    /**直营店仓库表*/
    public static String BILLTYPE_DIRECTWAREHOUSE = "yd_directwarehouse";
    /**调拨内部结算关系*/
    public static String BILLTYPE_TRANSSETTLERELA = "yd_transsettlerela";
    /**库存调拨单中间表*/
    public static String BILLTYPE_MIDTRANSBILL = "yd_midtransbill";
    /**委外品牌仓库映射关系表*/
    public static String BILLTYPE_OUTWAREHOUSEMAP = "yd_outwarehousemap";
    /**主商品拆分表*/
    public static String BILLTYPE_MATCOMBINATION = "yd_matcombination";

    /**SRM**/
    /**供应商研发新品单*/
    public static String BILLTYPE_SUPPLIERDEVELOPNP = "yd_supplierdevelopnp";
    /**供应商资质变更确认单*/
    public static String BILLTYPE_SUPAPTITUDECHANGE = "yd_supaptitudechange";

    // ************************* 弹窗 ******************************
    /**日期参数*/
    public static String BILLTYPE_RQCS = "yd_rqcs";
    /**日期过滤*/
    public static String BILLTYPE_PCPPARAMFILTER = "yd_pcpparamfilter";
    /**补充意见弹窗*/
    public static String BILLTYPE_SUPPLYCOMMENT = "yd_supplycomment";
    /**成品仓一盘货结算关系表*/
    public static String BILLTYPE_WARESETTLERELA = "yd_warehouseinventory";
    /**直接调拨单*/
    public static String BILLTYPE_TRANSDIRBILL = "im_transdirbill";
    /**其他出库单*/
    public static String BILLTYPE_OTHEROUTBILL = "im_otheroutbill";
    /**物料库存信息**/
    public static String BILLTYPE_MATINVENTORY = "bd_materialinventoryinfo";
}
