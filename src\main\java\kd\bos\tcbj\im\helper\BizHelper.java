package kd.bos.tcbj.im.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.db.tx.TX;
import kd.bos.db.tx.TXHandle;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.ValueMapItem;
import kd.bos.entity.operate.result.OperateErrorInfo;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.property.ComboProp;
import kd.bos.entity.property.EntryProp;
import kd.bos.entity.validate.ValidateResult;
import kd.bos.exception.ErrorCode;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.util.BigDecimalUtil;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.ORMUtils;
import kd.bos.tcbj.im.util.QueryUtil;
import kd.bos.tcbj.im.vo.R;
import kd.epm.eb.ebBusiness.serviceHelper.MutexServiceHelper;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务辅助类
 * <AUTHOR>
 * @Description:
 * @date 2021-12-13
 */
public class BizHelper {

    /**金额0*/
    public final static BigDecimal ZERO = BigDecimalUtil.ZERO;
    /**金额1*/
    public final static BigDecimal ONE = BigDecimalUtil.ONE;

    /**
     * 获取运行时的位置（类+方法+位置）
     * 可作为algo执行标识
     * @return 返回运行时的位置
     * <AUTHOR>
     * @date 2022-6-9
     */
    public static String getRunLocate() {
        // 0表示当前，1表示上级调用者的
        StackTraceElement ele = new Throwable().getStackTrace()[1];
        return new StringBuilder().append(ele.getClassName()).append("_")
                .append(ele.getMethodName()).append("_").append(ele.getLineNumber()).toString();
    }

    /**
     * 获取当前用户ID
     * @return 返回用户ID
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Long getUserId() {
        return UserServiceHelper.getCurrentUserId();
    }

    /**
     * 获取当前用户ID
     * @return 返回用户ID
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static DynamicObject getUserInfo() {
        return UserServiceHelper.getCurrentUser("name");
    }

    /**
     * 获取AppId
     * @param billType 单据类型
     * @return AppId
     * <AUTHOR>
     * @date 2021-12-14
     */
    public static String getAppId(String billType) {
        MainEntityType mainEntityType = EntityMetadataCache.getDataEntityType(billType);
        return mainEntityType.getAppId();
    }

    /**
     * 根据单据类型获取单据的名称
     * @param billType 单据类型
     * @return 单据类型对应的名称
     * <AUTHOR>
     * @date 2022-1-18
     */
    public static String getBillTypeName(String billType) {
        try {
            DynamicObjectType type = EntityMetadataCache.getDataEntityType(billType);
            return type.getDisplayName().toString();
        }catch (Exception err) {
            err.printStackTrace();
        }
        return null;
    }

    public static String colToString(Collection col) {
        return StringUtils.join(col.iterator(), ",");
    }

    public static String colToStringBrace(Collection col) {
        return colToStringBrace(col.toArray());
    }

    public static String colToStringBrace(Object[] objs) {
        Objects.requireNonNull(objs);
        StringBuilder builder = new StringBuilder();
        builder.append("(");
        for (int index = 0, size = objs.length; index < size; index++) {
            if (index == size-1) {
                builder.append("'").append(objs[index]).append("'");
            }else {
                builder.append("'").append(objs[index]).append("',");
            }
        }
        return builder.append(")").toString();
    }

    /**
     * 属性拷贝
     * @param 
     * @return 
     * <AUTHOR>
     * @date 2022-3-10
     */
    public static DynamicObject copy(DynamicObject info, String[][] headPropsArr, String entryType, String[][] entryPropsArr) {
        // 新增数据
        DynamicObject copyInfo = BusinessDataServiceHelper.newDynamicObject(info.getDataEntityType().getName());
        // 表头数据复制
        for (String[] headProps : headPropsArr) {
            if (headProps.length == 0) continue;
            String srcProp = headProps[0];
            String destProp = headProps.length>1?headProps[1]:headProps[0];
            copyInfo.set(destProp, info.get(srcProp));
        }
        // 明细数据复制
        if (StringUtils.isNotBlank(entryType) && entryPropsArr != null) {
            DynamicObjectCollection srcEntryCol = info.getDynamicObjectCollection(entryType);
            DynamicObjectCollection destEntryCol = copyInfo.getDynamicObjectCollection(entryType);
            for (DynamicObject srcEntryInfo : srcEntryCol) {
                DynamicObject destEntryInfo = destEntryCol.addNew();
                for (String[] entryProp : entryPropsArr) {
                    if (entryProp.length == 0) continue;
                    String srcProp = entryProp[0];
                    String destProp = entryProp.length > 1 ? entryProp[1] : entryProp[0];
                    destEntryInfo.set(destProp, srcEntryInfo.get(srcProp));
                }
            }
        }
        return copyInfo;
    }
    
    /**
     * 获取当前的会计期间
     * @return 返回当前会计期间
     * <AUTHOR>
     * @date 2021-12-16
     */
    public static DynamicObject getCurrentPeriod() {
        return getPeriod(new Date());
    }

    /**
     * 获取当前期间的上一期间
     * @return 返回对应的会计期间
     * <AUTHOR>
     * @date 2021-12-16
     */
    public static DynamicObject getLastPeriod() {
        Date lastMonthDate = DateTimeUtils.addMonth(new Date(), -1);
        DynamicObject info = getQuerySingleDynamicObject(BillTypeHelper.BILLTYPE_PERIOD, "id", QueryUtil.q(new Object[][]{{"begindate", "<=", lastMonthDate}, {"enddate", ">=", lastMonthDate}, {"isadjustperiod", 0}}));
        return getDynamicObjectById(BillTypeHelper.BILLTYPE_PERIOD, info.getLong("id"));
    }

    /**
     * 获取当前期间的上一期间
     * @param periodInfo 期间
     * @return 返回对应的会计期间
     * <AUTHOR>
     * @date 2021-12-16
     */
    public static DynamicObject getLastPeriod(DynamicObject periodInfo) {
        Objects.requireNonNull(periodInfo);
        periodInfo = getQuerySingleDynamicObject(BillTypeHelper.BILLTYPE_PERIOD, "begindate", QueryUtil.q("id", periodInfo.getPkValue()));
        Date beginDate = periodInfo.getDate("begindate");
        return getPeriod(DateTimeUtils.addDate(beginDate, -1));
    }

    /**
     * 获取当前期间的下一期间
     * @param periodInfo 期间
     * @return 返回对应的会计期间
     * <AUTHOR>
     * @date 2021-12-16
     */
    public static DynamicObject getNextPeriod(DynamicObject periodInfo) {
        Objects.requireNonNull(periodInfo);
        periodInfo = getQuerySingleDynamicObject(BillTypeHelper.BILLTYPE_PERIOD, "*", QueryUtil.q("id", periodInfo.getPkValue()));
        Date endDate = periodInfo.getDate("enddate");
        return getPeriod(DateTimeUtils.addDate(endDate, 1));
    }

    /**
     * 获取日期对应的会计期间
     * @param date 日期
     * @return 返回对应的会计期间
     * <AUTHOR>
     * @date 2021-12-16
     */
    public static DynamicObject getPeriod(Date date) {
        return BusinessDataServiceHelper.loadSingleFromCache("bd_period", QueryUtil.q(new Object[][] {
                {"begindate", QCP.less_equals, date} // 开始日期
                , {"enddate", QCP.large_equals, date} // 结束日期
                , {"isadjustperiod", 0} // 调整期
        }));
    }

    /**
     * 获取某个单据的下拉框值
     * @param billType 单据类型
     * @param fieldKey 查询的字段
     * @return {值: 别名}
     * <AUTHOR>
     * @date 2021-12-27
     */
    public static Map<String, String> getEnum(String billType, String fieldKey) {
        DynamicObjectType type = EntityMetadataCache.getDataEntityType(billType);
        ComboProp comboProp = (ComboProp) type.getProperties().get(fieldKey);
        List<ValueMapItem> comboItems = comboProp.getComboItems();
        Map<String, String> result = new LinkedHashMap<>();
        for (ValueMapItem comboItem : comboItems) {
            result.put(comboItem.getValue(), comboItem.getName().toString());
        }
        return result;
    }

    /**
     * 获取某个单据的下拉框值
     * @param billType 单据类型
     * @param fieldKey 查询的字段
     * @return {值: 别名}
     * <AUTHOR>
     * @date 2021-12-27
     */
    public static Map<String, String> getEnum(String billType, String entryType, String fieldKey) {
        DynamicObjectType type = EntityMetadataCache.getDataEntityType(billType);
        EntryProp entryProp = (EntryProp) type.getProperties().get(entryType);
        ComboProp comboProp = (ComboProp) entryProp.getDynamicCollectionItemPropertyType().getProperties().get(fieldKey);
        List<ValueMapItem> comboItems = comboProp.getComboItems();
        Map<String, String> result = new LinkedHashMap<>();
        for (ValueMapItem comboItem : comboItems) {
            result.put(comboItem.getValue(), comboItem.getName().toString());
        }
        return result;
    }

    /**
     * 比较两个期间的大小
     * @param periodInfo1 期间1
     * @param periodInfo2 期间2
     * @return 返回大小
     * <AUTHOR>
     * @date 2022-1-10
     */
    public static int periodCompareTo(DynamicObject periodInfo1, DynamicObject periodInfo2) {
        if (periodInfo1 == null && periodInfo2 != null) return -1;
        if (periodInfo1 != null && periodInfo2 == null) return 1;
        if (periodInfo1 == null) return 0;
        return periodInfo1.getString("number").compareTo(periodInfo2.getString("number"));
    }

    /**
     * 根据ID查询对象
     * @param billType 单据标识
     * @param billId 单据ID
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObjectCollection getQueryDynamicObjectById(String billType, Object billId) {
        return getQueryDynamicObjectById(billType, billId, QueryUtil.getSelectCols("*"));
    }

    /**
     * 根据ID查询对象
     * @param billType 单据标识
     * @param billId 单据ID
     * @param selectProperties 查询的字段
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObjectCollection getQueryDynamicObjectById(String billType, Object billId, String selectProperties) {
        return QueryServiceHelper.query(billType, selectProperties, QueryUtil.q("id", billId));
    }

    /**
     * 根据过滤信息查询对象
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject getQuerySingleDynamicObject(String billType, String selectProperties, QFilter[] qFilters) {
        return QueryServiceHelper.queryOne(billType, selectProperties, qFilters);
    }

    /**
     * 根据过滤信息查询对象
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObjectCollection getQueryDynamicObject(String billType, String selectProperties, QFilter[] qFilters) {
        return QueryServiceHelper.query(billType, selectProperties, qFilters);
    }

    /**
     * 根据过滤信息查询对象
     * @param algo 查询标识
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DataSet getQueryDataSet(String algo, String billType, String selectProperties, QFilter[] qFilters) {
        return getQueryDataSet(algo, billType, selectProperties, qFilters, null);
    }

    /**
     * 根据过滤信息查询对象
     * @param algo 查询标识
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @param orderBy 排序
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DataSet getQueryDataSet(String algo, String billType, String selectProperties, QFilter[] qFilters, String orderBy) {
        return QueryServiceHelper.queryDataSet(algo, billType, selectProperties, qFilters, orderBy);
    }

    /**
     * 根据条件获取查询的ID
     * @param billType 单据标识
     * @param qFilters 查询的条件
     * @return 返回单据的ID，如果不存在则返回0L
     * <AUTHOR>
     * @date 2022-6-16
     */
    public static long getQueryId(String billType, QFilter[] qFilters) {
        DataSet dataSet = null;
        try {
            dataSet = getQueryDataSet("getQueryId", billType, "id", qFilters);
            if (!dataSet.hasNext()) return 0L;
            Row row = dataSet.next();
            return row.getLong("id");
        }finally {
            ORMUtils.close(dataSet);
        }
    }

    /**
     * 根据条件获取查询的ID
     * @param billType 单据标识
     * @param qFilters 查询的条件
     * @return 返回单据的ID，如果不存在则返回0L
     * <AUTHOR>
     * @date 2022-6-16
     */
    public static <T> T getQueryOne(String billType, String selector, QFilter[] qFilters) {
        DataSet dataSet = null;
        try {
            dataSet = getQueryDataSet("getQueryOne", billType, selector, qFilters);
            if (!dataSet.hasNext()) return null;
            Row row = dataSet.next();
            return (T) row.get(0);
        }finally {
            ORMUtils.close(dataSet);
        }
    }

    /**
     * 根据条件获取查询的ID
     * @param billType 单据标识
     * @param qFilters 查询的条件
     * @return 返回单据的ID，如果不存在则返回0L
     * <AUTHOR>
     * @date 2022-6-16
     */
    public static Set<Object> getQueryCol(String billType, String selector, QFilter[] qFilters) {
        DataSet dataSet = null;
        try {
            dataSet = getQueryDataSet("getQueryCol", billType, selector, qFilters);
            if (!dataSet.hasNext()) return null;
            Set<Object> col = new HashSet<>();
            for (Row row : dataSet) {
                col.add(row.get(selector));
            }
            return col;
        }finally {
            ORMUtils.close(dataSet);
        }
    }

    /**
     * 根据ID查询对象
     * @param billType 单据标识
     * @param billId 单据ID
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject getDynamicObjectById(String billType, Object billId) {
        if (billId == null || StringUtils.isEmpty(billId.toString())) return null;
        return BusinessDataServiceHelper.loadSingle(billId, billType);
    }

    /**
     * 根据ID查询对象
     * @param billType 单据标识
     * @param billId 单据ID
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject getDynamicObjectByIdFromCache(String billType, Object billId) {
        if (billId == null || StringUtils.isEmpty(billId.toString())) return null;
        return BusinessDataServiceHelper.loadSingleFromCache(billId, billType);
    }

    /**
     * 根据ID查询对象
     * @param billType 单据标识
     * @param billId 单据ID
     * @param selectProperties 查询的字段
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject getDynamicObjectById(String billType, Object billId, String selectProperties) {
        if (billId == null || StringUtils.isEmpty(billId.toString())) return null;
        return BusinessDataServiceHelper.loadSingle(billId, billType, selectProperties);
    }

    /**
     * 根据ID查询对象
     * @param billType 单据标识
     * @param billId 单据ID
     * @param selectProperties 查询的字段
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject getDynamicObjectByIdFromCache(String billType, Object billId, String selectProperties) {
        if (billId == null || StringUtils.isEmpty(billId.toString())) return null;
        return BusinessDataServiceHelper.loadSingleFromCache(billId, billType, selectProperties);
    }

    /**
     * 根据过滤信息查询对象
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject getSingleDynamicObject(String billType, String selectProperties, QFilter[] qFilters) {
        return BusinessDataServiceHelper.loadSingle(billType, selectProperties, qFilters);
    }

    /**
     * 根据过滤信息从缓存中查询对象
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject getSingleDynamicObjectFromCache(String billType, String selectProperties, QFilter[] qFilters) {
        return BusinessDataServiceHelper.loadSingleFromCache(billType, selectProperties, qFilters);
    }

    /**
     * 根据过滤信息查询对象
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject[] getDynamicObject(String billType, String selectProperties, QFilter[] qFilters) {
        return BusinessDataServiceHelper.load(billType, selectProperties, qFilters);
    }

    /**
     * 根据过滤信息查询对象
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @param orderBy 按某个字段过滤
     * @param top 查询前几条数据
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject[] getDynamicObject(String billType, String selectProperties, QFilter[] qFilters, String orderBy, int top) {
        return BusinessDataServiceHelper.load(billType, selectProperties, qFilters, orderBy, top);
    }

    /**
     * 根据过滤信息从缓存中查询对象
     * @param billType 单据标识
     * @param selectProperties 查询的字段
     * @param qFilters 过滤信息
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static Map<Object, DynamicObject> getDynamicObjectFromCache(String billType, String selectProperties, QFilter[] qFilters) {
        return BusinessDataServiceHelper.loadFromCache(billType, selectProperties, qFilters);
    }

    /**
     * 创建一个对象信息
     * @param billType 单据类型
     * @return 返回新的对象信息
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static DynamicObject createDynamicObject(String billType) {
        return BusinessDataServiceHelper.newDynamicObject(billType);
    }

    /**
     * 创建一个分录的对象信息
     * @param entryCol 分录集合对象
     * @return 返回新的对象信息
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static DynamicObject createEntryDynamicObject(DynamicObjectCollection entryCol) {
        DynamicObjectType entryBillType = entryCol.getDynamicObjectType();
        return new DynamicObject(entryBillType);
    }

    /**
     * 保存对象信息
     * @param info 对象信息
     * @return 返回对象保存的结果信息
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static void save(DynamicObject info) {
        Objects.requireNonNull(info);
        SaveServiceHelper.save(new DynamicObject[]{info});
    }

    /**
     * 保存对象信息
     * @param info 对象信息
     * @return 返回对象保存的结果信息
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static OperationResult saveDynamicObject(DynamicObject info) {
        Objects.requireNonNull(info);
        return SaveServiceHelper.saveOperate(info.getDataEntityType().getName(), new DynamicObject[]{info}, OperateOption.create());
    }

    /**
     * 保存对象信息
     * @param cols 对象集合
     * @return 返回对象保存的结果信息
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static OperationResult saveDynamicObject(DynamicObject[] cols) {
        if (cols == null || cols.length == 0) return null;
        return SaveServiceHelper.saveOperate(cols[0].getDataEntityType().getName(), cols, null);
    }

    /**
     * 根据ID删除对应的单据（会判断是否允许删除）
     * @param billType 单据标识
     * @param billId 单据ID
     * @return 根据ID删除对应的单据信息
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static OperationResult deleteDynamicObjectById(String billType, Object billId) {
        return new DeleteServiceHelper().deleteOperate(billType, new Object[]{billId}, null);
    }

    /**
     * 根据ID删除对应的单据（会判断是否允许删除）
     * @param billType 单据标识
     * @param billIds 单据ID集合
     * @return 根据ID删除对应的单据信息
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static OperationResult deleteDynamicObjectById(String billType, Object[] billIds) {
        return new DeleteServiceHelper().deleteOperate(billType, billIds, null);
    }

    /**
     * 执行单据的操作（通常为单据的提交/审核/反审核）
     * @param operationKey 操作的标识
     * @param billType 单据标识
     * @param pks 处理的单据ID集合
     * @return 返回处理的结果信息
     * <AUTHOR>
     * @date 2022-06-15
     */
    public static OperationResult operate(String operationKey, String billType, Object[] pks) {
         return OperationServiceHelper.executeOperate(operationKey, billType, pks, OperateOption.create());
    }

    /**
     * 根据单据标识和单据ID查找系统是否存在单据信息
     * @param billType 单据标识
     * @param billId 单据ID
     * @return 是否存在当前的单据信息
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static boolean isExists(String billType, String billId) {
        if (StringUtils.isBlank(billId)) return false;
        return QueryServiceHelper.exists(billType, QueryUtil.q("id", billId));
    }
    /**
     * 根据单据标识和过滤条件查找系统是否存在单据信息
     * @param billType 单据标识
     * @param qFilters 过滤条件
     * @return 是否存在当前的单据信息
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static boolean isExists(String billType, QFilter[] qFilters) {
        return QueryServiceHelper.exists(billType, qFilters);
    }

    /**
     * 判断单据是否已经加锁
     * @param billType 单据标识
     * @param billId 单据ID
     * @param operationKey 操作标识
     * @return 是否已经加锁
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static boolean isBillLock(String billType, long billId, String operationKey) {
        return MutexServiceHelper.getLockInfo(String.valueOf(billId), billType, operationKey) != null;
    }

    /**
     * 单据请求加锁
     * @param billType 单据标识
     * @param billId 单据ID
     * @param operationKey 操作标识
     * @return 是否加锁成功
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static boolean requestLock(String billType, long billId, String operationKey) {
        return MutexServiceHelper.request(String.valueOf(billId), billType, operationKey);
    }

    /**
     * 释放互斥锁
     * @param billType 单据标识
     * @param billId 要释放锁的单据ID
     * @param operationKey 操作标识
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static void releaseLock(String billType, long billId, String operationKey) {
        MutexServiceHelper.release(String.valueOf(billId), billType, operationKey);
    }

    /**
     * 统计分录金额字段汇总
     * @param col 分录集合数据
     * @param key 金额字段
     * @return 返回汇总值
     * <AUTHOR>
     * @date 2022-1-5
     */
    public static BigDecimal sumEntryAmount(DynamicObjectCollection col, String key) {
        BigDecimal value = BigDecimalUtil.ZERO;
        for (DynamicObject info : col) {
            value = value.add(BigDecimalUtil.getValue(info.getBigDecimal(key)));
        }
        return value;
    }

    /**
     * 初始DynamicObject的系统默认字段
     * @param info 新增的单据对象
     * @param isBill 是否是业务单据，为否则为基础资料
     * <AUTHOR>
     * @date 2022-9-29
     */
    public static void defaultDynamicObject(DynamicObject info, boolean isBill) {
        // ---------- 标准字段的字段设置 ----------------
        if (isBill) {
            info.set("billstatus", "A"); // 单据状态
        }else {
            info.set("status", "A"); // 单据状态
        }
        DynamicObject currentUserInfo = getUserInfo();
        Date currentDate = new Date();
        info.set("creator", currentUserInfo); // 创建人
        info.set("createtime", currentDate); // 创建时间
        info.set("modifier", currentUserInfo); // 修改人
        info.set("modifytime", currentDate); // 修改时间
    }

    /**
     * 获取执行操作的错误信息
     * @param result 执行操作返回的操作结果对象——通常为保存、提交、审核
     * @return 错误信息
     * <AUTHOR>
     * @date 2022-9-29
     */
    public static String getOperationErrorMessage(OperationResult result) {
        if (result != null && !result.isSuccess()) {
            StringBuilder validMsg = new StringBuilder();
            if (result.getValidateResult() != null && result.getValidateResult().getValidateErrors().size()> 0) {
                int index = 0;
                for (ValidateResult validateError : result.getValidateResult().getValidateErrors()) {
                    for (OperateErrorInfo errInfo : validateError.getAllErrorInfo()) {
                        validMsg.append(++index).append("、").append(errInfo.getMessage());
                    }
                }
            }
            // 错误摘要
            String errMessage = result.getMessage();
            if (validMsg.length() > 0) errMessage = errMessage + "校验信息："+validMsg.toString();
            return errMessage;
        }
        return null;
    }
}
