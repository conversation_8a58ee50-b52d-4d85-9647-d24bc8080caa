package kd.bos.tcbj.im.helper;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.im.util.*;
import kd.bos.tcbj.im.util.special.HmacAlgorithms;
import kd.bos.tcbj.im.util.special.HmacUtils;
import kd.bos.tcbj.im.vo.R;
import kd.bos.tcbj.im.vo.RequestMap;
import org.apache.commons.codec.digest.DigestUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 接口辅助类
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-6-14
 */
public class InterHelper {
    // 创建logger
    private final static Log logger = LogFactory.getLog("InterHelper");

    /**默认页码*/
    public final static int DEFAULT_PAGENUMBER = 1;
    /**默认查询的页数*/
    public final static int DEFAULT_PAGESIZE = 10;

    /**
     * 获取PCP销售出库单
     * @param saleType 销售类型（SALE：销售单，SALE_RED：销售红单）
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @param billNo PCP出库结果单号
     * <AUTHOR>
     * @date 2022-6-14
     */
    public static boolean getPcpSaleOutBill(String saleType, Date beginDate, Date endDate, String billNo) {
        int nextPage = DEFAULT_PAGENUMBER; // 页码
        int pageSize = DEFAULT_PAGESIZE; // 页面大小
        boolean hasNextPage = true;
        boolean isPass = false;

        boolean isRefund = "SALE_RED".equals(saleType); // 业务类型是否为退货场景
        DynamicObject currentUserInfo = BizHelper.getUserInfo();

        //update by hst 2022/10/31 查询标记一盘货判断参数（true 业务类型是后置并且销售组织不等于库存组织；false 以接口中特定参数判断）
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"PCP_INVENTORY_SETTLE")});
        String value = param.getString("name");
        boolean isSettle = StringUtils.isNotBlank(value) ? "true".equals(value) ? true : false : false;
        // update by hst 2023/03/21 一盘货标记为线下一盘货（true)/开门红(false)控制
        DynamicObject OfflineParam = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"PCP_INVENTORY_OFFLINE")});
        String offlineValue = OfflineParam.getString("name");
        boolean isOffline = StringUtils.isNotBlank(offlineValue) ? "true".equals(offlineValue) ? true : false : true;

        // 按指定的页码和页数进行查询
        while (hasNextPage) {
            R result = getPcpSaleOrPurchaseBill(saleType, beginDate, endDate, billNo, nextPage, pageSize);
            if (result.getCode() != 0) throw new KDBizException((String) result.get("msg"));
            // 接口请求成功，则获取响应的结果
            RequestMap requestMap = (RequestMap) result.get("data");
            hasNextPage = InterUtils.getBoolean(requestMap.get("hasNextPage"), "是否存在下页", false);// 判断是否存在下页，存在下页则继续请求处理
            nextPage = InterUtils.getInt(requestMap.get("nextPage"), "下页数据页码", false); // 下页数据页码，方便数据请求

            List<RequestMap> billList = InterUtils.getList(requestMap.get("list"), "单据集合", false);

            // 遍历单据的结果集
            for (RequestMap billMap : billList) {
                //查看接口数据格式
                logger.info(billMap.toString());
                // 单头数据取数
                String documentNo = InterUtils.getString(billMap.get("documentNo"), "单号");    // 单号
                String createTime = InterUtils.getString(billMap.get("createTime"));    // 业务发生日期
                String organizationCode = InterUtils.getString(billMap.get("organizationCode"));    // 库存组织编码
                //update by hst 2022/11/09 接口字段名调整
                String saleOrganizationCode = InterUtils.getString(billMap.get("salesOrgCode"));    // 销售组织编码
                String customerCode = InterUtils.getString(billMap.get("customerCode"));    // 客户编码
                String remark = InterUtils.getString(billMap.get("remark"));    // 备注
//                String relevanceNo = InterUtils.getString(billMap.get("relevanceNo"));    // 来源销售订单（csp）
                String easSaleOrderNo = InterUtils.getString(billMap.get("easSaleOrderNo"));    // 来源销售订单（eas）
                String platformOrderNo = InterUtils.getString(billMap.get("platformOrderNo"));    // CSP单号（营销云单号）
                String itemTotalAmount = InterUtils.getString(billMap.get("itemTotalAmount"));    // 商品总金额，2022-08-28新增
                String businessType = InterUtils.getString(billMap.get("businessType"));    // 业务类型，2022-08-28新增
                String sourceTradeNumber = InterUtils.getString(billMap.get("sourceOrderNo"));    // 内部交易源头单号，2022-08-28新增
                String subDivisionPlatform = InterUtils.getString(billMap.get("saleChannel"));    // 电商渠道，2022-09-15新增
                String saleDistribution = InterUtils.getString(billMap.get("marketChannel"));    // 销售渠道，2022-09-15新增
                String office = InterUtils.getString(billMap.get("office"));    // 办事处，2022-09-20新增
                String province = InterUtils.getString(billMap.get("province"));   	 // 省份，2022-09-20新增
                String city = InterUtils.getString(billMap.get("city"));    // 城市，2022-09-20新增
                String district = InterUtils.getString(billMap.get("district"));    // 地区，2022-09-20新增
                String linkman = InterUtils.getString(billMap.get("linkman"));    // 联系人，2022-09-20新增
                String phone = InterUtils.getString(billMap.get("phone"));    // 联系方式，2022-09-20新增
                String address = InterUtils.getString(billMap.get("address"));    // 地址，2022-09-26新增
                String description = InterUtils.getString(billMap.get("description"));    // 摘要，2022-09-29新增
                String settleOrgNumber = InterUtils.getString(billMap.get("settlementOrgCode"));    // 结算组织编码，2022-11-1新增
                String purOrder = InterUtils.getString(billMap.get("maiyouEasNo"));    // 采购订单号，2023-02-15新增
                String orderBusinessType = InterUtils.getString(billMap.get("orderBusinessType"));      // 单据类型 2023-04-14新增

                // 校验单号是否存在
                boolean isExists = BizHelper.isExists(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, QFilter.of("yd_sourcefunction='PCP' and billno=?", documentNo).toArray());
                if (isExists) continue;

                // 设置单头值
                Date currentDate = new Date(); // 当前日期
                DynamicObject billInfo = BizHelper.createDynamicObject(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL);
                billInfo.set("billno", documentNo); // 单据编码
                billInfo.set("billstatus", "C"); // 单据状态，默认为保存
                billInfo.set("yd_biztype", isRefund ? "2101" : "210"); // 210-物料销售、2101-物料销售退货
                billInfo.set("yd_transno", isRefund ? "2101" : "210"); // 210-普通销售、2101-普通销售退货
                billInfo.set("yd_bizdate", StringUtils.isNoneBlank(createTime)?DateTimeUtils.parse(createTime):null); // 业务日期
                billInfo.set("yd_orgnumber", organizationCode); // 库存组织编码
                billInfo.set("yd_saleorgnumber", saleOrganizationCode); // 销售组织编码
                billInfo.set("yd_customnumber", customerCode); // 客户编码
                billInfo.set("yd_description", remark); // 备注
                billInfo.set("yd_sourcefunction", "PCP"); // 默认来源类型为PCP
                billInfo.set("yd_saleordernumber", easSaleOrderNo); // 来源销售订单
                billInfo.set("yd_platformorderno", platformOrderNo); // CSP单号（营销云单号）
                billInfo.set("yd_ishasnext", false); // 是否有下游单据
                billInfo.set("yd_settleorgnumber",settleOrgNumber);     // 结算组织 2022/11/19

                // 新增 cr 2022-08-28，额外新增对接字段
                billInfo.set("yd_itemtotalamount", itemTotalAmount); // 商品总金额
                billInfo.set("yd_businesstype", businessType); // 业务类型（internal_deal：内部交易；internal_deal_return：内部交易退；sale_return_internal_deal：销售退转内部交易；credit_control_pre：信控前置；credit_control_rear：信控后置；）
                billInfo.set("yd_sourcetradenumber", sourceTradeNumber); // 内部交易源头单号
                
                // 新增cr 2022-09-20，省市区联系人
                billInfo.set("yd_office", office); // 办事处
                billInfo.set("yd_province", province); // 省份
                billInfo.set("yd_city", city); // 城市
                billInfo.set("yd_district", district); // 地区
                billInfo.set("yd_linkman", linkman); // 联系人
                billInfo.set("yd_phone", phone); // 联系方式

                // cr 2022-09-26，新增地址
                billInfo.set("yd_address", address); // 地址

                // cr 2022-9-29，新增摘要
                billInfo.set("yd_description2", description); // 摘要

                // ---------- 标准字段的字段设置 ----------------
                billInfo.set("billstatus", "C"); // 审批状态
                billInfo.set("creator", currentUserInfo); // 创建人
                billInfo.set("createtime", currentDate); // 创建时间
                billInfo.set("modifier", currentUserInfo); // 修改人
                billInfo.set("modifytime", currentDate); // 修改时间
                billInfo.set("yd_purorder", purOrder); // update by hst 2023/03/15 麦优采购订单号
                billInfo.set("yd_ordertype",orderBusinessType); // update by hst 2023/04/14 单据类型

                //判断是否是一盘货 -hst 2022/11/1
                if (isSettle) {
                    //业务类型是后置并且销售组织不等于库存组织
                    if ("credit_control_rear".equals(businessType)) {
                        if (StringUtils.isNotBlank(organizationCode) && StringUtils.isNotBlank(saleOrganizationCode)
                                && !organizationCode.equals(saleOrganizationCode)) {
                            // update by hst 2023/03/21 增加标记类型判断
                            if (isOffline) {
                                billInfo.set("yd_isoffline", true);
                            } else {
                                billInfo.set("yd_isinventory", true);
                            }
                        }
                    }
                } else {
                    // TODO 待确定PCP接口区分是否一盘货方式
                }

                // update by hst 2023/02/15 增加校验，若存在采购订单号，则将该单据标记为虚单结算
                // update by hst 2023/04/14 增加单据类型校验
                if (StringUtils.isNotBlank(purOrder) && "baijian_direct_sales".equals(orderBusinessType)) {
                    billInfo.set("yd_isvirtual",true);
                }

                List<RequestMap> entryList = InterUtils.getEntry(billMap.get("details"), "单据明细", true);// 单据明细
                if (entryList == null || entryList.size() == 0) continue;

                // 遍历单据明细
                DynamicObjectCollection entryCol = billInfo.getDynamicObjectCollection("entryentity");
                for (RequestMap entryMap : entryList) {
                    String cargoCode = InterUtils.getString(entryMap.get("cargoCode"));    // 货品编码
                    String longCode = InterUtils.getString(entryMap.get("longCode"));    // 商品长编码
                    String cargoName = InterUtils.getString(entryMap.get("cargoName"));    // 货品名称
                    String batch = InterUtils.getString(entryMap.get("batch"));    // 批次号
                    String produceTime = InterUtils.getString(entryMap.get("produceTime"));    // 生产日期
                    String expireTime = InterUtils.getString(entryMap.get("expireTime"));    // 到期日期
                    BigDecimal quantity = InterUtils.getBigDecimal(entryMap.get("quantity"), "数量");    // 数量
                    BigDecimal taxPrice = InterUtils.getBigDecimal(entryMap.get("taxPrice"), "零售产品单价（含税）");    // 零售产品单价（含税）
                    String warehouseCode = InterUtils.getString(entryMap.get("warehouseCode"));    // 仓库编码
                    boolean ifGift = InterUtils.getBoolean(entryMap.get("ifGift"), "是否赠品", true);    // 是否赠品
                    boolean ifIntegral = InterUtils.getBoolean(entryMap.get("ifIntegral"), "是否积分产品", true);    // 是否积分产品
                    BigDecimal taxRate = InterUtils.getBigDecimal(entryMap.get("taxRate"), "税率");    // 税率
                    BigDecimal totalScore = InterUtils.getBigDecimal(entryMap.get("totalScore"), "使用积分");    // 使用积分
                    BigDecimal salePrice = InterUtils.getBigDecimal(entryMap.get("salePrice"), "供货单价（含税）");    // 供货单价（含税） cr 2022-08-28
                    BigDecimal actuallyAmount = InterUtils.getBigDecimal(entryMap.get("actuallyAmount"), "明细实付总额（含税）");    // 明细实付总额（含税） cr 2022-08-28
                    BigDecimal payableAmount = InterUtils.getBigDecimal(entryMap.get("payableAmount"), "明细应付总额（含税）");    // 明细应付总额（含税） cr 2022-08-28
                    BigDecimal discountAmount = InterUtils.getBigDecimal(entryMap.get("discountAmount"), "明细使用总折扣额");    // 明细使用总折扣额 cr 2022-08-28
                    BigDecimal transactionPrice = InterUtils.getBigDecimal(entryMap.get("transactionPrice"), "实收成本单价");    // 实收成本单价，2022-08-31新增
                    String sourceBillEntryId = InterUtils.getString(entryMap.get("sourceBillEntryId"));    // EAS销售订单分录ID，2022-09-18新增
                    String projectNo = InterUtils.getString(entryMap.get("projectNo"));    // 项目号，2022-11-1新增
                    String trackPlan = InterUtils.getString(entryMap.get("trackPlan"));    // 跟踪计划，2022-11-1新增
                    boolean ifOnline = InterUtils.getBoolean(entryMap.get("ifOnline"), "是否线上", true);    // 是否线上 2022-11-1新增
                    String orderTotalAmount = InterUtils.getString(entryMap.get("myBeforeDisAmt"));    // 应付金额(折前价税合计)，2023-12-04新增
                    String easPromotionActivityAmount = InterUtils.getString(entryMap.get("myPromotionDis"));    // 促销活动折扣，2023-12-04新增
                    String easSaleRebateAmount = InterUtils.getString(entryMap.get("mySaleReturnDis"));    // 销售返利折扣，2023-12-04新增

                    // 如果总折扣大于0，则折扣方式取“单位折扣额”
                    String discountType = null;
                    if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                        discountType = "B"; // A：折扣率，B：单位折扣额，NULL：无
                    }

                    DynamicObject entryInfo = entryCol.addNew(); // 新增分录
                    entryInfo.set("yd_materialnumber", cargoCode);    // 物料编码
                    entryInfo.set("yd_materialname", cargoName);    // 物料名称
                    entryInfo.set("yd_batchno", batch);    // 批号
                    entryInfo.set("yd_probegindate", StringUtils.isNoneBlank(produceTime)?DateTimeUtils.parse(produceTime):null);    // 生产日期
                    entryInfo.set("yd_proenddate", StringUtils.isNoneBlank(expireTime)?DateTimeUtils.parse(expireTime):null);    // 到期日
                    entryInfo.set("yd_measure", null);    // 计量单位
                    entryInfo.set("yd_quantity", quantity);    // 数量
                    entryInfo.set("yd_taxrate", taxRate);    // 税率
                    entryInfo.set("yd_taxprice", salePrice);    // 含税单价
                    entryInfo.set("yd_saleproprice", taxPrice);    // 零售产品单价
                    entryInfo.set("yd_transactionprice", transactionPrice);    // 实收成本单价
                    entryInfo.set("yd_amount", null);    // 价税合计
                    entryInfo.set("yd_discounttype", discountType);    // 折扣方式
                    entryInfo.set("yd_discountamount", discountAmount);    // 总折扣额
                    entryInfo.set("yd_actuallyamount", actuallyAmount);    // 明细实付总额（含税）
                    entryInfo.set("yd_payableamount", payableAmount);    // 明细应付总额（含税）
                    entryInfo.set("yd_warehouseNumber", warehouseCode);    // 仓库编码
                    entryInfo.set("yd_description_e", null);    // 备注
                    entryInfo.set("yd_ispresent", ifGift);    // 是否赠品
                    entryInfo.set("yd_isintegral", ifIntegral);    // 是否积分产品
                    entryInfo.set("yd_integral", totalScore);    // 使用积分
                    entryInfo.set("yd_subdivisionplatform", subDivisionPlatform);    // 电商渠道
                    entryInfo.set("yd_saledistribution", saleDistribution);    // 销售渠道
                    entryInfo.set("yd_easorderentryid", sourceBillEntryId);    // 销售出库单分录ID
                    entryInfo.set("yd_projectno", projectNo);    // 项目号 2022-11-1新增
                    entryInfo.set("yd_trackplan", trackPlan);    // 跟踪计划 2022-11-1新增
                    entryInfo.set("yd_ifonline", ifOnline);    // 是否线上 2022-11-1新增
                    entryInfo.set("yd_totalamount", orderTotalAmount);    // 应付金额(折前价税合计)，2023-12-04新增
                    entryInfo.set("yd_activityamount", easPromotionActivityAmount);    // 促销活动折扣，2023-12-04新增
                    entryInfo.set("yd_rebateamount", easSaleRebateAmount);    // 销售返利折扣，2023-12-04新增
                }

                BizHelper.saveDynamicObject(billInfo); // 保存单据信息
                isPass = true;
            }
        }
        return isPass;
    }

    /**
     * 获取PCP销售出库单、采购入库单
     * @param saleType 销售类型（SALE：销售单，SALE_RED：销售红单）
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @param billNo PCP出库结果单号
     * @param pageNum 分页页码
     * @param pageSize 分页大小
     * @return 返回执行结果
     * <AUTHOR>
     * @date 2022-6-14
     */
    public static R getPcpSaleOrPurchaseBill(String saleType, Date beginDate, Date endDate, String billNo, int pageNum, int pageSize) {
        // 对象不为空校验
        Objects.requireNonNull(saleType);
        Objects.requireNonNull(beginDate);
        Objects.requireNonNull(endDate);

        // 待获取接口的访问地址
        String interUrl = getInterConfigString("PCP_PULLSALEOUTURL");
        if (StringUtils.isBlank(interUrl)) throw new KDBizException("PCP销售出库单接口地址配置信息不存在，请检查参数配置表！");

        RequestMap requestMap = new RequestMap();
        requestMap.put("saleType", saleType)                      // 销售类型
                .put("createBeginTime", DateTimeUtils.format(beginDate))  // 开始范围时间
                .put("createEndTime", DateTimeUtils.format(endDate))      // 结束范围时间
                .put("pageNum", pageNum)                            // 分页页码
                .put("pageSize", pageSize);                         // 分页大小

        if (StringUtils.isNotBlank(billNo)) requestMap.put("documentNo", billNo); // PCP出库结果单号

        String reqJson = JsonUtils.toJsonString(requestMap); // 获取请求的Json数据

        // update by lzp, 增加接口签名处理
        Map<String, String> reqSignHeader = getPcpRequestParamSignHeader(reqJson);

        String respJson = HttpUtils.sendPost(interUrl, reqSignHeader, reqJson); // 相应的Json数据
        logger.info(String.format("PCP销售出库单调用推送报文：%s\nPCP销售出库单结果回传结果报文：%s", reqJson, respJson)); // 日志记录
        if (StringUtils.isBlank(respJson)) throw new KDBizException("接口异常，获取不了返回值，请联系PCP管理员！");
        Map<String, Object> responseMap = JsonUtils.toMap(respJson);// 将请求的数据转换成Map对象
        // 返回结果，为0标识表示处理成功，如果是非0则标识处理异常
        if (!"0".equals(responseMap.get("resultCode"))) {
            return R.error("读取接口数据出错，详情："+responseMap.get("resultMsg"));
        }else {
            // 获取相应的业务单据结果集
            return R.ok().put("data", RequestMap.to((Map<String, Object>) responseMap.get("data")));
        }
    }

    /**
     * 将获取未写回PCP销售结果的数据进行回写
     * @return R 返回执行的结果
     * <AUTHOR>
     * @date 2022-8-15
     */
    public static void pushSaleResultToPcp() {

        // 获取接口的回写地址
        String interUrl = getInterConfigString("PCP_PUSHSALEOUTRESULTURL");
        if (StringUtils.isBlank(interUrl)) throw new KDBizException("PCP销售出库单接口地址配置信息不存在，请检查参数配置表！");

        // 遍历结果
        Date lastMonth = DateTimeUtils.addMonth(new Date(), -1);
        String lastMonthStr = DateTimeUtils.format(lastMonth, DateTimeUtils.SDF_DATE);
        int maxTop = 100; // 控制单次推送的量大小
        // 单据已经审批、已经生成下游销售出库单、没有回写业务系统、一个月内的单据
        QFilter qFilter = QFilter.of("billstatus='C' and yd_ishasnext = 1 and yd_isreturnresult = 0 and createtime > ?", lastMonthStr);
        DynamicObject[] col = BizHelper.getDynamicObject(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, "billno, yd_saleordernumber, yd_salebillnumber", qFilter.toArray(), null, maxTop);
        RequestMap[] requestMaps = new RequestMap[col.length];
        int index = 0;
        // 遍历获取的数据对数据进行组装
        for (DynamicObject info : col) {
            String documentNo = info.getString("billno"); // 单据编码
//            String saleOrderNo = info.getString("yd_saleordernumber"); // 单据编码
            String saleOutNo = info.getString("yd_salebillnumber"); // 销售出库单单号
            RequestMap requestMap = new RequestMap();
            requestMap.put("documentNo", documentNo) // PCP销售出库单单号
                    .put("ifPullFinance", 1)  // 是否推送财务中台
                    .put("easOrderNo", saleOutNo);  // EAS销售出库单号
            requestMaps[index++] = requestMap;
        }

        String reqJson = JsonUtils.toJsonString(requestMaps); // 获取请求的Json数据

        // update by lzp, 增加接口签名处理
        Map<String, String> reqSignHeader = getPcpRequestParamSignHeader(reqJson);

        String respJson = HttpUtils.sendPost(interUrl, reqSignHeader, reqJson); // 相应的Json数据
        logger.info(String.format("PCP销售出库单调用推送报文：%s\nPCP销售出库单结果回传结果报文：%s", reqJson, respJson)); // 日志记录
        if (StringUtils.isBlank(reqJson)) throw new KDBizException("接口响应出错，返回结果为null");

        Map<String, Object> responseMap = JsonUtils.toMap(respJson);// 将相应的数据转换成Map对象
        if (InterUtils.getInt(responseMap.get("resultCode")) != 0) {
            throw new KDBizException((String) responseMap.get("resultMsg"));
        }
        // 获取相应的结果数据
        List<Map<String, Object>> respData = (List<Map<String, Object>>) responseMap.get("data");
        if (respData == null || respData.size() == 0) return;
        // 遍历结果集，将结果进行反写
        DynamicObject[] sInfoArr = respData.stream().map(item -> {
            boolean isSuccess = InterUtils.getInt(item.get("resultCode")) == 0;
            String resultMsg = InterUtils.getString(item.get("resultMsg"));
            String documentNo = InterUtils.getString(item.get("documentNo"));
            if (StringUtils.isNoneBlank(documentNo)) {
                DynamicObject info = BizHelper.getSingleDynamicObject(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, "yd_isreturnresult,yd_returnmsg", new QFilter("billno", "=", documentNo).toArray());
                if (info != null) {
                    info.set("yd_isreturnresult", isSuccess); // 更新已回写
                    info.set("yd_returnmsg", resultMsg); // 获取回写结果
                    return info;
                }
                return null;
            }
            return null;
        }).filter(Objects::nonNull).toArray(DynamicObject[]::new);
        // 批量更新
        if (sInfoArr.length > 0) {
            BizHelper.saveDynamicObject(sInfoArr);
        }
    }

    /**
     * 获取PCP调拨单
     * @return 返回是否获取数据
     * <AUTHOR>
     * @date 2022-7-18
     */
    @Deprecated
    public static boolean pullPcpTransBill() {

        // TODO 接口逻辑待完善


        return false;
    }

    /**
     * 获取PCP请求参数的加签请求头信息
     * @param reqJson 请求的报文信息
     * @return 返回加签的请求头信息
     * <AUTHOR>
     * @date 2022-8-24
     */
    private static Map<String, String> getPcpRequestParamSignHeader(String reqJson) {
        // 通过配置的方式设置
        String pcpSignConfig = getInterConfigString("pcpSignConfig");
        if (StringUtils.isBlank(pcpSignConfig)) throw new KDBizException("PCP的加签配置信息不能为空，请检查配置文件！");
        String[] pcpSignConfigArr = pcpSignConfig.split(";");
        if (pcpSignConfigArr.length != 2) throw new KDBizException("PCP的加签配置信息有误，配置的格式应为：应用身份标记;应用秘钥");
        String applicationKey = pcpSignConfigArr[0]; // 应用身份标记
        String applicationSecret = pcpSignConfigArr[1]; // 应用秘钥
        // 生成签名
        String nonce = UUID.randomUUID().toString();
        String currentTimestamp = String.valueOf(System.currentTimeMillis());
        // 对字串进行排序
        RequestMap signReqMap = new RequestMap();
        signReqMap.put("X-Yx-Timestamp", currentTimestamp);
        signReqMap.put("X-Nonce", nonce);
        signReqMap.put("Application-Key", applicationKey);
        signReqMap.put("Application-Secret", applicationSecret);
        if (StringUtils.isNoneBlank(reqJson)) {
            signReqMap.put("md5", DigestUtils.md5Hex(reqJson).substring(8, 24)); // 对请求的报文做16位加密方式
        }
        StringBuilder signBuilder = new StringBuilder();
        signReqMap.forEach((key, value) -> signBuilder.append(key).append("=").append(value).append("&"));
        // 签名
        String sign = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, applicationSecret).hmacHex(signBuilder.substring(0, signBuilder.length() - 1));
        // 封装公共参数
        Map<String, String> headerReqMap = new HashMap<>();
        headerReqMap.put("Application-Key", applicationKey);
        headerReqMap.put("X-Yx-Nonce", nonce);
        headerReqMap.put("X-Yx-Timestamp", currentTimestamp);
        headerReqMap.put("X-Yx-Signature", sign);
        return headerReqMap;
    }

    /**
     * 通过key值获取到配置基础资料中的值
     * @param key 配置表中的键
     * @return key 配置表中对应的value 也就是name
     * <AUTHOR>
     * @date 2022-8-24
     */
    public static String getInterConfigString(String key) {
        DynamicObject confInfo = BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_INTERFACECONFIG, "name", QFilter.of("status='C' and number = ?", key).toArray());
        if (confInfo != null) return confInfo.getString("name");
        return null;
    }
}
