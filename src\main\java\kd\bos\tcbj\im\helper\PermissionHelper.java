package kd.bos.tcbj.im.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.DispatchService;
import kd.bos.service.lookup.ServiceLookup;

import java.util.HashSet;
import java.util.Set;

/**
 * 权限辅助类
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-4-24
 */
public class PermissionHelper {

    /**
     * 获取数据权限过滤条件
     * @param appId 应用ID
     * @param entityNum 实体编码
     * @param userId 用户ID
     * @return 权限的过滤条件
     * <AUTHOR>
     * @date 2022-4-24
     */
    public static QFilter getDataPermission(String appId, String entityNum, Long userId) {
        //第二个参数是基础服务的应用标识
        DispatchService service = ServiceLookup.lookup(DispatchService.class, "base");
        return (QFilter)service.invoke("kd.bos.service.ServiceFactory", "PermissionService", "getDataPermission", userId, appId, entityNum);
    }

    /**
     * 获取数据权限过滤条件
     * @param appId 应用ID
     * @param entityNum 实体编码
     * @param userId 用户ID
     * @return 权限的过滤条件
     * <AUTHOR>
     * @date 2022-4-24
     */
    public static QFilter getDataPermissionTransId(String appId, String entityNum, Long userId) {
        //第二个参数是基础服务的应用标识
        QFilter filter = getDataPermission(appId, entityNum, userId);
        if (filter == null) return null;
        DataSet dataSet = BizHelper.getQueryDataSet(PermissionHelper.class.getName(), entityNum, "id", filter.toArray());
        if (!dataSet.hasNext()) return null;

        // 获取结果集合
        Set<Long> idSet = new HashSet<>();
        for (Row row : dataSet) {
            idSet.add(row.getLong("id"));
        }
        return new QFilter("id", QCP.in, idSet);
    }
}
