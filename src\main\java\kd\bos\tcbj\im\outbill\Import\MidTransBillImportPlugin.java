package kd.bos.tcbj.im.outbill.Import;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.ImportLogger;
import kd.bos.form.plugin.impt.BatchImportPlugin;
import kd.bos.form.plugin.impt.ImportBillData;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.ORMUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.im.outbill.Import.MidTransBillImportPlugin
 * @className MidTransBillImportPlugin
 * @author: hst
 * @createDate: 2024/01/17
 * @description: 库存调拨单中间表引入插件
 * @version: v1.0
 */
public class MidTransBillImportPlugin extends BatchImportPlugin {

    /**
     * 重写保存逻辑，对数据进行处理再保存
     * @param rowdatas
     * @param logger
     * @return
     * @author: hst
     * @createDate: 2024/01/17
     */
    @Override
    protected ApiResult save(List<ImportBillData> rowdatas, ImportLogger logger) {
        // 匹配物料批次信息
        this.getMaterialLotInfo(rowdatas);
        return super.save(rowdatas, logger);
    }

    /**
     * 匹配物料批次信息
     * @param rowdatas 引入的数据
     * @author: hst
     * @createDate: 2024/01/17
     */
    private void getMaterialLotInfo (List<ImportBillData> rowdatas) {
        for (ImportBillData rowdata : rowdatas) {
            JSONObject bill = rowdata.getData();
            // 获取分录数据
            if (bill.containsKey("entryentity")) {
                JSONArray enties = bill.getJSONArray("entryentity");
                for (Object entry : enties) {
                    JSONObject entryData = (JSONObject) entry;
                    // 批次号
                    String lot = entryData.containsKey("yd_batchno")
                            ? entryData.getString("yd_batchno") : null;
                    // 物料编码
                    String materialNum = entryData.containsKey("yd_intermaterial")
                            && Objects.nonNull(entryData.getString("yd_intermaterial"))
                            ? entryData.getString("yd_intermaterial") : entryData.containsKey("yd_material")
                            ? ((JSONObject) entryData.get("yd_material")).getString("number") : "";
                    // 仓库
                    String warehouse = bill.containsKey("yd_interoutwarehouse")
                            && Objects.nonNull(bill.getString("yd_interoutwarehouse"))
                            ? bill.getString("yd_interoutwarehouse") : bill.containsKey("yd_outwarehouse")
                            ? ((JSONObject) bill.get("yd_outwarehouse")).getString("number") : "";
                    // 生产日期
                    String beginDate = entryData.containsKey("yd_probegindate")
                            ? entryData.getString("yd_probegindate") : "";
                    // 到期日
                    String endDate = entryData.containsKey("yd_proenddate")
                            ? entryData.getString("yd_proenddate") : "";

                    if (StringUtils.isNotBlank(materialNum) && StringUtils.isNotBlank(warehouse)) {
                        if (Objects.isNull(lot) || (StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate))) {
                            Object[] matLogInfo = this.getMaterialLotInfo(lot, materialNum, warehouse, true);
                            if (matLogInfo != null) {
                                entryData.put("yd_batchno", matLogInfo[0]);
                                entryData.put("yd_probegindate", matLogInfo[1]);
                                entryData.put("yd_proenddate", matLogInfo[2]);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置批次对应的生产日期和到期日
     * @param lot
     * @param matNum
     * @param wsNum
     * @return
     * @author: hst
     * @createDate: 2024/01/17
     */
    public static Object[] getMaterialLotInfo(String lot, String matNum, String wsNum, Boolean isReturn) {
        QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
        filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
        if (StringUtils.isNotBlank(lot)) {
            filter.and(new QFilter("yd_flot", QCP.equals, lot));
        }
        // 如果是正向的需要查数量大于0的批次
        if (!isReturn) {
            filter.and(new QFilter("yd_qty", QCP.large_than, 0));
        }
        DataSet dataSet = null;
        try {
            dataSet = BizHelper.getQueryDataSet("inv", "yd_easinventory", "yd_flot,yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
            if (dataSet.hasNext()) {
                Row row = dataSet.next();
                return new Object[] {row.getString("yd_flot"), row.getDate("yd_mfgdate"), row.getDate("yd_expdate")};
            }
            return null;
        }finally {
            ORMUtils.close(dataSet);
        }
    }
}
