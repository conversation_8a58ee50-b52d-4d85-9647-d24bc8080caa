package kd.bos.tcbj.im.outbill.constants;

/**
 * 结算配置常量类
 * @package: kd.bos.tcbj.im.outbill.constants.SettleParamConstant
 * @className: SettleParamConstant
 * @author: hst
 * @createDate: 2024/05/26
 * @version: v1.0
 */
public class SettleParamConstant {
    // 单据标识
    public static String MAIN_ENTITY = "yd_settleconfig";
    // 类型
    public static String TYPE_FIELD = "yd_type";
    // 结算路径获取类名
    public static String CLASSNAME_FIELD = "yd_classname";
    // 结算路径获取方法名
    public static String METHOD_FIELD = "yd_method";
    // 退货路径获取方法名
    public static String RETURNMETHOD_FIELD = "yd_return_method";
    // 源单校验类名
    public static String VAILCLASS_FIELD = "yd_vailclass";
    // 源单校验方法名
    public static String VAILMETHOD_FIELD = "yd_vailmethod";
    // 生成结算单据类名
    public static String SETTLECLASS_FIELD = "yd_settleclass";
    // 生成结算单据方法名
    public static String SETTLEMETHOD_FIELD = "yd_settlemethod";
    // 生成直接调拨类名
    public static String TRANSCLASS_FIELD = "yd_transclass";
    // 生成直接调拨方法名
    public static String TRANSMETHOD_FIELD = "yd_transmethod";
    // 内部交易关系自定义校验类名
    public static String CUSCLASS_FIELD = "yd_cusclass";
    // 内部交易关系自定义校验方法名
    public static String CUSMETHOD_FIELD = "yd_cusmethod";
    // 库存中间表生成调拨单转换规则标识
    public static String TODIRBILL_FIELD = "yd_todirbill";
    // 源单to入库单转换规则标识（退货）
    public static String ORIOUTTOIN_FIELD = "yd_oriouttoin_re";
    // 入库单to出库单转换规则标识（正向）
    public static String INTOOUT_FIELD = "yd_intoout";
    // 入库单to出库单转换规则标识（退货）
    public static String INTOOUT_RE_FIELD = "yd_intoout_re";
    // 出库单to入库单转换规则标识（正向）
    public static String OUTTOIN_FIELD = "yd_outtoin";
    // 接口结算调度执行用户ID
    public static String USERID_FIELD = "yd_userid";
}
