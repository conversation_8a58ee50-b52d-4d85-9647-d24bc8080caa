package kd.bos.tcbj.im.outbill.helper;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import kd.bos.exception.KDBizException;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import org.apache.commons.lang3.StringUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.botp.ConvertDataService;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.botp.runtime.BFRow;
import kd.bos.entity.botp.runtime.BFRowId;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * 内部结算工具类
 * <AUTHOR>
 * @date 2021-12-27
 *
 */
public class InternalSettleServiceHelper {
	
	private final static SimpleDateFormat DATE_SDF =new SimpleDateFormat("yyyy-MM-dd");
	
	/**
	 * 校验末级销售出库单对应的内部交易关系是否有已维护
	 * @param saleoutBillId 末级销售出库单
	 * @param checkType 校验类型（1为一盘货结算校验、2为直营店结算校验、3为PCP一盘货结算校验（无需客户)、4为PCP开门红结算校验,5为成品仓一盘货）
	 * @return billId为空表示失败，errorMsg表示失败原因
	 */
	public static Map<String, String> checkInternalMsg(String saleoutBillId, String checkType) {
		Map<String,String> internalResult = new HashMap<String,String>();
		StringBuffer errorMsg = new StringBuffer();
		// 源末级销售出库单
		DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
		// 最终的销售收货的客户
		DynamicObject finalCustomer = saleoutBillObj.getDynamicObject("customer");
		String oriCustomerId = finalCustomer.getPkValue().toString();  // 原汇总生成的客户ID

		DataSet settleDataSet = null;
		// 调整，无论是直营店结算还是一盘货结算都是采用内部组织交易关系表进行获取结算路径
		// 获取第一行分录的品牌
		DynamicObject oneEntryObj = saleoutBillObj.getDynamicObjectCollection("billentry").get(0);
		DynamicObject brandObj = oneEntryObj.getDynamicObject("yd_basedata_pinpai");
		String brandNo = brandObj.getString("number");  // 品牌编码
		// 获取品牌对应的内部结算关系以及最后一级的组织，从上到下
		// update by hst -2022/10/31 PCP一盘货结算不校验销售组织与客户
		if (StringUtils.equals(checkType, "1") || StringUtils.equals(checkType, "3")) {
			// 经销商一盘货结算路径查询
			QFilter brandFilter = new QFilter("yd_brand.number", QCP.equals, brandNo);
			brandFilter.and(new QFilter("yd_oricustomer.id", QCP.equals, oriCustomerId));
			brandFilter.and(new QFilter("billstatus", QCP.equals, "C"));
			settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_innerrelbill",
					"yd_newcustomer.id newCusId,entryentity.yd_orglevel orglevel,entryentity.yd_org.id orgid,entryentity.yd_org.number orgNum,entryentity.yd_org.name orgName, yd_isoutsettle isOutSettle",
					brandFilter.toArray(), "entryentity.seq asc");
		}else if (StringUtils.equals(checkType, "2")) {
			// 直营店结算查询
			QFilter brandFilter = new QFilter("yd_brand.number", QCP.equals, brandNo);
			brandFilter.and(new QFilter("yd_cusentry.yd_customer.id", QCP.equals, oriCustomerId));
			brandFilter.and(new QFilter("status", QCP.equals, "C"));
			settleDataSet = QueryServiceHelper.queryDataSet("jy", BillTypeHelper.BILLTYPE_DIRECTSETTLERELA,
					"yd_cusentry.yd_customer.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid,yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName, yd_isoutsettle isOutSettle",
					brandFilter.toArray(), "yd_levelentry.seq asc");
		} else if (StringUtils.equals(checkType, "4")) {
			// update by hst 2022/11/14 PCP开门红结算查询
			QFilter brandFilter = new QFilter("yd_brand.number", QCP.equals, brandNo);
			brandFilter.and(new QFilter("yd_settleentry.yd_settleorg.id", QCP.equals,
					saleoutBillObj.getDynamicObject("yd_settleorg").getPkValue().toString()));
			brandFilter.and(new QFilter("status", QCP.equals, "C"));
			settleDataSet = QueryServiceHelper.queryDataSet("jy", BillTypeHelper.BILLTYPE_DIRECTSETTLERELA,
					"yd_settleentry.yd_settleorg.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid,yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName, yd_isoutsettle isOutSettle",
					brandFilter.toArray(), "yd_levelentry.seq asc");
		} else if (StringUtils.equals(checkType, "5")) {
			// update by hst 2023/04/14 成品仓一盘货结算
			QFilter brandFilter = new QFilter("yd_customentity.yd_custom.id", QCP.equals, oriCustomerId);
			brandFilter.and(new QFilter("yd_canalorg.id", QCP.equals,
					saleoutBillObj.getDynamicObject("bizorg").getPkValue().toString()));
			brandFilter.and(new QFilter("status", QCP.equals, "C"));
			settleDataSet = QueryServiceHelper.queryDataSet("jy", BillTypeHelper.BILLTYPE_WARESETTLERELA,
					"yd_customentity.yd_custom.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid,yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName, '0' isOutSettle",
					brandFilter.toArray(), "yd_levelentry.seq asc");
		}

		LinkedList<String> orgRelList = new LinkedList<String>();
		String outOrgId = "";  // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
		String finalCustomId = "";
		// 校验组织与仓库映射表
		Map<String, String> orgWarehouseMap = getOrgWarehouseMap();
		String warehouseError = "以下销售出库组织缺少默认仓库映射：";
		StringBuffer warehouseErrorMsg = new StringBuffer();
		LinkedList<String> orgList = new LinkedList<String>();
		boolean isOutSettle = false; // 是否委外品牌结算
		for (Row settleRow : settleDataSet) {
			String curOrgId = settleRow.getString("orgid");
			String orgLevel = settleRow.getString("orglevel");
			isOutSettle = settleRow.getBoolean("isOutSettle");

			orgList.add(curOrgId);
			if (StringUtils.isNotEmpty(outOrgId)) {
				String mapValue = outOrgId+"&"+curOrgId;
				orgRelList.add(mapValue);
			}
			outOrgId = curOrgId;
			finalCustomId = settleRow.getString("newCusId");
			// 一级股份不用校验仓库
			if ((!"1".equals(orgLevel)) && !orgWarehouseMap.containsKey(curOrgId)) {
				warehouseErrorMsg.append(settleRow.getString("orgNum")+settleRow.getString("orgName")+"，");
			}
		}
		if (warehouseErrorMsg.length() > 0) {
			warehouseErrorMsg.insert(0, warehouseError);
			warehouseErrorMsg.append("；");
		}
		errorMsg.append(warehouseErrorMsg);

		DynamicObjectCollection entryCol = saleoutBillObj.getDynamicObjectCollection("billentry");

		// cr by laizp, time：2022-08-31，判断委外品牌结算是否存在对应的基础配置
		// 用来存储委外仓库映射的值
		Map<String, String> outWarehouseMap = new HashMap<>();
		if (isOutSettle) {
			for (DynamicObject entryObj : entryCol) {
				String wsNum = entryObj.getDynamicObject("warehouse").getString("number");
				// 判断是否存在 共享仓+品牌+客户的仓库映射表
				String actWarehouseNumber = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.number", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
				if (StringUtils.isBlank(actWarehouseNumber)) {
					errorMsg.append(String.format("委外结算缺少（%s、%s、%s）委外品牌仓库映射表！", wsNum, brandNo, finalCustomer.getString("number")));
				}else {
					outWarehouseMap.put(wsNum+";"+brandNo, actWarehouseNumber);
				}
				// 需要清空生产日期和结束日期，便于检查新的库存信息
				entryObj.set("yd_scrq", null);
				entryObj.set("yd_dqr", null);
			}
		}

		// 如果参数为true表示校验生产日期，为false表示不校验
		DynamicObject biztype = saleoutBillObj.getDynamicObject("biztype");
		boolean isControlLot = true; // 默认校验
		boolean isGeneralDate = true; // 默认为true，自动带日期即不要校验日期，退货类型的如果开通了此参数则不用校验批次
		// 从配置表中查找对应的值
		String isControlLotStr = BizHelper.getQueryOne("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isControlLot").toArray());
		String isGeneralDateStr = BizHelper.getQueryOne("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
		if (StringUtils.isNotBlank(isControlLotStr)) {
			isControlLot = StringUtils.equalsIgnoreCase("true", isControlLotStr);
		}
		if (StringUtils.isNotBlank(isGeneralDateStr)) {
//			isGeneralDate = "2101".equals(biztype.getString("number")) && StringUtils.equalsIgnoreCase("true", isGeneralDateStr);
			isGeneralDate = StringUtils.equalsIgnoreCase("true", isGeneralDateStr);
		}
		
		Boolean isReturn = "2101".equals(biztype.getString("number"));  // 是否退货

		if (isControlLot) {
			StringBuffer lotErrorD = new StringBuffer();
			for (DynamicObject entryObj : entryCol) {
				// 校验是否存在对应的批次数据
				String lot = entryObj.getString("yd_ph");
//				lot = lot.trim().toUpperCase().replaceAll(" ", "");
				String materialNum = entryObj.getString("material.masterid.number");
				String wsNum = entryObj.getDynamicObject("warehouse").getString("number");
				Date beginDate = entryObj.getDate("yd_scrq");
				Date endDate = entryObj.getDate("yd_dqr");

				// cr by laizp, time：2022-08-31，如果是委外查找库存，则仓库按实际仓库查找
				if (isOutSettle) wsNum = outWarehouseMap.get(wsNum+";"+brandNo);
				if (wsNum == null) continue; // 如果不存在，只有委外中查找映射中不存在，这种情况异常已经记录，无须再次查找

				// 如果生产的日期为空，都需要从即时库存中查找
				if (beginDate==null || endDate == null) {
					// 按批次+物料+仓库，如果没有查找到，正向执行提示异常，如果是红字，则需要再次查找按物料+仓库查找，没有则提示异常
					boolean isHasLot = hasExpDate(lot, materialNum, wsNum, isReturn);
					if (!isHasLot) {
						if (isGeneralDate) {
							boolean isHasRefundLot = hasLastExpDate(materialNum, wsNum, isReturn);
							if (!isHasRefundLot) {
								if (lotErrorD.indexOf(materialNum+"的批次"+lot) < 0) lotErrorD.append("仓库编码"+wsNum+"的"+materialNum+"的批次"+lot+"，");
							}
						}else {
							if (lotErrorD.indexOf(materialNum+"的批次"+lot) < 0) lotErrorD.append("仓库编码"+wsNum+"的"+materialNum+"的批次"+lot+"，");
						}
					}
				}
			}
			if (lotErrorD.length() > 0) lotErrorD.insert(0, "以下物料对应的批次不存在即时库存数据：").append("；");
			// 添加的异常信息中
			errorMsg.append(lotErrorD);
		}
		
		// 按品牌获取最终的客户
		// update by hst -2022/11/14 PCP开门红是根据结算组织去找路径，所以路径中不会返回客户id
		if (!StringUtils.equals(checkType, "4")) {
			finalCustomer = BusinessDataServiceHelper.loadSingle(finalCustomId, "bd_customer");
		}
		
		// 校验组织是否维护内部客商，一级组织校验供应商，二级组织校验客户和供应商，末级组织校验客户
		String internalOrgError = "以下组织缺少设置对应的内部客商：";
		StringBuffer internalOrgErrorMsg = new StringBuffer();
		// 校验是否有维护组织间价格
		for (String orgRelMap : orgRelList) {
			String[] keys = orgRelMap.split("&");
			String srcOrg = keys[0];
			DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
			String srcOrgName = srcOrgObj.getString("name");
			String desOrg = keys[1];
			DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");
			String desOrgName = desOrgObj.getString("name");
			
			String orgPriceError = "销售出库组织（"+srcOrgName+"）与采购入库组织（"+desOrgName+"）缺失以下物料的价格：";
			StringBuffer matPriceError = new StringBuffer();
			// 获取组织内部各个物料新的结算价格集合
			// 20220419yzw调整，根据单据业务日期获取对应日期内的价格
			Date bizDate = saleoutBillObj.getDate("biztime");
			Map<String, BigDecimal> matPriceMap = getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);
			for (DynamicObject entryObj : entryCol) {
				String materialId = entryObj.getString("material.masterid.id");
				String materialNum = entryObj.getString("material.masterid.number");
				// 校验客户交易价格
				if (!matPriceMap.containsKey(materialId) && matPriceError.indexOf(materialNum)<0) {
					matPriceError.append(materialNum+"，");
				}
			}
			if (matPriceError.length() > 0) {
				matPriceError.insert(0, orgPriceError);
				matPriceError.append("；");
			}
			
			// 根据编码获取后者组织作为客户
			Boolean isCustomer = checkInternalCP("bd_customer", desOrgObj.getString("number"));
			if (!isCustomer) {
				internalOrgErrorMsg.append(desOrgObj.getString("number")+desOrgObj.getString("name")+"缺少对应客户，");
			}
			Boolean isSupplier = checkInternalCP("bd_supplier", srcOrgObj.getString("number"));
			if (!isSupplier) {
				internalOrgErrorMsg.append(srcOrgObj.getString("number")+srcOrgObj.getString("name")+"缺少对应供应商，");
			}
			if (internalOrgErrorMsg.length() > 0) {
				internalOrgErrorMsg.insert(0, internalOrgError);
				internalOrgErrorMsg.append("；");
			}
			errorMsg.append(matPriceError);
			errorMsg.append(internalOrgErrorMsg);
		}

		if ("1".equals(checkType)) {
			// 校验客户价格是否满足条件
			// 20220419yzw调整，根据单据业务日期获取对应日期内的价格
			Date bizDate = saleoutBillObj.getDate("biztime");
			Map<String, BigDecimal> orgtoCusNewPriceMap = getOrgtoCusNewPrice(outOrgId, finalCustomer.getPkValue().toString(), bizDate);
			DynamicObject finalOrg = BusinessDataServiceHelper.loadSingle(outOrgId, "bos_org");
			String cusPriceError = "销售出库组织（" + finalOrg.getString("name") + "）与客户（" + finalCustomer.getString("name") + "）缺失以下物料的价格：";
			StringBuffer cusMatPriceError = new StringBuffer();
			for (DynamicObject entryObj : entryCol) {
				String materialId = entryObj.getString("material.masterid.id");
				String materialNum = entryObj.getString("material.masterid.number");
				// 校验客户交易价格
				if (!orgtoCusNewPriceMap.containsKey(materialId) && cusMatPriceError.indexOf(materialNum) < 0) {
					cusMatPriceError.append(materialNum + ",");
				}
			}
			if (cusMatPriceError.length() > 0) {
				cusMatPriceError.insert(0, cusPriceError);
				cusMatPriceError.append("；");
			}
			errorMsg.append(cusMatPriceError);
		}
		
		if (errorMsg.length() > 0) {
			internalResult.put("billId", "");
			internalResult.put("errorMsg", errorMsg.toString());
		} else {
			internalResult.put("billId", saleoutBillId);
			internalResult.put("errorMsg", "");
		}
		
		return internalResult;
	}
	
	/**
	 * 获取组织与仓库的默认对应关系
	 */
	public static Map<String, String> getOrgWarehouseMap() {
		Map<String, String> orgWarehouseMap = new HashMap<String, String>();
		QFilter wsFilter = new QFilter("billstatus", QCP.equals, "C");
		// update by hst 20230915 排除股份仓
		DataSet orgWsSet = QueryServiceHelper.queryDataSet("ws", "yd_orgwarehouse", 
				"entryentity.yd_org.id orgId,entryentity.yd_org.number orgNum," +
						"entryentity.yd_warehouse.id wsId", wsFilter.toArray(), null);
		for(Row row : orgWsSet) {
			String orgNum = row.getString("orgNum");
			if (StringUtils.isNotBlank(orgNum) && !"000002".equals(orgNum)) {
				orgWarehouseMap.put(row.getString("orgId"), row.getString("wsId"));
			}
		}
		return orgWarehouseMap;
	}
	
	/**
	 * 根据调出调入组织获取各个物料最新的价格
	 * @param srcOrg
	 * @param desOrg
	 * @param bizDate 出库单业务日期，作为过滤条件查对应日期范围内的价格，yzw20220419
	 */
	public static Map<String,BigDecimal> getOrgtoOrgNewPrice(String srcOrg, String desOrg, Date bizDate) {
		Map<String,BigDecimal> matNewPriceMap = new HashMap<String,BigDecimal>();
		QFilter priceFilter = new QFilter("yd_outorg.id", QCP.equals, srcOrg);
		priceFilter.and(new QFilter("yd_inorg.id", QCP.equals, desOrg));
		priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
		String bizDateStr = DATE_SDF.format(bizDate);
		try {
			bizDate = DATE_SDF.parse(bizDateStr);
		} catch (ParseException e) {
			System.out.println(e.getLocalizedMessage());
		}
		priceFilter.and(new QFilter("entryentity.yd_begindate", QCP.less_equals, bizDate));  // 开始日期小于等于业务日期
		priceFilter.and(new QFilter("entryentity.yd_enddate", QCP.large_equals, bizDate));  // 结束日期大于等于业务日期
		DataSet priceSet = QueryServiceHelper.queryDataSet("price", "yd_pricerelbill", 
				"entryentity.yd_materiel.id matId,entryentity.yd_price newprice", priceFilter.toArray(), null);
		for(Row row : priceSet) {
			matNewPriceMap.put(row.getString("matId"), row.getBigDecimal("newprice"));
		}
		return matNewPriceMap;
	}
	
	/**
	 * 根据销售组织与客户关系获取各个物料最新的销售价格
	 * @param customer
	 * @param bizDate 出库单业务日期，作为过滤条件查对应日期范围内的价格，yzw20220419
	 */
	public static Map<String,BigDecimal> getOrgtoCusNewPrice(String srcOrg, String customer, Date bizDate) {
		Map<String,BigDecimal> matNewPriceMap = new HashMap<String,BigDecimal>();
		QFilter priceFilter = new QFilter("yd_org.id", QCP.equals, srcOrg);
		priceFilter.and(new QFilter("yd_customer.id", QCP.equals, customer));
		priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
		String bizDateStr = DATE_SDF.format(bizDate);
		try {
			bizDate = DATE_SDF.parse(bizDateStr);
		} catch (ParseException e) {
			System.out.println(e.getLocalizedMessage());
		}
		priceFilter.and(new QFilter("entryentity.yd_begindate", QCP.less_equals, bizDate));  // 开始日期小于等于业务日期
		priceFilter.and(new QFilter("entryentity.yd_enddate", QCP.large_equals, bizDate));  // 结束日期大于等于业务日期
		DataSet priceSet = QueryServiceHelper.queryDataSet("price", "yd_orgcuspricebill", 
				"entryentity.yd_materiel.id matId,entryentity.yd_saleprice newprice", priceFilter.toArray(), null);
		for(Row row : priceSet) {
			matNewPriceMap.put(row.getString("matId"), row.getBigDecimal("newprice"));
		}
		return matNewPriceMap;
	}

	/**
	 * 校验组织是否有对应的内部客户
	 * @param entity
	 * @param number
	 * @return
	 */
	public static Boolean checkInternalCP(String entity, String number) {
		QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, number);
		cusFilter.and(new QFilter("status", QCP.equals, "C"));
		DynamicObject[] customers = BusinessDataServiceHelper.load(entity, "id,name,number", cusFilter.toArray());
		if (customers.length > 0) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * 设置批次对应的生产日期和到期日
	 * @param lot
	 * @param matNum
	 * @param wsNum
	 * @return
	 */
	private static Boolean hasExpDate(String lot, String matNum, String wsNum, Boolean isReturn) {
		QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
		filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
		filter.and(new QFilter("yd_flot", QCP.equals, lot));
		// 如果是正向的需要查数量大于0的批次
		if (!isReturn) {
			filter.and(new QFilter("yd_qty", QCP.large_than, 0));
		}
		DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory", 
				"yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
		if (dataSet.hasNext()) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * 设置物料+仓库获取对应最新的批次
	 * @param matNum
	 * @param wsNum
	 * @return
	 */
	private static Boolean hasLastExpDate(String matNum, String wsNum, Boolean isReturn) {
		QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
		filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
		// 如果是正向的需要查数量大于0的批次
		if (!isReturn) {
			filter.and(new QFilter("yd_qty", QCP.large_than, 0));
		}
		DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory", 
				"yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
		if (dataSet.hasNext()) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * 根据关联关系保存下游单据，保存前先清理
	 * @param saleoutBillId 末级销售出库单ID
	 */
	public static void saveBotpBills(String saleoutBillId) {
		DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
		DynamicObjectCollection internalCol = saleoutBillObj.getDynamicObjectCollection("yd_internalentity");
		internalCol.clear();
		
		DynamicObjectType entryType = internalCol.getDynamicObjectType();
		
		ConvertDataService dataService = new ConvertDataService();
		String srcBillEntity = "im_saloutbill";
		String tempId = saleoutBillId;
		int seq = 1;
		while(StringUtils.isNotEmpty(tempId)) {
			// 遍历单据，获取下游单据数据
			Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
			if (dirTargetBill.size() == 1) {
				Long tempLongId = dirTargetBill.keySet().iterator().next();
				List<BFRow> targetRow = dirTargetBill.get(tempLongId);
				BFRowId targetBillRow = targetRow.get(0).getId();
				srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
				tempId = targetBillRow.getBillId().toString();

				DynamicObject entry = new DynamicObject(entryType);
				entry.set("yd_internalseq", seq);
				String billName = "";
				if ("im_purinbill".equals(srcBillEntity)) {
					billName = "采购入库单";
				}
				if ("im_saloutbill".equals(srcBillEntity)) {
					billName = "销售出库单";
				}
				entry.set("yd_internalbillname", billName);
				entry.set("yd_internalbilltype", srcBillEntity);
				entry.set("yd_internalbillid", tempId);
				DynamicObject tempBillObj = BusinessDataServiceHelper.loadSingle(tempId, srcBillEntity, "billno,org.name");
				entry.set("yd_internalbillno", tempBillObj.getString("billno"));
				entry.set("yd_internalorg", tempBillObj.getString("org.name"));
				internalCol.add(entry);
			} else {
				tempId = "";
				srcBillEntity = "";
			}
			System.out.println(srcBillEntity+tempId);
			seq++;
		}

		SaveServiceHelper.save(new DynamicObject[] {saleoutBillObj});// 保存
	}
	
	/**
	 * 根据物料品牌获取对应的内部结算关系，再获取到对应的内部组织交易关系，可能存在多条故用list
	 * @param matBrandId  物料品牌ID
	 * @param saleOrgIdSet  销售组织ID集合
	 * @return 组织关系：销售组织&采购组织
	 */
	public static List<String> getBrandOrgPriceMap(String matBrandId, Set<String> saleOrgIdSet) {
		List<String> orgMapList = new ArrayList<String>();
		// 根据品牌获取内部交易关系表
		QFilter filter = new QFilter("yd_brand.id", QCP.equals, matBrandId);
		filter.and(new QFilter("billstatus", QCP.equals, "C"));
		DynamicObject[] innerBills = BusinessDataServiceHelper.load("yd_innerrelbill", "entryentity.yd_orglevel,entryentity.yd_org", filter.toArray());
		// 遍历内部交易关系表获取对应组织交易关系
		for (DynamicObject innerrelBill : innerBills) {
			// 遍历分录创建内部组织价格表
			String[] orgs = new String[5];  // 设置最大5级组织
			DynamicObjectCollection enCol = innerrelBill.getDynamicObjectCollection("entryentity");
			for(int i=0;i<enCol.size();i++) {
				DynamicObject enObj = enCol.get(i);
				orgs[enObj.getInt("yd_orglevel")-1] = enObj.getDynamicObject("yd_org").getPkValue().toString();
			}
			
			for (int j=1;j<orgs.length;j++) {
				if (!StringUtil.isEmptyString(orgs[j]) && saleOrgIdSet.contains(orgs[j-1])) {
					String outOrgId = orgs[j-1];
					String inOrgId = orgs[j];
					orgMapList.add(outOrgId+"&"+inOrgId);
				}
			}
		}
		
		return orgMapList;
	}

	/**
	 * 校验开门红业务单据末级销售出库单对应的内部交易关系是否有已维护
	 * @author: hst
	 * @createDate: 2022/12/13
	 * @param saleoutBillId 末级销售出库单
	 * @return billId为空表示失败，errorMsg表示失败原因
	 */
	public static Map<String, String> checkPCPSettleMsg(String saleoutBillId) {
		Map<String,String> result = new HashMap<String,String>();
		// 源末级销售出库单
		DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
		DataSet settleDataSet = null;
		try {
			// update by hst 2023/04/13 区分开门红与线下一盘货链路获取
			boolean isStart = saleoutBillObj.getBoolean("yd_isinventory");
			boolean isOffline = saleoutBillObj.getBoolean("yd_isoffline");
			if (isStart) {
				settleDataSet = getGoodStartSettlePath(saleoutBillObj);
			} else if (isOffline) {
				settleDataSet = getOfflineSettlePath(saleoutBillObj);
			}
			StringBuffer errMsg = new StringBuffer();
			LinkedList<String> orgList = new LinkedList<String>();
			// 校验组织与仓库映射表
			errMsg.append(checkOrganizationWarehouse(settleDataSet.copy(),orgList));
			// 校验生产日期
			errMsg.append(checkGenerationDate(saleoutBillObj));
			// 校验内部商客
			errMsg.append(checkSupplyAndCustomer(saleoutBillObj, orgList));
			if (errMsg.length() > 0) {
				result.put("billId", "");
				result.put("errorMsg", errMsg.toString());
			} else {
				result.put("billId", saleoutBillId);
				result.put("errorMsg", "");
			}
		} finally {
			if (Objects.nonNull(settleDataSet)) {
				settleDataSet.close();
			}
		}
		return result;
	}

	/**
	 * 获取开门红业务结算路径
	 * @author: hst
	 * @createDate: 2022/12/13
	 * @param bill
	 * @return
	 */
	public static DataSet getGoodStartSettlePath(DynamicObject bill) {
		List<QFilter> qFilters = new ArrayList<>();
		DynamicObject settleOrg = bill.getDynamicObject("yd_settleorg");
		DynamicObject biztype = bill.getDynamicObject("biztype");
		boolean isForward = "210".equals(biztype.getString("number")) ? true : false;
		if (Objects.isNull(settleOrg)) {
			throw new KDBizException("获取不到结算组织信息，无法生成PCP结算流程单据！");
		}
		// 过滤条件
		qFilters.add(new QFilter("yd_settleorg.id", QCP.equals, settleOrg.getPkValue()));
		qFilters.add(new QFilter("status", QCP.equals, "C"));
		// 排序字段
		String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
		// 错误提示
		String tip = "";
		// 当前单据是否按品牌进行拆单
		boolean isSplit = bill.getBoolean("yd_issplit");
		if (isSplit) {
			// 按结算组织+品牌查找路径，取分录第一行的品牌，根据品牌+客户维度找到对应的结算路径
			DynamicObject brand = bill.getDynamicObjectCollection("billentry").size() > 0 ?
					bill.getDynamicObjectCollection("billentry").get(0).getDynamicObject("yd_basedata_pinpai") : null;
			if (Objects.isNull(brand)) {
				throw new KDBizException("获取不到品牌信息，无法生成PCP结算流程单据！");
			} else {
				//获取结算路径
				qFilters.add(QFilter.of("yd_brand.number = ? and yd_issplit = ?",brand.getString("number"),true));
				tip = "当前结算组织[" + settleOrg.getString("name") + "]的品牌[" + brand.getString("name") + "]没有配置内部结算关系，无法生成内部结算流程单据！";
			}
		} else {
			// 按结算组织查找路径
			qFilters.add(QFilter.of("yd_issplit = ?",false));
			tip = "当前" + settleOrg.getString("name") + "没有配置内部结算关系，无法生成内部结算流程单据！";
		}
		DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_goodstartsettle",
				"yd_settleorg.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
						"yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName", qFilters.toArray(new QFilter[qFilters.size()]), orderby);
		if (!settleDataSet.hasNext()) {
			// 品牌没有配置对应的内部结算关系，不生成内部结算单据
			throw new KDBizException(tip);
		}
		return settleDataSet;
	}

	/**
	 * 校验组织与仓库映射表
	 * @author: hst
	 * @createDate: 2022/12/14
	 * @param settleDataSet 结算路径
	 * @return
	 */
	private static String checkOrganizationWarehouse (DataSet settleDataSet,LinkedList<String> orgList) {
		String outOrgId = "";  // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
		// 校验组织与仓库映射表
		Map<String, String> orgWarehouseMap = getOrgWarehouseMap();
		String warehouseError = "以下销售出库组织缺少默认仓库映射：";
		StringBuffer warehouseErrorMsg = new StringBuffer();
		for (Row settleRow : settleDataSet) {
			String curOrgId = settleRow.getString("orgid");
			String orgLevel = settleRow.getString("orglevel");

			if (StringUtils.isNotEmpty(outOrgId)) {
				String mapValue = outOrgId+"&"+curOrgId;
				orgList.add(mapValue);
			}
			outOrgId = curOrgId;
			// 一级股份不用校验仓库
			if ((!"1".equals(orgLevel)) && !orgWarehouseMap.containsKey(curOrgId)) {
				warehouseErrorMsg.append(settleRow.getString("orgNum")+settleRow.getString("orgName")+"，");
			}
		}
		if (warehouseErrorMsg.length() > 0) {
			warehouseErrorMsg.insert(0, warehouseError);
			warehouseErrorMsg.deleteCharAt(warehouseErrorMsg.length() - 1).append("；");
		}
		return warehouseErrorMsg.toString();
	}

	/**
	 * 开门红业务校验生产日期
	 * @author: hst
	 * @createDate: 2022/12/14
	 * @param bill
	 * @return
	 */
	private static String checkGenerationDate(DynamicObject bill) {
		StringBuffer lotError = new StringBuffer();
		// 如果参数为true表示校验生产日期，为false表示不校验
		DynamicObject biztype = bill.getDynamicObject("biztype");
		boolean isControlLot = true; // 默认校验
		boolean isGeneralDate = true; // 默认为true，自动带日期即不要校验日期，退货类型的如果开通了此参数则不用校验批次
		// 从配置表中查找对应的值
		String isControlLotStr = BizHelper.getQueryOne("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isControlLot").toArray());
		String isGeneralDateStr = BizHelper.getQueryOne("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
		if (StringUtils.isNotBlank(isControlLotStr)) {
			isControlLot = StringUtils.equalsIgnoreCase("true", isControlLotStr);
		}
		if (StringUtils.isNotBlank(isGeneralDateStr)) {
			isGeneralDate = StringUtils.equalsIgnoreCase("true", isGeneralDateStr);
		}
		Boolean isReturn = "2101".equals(biztype.getString("number"));  // 是否退货
		DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("billentry");
		if (isControlLot) {
			for (DynamicObject entryObj : entryCol) {
				// 校验是否存在对应的批次数据
				String lot = entryObj.getString("yd_ph");
				String materialNum = entryObj.getString("material.masterid.number");
				String wsNum = entryObj.getDynamicObject("warehouse").getString("number");
				Date beginDate = entryObj.getDate("yd_scrq");
				Date endDate = entryObj.getDate("yd_dqr");
				if (wsNum == null) continue; // 如果不存在，只有委外中查找映射中不存在，这种情况异常已经记录，无须再次查找
				// 如果生产的日期为空，都需要从即时库存中查找
				if (beginDate==null || endDate == null) {
					// 按批次+物料+仓库，如果没有查找到，正向执行提示异常，如果是红字，则需要再次查找按物料+仓库查找，没有则提示异常
					boolean isHasLot = hasExpDate(lot, materialNum, wsNum, isReturn);
					if (!isHasLot) {
						if (isGeneralDate) {
							boolean isHasRefundLot = hasLastExpDate(materialNum, wsNum, isReturn);
							if (!isHasRefundLot) {
								if (lotError.indexOf(materialNum+"的批次"+lot) < 0) lotError.append("仓库编码"+wsNum+"的"+materialNum+"的批次"+lot+"，");
							}
						}else {
							if (lotError.indexOf(materialNum+"的批次"+lot) < 0) lotError.append("仓库编码"+wsNum+"的"+materialNum+"的批次"+lot+"，");
						}
					}
				}
			}
			if (lotError.length() > 0) lotError.insert(0, "以下物料对应的批次不存在即时库存数据：").deleteCharAt(lotError.length() - 1).append("；");
			// 添加的异常信息中
		}
		return lotError.toString();
	}

	/**
	 * 校验内部商客
	 * @author: hst
	 * @createDate: 2022/12/14
	 * @param bill
	 * @param orgRelList
	 * @return
	 */
	private static String checkSupplyAndCustomer(DynamicObject bill,LinkedList<String> orgRelList) {
		// 校验组织是否维护内部客商，一级组织校验供应商，二级组织校验客户和供应商，末级组织校验客户
		DynamicObject biztype = bill.getDynamicObject("biztype");
		boolean isForward = "210".equals(biztype.getString("number")) ? true : false;
		// update by hst 2023/02/17 虚单结算类型
		boolean isVirtual = bill.getBoolean("yd_isvirtual");
		boolean isCheckPrice = true;
		String internalOrgError = "以下组织缺少设置对应的内部客商信息：";
		StringBuffer internalOrgErrorMsg = new StringBuffer();
		StringBuffer ErrorMsg = new StringBuffer();
		// 校验是否有维护组织间价格
		for (String orgRelMap : orgRelList) {
			String[] keys = orgRelMap.split("&");
			String srcOrg = isForward ? keys[0] : keys[1];
			DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
			String srcOrgName = srcOrgObj.getString("name");
			String desOrg = isForward ? keys[1] : keys[0];
			DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");
			String desOrgName = desOrgObj.getString("name");

			String orgPriceError = "销售出库组织（" + srcOrgName + "）与采购入库组织（" + desOrgName + "）缺失以下物料的价格：";
			StringBuffer matPriceError = new StringBuffer();

			// 获取组织内部各个物料新的结算价格集合
			Date bizDate = bill.getDate("biztime");
			Map<String, BigDecimal> matPriceMap = getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);
			DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("billentry");
			for (DynamicObject entryObj : entryCol) {
				String materialId = entryObj.getString("material.masterid.id");
				String materialNum = entryObj.getString("material.masterid.number");
				// 校验客户交易价格
				if (!matPriceMap.containsKey(materialId) && matPriceError.indexOf(materialNum) < 0) {
					matPriceError.append(materialNum + "，");
				}
			}


			if (matPriceError.length() > 0) {
				matPriceError.insert(0, orgPriceError);
				matPriceError.deleteCharAt(matPriceError.length() - 1).append("；");
				ErrorMsg.append(matPriceError);
			}
			// update by hst 2023/02/17 重复提示错误信息，需要清空
			internalOrgErrorMsg = new StringBuffer();
			// 根据编码获取后者组织作为客户
			// update by hst 2022/12/27 校验结算路径上客户默认税率是否为空
			DynamicObject customer = getInternalCP("bd_customer", desOrgObj.getString("number"));
			if (Objects.isNull(customer)) {
				internalOrgErrorMsg.append(desOrgObj.getString("number") + desOrgObj.getString("name") + "缺少对应客户，");
			} else {
				DynamicObject taxRate = customer.getDynamicObject("taxrate");
				if (Objects.isNull(taxRate)) {
					internalOrgErrorMsg.append(desOrgObj.getString("number") + desOrgObj.getString("name") + "对应客户默认税率为空，");
				}
			}
			DynamicObject supplier = getInternalCP("bd_supplier", srcOrgObj.getString("number"));
			if (Objects.isNull(supplier)) {
				internalOrgErrorMsg.append(srcOrgObj.getString("number") + srcOrgObj.getString("name") + "缺少对应供应商，");
			}
			if (internalOrgErrorMsg.length() > 0) {
				internalOrgErrorMsg.insert(0, internalOrgError);
				internalOrgErrorMsg.deleteCharAt(internalOrgErrorMsg.length() - 1).append("；");
				ErrorMsg.append(internalOrgErrorMsg);
			}
		}
		return ErrorMsg.toString();
	}

	/**
	 * 校验组织是否有对应的内部客户，返回对象，校验税率
	 * @author: hst
	 * @createDate: 2022/12/27
	 * @param entity
	 * @param number
	 * @return
	 */
	public static DynamicObject getInternalCP(String entity, String number) {
		QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, number);
		cusFilter.and(new QFilter("status", QCP.equals, "C"));
		DynamicObject[] customers = BusinessDataServiceHelper.load(entity, "id,name,number,taxrate,yd_channels", cusFilter.toArray());
		if (customers.length > 0) {
			return customers[0];
		} else {
			return null;
		}
	}

	/**
	 * 获取结算路径
	 * @author: hst
	 * @createDate: 2023/02/21
	 * @param bill
	 * @param type (1 开门红、 2 虚体组织结算）
	 * @return
	 */
	public static DataSet getSettlePath(DynamicObject bill, String type) {
		List<QFilter> qFilters = new ArrayList<>();
		DynamicObject settleOrg = bill.getDynamicObject("yd_settleorg");
		DynamicObject biztype = bill.getDynamicObject("biztype");
		boolean isForward = "210".equals(biztype.getString("number")) ? true : false;
		if (Objects.isNull(settleOrg)) {
			throw new KDBizException("获取不到结算组织信息，无法生成PCP结算流程单据！");
		}
		// 过滤条件
		qFilters.add(new QFilter("yd_settleorg.id", QCP.equals, settleOrg.getPkValue()));
		qFilters.add(new QFilter("status", QCP.equals, "C"));
		// 排序字段
		String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
		// 错误提示
		String tip = "";
		// 按结算组织查找路径
		tip = "当前" + settleOrg.getString("name") + "没有配置内部结算关系，无法生成内部结算流程单据！";
		DataSet settleDataSet = null;
		switch (type) {
			case "2": {
				// 虚体组织结算链路 (组织 + 客户)
				// 客户
				DynamicObject customer = bill.getDynamicObject("customer");
				if (Objects.nonNull(customer)) {
					qFilters.add(new QFilter("yd_customer.id", QCP.equals, customer.getPkValue()));
					settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_virtualbodytissue",
							"yd_settleorg.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
									"yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName", qFilters.toArray(new QFilter[qFilters.size()]), orderby);
				}
				break;
			}
		}
		if (Objects.isNull(settleDataSet) || !settleDataSet.hasNext()) {
			// 品牌没有配置对应的内部结算关系，不生成内部结算单据
			throw new KDBizException(tip);
		}
		return settleDataSet;
	}

	/**
	 * 校验PCP销售结算单据末级销售出库单对应的内部交易关系是否有已维护
	 * @author: hst
	 * @createDate: 2023/02/20
	 * @param saleoutBillId 末级销售出库单
	 * @param type 类型（goodStart 开门红、 virtual 虚体组织）
	 * @return billId为空表示失败，errorMsg表示失败原因
	 */
	public static Map<String, String> checkPCPSettleMsg(String saleoutBillId, String type) {
		Map<String,String> result = new HashMap<String,String>();
		// 源末级销售出库单
		DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
		// 结算路径
		DataSet settleDataSet = null;
		try {
			switch (type) {
				case "goodStart" : {
					settleDataSet = getGoodStartSettlePath(saleoutBillObj);
					break;
				}
				case "virtual" : {
					settleDataSet = getSettlePath(saleoutBillObj,"2");
					break;
				}
			}
			StringBuffer errMsg = new StringBuffer();
			LinkedList<String> orgList = new LinkedList<String>();
			// 校验组织与仓库映射表
			errMsg.append(checkOrganizationWarehouse(settleDataSet.copy(),orgList));
			// 校验生产日期
			errMsg.append(checkGenerationDate(saleoutBillObj));
			// 校验内部商客
			errMsg.append(checkSupplyAndCustomer(saleoutBillObj, orgList));
			if (errMsg.length() > 0) {
				result.put("billId", "");
				result.put("errorMsg", errMsg.toString());
			} else {
				result.put("billId", saleoutBillId);
				result.put("errorMsg", "");
			}
		} finally {
			settleDataSet.close();
		}
		return result;
	}

	/**
	 * 根据关联关系保存下游单据，保存前先清理 (虚体组织结算)
	 * @author: hst
	 * @createDate: 2023/02/23
	 * @param saleoutBillId 末级销售出库单ID
	 */
	public static void saveVirtualBotpBills(String saleoutBillId) {
		DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
		DynamicObjectCollection internalCol = saleoutBillObj.getDynamicObjectCollection("yd_internalentity");
		internalCol.clear();

		DynamicObjectType entryType = internalCol.getDynamicObjectType();

		ConvertDataService dataService = new ConvertDataService();
		String srcBillEntity = "im_saloutbill";
		String tempId = saleoutBillId;
		int seq = 1;
		// 遍历单据，获取下游单据数据，虚体组织结算与其他不同，分两层生成
		Map<Long, List<BFRow>> dirTargetBills = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
		if (dirTargetBills.size() > 0) {
			for (BFRow targetRow : dirTargetBills.get(Long.valueOf(tempId))) {
				BFRowId targetBillRow = targetRow.getId();
				srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
				tempId = targetBillRow.getBillId().toString();
				DynamicObject entry = new DynamicObject(entryType);
				entry.set("yd_internalseq", seq);
				String billName = "";
				if ("im_purinbill".equals(srcBillEntity)) {
					billName = "采购入库单";
				}
				if ("im_saloutbill".equals(srcBillEntity)) {
					billName = "销售出库单";
				}
				entry.set("yd_internalbillname", billName);
				entry.set("yd_internalbilltype", srcBillEntity);
				entry.set("yd_internalbillid", tempId);
				DynamicObject tempBillObj = BusinessDataServiceHelper.loadSingle(tempId, srcBillEntity, "billno,org.name");
				entry.set("yd_internalbillno", tempBillObj.getString("billno"));
				entry.set("yd_internalorg", tempBillObj.getString("org.name"));
				internalCol.add(entry);
				// 分层获取下游单据
				saveDownStreamBill(tempId, srcBillEntity, seq, internalCol, entryType);
				seq++;
			}
			SaveServiceHelper.save(new DynamicObject[]{saleoutBillObj});// 保存
		}
	}

	/**
	 * 保存下游单据
	 * @author: hst
	 * @createDate: 2023/02/22
	 * @param id
	 * @param srcBillEntity
	 */
	public static void saveDownStreamBill (String id, String srcBillEntity, int seq, DynamicObjectCollection internalCol, DynamicObjectType entryType) {
		ConvertDataService dataService = new ConvertDataService();
		while(StringUtils.isNotEmpty(id)) {
			// 遍历单据，获取下游单据数据
			Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(id)});
			if (dirTargetBill.size() == 1) {
				Long tempLongId = dirTargetBill.keySet().iterator().next();
				List<BFRow> targetRow = dirTargetBill.get(tempLongId);
				BFRowId targetBillRow = targetRow.get(0).getId();
				srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
				id = targetBillRow.getBillId().toString();

				DynamicObject entry = new DynamicObject(entryType);
				entry.set("yd_internalseq", seq);
				String billName = "";
				if ("im_purinbill".equals(srcBillEntity)) {
					billName = "采购入库单";
				}
				if ("im_saloutbill".equals(srcBillEntity)) {
					billName = "销售出库单";
				}
				entry.set("yd_internalbillname", billName);
				entry.set("yd_internalbilltype", srcBillEntity);
				entry.set("yd_internalbillid", id);
				DynamicObject tempBillObj = BusinessDataServiceHelper.loadSingle(id, srcBillEntity, "billno,org.name");
				entry.set("yd_internalbillno", tempBillObj.getString("billno"));
				entry.set("yd_internalorg", tempBillObj.getString("org.name"));
				internalCol.add(entry);
			} else {
				id = "";
				srcBillEntity = "";
			}
			seq++;
		}
	}

	/**
	 * 获取线下一盘货业务结算路径
	 * @author: hst
	 * @createDate: 2023/03/22
	 * @param bill
	 * @return
	 */
	public static DataSet getOfflineSettlePath (DynamicObject bill) {
		List<QFilter> qFilters = new ArrayList<>();
		DynamicObject stockOrg = bill.getDynamicObject("org");
		DynamicObject settleOrg = bill.getDynamicObject("yd_settleorg");
		DynamicObject biztype = bill.getDynamicObject("biztype");
		boolean isForward = "210".equals(biztype.getString("number")) ? true : false;
		if (Objects.isNull(stockOrg)) {
			throw new KDBizException("获取不到库存组织信息，无法生成PCP结算流程单据！");
		}
		if (Objects.isNull(settleOrg)) {
			throw new KDBizException("获取不到结算组织信息，无法生成PCP结算流程单据！");
		}
		// 过滤条件
		qFilters.add(new QFilter("yd_stockorg.id", QCP.equals, stockOrg.getPkValue()));
		qFilters.add(new QFilter("yd_settleorg.id", QCP.equals, settleOrg.getPkValue()));
		qFilters.add(new QFilter("status", QCP.equals, "C"));
		// 排序字段
		String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
		// 错误提示
		String tip = "";
		tip = "当前库存组织：" + stockOrg.getString("name") + "与结算组织："
				+ settleOrg.getString("name") + "没有配置线下一盘货结算关系，无法生成内部结算流程单据！";
		DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_offlinesettle",
				"yd_settleorg.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
						"yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName", qFilters.toArray(new QFilter[qFilters.size()]), orderby);
		if (!settleDataSet.hasNext()) {
			// 品牌没有配置对应的内部结算关系，不生成内部结算单据
			throw new KDBizException(tip);
		}
		return settleDataSet;
	}
}
