package kd.bos.tcbj.im.outbill.helper;


import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.botp.ConvertDataService;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.botp.runtime.BFRow;
import kd.bos.entity.botp.runtime.BFRowId;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.form.IFormView;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.common.enums.PcpBusinessTypeEnum;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.helper.ConvertHelper;
import kd.bos.tcbj.im.helper.InterHelper;
import kd.bos.tcbj.im.outbill.imp.SaleOutBillMserviceImpl;
import kd.bos.tcbj.im.util.*;
import kd.bos.tcbj.im.vo.R;
import kd.bos.tcbj.im.vo.RequestMap;
import kd.epm.eb.ebBusiness.serviceHelper.MutexServiceHelper;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PCP处理类
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-6-15
 */
public class PCPServiceHelper {

    /**
     * 获取PCP销售出库单
     * 接口 / 销售出库单中间表调用
     * @param saleType 销售类型
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @param billNo PCP出库结果单号
     * <AUTHOR>
     * @date 2022-6-14
     */
    public static boolean getPcpSaleOutBill(String saleType, Date beginDate, Date endDate, String billNo) {
        return InterHelper.getPcpSaleOutBill(saleType, beginDate, endDate, billNo);
    }

    /**
     * 调度计划执行触发，PCP销售出库中间表已经已经接收后生成销售的销售出库单后回传
     * <AUTHOR>
     * @date 2022-8-15
     */
    public static void pushSaleResultToPcp() {
        // 调度计划执行触发
        // 调用接口做数据推送
        InterHelper.pushSaleResultToPcp();
    }

    /**
     * PCP调拨单获取
     * @return 数据是否获取成功
     * <AUTHOR>
     * @date 2022-7-18
     * @param beginDate 执行的日期
     */
    @Deprecated
    public static boolean pullPcpTransBill(Date beginDate) {
        Objects.requireNonNull(beginDate, "开始日期不能为空！");
        beginDate = DateTimeUtils.format2(beginDate);
        Date endDate = DateTimeUtils.addDate(beginDate, 1);
        // TODO 待完善参数信息
        return InterHelper.pullPcpTransBill();
    }

    /**
     * 销售出库中间表转换生成销售出库单
     * 接口 / 销售出库单调用
     * @param idList 需要指定的ID集合
     * @param transDate 需要转换的单据日期
     * @param isTask 是否后台事务调用
     * <AUTHOR>
     * @date 2022-6-15
     */
    public static void transSaleOutBill(List<Object> idList, Date transDate, boolean isTask) {

        DataSet midDataSet = null;

        try {
            // 1、获取还未生成的下游销售出库单的销售出库单中间表
            // 单据转换为已审核、还未生成下游销售出库单
            String midBillType = BillTypeHelper.BILLTYPE_MIDSALEOUTBILL;
            String operationKey = "transSaleOutBill";
            QFilter qFilter = QFilter.of("billstatus = 'C' and yd_ishasnext = 0");

            // 按转换日志查询
            if (transDate != null) {
//            	// 查询7天内还未转换的单据,yzw20220908
//                Date beginDate = DateTimeUtils.format2(transDate);
////                Date endDate = DateTimeUtils.addDate(beginDate, 1);
//                Date searchBeginDate = DateTimeUtils.addDate(beginDate, -7);
//                Date searchEndDate = DateTimeUtils.addDate(beginDate, 1);
//                qFilter.and(QFilter.of("yd_bizdate >= ? and yd_bizdate < ?", searchBeginDate, searchEndDate));
                Date beginDate = DateTimeUtils.format2(transDate);
                Date endDate = DateTimeUtils.addDate(beginDate, 1);
                qFilter.and(QFilter.of("yd_bizdate >= ? and yd_bizdate < ?", beginDate, endDate));
            }
            // 按单号查询
            if (idList != null && idList.size() > 0) {
                qFilter.and(QueryUtil.qf("id", QCP.in, idList));
            }
            // 判断是否是后台任务调用
            if (isTask) {
                // 如果是后台任务调用，只获取还未处理的信息（即异常信息为空的且没有下游单据）
                qFilter.and(QueryUtil.qf("yd_errmessage", ""));
            }
//            // 测试环境测试
//            qFilter.and(QueryUtil.qf("billno", "PSR20220513000003"));
            //update by hst 2022/10/26 增加查询出业务类型，用于判断是否是一盘货
            // update by hst 2023/03/22 增加是否线下一盘货标识
            midDataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), midBillType,
                    "id,yd_isinventory,yd_isvirtual,yd_isoffline", qFilter.toArray());

            if (!midDataSet.hasNext()) return;

            // 上下文处理
            Map<String, Object> context = new HashMap<>();
            for (Row midRow : midDataSet) {
                // 获取待处理的销售出库单中间表ID
                long billId = midRow.getLong("id");
                //update by hst 2022/10/26
                boolean isInventory = midRow.getBoolean("yd_isinventory");
                //update by hst 2023/02/15 是否虚单结算
                boolean isVirtual = midRow.getBoolean("yd_isvirtual");
                //update by hst 2023/03/22 是否线下一盘货
                boolean isOffline = midRow.getBoolean("yd_isoffline");
                // 校验单据是否已经锁定
                if (BizHelper.isBillLock(midBillType, billId, operationKey)) continue;

                try {
                    // 对处理的单据进行加锁处理
                    BizHelper.requestLock(midBillType, billId, operationKey);
                    // update by hst 2022/10/26 区分是否是一盘货结算，进行不同策略
                    // update by hst 2023/02/15 增加虚单结算类型
                    if (isInventory || isVirtual || isOffline) {
                        // 2、数据校验，如果有异常则将异常信息反写到单据上
                        transSaleOutBill_inventory_verify(billId,context);
                        Object[] targetIds = new Object[0];
                        // 3、转换生成销售出库单
                        if (isInventory) {
                            // 如果是开门红，无需排除物料、仓库
                            // update by hst 2022/12/13 由参数控制是否进行拆单
                            targetIds = transSaleOutBill_inventory_trans(context,billId,false);
                        } else if (isVirtual){
                            // update by hst 2023/02/15 如果是虚单结算，无需排除物料，仓库，无需拆单
                            targetIds = transSaleOutBill_virtual_trans(context,billId,false);
                        }else if (isOffline) {
                            // 如果是线下一盘货，需排除物料、仓库
                            // update by hst 2022/12/13 由参数控制是否进行拆单
                            // update by hst 2023/03/21 线下一盘货修改为无需排除仓库、物料
                            targetIds = transSaleOutBill_offline_trans(context, billId, false);
                        }
                        // 4、保存关联关系
                        transSaleOutBill_relation(billId,targetIds);
                    } else{
                        // 2、数据校验，如果有异常则将异常信息反写到单据上
                        transSaleOutBill_verify(context, billId);
                        // 3、转换生成销售出库单
                        long targetId = transSaleOutBill_trans(context, billId);
                        // 4、保存关联关系
                        transSaleOutBill_relation(context, billId, targetId);
                        // 5、提交 / 审核
                        transSaleOutBill_audit(context, targetId);
                    }
                }catch (Exception err) {
                    // 记录异常信息反写到单据字段信息中
                    DynamicObject midBillInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId, "yd_errmessage");
                    //add by mairq 20250410 如果错误信息为空则直接抛出原始信息
                    if(StringUtils.isBlank(err.getMessage())){
                        throw err;
                    }
                    String errMessage = err.getMessage();
                    // 如果字段过长，则截取长度
                    if (errMessage.length() > 2000) {
                        errMessage = errMessage.substring(0, 2000);
                    }
                    midBillInfo.set("yd_errmessage", errMessage);
                    BizHelper.saveDynamicObject(midBillInfo);
                }finally {
                    // 无论是否处理成功都需要释放锁
                    BizHelper.releaseLock(midBillType, billId, operationKey);
                }
            }
        }finally {
            ORMUtils.close(midDataSet);
        }
    }

    /**
     * 转换销售出库单，单据信息校验
     * @param context 上下文
     * @param billId 销售出库单中间表ID
     * <AUTHOR>
     * @date 2022-6-15
     */
    private static void transSaleOutBill_verify(Map<String, Object> context, long billId) {
        DataSet dataSet = null;
        StringBuilder errBuilder = new StringBuilder();
        int errIndex = 0;
        try {
            // 获取销售出库单中间表的编码、业务日期、库存组织、销售组织、客户、销售订单编码、物料、批次、生产日期、到期日、数量、含税单价、仓库
            // 取消selector查询，后续加的字段比较多，避免数据传递出错
//            String selector = "billno, yd_bizdate, yd_orgnumber, yd_saleorgnumber, yd_customnumber, yd_saleordernumber, entryentity.yd_materialnumber, entryentity.yd_batchno, entryentity.yd_probegindate, entryentity.yd_proenddate, entryentity.yd_quantity, entryentity.yd_taxprice, entryentity.yd_warehousenumber";
            DynamicObject midBillInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId);
            DynamicObjectCollection entryCol = midBillInfo.getDynamicObjectCollection("entryentity"); // 销售出库单中间表分录
            String orgNumber = midBillInfo.getString("yd_orgnumber"); // 库存组织
            String saleOrgNumber = midBillInfo.getString("yd_saleorgnumber"); // 销售组织
            String customerNumber = midBillInfo.getString("yd_customnumber"); // 客户
            String saleOrderNumber = midBillInfo.getString("yd_saleordernumber"); // 销售订单编码
            String businessType = midBillInfo.getString("yd_businesstype"); // 业务类型
            String sourceTradeNumber = midBillInfo.getString("yd_sourcetradenumber"); // 内部交易源头单号
            String bizType = midBillInfo.getString("yd_biztype"); // 业务类型

            // 校验必填字段，如果不符合则直接提示跳出
            // 校验表头字段信息
            String headWarning = InterUtils.verifyFieldIsNull(midBillInfo, new String[] {"billno","yd_bizdate","yd_orgnumber","yd_saleorgnumber","yd_customnumber"}, new String[] {"编码","业务日期","库存组织编码","销售组织编码","客户编码"});
            // 校验分录表头信息
            String entryWarning = InterUtils.verifyFieldIsNull(midBillInfo, "entryentity", new String[] {"yd_materialnumber","yd_batchno","yd_quantity","yd_warehousenumber"}, new String[] {"物料编码","批次","数量","仓库编码"});
            if (StringUtils.isNotBlank(headWarning) || StringUtils.isNotBlank(entryWarning)) {
                throw new KDBizException(new StringBuilder().append(headWarning).append("\n").append(entryWarning).toString());
            }

            // 如果是正向的并且有销售订单号则需要校验销售订单分录ID不能为空,yzw20220919
            if (StringUtils.isNotBlank(saleOrderNumber) && "210".equals(bizType)) {
            	String easIdWarning = InterUtils.verifyFieldIsNull(midBillInfo, "entryentity", new String[] {"yd_easorderentryid"}, new String[] {"EAS销售订单分录ID"});
            	if (StringUtils.isNotBlank(easIdWarning)) {
            		throw new KDBizException(new StringBuilder().append(easIdWarning).toString());
            	}
            }

            // 1、需检验组织映射、组织分类表、仓库映射表是否存在
            // 组织映射
            String billType = BillTypeHelper.BILLTYPE_PCPORGRELATION;
            String orgKey = new StringBuilder().append(billType).append("_").append(orgNumber).append("_").append("isExists").toString();
            if (!context.containsKey(orgKey)) {
                String relaOrgNumber = BizHelper.getQueryOne(billType, "entryentity.yd_org.number", QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber = ?", orgNumber).toArray());
                context.put(orgKey, relaOrgNumber);
                if (StringUtils.isBlank(relaOrgNumber)) {
                    errBuilder.append(++errIndex).append("、[").append(orgNumber).append("]不存在组织映射关系！\n");
                }
            }
            String key = new StringBuilder().append(billType).append("_").append(saleOrgNumber).append("_").append("isExists").toString();
            if (!context.containsKey(key)) {
                boolean isExists = BizHelper.isExists(billType, QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber = ?", saleOrgNumber).toArray());
                context.put(key, isExists);
                if (!isExists) {
                    errBuilder.append(++errIndex).append("、[").append(saleOrgNumber).append("]不存在组织映射关系！\n");
                }
            }
//            // 组织分类表校验
//            billType = BillTypeHelper.BILLTYPE_ORGCLASSIFY;
//            key = new StringBuilder().append(billType).append("_").append(orgNumber).append("_").append("isExists").toString();
//            String perOrderKey = new StringBuilder().append(billType).append("_").append(orgNumber).append("_").append("isPreOrder").toString();
//            if (!context.containsKey(key) && context.get(orgKey) != null) {
//                dataSet = BizHelper.getQueryDataSet("transSaleOutBill_verify", billType, "yd_orgtype", QFilter.of("billstatus='C' and entryentity.yd_org.number = ?", context.get(orgKey)).toArray());
//                boolean isExists = dataSet.hasNext();
//                context.put(key, isExists);
//                if (!isExists) {
//                    errBuilder.append(++errIndex).append("、[").append(context.get(orgKey)).append("]不存在组织分类映射关系！\n");
//                }else {
//                    Row row = dataSet.next();
//                    String orgType = row.getString("yd_orgtype");// 组织类型
//                    context.put(perOrderKey, "1".equals(orgType));
//                }
//            }
//            // 2、如果是当前组织的组织类型为“前置财审类”，则要求来源销售订单必填
//            if ((Boolean) context.getOrDefault(perOrderKey, false) && StringUtils.isBlank(saleOrderNumber)) {
//                errBuilder.append(++errIndex).append("、[").append(orgNumber).append("]属于非前置财审类组织，来源销售订单为空！\n");
//            }

            // 调整，PCP的源头单据分成5种类型
            // 信控前置
            if (PcpBusinessTypeEnum.CREDIT_CONTROL_PRE.getCode().equals(businessType)) {
                // 2022-08-31控制一定要传销售订单编码
                // 2022-09-07控制只有正向销售才校验销售订单，销售退不校验销售退货申请单
                if (StringUtils.isBlank(saleOrderNumber) && "210".equals(bizType)) {
                    errBuilder.append(++errIndex).append("、单据类型为财务前置营销云财审，来源销售订单/销售退货申请单号为空！\n");
                }
            }
            // 信控后置
            else if (PcpBusinessTypeEnum.CREDIT_CONTROL_REAR.getCode().equals(businessType)) {
                // 2022-09-07控制只有正向销售才校验销售订单，销售退不校验销售退货申请单
                if (StringUtils.isBlank(saleOrderNumber) && "210".equals(bizType)) {
                    errBuilder.append(++errIndex).append("、单据类型为财务后置EAS财审，来源销售订单/销售退货申请单号为空！\n");
                }
            }
            // 内部交易
            else if (PcpBusinessTypeEnum.INTERNAL_DEAL.getCode().equals(businessType)) {
                if (StringUtils.isBlank(saleOrderNumber)) {
                    errBuilder.append(++errIndex).append("、单据类型为内部交易类型，来源销售订单/销售退货申请单号为空！\n");
                }
            }
            // 内部交易退类型
            else if (PcpBusinessTypeEnum.INTERNAL_DEAL_RETURN.getCode().equals(businessType)) {
                if (StringUtils.isBlank(saleOrderNumber)) {
                    errBuilder.append(++errIndex).append("、单据类型为内部交易退类型，来源销售订单/销售退货申请单号为空！\n");
                }
            }
            // 销售退转内部交易
            else if (PcpBusinessTypeEnum.SALE_RETURN_INTERNAL_DEAL.getCode().equals(businessType)) {
                // 内部交易源头单号，正向和退货都需要传
                if (StringUtils.isBlank(sourceTradeNumber)) {
                    errBuilder.append(++errIndex).append("、单据类型为销售退转内部交易，内部交易源头单号不能为空！\n");
                }
                // 判断EAS销售订单是否为空
                if (StringUtils.isBlank(saleOrderNumber)) {
                    errBuilder.append(++errIndex).append("、单据类型为内部交易退类型，来源销售订单/销售退货申请单号为空！\n");
                }
                // 如果是销售正向的流程，需要增加判断是否存在销售退的单据推送，如果没有推送不允许生成销售出库单
                if ("210".equals(bizType)) {
                    // 查找当前单号
                    boolean isExistsSrcBill = BizHelper.isExists(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, QFilter.of("yd_sourcetradenumber=? and yd_biztype = '2101'", sourceTradeNumber).toArray());
                    if (!isExistsSrcBill) {
                        errBuilder.append(++errIndex).append("、单据类型为销售退转内部交易，找不到内部交易源头单号销售退对应的销售出库单中间数据！\n");
                    }
                }
            }

            // 校验客户是否存在
            billType = BillTypeHelper.BILLTYPE_CUSTOMER;
            key = new StringBuilder().append(billType).append("_").append(customerNumber).append("_").append("isExists").toString();
            if (!context.containsKey(key)) {
                boolean isExists = BizHelper.isExists(billType, QFilter.of("status='C' and number = ?", customerNumber).toArray());
                context.put(key, isExists);
                if (!isExists) {
                    errBuilder.append(++errIndex).append("、[").append(customerNumber).append("]不存在当前客户信息！\n");
                }
            }

            // update by hst 2023/02/15 校验客户是否存在默认税率
            verifycusTaxRate(context,midBillInfo);

            String materialBillType = BillTypeHelper.BILLTYPE_MATERIAL;
            String warehouseBillType = BillTypeHelper.BILLTYPE_WAREHOUSE;
            BigDecimal enTotalAmt = BigDecimal.ZERO; // 统计分录总金额
            Boolean matchLot = false;  // 是否匹配过批次
            for (DynamicObject entryInfo : entryCol) {
                String materialNumber = entryInfo.getString("yd_materialnumber"); // 物料编码
                String warehouseNumber = entryInfo.getString("yd_warehousenumber"); // 仓库编码
                // 校验物料是否存在
                key = new StringBuilder().append(materialBillType).append("_").append(materialNumber).append("_").append("isExists").toString();
                if (!context.containsKey(key)) {
                    boolean isExists = BizHelper.isExists(materialBillType, QFilter.of("status='C' and number = ?", materialNumber).toArray());
                    context.put(key, isExists);
                    if (!isExists) {
                        errBuilder.append(++errIndex).append("、[").append(materialNumber).append("]不存在当前物料信息！\n");
                    }
                }
                // 校验仓库是否存在
                key = new StringBuilder().append(warehouseBillType).append("_").append(warehouseNumber).append("_").append("isExists").toString();
                if (!context.containsKey(key)) {
                    // 平台类型为4,（PCP），PCP仓库为接口字段
                    boolean isExists = BizHelper.isExists(warehouseBillType, QFilter.of("status='C' and number = ?", warehouseNumber).toArray());
                    context.put(key, isExists);
                    if (!isExists) {
                        errBuilder.append(++errIndex).append("、[").append(warehouseNumber).append("]不存在当前仓库信息！\n");
                    }
                }
                // 合计分录总实付金额，后面与表头对比
                BigDecimal enAmt = entryInfo.getBigDecimal("yd_actuallyamount");
                enTotalAmt = enTotalAmt.add(enAmt);
                // 校验生产日期和到期日，根据批次+物料+仓库重新找一遍即时库存,yzw
                if (entryInfo.getDate("yd_probegindate")==null||entryInfo.getDate("yd_proenddate")==null) {
                	QFilter filter = new QFilter("yd_wsnum", QCP.equals, warehouseNumber);
            		filter.and(new QFilter("yd_matnum", QCP.equals, materialNumber));
            		filter.and(new QFilter("yd_flot", QCP.equals, entryInfo.getString("yd_batchno")));
            		DataSet lotData = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
            				"yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
            		if (lotData.hasNext()) {
            			matchLot = true;
            			Row lotRow = lotData.next();
            			entryInfo.set("yd_probegindate", lotRow.getDate("yd_mfgdate"));
            			entryInfo.set("yd_proenddate", lotRow.getDate("yd_expdate"));
            		}
                }
            }

            // 如果重新找过批次并且找到了，就要保存一下单据
            if (matchLot) {
            	SaveServiceHelper.save(new DynamicObject[] {midBillInfo});
            }

            // 重新校验批次
            String lotWarning = InterUtils.verifyFieldIsNull(midBillInfo, "entryentity", new String[] {"yd_probegindate","yd_proenddate"}, new String[] {"生产日期","到期日期"});
            if (StringUtils.isNotBlank(lotWarning)) {
                throw new KDBizException(new StringBuilder().append(lotWarning).toString());
            }

            // 如果都校验通过，并且是内部交易并且金额有差异，则重算分录金额，尾差放在最后一行
            BigDecimal billTotalAmt = midBillInfo.getBigDecimal("yd_itemtotalamount");
            if (errBuilder.length() == 0
            		&& (
            				PcpBusinessTypeEnum.INTERNAL_DEAL.getCode().equals(businessType)
            				|| PcpBusinessTypeEnum.INTERNAL_DEAL_RETURN.getCode().equals(businessType)
            				|| PcpBusinessTypeEnum.SALE_RETURN_INTERNAL_DEAL.getCode().equals(businessType)
            			)
            		&& billTotalAmt.compareTo(enTotalAmt) != 0) {
            	midBillInfo.set("yd_hasdiffamt", true);

            	// 对分录金额进行重算，数量乘以含税单价
            	BigDecimal tempTotalAmt = BigDecimal.ZERO;
            	for (int i=0,len=entryCol.size();i<len;i++) {
            		DynamicObject entryInfo = entryCol.get(i);
            		BigDecimal taxPrice = entryInfo.getBigDecimal("yd_taxprice");
            		BigDecimal qty = entryInfo.getBigDecimal("yd_quantity");
            		BigDecimal amt = taxPrice.multiply(qty).setScale(4, BigDecimal.ROUND_HALF_UP);
            		tempTotalAmt = tempTotalAmt.add(amt);
            		entryInfo.set("yd_oriactuallyamount", entryInfo.getBigDecimal("yd_actuallyamount"));
            		// 最后一行，金额存在差异时重置最后一行
            		if (tempTotalAmt.compareTo(billTotalAmt)!=0 && i==(len-1)) {
            			BigDecimal diffAmt = billTotalAmt.subtract(tempTotalAmt);
            			amt = amt.add(diffAmt);
            		}
            		entryInfo.set("yd_actuallyamount", amt);
            	}

            	SaveServiceHelper.save(new DynamicObject[] {midBillInfo});
            }

            if (errBuilder.length() > 0) {
                throw new KDBizException(errBuilder.toString());
            }
        }finally {
            ORMUtils.close(dataSet);
        }
    }

    /**
     * 转换销售出库单，单据信息校验
     * @param context 上下文
     * @param billId 销售出库单中间表ID
     * @return 销售出库单ID
     * <AUTHOR>
     * @date 2022-6-15
     */
    private static long transSaleOutBill_trans(Map<String, Object> context, long billId) {

        // 根据ID查询需要转换的销售出库中间表信息
        DynamicObject midBillInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId);
        // 转换生成销售出库单
//        String billNo = midBillInfo.getString("billno"); // 单据编号
        String bizType = midBillInfo.getString("yd_biztype"); // 业务类型编码
        String transNo = midBillInfo.getString("yd_transno"); // 库存事务编码
        Date bizDate = midBillInfo.getDate("yd_bizdate"); // 业务日期
        String orgNumber = midBillInfo.getString("yd_orgnumber"); // 库存组织编码
        String saleOrgNumber = midBillInfo.getString("yd_saleorgnumber"); // 销售组织编码
        String customNumber = midBillInfo.getString("yd_customnumber"); // 客户编码
        String description = midBillInfo.getString("yd_description"); // 备注
        String sourceFunction = midBillInfo.getString("yd_sourcefunction"); // 同步来源系统
        String saleOrderNumber = midBillInfo.getString("yd_saleordernumber"); // 来源销售订单
        String platformOrderNo = midBillInfo.getString("yd_platformorderno"); // 营销云单号（csp单号）
        String businessType = midBillInfo.getString("yd_businesstype"); // 业务类型
        String sourceTradeNumber = midBillInfo.getString("yd_sourcetradenumber"); // 内部交易源头单号

        // 获取基础资料数据
        // 客户
//        long customerId = BizHelper.getQueryId(BillTypeHelper.BILLTYPE_CUSTOMER, QFilter.of("status='C' and number=?", customNumber).toArray());
//        DynamicObject customerInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_CUSTOMER, customerId);
        // 组织
        Long orgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id", QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber=?", orgNumber).toArray());
        // 销售组织
        Long saleOrgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id", QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber=?", saleOrgNumber).toArray());

        IFormView saleView = ABillServiceHelper.createAddView(BillTypeHelper.BILLTYPE_SALEOUTBILL); // 销售出库单新增
        IDataModel saleModel = saleView.getModel();

//        // 通过编码获取单据类型
//        long bTypeId = BizHelper.getQueryId(BillTypeHelper.BILLTYPE_BILLTYPE, QueryUtil.q("number", "im_SalOutBill_STD_BT_S"));
//        DynamicObject bTypeInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_BILLTYPE, bTypeId, "id,number,name");

        String sourceFunction2 = "PCP".equals(sourceFunction)?"4":null;

        // 赋值
        saleModel.setItemValueByNumber("billtype", "im_SalOutBill_STD_BT_S"); // 单据类型（标准销售出库单）
        saleModel.setItemValueByID("org", orgId); // 库存组织
        saleModel.setItemValueByID("bizorg", saleOrgId); // 销售组织
        saleModel.setItemValueByNumber("biztype", bizType); // 业务类型
        saleModel.setItemValueByNumber("invscheme", transNo); // 库存事务
        saleModel.setItemValueByNumber("customer", customNumber); // 客户
        saleModel.setItemValueByNumber("yd_dzdkh", customNumber); // 对账客户
        saleModel.setValue("biztime", bizDate); // 业务日期
        saleModel.setValue("yd_tbly", sourceFunction2); // 同步来源系统
        saleModel.setValue("yd_sourcebilltype", 3);  // 来源单据类型 —— PCP销售出库单
        saleModel.setValue("yd_saleordernumber", saleOrderNumber); // 来源销售订单
        saleModel.setValue("yd_platformorderno", platformOrderNo); // 营销云单号（csp单号）
        saleModel.setValue("comment", description); // 备注
        saleModel.setValue("yd_businesstype", businessType); // 业务类型
        saleModel.setValue("yd_sourcetradenumber", sourceTradeNumber); // 内部交易源头单号
        saleModel.setValue("yd_office", midBillInfo.getString("yd_office")); // 办事处
        saleModel.setValue("yd_province", midBillInfo.getString("yd_province")); // 省份
        saleModel.setValue("yd_city", midBillInfo.getString("yd_city")); // 城市
        saleModel.setValue("yd_district", midBillInfo.getString("yd_district")); // 地区
        saleModel.setValue("yd_linkman", midBillInfo.getString("yd_linkman")); // 联系人
        saleModel.setValue("yd_phone", midBillInfo.getString("yd_phone")); // 联系方式
        saleModel.setValue("yd_address", midBillInfo.getString("yd_address")); // 地址
        saleModel.setValue("yd_description2", midBillInfo.getString("yd_description2")); // 摘要

        // 明细数据下推保存
        DynamicObjectCollection midBillEntryCol = midBillInfo.getDynamicObjectCollection("entryentity");
        for (int index=0; index<midBillEntryCol.size(); index++) {
            DynamicObject midBillEntryInfo = midBillEntryCol.get(index);
            String materialNumber = midBillEntryInfo.getString("yd_materialnumber"); // 物料编码
//            String materialName = midBillEntryInfo.getString("yd_materialname"); // 物料名称
            String batchNo = midBillEntryInfo.getString("yd_batchno"); // 批号
            Date proBeginDate = midBillEntryInfo.getDate("yd_probegindate"); // 生产日期
            Date proEndDate = midBillEntryInfo.getDate("yd_proenddate"); // 到期日
            BigDecimal quantity = midBillEntryInfo.getBigDecimal("yd_quantity"); // 数量
//            BigDecimal taxRate = midBillEntryInfo.getBigDecimal("yd_taxrate"); // 税率
//            BigDecimal taxPrice = midBillEntryInfo.getBigDecimal("yd_taxprice"); // 含税单价
            BigDecimal payableAmount = midBillEntryInfo.getBigDecimal("yd_payableamount"); // 明细应付总额（含税）
            BigDecimal actuallyAmount = midBillEntryInfo.getBigDecimal("yd_actuallyamount"); // 明细实付总额（含税）
            String warehouseNumber = midBillEntryInfo.getString("yd_warehousenumber"); // 仓库编码
            boolean isPresent = midBillEntryInfo.getBoolean("yd_ispresent"); // 是否赠品
            boolean isIntegral = midBillEntryInfo.getBoolean("yd_isintegral"); // 是否积分产品
            String integral = midBillEntryInfo.getString("yd_integral"); // 使用积分
            BigDecimal discountAmount = midBillEntryInfo.getBigDecimal("yd_discountamount"); // 总折扣额
            String discountType = midBillEntryInfo.getString("yd_discounttype"); // 折扣方式
            String subDivisionPlatform = midBillEntryInfo.getString("yd_subdivisionplatform"); // 电商渠道
            String saleDistribution = midBillEntryInfo.getString("yd_saledistribution"); // 销售渠道
            String sourceBillEntryId = midBillEntryInfo.getString("yd_easorderentryid"); // 销售订单分录ID
            BigDecimal totalAmount = midBillEntryInfo.getBigDecimal("yd_totalamount"); // update by hst 2023/12/08 应付金额(折前价税合计)
            BigDecimal activityAmount = midBillEntryInfo.getBigDecimal("yd_activityamount"); // update by hst 2023/12/08 促销活动折扣
            BigDecimal rebateAmount = midBillEntryInfo.getBigDecimal("yd_rebateamount"); // update by hst 2023/12/08 销售返利折扣

            // 基础资料
            DataSet dataSet = null;
            String taxRateNum = null;
            try {
                // 调整，不从客户基础资料取，改成取接口的税率字段
                // 如果税率不为0，则从数据库查询基础资料只并带入到出库单中
                dataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), BillTypeHelper.BILLTYPE_CUSTOMER, "taxrate.number as taxRateNum", QFilter.of("status='C' and number=?", customNumber).toArray());
//                dataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), BillTypeHelper.BILLTYPE_TAXRATE, "number as taxRateNum", QFilter.of("status='C' and taxrate=?", taxRate.setScale(2, 4)).toArray());
                if (dataSet.hasNext()) {
                    Row row = dataSet.next();
                    taxRateNum = row.getString("taxRateNum");
                }
            }finally {
                ORMUtils.close(dataSet);
            }

//            BigDecimal taxAmount = taxPrice.multiply(quantity).setScale(2, 4);

            if (saleModel.getEntryRowCount("billentry") == index) {
                saleModel.createNewEntryRow("billentry");
            }
            saleModel.setItemValueByNumber("material", materialNumber, index); // 物料
            saleModel.setValue("yd_ph", batchNo, index); // E3批号
            saleModel.setValue("yd_scrq", proBeginDate, index); // E3生产日期
            saleModel.setValue("yd_dqr", proEndDate, index); // E3到期日
            saleModel.setValue("qty", quantity, index); // 数量
//            saleModel.setValue("priceandtax", taxPrice, index);
            saleModel.setValue("amountandtax", actuallyAmount.add(discountAmount), index); // 应付金额 —— 用来统计出单价和后续折扣额的统计
            saleModel.setItemValueByNumber("taxrateid", taxRateNum, index); // 税率
            if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
            	saleModel.setValue("discounttype", discountType, index); // 折扣方式
            	saleModel.setValue("discountamount", discountAmount, index); // 折扣额
            }
            saleModel.setValue("amountandtax", actuallyAmount, index); // 实付金额 —— 最终标识的实付金额
            saleModel.setItemValueByNumber("warehouse", warehouseNumber, index); // 仓库

            saleModel.setValue("ispresent", isPresent, index);    // 是否赠品
            saleModel.setValue("yd_isintegral", isIntegral, index);    // 是否积分产品
            saleModel.setValue("yd_integral", integral, index);    // 使用积分
            saleModel.setValue("yd_subdivisionplatform", subDivisionPlatform, index);    // 电商渠道
            saleModel.setValue("yd_saledistribution", saleDistribution, index);    // 销售渠道
            saleModel.setValue("yd_easorderentryid", sourceBillEntryId, index);  // 销售订单分录ID

            saleModel.setValue("srcsysbillentryid", midBillEntryInfo.getPkValue(), index);  // 来源系统单据分录ID
            saleModel.setValue("srcsysbillno", midBillInfo.getString("billno"), index);  // 来源系统单据编号
            saleModel.setValue("srcsysbillid", billId, index);  // 来源系统单据ID
            saleModel.setValue("srcbillid", billId, index);  // 来源单据ID
            saleModel.setValue("srcbillentryid", midBillEntryInfo.getPkValue(), index);  // 来源单据行ID
            saleModel.setValue("srcbillentryseq", midBillEntryInfo.get("seq"), index);  // 来源单据分录序号
            saleModel.setValue("srcbillnumber", midBillInfo.getString("billno"), index);  // 来源单据分录序号
            // update by hst 2023/02/02 PCP退货单若价税合计为0，则标记为赠品
            if ("PCP".equals(sourceFunction) && "2101".equals(transNo) && BigDecimal.ZERO.compareTo(actuallyAmount) == 0) {
                saleModel.setValue("ispresent",true, index);
            }

            saleModel.setValue("yd_totaleasamount", totalAmount, index);  // update by hst 2023/12/08 应付金额(折前价税合计)
            saleModel.setValue("yd_activityamount", activityAmount, index);  // update by hst 2023/12/08 促销活动折扣
            saleModel.setValue("yd_rebateamount", rebateAmount, index);  // update by hst 2023/12/08 销售返利折扣

            // update by hst 2023/12/18 促销活动折扣率\销售返利折扣率 计算
            if (Objects.nonNull(totalAmount) && totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (Objects.nonNull(activityAmount) && activityAmount.compareTo(BigDecimal.ZERO) > 0) {
                    saleModel.setValue("yd_activityrate", (activityAmount.divide(totalAmount,4, BigDecimal.ROUND_HALF_UP))
                            .multiply(new BigDecimal("100")).setScale(2), index);  // update by hst 2023/12/18 促销活动折扣率
                }
                if (Objects.nonNull(rebateAmount) && rebateAmount.compareTo(BigDecimal.ZERO) > 0) {
                    saleModel.setValue("yd_rebaterate", (rebateAmount.divide(totalAmount,4, BigDecimal.ROUND_HALF_UP))
                            .multiply(new BigDecimal("100")).setScale(2), index);  // update by hst 2023/12/18 销售返利折扣率
                }
                if (Objects.nonNull(activityAmount) && Objects.nonNull(rebateAmount)) {
                    if ((activityAmount.add(rebateAmount)).compareTo(totalAmount) == 0) {
                        saleModel.setValue("yd_isdiscount", true, index);  // update by hst 2023/12/18 折扣品

                    }
                }
            }
        }

        try {
            if (midBillEntryCol.size() > 0) {
                // 触发保存功能
                OperationResult result = ABillServiceHelper.saveOperate(saleView);
                if (result.isSuccess()) {
                    // 生成成功，设置下游已经生成销售出库单
                    midBillInfo.set("yd_ishasnext", true);
                    midBillInfo.set("yd_salebillnumber", saleModel.getValue("billno"));
                    midBillInfo.set("yd_errmessage", ""); // 错误信息重置为空
                    BizHelper.saveDynamicObject(midBillInfo);
                    // 返回下游销售出库单的ID
                    return (Long) result.getSuccessPkIds().get(0);
                }
                String errorMessage = BizHelper.getOperationErrorMessage(result);
                if (StringUtils.isNotBlank(errorMessage)) {
                    throw new KDBizException("销售出库单["+saleModel.getValue("billno")+"]保存失败，查看保存失败原因："+errorMessage);
                }
            }
            throw new KDBizException("销售中间表明细不能为空！");
        }finally {
            ABillServiceHelper.exitView(saleView);
        }
    }

    /**
     * 转换销售出库单，单据信息校验
     * @param context 上下文
     * @param targetId 销售出库单ID
     * <AUTHOR>
     * @date 2022-6-15
     */
    private static void transSaleOutBill_audit(Map<String, Object> context, long targetId) {
        DynamicObject info = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_SALEOUTBILL, targetId, "billno");
        String billNo = info.getString("billno");// 单据编码
        OperationResult result = BizHelper.operate("submit", BillTypeHelper.BILLTYPE_SALEOUTBILL, new Object[]{targetId});
        if (!result.isSuccess()) {
            // 提交出错信息记录
            String errMessage = new StringBuilder("销售出库单[").append(billNo).append("]生成成功，但提交失败，请到销售出库单手动提交，查看提交失败原因：").append(result.getMessage()).toString();
            throw new KDBizException(errMessage);
        }
        result = BizHelper.operate("audit", BillTypeHelper.BILLTYPE_SALEOUTBILL, new Object[]{targetId});
        if (!result.isSuccess()) {
            // 审核出错信息记录
            String errMessage = new StringBuilder("销售出库单[").append(billNo).append("]提交成功，但审核失败，请到销售出库单手动审核，查看审核失败原因：").append(result.getMessage()).toString();
            throw new KDBizException(errMessage);
        }
    }

    /**
     * 转换销售出库单，单据信息校验
     * @param context 上下文
     * @param billId 销售出库单中间表ID
     * @param targetId 销售出库单ID
     * <AUTHOR>
     * @date 2022-6-15
     */
    private static void transSaleOutBill_relation(Map<String, Object> context, long billId, long targetId) {
        BotpUtils.createRelation(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId, BillTypeHelper.BILLTYPE_SALEOUTBILL, targetId);
    }

    /**
     * 执行调拨单的结算（调度任务 / 列表的执行）
     * @param idList 需要执行的ID集合
     * @param dealDate 执行的日期
     * @param isTask 是否是后台事务调用
     * <AUTHOR>
     * @date 2022-7-19
     */
    public static void doTransSettle(List<Object> idList, Date dealDate, boolean isTask) {

        DataSet midDataSet = null;

        try {
            // 1、获取已经审核还未结算的库存调拨单中间表
            String midBillType = BillTypeHelper.BILLTYPE_MIDTRANSBILL;
            String operationKey = "doTransSettle";
            QFilter qFilter = QFilter.of("billstatus = 'C' and yd_issettle = 0 and yd_ishasnext = 0");

            // 如果处理日期不为空，则按日期查询
            if (dealDate != null) {
                Date beginDate = DateTimeUtils.format2(dealDate);
                Date endDate = DateTimeUtils.addDate(beginDate, 1);
                qFilter.and(QFilter.of("yd_bizdate >= ? and yd_bizdate < ?", beginDate, endDate));
            }
            // 按ID过滤查询
            if (idList != null && idList.size() > 0) {
                qFilter.and(QueryUtil.qf("id", QCP.in, idList));
            }
            // 判断是否是调度任务执行
            if (isTask) {
                // 如果是后台事务处理，如果处理有异常，则不在调度任务的处理范围
                qFilter.and(QueryUtil.qf("yd_settleerror", ""));
            }
//            // 测试环境测试
//            qFilter.and(QueryUtil.qf("billno", "PSR20220513000003"));
            midDataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), midBillType, "id", qFilter.toArray());
            // 不存在可执行的数据，则执行退出
            if (!midDataSet.hasNext()) return;

            // 上下文处理
            Map<String, Object> context = new HashMap<>();
            for (Row midRow : midDataSet) {
                // 获取待处理的销售出库单中间表ID
                long billId = midRow.getLong("id");
                // 校验单据是否已经锁定
                if (BizHelper.isBillLock(midBillType, billId, operationKey)) continue;

                try {
                    // 对处理的单据进行加锁处理
                    BizHelper.requestLock(midBillType, billId, operationKey);
                    // 2、数据校验，如果有异常则将异常信息反写到单据上
                    doTransSettle_verify(context, billId);
                    // 3、执行结算功能
                    doTransSettle_settle(context, billId);
                }catch (Exception err) {
                    err.printStackTrace();
                    // 记录异常信息反写到单据字段信息中
                    DynamicObject midBillInfo = BizHelper.getDynamicObjectById(midBillType, billId, "yd_settleerror");
                    String errMessage = err.getMessage();
                    // 如果字段过长，则截取长度
                    if (errMessage != null && errMessage.length() > 2000) {
                        errMessage = errMessage.substring(0, 2000);
                    }
                    midBillInfo.set("yd_settleerror", errMessage);
                    BizHelper.save(midBillInfo);
                }finally {
                    // 无论是否处理成功都需要释放锁
                    BizHelper.releaseLock(midBillType, billId, operationKey);
                }
            }
        }finally {
            ORMUtils.close(midDataSet);
        }
    }

    /**
     * 调拨单执行结算处理
     * @param context 上下文
     * @param billId 执行的调拨单单据ID
     * <AUTHOR>
     * @date 2022-7-19
     */
    private static void doTransSettle_verify(Map<String, Object> context, long billId) {

        StringBuilder errBuilder = new StringBuilder();
        int errIndex = 0;
        // 1、表头分录数据必填性校验
        // 表头：判断 调出组织、调入组织、调出仓库、调入仓库是否存在，如果存在，则将对应的数据反写到单据上
        // 分录，校验 批次、生产日期、到期日、数量 是头填写，如果没有填写，则提示
//        String selector = "billno, yd_bizdate, yd_outorg, yd_interoutorg, yd_inorg, yd_interinorg, yd_outwarehouse, yd_interoutwarehouse, yd_inwarehouse, yd_interinwarehouse, yd_sourcefunction, yd_isinter, yd_description, yd_issettle, yd_istoeas, yd_settleerror, entryentity.yd_intermaterial, entryentity.yd_intermaterialname, entryentity.yd_material, entryentity.yd_batchno, entryentity.yd_probegindate, entryentity.yd_proenddate, entryentity.yd_quantity";
        DynamicObject midBillInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDTRANSBILL, billId);
        boolean isInter = midBillInfo.getBoolean("yd_isinter"); // 是否接口推送
        DynamicObjectCollection entryCol = midBillInfo.getDynamicObjectCollection("entryentity"); // 销售出库单中间表分录

        DynamicObject outOrgInfo;
        DynamicObject inOrgInfo;

        // 如果是接口推送的字段，则需要校验中台是否存在对应的值匹配
        if (isInter) {
            // 校验必填字段，如果不符合则直接提示跳出
            // 校验表头字段信息
            String headWarning = InterUtils.verifyFieldIsNull(midBillInfo, new String[]{"billno", "yd_bizdate", "yd_interoutorg", "yd_interinorg", "yd_interoutwarehouse", "yd_interinwarehouse"});
            // 校验分录表头信息
            String entryWarning = InterUtils.verifyFieldIsNull(midBillInfo, "entryentity", new String[]{"yd_intermaterial", "yd_intermaterialname", "yd_batchno", "yd_quantity"});
            if (StringUtils.isNotBlank(headWarning) || StringUtils.isNotBlank(entryWarning)) {
                throw new KDBizException(new StringBuilder().append(headWarning).append("\n").append(entryWarning).toString());
            }
            // 2. 根据组织的映射关系，校验PCP组织是否已经存在映射
            String interOutOrg = midBillInfo.getString("yd_interoutorg"); // 调出组织
            String interInOrg = midBillInfo.getString("yd_interinorg"); // 调入组织
            String interOutWarehouse = midBillInfo.getString("yd_interoutwarehouse"); // 调出仓库
            String interInWarehouse = midBillInfo.getString("yd_interinwarehouse"); // 调入仓库

            // 调出组织
            outOrgInfo = getOrgByPcpOrg(interOutOrg);
            // 调入组织
            inOrgInfo = getOrgByPcpOrg(interInOrg);
            // 调出仓库
            DynamicObject outWareInfo = getWarehouseByNumber(interOutWarehouse);
            // 调入仓库
            DynamicObject inWareInfo = getWarehouseByNumber(interInWarehouse);

            // 校验调出组织、调入组织、调出仓库、调入仓库是否存在
            if (outOrgInfo == null)
                errBuilder.append(++errIndex).append("、[").append(interOutOrg).append("]不存在组织映射关系！\n");
            if (inOrgInfo == null)
                errBuilder.append(++errIndex).append("、[").append(interInOrg).append("]不存在组织映射关系！\n");
            if (outWareInfo == null)
                errBuilder.append(++errIndex).append("、[").append(interOutWarehouse).append("]不存在仓库信息！\n");
            if (inWareInfo == null)
                errBuilder.append(++errIndex).append("、[").append(interInWarehouse).append("]不存在仓库信息！\n");

            midBillInfo.set("yd_outorg", outOrgInfo); // 调出组织
            midBillInfo.set("yd_inorg", inOrgInfo); // 调入组织
            midBillInfo.set("yd_outwarehouse", outWareInfo); // 调出仓库
            midBillInfo.set("yd_inwarehouse", inWareInfo); // 调入仓库

            // 校验分录的物料是否存在
            for (DynamicObject entryInfo: entryCol) {
                String materialNumber = entryInfo.getString("yd_intermaterial"); // 接口推送物料\
                // 查找物料信息
                DynamicObject materialInfo = getMaterialByNumber(materialNumber);
                if (materialInfo == null) errBuilder.append(++errIndex).append("、[").append(materialNumber).append("]不存在物料信息！\n");
                entryInfo.set("yd_material", materialInfo);
            }
        }else {
            outOrgInfo = midBillInfo.getDynamicObject("yd_outorg"); // 调出组织
            inOrgInfo = midBillInfo.getDynamicObject("yd_inorg"); // 调入组织
        }

        // 校验分录是否存在
        if (entryCol.size() == 0) errBuilder.append(++errIndex).append("、调拨单中间表明细不能为空！\n");

        // 如果存在异常，则返回
        if (errBuilder.length() > 0) throw new KDBizException(errBuilder.toString());

        // 3. 校验结算路径、内部交易表是否存在
        String outOrgNumber = outOrgInfo.getString("number"); // 调出组织编码
        String inOrgNumber = inOrgInfo.getString("number"); // 调入组织编码

        // 查找结算关系
        DynamicObject settleRelInfo = getTransRelationByOrg(outOrgNumber, inOrgNumber);
        if (settleRelInfo == null) {
            errBuilder.append(++errIndex).append("、不存在[").append(outOrgNumber).append("]与[").append(inOrgNumber).append("]组织间调拨的结算关系！\n");
            throw new KDBizException(errBuilder.toString());
        }

        DynamicObjectCollection orgRelEntry = settleRelInfo.getDynamicObjectCollection("yd_entryentity");
        // 遍历结算路径，校验组织交易表是否存在
        boolean isForward = false;
        if ("210".equals(settleRelInfo.getDynamicObject("yd_biztype").getString("number"))) isForward = true;
        Date bizDate = midBillInfo.getDate("yd_bizdate"); // 日期

        // 物料编码
        Map<String, String> materialNumberMap = entryCol.stream().map(item -> item.getDynamicObject("yd_material")).collect(Collectors.toMap(k->k.getString("id"), v->v.getString("number"), (a,b)->a));

        // 获取组织仓库对应关系
        Map<String, String> orgWarehouseMap = InternalSettleServiceHelper.getOrgWarehouseMap();

        // 遍历结算关系表
        for (int index = 0; index < orgRelEntry.size()-1; index++) {
            DynamicObject bfOrgInfo = orgRelEntry.get(index).getDynamicObject("yd_org");
            DynamicObject afOrgInfo = orgRelEntry.get(index+1).getDynamicObject("yd_org");

            String srcOrgId, destOrgId;

            if (isForward) {
                srcOrgId = bfOrgInfo.getString("id");
                destOrgId = afOrgInfo.getString("id");
            }else {
                srcOrgId = afOrgInfo.getString("id");
                destOrgId = bfOrgInfo.getString("id");
            }
            Map<String, BigDecimal> matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(srcOrgId, destOrgId, bizDate);

            // 查询调出组织、调入组织是否存在对应的内部客户信息
            DynamicObject toCustomer = getCustomerByInternalCompany(afOrgInfo.getString("number")); // 调入组织对应的客户信息
            if (toCustomer == null) errBuilder.append(++errIndex).append("、[").append(afOrgInfo.getString("number")).append("]不存在内部客户信息！\n");
            // 查询是否存在供应商信息
            DynamicObject fromSupplierInfo = getSupplierByInternalCompany(bfOrgInfo.getString("number"));// 按调出组织查询供应商是否存在
            if (fromSupplierInfo == null) errBuilder.append(++errIndex).append("、[").append(bfOrgInfo.getString("number")).append("]不存在内部供应商信息！\n");
            // 查询调入调出组织是否存在对应的仓库信息
            if (index != 0) {
                String warehouseNumber = orgWarehouseMap.get(bfOrgInfo.getString("id"));
                if (StringUtils.isBlank(warehouseNumber)) errBuilder.append(++errIndex).append("、[").append(bfOrgInfo.getString("number")).append("]不存在组织仓库对应关系！\n");
            }
            if (index != orgRelEntry.size() - 2) {
                String warehouseNumber = orgWarehouseMap.get(afOrgInfo.getString("id"));
                if (StringUtils.isBlank(warehouseNumber)) errBuilder.append(++errIndex).append("、[").append(afOrgInfo.getString("number")).append("]不存在组织仓库对应关系！\n");
            }

            // 校验推送的物料是否存在对应组织的价格表
            StringBuilder materialErrorBuilder = new StringBuilder();
            for (String materialId : materialNumberMap.keySet()) {
                String materialNumber = materialNumberMap.get(materialId);
                if (!matPriceMap.containsKey(materialId) && materialErrorBuilder.indexOf(materialNumber) == -1) {
                    materialErrorBuilder.append(materialNumber).append("、");
                }
            }
            if (materialErrorBuilder.length() > 0) errBuilder.append(++errIndex).append("、").append(materialErrorBuilder.delete(materialErrorBuilder.length()-1, materialErrorBuilder.length()).insert(0, "不存在物料为[").append("]的内部组织交易价格表！").toString());
            if (errBuilder.length() > 0) throw new KDBizException(errBuilder.toString());
            // 存储两个组织直接的结算价格表信息
            context.put(bfOrgInfo.getString("number")+"_"+afOrgInfo.getString("number")+"_matPriceMap", matPriceMap);
        }

        // 校验库存信息
        StringBuilder lotErrorD = new StringBuilder();
        // 如果是正向，则采用出库的仓库校验库存，如果是退货，则采用入库的仓库进行校验库存
        // 股份 -> 麦优，需要校验股份是否存在库存
        // 麦优 -> 股份，因为麦优可能是不管理批次，所以需要按股份进行校验
        // update by hst 2023/11/27 无论正向、反向，都采用出库的仓库
//        String wsNum = isForward ? midBillInfo.getString("yd_outwarehouse.number"):midBillInfo.getString("yd_inwarehouse.number");
        String wsNum = midBillInfo.getString("yd_outwarehouse.number");
        for (DynamicObject entryObj : entryCol) {
            // 校验是否存在对应的批次数据
            String lot = entryObj.getString("yd_batchno");
            String materialNum = entryObj.getString("yd_material.number");
            Date beginDate = entryObj.getDate("yd_probegindate");
            Date endDate = entryObj.getDate("yd_proenddate");

            // 如果生产的日期为空，都需要从即时库存中查找
            if (beginDate==null || endDate == null) {
                // 按批次+物料+仓库，如果没有查找到，正向执行提示异常，如果是红字，则需要再次查找按物料+仓库查找，没有则提示异常
                Object[] matLogInfo = getMaterialLotInfo(lot, materialNum, wsNum, true);
                if (matLogInfo == null) {
                    matLogInfo = getMaterialLotInfo(null, materialNum, wsNum, true);
                    if (matLogInfo == null) {
                        if (lotErrorD.indexOf(materialNum+"的批次"+lot) < 0) {
                            lotErrorD.append("仓库编码"+wsNum+"的"+materialNum+"的批次"+lot+"，");
                        }
                    }
                }
                if (matLogInfo != null) {
                    entryObj.set("yd_batchno", matLogInfo[0]);
                    entryObj.set("yd_probegindate", matLogInfo[1]);
                    entryObj.set("yd_proenddate", matLogInfo[2]);
                }
            }
        }
        if (lotErrorD.length() > 0) lotErrorD.insert(0, "以下物料对应的批次不存在即时库存数据：").append("；");
        // 添加的异常信息中
        errBuilder.append(lotErrorD);
        if (errBuilder.length() > 0) throw new KDBizException(errBuilder.toString());

        // 将数据放入到上下文中，便于后续的操作使用
        context.put("orgWarehouseMap", orgWarehouseMap); // 组织仓库对应关系
        context.put("settleRelInfo", settleRelInfo); // 结算关系
        context.put("midBillInfo", midBillInfo); // 调拨单中间表
    }

    /**
     * 校验没有问题后，执行结算处理
     * @param context 上下文
     * @param billId 执行的调拨单单据ID
     * <AUTHOR>
     * @date 2022-7-19
     */
    private static void doTransSettle_settle(Map<String, Object> context, long billId) {
        // 获取context缓存的数据
        DynamicObject midBillInfo = (DynamicObject) context.get("midBillInfo"); // 调拨单中间表
        DynamicObject settleRelInfo = (DynamicObject) context.get("settleRelInfo"); // 结算关系
        // 保存对应的值
        BizHelper.saveDynamicObject(midBillInfo);
        // 执行结算的处理
        // 结算路径分两种，一种是正向的调拨，一种是逆向的红字调拨
        String transNo = settleRelInfo.getDynamicObject("yd_biztype").getString("number");// 获取结算路径的事务类型
        boolean isForward = "210".equals(transNo); // 是否为正向
        if (isForward) {
            // 正向
            // 根据中间表先生成股份的销售出库单（仓库取PCP传递的调出仓库）
            //      再生成药业的采购入库单（仓库取默认共享仓），
            //      再生成药业的销售出库单（仓库取默认共享仓），
            //      最后生成麦优的采购入库单（仓库取PCP传递的调入仓库），整个流程涉及的单据都是自动提交审核
            doTransSettle_settle_forward(context);
            // 保存关系
            if (context.containsKey("firstSaleOutBillId")) {
                InternalSettleServiceHelper.saveBotpBills((String) context.get("firstSaleOutBillId"));
            }
        }else {
            // 逆向
            // 根据中间表先生成麦优的采购入库单（红字）（仓库取PCP传递的调出仓库），
            //      再生成药业的销售出库单（红字）（仓库取默认共享仓），
            //      再生成药业的采购入库单（红字）（仓库取默认共享仓），
            //      最后生成股份的销售出库单（红字）（仓库取PCP传递的调入仓库），整个流程涉及的单据都是自动提交审核
            doTransSettle_settle_reverse(context);
        }
    }

    private static void doTransSettle_settle_forward(Map<String, Object> context) {

        // 获取context缓存的数据
        DynamicObject midBillInfo = (DynamicObject) context.get("midBillInfo"); // 调拨单中间表
        DynamicObject settleRelInfo = (DynamicObject) context.get("settleRelInfo"); // 结算关系
        DynamicObjectCollection settleRelEntryCol = settleRelInfo.getDynamicObjectCollection("yd_entryentity");

        String[][] dealRules = new String[][] {
                {"im_saloutbill", "im_purinbill", "1308580694741490688"}, // ISM预制转换规则_销售出库单推下级组织采购入库单_正向
                {"im_purinbill", "im_saloutbill", "1308580938774484992"}, // ISM预制转换规则_采购入库单推本级销售出库单_正向
        };

        // 调拨单中间表 => 销售出库单（正向、蓝字）
        IFormView saleView = ABillServiceHelper.createAddView(BillTypeHelper.BILLTYPE_SALEOUTBILL);
        IDataModel saleModel = saleView.getModel();

        DynamicObject onOrgInfo = midBillInfo.getDynamicObject("yd_outorg"); // 调出组织
        DynamicObject inOrgInfo = midBillInfo.getDynamicObject("yd_inorg"); // 调入组织
        DynamicObject outWarehouse = midBillInfo.getDynamicObject("yd_outwarehouse"); // 调出仓库
        DynamicObject inWarehouse = midBillInfo.getDynamicObject("yd_inwarehouse"); // 调出仓库
        Date bizDate = midBillInfo.getDate("yd_bizdate"); // 业务日期
        String description = midBillInfo.getString("yd_description"); // 备注
        String sourceFunction = "OCS".equals(midBillInfo.getString("yd_sourcefunction"))?"4":null; // 来源单据类型，如果是手工，则值为null

        // 获取组织所对应的
        DynamicObject fromOrgInfo = onOrgInfo;
        DynamicObject toOrgInfo = settleRelEntryCol.get(1).getDynamicObject("yd_org"); // 结算的对方公司
        // 从对方公司中获取客户信息
        DynamicObject toCustomerInfo = getCustomerByInternalCompany(toOrgInfo.getString("number"));

        // 获取物料的价格
        Map<String, BigDecimal> matPriceMap = (Map<String, BigDecimal>) context.get(onOrgInfo.getString("number") + "_" + toOrgInfo.getString("number")+ "_matPriceMap");

        saleModel.setItemValueByNumber("billtype", "im_SalOutBill_STD_BT_S"); // 单据类型（标准销售出库单）
        saleModel.setValue("org", fromOrgInfo); // 库存组织
        saleModel.setValue("bizorg", fromOrgInfo); // 销售组织
        saleModel.setItemValueByNumber("biztype", "210"); // 业务类型
        saleModel.setItemValueByNumber("invscheme", "210"); // 库存事务
        saleModel.setValue("customer", toCustomerInfo); // 客户
        saleModel.setValue("yd_dzdkh", toCustomerInfo); // 对账客户
        saleModel.setValue("biztime", bizDate); // 业务日期
        saleModel.setValue("yd_tbly", sourceFunction); // 同步来源系统
        saleModel.setValue("yd_sourcebilltype", "4");  // 来源单据类型 —— PCP库存调拨单
        saleModel.setValue("comment", description); // 备注
        saleModel.setValue("yd_firstinternalbill", true); // 首张结算单
        saleModel.setValue("yd_platformorderno", midBillInfo.getString("billno")); // lzp，2023-04-19，库存调拨单号 => 外部系统单号
        saleModel.setValue("yd_ocsresultnumber", midBillInfo.getString("yd_ocs_dsnumber")); // lzp，2023-04-19，OCS结果单号 => 外部系统单号
        DynamicObjectCollection midBillEntryCol = midBillInfo.getDynamicObjectCollection("entryentity");
        for (int index=0; index<midBillEntryCol.size(); index++) {
            DynamicObject midBillEntryInfo = midBillEntryCol.get(index);
            DynamicObject materialInfo = midBillEntryInfo.getDynamicObject("yd_material"); // 物料
            String batchNo = midBillEntryInfo.getString("yd_batchno"); // 批号
            Date proBeginDate = midBillEntryInfo.getDate("yd_probegindate"); // 生产日期
            Date proEndDate = midBillEntryInfo.getDate("yd_proenddate"); // 到期日
            BigDecimal quantity = midBillEntryInfo.getBigDecimal("yd_quantity"); // 数量
            // 获取物料编码
            String materialId = materialInfo.getString("id");
            // 获取价格
            BigDecimal taxPrice = matPriceMap.get(materialId);

            // 基础资料
            String taxRateNum = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_CUSTOMER, "taxrate.number", QFilter.of("status='C' and number=?", toCustomerInfo.getString("number")).toArray());
            BigDecimal taxAmount = taxPrice.multiply(quantity).setScale(2, 4);
            if (index == saleModel.getEntryRowCount("billentry")) {
                saleModel.createNewEntryRow("billentry");
            }
            saleModel.setItemValueByNumber("material", materialInfo.getString("number"), index); // 物料
            saleModel.setValue("yd_ph", batchNo, index); // E3批号
            saleModel.setValue("yd_scrq", proBeginDate, index); // E3生产日期
            saleModel.setValue("yd_dqr", proEndDate, index); // E3到期日
            saleModel.setValue("qty", quantity, index); // 数量
            saleModel.setValue("priceandtax", taxPrice, index);
            saleModel.setValue("amountandtax", taxAmount, index);
            saleModel.setItemValueByNumber("taxrateid", taxRateNum, index); // 税率
            saleModel.setValue("warehouse", outWarehouse, index); // 仓库（按调出仓库设置）
            saleModel.setValue("yd_e3oriwarehouse", outWarehouse, index); // 原仓库
//            // 是否为赠品
//            if (taxAmount.compareTo(BigDecimal.ZERO) == 0) {
//                saleModel.setValue("ispresent", "1", index);
//            }
            saleModel.setValue("srcsysbillentryid", midBillEntryInfo.getPkValue(), index);  // 来源系统单据分录ID
            saleModel.setValue("srcsysbillno", midBillInfo.getString("billno"), index);  // 来源系统单据编号
            saleModel.setValue("srcsysbillid", midBillInfo.getPkValue(), index);  // 来源系统单据ID
            saleModel.setValue("srcbillid", midBillInfo.getPkValue(), index);  // 来源单据ID
            saleModel.setValue("srcbillentryid", midBillEntryInfo.getPkValue(), index);  // 来源单据行ID
            saleModel.setValue("srcbillentryseq", midBillEntryInfo.get("seq"), index);  // 来源单据分录序号
            saleModel.setValue("srcbillnumber", midBillInfo.getString("billno"), index);  // 来源单据分录序号
            saleModel.setValue("yd_sourceentryid",midBillEntryInfo.getString("id"), index);    // update by hst 2024/01/31 来源分录id
        }
        // 对生成的销售出库单进行数据保存、提交、审批
        R result = saveAndAuditBill(BillTypeHelper.BILLTYPE_SALEOUTBILL, saleView);
        if (result.get("billId") != null) {
            // 设置关联关系
            String billNumber = (String) saleModel.getValue("billno");
            // 保存下游单据编码到单据中
            midBillInfo.set("yd_ishasnext", true); // 是否有下游单号
            midBillInfo.set("yd_nextbillno", billNumber);
            BizHelper.save(midBillInfo);
        }
        if (result.getCode() != 0) throw new KDBizException(result.getMessage());
        Object saleId = result.get("billId"); // 获取已经审批通过的销售出库单，进行生成下游的结算单
        // 将第一张销售出库单的ID保存到上下文中
        context.put("firstSaleOutBillId", String.valueOf(saleId));
        // 如果数据已经保存成功，则保存对应的关联关系
        BotpUtils.createRelation(BillTypeHelper.BILLTYPE_MIDTRANSBILL, midBillInfo.getLong("id"), BillTypeHelper.BILLTYPE_SALEOUTBILL, (Long) saleId);

        DynamicObject fromSupplierInfo; // 供应商信息
        // 根据结算路径进行生成下游单据
        SaleOutBillMserviceImpl service = new SaleOutBillMserviceImpl(); // 方便调用对应的方法
//        // 加载组织对应的仓库的数据
        Map<String, String> orgWarehouseMap = (Map<String, String>) context.get("orgWarehouseMap");

        boolean isLastDeal; // 是否到结算关系的最后一环
        for (int index = 1; index < settleRelEntryCol.size(); index++) {

            isLastDeal = index == settleRelEntryCol.size() - 1; // 是否最后一轮执行
            fromOrgInfo = settleRelEntryCol.get(index-1).getDynamicObject("yd_org"); // 源组织
            toOrgInfo = settleRelEntryCol.get(index).getDynamicObject("yd_org"); // 目标组织
            // 获取对应的供应商信息
            fromSupplierInfo = getSupplierByInternalCompany(fromOrgInfo.getString("number")); // 供应商信息
            // 获取两个组织之间的销售价格表
            matPriceMap = (Map<String, BigDecimal>) context.get(fromOrgInfo.getString("number")+"_"+toOrgInfo.getString("number")+"_matPriceMap");

            // 设置苍穹信息
            DynamicObject warehouseInfo = null;
            if (isLastDeal) {
                warehouseInfo = inWarehouse;
            }else {
                // 获取对应的默认仓库信息
                String warehouseId = orgWarehouseMap.get(toOrgInfo.getString("id"));
                // 获取仓库的信息
                warehouseInfo = BizHelper.createDynamicObject(BillTypeHelper.BILLTYPE_WAREHOUSE);
                warehouseInfo.set("id", Long.parseLong(warehouseId));
            }

            String[] currentRule = dealRules[0];
            // 调用转换规则，生成下游的采购入库单
            // 调用转换规则，生成采购入库单
            result = ConvertHelper.push(currentRule[0], currentRule[1], new ListSelectedRow(saleId), currentRule[2]); // 根据转换规则生成数据
            // 如果转换失败，则提示异常信息
            ConvertOperationResult pushResult = ConvertHelper.getPushResult(result);
            if (result.getCode() != 0) throw new KDBizException(pushResult.getMessage());
            List<DynamicObject> targetBillObjs = (List<DynamicObject>) result.get("targetBillObjs");
            DynamicObject purInfo = targetBillObjs.get(0); // 获取下游的采购入库单
            // 获取下游的采购入库单，对数据进行审批
            // 对应数据进行赋值
            RequestMap param = new RequestMap()
                    .put("service", service)
                    .put("toOrgInfo", toOrgInfo)
                    .put("fromSupplierInfo", fromSupplierInfo)
                    .put("warehouseInfo", warehouseInfo)
                    .put("matPriceMap", matPriceMap);
            resetSaleOrPurValue(purInfo, param);
            purInfo.set("yd_platformorderno", midBillInfo.getString("billno")); // lzp，2023-04-19，库存调拨单号 => 外部系统单号
            purInfo.set("yd_ocsresultnumber", midBillInfo.getString("yd_ocs_rsnumber")); // lzp，2023-04-19，OCS结果单号 => 外部系统单号
            // 执行完对字段的赋值，则对单据进行保存且审核
            result = saveAndAuditBill(currentRule[1], purInfo);
            if (result.getCode() != 0) throw new KDBizException(result.getMessage());
            // 获取采购入库单ID
            Object purId = purInfo.getPkValue();

            // 调用转换规则，生成采购入库单下级的销售出库单
            // 如果当前的index为size-1，则不需要生成对应的销售出库单
            if (isLastDeal) break; // 如果最后一轮已经入库，则不需要再往下级生成出入库单

            fromOrgInfo = settleRelEntryCol.get(index).getDynamicObject("yd_org"); // 源组织
            toOrgInfo = settleRelEntryCol.get(index+1).getDynamicObject("yd_org"); // 目标组织
            // 获取对应的供应商信息
            toCustomerInfo = getCustomerByInternalCompany(toOrgInfo.getString("number")); // 供应商信息
            // 获取两个组织之间的销售价格表
            matPriceMap = (Map<String, BigDecimal>) context.get(fromOrgInfo.getString("number")+"_"+toOrgInfo.getString("number")+"_matPriceMap");
            // 仓库信息
            // 获取对应的默认仓库信息
            String warehouseId = orgWarehouseMap.get(fromOrgInfo.getString("id"));
            // 获取仓库的信息
            warehouseInfo = BizHelper.createDynamicObject(BillTypeHelper.BILLTYPE_WAREHOUSE);
            warehouseInfo.set("id", Long.parseLong(warehouseId));

            // 调用规则转换生成
            currentRule = dealRules[1]; // 规则指向第二个规则
            result = ConvertHelper.push(currentRule[0], currentRule[1], new ListSelectedRow(purId), currentRule[2]);
            pushResult = ConvertHelper.getPushResult(result);
            if (result.getCode() != 0) throw new KDBizException(pushResult.getMessage());
            targetBillObjs = (List<DynamicObject>) result.get("targetBillObjs");
            DynamicObject saleInfo = targetBillObjs.get(0); // 获取生成的销售出库单，获取对应的ID值
            // 获取下游的采购入库单，对数据进行审批
            // 需要重置仓库和调入调出组织，以及价格数据重算
            param = new RequestMap()
                    .put("service", service)
                    .put("fromOrgInfo", fromOrgInfo)
                    .put("toCustomerInfo", toCustomerInfo)
                    .put("warehouseInfo", warehouseInfo)
                    .put("matPriceMap", matPriceMap);
            resetSaleOrPurValue(saleInfo, param);
            saleInfo.set("yd_platformorderno", midBillInfo.getString("billno")); // lzp，2023-04-19，库存调拨单号 => 外部系统单号
            saleInfo.set("yd_ocsresultnumber", midBillInfo.getString("yd_ocs_dsnumber")); // lzp，2023-04-19，OCS结果单号 => 外部系统单号
            saleInfo.set("yd_frome3", false); // 是否按品牌汇总
            saleInfo.set("yd_sourcebilltype", "4");  // 来源单据类型 —— PCP库存调拨单
            saleInfo.set("yd_tbly", sourceFunction); // 同步来源系统
            // 对数据进行保存和审批
            result = saveAndAuditBill(currentRule[1], saleInfo);
            // 如果执行过程中有异常信息，则返回异常信息
            if (result.getCode() != 0) throw new KDBizException(result.getMessage());
            // 获取销售单ID
            saleId = saleInfo.getPkValue();
        }
        // 执行完后，更新单据的是否已结算的状态
        midBillInfo.set("yd_issettle", true); // 是否已经结算
        midBillInfo.set("yd_settleerror", null) ;// 结算失败原因
        BizHelper.save(midBillInfo);
    }

    /**
     * 调拨单结算，逆向结算，退货流程结算
     * @param context 上下文信息
     * <AUTHOR>
     * @date 2022-7-27
     */
    private static void doTransSettle_settle_reverse(Map<String, Object> context) {

        // 获取context缓存的数据
        DynamicObject midBillInfo = (DynamicObject) context.get("midBillInfo"); // 调拨单中间表
        DynamicObject settleRelInfo = (DynamicObject) context.get("settleRelInfo"); // 结算关系
        DynamicObjectCollection settleRelEntryCol = settleRelInfo.getDynamicObjectCollection("yd_entryentity");

        String[][] dealRules = new String[][] {
                {"im_purinbill", "im_saloutbill", "1311726856189247488"}, // ISM预制转换规则_采购入库单推上级销售出库单_退货
                {"im_saloutbill", "im_purinbill", "1311714048017963008"}, // ISM预制转换规则_销售出库单推本级采购入库单_退货
        };

        // 调拨单中间表 => 销售出库单（正向、红字）
        // 调拨单中间表 => 销售出库单（正向、蓝字）
        IFormView purView = ABillServiceHelper.createAddView(BillTypeHelper.BILLTYPE_PURINBILL);
        IDataModel purModel = purView.getModel();

        DynamicObject onOrgInfo = midBillInfo.getDynamicObject("yd_outorg"); // 调出组织
        DynamicObject inOrgInfo = midBillInfo.getDynamicObject("yd_inorg"); // 调入组织
        DynamicObject outWarehouse = midBillInfo.getDynamicObject("yd_outwarehouse"); // 调出仓库
        DynamicObject inWarehouse = midBillInfo.getDynamicObject("yd_inwarehouse"); // 调出仓库
        Date bizDate = midBillInfo.getDate("yd_bizdate"); // 业务日期
        String description = midBillInfo.getString("yd_description"); // 备注
        String sourceFunction = "OCS".equals(midBillInfo.getString("yd_sourcefunction"))?"4":null; // 来源单据类型，如果是手工，则值为null

        // 获取组织所对应的
        DynamicObject fromOrgInfo = onOrgInfo;
        DynamicObject toOrgInfo = settleRelEntryCol.get(1).getDynamicObject("yd_org"); // 结算的对方公司
        // 从对方公司中获取客户信息
        DynamicObject fromCustomerInfo;
        DynamicObject toSupplierInfo = getSupplierByInternalCompany(toOrgInfo.getString("number"));

        // 获取物料的价格
        Map<String, BigDecimal> matPriceMap = (Map<String, BigDecimal>) context.get(onOrgInfo.getString("number") + "_" + toOrgInfo.getString("number") + "_matPriceMap");

        purModel.setItemValueByNumber("billtype", "im_PurInBill_STD_BT_S"); // 单据类型（标准采购入库单）
        purModel.setValue("org", fromOrgInfo); // 库存组织
        purModel.setValue("bizorg", fromOrgInfo); // 采购组织
        purModel.setValue("bizdept", fromOrgInfo); // 采购部门
        purModel.setValue("dept", fromOrgInfo); // 库存部门
        purModel.setValue("supplier", toSupplierInfo); // 供应商
        purModel.setItemValueByNumber("biztype", "1101"); // 业务类型（物料类采购退货）
        purModel.setItemValueByNumber("invscheme", "1101"); // 库存事务（普通采购退、补、换货）
        purModel.setValue("biztime", bizDate); // 业务日期
        purModel.setValue("comment", description); // 备注
        purModel.setValue("billcretype", 2); // 单据生成类型（手工生成：0 / 导入生成：1 / 后台生成：2 / webApi生成：3）
        purModel.setValue("yd_platformorderno", midBillInfo.getString("billno")); // lzp，2023-04-19，上游单号 => 外部系统单号
        purModel.setValue("yd_ocsresultnumber", midBillInfo.getString("yd_ocs_dsnumber")); // lzp，2023-04-19，OCS结果单号 => 外部系统单号

        DynamicObjectCollection midBillEntryCol = midBillInfo.getDynamicObjectCollection("entryentity");
        for (int index=0; index<midBillEntryCol.size(); index++) {
            DynamicObject midBillEntryInfo = midBillEntryCol.get(index);
            DynamicObject materialInfo = midBillEntryInfo.getDynamicObject("yd_material"); // 物料
            String batchNo = midBillEntryInfo.getString("yd_batchno"); // 批号
            Date proBeginDate = midBillEntryInfo.getDate("yd_probegindate"); // 生产日期
            Date proEndDate = midBillEntryInfo.getDate("yd_proenddate"); // 到期日
            BigDecimal quantity = midBillEntryInfo.getBigDecimal("yd_quantity"); // 数量
            // 获取物料编码
            String materialId = materialInfo.getString("id");
            // 获取价格
            BigDecimal taxPrice = matPriceMap.get(materialId);

            // 基础资料
            String taxRateNum = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_SUPPLIER, "taxrate.number", QFilter.of("status='C' and number=?", toSupplierInfo.getString("number")).toArray());
            BigDecimal taxAmount = taxPrice.multiply(quantity).setScale(2, 4);
            if (index == purModel.getEntryRowCount("billentry")) {
                purModel.createNewEntryRow("billentry");
            }
            purModel.setItemValueByNumber("material", materialInfo.getString("number"), index); // 物料
            purModel.setValue("yd_ph", batchNo, index); // E3批号
            purModel.setValue("yd_scrq", proBeginDate, index); // E3生产日期
            purModel.setValue("yd_dqr", proEndDate, index); // E3到期日
            purModel.setValue("qty", quantity, index); // 数量
            purModel.setValue("priceandtax", taxPrice, index);
            purModel.setValue("amountandtax", taxAmount, index);
            purModel.setItemValueByNumber("taxrateid", taxRateNum, index); // 税率
            purModel.setValue("warehouse", outWarehouse, index); // 仓库（按调出仓库设置）

//            // 是否为赠品
//            if (taxAmount.compareTo(BigDecimal.ZERO) == 0) {
//                saleModel.setValue("ispresent", "1", index);
//            }
            purModel.setValue("srcsysbillentryid", midBillEntryInfo.getPkValue(), index);  // 来源系统单据分录ID
            purModel.setValue("srcsysbillno", midBillInfo.getString("billno"), index);  // 来源系统单据编号
            purModel.setValue("srcsysbillid", midBillInfo.getPkValue(), index);  // 来源系统单据ID
            purModel.setValue("srcbillid", midBillInfo.getPkValue(), index);  // 来源单据ID
            purModel.setValue("srcbillentryid", midBillEntryInfo.getPkValue(), index);  // 来源单据行ID
            purModel.setValue("srcbillentryseq", midBillEntryInfo.get("seq"), index);  // 来源单据分录序号
            purModel.setValue("srcbillnumber", midBillInfo.getString("billno"), index);  // 来源单据分录序号
            purModel.setValue("yd_sourceentryid",midBillEntryInfo.getString("id"), index);    // update by hst 2024/01/31 来源分录id
        }
        // 对生成的销售出库单进行数据保存、提交、审批
        R result = saveAndAuditBill(BillTypeHelper.BILLTYPE_PURINBILL, purView);
        if (result.get("billId") != null) {
            // 设置关联关系
            String billNumber = (String) purModel.getValue("billno");
            // 保存下游单据编码到单据中
            midBillInfo.set("yd_ishasnext", true); // 是否有下游单号
            midBillInfo.set("yd_nextbillno", billNumber);
            BizHelper.save(midBillInfo);
        }
        if (result.getCode() != 0) throw new KDBizException(result.getMessage());
        Object purId = result.get("billId"); // 获取已经审批通过的销售出库单，进行生成下游的结算单
        resetReturnQtyFields(BillTypeHelper.BILLTYPE_PURINBILL, purId, settleRelEntryCol.size());
        // 如果数据已经保存成功，则保存对应的关联关系
        BotpUtils.createRelation(BillTypeHelper.BILLTYPE_MIDTRANSBILL, midBillInfo.getLong("id"), BillTypeHelper.BILLTYPE_PURINBILL, (Long) purId);

        // 根据结算路径进行生成下游单据
        SaleOutBillMserviceImpl service = new SaleOutBillMserviceImpl(); // 方便调用对应的方法
//        // 加载组织对应的仓库的数据
        Map<String, String> orgWarehouseMap = (Map<String, String>) context.get("orgWarehouseMap");

        boolean isLastDeal; // 是否到结算关系的最后一环
        for (int index = 1; index < settleRelEntryCol.size(); index++) {

            isLastDeal = index == settleRelEntryCol.size() - 1; // 是否最后一轮执行

            fromOrgInfo = settleRelEntryCol.get(index-1).getDynamicObject("yd_org"); // 源组织
            toOrgInfo = settleRelEntryCol.get(index).getDynamicObject("yd_org"); // 目标组织
            // 获取对应的供应商信息
            fromCustomerInfo = getCustomerByInternalCompany(fromOrgInfo.getString("number")); // 供应商信息
            // 获取两个组织之间的销售价格表
            matPriceMap = (Map<String, BigDecimal>) context.get(fromOrgInfo.getString("number")+"_"+toOrgInfo.getString("number")+"_matPriceMap");
            DynamicObject warehouseInfo;
            // 仓库信息
            if (isLastDeal) {
                warehouseInfo = inWarehouse;
            }else {
                // 获取对应的默认仓库信息
                String warehouseId = orgWarehouseMap.get(toOrgInfo.getString("id"));
                // 获取仓库的信息
                warehouseInfo = BizHelper.createDynamicObject(BillTypeHelper.BILLTYPE_WAREHOUSE);
                warehouseInfo.set("id", Long.parseLong(warehouseId));
            }

            // 调用规则转换生成
            String[] currentRule = dealRules[0]; // 规则指向第二个规则
            result = ConvertHelper.push(currentRule[0], currentRule[1], new ListSelectedRow(purId), currentRule[2]);
            ConvertOperationResult pushResult = ConvertHelper.getPushResult(result);
            if (result.getCode() != 0) throw new KDBizException(pushResult.getMessage());
            List<DynamicObject> targetBillObjs = (List<DynamicObject>) result.get("targetBillObjs");
            DynamicObject saleInfo = targetBillObjs.get(0); // 获取生成的销售出库单，获取对应的ID值
            // 获取下游的采购入库单，对数据进行审批
            // 需要重置仓库和调入调出组织，以及价格数据重算
            RequestMap param = new RequestMap()
                    .put("service", service)
                    .put("fromOrgInfo", toOrgInfo)
                    .put("toCustomerInfo", fromCustomerInfo)
                    .put("warehouseInfo", warehouseInfo)
                    .put("matPriceMap", matPriceMap);
            resetSaleOrPurValue(saleInfo, param);
            saleInfo.set("yd_platformorderno", midBillInfo.getString("billno")); // lzp，2023-04-19，库存调拨单号 => 外部系统单号
            saleInfo.set("yd_ocsresultnumber", midBillInfo.getString("yd_ocs_rsnumber")); // lzp，2023-04-19，OCS结果单号 => 外部系统单号
            saleInfo.set("yd_frome3", false); // 是否按品牌汇总
            saleInfo.set("yd_tbly", sourceFunction); // 同步来源系统
            saleInfo.set("yd_sourcebilltype", "4");  // 来源单据类型 —— PCP库存调拨单
            // 对数据进行保存和审批
            result = saveAndAuditBill(currentRule[1], saleInfo);
            // 如果执行过程中有异常信息，则返回异常信息
            if (result.getCode() != 0) throw new KDBizException(result.getMessage());
            Object saleId = saleInfo.getPkValue();
            resetReturnQtyFields(currentRule[1], saleId, settleRelEntryCol.size());

            // 调用转换规则，生成采购入库单下级的销售出库单
            // 如果当前的index为size-1，则不需要生成对应的销售出库单
            if (isLastDeal) break; // 如果最后一轮已经入库，则不需要再往下级生成出入库单

            fromOrgInfo = settleRelEntryCol.get(index).getDynamicObject("yd_org"); // 源组织
            toOrgInfo = settleRelEntryCol.get(index+1).getDynamicObject("yd_org"); // 目标组织
            // 获取对应的供应商信息
            toSupplierInfo = getSupplierByInternalCompany(toOrgInfo.getString("number")); // 供应商信息
            // 获取两个组织之间的销售价格表
            matPriceMap = (Map<String, BigDecimal>) context.get(fromOrgInfo.getString("number")+"_"+toOrgInfo.getString("number")+"_matPriceMap");

            // 设置苍穹信息
            // 获取对应的默认仓库信息
            String warehouseId = orgWarehouseMap.get(fromOrgInfo.getString("id"));
            // 获取仓库的信息
            warehouseInfo = BizHelper.createDynamicObject(BillTypeHelper.BILLTYPE_WAREHOUSE);
            warehouseInfo.set("id", Long.parseLong(warehouseId));

            currentRule = dealRules[1];
            // 调用转换规则，生成下游的采购入库单
            // 调用转换规则，生成采购入库单
            result = ConvertHelper.push(currentRule[0], currentRule[1], new ListSelectedRow(saleId), currentRule[2]); // 根据转换规则生成数据
            // 如果转换失败，则提示异常信息
            pushResult = ConvertHelper.getPushResult(result);
            if (result.getCode() != 0) throw new KDBizException(pushResult.getMessage());
            targetBillObjs = (List<DynamicObject>) result.get("targetBillObjs");
            // 获取下游的采购入库单，对数据进行审批
            DynamicObject purInfo = targetBillObjs.get(0);
            // 对应数据进行赋值
            param = new RequestMap()
                    .put("service", service)
                    .put("toOrgInfo", fromOrgInfo)
                    .put("fromSupplierInfo", toSupplierInfo)
                    .put("warehouseInfo", warehouseInfo)
                    .put("matPriceMap", matPriceMap);
            resetSaleOrPurValue(purInfo, param);
            purInfo.set("yd_platformorderno", midBillInfo.getString("billno")); // lzp，2023-04-19，库存调拨单号 => 外部系统单号
            purInfo.set("yd_ocsresultnumber", midBillInfo.getString("yd_ocs_dsnumber")); // lzp，2023-04-19，OCS结果单号 => 外部系统单号
            // 执行完对字段的赋值，则对单据进行保存且审核
            result = saveAndAuditBill(currentRule[1], purInfo);
            if (result.getCode() != 0) throw new KDBizException(result.getMessage());
            purId = purInfo.getPkValue();
            resetReturnQtyFields(currentRule[1], purId, settleRelEntryCol.size());
        }
        // 执行完后，更新单据的是否已结算的状态
        midBillInfo.set("yd_issettle", true); // 是否已经结算
        midBillInfo.set("yd_settleerror", null) ;// 结算失败原因
        BizHelper.save(midBillInfo);
    }

    /**
     * 重置累计退货数量等字段
     * <AUTHOR>
     * @date 2022-7-28
     */
    private static void resetReturnQtyFields(String billType, Object pkId, int size) {
        DynamicObject info = BizHelper.getDynamicObjectById(billType, pkId, "id,billentry.remainreturnqty,billentry.returnqty,billentry.remainreturnbaseqty,billentry.returnbaseqty,billentry.qty,billentry.baseqty");
        DynamicObjectCollection billEntryCol = info.getDynamicObjectCollection("billentry");
        boolean isSale = BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(billType);
        for (DynamicObject billEntryInfo : billEntryCol) {
            if (isSale) {
                billEntryInfo.set("remainreturnqty", billEntryInfo.getBigDecimal("qty").abs().multiply(new BigDecimal(size)));
                billEntryInfo.set("remainreturnbaseqty", billEntryInfo.getBigDecimal("baseqty").abs().multiply(new BigDecimal(size)));
            }else {
                billEntryInfo.set("returnqty", billEntryInfo.getBigDecimal("qty").abs().multiply(new BigDecimal(size)));
                billEntryInfo.set("returnbaseqty", billEntryInfo.getBigDecimal("baseqty").abs().multiply(new BigDecimal(size)));
            }
        }
        SaveServiceHelper.update(info);
    }

    /**
     * 重置销售出库单和采购入库单的表头字段和分录字段
     * @param info 采购入库单/销售出库单
     * @param param 需要传递设置的值信息
     * <AUTHOR>
     * @date 2022-7-27
     */
    private static void resetSaleOrPurValue(DynamicObject info, RequestMap param) {
        // 获取要处理的单据，只有两种 -- 销售出库单/采购入库单
        String billType = info.getDataEntityType().getName();
        if (BillTypeHelper.BILLTYPE_PURINBILL.equals(billType)) {
            DynamicObject purInfo = info;
            SaleOutBillMserviceImpl service = (SaleOutBillMserviceImpl) param.get("service");
            DynamicObject toOrgInfo = (DynamicObject) param.get("toOrgInfo");
            DynamicObject fromSupplierInfo = (DynamicObject) param.get("fromSupplierInfo");
            DynamicObject warehouseInfo = (DynamicObject) param.get("warehouseInfo");
            Map<String, BigDecimal> matPriceMap = (Map<String, BigDecimal>) param.get("matPriceMap");

            purInfo.set("org", toOrgInfo);
            purInfo.set("bizorg", toOrgInfo);
            purInfo.set("bizdept", toOrgInfo);
            purInfo.set("dept", toOrgInfo);
            purInfo.set("supplier", fromSupplierInfo);
            DynamicObjectCollection purEntryCol = purInfo.getDynamicObjectCollection("billentry");
            for (DynamicObject purEntryInfo : purEntryCol) {
                purEntryInfo.set("owner", toOrgInfo);
                purEntryInfo.set("keeper", toOrgInfo);
                purEntryInfo.set("entrysettleorg", toOrgInfo);
                purEntryInfo.set("entryreqorg", toOrgInfo);
                purEntryInfo.set("providersupplier", fromSupplierInfo);
                purEntryInfo.set("invoicesupplier", fromSupplierInfo);
                purEntryInfo.set("receivesupplier", fromSupplierInfo);

                purEntryInfo.set("remainreturnqty", purEntryInfo.getBigDecimal("qty"));
                purEntryInfo.set("remainreturnbaseqty", purEntryInfo.getBigDecimal("qty"));

                purEntryInfo.set("warehouse", warehouseInfo); // 设置仓库信息
                // 再重算金额
                String materialId = purEntryInfo.getString("material.masterid.id");
                if (matPriceMap.containsKey(materialId)) {
                    BigDecimal newPrice = matPriceMap.get(materialId);
                    purEntryInfo.set("priceandtax", newPrice);
                    // 重算价格
                    service.changePriceAndTax(purInfo, purEntryInfo);
                }
            }
        }else if (BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(billType)) {
            DynamicObject saleInfo = info;
            SaleOutBillMserviceImpl service = (SaleOutBillMserviceImpl) param.get("service");
            DynamicObject fromOrgInfo = (DynamicObject) param.get("fromOrgInfo");
            DynamicObject toCustomerInfo = (DynamicObject) param.get("toCustomerInfo");
            DynamicObject warehouseInfo = (DynamicObject) param.get("warehouseInfo");
            Map<String, BigDecimal> matPriceMap = (Map<String, BigDecimal>) param.get("matPriceMap");

            saleInfo.set("org", fromOrgInfo);
            saleInfo.set("bizorg", fromOrgInfo);
            saleInfo.set("bizdept", fromOrgInfo);
            saleInfo.set("dept", fromOrgInfo);
            // 单据头.客户，为组织关系中的后者，先根据名称再根据编码查找bd_customer
            saleInfo.set("customer", toCustomerInfo);
            saleInfo.set("yd_cuschannel", toCustomerInfo.getDynamicObject("yd_channel"));  // 客户所属渠道
            DynamicObjectCollection saleEntryCol = saleInfo.getDynamicObjectCollection("billentry");
            for (DynamicObject saleEntryInfo : saleEntryCol) {
                saleEntryInfo.set("entrysettleorg", fromOrgInfo);  // 结算组织
                saleEntryInfo.set("outowner", fromOrgInfo);  // 出库货主
                saleEntryInfo.set("outkeeper", fromOrgInfo);  // 出库保管者
                saleEntryInfo.set("settlecustomer", toCustomerInfo);  // 应收客户
                saleEntryInfo.set("payingcustomer", toCustomerInfo);  // 付款客户
                saleEntryInfo.set("reccustomer", toCustomerInfo);  // 收货客户
                saleEntryInfo.set("warehouse", warehouseInfo);  // 仓库
                // 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
                String materialId = saleEntryInfo.getString("material.masterid.id");
                if (matPriceMap.containsKey(materialId)) {
                    BigDecimal newPrice = matPriceMap.get(materialId);
                    saleEntryInfo.set("priceandtax", newPrice);
                    service.changePriceAndTax(saleInfo, saleEntryInfo);
                }
            }
        }
    }

    /**
     * 重置调拨单的结算状态（列表）
     * <AUTHOR>
     * @date 2022-7-19
     */
    public static void resetTransSettleStatus(IFormView formView) {
        UIUtils.checkSelected(formView, "请选择需要重置结算的单据！");
        List<Object> selectIds = UIUtils.getSelectIds(formView);

        ConvertDataService dataService = new ConvertDataService();
        // 检查是否符合重置结算状态的条件
        for (Object selectId : selectIds) {
            DynamicObject midBillInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDTRANSBILL, selectId);
            String billNumber = midBillInfo.getString("billno");
            // 1、已经标识了推送EAS的，不能重置结算状态
            if (midBillInfo.getBoolean("yd_istoeas")) {
                formView.showErrorNotification(String.format("单据[%s]已经推送EAS，不能重置结算状态！", billNumber));
                return;
            }
            // 2、校验状态是否已经审核，不为审核状态，不能重置状态
            if (!"C".equals(midBillInfo.getString("billstatus"))) {
                formView.showErrorNotification(String.format("单据[%s]还未审核不能重置结算状态！", billNumber));
                return;
            }
            // 通过关联关系，查找所有的单据，叫所有的单据删除，且重置调拨单中间表的“是否已经结算”和“是否推送EAS”为否
            String tempId = selectId.toString();
            Stack<String> relationStack = new Stack<>();
            String srcBillEntity = BillTypeHelper.BILLTYPE_MIDTRANSBILL;

            while(true) {
                // 遍历单据，获取下游单据数据
                Map<Long, List<BFRow>> targetBills = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
                if (targetBills == null || targetBills.size() == 0) break;
                // 获取执行的结果集合
                List<BFRow> targetRow = targetBills.values().iterator().next();
                BFRowId targetBillRow = targetRow.get(0).getId();
                srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
                tempId = targetBillRow.getBillId().toString();
                relationStack.push(srcBillEntity+"&"+tempId);
                System.out.println(srcBillEntity+tempId);
            }

            // 对查找出来的数据统一删除
            while (relationStack.size() != 0) {
                String billTypeAndId = relationStack.pop();
                String[] billTypeAndIdArr = billTypeAndId.split("&");
                String billType = billTypeAndIdArr[0]; // 单据类型
                String billId = billTypeAndIdArr[1]; // 单据类型
                // 查找数据
                DynamicObject info = BizHelper.getDynamicObjectById(billType, billId);
                // update by hst 2024/05/16 校验下游单据同步EAS状态
                boolean isAllowed = checkIfDeletionIsAllowed(info,billType);
                if (!isAllowed) {
                    formView.showErrorNotification(String.format("下游单据[%s] EAS已审核成功，请联系管理员进行删除！", info.getString("billno")));
                    return;
                }
                // 校验状态，进行反审核、撤销提交
                deleteBillWithCheckStatus(billType, info);
            }

            // 删除完后，重置中间表的单据信息
            midBillInfo.set("yd_issettle", false); // 是否已经结算
            midBillInfo.set("yd_settleerror", ""); // 结算错误信息
            midBillInfo.set("yd_ishasnext", false); // 是否有下游单号
            midBillInfo.set("yd_nextbillno", ""); // 是否有下游单号
            BizHelper.saveDynamicObject(midBillInfo);
            formView.showSuccessNotification("执行重置结算状态成功！");
        }
    }

    /**
     * 通过内部公司查找客户信息
     * <AUTHOR>
     * @date 2022-7-26
     */
    public static DynamicObject getCustomerByInternalCompany(String internalCompanyNumber) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_CUSTOMER, "id,name,number,yd_channel", QFilter.of("status='C' and internal_company.number=?", internalCompanyNumber).toArray());
    }

    public static DynamicObject getSupplierByInternalCompany(String internalCompanyNumber) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_SUPPLIER, "id,number,name", QFilter.of("status='C' and internal_company.number=?", internalCompanyNumber).toArray());
    }

    /**
     * 通过PCP组织编码获取组织信息
     * <AUTHOR>
     * @date 2022-7-26
     */
    public static DynamicObject getOrgByPcpOrg(String pcpOrgNumber) {
        Long orgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id", QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber = ?", pcpOrgNumber).toArray());
        if (orgId == null || orgId == 0) return null;
        return BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_ORG, orgId);
    }

    /**
     * 通过仓库编码获取仓库信息
     * <AUTHOR>
     * @date 2022-7-26
     */
    public static DynamicObject getWarehouseByNumber(String warehouseNumber) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, "id, number", QFilter.of("status='C' and number = ?", warehouseNumber).toArray());
    }

    /**
     * 通过物料编码查找物料基础资料
     * <AUTHOR>
     * @date 2022-7-26
     */
    public static DynamicObject getMaterialByNumber(String materialNumber) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_MATERIAL, "id, number", QFilter.of("status='C' and number = ?", materialNumber).toArray());
    }

    /**
     * 通过调出调入组织获取PCP调拨单结算方式
     * <AUTHOR>
     * @date 2022-7-26
     */
    public static DynamicObject getTransRelationByOrg(String outOrgNumber, String inOrgNumber) {
        return BizHelper.getSingleDynamicObject(BillTypeHelper.BILLTYPE_TRANSSETTLERELA, "id, yd_biztype, yd_entryentity.yd_orglevel, yd_entryentity.yd_org", QFilter.of("status='C' and yd_outorg.number=? and yd_inorg.number=?", outOrgNumber, inOrgNumber).toArray());
    }

    /**
     * 对单据进行保存、提交、审核，审核成功后会返回执行后的R结果
     * @param billType 需要执行的单据类型
     * @param info 执行的对象信息
     * @return 返回执行后的结果，如果执行成功会返回billId值，即info的pkValue
     * <AUTHOR>
     * @date 2022-7-27
     */
    public static R saveAndAuditBill(String billType, DynamicObject info) {
        // 保存目标单据
        String billTypeName = BizHelper.getBillTypeName(billType);
        OperationResult result = SaveServiceHelper.saveOperate(billType, new DynamicObject[]{info}, OperateOption.create());
        String errorMessage = BizHelper.getOperationErrorMessage(result);
        if (StringUtils.isNotBlank(errorMessage)) {
            return R.error("保存"+billTypeName+"失败："+errorMessage);
        }
        // 保存成功后，执行提交方案
        Object pkValue = info.getPkValue();
        String billNo = info.getString("billno");
        result = OperationServiceHelper.executeOperate("submit", billType, new Object[]{pkValue}, OperateOption.create());
        errorMessage = BizHelper.getOperationErrorMessage(result);
        if (StringUtils.isNotBlank(errorMessage)) {
            return R.error(billTypeName + billNo + "生成成功，但提交失败，请到"+billTypeName+"手动提交，查看提交失败原因："+errorMessage);
        }
        // 针对已经提交成功的，进行审核处理
        result = OperationServiceHelper.executeOperate("audit", billType, new Object[]{pkValue}, OperateOption.create());
        errorMessage = BizHelper.getOperationErrorMessage(result);
        if (StringUtils.isNotBlank(errorMessage)) {
            return R.error(billTypeName + billNo + "提交成功，但审核失败，请到"+billTypeName+"手动审核，查看提交失败原因："+errorMessage);
        }
        return R.ok("ok").put("billId", pkValue);
    }

    /**
     * 取消审批、撤销提交，再删除数据
     * @param billType 需要执行的单据类型
     * @param info 执行的对象信息
     * @return 返回执行后的结果
     * <AUTHOR>
     * @date 2022-7-27
     */
    public static void deleteBillWithCheckStatus(String billType, DynamicObject info) {
        // 保存目标单据
        String billTypeName = BizHelper.getBillTypeName(billType);
        // 单据状态
        String billStatus = info.getString("billstatus");
        Object billId = info.getPkValue(); // 单据ID
        String billNumber = info.getString("billno"); // 单据编码

        OperationResult result;
        if ("B".equals(billStatus)) {
            result = OperationServiceHelper.executeOperate("unsubmit", billType, new Object[]{billId}, OperateOption.create());
            String errorMessage = BizHelper.getOperationErrorMessage(result);
            if (StringUtils.isNotBlank(errorMessage)) {
                throw new KDBizException("单据"+billNumber+ "撤销提交失败，请到"+billTypeName+"手动撤销提交，查看撤销提交失败原因：" + errorMessage);
            }
        }else if ("C".equals(billStatus)) {
            result = OperationServiceHelper.executeOperate("unaudit", billType, new Object[]{billId}, OperateOption.create());
            String errorMessage = BizHelper.getOperationErrorMessage(result);
            if (StringUtils.isNotBlank(errorMessage)) {
                throw new KDBizException("单据"+billNumber+ "反审核失败，请到"+billTypeName+"手动反审核，查看反审核失败原因：" + errorMessage);
            }
        }
        result = OperationServiceHelper.executeOperate("delete", billType, new Object[]{billId}, OperateOption.create());
        String errorMessage = BizHelper.getOperationErrorMessage(result);
        if (StringUtils.isNotBlank(errorMessage)) {
            throw new KDBizException("单据"+billNumber+ "删除失败，请到"+billTypeName+"手动删除，查看删除失败原因：" + errorMessage);
        }
    }

    /**
     * 对单据进行保存、提交、审核，审核成功后会返回执行后的R结果
     * @param billType 需要执行的单据类型
     * @param formView FormView对象
     * @return 返回执行后的结果，如果执行成功会返回billId值，即info的pkValue
     * <AUTHOR>
     * @date 2022-7-27
     */
    public static R saveAndAuditBill(String billType, IFormView formView) {
        // 保存目标单据
        String billTypeName = BizHelper.getBillTypeName(billType);
        OperationResult result = ABillServiceHelper.saveOperate(formView);
        String errorMessage = BizHelper.getOperationErrorMessage(result);
        if (StringUtils.isNotBlank(errorMessage)) {
            return R.error("保存"+billTypeName+"失败："+errorMessage);
        }
        // 保存成功后，执行提交方案
        DynamicObject info = formView.getModel().getDataEntity();
        Object pkValue = info.getPkValue();
        String billNo = info.getString("billno");
        result = ABillServiceHelper.executeOperate("submit", billType, new Object[] {pkValue}, OperateOption.create());
        errorMessage = BizHelper.getOperationErrorMessage(result);
        if (StringUtils.isNotBlank(errorMessage)) {
            return R.error(billTypeName + billNo + "生成成功，但提交失败，请到"+billTypeName+"手动提交，查看提交失败原因："+errorMessage);
        }
        // 针对已经提交成功的，进行审核处理
        result = ABillServiceHelper.executeOperate("audit", billType, new Object[] {pkValue}, OperateOption.create());
        errorMessage = BizHelper.getOperationErrorMessage(result);
        if (StringUtils.isNotBlank(errorMessage)) {
            return R.error(billTypeName + billNo + "提交成功，但审核失败，请到"+billTypeName+"手动审核，查看提交失败原因：" +errorMessage);
        }
        return R.ok("ok").put("billId", pkValue);
    }

    /**
     * PCP一盘货下推校验
     * @author: hst
     * @createDate: 2022/10/26
     * @param billId
     * @param context
     */
    public static void transSaleOutBill_inventory_verify (Object billId,Map<String, Object> context) {
        DataSet dataSet = null;
        try {
            DynamicObject outBill = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId);
            //错误信息置空
            outBill.set("yd_errmessage", "");
            // 信控后置
            if (PcpBusinessTypeEnum.CREDIT_CONTROL_REAR.getCode().equals(outBill.getString("yd_businesstype"))) {
                // 正向销售才校验销售订单，销售退不校验销售退货申请单
                if (StringUtils.isBlank(outBill.getString("yd_saleordernumber"))
                        && "210".equals(outBill.getString("yd_biztype"))) {
                   throw new KDBizException("单据类型为财务后置EAS财审，来源销售订单/销售退货申请单号为空！\n");
                }
            }
            //字段非空性校验
            verifyFieldIsNull(outBill);
            //检验库存组织是否存在
            verifyEntryInfo(context,outBill,"yd_orgnumber",BillTypeHelper.BILLTYPE_PCPORGRELATION);
            //校验销售组织是否存在
            verifyEntryInfo(context,outBill,"yd_saleorgnumber",BillTypeHelper.BILLTYPE_PCPORGRELATION);
            //校验结算组织是否存在
            verifyEntryInfo(context,outBill,"yd_settleorgnumber",BillTypeHelper.BILLTYPE_PCPORGRELATION);
            //校验客户是否存在
            verifyEntryInfo(context,outBill,"yd_customnumber",BillTypeHelper.BILLTYPE_CUSTOMER);
            // update by hst 校验客户是否存在默认税率
            verifycusTaxRate(context,outBill);
            ///校验分录中物料是否存在
            verifyEntryInfo(context,outBill,"yd_materialnumber",BillTypeHelper.BILLTYPE_MATERIAL);
            //校验分录中仓库是否存在
            verifyEntryInfo(context,outBill,"yd_warehousenumber",BillTypeHelper.BILLTYPE_WAREHOUSE);
            //校验生产日期、到期日期，为空是匹配批次，然后再次校验是否存在批次
            materialVerify(outBill,context);
            //校验金额，若表头金额与分录合计金额不一致，则进行重算
            Map<String,String> fields = new HashMap<>();
            fields.put("totalField","yd_itemtotalamount");  //表头总金额字段
            fields.put("amountField","yd_actuallyamount");  //明细实付总额字段
            fields.put("oriamountField","yd_oriactuallyamount");  //明细实付总额字段
            fields.put("priceField","yd_taxprice");  //分录单价字段
            fields.put("qtyField","yd_quantity");  //分录数量字段
            fields.put("isDiffField","yd_hasdiffamt");  //标记金额存在差异字段
            //校验表头金额是否与分录合计金额一致，若不一致将差异金额置于最后一行
//            verifyAmountDifference(outBill,"entryentity",fields);  // 因为PCP推送的表头金额有问题，暂不校验，yzl,20221126

        } catch (Exception e) {
            throw new KDBizException(e.getMessage());
        } finally {
            ORMUtils.close(dataSet);
        }
    }

    /**
     * 字段非空性校验
     * @author: hst
     * @createDate: 2022/10/26
     * @param outBill
     */
    public static void verifyFieldIsNull(DynamicObject outBill) {
        // 校验表头字段信息
        // update by hst 2022/11/14 增加结算组织编码校验
        String headWarning = InterUtils.verifyFieldIsNull(outBill,
                new String[] {"billno","yd_bizdate","yd_orgnumber","yd_saleorgnumber","yd_customnumber","yd_settleorgnumber"},
                new String[] {"编码","业务日期","库存组织编码","销售组织编码","客户编码","结算组织编码"});
        // 校验分录表头信息
        String entryWarning = InterUtils.verifyFieldIsNull(outBill, "entryentity",
                new String[] {"yd_materialnumber","yd_batchno","yd_quantity","yd_warehousenumber"},
                new String[] {"物料编码","批次","数量","仓库编码"});
        if (StringUtils.isNotBlank(headWarning) || StringUtils.isNotBlank(entryWarning)) {
            throw new KDBizException(new StringBuilder().append(headWarning).append("\n").append(entryWarning).toString());
        }
    }

    /**
     * 校验物料、仓库是否存在
     * @author: hst
     * @createDate: 2022/10/26
     * @param outBill
     * @param context
     */
    public static void materialVerify (DynamicObject outBill,Map<String, Object> context) {
        BigDecimal enTotalAmt = BigDecimal.ZERO; // 统计分录总金额
        DynamicObjectCollection entryCol = outBill.getDynamicObjectCollection("entryentity");
        for (DynamicObject entryInfo : entryCol) {
            String materialNumber = entryInfo.getString("yd_materialnumber"); // 物料编码
            String warehouseNumber = entryInfo.getString("yd_warehousenumber"); // 仓库编码
            // 合计分录总实付金额，后面与表头对比
            BigDecimal enAmt = entryInfo.getBigDecimal("yd_actuallyamount");
            enTotalAmt = enTotalAmt.add(enAmt);
            // 校验生产日期和到期日，根据批次+物料+仓库重新找一遍即时库存
            if (entryInfo.getDate("yd_probegindate")==null||entryInfo.getDate("yd_proenddate")==null) {
                String batchNo = entryInfo.getString("yd_batchno");
                QFilter filter = new QFilter("yd_wsnum", QCP.equals, warehouseNumber);
                filter.and(new QFilter("yd_matnum", QCP.equals, materialNumber));
                filter.and(new QFilter("yd_flot", QCP.equals, batchNo));
                filter.and(new QFilter("yd_mfgdate", QCP.is_notnull,""));
                filter.and(new QFilter("yd_expdate", QCP.is_notnull, ""));
                DataSet lotData = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
                        "yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
                if (!lotData.hasNext()) {
                    throw new KDBizException("EAS及时库存中查询不到仓库" + warehouseNumber + "中的物料" + materialNumber + "的" + batchNo + "批次信息！");
                } else {
                    Row lotRow = lotData.next();
                    entryInfo.set("yd_probegindate", lotRow.getDate("yd_mfgdate"));
                    entryInfo.set("yd_proenddate", lotRow.getDate("yd_expdate"));
                    SaveServiceHelper.save(new DynamicObject[] {outBill});
                }
            }
        }
    }

    /**
     * 校验表头金额是否与分录合计金额一致，若不一致将差异金额置于最后一行
     * @author: hst
     * @createDate: 2022/10/27
     * @param entity 单据
     * @param entryName 分录标识
     * @param fields 字段映射  需包含一下key （totalField 表头金额字段
     *                                         amountField 明细实付总额字段
     *                                         oriamountField 原明细实付总额字段
     *                                         priceField 分录单价字段
     *                                         qtyField 分录数量字段
     *                                         isDiffField 标记金额存在差异字段）
     */
    public static void verifyAmountDifference (DynamicObject entity, String entryName, Map<String,String> fields) {
        // 统计分录总金额
        BigDecimal enTotalAmt = BigDecimal.ZERO;
        // 对分录金额进行重算，数量乘以含税单价
        BigDecimal tempTotalAmt = BigDecimal.ZERO;
        //单据总金额
        BigDecimal billTotalAmt = entity.getBigDecimal(fields.get("totalField"));
        //是否需要对单据重新保存
        boolean reSave = false;
        int index = 0;
        DynamicObjectCollection entries = entity.getDynamicObjectCollection(entryName);
        for (DynamicObject entry : entries) {
            // 合计分录总实付金额，后面与表头对比
            BigDecimal enAmt = entry.getBigDecimal(fields.get("amountField"));
            enTotalAmt = enTotalAmt.add(enAmt);
            //数量乘以含税单价
            BigDecimal taxPrice = entry.getBigDecimal(fields.get("priceField"));
            BigDecimal qty = entry.getBigDecimal(fields.get("qtyField"));
            BigDecimal amt = taxPrice.multiply(qty).setScale(4, BigDecimal.ROUND_HALF_UP);
            tempTotalAmt = tempTotalAmt.add(amt);
            entry.set(fields.get("oriamountField"), entry.getBigDecimal(fields.get("amountField")));
            //记录原实付金额
            if (index == entries.size() - 1 && enTotalAmt.compareTo(billTotalAmt) != 0 && tempTotalAmt.compareTo(billTotalAmt) != 0) {
                reSave = true;
                entity.set(fields.get("isDiffField"), true);
                BigDecimal diffAmt = billTotalAmt.subtract(tempTotalAmt);
                amt = amt.add(diffAmt);
            }
            entry.set(fields.get("amountField"), amt);
            index++;
        }
        if (reSave) {
            SaveServiceHelper.save(new DynamicObject[]{entity});
        }
    }

    /**
     * 校验单据中F7信息是否存在
     * @author: hst
     * @createDate: 2022/10/27
     * @param context
     * @param entity
     * @param businessType
     * @return
     */
    public static void verifyEntryInfo (Map<String,Object> context, DynamicObject entity, String field, String businessType) {
        StringBuilder errBuilder = new StringBuilder();
        String tip = "";        //提示信息
        String entryName = "";  //分录名
        String filterInfo = "";
        //类型判断
        switch (businessType) {
            case "bd_material": {
                tip = "当前物料信息";
                entryName = "entryentity";
                filterInfo = "status='C' and number = ?";
                break;
            }
            case "bd_warehouse": {
                tip = "当前仓库信息";
                entryName = "entryentity";
                filterInfo = "status='C' and number = ?";
                break;
            }
            case "bd_customer": {
                tip = "当前客户信息";
                filterInfo = "status='C' and number = ?";
                break;
            }
            case "yd_pcporgrelation": {
                tip = "组织映射关系";
                filterInfo = "billstatus='C' and entryentity.yd_pcporgnumber = ?";
                break;
            }
            default: {
                throw new KDBizException("系统异常，请联系管理员");
            }
        }
        Set<String> numbers = new HashSet<>();
        //取出需要校验的值
        if (!"".equals(entryName)) {
            for (DynamicObject entry : entity.getDynamicObjectCollection(entryName)) {
                if (StringUtils.isNotBlank(entry.getString(field))) {
                    numbers.add(entry.getString(field));
                }
            }
        } else {
            numbers.add(entity.getString(field));
        }
        if (numbers.size() > 0) {
            for (String number : numbers) {
                String key = new StringBuilder().append(businessType).append("_").append(number).append("_").append("isExists").toString();
                if (!context.containsKey(key)) {
                    boolean isExists = BizHelper.isExists(businessType, QFilter.of(filterInfo, number).toArray());
                    context.put(key, isExists);
                    if (!isExists) {
                        errBuilder.append(number).append("、");
                    }
                }
            }
        }
        if (errBuilder.length() > 0) {
            throw new KDBizException("[" + errBuilder.deleteCharAt(errBuilder.length() - 1) + "]不存在" + tip + "！\n");
        }
    }

    /**
     * PCP销售出库单中间表一盘货下推生成销售出库单(开门红）
     * @author: hst
     * @createDate: 2022/10/27
     * @param context 上下文
     * @param billId 销售出库单中间表ID
     * @param isExclude 是否排除物料、仓库
     */
    public static String[] transSaleOutBill_inventory_trans(Map<String, Object> context, long billId, boolean isExclude) {
        DynamicObject outBill = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId);
        //排除物料，仓库后需要下推的物料明细
        DynamicObjectCollection materials = eliminateMaterialAndWarehouse(context,outBill,isExclude,isExclude);
        //按品牌拆分物料  update by hst 2022/12/13 由参数控制是否进行拆单
        List<List<DynamicObject>> splitMaterials = splitMaterialByBrand(context,materials);
        //下推生成销售出库单
        String outId = generateSaleOutBill(context,outBill,splitMaterials);
        // update by hst 2022/12/26 不拆分时保存失败异常处理优化
        return outId.length() > 0 ? outId.split(",") : new String[0];
    }

    /**
     * 是否剔除物料、仓库
     * @author: hst
     * @createDate: 2022/10/27
     * @param context 上下文
     * @param outBill 单据
     * @param isMaterial 剔除物料
     * @param isWarehouse 剔除仓库
     */
    public static DynamicObjectCollection eliminateMaterialAndWarehouse (Map<String, Object> context, DynamicObject outBill, boolean isMaterial, boolean isWarehouse) {
        List<String> elimMaterial = new ArrayList<>();
        List<String> elimWarehouse = new ArrayList<>();
        //查询需要排除的物料
        if (isMaterial) {
            if (context.containsKey("elimMaterial")) {
                elimMaterial = (List<String>) context.get("elimMaterial");
            } else {
                DataSet material = QueryServiceHelper.queryDataSet("PCPServiceHelp", "yd_pcwl", "yd_entryentity.yd_textfield_bm as number",
                        new QFilter[]{QFilter.of("yd_lx = '4' and billstatus = 'C'")}, null);
                while (material.hasNext()) {
                    Row row = material.next();
                    elimMaterial.add(row.getString("number"));
                }
                context.put("elimMaterial", elimMaterial);
            }
        }
        //查询需要排除的仓库
        if (isWarehouse) {
            if (context.containsKey("elimWarehouse")) {
                elimWarehouse = (List<String>) context.get("elimWarehouse");
            } else {
                DataSet warehouse = QueryServiceHelper.queryDataSet("PCPServiceHelp", "yd_pcck", "yd_entryentity.yd_textfield_bm as number",
                        new QFilter[]{QFilter.of("yd_lx = '1' and billstatus = 'C'")}, null);
                while (warehouse.hasNext()) {
                    Row row = warehouse.next();
                    elimWarehouse.add(row.getString("number"));
                }
                context.put("elimWarehouse", elimWarehouse);
            }
        }
        DynamicObjectCollection enties = outBill.getDynamicObjectCollection("entryentity");
        DynamicObjectCollection newEnties = new DynamicObjectCollection();
        for (DynamicObject entry : enties) {
            String materialNumber = entry.getString("yd_materialnumber");
            String warehouse = entry.getString("yd_warehousenumber");
            //排除物料
            if (elimMaterial.indexOf(materialNumber) > -1) {
                entry.set("yd_outmaterial",true);
                continue;
            }
            //排除仓库
            if (elimWarehouse.indexOf(warehouse) > -1) {
                entry.set("yd_outwarehouse",true);
                continue;
            }
            newEnties.add(entry);
        }
        if (newEnties.size() != enties.size()) {
            SaveServiceHelper.save(new DynamicObject[]{outBill});
        }
        return newEnties;
    }

    /**
     * 按品牌拆分物料
     * @author: hst
     * @createDate: 2022/10/27
     * @param context 上下文
     * @param materials 物料明细
     */
    public static List<List<DynamicObject>> splitMaterialByBrand (Map<String, Object> context, DynamicObjectCollection materials) {
        // update by hst 2022/12/13 由参数控制是否进行拆单
        boolean isSplit;
        if (context.containsKey("isSplit")) {
            isSplit = (boolean) context.get("isSplit");
        } else {
            DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name",
                    new QFilter[]{QFilter.of("number = ? and status = ?", "PCP_INVENTORY_ISSPLIT", "C")});
            if (Objects.isNull(param)) {
                throw new KDBizException("请在参数配置表中配置PCP_INVENTORY_ISSPLIT参数，参数值为true/false!");
            }
            String value = param.getString("name");
            isSplit = kd.bos.tcbj.im.util.StringUtils.isNotBlank(value) ? "true".equals(value) ? true : false : false;
            context.put("isSplit",isSplit);
        }
        StringBuffer errString = new StringBuffer();
        List<List<DynamicObject>> splitMaterial = new ArrayList<>();
        //按品牌拆分
        if (isSplit) {
            Map<String,List<DynamicObject>> brandMaterials = new HashMap<>();
            for (DynamicObject materialInfo : materials) {
                //查询物料所属品牌
                String matNumber = materialInfo.getString("yd_materialnumber");
                DynamicObject material = BusinessDataServiceHelper.loadSingle("bd_material","yd_basedatafield",
                        new QFilter[]{new QFilter("number",QFilter.equals,matNumber)});
                if (Objects.nonNull(material)) {
                    //按品牌分组存储
                    String brand = Objects.nonNull(material.getDynamicObject("yd_basedatafield")) ?
                            material.getDynamicObject("yd_basedatafield").getString("number") : "";
                    if (StringUtils.isBlank(brand)) {
                        errString.append(matNumber + ",");
                        continue;
                    }
                    if (brandMaterials.containsKey(brand)) {
                        brandMaterials.get(brand).add(materialInfo);
                    } else {
                        List<DynamicObject> temp = new ArrayList<>();
                        temp.add(materialInfo);
                        brandMaterials.put(brand, temp);
                    }
                }
            }
            for (Map.Entry<String,List<DynamicObject>> brandMaterial : brandMaterials.entrySet()) {
                splitMaterial.add(brandMaterial.getValue());
            }
        } else {
            //若无需拆分，转List<DynamicObject>返回
            splitMaterial.add(Arrays.stream(materials.toArray(new DynamicObject[materials.size()])).collect(Collectors.toList()));
        }
        if (errString.length() > 0) {
            throw new KDBizException("物料[" + errString.deleteCharAt(errString.length() - 1).toString() + "]查询不到对应品牌\n");
        }
        return splitMaterial;
    }

    /**
     * 下推生成一盘货销售出库单
     * @author: hst
     * @createDate: 2022/10/27
     * @param context 上下文
     * @param oriBill 原单据
     * @param splitMaterials 物料信息
     */
    public static String generateSaleOutBill (Map<String,Object> context, DynamicObject oriBill, List<List<DynamicObject>> splitMaterials) {
        //生成的销售出库单单号，回写到来源单据中
        StringBuffer outBillNo = new StringBuffer();
        //生成的销售出库单ID，用于绑定botp关系
        StringBuffer outId = new StringBuffer();
        // update by hst 2022/12/13 由参数控制是否进行拆单,在销售出库单上打上标识
        boolean isSplit = context.containsKey("isSplit") ? (boolean) context.get("isSplit") : false;

        for (List<DynamicObject> materials : splitMaterials) {
            String orgNumber = oriBill.getString("yd_orgnumber"); // 库存组织编码
            String saleOrgNumber = oriBill.getString("yd_saleorgnumber"); // 销售组织编码
            String settleOrgNumber = oriBill.getString("yd_settleorgnumber"); // 结算组织编码
            String sourceFunction = oriBill.getString("yd_sourcefunction"); // 同步来源系统
            String sourceFunction2 = "PCP".equals(sourceFunction) ? "4" : null;
            //组织
            Long orgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id",
                    QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber=?", orgNumber).toArray());
            //销售组织
            Long saleOrgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id",
                    QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber=?", saleOrgNumber).toArray());
            //结算组织
            Long settleOrgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id",
                    QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber=?", settleOrgNumber).toArray());
            //新增销售出库单
            IFormView saleView = ABillServiceHelper.createAddView(BillTypeHelper.BILLTYPE_SALEOUTBILL);
            IDataModel saleModel = saleView.getModel();
            //赋值
            saleModel.setItemValueByNumber("billtype", "im_SalOutBill_STD_BT_S"); // 单据类型（标准销售出库单）
            saleModel.setItemValueByID("org", orgId); // 库存组织
            saleModel.setItemValueByID("bizorg", saleOrgId); // 销售组织
            saleModel.setItemValueByNumber("biztype", oriBill.getString("yd_biztype")); // 业务类型
            saleModel.setItemValueByNumber("invscheme", oriBill.getString("yd_transno")); // 库存事务
            saleModel.setItemValueByNumber("customer", oriBill.getString("yd_customnumber")); // 客户
            saleModel.setItemValueByNumber("yd_dzdkh", oriBill.getString("yd_customnumber")); // 对账客户
            saleModel.setValue("biztime", oriBill.getDate("yd_bizdate")); // 业务日期
            saleModel.setValue("yd_tbly", sourceFunction2); // 同步来源系统
            saleModel.setValue("yd_sourcebilltype", 3);  // 来源单据类型 —— PCP销售出库单
            saleModel.setValue("yd_saleordernumber", oriBill.getString("yd_saleordernumber")); // 来源销售订单
            saleModel.setValue("yd_platformorderno", oriBill.getString("yd_platformorderno")); // 营销云单号（csp单号）
            saleModel.setValue("comment", oriBill.getString("yd_description")); // 备注
            saleModel.setValue("yd_businesstype", oriBill.getString("yd_businesstype")); // 业务类型
            saleModel.setValue("yd_sourcetradenumber", oriBill.getString("yd_sourcetradenumber")); // 内部交易源头单号
            saleModel.setValue("yd_office", oriBill.getString("yd_office")); // 办事处
            saleModel.setValue("yd_province", oriBill.getString("yd_province")); // 省份
            saleModel.setValue("yd_city", oriBill.getString("yd_city")); // 城市
            saleModel.setValue("yd_district", oriBill.getString("yd_district")); // 地区
            saleModel.setValue("yd_linkman", oriBill.getString("yd_linkman")); // 联系人
            saleModel.setValue("yd_phone", oriBill.getString("yd_phone")); // 联系方式
            saleModel.setValue("yd_address", oriBill.getString("yd_address")); // 地址
            saleModel.setValue("yd_description2", oriBill.getString("yd_description2")); // 摘要
            saleModel.setValue("yd_isinventory", oriBill.getString("yd_isinventory"));  //是否一盘货
            saleModel.setValue("yd_isvirtual", oriBill.getString("yd_isvirtual"));  // update by hst 2023/02/15 是否虚单结算
            saleModel.setValue("yd_purorder", oriBill.getString("yd_purorder"));  // update by hst 2023/02/15 采购订单号
            saleModel.setItemValueByID("yd_settleorg", settleOrgId); // 结算组织
            saleModel.setValue("billstatus", "A");
            // update by hst 2022/12/13 在销售出库单上打上按品牌拆单标识
            saleModel.setValue("yd_issplit",isSplit);
            // update by hst 2023/03/22 是否线下一盘货
            saleModel.setValue("yd_isoffline",oriBill.getBoolean("yd_isoffline"));
            // update by hst 2023/04/14 PCP单据类型
            saleModel.setValue("yd_ordertype",oriBill.getString("yd_ordertype"));
            //分录信息
            int index = 0;
            if (materials.size() > 0) {
                for (DynamicObject material : materials) {
                    //新建分录行
                    saleModel.createNewEntryRow("billentry");
                    saleModel.setItemValueByNumber("material", material.getString("yd_materialnumber"), index); // 物料
                    saleModel.setValue("yd_ph", material.getString("yd_batchno"), index); // E3批号
                    saleModel.setValue("yd_scrq", material.getDate("yd_probegindate"), index); // E3生产日期
                    saleModel.setValue("yd_dqr", material.getDate("yd_proenddate"), index); // E3到期日
                    saleModel.setValue("qty", material.getBigDecimal("yd_quantity"), index); // 数量
                    BigDecimal discountAmount = material.getBigDecimal("yd_discountamount"); // 总折扣额
                    saleModel.setValue("amountandtax", material.getBigDecimal("yd_actuallyamount").add(discountAmount), index); // 应付金额 —— 用来统计出单价和后续折扣额的统计
                    if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                        saleModel.setValue("discounttype", material.getString("yd_discounttype"), index); // 折扣方式
                        saleModel.setValue("discountamount", discountAmount, index); // 折扣额
                    }
                    saleModel.setValue("amountandtax", material.getBigDecimal("yd_actuallyamount"), index); // 实付金额 —— 最终标识的实付金额
                    saleModel.setItemValueByNumber("warehouse", material.getString("yd_warehousenumber"), index); // 仓库
                    saleModel.setValue("ispresent", material.getBoolean("yd_ispresent"), index);    // 是否赠品
                    saleModel.setValue("yd_isintegral", material.getBoolean("yd_isintegral"), index);    // 是否积分产品
                    saleModel.setValue("yd_integral", material.getString("yd_integral"), index);    // 使用积分
                    saleModel.setValue("yd_subdivisionplatform", material.getString("yd_subdivisionplatform"), index);    // 电商渠道
                    saleModel.setValue("yd_saledistribution", material.getString("yd_saledistribution"), index);    // 销售渠道
                    saleModel.setValue("yd_easorderentryid", material.getString("yd_easorderentryid"), index);  // 销售订单分录ID
                    saleModel.setValue("srcsysbillentryid", material.getPkValue(), index);  // 来源系统单据分录ID
                    saleModel.setValue("srcsysbillno", oriBill.getString("billno"), index);  // 来源系统单据编号
                    saleModel.setValue("srcsysbillid", oriBill.getPkValue(), index);  // 来源系统单据ID
                    saleModel.setValue("srcbillid", oriBill.getPkValue(), index);  // 来源单据ID
                    saleModel.setValue("srcbillentryid", material.getPkValue(), index);  // 来源单据行ID
                    saleModel.setValue("srcbillentryseq", material.get("seq"), index);  // 来源单据分录序号
                    saleModel.setValue("srcbillnumber", oriBill.getString("billno"), index);  // 来源单据分录序号
                    // 查询税率基础资料
                    DataSet dataSet = null;
                    String taxRateNum = null;
                    String customNumber = oriBill.getString("yd_customnumber"); // 客户编码
                    try {
                        dataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), BillTypeHelper.BILLTYPE_CUSTOMER, "taxrate.number as taxRateNum", QFilter.of("status='C' and number=?", customNumber).toArray());
                        if (dataSet.hasNext()) {
                            Row row = dataSet.next();
                            taxRateNum = row.getString("taxRateNum");
                        }
                    } finally {
                        ORMUtils.close(dataSet);
                    }
                    saleModel.setItemValueByNumber("taxrateid", taxRateNum, index); // 税率
                    index++;
                }
                try {
                    // 触发保存功能
                    OperationResult result = ABillServiceHelper.saveOperate(saleView);
                    if (result.isSuccess()) {
                        outBillNo.append(saleModel.getValue("billno") + ",");
                        outId.append(result.getSuccessPkIds().get(0) + ",");
                    }
                    String errorMessage = BizHelper.getOperationErrorMessage(result);
                    if (StringUtils.isNotBlank(errorMessage)) {
                        //一张保存失败的，删除之前生成的单据
                        // update by hst 2022/12/26 不拆分时保存失败异常处理优化
                        if (outId.length() > 0) {
                            DeleteServiceHelper.delete("im_saloutbill",
                                    new QFilter[]{new QFilter("id", QFilter.in, outId.deleteCharAt(outId.length() - 1).toString().split(","))});
                        }
                        throw new KDBizException("销售出库单[" + saleModel.getValue("billno") + "]保存失败，查看保存失败原因：" + errorMessage);
                    }
                } finally {
                    ABillServiceHelper.exitView(saleView);
                }
            }
        }
        if (outBillNo.length() > 0) {
            oriBill.set("yd_ishasnext", true);
            oriBill.set("yd_salebillnumber", outBillNo.deleteCharAt(outBillNo.length() - 1).toString());
            oriBill.set("yd_errmessage", ""); // 错误信息重置为空
            BizHelper.saveDynamicObject(oriBill);
        }
        return outId.length() > 0 ? outId.deleteCharAt(outId.length() - 1).toString() : "";
    }

    /**
     * 保存关联关系(一对多)
     * @param billId 销售出库单中间表ID
     * @param targetIds 销售出库单ID
     * <AUTHOR>
     * @date 2022/10/27
     */
    private static void transSaleOutBill_relation(long billId, Object[] targetIds) {
        for (Object targetId : targetIds) {
            BotpUtils.createRelation(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId, BillTypeHelper.BILLTYPE_SALEOUTBILL, Long.valueOf(String.valueOf(targetId)));
        }
    }

    /**
     *  PCP销售出库单一盘货结算
     * @param entityName 单据类型
     * @param operationType 操作标识
     * @param qFilter 查询单据的过滤条件
     * @param tip 当获取不到单据的提示信息
     */
    public static void createPCPSettleBill (String entityName,String operationType, QFilter qFilter, String tip) {
        DataSet saleBillSet = null;
        try {
            saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", entityName, "id,billno", qFilter.toArray(), null);
            if (!saleBillSet.hasNext()) {
                if (StringUtils.isNotBlank(tip)) {
                    throw new KDBizException(tip);
                } else {
                    return;
                }
            }
            Set<String> doneBillSet = new HashSet<>();
            for (Row row : saleBillSet) {
                //单据id
                String billId = row.getString("id");
                //避免对一条单据重复处理
                if (doneBillSet.contains(billId) || Strings.isBlank(billId)) {
                    continue;
                }
                // 如果有互斥锁则提示不允许操作
                Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(billId, entityName, operationType);
                if (lockInfo != null) {
                    throw new KDBizException("系统定时的调度计划在执行中，请勿重复操作发起内部结算！");
                }
                // 对进行结算的出库单进行加锁
                MutexServiceHelper.request(billId, entityName, operationType);
                try {
                    DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId,entityName);
                    ApiResult result = new ApiResult();
                    // 业务类型是2101(退货)的需要走反方向，210(销售)的走正方向
                    DynamicObject biztype = bill.getDynamicObject("biztype");
                    if ("210".equals(biztype.getString("number"))) {
                        // 正向流程
                        result = SaleOutBillMserviceHelper.createPCPSettleBill(bill,true);
                    } else if ("2101".equals(biztype.getString("number"))) {
                        // 逆向流程
                        result = SaleOutBillMserviceHelper.createPCPSettleBill(bill,false);
                    }
                    if (!result.getSuccess()) {
                        throw new KDBizException(result.getMessage());
                    }
                    // 使用关联关系将生成的下游单据保存到源销售出库单上
                    InternalSettleServiceHelper.saveBotpBills(billId);
                } catch(Exception e) {
                    // update by hst 2022/11/28 记录异常信息反写到单据字段信息中
                    DynamicObject BillInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_SALEOUTBILL, billId, "billno,yd_settleerror");
                    String errMessage = e.getMessage();
                    // 如果字段过长，则截取长度
                    if (errMessage.length() > 2000) {
                        errMessage = errMessage.substring(0, 2000);
                    }
                    BillInfo.set("yd_settleerror", errMessage);
                    SaveServiceHelper.save(new DynamicObject[]{BillInfo});
                } finally {
                    // 释放锁
                    MutexServiceHelper.release(billId, entityName, operationType);
                }
            }
        } finally {
            // 释放DataSet
            ORMUtils.close(saleBillSet);
        }
    }

    /**
     * 校验是否符合重置PCP开门红结算条件
     * @author: hst
     * @createDate: 2022/10/31
     * @param bill
     */
    public static void checkPCPBillResetSettle (DynamicObject bill) {
        // 不是审核状态不允许
        String billNo = bill.getString("billno");
        if ("4".equals(bill.getBoolean("yd_tbly"))) {
            throw new KDBizException("源销售出库单" + billNo + "的同步来源系统不是PCP，无法执行清理操作！");
        }
        if (!bill.getBoolean("yd_isinventory")) {
            throw new KDBizException("源销售出库单" + billNo + "不是PCP开门红结算，无法执行清理操作！");
        }
        if (bill.getBoolean("yd_internaltoeas")) {
            throw new KDBizException("源销售出库单" + billNo + "涉及的内部结算单据已全部传递EAS，需要在EAS与中台手工回退！");
        }
        if (!bill.getBoolean("yd_internalsettle")) {
            throw new KDBizException("源销售出库单" + billNo + "不是末级组织结算单，无法执行清理操作！");
        }
        if (!"C".equals(bill.getString("billstatus"))) {
            throw new KDBizException("源销售出库单" + billNo + "未发起过内部结算，无法执行清理操作！");
        }
    }

    /**
     * 校验是否符合重置PCP线下一盘货结算条件
     * @author: hst
     * @createDate: 2023/03/22
     * @param bill
     */
    public static void checkPCPOfflineResetSettle (DynamicObject bill) {
        // 不是审核状态不允许
        String billNo = bill.getString("billno");
        if ("4".equals(bill.getBoolean("yd_tbly"))) {
            throw new KDBizException("源销售出库单" + billNo + "的同步来源系统不是PCP，无法执行清理操作！");
        }
        if (!bill.getBoolean("yd_isoffline")) {
            throw new KDBizException("源销售出库单" + billNo + "不是PCP线下一盘货结算，无法执行清理操作！");
        }
        if (bill.getBoolean("yd_internaltoeas")) {
            throw new KDBizException("源销售出库单" + billNo + "涉及的内部结算单据已全部传递EAS，需要在EAS与中台手工回退！");
        }
        if (!bill.getBoolean("yd_internalsettle")) {
            throw new KDBizException("源销售出库单" + billNo + "不是末级组织结算单，无法执行清理操作！");
        }
        if (!"C".equals(bill.getString("billstatus"))) {
            throw new KDBizException("源销售出库单" + billNo + "未发起过内部结算，无法执行清理操作！");
        }
    }

    /**
     * 清除下游单据
     * @author: hst
     * @createDate: 2022/10/31
     * @param id
     * @param srcBillEntity
     */
    public static void clearDownStreamBill (String id, String srcBillEntity) {
        ConvertDataService dataService = new ConvertDataService();
        LinkedList<String> billRelList = new LinkedList<String>();
        StringBuffer errorStr = new StringBuffer();
        while(org.apache.commons.lang3.StringUtils.isNotEmpty(id)) {
            // 遍历单据，获取下游单据数据
            Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(id)});
            if (dirTargetBill.size() == 1) {
                Long tempLongId = dirTargetBill.keySet().iterator().next();
                List<BFRow> targetRow = dirTargetBill.get(tempLongId);
                BFRowId targetBillRow = targetRow.get(0).getId();
                srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
                id = targetBillRow.getBillId().toString();
                billRelList.add(srcBillEntity+"&"+id);
            } else {
                id = "";
                srcBillEntity = "";
            }
        }
        // 从下到上删单
        for(int i=billRelList.size()-1;i>=0;i--) {
            String[] keys = billRelList.get(i).split("&");
            String entity = keys[0];
            String tempBillId = keys[1];
            DynamicObject billObj = BusinessDataServiceHelper.loadSingle(tempBillId, entity);
            try {
                checkPCPBillSendToEAS(billObj);
            } catch (KDBizException e) {
                errorStr.append(e.getMessage());
                continue;
            }
            String billstatus = billObj.getString("billstatus");
            String billName = "";
            billName = "im_purinbill".equals(entity) ? "采购入库单" : "im_saloutbill".equals(entity) ? "销售出库单" : "";
            if ("B".equals(billstatus)) {
                // 撤销再删除
                execOperation("unsubmit","撤销提交", entity, billName,billObj);
            } else if ("C".equals(billstatus)) {
                // 反审再删除
                execOperation("unaudit","反审核",  entity, billName,billObj);
            }
            // 删除单据
            execOperation("delete","删除",  entity, billName,billObj);
        }
        if (errorStr.length() > 0) {
            throw new KDBizException(errorStr.toString());
        }
    }

    /**
     * 执行特定操作
     * @param operationKey 操作标识
     * @param operateName 操作名称
     * @param entityName 实体标识
     * @param bill 单据
     * @param billName 菜单名称
     * @author: hst
     * @createDate: 2022/10/31
     * @return
     */
    public static void execOperation(String operationKey, String operateName, String entityName, String billName, DynamicObject bill) {
        OperationResult result = OperationServiceHelper.executeOperate(operationKey, entityName, new DynamicObject[]{bill}, OperateOption.create());
        if (!result.isSuccess()) {
            // 错误摘要
            throw new KDException("单据"+bill.getString("billno") + operateName +
                    "失败，请到"+billName+"手动撤销提交，查看撤销提交失败原因：" + result.getMessage());
        }
    }

    /**
     * 校验客户是否存在默认税率
     * @author: hst
     * @createDate: 2022/12/27
     * @param context
     * @param outBill
     */
    private static void verifycusTaxRate(Map<String, Object> context, DynamicObject outBill) {
        String cusNumber = outBill.getString("yd_customnumber");
        if (context.containsKey("cus_"+ cusNumber)) {
            if (!((Boolean) context.get("cus_"+ cusNumber))) {
                throw new KDBizException("客户[" + cusNumber + "]默认税率为空");
            }
        } else {
            QFilter cusFilter = new QFilter("number", QCP.equals, cusNumber);
            cusFilter.and(new QFilter("status", QCP.equals, "C"));
            DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel,taxrate", cusFilter.toArray());
            DynamicObject customer = null;
            if (customers.length > 0) {
                customer = customers[0];
            }
            if (Objects.nonNull(customer)) {
                DynamicObject cusTaxRateObj = customer.getDynamicObject("taxrate");
                if (Objects.isNull(cusTaxRateObj)) {
                    context.put("cus_"+ cusNumber,false);
                    throw new KDBizException("客户[" + cusNumber + "]默认税率为空");
                } else {
                    context.put("cus_"+ cusNumber,true);
                }
            } else {
                throw new KDBizException("[" + cusNumber + "]不存在当前客户信息！");
            }
        }
    }

    /**
     * PCP销售出库单中间表一盘货下推生成销售出库单(虚单结算类)
     * @author: hst
     * @createDate: 2023/02/15
     * @param context 上下文
     * @param billId 销售出库单中间表ID
     * @param isExclude 是否排除物料、仓库
     */
    public static String[] transSaleOutBill_virtual_trans(Map<String, Object> context, long billId, boolean isExclude) {
        DynamicObject outBill = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId);
        // 获取物料信息
        DynamicObjectCollection materials = outBill.getDynamicObjectCollection("entryentity");
        List<List<DynamicObject>> splitMaterials = new ArrayList<>();
        splitMaterials.add(Arrays.stream(materials.toArray(new DynamicObject[materials.size()])).collect(Collectors.toList()));
        //下推生成销售出库单
        String outId = generateSaleOutBill(context,outBill,splitMaterials);
        // update by hst 2022/12/26 不拆分时保存失败异常处理优化
        return outId.length() > 0 ? outId.split(",") : new String[0];
    }

    /**
     *  PCP销售出库单虚体组织结算
     * @author: hst
     * @createDate: 2023/02/16
     * @param entityName 单据类型
     * @param operationType 操作标识
     * @param qFilter 查询单据的过滤条件
     * @param tip 当获取不到单据的提示信息
     */
    public static void createPCPVirtualSettleBill (String entityName,String operationType, QFilter qFilter, String tip) {
        DataSet saleBillSet = null;
        try {
            saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", entityName, "id,billno", qFilter.toArray(), null);
            if (!saleBillSet.hasNext()) {
                if (StringUtils.isNotBlank(tip)) {
                    throw new KDBizException(tip);
                } else {
                    return;
                }
            }
            Set<String> doneBillSet = new HashSet<>();
            for (Row row : saleBillSet) {
                //单据id
                String billId = row.getString("id");
                //避免对一条单据重复处理
                if (doneBillSet.contains(billId) || Strings.isBlank(billId)) {
                    continue;
                }
                // 如果有互斥锁则提示不允许操作
                Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(billId, entityName, operationType);
                if (lockInfo != null) {
                    throw new KDBizException("系统定时的调度计划在执行中，请勿重复操作发起内部结算！");
                }
                // 对进行结算的出库单进行加锁
                MutexServiceHelper.request(billId, entityName, operationType);
                try {
                    DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId,entityName);
                    ApiResult result = new ApiResult();
                    // 虚体组织结算只有正向流程
                    result = SaleOutBillMserviceHelper.createVirtualPCPSettleBill(bill);
                    if (!result.getSuccess()) {
                        throw new KDBizException(result.getMessage());
                    }
                    // 使用关联关系将生成的下游单据保存到源销售出库单上
                    InternalSettleServiceHelper.saveVirtualBotpBills(billId);
                } catch(Exception e) {
                    // update by hst 2022/11/28 记录异常信息反写到单据字段信息中
                    DynamicObject BillInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_SALEOUTBILL, billId, "billno,yd_settleerror");
                    String errMessage = e.getMessage();
                    // 如果字段过长，则截取长度
                    if (errMessage.length() > 2000) {
                        errMessage = errMessage.substring(0, 2000);
                    }
                    BillInfo.set("yd_settleerror", errMessage);
                    SaveServiceHelper.save(new DynamicObject[]{BillInfo});
                } finally {
                    // 释放锁
                    MutexServiceHelper.release(billId, entityName, operationType);
                }
            }
        } finally {
            // 释放DataSet
            ORMUtils.close(saleBillSet);
        }
    }

    /**
     * 校验是否符合重置PCP虚体组织结算条件
     * @author: hst
     * @createDate: 2022/10/31
     * @param bill
     */
    public static void checkPCPVirtualResetSettle (DynamicObject bill) {
        // 不是审核状态不允许
        String billNo = bill.getString("billno");
        if ("4".equals(bill.getBoolean("yd_tbly"))) {
            throw new KDBizException("源销售出库单" + billNo + "的同步来源系统不是PCP，无法执行清理操作！");
        }
        if (!bill.getBoolean("yd_isvirtual")) {
            throw new KDBizException("源销售出库单" + billNo + "不是PCP虚单结算，无法执行清理操作！");
        }
        if (bill.getBoolean("yd_internaltoeas")) {
            throw new KDBizException("源销售出库单" + billNo + "涉及的内部结算单据已全部传递EAS，需要在EAS与中台手工回退！");
        }
        // update by hst 2023/04/27 修改一级组织校验条件
        if (!bill.getBoolean("yd_firstinternalbill")) {
            throw new KDBizException("源销售出库单" + billNo + "不是一级组织结算单，无法执行清理操作！");
        }
    }

    /**
     * 校验下游单据是否已推送EAS
     * @author: hst
     * @createDate: 2023/02/22
     */
    public static void checkPCPBillSendToEAS (DynamicObject bill) {
        if (bill.getBoolean("yd_checkboxsf01")) {
            throw new KDBizException("单据" + bill.getString("billno") + "已推送EAS，需要在EAS与中台手工回退;");
        }
    }

    /**
     * 获取下游单据
     * @author: hst
     * @createDate: 2023/02/22
     * @param id
     * @param srcBillEntity
     */
    public static void getDownStreamBill (String id, String srcBillEntity, LinkedList<String> billRelList) {
        ConvertDataService dataService = new ConvertDataService();
        while(org.apache.commons.lang3.StringUtils.isNotEmpty(id)) {
            // 遍历单据，获取下游单据数据
            Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(id)});
            if (dirTargetBill.size() == 1) {
                Long tempLongId = dirTargetBill.keySet().iterator().next();
                List<BFRow> targetRow = dirTargetBill.get(tempLongId);
                BFRowId targetBillRow = targetRow.get(0).getId();
                srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
                id = targetBillRow.getBillId().toString();
                billRelList.add(srcBillEntity+"&"+id);
            } else {
                id = "";
                srcBillEntity = "";
            }
        }
    }

    /**
     * 清除下游单据（虚体组织结算)
     * @author: hst
     * @createDate: 2023/02/23
     * @param id
     * @param srcBillEntity
     */
    public static void clearVirtualDownStreamBill (String id, String srcBillEntity) {
        ConvertDataService dataService = new ConvertDataService();
        LinkedList<String> billRelList = new LinkedList<String>();
//        StringBuffer errorStr = new StringBuffer();
        // 遍历单据，获取下游单据数据，虚体组织结算与其他不同，分两层生成
        Map<Long, List<BFRow>> dirTargetBills = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(id)});
        if (dirTargetBills.size() > 0) {
            for (BFRow targetRow : dirTargetBills.get(Long.valueOf(id))) {
                BFRowId targetBillRow = targetRow.getId();
                srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
                id = targetBillRow.getBillId().toString();
                billRelList.add(srcBillEntity + "&" + id);
                // 分层获取下游单据
                getDownStreamBill(id, srcBillEntity, billRelList);
            }
            // 从下到上删单
            for (int i = billRelList.size() - 1; i >= 0; i--) {
                String[] keys = billRelList.get(i).split("&");
                String entity = keys[0];
                String tempBillId = keys[1];
                DynamicObject billObj = BusinessDataServiceHelper.loadSingle(tempBillId, entity);
//                try {
//                    checkPCPBillSendToEAS(billObj);
//                } catch (KDBizException e) {
//                    errorStr.append(e.getMessage());
//                    continue;
//                }
                String billstatus = billObj.getString("billstatus");
                String billName = "";
                billName = "im_purinbill".equals(entity) ? "采购入库单" : "im_saloutbill".equals(entity) ? "销售出库单" : "";
                if ("B".equals(billstatus)) {
                    // 撤销再删除
                    execOperation("unsubmit", "撤销提交", entity, billName, billObj);
                } else if ("C".equals(billstatus)) {
                    // 反审再删除
                    execOperation("unaudit", "反审核", entity, billName, billObj);
                }
                // 删除单据
                execOperation("delete", "删除", entity, billName, billObj);
            }
        }
//        if (errorStr.length() > 0) {
//            throw new KDBizException(errorStr.toString());
//        }
    }

    /**
     * 设置批次对应的生产日期和到期日
     * @param lot
     * @param matNum
     * @param wsNum
     * @return
     */
    public static Object[] getMaterialLotInfo(String lot, String matNum, String wsNum, Boolean isReturn) {
        QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
        filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
        if (StringUtils.isNotBlank(lot)) {
            filter.and(new QFilter("yd_flot", QCP.equals, lot));
        }
        // 如果是正向的需要查数量大于0的批次
        if (!isReturn) {
            filter.and(new QFilter("yd_qty", QCP.large_than, 0));
        }
        DataSet dataSet = null;
        try {
            dataSet = BizHelper.getQueryDataSet("inv", "yd_easinventory", "yd_flot,yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
            if (dataSet.hasNext()) {
                Row row = dataSet.next();
                return new Object[] {row.getString("yd_flot"), row.getDate("yd_mfgdate"), row.getDate("yd_expdate")};
            }
            return null;
        }finally {
            ORMUtils.close(dataSet);
        }
    }

    /**
     * PCP销售出库单中间表一盘货下推生成销售出库单(线下一盘货退货结算）
     * @author: hst
     * @createDate: 2023/09/07
     * @param context 上下文
     * @param billId 销售出库单中间表ID
     * @param isExclude 是否排除物料、仓库
     */
    public static String[] transSaleOutBill_offline_trans(Map<String, Object> context, long billId, boolean isExclude) {
        DynamicObject outBill = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDSALEOUTBILL, billId);
        // 排除物料，仓库后需要下推的物料明细
        DynamicObjectCollection materials = eliminateMaterialAndWarehouse(context,outBill,isExclude,isExclude);
        // 根据结算路径按品牌拆分物料
        Map<String,List<DynamicObject>> splitMaterials = splitMaterialBySettle(context,outBill,materials,"1");
        //下推生成销售出库单
        String outId = generateSaleOutBill(outBill,splitMaterials);
        return outId.length() > 0 ? outId.split(",") : new String[0];
    }

    /**
     * 根据结算路径按品牌拆分物料
     * @author: hst
     * @createDate: 2023/09/06
     * @param context 上下文
     * @param bill 销售出库单中间表
     * @param materials 物料明细
     * @param type 类型
     */
    private static Map<String,List<DynamicObject>> splitMaterialBySettle (Map<String, Object> context,
                                                                          DynamicObject bill, DynamicObjectCollection materials, String type) {
        StringBuffer brandErrString = new StringBuffer();
        StringBuilder settleErrString = new StringBuilder();
        // update by hst 2023/09/26 修改拆分方式
        Map<String,List<String>> settels = getSettlePathMap(bill, type);
        Map<String, List<DynamicObject>> materialMap = new HashMap<>();
        // 获取结算路径
        for (DynamicObject materialInfo : materials) {
            //查询物料所属品牌
            String matNumber = materialInfo.getString("yd_materialnumber");
            DynamicObject material = BusinessDataServiceHelper.loadSingle("bd_material", "yd_basedatafield",
                    new QFilter[]{new QFilter("number", QFilter.equals, matNumber)});
            if (Objects.nonNull(material)) {
                //按品牌分组存储
                String brand = Objects.nonNull(material.getDynamicObject("yd_basedatafield")) ?
                        material.getDynamicObject("yd_basedatafield").getString("number") : "";
                if (StringUtils.isBlank(brand)) {
                    brandErrString.append(matNumber + ",");
                    continue;
                }
                // update by hst 2023/09/26 修改拆分方式
                // 标记当前物料是否存在结算路径
                boolean isExist = false;
                for (Map.Entry<String,List<String>> entry : settels.entrySet()) {
                    String key = entry.getKey();
                    List<String> brands = entry.getValue();
                    if (brands.indexOf(brand) >= 0) {
                        if (materialMap.containsKey(key)) {
                            List<DynamicObject> temp = materialMap.get(key);
                            temp.add(materialInfo);
                            materialMap.put(key, temp);
                        } else {
                            List<DynamicObject> temp = new ArrayList<>();
                            temp.add(materialInfo);
                            materialMap.put(key, temp);
                        }
                        isExist = true;
                    }
                }
                if (!isExist) {
                    settleErrString.append(matNumber + ",");
                }
            }
        }
        if (brandErrString.length() > 0) {
            throw new KDBizException("物料[" + brandErrString.deleteCharAt(brandErrString.length() - 1).toString() + "]查询不到对应品牌\n");
        }
        if (settleErrString.length() > 0) {
            throw new KDBizException("物料[" + settleErrString.deleteCharAt(settleErrString.length() - 1).toString()
                    + "]对应品牌未设置结算路径\n");
        }
        return materialMap;
    }

    /**
     * 获取线下一盘货业务结算路径
     * @author: hst
     * @createDate: 2023/03/22
     * @param bill
     * @return
     */
    public static DataSet getOfflineSettlePath (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        String stockOrg = bill.getString("yd_orgnumber");
        String settleOrg = bill.getString("yd_settleorgnumber");
        if (Objects.isNull(stockOrg)) {
            throw new KDBizException("获取不到库存组织信息，无法获取结算路径！");
        }
        if (Objects.isNull(settleOrg)) {
            throw new KDBizException("获取不到结算组织信息，无法获取结算路径！");
        }
        // 过滤条件
        qFilters.add(new QFilter("yd_stockorg.yd_eas_number", QCP.equals, stockOrg));
        qFilters.add(new QFilter("yd_settleorg.yd_eas_number", QCP.equals, settleOrg));
        qFilters.add(new QFilter("status", QCP.equals, "C"));
        // 错误提示
        String tip = "";
        tip = "当前库存组织：" + stockOrg + " 与结算组织：" + settleOrg + " 没有配置线下一盘货结算关系！";
        DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_offlinesettle",
                "yd_settleorg.id newCusId,yd_isreturn isReturn,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
                        "yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName,yd_brandentity.yd_brand.number brandNum," +
                        "yd_brandentity.yd_brand.name brandName", qFilters.toArray(new QFilter[qFilters.size()]), null);
        if (!settleDataSet.hasNext()) {
            // 品牌没有配置对应的内部结算关系，不生成内部结算单据
            throw new KDBizException(tip);
        }
        return settleDataSet;
    }

    /**
     * 下推生成一盘货销售出库单
     * @author: hst
     * @createDate: 2023/09/06
     * @param oriBill 原单据
     * @param splitMaterials 物料信息
     */
    public static String generateSaleOutBill (DynamicObject oriBill, Map<String,List<DynamicObject>> splitMaterials) {
        //生成的销售出库单单号，回写到来源单据中
        StringBuffer outBillNo = new StringBuffer();
        //生成的销售出库单ID，用于绑定botp关系
        StringBuffer outId = new StringBuffer();
        for (Map.Entry<String, List<DynamicObject>> splitMaterial : splitMaterials.entrySet()) {
            String key = splitMaterial.getKey();
            String isReturn = key.split("&")[1];
            IFormView saleView = generateSaleOutBill(oriBill, splitMaterial.getValue(), Boolean.valueOf(isReturn));
            try {
                if (Objects.nonNull(saleView)) {
                    // 触发保存功能
                    OperationResult result = ABillServiceHelper.saveOperate(saleView);
                    IDataModel saleModel = saleView.getModel();
                    if (result.isSuccess()) {
                        outBillNo.append(saleModel.getValue("billno") + ",");
                        outId.append(result.getSuccessPkIds().get(0) + ",");
                    }
                    String errorMessage = BizHelper.getOperationErrorMessage(result);
                    if (StringUtils.isNotBlank(errorMessage)) {
                        //一张保存失败的，删除之前生成的单据
                        // update by hst 2022/12/26 不拆分时保存失败异常处理优化
                        if (outId.length() > 0) {
                            DeleteServiceHelper.delete("im_saloutbill",
                                    new QFilter[]{new QFilter("id", QFilter.in, outId.deleteCharAt(outId.length() - 1).toString().split(","))});
                        }
                        throw new KDBizException("销售出库单[" + saleModel.getValue("billno") + "]保存失败，查看保存失败原因：" + errorMessage);
                    }
                }
            } finally {
                ABillServiceHelper.exitView(saleView);
            }
        }
        if (outBillNo.length() > 0) {
            oriBill.set("yd_ishasnext", true);
            oriBill.set("yd_salebillnumber", outBillNo.deleteCharAt(outBillNo.length() - 1).toString());
            oriBill.set("yd_errmessage", ""); // 错误信息重置为空
            BizHelper.saveDynamicObject(oriBill);
        }
        return outId.length() > 0 ? outId.deleteCharAt(outId.length() - 1).toString() : "";
    }

    /**
     * 下推生成一盘货销售出库单
     * @author: hst
     * @createDate: 2023/09/06
     * @param oriBill 原单据
     * @param splitMaterial 物料信息
     * @param isReturn 是否退货结算
     */
    private static IFormView generateSaleOutBill (DynamicObject oriBill,List<DynamicObject> splitMaterial, Boolean isReturn) {
        String orgNumber = oriBill.getString("yd_orgnumber"); // 库存组织编码
        String saleOrgNumber = oriBill.getString("yd_saleorgnumber"); // 销售组织编码
        String settleOrgNumber = oriBill.getString("yd_settleorgnumber"); // 结算组织编码
        String sourceFunction = oriBill.getString("yd_sourcefunction"); // 同步来源系统
        String sourceFunction2 = "PCP".equals(sourceFunction) ? "4" : null;
        //组织
        Long orgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id",
                QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber=?", orgNumber).toArray());
        //销售组织
        Long saleOrgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id",
                QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber=?", saleOrgNumber).toArray());
        //结算组织
        Long settleOrgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id",
                QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber=?", settleOrgNumber).toArray());
        //新增销售出库单
        IFormView saleView = ABillServiceHelper.createAddView(BillTypeHelper.BILLTYPE_SALEOUTBILL);
        IDataModel saleModel = saleView.getModel();
        //赋值
        saleModel.setItemValueByNumber("billtype", "im_SalOutBill_STD_BT_S"); // 单据类型（标准销售出库单）
        saleModel.setItemValueByID("org", orgId); // 库存组织
        saleModel.setItemValueByID("bizorg", saleOrgId); // 销售组织
        saleModel.setItemValueByNumber("biztype", oriBill.getString("yd_biztype")); // 业务类型
        saleModel.setItemValueByNumber("invscheme", oriBill.getString("yd_transno")); // 库存事务
        saleModel.setItemValueByNumber("customer", oriBill.getString("yd_customnumber")); // 客户
        saleModel.setItemValueByNumber("yd_dzdkh", oriBill.getString("yd_customnumber")); // 对账客户
        saleModel.setValue("biztime", oriBill.getDate("yd_bizdate")); // 业务日期
        saleModel.setValue("yd_tbly", sourceFunction2); // 同步来源系统
        saleModel.setValue("yd_sourcebilltype", 3);  // 来源单据类型 —— PCP销售出库单
        saleModel.setValue("yd_saleordernumber", oriBill.getString("yd_saleordernumber")); // 来源销售订单
        saleModel.setValue("yd_platformorderno", oriBill.getString("yd_platformorderno")); // 营销云单号（csp单号）
        saleModel.setValue("comment", oriBill.getString("yd_description")); // 备注
        saleModel.setValue("yd_businesstype", oriBill.getString("yd_businesstype")); // 业务类型
        saleModel.setValue("yd_sourcetradenumber", oriBill.getString("yd_sourcetradenumber")); // 内部交易源头单号
        saleModel.setValue("yd_office", oriBill.getString("yd_office")); // 办事处
        saleModel.setValue("yd_province", oriBill.getString("yd_province")); // 省份
        saleModel.setValue("yd_city", oriBill.getString("yd_city")); // 城市
        saleModel.setValue("yd_district", oriBill.getString("yd_district")); // 地区
        saleModel.setValue("yd_linkman", oriBill.getString("yd_linkman")); // 联系人
        saleModel.setValue("yd_phone", oriBill.getString("yd_phone")); // 联系方式
        saleModel.setValue("yd_address", oriBill.getString("yd_address")); // 地址
        saleModel.setValue("yd_description2", oriBill.getString("yd_description2")); // 摘要
        saleModel.setValue("yd_isinventory", oriBill.getString("yd_isinventory"));  //是否一盘货
        saleModel.setValue("yd_isvirtual", oriBill.getString("yd_isvirtual"));  // update by hst 2023/02/15 是否虚单结算
        saleModel.setValue("yd_purorder", oriBill.getString("yd_purorder"));  // update by hst 2023/02/15 采购订单号
        saleModel.setItemValueByID("yd_settleorg", settleOrgId); // 结算组织
        saleModel.setValue("billstatus", "A");
        // 是否线下一盘货
        saleModel.setValue("yd_isoffline",oriBill.getBoolean("yd_isoffline"));
        // PCP单据类型
        saleModel.setValue("yd_ordertype",oriBill.getString("yd_ordertype"));
        // 是否退货结算
        saleModel.setValue("yd_isreturn",isReturn.booleanValue());
        //分录信息
        int index = 0;
        if (splitMaterial.size() > 0) {
            for (DynamicObject material : splitMaterial) {
                //新建分录行
                saleModel.createNewEntryRow("billentry");
                saleModel.setItemValueByNumber("material", material.getString("yd_materialnumber"), index); // 物料
                saleModel.setValue("yd_ph", material.getString("yd_batchno"), index); // E3批号
                saleModel.setValue("yd_scrq", material.getDate("yd_probegindate"), index); // E3生产日期
                saleModel.setValue("yd_dqr", material.getDate("yd_proenddate"), index); // E3到期日
                saleModel.setValue("qty", material.getBigDecimal("yd_quantity"), index); // 数量
                BigDecimal discountAmount = material.getBigDecimal("yd_discountamount"); // 总折扣额
                saleModel.setValue("amountandtax", material.getBigDecimal("yd_actuallyamount").add(discountAmount), index); // 应付金额 —— 用来统计出单价和后续折扣额的统计
                if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                    saleModel.setValue("discounttype", material.getString("yd_discounttype"), index); // 折扣方式
                    saleModel.setValue("discountamount", discountAmount, index); // 折扣额
                }
                saleModel.setValue("amountandtax", material.getBigDecimal("yd_actuallyamount"), index); // 实付金额 —— 最终标识的实付金额
                saleModel.setItemValueByNumber("warehouse", material.getString("yd_warehousenumber"), index); // 仓库
                saleModel.setValue("ispresent", material.getBoolean("yd_ispresent"), index);    // 是否赠品
                saleModel.setValue("yd_isintegral", material.getBoolean("yd_isintegral"), index);    // 是否积分产品
                saleModel.setValue("yd_integral", material.getString("yd_integral"), index);    // 使用积分
                saleModel.setValue("yd_subdivisionplatform", material.getString("yd_subdivisionplatform"), index);    // 电商渠道
                saleModel.setValue("yd_saledistribution", material.getString("yd_saledistribution"), index);    // 销售渠道
                saleModel.setValue("yd_easorderentryid", material.getString("yd_easorderentryid"), index);  // 销售订单分录ID
                saleModel.setValue("srcsysbillentryid", material.getPkValue(), index);  // 来源系统单据分录ID
                saleModel.setValue("srcsysbillno", oriBill.getString("billno"), index);  // 来源系统单据编号
                saleModel.setValue("srcsysbillid", oriBill.getPkValue(), index);  // 来源系统单据ID
                saleModel.setValue("srcbillid", oriBill.getPkValue(), index);  // 来源单据ID
                saleModel.setValue("srcbillentryid", material.getPkValue(), index);  // 来源单据行ID
                saleModel.setValue("srcbillentryseq", material.get("seq"), index);  // 来源单据分录序号
                saleModel.setValue("srcbillnumber", oriBill.getString("billno"), index);  // 来源单据分录序号
                // 查询税率基础资料
                DataSet dataSet = null;
                String taxRateNum = null;
                String customNumber = oriBill.getString("yd_customnumber"); // 客户编码
                try {
                    dataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), BillTypeHelper.BILLTYPE_CUSTOMER, "taxrate.number as taxRateNum", QFilter.of("status='C' and number=?", customNumber).toArray());
                    if (dataSet.hasNext()) {
                        Row row = dataSet.next();
                        taxRateNum = row.getString("taxRateNum");
                    }
                } finally {
                    ORMUtils.close(dataSet);
                }
                saleModel.setItemValueByNumber("taxrateid", taxRateNum, index); // 税率

                BigDecimal totalAmount = material.getBigDecimal("yd_totalamount"); // update by hst 2023/12/08 应付金额(折前价税合计)
                BigDecimal activityAmount = material.getBigDecimal("yd_activityamount"); // update by hst 2023/12/08 促销活动折扣
                BigDecimal rebateAmount = material.getBigDecimal("yd_rebateamount"); // update by hst 2023/12/08 销售返利折扣

                saleModel.setValue("yd_totaleasamount", totalAmount, index);  // update by hst 2023/12/08 应付金额(折前价税合计)
                saleModel.setValue("yd_activityamount", activityAmount, index);  // update by hst 2023/12/08 促销活动折扣
                saleModel.setValue("yd_rebateamount", rebateAmount, index);  // update by hst 2023/12/08 销售返利折扣

                // update by hst 2023/12/18 促销活动折扣率\销售返利折扣率 计算
                if (Objects.nonNull(totalAmount) && totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                    if (Objects.nonNull(activityAmount) && activityAmount.compareTo(BigDecimal.ZERO) > 0) {
                        saleModel.setValue("yd_activityrate", (activityAmount.divide(totalAmount,4, BigDecimal.ROUND_HALF_UP))
                                .multiply(new BigDecimal("100")).setScale(2), index);  // update by hst 2023/12/18 促销活动折扣率
                    }
                    if (Objects.nonNull(rebateAmount) && rebateAmount.compareTo(BigDecimal.ZERO) > 0) {
                        saleModel.setValue("yd_rebaterate", (rebateAmount.divide(totalAmount,4, BigDecimal.ROUND_HALF_UP))
                                .multiply(new BigDecimal("100")).setScale(2), index);  // update by hst 2023/12/18 销售返利折扣率
                    }
                    if (Objects.nonNull(activityAmount) && Objects.nonNull(rebateAmount)) {
                        if ((activityAmount.add(rebateAmount)).compareTo(totalAmount) == 0) {
                            saleModel.setValue("yd_isdiscount", true, index);  // update by hst 2023/12/18 折扣品

                        }
                    }
                }
                index++;
            }
            return saleView;
        }
        return null;
    }

    /**
     * 获取结算路径
     * @author: hst
     * @createDate  : 2022/09/26
     * @param bill 单据
     * @param type （1: 线下一盘货）
     * @return (path:结算路径)
     */
    public static Map<String,List<String>> getSettlePathMap (DynamicObject bill, String type) {
        Map<String,List<String>> settles = null;
        switch (type) {
            case "1" : {
                settles = getOfflineSettlePathMap(bill);
                break;
            }
        }
        if (Objects.isNull(settles)) {
            throw new KDBizException("未能获取到当前库存组织与结算组织的结算关系，请联系管理员！");
        }
        return settles;
    }

    /**
     * 获取线下一盘货业务结算路径
     * @author: hst
     * @createDate: 2023/09/26
     * @param bill
     * @return
     */
    public static Map<String,List<String>> getOfflineSettlePathMap (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        String stockOrg = bill.getString("yd_orgnumber");
        String settleOrg = bill.getString("yd_settleorgnumber");
        if (Objects.isNull(stockOrg)) {
            throw new KDBizException("获取不到库存组织信息，无法获取结算路径！");
        }
        if (Objects.isNull(settleOrg)) {
            throw new KDBizException("获取不到结算组织信息，无法获取结算路径！");
        }
        // 过滤条件
        qFilters.add(new QFilter("yd_stockorg.yd_eas_number", QCP.equals, stockOrg));
        qFilters.add(new QFilter("yd_settleorg.yd_eas_number", QCP.equals, settleOrg));
        qFilters.add(new QFilter("status", QCP.equals, "C"));
        // 错误提示
        String tip = "";
        tip = "当前库存组织：" + stockOrg + " 与结算组织：" + settleOrg + " 没有配置线下一盘货结算关系！";
        DynamicObject[] settels = BusinessDataServiceHelper.load("yd_offlinesettle",
                "yd_settleorg,yd_isreturn,yd_levelentry.yd_orglevel,yd_levelentry.yd_org," +
                        "yd_levelentry.yd_org,yd_brandentity.yd_brand",
                qFilters.toArray(new QFilter[qFilters.size()]), null);
        if (Objects.isNull(settels) || settels.length == 0) {
            // 品牌没有配置对应的内部结算关系，不生成内部结算单据
            throw new KDBizException(tip);
        }
        Map<String,List<String>> settelMap = new HashMap<>();
        for (DynamicObject settel : settels) {
            String number = settel.getString("number");
            boolean isReturn = settel.getBoolean("yd_isreturn");
            List<String> temp = new ArrayList<>();
            for (DynamicObject entry : settel.getDynamicObjectCollection("yd_brandentity")) {
                DynamicObject brand = entry.getDynamicObject("yd_brand");
                if (Objects.nonNull(brand)) {
                    temp.add(brand.getString("number"));
                }
            }
            settelMap.put(number + "&" + isReturn,temp);
        }
        return settelMap;
    }

    /**
     * 校验单据是否允许删除
     * @param bill
     * @param billType
     * @return
     * @author: hst
     * @createDate: 2024/05/16
     */
    private static boolean checkIfDeletionIsAllowed (DynamicObject bill, String billType) {
        boolean isAllowed = true;
        // 销售出库单
        if ("im_saloutbill".equals(billType)) {
            boolean isAudit = bill.getBoolean("yd_checkboxsf");
            if (isAudit) {
                isAllowed = false;
            }
        } else if ("im_purinbill".equals(billType)) {
            String easStatus = bill.getString("yd_easstatus");
            boolean isToEas = bill.getBoolean("yd_issyneas");
            if (isToEas && "审核".equals(easStatus)) {
                isAllowed = false;
            }
        }
        return isAllowed;
    }
}