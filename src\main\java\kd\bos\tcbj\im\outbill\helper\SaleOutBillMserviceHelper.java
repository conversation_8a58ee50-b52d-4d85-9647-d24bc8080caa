package kd.bos.tcbj.im.outbill.helper;

import java.util.Set;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.tcbj.im.outbill.imp.SaleOutBillMserviceImpl;
import kd.bos.tcbj.im.outbill.mservice.SaleOutBillMservice;

/**
* @auditor yanzuwei
* @date 2021年12月30日
* 
*/
public class SaleOutBillMserviceHelper {
	
	/**
	 * 描述：正向内部结算流程
	 * 
	 * @createDate  : 2021-12-30
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param saleoutBillId 源末级销售出库单ID
	 * @return 是否成功
	 */
	public static ApiResult createForwardBill(String saleoutBillId) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			SaleOutBillMservice mservice = SaleOutBillMserviceImpl.getInstance();
			apiResult = mservice.createForwardBill(saleoutBillId);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}
	
	/**
	 * 描述：退货内部结算流程
	 * 
	 * @createDate  : 2021-12-30
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param saleoutBillId 源末级销售出库单ID
	 * @return 是否成功
	 */
	public static ApiResult createBackwardBill(String saleoutBillId) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			SaleOutBillMservice mservice = SaleOutBillMserviceImpl.getInstance();
			apiResult =mservice.createBackwardBill(saleoutBillId);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：正向直营店结算流程
	 *
	 * @createDate  : 2022-06-06
	 * @author: lzp
	 * @updateDate  :
	 * @updateAuthor:
	 * @param saleoutBillId 源末级销售出库单ID
	 * @return 是否成功
	 */
	public static ApiResult createDirectForwardBill(String saleoutBillId) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			SaleOutBillMservice mservice = SaleOutBillMserviceImpl.getInstance();
			apiResult =mservice.createDirectForwardBill(saleoutBillId);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：退货直营店结算流程
	 *
	 * @createDate  : 2022-06-06
	 * @author: 严祖威
	 * @updateDate:
	 * @updateAuthor:
	 * @param saleoutBillId 源末级销售出库单ID
	 * @return 是否成功
	 */
	public static ApiResult createDirectBackwardBill(String saleoutBillId) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			SaleOutBillMservice mservice = SaleOutBillMserviceImpl.getInstance();
			apiResult =mservice.createDirectBackwardBill(saleoutBillId);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * PCP销售出库单一盘货结算
	 * @author: hst
	 * @createDate  : 2022/10/28
	 * @param saleoutBill 单据
	 * @param isForward 是否是正向
	 * @return 是否成功
	 */
	public static ApiResult createPCPSettleBill (DynamicObject saleoutBill, boolean isForward) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			SaleOutBillMservice mservice = SaleOutBillMserviceImpl.getInstance();
			apiResult =mservice.createPCPSettleBill(saleoutBill,isForward);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}
	
	/**
	 * 描述：根据股份出库批次结果重置整条流程的出入库单的批次
	 * 
	 * @createDate  : 2022-12-26
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param lotBillIdSet 股份出库结果单ID集合
	 * @return 是否成功
	 */
	public static ApiResult resetSettleBillLot(Set<String> lotBillIdSet) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			SaleOutBillMservice mservice = SaleOutBillMserviceImpl.getInstance();
			apiResult = mservice.resetSettleBillLot(lotBillIdSet);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * PCP销售出库单虚体组织结算
	 * @author: hst
	 * @createDate  : 2023/02/16
	 * @param saleoutBill 单据
	 * @return 是否成功
	 */
	public static ApiResult createVirtualPCPSettleBill (DynamicObject saleoutBill) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			SaleOutBillMservice mservice = SaleOutBillMserviceImpl.getInstance();
			apiResult =mservice.createPCPVirtualSettleBill(saleoutBill);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：根据股份出库批次结果重置整条流程的出入库单的批次(以一个字段作为key)
	 *
	 * @createDate  : 2024/01/31
	 * @author: hst
	 * @updateDate  :
	 * @updateAuthor:
	 * @param lotBillIdSet 股份出库结果单ID集合
	 * @return 是否成功
	 */
	public static ApiResult resetSettleBillLotByKey(Set<String> lotBillIdSet) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			SaleOutBillMservice mservice = SaleOutBillMserviceImpl.getInstance();
			apiResult = mservice.resetSettleBillLotByKey(lotBillIdSet);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}
}
