package kd.bos.tcbj.im.outbill.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.DispatchServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.constants.SettleParamConstant;
import kd.bos.tcbj.im.util.MethodUtils;
import kd.bos.tcbj.im.util.ORMUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.epm.eb.ebBusiness.serviceHelper.MutexServiceHelper;
import org.apache.logging.log4j.util.Strings;

import java.util.*;

/**
 * 结算共性类
 * @package: kd.bos.tcbj.im.outbill.helper.SettleServiceHelper
 * @className: SettleServiceHelper
 * @author: hst
 * @createDate: 2023/05/12
 * @version: v1.0
 */
public class SettleServiceHelper {

    private static final Log log = LogFactory.getLog(SettleServiceHelper.class.getName());

    /**
     *
     * @param key
     * @param config
     * @param qFilter
     * @param tip
     * @author: hst
     * @createDate:
     */
    public static void createSettleBill (String key, String entityName, DynamicObject config, QFilter qFilter, String tip, boolean isThrow) {
        DataSet saleBillSet = null;
        try {
            saleBillSet = QueryServiceHelper.queryDataSet("createSettleBill", entityName,
                    "id,billno", qFilter.toArray(), null);
            if (!saleBillSet.hasNext()) {
                if (StringUtils.isNotBlank(tip)) {
                    if (isThrow) {
                        throw new KDBizException(tip);
                    }
                } else {
                    return;
                }
            }
            Set<String> doneBillSet = new HashSet<>();
            for (Row row : saleBillSet) {
                //单据id
                String billId = row.getString("id");
                //避免对一条单据重复处理
                if (doneBillSet.contains(billId) || Strings.isBlank(billId)) {
                    continue;
                }
                // 如果有互斥锁则提示不允许操作
                Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(billId, entityName, key);
                if (lockInfo != null) {
                    if (isThrow) {
                        throw new KDBizException("系统定时的调度计划在执行中，请勿重复操作发起结算！");
                    } else {
                        log.error(billId + "单据已锁定，请勿重复操作发起结算");
                        continue;
                    }
                }
                // 对进行结算的出库单进行加锁
                MutexServiceHelper.request(billId, entityName, key);
                try {
                    DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId,entityName);
                    ApiResult result = new ApiResult();
                    // 业务类型是2101(退货)的需要走反方向，210(销售)的走正方向
                    String biztype = bill.getString("biztype.number");
                    if ("210".equals(biztype) || "355".equals(biztype)) {
                        // 正向流程
                        result = (ApiResult) MethodUtils.invokeMethodByConfig(config.getString(SettleParamConstant.SETTLECLASS_FIELD),
                                config.getString(SettleParamConstant.SETTLEMETHOD_FIELD), bill, config, true);
                    } else if ("2101".equals(biztype) || "3551".equals(biztype)) {
                        // 逆向流程
                        result = (ApiResult) MethodUtils.invokeMethodByConfig(config.getString(SettleParamConstant.SETTLECLASS_FIELD),
                                config.getString(SettleParamConstant.SETTLEMETHOD_FIELD), bill, config, false);
                    }
                    if (!result.getSuccess()) {
                        throw new KDBizException(result.getMessage());
                    }
                } catch(Exception e) {
                    DynamicObject BillInfo = BizHelper.getDynamicObjectById(entityName, billId, "billno,yd_settleerror");
                    String errMessage = e.getMessage();
                    // 如果字段过长，则截取长度
                    if (StringUtils.isNotBlank(errMessage) && errMessage.length() > 2000) {
                        errMessage = errMessage.substring(0, 2000);
                    }
                    BillInfo.set("yd_settleerror", errMessage);
                    SaveServiceHelper.save(new DynamicObject[]{BillInfo});
                } finally {
                    // 释放锁
                    MutexServiceHelper.release(billId, entityName, key);
                }

                // 触发同步EAS
                executeIscFlowService(billId, entityName, config);
            }
        } finally {
            // 释放DataSet
            ORMUtils.close(saleBillSet);
        }
    }

    /**
     * 启动服务流程
     * @param billId
     * @param config 入参
     * @return
     * @author: hst
     * @createDate: 2024/06/19
     */
    private static void executeIscFlowService (String billId, String entityName, DynamicObject config) {
        String flowNumber = config.getString("yd_flow");

        if (StringUtils.isNotBlank(flowNumber)) {
            DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, entityName);
            if (Objects.nonNull(bill)) {
                boolean isSuccess = bill.getBoolean("yd_issettled");
                boolean isToEas = bill.getBoolean("yd_istoeas");

                if (isSuccess && !isToEas) {
                    boolean type = config.getBoolean("yd_flowtype");

                    // 构建服务流程参数
                    LinkedList<Object> inputs = new LinkedList<>();
                    inputs.add(billId);
                    inputs.add(bill.getString("yd_settletype"));

                    // 启动服务流程
                    String execType = type ? "execute" : "start";
                    DispatchServiceHelper.invokeBizService("isc", "iscb",
                            "IscFlowService", execType, flowNumber, inputs);
                }
            }
        }
    }
}
