package kd.bos.tcbj.im.outbill.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.DispatchServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.common.enums.PcpTransBizTypeEnum;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.constants.SettleParamConstant;
import kd.bos.tcbj.im.util.InterUtils;
import kd.bos.tcbj.im.util.MethodUtils;
import kd.bos.tcbj.im.util.ORMUtils;
import kd.bos.tcbj.im.util.SettleUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.epm.eb.ebBusiness.serviceHelper.MutexServiceHelper;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 调拨结算类
 * @package: kd.bos.tcbj.im.outbill.helper.TransSettleServiceHelper
 * @className: TransSettleServiceHelper
 * @author: hst
 * @createDate: 2024/05/26
 * @version: v1.0
 */
public class TransSettleServiceHelper {

    private static final Log log = LogFactory.getLog(TransSettleServiceHelper.class.getName());

    public void createSettleBill (String key, DynamicObject config, QFilter qFilter, String tip, boolean isThrow) {
        DataSet saleBillSet = null;
        try {
            saleBillSet = QueryServiceHelper.queryDataSet("createTransSettleBill", BillTypeHelper.BILLTYPE_MIDTRANSBILL,
                    "id,billno", qFilter.toArray(), null);
            if (!saleBillSet.hasNext()) {
                if (StringUtils.isNotBlank(tip)) {
                    if (isThrow) {
                        throw new KDBizException(tip);
                    }
                } else {
                    return;
                }
            }

            Set<String> doneBillSet = new HashSet<>();
            Map<String, Object> context = new HashMap<>();
            for (Row row : saleBillSet) {
                //单据id
                String billId = row.getString("id");
                //避免对一条单据重复处理
                if (doneBillSet.contains(billId) || Strings.isBlank(billId)) {
                    continue;
                }
                // 如果有互斥锁则提示不允许操作
                Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(billId, BillTypeHelper.BILLTYPE_MIDTRANSBILL, key);
                if (lockInfo != null) {
                    if (isThrow) {
                        throw new KDBizException("系统定时的调度计划在执行中，请勿重复操作发起结算！");
                    } else {
                        log.error(billId + "单据已锁定，请勿重复操作发起结算");
                        continue;
                    }
                }
                // 对进行结算的出库单进行加锁
                MutexServiceHelper.request(billId, BillTypeHelper.BILLTYPE_MIDTRANSBILL, key);

                try {
                    DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, BillTypeHelper.BILLTYPE_MIDTRANSBILL);
                    // 结算推送信息校验
                    this.interface_verify(context, bill);
                    // 跨组织
                    if (!bill.getString("yd_outorg.id").equals(bill.getString("yd_inorg.id"))) {
                        // 获取结算路径
                        this.getSettlePath(context, config, bill);
                        // 调拨中间表结算前校验
                        MethodUtils.invokeMethodByConfig(config.getString(SettleParamConstant.VAILCLASS_FIELD),
                                config.getString(SettleParamConstant.VAILMETHOD_FIELD), context, config, bill);
                        // 生成结算链路单据
                        MethodUtils.invokeMethodByConfig(config.getString(SettleParamConstant.SETTLECLASS_FIELD),
                                config.getString(SettleParamConstant.SETTLEMETHOD_FIELD), context, config, bill);
                        // 标记单据结算成功
                        this.settlementSuccessful_sign(bill.getString("id"), "");
                    } else {
                        // 同组织调拨
                        String tarBillNo = (String) MethodUtils.invokeMethodByConfig(config.getString(SettleParamConstant.TRANSCLASS_FIELD),
                                config.getString(SettleParamConstant.TRANSMETHOD_FIELD), config, bill);
                        // 标记单据结算成功
                        this.settlementSuccessful_sign(bill.getString("id"), tarBillNo);
                    }
                } catch (Exception e) {
                    DynamicObject billInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_MIDTRANSBILL, billId, "billno,yd_issettle,yd_settleerror");
                    String errMessage = e.getMessage();
                    // 如果字段过长，则截取长度
                    if (StringUtils.isNotBlank(e.getMessage()) && errMessage.length() > 2000) {
                        errMessage = errMessage.substring(0, 2000);
                    }
                    billInfo.set("yd_settleerror", errMessage);

                    // 校验是否有下游单
                    boolean isHasNext = SettleUtils.checkBillHasDownStream(billId, BillTypeHelper.BILLTYPE_MIDTRANSBILL);
                    if (isHasNext) {
                        billInfo.set("yd_issettle", true);
                    } else {
                        billInfo.set("yd_issettle", false);
                    }
                    SaveServiceHelper.save(new DynamicObject[]{billInfo});
                } finally {
                    MutexServiceHelper.release(billId, BillTypeHelper.BILLTYPE_MIDTRANSBILL, key);
                }

                // 触发同步EAS
                this.executeIscFlowService(billId, config);
            }
        } finally {
            // 释放DataSet
            ORMUtils.close(saleBillSet);
        }
    }

    /**
     * 结算推送信息校验
     * @param context
     * @param bill
     * @author: hst
     * @createDate: 2024/05/31
     */
    public void interface_verify(Map<String,Object> context, DynamicObject bill) {
        StringBuilder errBuilder = new StringBuilder();

        // 接口推送信息校验
        errBuilder.append(interfacePushInfo_verify(context, bill));

        if (errBuilder.length() > 0) {
            throw new KDBizException(errBuilder.toString());
        }
    }

    /**
     * 调拨中间表结算前校验
     * @param context
     * @param config
     * @param bill
     * @author: hst
     * @createDate: 2024/05/27
     */
    public void doTransSettle_verify(HashMap context, DynamicObject config, DynamicObject bill) {
        StringBuilder errBuilder = new StringBuilder();

        // 校验内部交易关系是否有已维护
        errBuilder.append(checkSettleMsg(context,config,bill));

        if (errBuilder.length() > 0) {
            throw new KDBizException(errBuilder.toString());
        }
    }

    /**
     * 接口推送信息校验
     * @param context
     * @param bill
     * @author: hst
     * @createDate: 2024/05/27
     */
    private String interfacePushInfo_verify (Map<String,Object> context, DynamicObject bill) {
        StringBuilder errBuilder = new StringBuilder();
        boolean isInter = bill.getBoolean("yd_isinter"); // 是否接口推送

        // 通过业务类型校验EAS单据ID是否为空
        String bizType = bill.getString("yd_businesstype");
        if ("internal_deal_ba".equals(bizType) || "route_internal_deal_ba".equals(bizType)) {
            // 内部交易类采购订单信息和销售订单信息不能为空
            String headWarning = InterUtils.verifyFieldIsNull(bill, new String[]{"yd_saleorderid", "yd_purorderid"});
            String entryWarning = InterUtils.verifyFieldIsNull(bill, "entryentity", new String[]{"yd_purorderentryid", "yd_saleorderentryid"});
            if (StringUtils.isNotBlank(headWarning) || StringUtils.isNotBlank(entryWarning)) {
                throw new KDBizException(new StringBuilder().append(SettleUtils.getBussinessTypeName(bizType) + "：\n").append(headWarning)
                        .append("\n").append(entryWarning).toString());
            }
        }
        if ("internal_deal_return_ba".equals(bizType)) {
            // 内部交易退货类采购退货申请信息和销售退货申请信息不能为空
            String headWarning = InterUtils.verifyFieldIsNull(bill, new String[]{"yd_purreturnid", "yd_salereturnid"});
            String entryWarning = InterUtils.verifyFieldIsNull(bill, "entryentity", new String[]{"yd_purreturnentryid", "yd_salereturnentryid"});
            if (StringUtils.isNotBlank(headWarning) || StringUtils.isNotBlank(entryWarning)) {
                throw new KDBizException(new StringBuilder().append(SettleUtils.getBussinessTypeName(bizType) + "：\n").append(headWarning)
                        .append("\n").append(entryWarning).toString());
            }
        }
        if ("sale_return_internal_deal_ba".equals(bizType)) {
            // 销售退货转内部交易类采购订单信息、销售订单信息、采购退货申请单信息、销售退货申请单不能为空
            String headWarning = InterUtils.verifyFieldIsNull(bill, new String[]{"yd_saleorderid", "yd_purorderid","yd_purreturnid", "yd_salereturnid"});
            String entryWarning = InterUtils.verifyFieldIsNull(bill, "entryentity", new String[]{"yd_purorderentryid", "yd_saleorderentryid",
                    "yd_purreturnentryid", "yd_salereturnentryid"});
            if (StringUtils.isNotBlank(headWarning) || StringUtils.isNotBlank(entryWarning)) {
                throw new KDBizException(new StringBuilder().append(SettleUtils.getBussinessTypeName(bizType) + "：\n").append(headWarning)
                        .append("\n").append(entryWarning).toString());
            }
        }

        // 如果是接口推送的字段，则需要校验中台是否存在对应的值匹配
        if (isInter) {
            // 校验必填字段，如果不符合则直接提示跳出
            // 校验表头字段信息
            String headWarning = InterUtils.verifyFieldIsNull(bill, new String[]{"billno", "yd_bizdate", "yd_interoutorg", "yd_interinorg",
                    "yd_interoutwarehouse", "yd_interinwarehouse","yd_businesstype"});
            // 校验分录表头信息
            String entryWarning = InterUtils.verifyFieldIsNull(bill, "entryentity", new String[]{"yd_intermaterial", "yd_intermaterialname", "yd_batchno", "yd_quantity"});
            if (StringUtils.isNotBlank(headWarning) || StringUtils.isNotBlank(entryWarning)) {
                throw new KDBizException(new StringBuilder().append(headWarning).append("\n").append(entryWarning).toString());
            }

            String outOrgNum = bill.getString("yd_interoutorg");
            String inOrgNum = bill.getString("yd_interinorg");
            Object outOrgValue = SettleUtils.getBaseInfoByContext("bos_org", outOrgNum, context);
            Object inOrgValue = SettleUtils.getBaseInfoByContext("bos_org", inOrgNum, context);
            if (outOrgValue == null || inOrgValue == null) {
                String errMsg = outOrgValue == null ? inOrgValue == null ? "[" + outOrgNum + "]不存在组织映射关系！\n[" + inOrgNum + "]不存在组织映射关系！\n"
                        : "[" + outOrgNum + "]不存在组织映射关系！\n" : "[" + inOrgNum + "]不存在组织映射关系！\n";
                errBuilder.append(errMsg);
            } else {
                bill.set("yd_outorg", outOrgValue);
                bill.set("yd_inorg", inOrgValue);
            }

            // 根据仓库的映射关系，校验仓库是否已经存在
            // 调出仓库
            String interOutWarehouse = bill.getString("yd_interoutwarehouse");
            // 调入仓库
            String interInWarehouse = bill.getString("yd_interinwarehouse");
            // 调出仓库
            DynamicObject outWareInfo = SettleUtils.getWarehouseByNumber(interOutWarehouse);
            // 调入仓库
            DynamicObject inWareInfo = SettleUtils.getWarehouseByNumber(interInWarehouse);
            // 校验调出仓库、调入仓库是否存在
            if (outWareInfo == null)
                errBuilder.append("[").append(interOutWarehouse).append("]不存在仓库信息！\n");
            if (inWareInfo == null)
                errBuilder.append("[").append(interInWarehouse).append("]不存在仓库信息！\n");

            bill.set("yd_outwarehouse", outWareInfo); // 调出仓库
            bill.set("yd_inwarehouse", inWareInfo); // 调入仓库

            // 分录明细
            DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("entryentity"); // 销售出库单中间表分录
            // 校验分录是否存在
            if (entryCol.size() == 0) errBuilder.append("调拨单中间表明细不能为空！\n");
            // 校验分录的物料是否存在
            for (DynamicObject entryInfo : entryCol) {
                String materialNumber = entryInfo.getString("yd_intermaterial"); // 接口推送物料\
                // 查找物料信息
                DynamicObject materialInfo = SettleUtils.getMaterialByNumber(materialNumber);
                if (materialInfo == null) {
                    errBuilder.append("[").append(materialNumber).append("]不存在物料信息！\n");
                }
                entryInfo.set("yd_material", materialInfo);
            }
        }

        // 分录明细
        DynamicObjectCollection entries = bill.getDynamicObjectCollection("entryentity"); // 销售出库单中间表分录
        // update by hst 2024/09/04 校验分录的物料库存信息是否存在
        for (DynamicObject entryInfo : entries) {
            DynamicObject material = entryInfo.getDynamicObject("yd_material");

            if (Objects.nonNull(material)) {
                // 查找物料库存信息
                DynamicObject matInvInfo = SettleUtils.getMatInventoryById(material.getString("id"));
                if (matInvInfo == null) {
                    errBuilder.append("[").append(material.getString("number")).append("]不存在物料库存信息！\n");
                }
            }
        }
        return errBuilder.toString();
    }

    /**
     * 校验内部交易关系是否有已维护
     *
     * @param context
     * @param config
     * @param bill
     * @return billId为空表示失败，errorMsg表示失败原因
     * @author: hst
     * @createDate: 2024/05/27
     */
    public String checkSettleMsg(HashMap context, DynamicObject config, DynamicObject bill) {
        StringBuilder errMsg = new StringBuilder();

        boolean isForward = SettleUtils.determineIsSaleOrReturn(bill);
        DynamicObject outOrg = isForward ? bill.getDynamicObject("yd_outorg") : bill.getDynamicObject("yd_inorg");
        DynamicObject inOrg = isForward ? bill.getDynamicObject("yd_inorg") : bill.getDynamicObject("yd_outorg");

        String key = outOrg.getString("id") + "&" + inOrg.getString("id") + "&" + bill.getString("yd_businesstype");
        Map<String,Object> settlePath = SettleUtils.getSettlePathByContext(context,key);

        // 校验结算路径上的组织与仓库映射表
        errMsg.append(SettleUtils.checkOrganizationWarehouse(context,settlePath));
        // 校验生产日期
        errMsg.append(SettleUtils.checkGenerationDate(bill));
        // 校验内部商客
        errMsg.append(SettleUtils.checkSupplyAndCustomer(context,settlePath,bill));
        // 自定义校验（通过配置信息中的类名及方法名自定义添加校验）
        Object custom = MethodUtils.invokeMethodByConfig(config.getString(SettleParamConstant.CUSCLASS_FIELD),
                config.getString(SettleParamConstant.CUSMETHOD_FIELD),bill);
        if (Objects.nonNull(custom)) {
            errMsg.append(custom.toString());
        }
        return errMsg.toString();
    }

    /**
     * 获取结算路径
     * @param context
     * @param bill   单据
     * @param config 配置信息
     * @return (path : 结算路径)
     * @author: hst
     * @createDate: 2024/05/27
     */
    public Map<String, Object> getSettlePath (Map<String,Object> context, DynamicObject config, DynamicObject bill) {
        Map<String, Object> map = new HashMap<>();

        String billType = bill.getString("yd_businesstype");

        boolean isForward = SettleUtils.determineIsSaleOrReturn(bill);
        DynamicObject outOrg = isForward ? bill.getDynamicObject("yd_outorg") : bill.getDynamicObject("yd_inorg");
        DynamicObject inOrg = isForward ? bill.getDynamicObject("yd_inorg") : bill.getDynamicObject("yd_outorg");

        String key = outOrg.getString("id") + "&" + inOrg.getString("id") + "&" + billType;

        DataSet settleDataSet = null;
        LinkedList<String> orgReturnlList = new LinkedList<>();
        // 通过配置信息获取退货路径
        settleDataSet = SettleUtils.getSettlePathByConfig(bill, config.getString(SettleParamConstant.CLASSNAME_FIELD),
                config.getString(SettleParamConstant.RETURNMETHOD_FIELD));
        // 退货结算
        String outOrgId = "";
        if (Objects.nonNull(settleDataSet)) {
            for (Row settleRow : settleDataSet) {
                String curOrgId = settleRow.getString("orgid");
                if (StringUtils.isNotBlank(outOrgId)) {
                    String mapValue = outOrgId + "&" + curOrgId;
                    orgReturnlList.add(mapValue);
                }
                outOrgId = curOrgId;
            }
        }
        map.put("orgReturnlList", orgReturnlList);

        // 标记退货类型结算
        if (Objects.nonNull(orgReturnlList) && orgReturnlList.size() > 0) {
            bill.set("yd_isreturn", true);
        } else {
            bill.set("yd_isreturn", false);
        }

        // 通过配置信息获取结算路径
        settleDataSet = SettleUtils.getSettlePathByConfig(bill, config.getString(SettleParamConstant.CLASSNAME_FIELD),
                config.getString(SettleParamConstant.METHOD_FIELD));
        LinkedList<String> orgSettleList = new LinkedList<String>();
        // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
        // 正常结算
        outOrgId = "";
        for (Row settleRow : settleDataSet) {
            String curOrgId = settleRow.getString("orgid");
            if (StringUtils.isNotEmpty(outOrgId)) {
                String mapValue = outOrgId + "&" + curOrgId;
                orgSettleList.add(mapValue);
            }
            outOrgId = curOrgId;
        }
        map.put("orgSettleList", orgSettleList);

        Map<String, Object> infos = new HashMap<String, Object>();
        infos.put(key, map);
        context.put("settlePath", infos);
        return map;

    }

    /**
     * 获取业务结算路径
     * @author: hst
     * @createDate: 2024/05/27
     * @param bill
     * @return
     */
    public DataSet getTransSettlePath (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        boolean isForward = SettleUtils.determineIsSaleOrReturn(bill);
        DynamicObject outOrg = isForward ? bill.getDynamicObject("yd_outorg") : bill.getDynamicObject("yd_inorg");
        DynamicObject inOrg = isForward ? bill.getDynamicObject("yd_inorg") : bill.getDynamicObject("yd_outorg");

        String biztype = bill.getString("yd_businesstype");
        // 调出组织
        qFilters.add(new QFilter("yd_outorg.id", QCP.equals, outOrg.getPkValue()));
        // 调入组织
        qFilters.add(new QFilter("yd_inorg.id", QCP.equals, inOrg.getPkValue()));
        // 审核状态
        qFilters.add(new QFilter("status", QCP.equals, "C"));
        // 业务类型
        qFilters.add(new QFilter("yd_businesstype", QCP.like, "%," + biztype + ",%"));
        // 错误提示
        String tip = "";
        tip = "当前调出组织：" + outOrg.getString("name") + "与调入组织：" + inOrg.getString("name")
                + "没有配置" + SettleUtils.getBussinessTypeName(bill.getString("yd_businesstype")) + "类型结算关系，无法生成内部结算流程单据！";
        DataSet settleDataSet = null;

        boolean isReturn = bill.getBoolean("yd_isreturn");
        if (!isForward && isReturn) {
            // 排序字段
            String orderby = "yd_returnentity.seq asc";
            settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_transsettlerela",
                    "yd_returnentity.yd_returnlevel orglevel,yd_returnentity.yd_returnorg.id orgid," +
                            "yd_returnentity.yd_returnorg.number orgNum,yd_returnentity.yd_returnorg.name orgName",
                    qFilters.toArray(new QFilter[qFilters.size()]), orderby);
        } else {
            // 排序字段
            String orderby = "yd_entryentity.seq asc";
            settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_transsettlerela",
                    "yd_entryentity.yd_orglevel orglevel,yd_entryentity.yd_org.id orgid," +
                            "yd_entryentity.yd_org.number orgNum,yd_entryentity.yd_org.name orgName",
                    qFilters.toArray(new QFilter[qFilters.size()]), orderby);
        }
        if (Objects.isNull(settleDataSet) || !settleDataSet.hasNext()) {
            // 品牌没有配置对应的内部结算关系，不生成内部结算单据
            throw new KDBizException(tip);
        }
        return settleDataSet;
    }

    /**
     * 获取业务退货结算路径
     * @author: hst
     * @createDate: 2024/05/27
     * @param bill
     * @return
     */
    public DataSet getTransReturnSettlePath (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        boolean isForward = SettleUtils.determineIsSaleOrReturn(bill);
        DynamicObject outOrg = isForward ? bill.getDynamicObject("yd_outorg") : bill.getDynamicObject("yd_inorg");
        DynamicObject inOrg = isForward ? bill.getDynamicObject("yd_inorg") : bill.getDynamicObject("yd_outorg");

        String biztype = bill.getString("yd_businesstype");
        // 调出组织
        qFilters.add(new QFilter("yd_outorg.id", QCP.equals, outOrg.getPkValue()));
        // 调入组织
        qFilters.add(new QFilter("yd_inorg.id", QCP.equals, inOrg.getPkValue()));
        // 审核状态
        qFilters.add(new QFilter("status", QCP.equals, "C"));
        // 业务类型
        qFilters.add(new QFilter("yd_businesstype", QCP.like, "%," + biztype + ",%"));
        // 退货结算
        qFilters.add(new QFilter("yd_isreturn", QCP.equals, true));
        DataSet settleDataSet = null;
        if (!isForward) {
            // 排序字段
            String orderby = "yd_entryentity.seq asc";
            settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_transsettlerela",
                    "yd_entryentity.yd_orglevel orglevel,yd_entryentity.yd_org.id orgid," +
                            "yd_entryentity.yd_org.number orgNum,yd_entryentity.yd_org.name orgName",
                    qFilters.toArray(new QFilter[qFilters.size()]), orderby);
        } else {
            // 排序字段
            String orderby = "yd_returnentity.seq asc";
            settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_transsettlerela",
                    "yd_returnentity.yd_returnlevel orglevel,yd_returnentity.yd_returnorg.id orgid," +
                            "yd_returnentity.yd_returnorg.number orgNum,yd_returnentity.yd_returnorg.name orgName",
                    qFilters.toArray(new QFilter[qFilters.size()]), orderby);
        }
        return settleDataSet;
    }

    /**
     * 标记单据结算成功
     * @param billId
     * @param tarBillNo
     * @author: hst
     * @createDate: 2024/06/01
     */
    private void settlementSuccessful_sign (String billId, String tarBillNo) {
        DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, BillTypeHelper.BILLTYPE_MIDTRANSBILL);
        bill.set("yd_issuccess",true);
        if (StringUtils.isNotBlank(tarBillNo)) {
            bill.set("yd_nextbillno",tarBillNo);
        }
        SaveServiceHelper.save(new DynamicObject[]{bill});
    }

    /**
     * 调拨中间表生成直接调拨单
     * @param bill
     * @author: hst
     * @createDate: 2024/06/01
     */
    public String createTransDirectbill (DynamicObject config, DynamicObject bill) {
        String ruleId = config.getString(SettleParamConstant.TODIRBILL_FIELD);
        DynamicObject directBill = SettleUtils.pushBill(bill.getString("id"), BillTypeHelper.BILLTYPE_MIDTRANSBILL,
                BillTypeHelper.BILLTYPE_TRANSDIRBILL, ruleId);

        // 获取结算币别
        DynamicObject org = directBill.getDynamicObject("outorg");
        DynamicObject currency = this.getSettleOrgCurrency(org);
        directBill.set("settlescurrency",currency);

        // 源单记录下游单据信息
        bill.set("yd_issettle",true);
        bill.set("yd_settleerror","");
        SaveServiceHelper.save(new DynamicObject[]{bill});

        Map<String, String> opResult = SettleUtils.operateTargetBill(BillTypeHelper.BILLTYPE_TRANSDIRBILL, directBill);

        // 保存关联关系
        String tarBillId = opResult.get("billId");
        SettleUtils.saveRelation(bill.getLong("id"), Long.valueOf(tarBillId),
                BillTypeHelper.BILLTYPE_MIDTRANSBILL,BillTypeHelper.BILLTYPE_TRANSDIRBILL);

        return directBill.getString("billno");
    }

    /**
     * 获取组织结算币别
     * @param org
     * @return
     * @author: hst
     * @createDate: 2024/06/01
     */
    private DynamicObject getSettleOrgCurrency (DynamicObject org) {
        QFilter qFilter = QFilter.of("baseacctorg.id = ?", org.getString("id"));
        DynamicObject baseInfo = BusinessDataServiceHelper.loadSingle("bd_accountingsys_base",new QFilter[]{qFilter});
        if (Objects.nonNull(baseInfo)) {
            return baseInfo.getDynamicObject("basecurrrency");
        }
        return null;
    }

    /**
     * 启动服务流程
     * @param billId
     * @param config 入参
     * @return
     * @author: hst
     * @createDate: 2024/06/04
     */
    private void executeIscFlowService (String billId, DynamicObject config) {
        DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, BillTypeHelper.BILLTYPE_MIDTRANSBILL);
        if (Objects.nonNull(bill)) {
            boolean isSuccess = bill.getBoolean("yd_issuccess");
            boolean isToEas = bill.getBoolean("yd_istoeas");

            if (isSuccess && !isToEas) {
                boolean type = config.getBoolean("yd_flowtype");
                String flowNumber = config.getString("yd_flow");

                if (StringUtils.isNotBlank(flowNumber)) {
                    // 构建服务流程参数
                    LinkedList<Object> inputs = new LinkedList<>();
                    inputs.add(billId);
                    inputs.add(bill.getString("yd_businesstype"));
                    inputs.add(bill.getString("yd_settletype"));
                    inputs.add(bill.getString("yd_isreturn"));

                    // 启动服务流程
                    String execType = type ? "execute" : "start";
                    DispatchServiceHelper.invokeBizService("isc", "iscb",
                            "IscFlowService", execType, flowNumber, inputs);
                }
            }
        }
    }
}
