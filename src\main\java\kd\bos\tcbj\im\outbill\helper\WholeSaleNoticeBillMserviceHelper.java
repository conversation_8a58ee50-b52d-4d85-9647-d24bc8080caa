package kd.bos.tcbj.im.outbill.helper;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import json.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.outbill.imp.WholeSaleNoticeBillMserviceImpl;
import kd.bos.tcbj.im.outbill.mservice.WholeSaleNoticeBillMservice;

/**
 * 批发通知单接口工具类
 * @auditor yanzuwei
 * @date 2022年5月12日
 * 
 */
public class WholeSaleNoticeBillMserviceHelper {
	/**
	 * 描述：查询E3批发通知单功能
	 * 
	 * @createDate  : 2022-05-12
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public static ApiResult getWholeSaleNoticeBill(JSONObject params) {
		WholeSaleNoticeBillMservice wholeSaleNoticeBillMservice = WholeSaleNoticeBillMserviceImpl.getInstance();
		return wholeSaleNoticeBillMservice.getWholeSaleNoticeBill(params);
	}
	
	/**
	 * 描述：刷新单据数据
	 * 
	 * @createDate  : 2022-05-12
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public static ApiResult refreshBill(Set<String> idSet) {
		WholeSaleNoticeBillMservice wholeSaleNoticeBillMservice = WholeSaleNoticeBillMserviceImpl.getInstance();
		return wholeSaleNoticeBillMservice.refreshBill(idSet);
	}
	
	/**
	 * 描述：下推销售出库单
	 * 
	 * @createDate  : 2022-05-12
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param idSet 单据ID集合
	 * @return 是否成功
	 */
	public static ApiResult pushToSaleoutBill(Set<String> idSet) {
		WholeSaleNoticeBillMservice wholeSaleNoticeBillMservice = WholeSaleNoticeBillMserviceImpl.getInstance();
		return wholeSaleNoticeBillMservice.pushToSaleoutBill(idSet);
	}

	/**
	 * 根据E3组装物料编码获取拆分后的中台物料集合
	 * @return 拆分后的中台物料集合
	 */
	public static DynamicObjectCollection getCombiMatCol(String oriMatNum) {
		QFilter matComFilter = new QFilter("entryentity.yd_mainmatnum", QCP.equals, oriMatNum);
		matComFilter.and(new QFilter("billstatus", QCP.equals, "C"));
		// update by hst 2023/11/22 增加物料类型过滤
		matComFilter.and(new QFilter("entryentity.yd_mattype",QFilter.equals,"1"));
		DynamicObjectCollection matComCol = QueryServiceHelper.query("yd_matcombination", 
				"id billId,entryentity.id enId,entryentity.yd_mainmatnum yd_mainmatnum,entryentity.yd_subentryentity.id subEnId,"
				+ "entryentity.yd_subentryentity.yd_material.number matNum,entryentity.yd_subentryentity.yd_qty yd_qty,entryentity.yd_subentryentity.yd_price yd_price", 
				matComFilter.toArray());
		return matComCol;
	}

	/**
	 * 描述：下推销售出库单（拆单）
	 *
	 * @createDate  : 2022-09-22
	 * @author: hst
	 * @updateDate  :
	 * @updateAuthor:
	 * @param idSet 单据ID集合
	 * @return 是否成功
	 */
	public static ApiResult pushToSaleoutBill_split(Set<String> idSet) {
		WholeSaleNoticeBillMservice wholeSaleNoticeBillMservice = WholeSaleNoticeBillMserviceImpl.getInstance();
		return wholeSaleNoticeBillMservice.pushToSaleoutBill_split(idSet);
	}
}
