package kd.bos.tcbj.im.outbill.helper;

import java.util.Set;

import json.JSONObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.tcbj.im.outbill.imp.WholeSaleReturnBillMserviceImpl;
import kd.bos.tcbj.im.outbill.mservice.WholeSaleReturnBillMservice;

/**
 * 描述：批发退货单工具类
 * WholeSaleReturnBillMserviceHelper.java
* @auditor yanzuwei
* @date 2022年1月10日
* 
*/
public class WholeSaleReturnBillMserviceHelper {
	
	/**
	 * 描述：查询E3批发退货单功能
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public static ApiResult getWholeSaleReturnBill(JSONObject params) {
		WholeSaleReturnBillMservice mservice = WholeSaleReturnBillMserviceImpl.getInstance();
		return mservice.getWholeSaleReturnBill(params);
	}
	
	/**
	 * 描述：刷新单据数据
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public static ApiResult refreshBill(Set<String> idSet) {
		WholeSaleReturnBillMservice mservice = WholeSaleReturnBillMserviceImpl.getInstance();
		return mservice.refreshBill(idSet);
	}
	
	/**
	 * 描述：下推红字销售出库单
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param idSet 单据ID集合
	 * @return 是否成功
	 */
	public static ApiResult pushToSaleoutBill(Set<String> idSet) {
		WholeSaleReturnBillMservice mservice = WholeSaleReturnBillMserviceImpl.getInstance();
		return mservice.pushToSaleoutBill(idSet);
	}
}
