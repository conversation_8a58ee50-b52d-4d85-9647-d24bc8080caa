package kd.bos.tcbj.im.outbill.imp;

import java.math.BigDecimal;
import java.util.*;

import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.tcbj.im.common.enums.OutSettleTypeEnum;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import org.apache.commons.lang3.StringUtils;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.outbill.mservice.SaleOutBillMservice;
import kd.bos.tcbj.im.transbill.service.E3Service;
import kd.bos.tcbj.im.util.CommonUtils;
import kd.bos.tcbj.im.vo.SaleOutEnLotVo;

/**
* @auditor yanzuwei
* @date 2021年12月30日
*
*/
public class SaleOutBillMserviceImpl implements SaleOutBillMservice {

	protected int amountPrecision = 2;
	protected int curAmountPrecision = 2;
	protected int pricePrecision = 10;
	protected int curPricePrecision = 10;
	protected BigDecimal ONEHUNDRED = new BigDecimal(100);

	private static Log logger = LogFactory.getLog(SaleOutBillMserviceImpl.class.getName());

	/**
	 * 内部实例化
	 */
	private final static SaleOutBillMserviceImpl saleOutBillMserviceImpl = new SaleOutBillMserviceImpl();

	/**
	 * (构造函数：私有化的销售出库单内部接口实现类)
	 *
	 * @createDate  : 2021-12-30
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor:
	 */
	public SaleOutBillMserviceImpl() {

	}

	/**
	 * 描述：获取采购通知单内部接口实现类实例
	 *
	 * @createDate  : 2021-12-08
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor:
	 * @return 采购任务内部接口实现类
	 */
	public static SaleOutBillMservice getInstance() {
		return saleOutBillMserviceImpl;
	}

	/**
	 * 描述：正向内部结算流程
	 *
	 * @createDate  : 2021-12-30
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor:
	 * @param saleoutBillId 源末级销售出库单ID
	 * @return 是否成功
	 */
	@Override
	public ApiResult createForwardBill(String saleoutBillId) {

		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 源末级销售出库单
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			// 源末级销售组织
			DynamicObject bizorg = saleoutBillObj.getDynamicObject("bizorg");
			// 最终的销售收货的客户
			DynamicObject finalCustomer = saleoutBillObj.getDynamicObject("customer");
			String oriCustomerId = finalCustomer.getPkValue().toString();  // 原汇总生成的客户ID
			// update by hst 2022/11/30 校验客户基础资料里面的税率是否为空
			if (Objects.isNull(finalCustomer.get("taxrate"))) {
				throw new KDBizException("客户[" + finalCustomer.getString("name") + "]默认税率为空");
			}
			// 获取第一行分录的品牌
			DynamicObject oneEntryObj = saleoutBillObj.getDynamicObjectCollection("billentry").get(0);
			DynamicObject brandObj = oneEntryObj.getDynamicObject("yd_basedata_pinpai");
			String brandNo = brandObj.getString("number");  // 品牌编码
			String brandName = brandObj.getString("name");  // 品牌名称
			// 获取品牌对应的内部结算关系以及最后一级的组织，如果是正向则从上到下，升序
			QFilter brandFilter = new QFilter("yd_brand.number", QCP.equals, brandNo);
			brandFilter.and(new QFilter("yd_oricustomer.id", QCP.equals, oriCustomerId));
			brandFilter.and(new QFilter("billstatus", QCP.equals, "C"));
			DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_innerrelbill",
					"yd_newcustomer.id newCusId,entryentity.yd_orglevel orglevel,entryentity.yd_org.id orgid,entryentity.yd_org.number orgNum, yd_isoutsettle isOutSettle", brandFilter.toArray(), "entryentity.seq asc");
			if (!settleDataSet.hasNext()) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("当前品牌"+ brandName + "没有配置内部结算关系，无法生成内部结算流程单据！");
			}
			LinkedList<String> orgRelList = new LinkedList<String>();
			String outOrgId = "";  // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
			String lastOrgNum = "";
			String lastCustomerId = "";
			boolean isOutSettle = false; // 是否委外品牌结算
			for (Row settleRow : settleDataSet) {
				String curOrgId = settleRow.getString("orgid");
				if (StringUtils.isNotEmpty(outOrgId)) {
					String mapValue = outOrgId+"&"+curOrgId;
					orgRelList.add(mapValue);
				}
				isOutSettle = settleRow.getBoolean("isOutSettle");
				outOrgId = curOrgId;
				lastOrgNum = settleRow.getString("orgNum");
				lastCustomerId = settleRow.getString("newCusId");
			}
			// 校验物料是否都设置了组织交易价格和客户交易价格和仓库映射关系，其中一个没有就报错
			Map<String,String> internalCheckResult = InternalSettleServiceHelper.checkInternalMsg(saleoutBillId, "1");
			if (StringUtils.isEmpty(internalCheckResult.get("billId"))) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("单据"+saleoutBillObj.getString("billno")+"无法进行内部结算："+internalCheckResult.get("errorMsg"));
//					continue;
			}

			// 按品牌获取最终的客户
			finalCustomer = BusinessDataServiceHelper.loadSingle(lastCustomerId, "bd_customer");

			// 根据组织与客户的交易价格关系重新对单据的单价进行赋值
			// 20220419yzw调整，根据单据业务日期获取对应日期内的价格
			Date bizDate = saleoutBillObj.getDate("biztime");
			Map<String, BigDecimal> orgtoCusNewPriceMap = InternalSettleServiceHelper.getOrgtoCusNewPrice(outOrgId, finalCustomer.getPkValue().toString(), bizDate);

			// 组织不一样，重置单据的组织
			DynamicObject lastOrg = BusinessDataServiceHelper.loadSingle(outOrgId, "bos_org");
			if (!lastOrgNum.equals(bizorg.getString("number"))) {
				saleoutBillObj.set("org", lastOrg);
				saleoutBillObj.set("bizorg", lastOrg);
				saleoutBillObj.set("bizdept", lastOrg);
				saleoutBillObj.set("dept", lastOrg);
			}

			// 重置最终的客户
			saleoutBillObj.set("customer", finalCustomer);
			saleoutBillObj.set("yd_agencycustomer", finalCustomer);
			saleoutBillObj.set("yd_cuschannel", finalCustomer.getDynamicObject("yd_channel"));  // 客户所属渠道

			// 根据客户获取税率
			DynamicObject cusTaxRateObj = finalCustomer.getDynamicObject("taxrate");

			// 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
			DynamicObjectCollection oriSaleEntryCol = saleoutBillObj.getDynamicObjectCollection("billentry");
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
				Boolean isGif = oriSaleEntry.getBoolean("ispresent");
				if (isGif) {
					oriSaleEntry.set("ispresent", false);
					cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
					oriSaleEntry.set("taxrateid", cusTaxRateObj);
					oriSaleEntry.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
				}
				// 根据销售关系yd_orgcuspricebill获取物料价格，再重算金额
				String materialId = oriSaleEntry.getString("material.masterid.id");
				DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
				if (orgtoCusNewPriceMap.containsKey(materialId) && orgtoCusNewPriceMap.get(materialId).compareTo(BigDecimal.ZERO) > 0) {
					// 20211214验证过不会自动计算，需要自动重算
					// 组织不一样，重置单据的组织
					BigDecimal newPrice = orgtoCusNewPriceMap.get(materialId);
					oriSaleEntry.set("priceandtax", newPrice);
					changePriceAndTax(saleoutBillObj, oriSaleEntry);
				}

				// 组织不一样，重置单据的组织
				if (!lastOrgNum.equals(bizorg.getString("number"))) {
					oriSaleEntry.set("entrysettleorg", lastOrg);  // 结算组织
					oriSaleEntry.set("outowner", lastOrg);  // 出库货主
					oriSaleEntry.set("outkeeper", lastOrg);  // 出库保管者
				}

				// 保存一下源单的E3仓库
				oriSaleEntry.set("yd_e3oriwarehouse", oriSaleEntry.get("warehouse"));

				// 库存组织+物料+批次获取生产日期和到期日期
				String lot = oriSaleEntry.getString("yd_ph");
//				// 需要对批号进行去空格和转大写操作 20220303
//				lot = lot.trim().toUpperCase().replaceAll(" ", "");
//				oriSaleEntry.set("yd_ph", lot);
				String matNum = tempMat.getString("number");
				String wsNum = oriSaleEntry.getDynamicObject("warehouse").getString("number");
				// 如果是委外结算 且 没有结算路径，更新仓库的值
				if (isOutSettle) {
					// 从数据库中查找
					Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.id", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
					DynamicObject actWarehouseInfo = BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, actWarehouseId);
					wsNum = actWarehouseInfo.getString("number"); // 更新值
					if (orgRelList.size() == 0) {
						oriSaleEntry.set("warehouse", actWarehouseInfo);
					}
				}
				oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, false);
//					if (oriSaleEntry.get("yd_dqr") == null) {
//						throw new KDBizException("单据"+saleoutBillObj.getString("billno")+"的批次"+ lot + "没有查到库存信息，无法生成内部结算流程单据！");
//					}
				// 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）,yzw20220802
				DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
				String isGeneralDate = "";
				if (isGeneralDateObj.length > 0) {
					isGeneralDate = isGeneralDateObj[0].getString("name");
				}
				if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
						&& oriSaleEntry.get("yd_dqr") == null) {
					// 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期），yzw20220607
					oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, false);
				}

				// 重置最终的客户
				oriSaleEntry.set("settlecustomer", finalCustomer);  // 应收客户
				oriSaleEntry.set("payingcustomer", finalCustomer);  // 付款客户
				oriSaleEntry.set("reccustomer", finalCustomer);  // 收货客户
				// update by hst 2024/01/31 携带源id
				oriSaleEntry.set("yd_sourceentryid",oriSaleEntry.getString("id"));
			}

			// 标记源单为已生成内部结算单据标识
			saleoutBillObj.set("yd_internalsettle", true);

			// 判断是否委外结算
			if (isOutSettle) {
				// 如果是委外结算，则标识为“经销商一盘货委外结算”
				saleoutBillObj.set("yd_outsettletype", OutSettleTypeEnum.DISTRIBUTOROUTSETTLE.getCode());
				saleoutBillObj.set("yd_frome3", false); // 设置是否按品牌汇总为否

				// 如果不存在结算关系，则标识为第一张
				if (orgRelList.size() == 0) {
					saleoutBillObj.set("yd_firstinternalbill", true);
				}
			}

			// 如果只有一级结算组织，就在股份，则不会有下游单据，标记这种进行单独集成流程传递
			if (orgRelList.size() == 0) {
				saleoutBillObj.set("yd_singleSettle", true);
			}

			// 再次发起内部结算时，清空结算失败原因
			saleoutBillObj.set("yd_settleerror", "");

			Map<String, String> opResult = operateTargetBill("im_saloutbill", saleoutBillObj);
			if (StringUtils.isEmpty(opResult.get("billId"))) {
				// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
				String error = opResult.get("errorMsg");
				errorSettleBill(saleoutBillId, "im_saloutbill", error);
				throw new KDBizException(error);
			}
			saleoutBillId = opResult.get("billId");

			// 编辑组织关系，创建每组关系的销售出库和采购入库
			// 第一组组织关系是从末级销售出库单开始创建销售出库和采购入库
			// 第二组及以后的根据上一组关系的后者组织的采购入库单开始创建本组的销售出库和采购入库
			// 最后一组组织关系需要再根据后者组织的采购入库单创建销售出库单
			// 如果只有一组关系时，只生成销售出库单和采购入库单
			// 如果没有关系时（股份直接卖给经销商），只需要处理源销售出库单
			String oriBillId = "";
			DynamicObject tempTargetBillObj = null;
			String desBillId = "";
			// 获取组织与默认仓库的关系
			Map<String, String> orgWarehouseMap = InternalSettleServiceHelper.getOrgWarehouseMap();

			for (String orgRelMap : orgRelList) {
				String[] keys = orgRelMap.split("&");
				String srcOrg = keys[0];
				DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
				String desOrg = keys[1];
				DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");

				// 获取组织内部各个物料新的结算价格集合
				// 20220419yzw调整，根据单据业务日期获取对应日期范围内的价格
				Map<String, BigDecimal> matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);

				if (orgRelMap.equals(orgRelList.getFirst())) {
					System.out.println("第一组组织关系");
					// 根据编码获取后者组织作为客户
					QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, desOrgObj.getString("number"));
					cusFilter.and(new QFilter("status", QCP.equals, "C"));
					DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel", cusFilter.toArray());
					DynamicObject customer = null;
					if (customers.length > 0) {
						customer = customers[0];
					}

					// 根据源销售出库单创建销售出库单
					oriBillId = saleoutBillId;
					String oriBillEntity = "im_saloutbill";
					String desBillEntity = "im_saloutbill";
					String ruleId = "1308718326465174528";
					tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);

					// 首笔销售出库单需要标记为首单，重置首单的信息，主组织应该为第一组组织中的前者
					// TODO
					// 需要赋值的字段：单据头.库存组织，单据头.销售组织，单据头.销售部门，单据头.库管部门，物料明细.结算组织，为组织关系中的前者
					// org、bizorg、bizdept、dept、billentry.entrysettleorg、
					tempTargetBillObj.set("org", srcOrgObj);
					tempTargetBillObj.set("bizorg", srcOrgObj);
					tempTargetBillObj.set("bizdept", srcOrgObj);
					tempTargetBillObj.set("dept", srcOrgObj);
					// 单据头.客户，为组织关系中的后者，先根据名称再根据编码查找bd_customer
					// customer
					tempTargetBillObj.set("customer", customer);
					tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道
					DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
					for (DynamicObject entryObj : outEntryCol) {
						entryObj.set("entrysettleorg", srcOrgObj);  // 结算组织
						entryObj.set("outowner", srcOrgObj);  // 出库货主
						entryObj.set("outkeeper", srcOrgObj);  // 出库保管者
						entryObj.set("settlecustomer", customer);  // 应收客户
						entryObj.set("payingcustomer", customer);  // 付款客户
						entryObj.set("reccustomer", customer);  // 收货客户
						// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
						String materialId = entryObj.getString("material.masterid.id");
						DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
						if (matPriceMap.containsKey(materialId)) {
							// TODO 待验证会不会触发自动计算，20211214验证过不会自动计算，需要自动重算
							BigDecimal newPrice = matPriceMap.get(materialId);
							entryObj.set("priceandtax", newPrice);
							// 如果单价是零还需要标记赠品,yzl,20230129
							if (newPrice.compareTo(BigDecimal.ZERO) == 0) {
								entryObj.set("ispresent", true);
							} else {
								entryObj.set("ispresent", false);
							}
							changePriceAndTax(tempTargetBillObj, entryObj);
						}
						// 如果是委外，需要重置仓库和批次信息
						if (isOutSettle) {
							String wsNum = entryObj.getDynamicObject("yd_e3oriwarehouse").getString("number");
							// 从数据库中查找
							Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.id", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
							DynamicObject actWarehouseInfo = BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, actWarehouseId);

							String lot = entryObj.getString("yd_ph"); // 批次
							String matNum = entryObj.getString("material.masterid.number"); // 物料编码
							wsNum = actWarehouseInfo.getString("number"); // 仓库编码
							entryObj.set("warehouse", actWarehouseInfo);

							// 更新批次
							setExpDate(lot, matNum, wsNum, entryObj, false);
						}
					}

					// 判断是否委外结算
					if (isOutSettle) {
						// 如果是委外结算，则标识为“经销商一盘货委外结算”
						tempTargetBillObj.set("yd_outsettletype", OutSettleTypeEnum.DISTRIBUTOROUTSETTLE.getCode());
						tempTargetBillObj.set("yd_frome3", false); // 设置是否按品牌汇总为否
					}

					// 标记为首单，用于集成平台开始同步的单据
					tempTargetBillObj.set("yd_firstinternalbill", true);
					// 标记股份的出库单为未同步批次，yzl,20230105
					tempTargetBillObj.set("yd_beresetlot", false);

					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
					if (StringUtils.isEmpty(opResult.get("billId"))) {
						// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
						String error = opResult.get("errorMsg");
						errorSettleBill(saleoutBillId, "im_saloutbill", error);
						throw new KDBizException(error);
					}
					desBillId = opResult.get("billId");

					// 无论是不是委外的产品，都需要重置仓库
					//     |- 委外单级次，不会进入到这个方法
					//     |- 委外多级次，最后一张需要重置仓库
					//     |- 经销商一盘货多级次，最后一张需要重置仓库
					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(oriBillId, "im_saloutbill", orgWarehouseMap);

					// 根据销售出库单创建采购入库单，到此就完结了
					oriBillId = desBillId;
					oriBillEntity = "im_saloutbill";
					desBillEntity = "im_purinbill";
					ruleId = "1308580694741490688";
//									desBillId = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					// 赋值采购入库单的主组织为第一组关系中的后者
					// TODO
					// 需要赋值的字段：单据头.库存组织、单据头.采购组织、单据头.采购部门、单据头.库管部门、物料明细.入库货主、物料明细.入库保管者、物料明细.结算组织、物料明细.需求组织
					// org、bizorg、bizdept、dept、billentry.owner、billentry.keeper、billentry.entrysettleorg、billentry.entryreqorg
					tempTargetBillObj.set("org", desOrgObj);
					tempTargetBillObj.set("bizorg", desOrgObj);
					tempTargetBillObj.set("bizdept", desOrgObj);
					tempTargetBillObj.set("dept", desOrgObj);

					// 获取供应商
					QFilter supFilter = new QFilter("internal_company.number", QCP.equals, srcOrgObj.getString("number"));
					supFilter.and(new QFilter("status", QCP.equals, "C"));
					DynamicObject[] suppliers = BusinessDataServiceHelper.load("bd_supplier", "id,name,number", supFilter.toArray());
					DynamicObject supplier = null;
					if (suppliers.length > 0) {
						supplier = suppliers[0];
					}

					// 单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商、
					// supplier、billentry.providersupplier、billentry.invoicesupplier、billentry.receivesupplier
					tempTargetBillObj.set("supplier", supplier);

					DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
					for(DynamicObject inEntryObj : inEntryCol) {
						inEntryObj.set("owner", desOrgObj);
						inEntryObj.set("keeper", desOrgObj);
						inEntryObj.set("entrysettleorg", desOrgObj);
						inEntryObj.set("entryreqorg", desOrgObj);

						inEntryObj.set("providersupplier", supplier);
						inEntryObj.set("invoicesupplier", supplier);
						inEntryObj.set("receivesupplier", supplier);

						// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
						String materialId = inEntryObj.getString("material.masterid.id");
						if (matPriceMap.containsKey(materialId)) {
							BigDecimal newPrice = matPriceMap.get(materialId);
							inEntryObj.set("priceandtax", newPrice);
							// 如果金额是零还需要标记赠品,yzl,20230129
							if (newPrice.compareTo(BigDecimal.ZERO) == 0) {
								inEntryObj.set("ispresent", true);
							} else {
								inEntryObj.set("ispresent", false);
							}
							changePriceAndTax(tempTargetBillObj, inEntryObj);
							// TODO 待验证会不会触发自动计算
						}
					}

					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
					if (StringUtils.isEmpty(opResult.get("billId"))) {
						// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
						String error = opResult.get("errorMsg");
						errorSettleBill(saleoutBillId, "im_saloutbill", error);
						throw new KDBizException(error);
					}
					desBillId = opResult.get("billId");

					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);

					oriBillId = desBillId;
				}

				// 不是第一组，创建销售出和采购入
				if (!orgRelMap.equals(orgRelList.getFirst())) {
					System.out.println("中间组别");
					// 获取后者组织作为客户
					QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, desOrgObj.getString("number"));
					cusFilter.and(new QFilter("status", QCP.equals, "C"));
					DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel", cusFilter.toArray());
					DynamicObject customer = null;
					if (customers.length > 0) {
						customer = customers[0];
					}

					// 根据上一级的采购入库单创建销售出库单
					String oriBillEntity = "im_purinbill";
					String desBillEntity = "im_saloutbill";
					String ruleId = "1308580938774484992";
//									String desBillId = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					// 赋值销售出库单的主组织为这组关系中的前者
					// TODO
					// 需要赋值的字段：单据头.库存组织，单据头.销售组织，单据头.销售部门，单据头.库管部门，物料明细.结算组织，为组织关系中的前者
					// org、bizorg、bizdept、dept、billentry.entrysettleorg、
					tempTargetBillObj.set("org", srcOrgObj);
					tempTargetBillObj.set("bizorg", srcOrgObj);
					tempTargetBillObj.set("bizdept", srcOrgObj);
					tempTargetBillObj.set("dept", srcOrgObj);
					// 单据头.客户，为组织关系中的后者，先根据名称再根据编码查找bd_customer
					// customer
					tempTargetBillObj.set("customer", customer);
					tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道
					DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
					for (DynamicObject entryObj : outEntryCol) {
						entryObj.set("entrysettleorg", srcOrgObj);  // 结算组织
						entryObj.set("outowner", srcOrgObj);  // 出库货主
						entryObj.set("outkeeper", srcOrgObj);  // 出库保管者
						entryObj.set("settlecustomer", customer);  // 应收客户
						entryObj.set("payingcustomer", customer);  // 付款客户
						entryObj.set("reccustomer", customer);  // 收货客户
						// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
						String materialId = entryObj.getString("material.masterid.id");
						if (matPriceMap.containsKey(materialId)) {
							BigDecimal newPrice = matPriceMap.get(materialId);
							entryObj.set("priceandtax", newPrice);
							// 如果金额是零还需要标记赠品,yzl,20230129
							if (newPrice.compareTo(BigDecimal.ZERO) == 0) {
								entryObj.set("ispresent", true);
							} else {
								entryObj.set("ispresent", false);
							}
							changePriceAndTax(tempTargetBillObj, entryObj);
							// TODO 待验证会不会触发自动计算
						}
					}

					// 判断是否委外结算
					if (isOutSettle) {
						// 如果是委外结算，则标识为“经销商一盘货委外结算”
						tempTargetBillObj.set("yd_outsettletype", OutSettleTypeEnum.DISTRIBUTOROUTSETTLE.getCode());
						tempTargetBillObj.set("yd_frome3", false); // 设置是否按品牌汇总为否
					}

					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
					if (StringUtils.isEmpty(opResult.get("billId"))) {
						// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
						String error = opResult.get("errorMsg");
						errorSettleBill(saleoutBillId, "im_saloutbill", error);
						throw new KDBizException(error);
					}
					desBillId = opResult.get("billId");

					// 根据销售出库单创建采购入库单，到此就完结了
					oriBillId = desBillId;
					oriBillEntity = "im_saloutbill";
					desBillEntity = "im_purinbill";
					ruleId = "1308580694741490688";
//						desBillId = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					// 赋值采购入库单的主组织为这组关系中的后者
					// TODO
					// 需要赋值的字段：单据头.库存组织、单据头.采购组织、单据头.采购部门、单据头.库管部门、物料明细.入库货主、物料明细.入库保管者、物料明细.结算组织、物料明细.需求组织
					// org、bizorg、bizdept、dept、billentry.owner、billentry.keeper、billentry.entrysettleorg、billentry.entryreqorg
					tempTargetBillObj.set("org", desOrgObj);
					tempTargetBillObj.set("bizorg", desOrgObj);
					tempTargetBillObj.set("bizdept", desOrgObj);
					tempTargetBillObj.set("dept", desOrgObj);

					// 获取供应商
					QFilter supFilter = new QFilter("internal_company.number", QCP.equals, srcOrgObj.getString("number"));
					supFilter.and(new QFilter("status", QCP.equals, "C"));
					DynamicObject[] suppliers = BusinessDataServiceHelper.load("bd_supplier", "id,name,number", supFilter.toArray());
					DynamicObject supplier = null;
					if (suppliers.length > 0) {
						supplier = suppliers[0];
					}

					// 单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商、
					// supplier、billentry.providersupplier、billentry.invoicesupplier、billentry.receivesupplier
					tempTargetBillObj.set("supplier", supplier);

					DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
					for(DynamicObject inEntryObj : inEntryCol) {
						inEntryObj.set("owner", desOrgObj);
						inEntryObj.set("keeper", desOrgObj);
						inEntryObj.set("entrysettleorg", desOrgObj);
						inEntryObj.set("entryreqorg", desOrgObj);
						inEntryObj.set("providersupplier", supplier);
						inEntryObj.set("invoicesupplier", supplier);
						inEntryObj.set("receivesupplier", supplier);

						// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
						String materialId = inEntryObj.getString("material.masterid.id");
						if (matPriceMap.containsKey(materialId)) {
							BigDecimal newPrice = matPriceMap.get(materialId);
							inEntryObj.set("priceandtax", newPrice);
							// 如果金额是零还需要标记赠品,yzl,20230129
							if (newPrice.compareTo(BigDecimal.ZERO) == 0) {
								inEntryObj.set("ispresent", true);
							} else {
								inEntryObj.set("ispresent", false);
							}
							changePriceAndTax(tempTargetBillObj, inEntryObj);
							// TODO 待验证会不会触发自动计算
						}
					}

					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
					if (StringUtils.isEmpty(opResult.get("billId"))) {
						// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
						String error = opResult.get("errorMsg");
						errorSettleBill(saleoutBillId, "im_saloutbill", error);
						throw new KDBizException(error);
					}
					desBillId = opResult.get("billId");

					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);

					oriBillId = desBillId;
				}

				// 最后一组只执行创建销售出库单
				if (orgRelMap.equals(orgRelList.getLast())) {
					System.out.println("最后一组组织关系");
				}

			}

			// 标记源单为已生成内部结算单据标识
			DynamicObject oriSaleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			oriSaleoutBill.set("yd_internalsettle", true);
			SaveServiceHelper.save(new DynamicObject[] {oriSaleoutBill});// 保存
			System.out.println("结束内部流程");

		} catch (Exception e) {
			// 记录错误信息
			logger.error(e);
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;

	}

	/**
	 * 描述：退货内部结算流程
	 *
	 * @createDate  : 2021-12-30
	 * @author: 严祖威
	 * @updateDate  :
	 * @updateAuthor:
	 * @param saleoutBillId 源末级销售出库单ID
	 * @return 是否成功
	 */
	@Override
	public ApiResult createBackwardBill(String saleoutBillId) {

		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 获取组织与默认仓库的关系
			Map<String, String> orgWarehouseMap = InternalSettleServiceHelper.getOrgWarehouseMap();
			// 源末级销售出库单
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			// 源末级销售组织
			DynamicObject bizorg = saleoutBillObj.getDynamicObject("bizorg");
			// 最终的销售收货的客户
			DynamicObject finalCustomer = saleoutBillObj.getDynamicObject("customer");
			String oriCustomerId = finalCustomer.getPkValue().toString();  // 原汇总生成的客户ID
			// update by hst 2022/11/30 校验客户基础资料里面的税率是否为空
			if (Objects.isNull(finalCustomer.get("taxrate"))) {
				throw new KDBizException("客户[" + finalCustomer.getString("name") + "]默认税率为空");
			}
			// 获取第一行分录的品牌
			DynamicObject oneEntryObj = saleoutBillObj.getDynamicObjectCollection("billentry").get(0);
			DynamicObject brandObj = oneEntryObj.getDynamicObject("yd_basedata_pinpai");
			String brandNo = brandObj.getString("number");  // 品牌编码
			String brandName = brandObj.getString("name");  // 品牌名称
			// 获取品牌对应的内部结算关系，如果是正向则从上到下，升序
			QFilter brandFilter = new QFilter("yd_brand.number", QCP.equals, brandNo);
			brandFilter.and(new QFilter("yd_oricustomer.id", QCP.equals, oriCustomerId));
			brandFilter.and(new QFilter("billstatus", QCP.equals, "C"));
			DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_innerrelbill",
					"yd_newcustomer.id newCusId,entryentity.yd_orglevel orglevel,entryentity.yd_org.id orgid,entryentity.yd_org.number orgNum, yd_isoutsettle isOutSettle", brandFilter.toArray(), "entryentity.seq desc");
			if (!settleDataSet.hasNext()) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("当前品牌"+ brandName + "没有配置内部结算关系，无法生成内部结算流程单据！");
//			continue;
			}

			DataSet copySet = settleDataSet.copy();
			Row firstRow = copySet.next();
			String lastOrgId = firstRow.getString("orgid");  // 第一级组织ID
			String lastOrgNum = firstRow.getString("orgNum");  // 第一级组织编码
			String finalCustomerId = firstRow.getString("newCusId");  // 最终的客户ID
			// 校验物料是否都设置了组织交易价格和客户交易价格和仓库映射关系，其中一个没有就报错
			Map<String,String> internalCheckResult = InternalSettleServiceHelper.checkInternalMsg(saleoutBillId, "1");
			if (StringUtils.isEmpty(internalCheckResult.get("billId"))) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("单据"+saleoutBillObj.getString("billno")+"因缺失映射表数据而无法进行内部结算："+internalCheckResult.get("errorMsg"));
//				continue;
			}

			// 按品牌最终的客户获取
			finalCustomer = BusinessDataServiceHelper.loadSingle(finalCustomerId, "bd_customer");

			// 根据组织与客户的交易价格关系重新对单据的单价进行赋值
			// 20220419yzw调整，根据单据业务日期获取对应日期内的价格
			Date bizDate = saleoutBillObj.getDate("biztime");
			Map<String, BigDecimal> orgtoCusNewPriceMap = InternalSettleServiceHelper.getOrgtoCusNewPrice(lastOrgId, finalCustomer.getPkValue().toString(), bizDate);

			// 获取两两组织的关系
			LinkedList<String> orgRelList = new LinkedList<String>();
			String outOrgId = "";  // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
			boolean isOutSettle = false; // 是否委外品牌结算
			for (Row settleRow : settleDataSet) {
				String curOrgId = settleRow.getString("orgid");
				if (StringUtils.isNotEmpty(outOrgId)) {
					String mapValue = outOrgId+"&"+curOrgId;
					orgRelList.add(mapValue);
				}
				isOutSettle = settleRow.getBoolean("isOutSettle");
				outOrgId = curOrgId;
			}
			// 组织不一样，重置单据的组织
			DynamicObject lastOrg = BusinessDataServiceHelper.loadSingle(lastOrgId, "bos_org");
			if (!lastOrgNum.equals(bizorg.getString("number"))) {
				saleoutBillObj.set("org", lastOrg);  // 库存组织
				saleoutBillObj.set("bizorg", lastOrg);  // 销售组织
				saleoutBillObj.set("bizdept", lastOrg);  // 销售部门
				saleoutBillObj.set("dept", lastOrg);  // 库存部门
			}

			saleoutBillObj.set("customer", finalCustomer);
			saleoutBillObj.set("yd_agencycustomer", finalCustomer);
			saleoutBillObj.set("yd_cuschannel", finalCustomer.getDynamicObject("yd_channel"));  // 客户所属渠道

			// 根据客户获取税率
			DynamicObject cusTaxRateObj = finalCustomer.getDynamicObject("taxrate");

			// 对源销售出库单进行单价重置
			DynamicObjectCollection oriSaleEntryCol = saleoutBillObj.getDynamicObjectCollection("billentry");
			// 源单E3仓库
//			DynamicObject oriE3Warehouse = null;
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
				Boolean isGif = oriSaleEntry.getBoolean("ispresent");
				if (isGif) {
					oriSaleEntry.set("ispresent", false);
					cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
					oriSaleEntry.set("taxrateid", cusTaxRateObj);
					oriSaleEntry.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
				}

				// 根据销售关系yd_orgcuspricebill获取物料价格，再重算金额
				String materialId = oriSaleEntry.getString("material.masterid.id");
				DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
				if (orgtoCusNewPriceMap.containsKey(materialId) && orgtoCusNewPriceMap.get(materialId).compareTo(BigDecimal.ZERO) > 0) {
					// TODO 待验证会不会触发自动计算，20211214验证过不会自动计算，需要自动重算
					BigDecimal newPrice = orgtoCusNewPriceMap.get(materialId);
					oriSaleEntry.set("priceandtax", newPrice);
					changePriceAndTax(saleoutBillObj, oriSaleEntry);
				}

				// 组织不一样，重置单据的组织
				if (!lastOrgNum.equals(bizorg.getString("number"))) {
					oriSaleEntry.set("outowner", lastOrg);  // 出库货主
					oriSaleEntry.set("outkeeper", lastOrg);  // 出库保管者
					oriSaleEntry.set("entrysettleorg", lastOrg);  // 结算组织
				}

				// 保存一下源单的E3仓库 -- 前置仓库
//				oriE3Warehouse = oriSaleEntry.getDynamicObject("warehouse");
				oriSaleEntry.set("yd_e3oriwarehouse", oriSaleEntry.getDynamicObject("warehouse"));

				// 获取生产日期和到期日
				String lot = oriSaleEntry.getString("yd_ph");
//				// 需要对批号进行去空格和转大写操作 20220303
//				lot = lot.trim().toUpperCase().replaceAll(" ", "");
//				oriSaleEntry.set("yd_ph", lot);
				String matNum = tempMat.getString("number");
				String wsNum = oriSaleEntry.getDynamicObject("warehouse").getString("number");
				// 如果是委外结算，更新仓库的值
				if (isOutSettle) {
					// 从数据库中查找
					Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.id", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
					DynamicObject actWarehouseInfo = BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, actWarehouseId);
					wsNum = actWarehouseInfo.getString("number"); // 更新值
					if (orgRelList.size() == 0) {
						oriSaleEntry.set("warehouse", actWarehouseInfo);
					}
				}
				oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, true);
				// 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）,yzw20220125
				DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
				String isGeneralDate = "";
				if (isGeneralDateObj.length > 0) {
					isGeneralDate = isGeneralDateObj[0].getString("name");
				}
				if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
						&& oriSaleEntry.get("yd_dqr") == null) {
//					throw new KDBizException("单据"+saleoutBillObj.getString("billno")+"的批次"+ lot + "没有查到库存信息，无法生成内部结算流程单据！");
//					Calendar cal = Calendar.getInstance();
//					cal.setTime(saleoutBillObj.getDate("biztime"));
//					cal.set(5, 1);
//					cal.set(2, 0);  // 月份是从0开始
//					oriSaleEntry.set("yd_scrq", cal.getTime());
//					cal.add(1, 2);
//					cal.add(5, -1);
//					oriSaleEntry.set("yd_dqr", cal.getTime());
					// 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期），yzw20220607
					oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, true);
				}

				oriSaleEntry.set("reccustomer", finalCustomer);
				oriSaleEntry.set("settlecustomer", finalCustomer);
				oriSaleEntry.set("payingcustomer", finalCustomer);
				// update by hst 2024/01/31 携带源id
				oriSaleEntry.set("yd_sourceentryid",oriSaleEntry.getString("id"));
			}
			// 标记为首单，用于集成平台开始同步的单据
			saleoutBillObj.set("yd_firstinternalbill", true);

			// 标记源单为已生成内部结算单据标识
			saleoutBillObj.set("yd_internalsettle", true);

			// 判断是否委外结算
			if (isOutSettle) {
				// 如果是委外结算，则标识为“经销商一盘货委外结算”
				saleoutBillObj.set("yd_outsettletype", OutSettleTypeEnum.DISTRIBUTOROUTSETTLE.getCode());
				saleoutBillObj.set("yd_frome3", false); // 设置是否按品牌汇总为否
			}

			// 如果只有一级结算组织，就在股份，则不会有下游单据，标记这种进行单独集成流程传递
			if (orgRelList.size() == 0) {
				saleoutBillObj.set("yd_singleSettle", true);
			}

			// 再次发起内部结算时，清空结算失败原因
			saleoutBillObj.set("yd_settleerror", "");

			// 对单据进行保存提交审核，拿到成功审核的单据ID
			Map<String, String> opResult = operateTargetBill("im_saloutbill", saleoutBillObj);
			if (StringUtils.isEmpty(opResult.get("billId"))) {
				// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
				String error = opResult.get("errorMsg");
				errorSettleBill(saleoutBillId, "im_saloutbill", error);
				throw new KDBizException(error);
			}
			saleoutBillId = opResult.get("billId");

			// 如果是第一组则重置多少倍退货数量
			DynamicObject backSaleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			DynamicObjectCollection backSaleEntryCol = backSaleoutBill.getDynamicObjectCollection("billentry");
			for(DynamicObject entry : backSaleEntryCol) {
				entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(new BigDecimal(orgRelList.size())));
				entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(new BigDecimal(orgRelList.size())));
			}
			SaveServiceHelper.save(new DynamicObject[] {backSaleoutBill});// 保存

			// 重置源单上分录的仓库为组织对应的仓库
			// 如果是委外的单，则最后一张是不需要替换仓库的，前面已经替换了
			if (!(isOutSettle && orgRelList.size() == 0)) {
				resetBillWarehouse(saleoutBillId, "im_saloutbill", orgWarehouseMap);
			}

			// 编辑组织关系，创建每组关系的销售出库和采购入库
			// 第一组组织关系是从末级销售出库单开始创建本级组织的采购入库和上级组织的销售出库
			// 第二组及以后（最后一组）的根据上一组关系的后者组织的销售出库单开始创建本组的采购入库和销售出库
			// 如果只有一组关系时，需要创建采购入库和销售出库
			String oriBillId = saleoutBillId;
			DynamicObject tempTargetBillObj = null;
			String desBillId = "";

			for (String orgRelMap : orgRelList) {
				String[] keys = orgRelMap.split("&");
				String srcOrg = keys[0];
				DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
				String desOrg = keys[1];
				DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");

				boolean isLast = orgRelMap.equals(orgRelList.getLast());

				// 获取组织内部各个物料新的结算价格集合
				// 20220419yzw调整，根据单据业务日期获取对应日期内的价格
				Map<String, BigDecimal> matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(desOrg, srcOrg, bizDate);

				// 根据源销售出库单创建本级采购入库单
//				oriBillId = oriBillId;
				String oriBillEntity = "im_saloutbill";
				String desBillEntity = "im_purinbill";
				String ruleId = "1311714048017963008";
				tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);

				// 首笔采购入库单需要标记为首单，重置首单的信息，主组织应该为第一组组织中的前者
				// TODO
				// 需要赋值的字段：单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商
				// supplier、billentry.providersupplier、billentry.invoicesupplier、billentry.receivesupplier
				// 获取组织关系中后者组织对应的内部供应商
				QFilter supFilter = new QFilter("internal_company.number", QCP.equals, desOrgObj.getString("number"));
				supFilter.and(new QFilter("status", QCP.equals, "C"));
				DynamicObject[] suppliers = BusinessDataServiceHelper.load("bd_supplier", "id,name,number", supFilter.toArray());
				DynamicObject supplier = null;
				if (suppliers.length > 0) {
					supplier = suppliers[0];
				}
				tempTargetBillObj.set("supplier", supplier);

				DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
				for(DynamicObject inEntryObj : inEntryCol) {
					inEntryObj.set("providersupplier", supplier);
					inEntryObj.set("invoicesupplier", supplier);
					inEntryObj.set("receivesupplier", supplier);

					// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
					String materialId = inEntryObj.getString("material.masterid.id");
					if (matPriceMap.containsKey(materialId)) {
						BigDecimal newPrice = matPriceMap.get(materialId);
						inEntryObj.set("priceandtax", newPrice);
						// 如果金额是零还需要标记赠品,yzl,20230129
						if (newPrice.compareTo(BigDecimal.ZERO) == 0) {
							inEntryObj.set("ispresent", true);
						} else {
							inEntryObj.set("ispresent", false);
						}
						changePriceAndTax(tempTargetBillObj, inEntryObj);
						// TODO 待验证会不会触发自动计算
					}
				}

				// 对单据进行保存提交审核，拿到成功审核的单据ID
				opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
				if (StringUtils.isEmpty(opResult.get("billId"))) {
					// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
					String error = opResult.get("errorMsg");
					errorSettleBill(saleoutBillId, "im_saloutbill", error);
					throw new KDBizException(error);
				}
				desBillId = opResult.get("billId");

				// 重置源单上分录的仓库为组织对应的仓库
				resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);

				// 如果是第一组则重置多少倍退货数量
				if (orgRelMap.equals(orgRelList.getFirst())) {
					DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_purinbill");
					DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
					for(DynamicObject entry : entryCol) {
						entry.set("returnqty", entry.getBigDecimal("qty").multiply(new BigDecimal(orgRelList.size())));
						entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(new BigDecimal(orgRelList.size())));
					}
					SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存
				}

				// 根据采购入库单创建上级销售出库单，到此就完结了
				oriBillId = desBillId;
				oriBillEntity = "im_purinbill";
				desBillEntity = "im_saloutbill";
				ruleId = "1311726856189247488";
//			desBillId = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
				tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
				// 赋值销售出库单的主组织为第一组关系中的后者
				// TODO
				// 需要赋值的字段：单据头.库存组织、单据头.销售组织、单据头.销售部门、单据头.库管部门、物料明细.出库货主、物料明细.出库保管者、物料明细.结算组织
				// org、bizorg、bizdept、dept、billentry.outowner、billentry.outkeeper、billentry.entrysettleorg
				// 需要赋值的字段：单据头.客户、物料明细.收货客户、物料明细.应收客户、物料明细.付款客户，根据前者组织获取内部客户
				// customer、reccustomer、settlecustomer、payingcustomer
				QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, srcOrgObj.getString("number"));
				cusFilter.and(new QFilter("status", QCP.equals, "C"));
				DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel", cusFilter.toArray());
				DynamicObject customer = null;
				if (customers.length > 0) {
					customer = customers[0];
				}

				tempTargetBillObj.set("org", desOrgObj);
				tempTargetBillObj.set("bizorg", desOrgObj);
				tempTargetBillObj.set("bizdept", desOrgObj);
				tempTargetBillObj.set("dept", desOrgObj);
				tempTargetBillObj.set("customer", customer);
				tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道

				DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
				for (DynamicObject entryObj : outEntryCol) {
					entryObj.set("outowner", desOrgObj);
					entryObj.set("outkeeper", desOrgObj);
					entryObj.set("entrysettleorg", desOrgObj);
					entryObj.set("reccustomer", customer);
					entryObj.set("settlecustomer", customer);
					entryObj.set("payingcustomer", customer);

					// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
					String materialId = entryObj.getString("material.masterid.id");
					DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
					if (matPriceMap.containsKey(materialId)) {
						BigDecimal newPrice = matPriceMap.get(materialId);
						// TODO 待验证会不会触发自动计算，20211214验证过不会自动计算，需要自动重算
						entryObj.set("priceandtax", newPrice);
						// 如果金额是零还需要标记赠品,yzl,20230129
						if (newPrice.compareTo(BigDecimal.ZERO) == 0) {
							entryObj.set("ispresent", true);
						} else {
							entryObj.set("ispresent", false);
						}
						changePriceAndTax(tempTargetBillObj, entryObj);
					}
					// 如果是委外，需要重置仓库和批次信息
					if (isOutSettle && isLast) {
						String wsNum = entryObj.getDynamicObject("yd_e3oriwarehouse").getString("number");
						// 从数据库中查找
						Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.id", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
						DynamicObject actWarehouseInfo = BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, actWarehouseId);

						String lot = entryObj.getString("yd_ph"); // 批次
						String matNum = entryObj.getString("material.masterid.number"); // 物料编码
						wsNum = actWarehouseInfo.getString("number"); // 仓库编码
						entryObj.set("warehouse", actWarehouseInfo);

						// 更新批次
						setExpDate(lot, matNum, wsNum, entryObj, true);
						// 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）,yzw20220125
						DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
						String isGeneralDate = "";
						if (isGeneralDateObj.length > 0) {
							isGeneralDate = isGeneralDateObj[0].getString("name");
						}
						if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
								&& entryObj.get("yd_dqr") == null) {
							// 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期），yzw20220607
							setLastExpDate(matNum, wsNum, entryObj, true);
						}
					}
				}

				// 判断是否委外结算
				if (isOutSettle) {
					// 如果是委外结算，则标识为“经销商一盘货委外结算”
					tempTargetBillObj.set("yd_outsettletype", OutSettleTypeEnum.DISTRIBUTOROUTSETTLE.getCode());
					tempTargetBillObj.set("yd_frome3", false); // 设置是否按品牌汇总为否
				}

				// 对单据进行保存提交审核，拿到成功审核的单据ID
				opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
				if (StringUtils.isEmpty(opResult.get("billId"))) {
					// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
					String error = opResult.get("errorMsg");
					errorSettleBill(saleoutBillId, "im_saloutbill", error);
					return null;
				}
				desBillId = opResult.get("billId");

				// 重置源单上分录的仓库为组织对应的仓库
				if (!(isOutSettle && isLast)) {
					resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
				}

				// 最后一张销售出库单的仓库为源单E3仓库
				DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_saloutbill");
				DynamicObjectCollection saleoutEntryCol = resetBill.getDynamicObjectCollection("billentry");
				for (DynamicObject resetSaleEntry : saleoutEntryCol) {
					if (!isOutSettle && isLast) {
						resetSaleEntry.set("warehouse", resetSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
					}
					resetSaleEntry.set("remainreturnqty", resetSaleEntry.getBigDecimal("qty").abs().multiply(new BigDecimal(orgRelList.size())));
					resetSaleEntry.set("remainreturnbaseqty", resetSaleEntry.getBigDecimal("baseqty").abs().multiply(new BigDecimal(orgRelList.size())));
				}
				SaveServiceHelper.save(new DynamicObject[] {resetBill});// 保存

				// 上一组组织关系中产生的销售出库单作为用于创建下一组组织关系的采购入库单的源单
				oriBillId = desBillId;
			}

			// 标记源单为已生成内部结算单据标识
			DynamicObject oriSaleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			oriSaleoutBill.set("yd_internalsettle", true);
			SaveServiceHelper.save(new DynamicObject[] {oriSaleoutBill});// 保存
			System.out.println("结束内部流程");
		} catch (Exception e) {
			// 记录错误信息
			logger.error(e);
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;

	}

	/**
	 * 正向直营店结算流程
	 * @param saleoutBillId 销售单ID
	 * @return 返回结果
	 */
	@Override
	public ApiResult createDirectForwardBill(String saleoutBillId) {

		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 源末级销售出库单
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			// 源末级销售组织
			DynamicObject bizorg = saleoutBillObj.getDynamicObject("bizorg");
			// 最终的销售收货的客户
			DynamicObject finalCustomer = saleoutBillObj.getDynamicObject("customer");
			String oriCustomerId = finalCustomer.getPkValue().toString();  // 原汇总生成的客户ID
			String oriCustomerName = finalCustomer.getString("name");  // 原汇总生成的客户名称
			// update by hst 2023/04/14 来源单据类型，批发通知单走直营店结算逻辑
			String sourceBillType = saleoutBillObj.getString("yd_sourcebilltype");
			// update by hst 2022/11/30 校验客户基础资料里面的税率是否为空
			if (Objects.isNull(finalCustomer.get("taxrate"))) {
				throw new KDBizException("客户[" + finalCustomer.getString("name") + "]默认税率为空");
			}
			DynamicObject oneEntryObj = saleoutBillObj.getDynamicObjectCollection("billentry").get(0);
			DynamicObject brandObj = oneEntryObj.getDynamicObject("yd_basedata_pinpai");
			String brandNo = brandObj.getString("number");  // 品牌编码
			// update by hst 2023/04/14 若是批发通知单，则查询成品仓一盘货结算关系表，否则走原逻辑
			DataSet settleDataSet = null;
			if ("2".equals(sourceBillType)) {
				QFilter orgFilter = QFilter.of("yd_canalorg.id = ? and yd_customentity.yd_custom.id = ? and status = 'C'",
						bizorg.getLong("id"),finalCustomer.getLong("id"));
				// 采用“直营店结算关系”获取结算路径
				settleDataSet = QueryServiceHelper.queryDataSet("jy", BillTypeHelper.BILLTYPE_WARESETTLERELA,
						"yd_customentity.yd_custom.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid,yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName, '0' isOutSettle"
						, orgFilter.toArray(), "yd_levelentry.seq asc");
				if (!settleDataSet.hasNext()) {
					// 没有配置对应的内部结算关系，不生成成品仓一盘货结算单据
					throw new KDBizException("当前渠道组织[" + bizorg.getString("name") + "]与客户[" + oriCustomerName
							+ "]没有配置成品仓一盘货结算关系，无法生成成品仓一盘货结算流程单据！");
				}
				// 存储原中间表的库存组织，用于重置结算时恢复,方便重置结算后再次找到正确的结算路径
				saleoutBillObj.set("yd_oriorg", saleoutBillObj.get("org"));
			} else {
				// 获取客户对应的内部结算关系以及最后一级的组织，如果是正向则从上到下，升序
				QFilter brandFilter = QFilter.of("yd_brand.number=? and yd_cusentry.yd_customer.id=? and status='C'", brandNo, oriCustomerId);
				// 采用“直营店结算关系”获取结算路径
				settleDataSet = QueryServiceHelper.queryDataSet("jy", BillTypeHelper.BILLTYPE_DIRECTSETTLERELA,
						"yd_cusentry.yd_customer.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid,yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName, yd_isoutsettle isOutSettle"
						, brandFilter.toArray(), "yd_levelentry.seq asc");
				if (!settleDataSet.hasNext()) {
					// 品牌没有配置对应的内部结算关系，不生成内部结算单据
					throw new KDBizException("当前客户["+ oriCustomerName + "]没有配置直营店客户结算关系，无法生成直营店内部结算流程单据！");
				}
			}
			LinkedList<String> orgRelList = new LinkedList<>();
			String outOrgId = "";  // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
			String lastOrgNum = "";
			String lastCustomerId = "";
			boolean isOutSettle = false; // 是否委外品牌结算
			for (Row settleRow : settleDataSet) {
				String curOrgId = settleRow.getString("orgid");
				if (StringUtils.isNotEmpty(outOrgId)) {
					String mapValue = outOrgId+"&"+curOrgId;
					orgRelList.add(mapValue);
				}
				isOutSettle = settleRow.getBoolean("isOutSettle");
				outOrgId = curOrgId;
				lastOrgNum = settleRow.getString("orgNum");
				lastCustomerId = settleRow.getString("newCusId");
			}

			// 校验物料是否都设置了组织交易价格和客户交易价格和仓库映射关系，其中一个没有就报错
			// checkType为2，标识校验直营店结算，1标识一盘货按品牌结算，5标识校验成品仓一盘货
			// update by hst 2023/04/14 若是批发通知单，则查询成品仓一盘货结算关系表，否则走原逻辑
			Map<String, String> internalCheckResult = null;
			if ("2".equals(sourceBillType)) {
				internalCheckResult = InternalSettleServiceHelper.checkInternalMsg(saleoutBillId, "5");
			} else {
				internalCheckResult = InternalSettleServiceHelper.checkInternalMsg(saleoutBillId, "2");
			}
			if (Objects.isNull(internalCheckResult) || StringUtils.isEmpty(internalCheckResult.get("billId"))) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("单据"+saleoutBillObj.getString("billno")+"无法进行内部结算："+internalCheckResult.get("errorMsg"));
//					continue;
			}

			// 按品牌获取最终的客户
			finalCustomer = BusinessDataServiceHelper.loadSingle(lastCustomerId, "bd_customer");

			// 根据组织与客户的交易价格关系重新对单据的单价进行赋值
			// 20220419yzw调整，根据单据业务日期获取对应日期内的价格
			Date bizDate = saleoutBillObj.getDate("biztime");

			// 组织不一样，重置单据的组织
			DynamicObject lastOrg = BusinessDataServiceHelper.loadSingle(outOrgId, "bos_org");
			if (!lastOrgNum.equals(bizorg.getString("number"))) {
				saleoutBillObj.set("org", lastOrg);
				saleoutBillObj.set("bizorg", lastOrg);
				saleoutBillObj.set("bizdept", lastOrg);
				saleoutBillObj.set("dept", lastOrg);
			}

			// 获取末级组织对应的内部客户
			DynamicObject agencyCustomerInfo = PCPServiceHelper.getCustomerByInternalCompany(lastOrgNum);

			// 重置最终的客户
			saleoutBillObj.set("customer", finalCustomer);
			saleoutBillObj.set("yd_agencycustomer", agencyCustomerInfo); // 经销商客户
			saleoutBillObj.set("yd_cuschannel", finalCustomer.getDynamicObject("yd_channel"));  // 客户所属渠道

			// 根据客户获取税率
			DynamicObject cusTaxRateObj = finalCustomer.getDynamicObject("taxrate");

			// 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
			DynamicObjectCollection oriSaleEntryCol = saleoutBillObj.getDynamicObjectCollection("billentry");
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {

				// 组织不一样，重置单据的组织
				if (!lastOrgNum.equals(bizorg.getString("number"))) {
					oriSaleEntry.set("entrysettleorg", lastOrg);  // 结算组织
					oriSaleEntry.set("outowner", lastOrg);  // 出库货主
					oriSaleEntry.set("outkeeper", lastOrg);  // 出库保管者
				}

				// 保存一下源单的E3仓库
				oriSaleEntry.set("yd_e3oriwarehouse", oriSaleEntry.get("warehouse"));

				String materialId = oriSaleEntry.getString("material.masterid.id");
				DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
				// 库存组织+物料+批次获取生产日期和到期日期
				String lot = oriSaleEntry.getString("yd_ph");
//				// 需要对批号进行去空格和转大写操作 20220303
//				lot = lot.trim().toUpperCase().replaceAll(" ", "");
//				oriSaleEntry.set("yd_ph", lot);
				String matNum = tempMat.getString("number");
				String wsNum = oriSaleEntry.getDynamicObject("warehouse").getString("number");
				// 如果是委外结算 且 没有结算路径，更新仓库的值
				if (isOutSettle) {
					// 从数据库中查找
					Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.id", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
					DynamicObject actWarehouseInfo = BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, actWarehouseId);
					wsNum = actWarehouseInfo.getString("number"); // 更新值
					if (orgRelList.size() == 0) {
						oriSaleEntry.set("warehouse", actWarehouseInfo);
					}
				}
				setExpDate(lot, matNum, wsNum, oriSaleEntry, false);
//					if (oriSaleEntry.get("yd_dqr") == null) {
//						throw new KDBizException("单据"+saleoutBillObj.getString("billno")+"的批次"+ lot + "没有查到库存信息，无法生成内部结算流程单据！");
//					}
				// 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）,yzw20220802
				DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
				String isGeneralDate = "";
				if (isGeneralDateObj.length > 0) {
					isGeneralDate = isGeneralDateObj[0].getString("name");
				}
				if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
						&& oriSaleEntry.get("yd_dqr") == null) {
					// 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期），yzw20220607
					oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, false);
				}

				// 重置最终的客户
				oriSaleEntry.set("settlecustomer", finalCustomer);  // 应收客户
				oriSaleEntry.set("payingcustomer", finalCustomer);  // 付款客户
				oriSaleEntry.set("reccustomer", finalCustomer);  // 收货客户
				// update by hst 2024/01/31 携带源id
				oriSaleEntry.set("yd_sourceentryid",oriSaleEntry.getString("id"));
			}

			// 标记源单为已生成内部结算单据标识
			saleoutBillObj.set("yd_internalsettle", true);

			// 判断是否委外结算
			// update by hst 2023/04/18 批发通知单无需标记直营店结算
			if (!"2".equals(sourceBillType)) {
				if (isOutSettle) {
					// 如果是委外结算，则标识为“直营店一盘货委外结算”
					saleoutBillObj.set("yd_outsettletype", OutSettleTypeEnum.DIRECTOUTSETTLE.getCode());
					saleoutBillObj.set("yd_isdirectsettle", false); // 设置是否按直营店结算为否
					
					// 如果不存在结算关系，则标识为第一张
					if (orgRelList.size() == 0) {
						saleoutBillObj.set("yd_firstinternalbill", true);
					}
				}
			}

			// 如果只有一级结算组织，就在股份，则不会有下游单据，标记这种进行单独集成流程传递
			if (orgRelList.size() == 0) {
				saleoutBillObj.set("yd_singleSettle", true);
			}

			// 再次发起内部结算时，清空结算失败原因
			saleoutBillObj.set("yd_settleerror", "");

			Map<String, String> opResult = operateTargetBill("im_saloutbill", saleoutBillObj);
			if (StringUtils.isEmpty(opResult.get("billId"))) {
				// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
				String error = opResult.get("errorMsg");
				errorSettleBill(saleoutBillId, "im_saloutbill", error);
				throw new KDBizException(error);
			}
			saleoutBillId = opResult.get("billId");

			// 编辑组织关系，创建每组关系的销售出库和采购入库
			// 第一组组织关系是从末级销售出库单开始创建销售出库和采购入库
			// 第二组及以后的根据上一组关系的后者组织的采购入库单开始创建本组的销售出库和采购入库
			// 最后一组组织关系需要再根据后者组织的采购入库单创建销售出库单
			// 如果只有一组关系时，只生成销售出库单和采购入库单
			// 如果没有关系时（股份直接卖给经销商），只需要处理源销售出库单
			// 3销售出库 -> 1销售出库 -> 2采购入库 -> 2销售出库 -> 3采购入库
			String oriBillId = "";
			DynamicObject tempTargetBillObj = null;
			String desBillId = "";
			// 获取组织与默认仓库的关系
			Map<String, String> orgWarehouseMap = InternalSettleServiceHelper.getOrgWarehouseMap();

			for (String orgRelMap : orgRelList) {
				String[] keys = orgRelMap.split("&");
				String srcOrg = keys[0];
				DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
				String desOrg = keys[1];
				DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");

				// 获取组织内部各个物料新的结算价格集合
				// 20220419yzw调整，根据单据业务日期获取对应日期范围内的价格
				Map<String, BigDecimal> matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);

				if (orgRelMap.equals(orgRelList.getFirst())) {
					System.out.println("第一组组织关系");
					// 根据编码获取后者组织作为客户
					QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, desOrgObj.getString("number"));
					cusFilter.and(new QFilter("status", QCP.equals, "C"));
					DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel", cusFilter.toArray());
					DynamicObject customer = null;
					if (customers.length > 0) {
						customer = customers[0];
					}

					// 根据源销售出库单创建销售出库单
					oriBillId = saleoutBillId;
					String oriBillEntity = "im_saloutbill";
					String desBillEntity = "im_saloutbill";
					String ruleId = "1308718326465174528";
					tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);

					// 首笔销售出库单需要标记为首单，重置首单的信息，主组织应该为第一组组织中的前者
					// TODO
					// 需要赋值的字段：单据头.库存组织，单据头.销售组织，单据头.销售部门，单据头.库管部门，物料明细.结算组织，为组织关系中的前者
					// org、bizorg、bizdept、dept、billentry.entrysettleorg、
					tempTargetBillObj.set("org", srcOrgObj);
					tempTargetBillObj.set("bizorg", srcOrgObj);
					tempTargetBillObj.set("bizdept", srcOrgObj);
					tempTargetBillObj.set("dept", srcOrgObj);
					// 单据头.客户，为组织关系中的后者，先根据名称再根据编码查找bd_customer
					// customer
					tempTargetBillObj.set("customer", customer);
					tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道
					DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
					for (DynamicObject entryObj : outEntryCol) {
						entryObj.set("entrysettleorg", srcOrgObj);  // 结算组织
						entryObj.set("outowner", srcOrgObj);  // 出库货主
						entryObj.set("outkeeper", srcOrgObj);  // 出库保管者
						entryObj.set("settlecustomer", customer);  // 应收客户
						entryObj.set("payingcustomer", customer);  // 付款客户
						entryObj.set("reccustomer", customer);  // 收货客户

						// 直营店内部结算的赠品字段需要重置为false
						Boolean isGif = entryObj.getBoolean("ispresent");
						if (isGif) {
							entryObj.set("ispresent", false);
							cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
							entryObj.set("taxrateid", cusTaxRateObj);
							entryObj.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
						}

						String materialId = entryObj.getString("material.masterid.id");
//						DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
						// update by hst 2023/06/01 单价为0标记为赠品
						if (matPriceMap.containsKey(materialId)) {
							// TODO 待验证会不会触发自动计算，20211214验证过不会自动计算，需要自动重算
							BigDecimal newPrice = matPriceMap.get(materialId);
							entryObj.set("priceandtax", newPrice);
							changePriceAndTax(tempTargetBillObj, entryObj);
							// update by hst 2023/06/01 单价为0标记为赠品
							if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
								entryObj.set("ispresent", true);
							} else {
								entryObj.set("ispresent", false);
							}
						}
						// 如果是委外，需要重置仓库和批次信息
						if (isOutSettle) {
							String wsNum = entryObj.getDynamicObject("yd_e3oriwarehouse").getString("number");
							// 从数据库中查找
							Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.id", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
							DynamicObject actWarehouseInfo = BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, actWarehouseId);

							String lot = entryObj.getString("yd_ph"); // 批次
							String matNum = entryObj.getString("material.masterid.number"); // 物料编码
							wsNum = actWarehouseInfo.getString("number"); // 仓库编码
							entryObj.set("warehouse", actWarehouseInfo);

							// 更新批次
							setExpDate(lot, matNum, wsNum, entryObj, false);
						}
					}

					// 标记为首单，用于集成平台开始同步的单据
					tempTargetBillObj.set("yd_firstinternalbill", true);
					// 标记股份的出库单为未同步批次，yzl,20230105
					tempTargetBillObj.set("yd_beresetlot", false);

					// 设置是否按品牌汇总为“否”、是否直营店结算设置为“是”
					tempTargetBillObj.set("yd_frome3", false); // 是否按品牌汇总
					// 判断是否委外结算
					// update by hst 2023/04/18 批发通知单无需标记直营店结算
					if (!"2".equals(sourceBillType)) {
						if (isOutSettle) {
							// 如果是委外结算，则标识为“直营店一盘货委外结算”
							tempTargetBillObj.set("yd_outsettletype", OutSettleTypeEnum.DIRECTOUTSETTLE.getCode());
							tempTargetBillObj.set("yd_isdirectsettle", false); // 设置是否按直营店结算为否
						}else {
							tempTargetBillObj.set("yd_isdirectsettle", true); // 是否直营结算
						}
					}

					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
					if (StringUtils.isEmpty(opResult.get("billId"))) {
						// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
						String error = opResult.get("errorMsg");
						errorSettleBill(saleoutBillId, "im_saloutbill", error);
						throw new KDBizException(error);
					}
					desBillId = opResult.get("billId");

					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(oriBillId, "im_saloutbill", orgWarehouseMap);

					// 根据销售出库单创建采购入库单，到此就完结了
					oriBillId = desBillId;
					oriBillEntity = "im_saloutbill";
					desBillEntity = "im_purinbill";
					ruleId = "1308580694741490688";
//									desBillId = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					// 赋值采购入库单的主组织为第一组关系中的后者
					// TODO
					// 需要赋值的字段：单据头.库存组织、单据头.采购组织、单据头.采购部门、单据头.库管部门、物料明细.入库货主、物料明细.入库保管者、物料明细.结算组织、物料明细.需求组织
					// org、bizorg、bizdept、dept、billentry.owner、billentry.keeper、billentry.entrysettleorg、billentry.entryreqorg
					tempTargetBillObj.set("org", desOrgObj);
					tempTargetBillObj.set("bizorg", desOrgObj);
					tempTargetBillObj.set("bizdept", desOrgObj);
					tempTargetBillObj.set("dept", desOrgObj);

					// 获取供应商
					QFilter supFilter = new QFilter("internal_company.number", QCP.equals, srcOrgObj.getString("number"));
					supFilter.and(new QFilter("status", QCP.equals, "C"));
					DynamicObject[] suppliers = BusinessDataServiceHelper.load("bd_supplier", "id,name,number", supFilter.toArray());
					DynamicObject supplier = null;
					if (suppliers.length > 0) {
						supplier = suppliers[0];
					}

					// 单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商、
					// supplier、billentry.providersupplier、billentry.invoicesupplier、billentry.receivesupplier
					tempTargetBillObj.set("supplier", supplier);

					DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
					for(DynamicObject inEntryObj : inEntryCol) {
						inEntryObj.set("owner", desOrgObj);
						inEntryObj.set("keeper", desOrgObj);
						inEntryObj.set("entrysettleorg", desOrgObj);
						inEntryObj.set("entryreqorg", desOrgObj);

						inEntryObj.set("providersupplier", supplier);
						inEntryObj.set("invoicesupplier", supplier);
						inEntryObj.set("receivesupplier", supplier);

						// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
						String materialId = inEntryObj.getString("material.masterid.id");
						if (matPriceMap.containsKey(materialId)) {
							BigDecimal newPrice = matPriceMap.get(materialId);
							inEntryObj.set("priceandtax", newPrice);
							changePriceAndTax(tempTargetBillObj, inEntryObj);
							// TODO 待验证会不会触发自动计算
							// update by hst 20230601 单价为0标记为赠品
							if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
								inEntryObj.set("ispresent", true);
							} else {
								inEntryObj.set("ispresent", false);
							}
						}
					}

					// 判断是否委外结算
					// update by hst 2023/04/18 批发通知单无需标记直营店结算
					if (!"2".equals(sourceBillType)) {
						if (isOutSettle) {
							tempTargetBillObj.set("yd_isdirectsettle", false); // 设置是否按直营店结算为否
						}else {
							tempTargetBillObj.set("yd_isdirectsettle", true); // 是否直营结算
						}
					}

					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
					if (StringUtils.isEmpty(opResult.get("billId"))) {
						// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
						String error = opResult.get("errorMsg");
						errorSettleBill(saleoutBillId, "im_saloutbill", error);
						throw new KDBizException(error);
					}
					desBillId = opResult.get("billId");

					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);

					oriBillId = desBillId;
				}

				// 不是第一组，创建销售出和采购入
				if (!orgRelMap.equals(orgRelList.getFirst())) {
					System.out.println("中间组别");
					// 获取后者组织作为客户
					QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, desOrgObj.getString("number"));
					cusFilter.and(new QFilter("status", QCP.equals, "C"));
					DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel", cusFilter.toArray());
					DynamicObject customer = null;
					if (customers.length > 0) {
						customer = customers[0];
					}

					// 根据上一级的采购入库单创建销售出库单
					String oriBillEntity = "im_purinbill";
					String desBillEntity = "im_saloutbill";
					String ruleId = "1308580938774484992";
//									String desBillId = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					// 赋值销售出库单的主组织为这组关系中的前者
					// TODO
					// 需要赋值的字段：单据头.库存组织，单据头.销售组织，单据头.销售部门，单据头.库管部门，物料明细.结算组织，为组织关系中的前者
					// org、bizorg、bizdept、dept、billentry.entrysettleorg、
					tempTargetBillObj.set("org", srcOrgObj);
					tempTargetBillObj.set("bizorg", srcOrgObj);
					tempTargetBillObj.set("bizdept", srcOrgObj);
					tempTargetBillObj.set("dept", srcOrgObj);
					// 单据头.客户，为组织关系中的后者，先根据名称再根据编码查找bd_customer
					// customer
					tempTargetBillObj.set("customer", customer);
					tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道
					DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
					for (DynamicObject entryObj : outEntryCol) {
						entryObj.set("entrysettleorg", srcOrgObj);  // 结算组织
						entryObj.set("outowner", srcOrgObj);  // 出库货主
						entryObj.set("outkeeper", srcOrgObj);  // 出库保管者
						entryObj.set("settlecustomer", customer);  // 应收客户
						entryObj.set("payingcustomer", customer);  // 付款客户
						entryObj.set("reccustomer", customer);  // 收货客户
						// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
						String materialId = entryObj.getString("material.masterid.id");
						if (matPriceMap.containsKey(materialId)) {
							BigDecimal newPrice = matPriceMap.get(materialId);
							entryObj.set("priceandtax", newPrice);
							changePriceAndTax(tempTargetBillObj, entryObj);
							// TODO 待验证会不会触发自动计算
							// update by hst 20230601 单价为0标记为赠品
							if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
								entryObj.set("ispresent", true);
							} else {
								entryObj.set("ispresent", false);
							}
						}
					}

					// 设置是否按品牌汇总为“否”、是否直营店结算设置为“是”
					tempTargetBillObj.set("yd_frome3", false); // 是否按品牌汇总
					// 判断是否委外结算
					// update by hst 2023/04/18 批发通知单无需标记直营店结算
					if (!"2".equals(sourceBillType)) {
						if (isOutSettle) {
							// 如果是委外结算，则标识为“直营店一盘货委外结算”
							tempTargetBillObj.set("yd_outsettletype", OutSettleTypeEnum.DIRECTOUTSETTLE.getCode());
							tempTargetBillObj.set("yd_isdirectsettle", false); // 设置是否按直营店结算为否
						}else {
							tempTargetBillObj.set("yd_isdirectsettle", true); // 是否直营结算
						}
					}

					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
					if (StringUtils.isEmpty(opResult.get("billId"))) {
						// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
						String error = opResult.get("errorMsg");
						errorSettleBill(saleoutBillId, "im_saloutbill", error);
						throw new KDBizException(error);
					}
					desBillId = opResult.get("billId");

					// 根据销售出库单创建采购入库单，到此就完结了
					oriBillId = desBillId;
					oriBillEntity = "im_saloutbill";
					desBillEntity = "im_purinbill";
					ruleId = "1308580694741490688";
//						desBillId = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
					// 赋值采购入库单的主组织为这组关系中的后者
					// 需要赋值的字段：单据头.库存组织、单据头.采购组织、单据头.采购部门、单据头.库管部门、物料明细.入库货主、物料明细.入库保管者、物料明细.结算组织、物料明细.需求组织
					// org、bizorg、bizdept、dept、billentry.owner、billentry.keeper、billentry.entrysettleorg、billentry.entryreqorg
					tempTargetBillObj.set("org", desOrgObj);
					tempTargetBillObj.set("bizorg", desOrgObj);
					tempTargetBillObj.set("bizdept", desOrgObj);
					tempTargetBillObj.set("dept", desOrgObj);

					// 获取供应商
					QFilter supFilter = new QFilter("internal_company.number", QCP.equals, srcOrgObj.getString("number"));
					supFilter.and(new QFilter("status", QCP.equals, "C"));
					DynamicObject[] suppliers = BusinessDataServiceHelper.load("bd_supplier", "id,name,number", supFilter.toArray());
					DynamicObject supplier = null;
					if (suppliers.length > 0) {
						supplier = suppliers[0];
					}

					// 单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商、
					// supplier、billentry.providersupplier、billentry.invoicesupplier、billentry.receivesupplier
					tempTargetBillObj.set("supplier", supplier);

					DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
					for(DynamicObject inEntryObj : inEntryCol) {
						inEntryObj.set("owner", desOrgObj);
						inEntryObj.set("keeper", desOrgObj);
						inEntryObj.set("entrysettleorg", desOrgObj);
						inEntryObj.set("entryreqorg", desOrgObj);
						inEntryObj.set("providersupplier", supplier);
						inEntryObj.set("invoicesupplier", supplier);
						inEntryObj.set("receivesupplier", supplier);

						// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
						String materialId = inEntryObj.getString("material.masterid.id");
						if (matPriceMap.containsKey(materialId)) {
							BigDecimal newPrice = matPriceMap.get(materialId);
							inEntryObj.set("priceandtax", newPrice);
							changePriceAndTax(tempTargetBillObj, inEntryObj);
							// TODO 待验证会不会触发自动计算
							// update by hst 20230601 单价为0标记为赠品
							if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
								inEntryObj.set("ispresent", true);
							} else {
								inEntryObj.set("ispresent", false);
							}
						}
					}

					// 判断是否委外结算
					// update by hst 2023/04/18 批发通知单无需标记直营店结算
					if (!"2".equals(sourceBillType)) {
						if (isOutSettle) {
							tempTargetBillObj.set("yd_isdirectsettle", false); // 设置是否按直营店结算为否
						}else {
							tempTargetBillObj.set("yd_isdirectsettle", true); // 是否直营结算
						}
					}

					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
					if (StringUtils.isEmpty(opResult.get("billId"))) {
						// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
						String error = opResult.get("errorMsg");
						errorSettleBill(saleoutBillId, "im_saloutbill", error);
						throw new KDBizException(error);
					}
					desBillId = opResult.get("billId");

					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);

					oriBillId = desBillId;
				}

				// 最后一组只执行创建销售出库单
				if (orgRelMap.equals(orgRelList.getLast())) {
					System.out.println("最后一组组织关系");
				}
			}

			// 标记源单为已生成内部结算单据标识
			DynamicObject oriSaleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			oriSaleoutBill.set("yd_internalsettle", true);
			SaveServiceHelper.save(new DynamicObject[] {oriSaleoutBill});// 保存
			System.out.println("结束内部流程");
		} catch (Exception e) {
			// 记录错误信息
			logger.error(e);
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 逆向直营店结算流程
	 * @param saleoutBillId 销售单ID
	 * @return 返回结果
	 */
	@Override
	public ApiResult createDirectBackwardBill(String saleoutBillId) {

		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 获取组织与默认仓库的关系
			Map<String, String> orgWarehouseMap = InternalSettleServiceHelper.getOrgWarehouseMap();
			// 源末级销售出库单
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			// 源末级销售组织
			DynamicObject bizorg = saleoutBillObj.getDynamicObject("bizorg");
			// 最终的销售收货的客户
			DynamicObject finalCustomer = saleoutBillObj.getDynamicObject("customer");
			String oriCustomerId = finalCustomer.getPkValue().toString();  // 原汇总生成的客户ID
			String oriCustomerName = finalCustomer.getString("name");  // 原汇总生成的客户名称
			// update by hst 2022/11/30 校验客户基础资料里面的税率是否为空
			if (Objects.isNull(finalCustomer.get("taxrate"))) {
				throw new KDBizException("客户[" + finalCustomer.getString("name") + "]默认税率为空");
			}
			DynamicObject oneEntryObj = saleoutBillObj.getDynamicObjectCollection("billentry").get(0);
			DynamicObject brandObj = oneEntryObj.getDynamicObject("yd_basedata_pinpai");
			String brandNo = brandObj.getString("number");  // 品牌编码
			// 获取客户对应的内部结算关系，如果是正向则从上到下，升序
			QFilter brandFilter = QFilter.of("yd_brand.number=? and yd_cusentry.yd_customer.id=? and status='C'", brandNo, oriCustomerId);
			// 采用“直营店结算关系”获取结算路径
			DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", BillTypeHelper.BILLTYPE_DIRECTSETTLERELA,
					"yd_cusentry.yd_customer.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid,yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName, yd_isoutsettle isOutSettle"
					, brandFilter.toArray(), "yd_levelentry.seq desc");
			if (!settleDataSet.hasNext()) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("当前客户["+ oriCustomerName + "]没有配置内部结算关系，无法生成内部结算流程单据！");
			}

			DataSet copySet = settleDataSet.copy();
			Row firstRow = copySet.next();
			String lastOrgId = firstRow.getString("orgid");  // 第一级组织ID
			String lastOrgNum = firstRow.getString("orgNum");  // 第一级组织编码
			String finalCustomerId = firstRow.getString("newCusId");  // 最终的客户ID

			// 校验物料是否都设置了组织交易价格和客户交易价格和仓库映射关系，其中一个没有就报错
			Map<String,String> internalCheckResult = InternalSettleServiceHelper.checkInternalMsg(saleoutBillId, "2");
			if (StringUtils.isEmpty(internalCheckResult.get("billId"))) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("单据"+saleoutBillObj.getString("billno")+"因缺失映射表数据而无法进行内部结算："+internalCheckResult.get("errorMsg"));
//				continue;
			}

			// 按品牌最终的客户获取
			finalCustomer = BusinessDataServiceHelper.loadSingle(finalCustomerId, "bd_customer");

			// 根据组织与客户的交易价格关系重新对单据的单价进行赋值
			// 20220419yzw调整，根据单据业务日期获取对应日期内的价格
			Date bizDate = saleoutBillObj.getDate("biztime");

			// 获取两两组织的关系
			LinkedList<String> orgRelList = new LinkedList<String>();
			String outOrgId = "";  // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
			boolean isOutSettle = false; // 是否委外品牌结算
			for (Row settleRow : settleDataSet) {
				String curOrgId = settleRow.getString("orgid");
				if (StringUtils.isNotEmpty(outOrgId)) {
					String mapValue = outOrgId+"&"+curOrgId;
					orgRelList.add(mapValue);
				}
				isOutSettle = settleRow.getBoolean("isOutSettle");
				outOrgId = curOrgId;
			}
			// 组织不一样，重置单据的组织
			DynamicObject lastOrg = BusinessDataServiceHelper.loadSingle(lastOrgId, "bos_org");
			if (!lastOrgNum.equals(bizorg.getString("number"))) {
				saleoutBillObj.set("org", lastOrg);  // 库存组织
				saleoutBillObj.set("bizorg", lastOrg);  // 销售组织
				saleoutBillObj.set("bizdept", lastOrg);  // 销售部门
				saleoutBillObj.set("dept", lastOrg);  // 库存部门
			}

			// 获取末级组织对应的内部客户
			DynamicObject agencyCustomerInfo = PCPServiceHelper.getCustomerByInternalCompany(lastOrgNum);

			saleoutBillObj.set("customer", finalCustomer);
			saleoutBillObj.set("yd_agencycustomer", agencyCustomerInfo); // 经销商客户
			saleoutBillObj.set("yd_cuschannel", finalCustomer.getDynamicObject("yd_channel"));  // 客户所属渠道

			// 根据客户获取税率
			DynamicObject cusTaxRateObj = finalCustomer.getDynamicObject("taxrate");

			// 对源销售出库单进行单价重置
			DynamicObjectCollection oriSaleEntryCol = saleoutBillObj.getDynamicObjectCollection("billentry");
			// 数量基数
			BigDecimal baseNum = new BigDecimal(orgRelList.size());
			// 源单E3仓库
//			DynamicObject oriE3Warehouse = null;
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {

				// 组织不一样，重置单据的组织
				if (!lastOrgNum.equals(bizorg.getString("number"))) {
					oriSaleEntry.set("outowner", lastOrg);  // 出库货主
					oriSaleEntry.set("outkeeper", lastOrg);  // 出库保管者
					oriSaleEntry.set("entrysettleorg", lastOrg);  // 结算组织
				}

				String materialId = oriSaleEntry.getString("material.masterid.id");
				DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");

				// 保存一下源单的E3仓库 -- 需前置
//				oriE3Warehouse = oriSaleEntry.getDynamicObject("warehouse");
				oriSaleEntry.set("yd_e3oriwarehouse", oriSaleEntry.getDynamicObject("warehouse"));

				// 获取生产日期和到期日
				String lot = oriSaleEntry.getString("yd_ph");
//				// 需要对批号进行去空格和转大写操作 20220303
//				lot = lot.trim().toUpperCase().replaceAll(" ", "");
//				oriSaleEntry.set("yd_ph", lot);
				String matNum = tempMat.getString("number");
				String wsNum = oriSaleEntry.getDynamicObject("warehouse").getString("number");
				// 如果是委外结算且只有一级结算的时候，更新仓库的值
				if (isOutSettle) {
					// 从数据库中查找
					Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.id", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
					DynamicObject actWarehouseInfo = BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, actWarehouseId);
					wsNum = actWarehouseInfo.getString("number"); // 更新值
					if (orgRelList.size() == 0) {
						oriSaleEntry.set("warehouse", actWarehouseInfo);
					}
				}
				setExpDate(lot, matNum, wsNum, oriSaleEntry, true);
				// 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）,yzw20220125
				DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
				String isGeneralDate = "";
				if (isGeneralDateObj.length > 0) {
					isGeneralDate = isGeneralDateObj[0].getString("name");
				}
				if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
						&& oriSaleEntry.get("yd_dqr") == null) {
//					throw new KDBizException("单据"+saleoutBillObj.getString("billno")+"的批次"+ lot + "没有查到库存信息，无法生成内部结算流程单据！");
//					Calendar cal = Calendar.getInstance();
//					cal.setTime(saleoutBillObj.getDate("biztime"));
//					cal.set(5, 1);
//					cal.set(2, 0);  // 月份是从0开始
//					oriSaleEntry.set("yd_scrq", cal.getTime());
//					cal.add(1, 2);
//					cal.add(5, -1);
//					oriSaleEntry.set("yd_dqr", cal.getTime());
					// 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期），yzw20220607
					oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, true);
				}

				oriSaleEntry.set("reccustomer", finalCustomer);
				oriSaleEntry.set("settlecustomer", finalCustomer);
				oriSaleEntry.set("payingcustomer", finalCustomer);
				// update by hst 2024/01/31 携带源id
				oriSaleEntry.set("yd_sourceentryid",oriSaleEntry.getString("id"));
			}
			// 标记为首单，用于集成平台开始同步的单据
			saleoutBillObj.set("yd_firstinternalbill", true);

			// 标记源单为已生成内部结算单据标识
			saleoutBillObj.set("yd_internalsettle", true);

			// 判断是否委外结算
			if (isOutSettle) {
				// 如果是委外结算，则标识为“直营店一盘货委外结算”
				saleoutBillObj.set("yd_outsettletype", OutSettleTypeEnum.DIRECTOUTSETTLE.getCode());
				saleoutBillObj.set("yd_isdirectsettle", false); // 设置是否按直营店结算为否
			}

			// 如果只有一级结算组织，就在股份，则不会有下游单据，标记这种进行单独集成流程传递
			if (orgRelList.size() == 0) {
				saleoutBillObj.set("yd_singleSettle", true);
			}

			// 再次发起内部结算时，清空结算失败原因
			saleoutBillObj.set("yd_settleerror", "");

			// 对单据进行保存提交审核，拿到成功审核的单据ID
			Map<String, String> opResult = operateTargetBill("im_saloutbill", saleoutBillObj);
			if (StringUtils.isEmpty(opResult.get("billId"))) {
				// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
				String error = opResult.get("errorMsg");
				errorSettleBill(saleoutBillId, "im_saloutbill", error);
				return ApiResult.fail(error);
			}
			saleoutBillId = opResult.get("billId");

			// 如果是第一组则重置多少倍退货数量
			DynamicObject backSaleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			DynamicObjectCollection backSaleEntryCol = backSaleoutBill.getDynamicObjectCollection("billentry");
			for(DynamicObject entry : backSaleEntryCol) {
				entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(baseNum));
				entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(baseNum));
			}
			SaveServiceHelper.save(new DynamicObject[] {backSaleoutBill});// 保存

			// 如果是直营店委外结算，前面已经重置了仓库信息，这里不需要重置仓库信息
			// 重置源单上分录的仓库为组织对应的仓库
			if (!(isOutSettle && orgRelList.size() == 0)) {
				resetBillWarehouse(saleoutBillId, "im_saloutbill", orgWarehouseMap);
			}

			// 编辑组织关系，创建每组关系的销售出库和采购入库
			// 第一组组织关系是从末级销售出库单开始创建本级组织的采购入库和上级组织的销售出库
			// 第二组及以后（最后一组）的根据上一组关系的后者组织的销售出库单开始创建本组的采购入库和销售出库
			// 如果只有一组关系时，需要创建采购入库和销售出库
			// 3销售出库 -> 3采购入库 -> 2销售出库 -> 2采购入库 -> 1销售出库
			String oriBillId = saleoutBillId;
			DynamicObject tempTargetBillObj = null;
			String desBillId = "";

			for (String orgRelMap : orgRelList) {
				String[] keys = orgRelMap.split("&");
				String srcOrg = keys[0];
				DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
				String desOrg = keys[1];
				DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");

				boolean isLast = orgRelMap.equals(orgRelList.getLast());

				// 获取组织内部各个物料新的结算价格集合
				// 20220419yzw调整，根据单据业务日期获取对应日期内的价格
				Map<String, BigDecimal> matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(desOrg, srcOrg, bizDate);

				// 根据源销售出库单创建本级采购入库单
//				oriBillId = oriBillId;
				String oriBillEntity = "im_saloutbill";
				String desBillEntity = "im_purinbill";
				String ruleId = "1311714048017963008";
				tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);

				// 首笔采购入库单需要标记为首单，重置首单的信息，主组织应该为第一组组织中的前者
				// TODO
				// 需要赋值的字段：单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商
				// supplier、billentry.providersupplier、billentry.invoicesupplier、billentry.receivesupplier
				// 获取组织关系中后者组织对应的内部供应商
				QFilter supFilter = new QFilter("internal_company.number", QCP.equals, desOrgObj.getString("number"));
				supFilter.and(new QFilter("status", QCP.equals, "C"));
				DynamicObject[] suppliers = BusinessDataServiceHelper.load("bd_supplier", "id,name,number", supFilter.toArray());
				DynamicObject supplier = null;
				if (suppliers.length > 0) {
					supplier = suppliers[0];
				}
				tempTargetBillObj.set("supplier", supplier);

				DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
				for(DynamicObject inEntryObj : inEntryCol) {
					inEntryObj.set("providersupplier", supplier);
					inEntryObj.set("invoicesupplier", supplier);
					inEntryObj.set("receivesupplier", supplier);

					// 直营店内部结算的赠品字段需要重置为false
					Boolean isGif = inEntryObj.getBoolean("ispresent");
					if (isGif) {
						inEntryObj.set("ispresent", false);
						cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
						inEntryObj.set("taxrateid", cusTaxRateObj);
						inEntryObj.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
					}

					// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
					String materialId = inEntryObj.getString("material.masterid.id");
					if (matPriceMap.containsKey(materialId)) {
						BigDecimal newPrice = matPriceMap.get(materialId);
						inEntryObj.set("priceandtax", newPrice);
						changePriceAndTax(tempTargetBillObj, inEntryObj);
						// update by hst 20230601 单价为0标记为赠品
						if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
							inEntryObj.set("ispresent", true);
						} else {
							inEntryObj.set("ispresent", false);
						}
						// TODO 待验证会不会触发自动计算
					}
				}

				// 判断是否委外结算
				if (isOutSettle) {
					tempTargetBillObj.set("yd_isdirectsettle", false); // 设置是否按直营店结算为否
				}else {
					tempTargetBillObj.set("yd_isdirectsettle", true); // 是否直营结算
				}

				// 对单据进行保存提交审核，拿到成功审核的单据ID
				opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
				if (StringUtils.isEmpty(opResult.get("billId"))) {
					// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
					String error = opResult.get("errorMsg");
					errorSettleBill(saleoutBillId, "im_saloutbill", error);
					return ApiResult.fail(error);
				}
				desBillId = opResult.get("billId");

				// 重置源单上分录的仓库为组织对应的仓库
				resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);

				// 如果是第一组则重置多少倍退货数量
				DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_purinbill");
				DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
				for(DynamicObject entry : entryCol) {
					entry.set("returnqty", entry.getBigDecimal("qty").multiply(baseNum));
					entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(baseNum));
				}
				SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存

				// 根据采购入库单创建上级销售出库单，到此就完结了
				oriBillId = desBillId;
				oriBillEntity = "im_purinbill";
				desBillEntity = "im_saloutbill";
				ruleId = "1311726856189247488";
//			desBillId = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
				tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
				// 赋值销售出库单的主组织为第一组关系中的后者
				// TODO
				// 需要赋值的字段：单据头.库存组织、单据头.销售组织、单据头.销售部门、单据头.库管部门、物料明细.出库货主、物料明细.出库保管者、物料明细.结算组织
				// org、bizorg、bizdept、dept、billentry.outowner、billentry.outkeeper、billentry.entrysettleorg
				// 需要赋值的字段：单据头.客户、物料明细.收货客户、物料明细.应收客户、物料明细.付款客户，根据前者组织获取内部客户
				// customer、reccustomer、settlecustomer、payingcustomer
				QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, srcOrgObj.getString("number"));
				cusFilter.and(new QFilter("status", QCP.equals, "C"));
				DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel", cusFilter.toArray());
				DynamicObject customer = null;
				if (customers.length > 0) {
					customer = customers[0];
				}

				tempTargetBillObj.set("org", desOrgObj);
				tempTargetBillObj.set("bizorg", desOrgObj);
				tempTargetBillObj.set("bizdept", desOrgObj);
				tempTargetBillObj.set("dept", desOrgObj);
				tempTargetBillObj.set("customer", customer);
				tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道

				DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
				for (DynamicObject entryObj : outEntryCol) {
					entryObj.set("outowner", desOrgObj);
					entryObj.set("outkeeper", desOrgObj);
					entryObj.set("entrysettleorg", desOrgObj);
					entryObj.set("reccustomer", customer);
					entryObj.set("settlecustomer", customer);
					entryObj.set("payingcustomer", customer);

					// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
					String materialId = entryObj.getString("material.masterid.id");
					DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
					if (matPriceMap.containsKey(materialId)) {
						BigDecimal newPrice = matPriceMap.get(materialId);
						// TODO 待验证会不会触发自动计算，20211214验证过不会自动计算，需要自动重算
						entryObj.set("priceandtax", newPrice);
						changePriceAndTax(tempTargetBillObj, entryObj);
						// update by hst 20230601 单价为0标记为赠品
						if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
							entryObj.set("ispresent", true);
						} else {
							entryObj.set("ispresent", false);
						}
					}
					// 如果是委外，需要重置仓库和批次信息
					if (isOutSettle && isLast) {
						String wsNum = entryObj.getDynamicObject("yd_e3oriwarehouse").getString("number");
						// 从数据库中查找
						Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP, "yd_entryentity.yd_acturalwarehouse.id", QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=? and yd_entryentity.yd_brand.number=? and yd_entryentity.yd_customer.id=?", wsNum, brandNo, oriCustomerId).toArray());
						DynamicObject actWarehouseInfo = BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, actWarehouseId);

						String lot = entryObj.getString("yd_ph"); // 批次
						String matNum = entryObj.getString("material.masterid.number"); // 物料编码
						wsNum = actWarehouseInfo.getString("number"); // 仓库编码
						entryObj.set("warehouse", actWarehouseInfo);

						// 更新批次
						setExpDate(lot, matNum, wsNum, entryObj, true);
						// 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）,yzw20220125
						DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
						String isGeneralDate = "";
						if (isGeneralDateObj.length > 0) {
							isGeneralDate = isGeneralDateObj[0].getString("name");
						}
						if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
								&& entryObj.get("yd_dqr") == null) {
							// 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期），yzw20220607
							setLastExpDate(matNum, wsNum, entryObj, true);
						}
					}
				}

				// 设置是否按品牌汇总为“否”、是否直营店结算设置为“是”
				tempTargetBillObj.set("yd_frome3", false); // 是否按品牌汇总
				// 判断是否委外结算
				if (isOutSettle) {
					// 如果是委外结算，则标识为“直营店一盘货委外结算”
					tempTargetBillObj.set("yd_outsettletype", OutSettleTypeEnum.DIRECTOUTSETTLE.getCode());
					tempTargetBillObj.set("yd_isdirectsettle", false); // 设置是否按直营店结算为否
				}else {
					tempTargetBillObj.set("yd_isdirectsettle", true); // 是否直营结算
				}

				// 对单据进行保存提交审核，拿到成功审核的单据ID
				opResult = operateTargetBill(desBillEntity, tempTargetBillObj);
				if (StringUtils.isEmpty(opResult.get("billId"))) {
					// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
					String error = opResult.get("errorMsg");
					errorSettleBill(saleoutBillId, "im_saloutbill", error);
					return ApiResult.fail(error);
				}
				desBillId = opResult.get("billId");

				// 重置源单上分录的仓库为组织对应的仓库
				if (!(isOutSettle && isLast)) {
					resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
				}

				// 最后一张销售出库单的仓库为源单E3仓库
				DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_saloutbill");
				DynamicObjectCollection saleoutEntryCol = resetBill.getDynamicObjectCollection("billentry");
				for (DynamicObject resetSaleEntry : saleoutEntryCol) {
					if (!isOutSettle && isLast) {
						resetSaleEntry.set("warehouse", resetSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
					}
					resetSaleEntry.set("remainreturnqty", resetSaleEntry.getBigDecimal("qty").abs().multiply(baseNum));
					resetSaleEntry.set("remainreturnbaseqty", resetSaleEntry.getBigDecimal("baseqty").abs().multiply(baseNum));
				}
				SaveServiceHelper.save(new DynamicObject[] {resetBill});// 保存

				// 上一组组织关系中产生的销售出库单作为用于创建下一组组织关系的采购入库单的源单
				oriBillId = desBillId;
			}

			// 标记源单为已生成内部结算单据标识
			DynamicObject oriSaleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			oriSaleoutBill.set("yd_internalsettle", true);
			SaveServiceHelper.save(new DynamicObject[] {oriSaleoutBill});// 保存
			System.out.println("结束内部流程");
		} catch (Exception e) {
			// 记录错误信息
			logger.error(e);
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;

	}

	/**
	 * 设置批次对应的生产日期和到期日
	 * @param lot
	 * @param matNum
	 * @param wsNum
	 * @param entry
	 * @param isReturn 是否退货业务，退货业务取最新批次，销售业务取最旧并且有库存的批次
	 * @return
	 */
	private DynamicObject setExpDate(String lot, String matNum, String wsNum, DynamicObject entry, Boolean isReturn) {
		QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
		filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
		filter.and(new QFilter("yd_flot", QCP.equals, lot));
		// 如果是正向的需要查数量大于0、并且有库存的批次
		String sort = "yd_mfgdate desc";
		if (!isReturn) {
			filter.and(new QFilter("yd_qty", QCP.large_than, 0));
			sort = "yd_mfgdate asc";
		}
		DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
				"yd_mfgdate,yd_expdate", filter.toArray(), sort);
		if (dataSet.hasNext()) {
			Row tempRow = dataSet.next();
			entry.set("yd_scrq", tempRow.getDate("yd_mfgdate"));  // 生产日期
			entry.set("yd_dqr", tempRow.getDate("yd_expdate"));  // 到期日
		}
		return entry;
	}

	/**
	 * 设置物料+仓库获取最新的批次及日期
	 * @param matNum 物料编码
	 * @param wsNum 仓库编码
	 * @param entry 被处理的分录
	 * @param isReturn 是否退货业务，退货业务取最新批次，销售业务取最旧并且有库存的批次
	 * @return
	 */
	private DynamicObject setLastExpDate(String matNum, String wsNum, DynamicObject entry, Boolean isReturn) {
		QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
		filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
		// 如果是正向的需要查数量大于0、并且有库存的批次
		String sort = "yd_mfgdate desc";
		if (!isReturn) {
			filter.and(new QFilter("yd_qty", QCP.large_than, 0));
			sort = "yd_mfgdate asc";
		}
		DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
				"yd_flot,yd_mfgdate,yd_expdate", filter.toArray(), sort);
		if (dataSet.hasNext()) {
			Row tempRow = dataSet.next();
			entry.set("yd_ph", tempRow.getString("yd_flot"));  // 生产日期
			entry.set("yd_scrq", tempRow.getDate("yd_mfgdate"));  // 生产日期
			entry.set("yd_dqr", tempRow.getDate("yd_expdate"));  // 到期日
		}
		return entry;
	}

	/**
	 * 将错误信息记录到源销售出库单上yd_settleerror
	 * @param saleoutBillId
	 * @param billEntity
	 * @param errorMsg
	 */
	private void errorSettleBill(String saleoutBillId, String billEntity, String errorMsg) {
		DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, billEntity);
		resetBill.set("yd_settleerror", errorMsg);
		SaveServiceHelper.save(new DynamicObject[] {resetBill});// 保存
	}

	/**
	 * 重置单据上的仓库为组织指定的仓库
	 * @param billId
	 * @param billEntity
	 * @param orgWarehouseMap
	 */
	private void resetBillWarehouse(String billId, String billEntity, Map<String, String> orgWarehouseMap) {
		DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(billId, billEntity);
		if (orgWarehouseMap.containsKey(resetBill.getString("org.id"))) {
			String newWs = orgWarehouseMap.get(resetBill.getString("org.id"));
			DynamicObjectCollection resetSaleEntryCol = resetBill.getDynamicObjectCollection("billentry");
			for (DynamicObject resetSaleEntry : resetSaleEntryCol) {
				resetSaleEntry.set("warehouse", BusinessDataServiceHelper.loadSingle(newWs, "bd_warehouse"));
			}
		}
		SaveServiceHelper.save(new DynamicObject[] {resetBill});// 保存
	}

	/**
	 * 重置分录含税单价后重新金额字段
	 */
	public void changePriceAndTax(DynamicObject tempTargetBillObj, DynamicObject entryObj) {
		boolean isTax = tempTargetBillObj.getBoolean("istax");
		// 计算单价
		BigDecimal taxPrice = entryObj.getBigDecimal("priceandtax");
		BigDecimal taxRate = entryObj.getBigDecimal("taxrate");
		BigDecimal price = BigDecimal.ZERO;
		BigDecimal ONEHUNDRED = new BigDecimal(100);
		if (taxPrice != null) {
			BigDecimal zero = BigDecimal.ZERO;
			BigDecimal one = BigDecimal.ONE;
			BigDecimal oneHundred = ONEHUNDRED;
			taxRate = taxRate == null ? zero : taxRate.divide(oneHundred, taxRate.scale() + 2, 4);
			price = taxPrice.divide(one.add(taxRate), pricePrecision, 4);
		}
		entryObj.set("price", price);

		if (isTax) {
			this.calAmountAndTax(entryObj, isTax);
			this.calTaxAmount(entryObj, isTax);
			this.calAmount(entryObj, isTax);
		} else {
			this.calAmount(entryObj, isTax);
			this.calTaxAmount(entryObj, isTax);
			this.calAmountAndTax(entryObj, isTax);
		}

		if (isTax) {
			this.calCurAmountAndTax(tempTargetBillObj, entryObj, isTax);
			this.calCurTaxAmount(tempTargetBillObj, entryObj, isTax);
			this.calCurAmount(tempTargetBillObj, entryObj, isTax);
		} else {
			this.calCurAmount(tempTargetBillObj, entryObj, isTax);
			this.calCurTaxAmount(tempTargetBillObj, entryObj, isTax);
			this.calCurAmountAndTax(tempTargetBillObj, entryObj, isTax);
		}

		BigDecimal discountAmount = entryObj.getBigDecimal("discountamount");
		BigDecimal amount;
		BigDecimal amountAndTax;
		if (BigDecimal.ZERO.compareTo(discountAmount) == 0) {
			amount = entryObj.getBigDecimal("price");
			entryObj.set("actualprice", amount);
			amountAndTax = entryObj.getBigDecimal("priceandtax");
			entryObj.set("actualtaxprice", amountAndTax);
		} else {
			amount = entryObj.getBigDecimal("amount");
			BigDecimal qty = entryObj.getBigDecimal("qty");
			if (BigDecimal.ZERO.compareTo(amount) != 0 && BigDecimal.ZERO.compareTo(qty) != 0) {
				BigDecimal tempPrice = amount.divide(qty, this.pricePrecision, 4);
				entryObj.set("actualprice", tempPrice);
			} else {
				entryObj.set("actualprice", BigDecimal.ZERO);
			}
			amountAndTax = entryObj.getBigDecimal("amountandtax");
			if (BigDecimal.ZERO.compareTo(amountAndTax) != 0 && BigDecimal.ZERO.compareTo(qty) != 0) {
				BigDecimal temptaxPrice = amountAndTax.divide(qty, this.pricePrecision, 4);
				entryObj.set("actualtaxprice", temptaxPrice);
			} else {
				entryObj.set("actualtaxprice", BigDecimal.ZERO);
			}
		}

	}

	private void calCurAmount(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
		BigDecimal amount;
		BigDecimal exchangerate;
		if (isTax) {
			amount = entryObj.getBigDecimal("curamountandtax");
			exchangerate = entryObj.getBigDecimal("curtaxamount");
			entryObj.set("curamount", amount.subtract(exchangerate));
		} else {
			amount = entryObj.getBigDecimal("amount");
			exchangerate = tempTargetBillObj.getBigDecimal("exchangerate");
			BigDecimal localTaxAmount = amount.multiply(exchangerate).setScale(this.curAmountPrecision, 4);
			entryObj.set("curamount", localTaxAmount);
		}

	}

	private void calCurTaxAmount(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
		BigDecimal tax = entryObj.getBigDecimal("taxamount");
		BigDecimal exchangerate = tempTargetBillObj.getBigDecimal("exchangerate");
		BigDecimal localTax = tax.multiply(exchangerate).setScale(this.curAmountPrecision, 4);
		entryObj.set("curtaxamount", localTax);
	}

	private void calCurAmountAndTax(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
		BigDecimal localAmount;
		BigDecimal localTax;
		if (isTax) {
			localAmount = entryObj.getBigDecimal("amountandtax");
			localTax = tempTargetBillObj.getBigDecimal("exchangerate");
			BigDecimal localTaxAmount = localAmount.multiply(localTax).setScale(this.curAmountPrecision, 4);
			entryObj.set("curamountandtax", localTaxAmount);
		} else {
			localAmount = entryObj.getBigDecimal("curamount");
			localTax = entryObj.getBigDecimal("curtaxamount");
			entryObj.set("curamountandtax", localAmount.add(localTax));
		}

	}

	private void calAmount(DynamicObject entryObj, boolean isTax) {
		BigDecimal price;
		BigDecimal qty;
		BigDecimal amount;
		if (isTax) {
			price = entryObj.getBigDecimal("amountandtax");
			qty = entryObj.getBigDecimal("taxamount");
			amount = price.subtract(qty);
			entryObj.set("amount", amount);
		} else {
			price = entryObj.getBigDecimal("price");
			qty = entryObj.getBigDecimal("qty");
			amount = qty.multiply(price).setScale(this.amountPrecision, 4);

			entryObj.set("amount", amount);
		}

	}

	private void calTaxAmount(DynamicObject entryObj, boolean isTax) {
		BigDecimal amount;
		BigDecimal taxRate;
		BigDecimal tax;
		if (isTax) {
			amount = entryObj.getBigDecimal("amountandtax");
			taxRate = entryObj.getBigDecimal("taxrate");
			tax = BigDecimal.ZERO;
			if (amount.compareTo(BigDecimal.ZERO) != 0 && taxRate.compareTo(BigDecimal.ZERO) != 0) {
				taxRate = taxRate.divide(ONEHUNDRED, taxRate.scale() + 2, 4);
				tax = amount.multiply(taxRate).divide(taxRate.add(BigDecimal.ONE), this.amountPrecision, 4);
			}

			entryObj.set("taxamount", tax);
		} else {
			amount = entryObj.getBigDecimal("amount");
			taxRate = entryObj.getBigDecimal("taxrate");
			tax = BigDecimal.ZERO;
			if (amount.compareTo(BigDecimal.ZERO) != 0 && taxRate.compareTo(BigDecimal.ZERO) != 0) {
				taxRate = taxRate.divide(ONEHUNDRED, taxRate.scale() + 2, 4);
				tax = amount.multiply(taxRate).setScale(this.amountPrecision, 4);
			}

			entryObj.set("taxamount", tax);
		}

	}

	private void calAmountAndTax(DynamicObject entryObj, boolean isTax) {
		BigDecimal amount;
		BigDecimal tax;
		BigDecimal taxAmount;
		if (isTax) {
			amount = entryObj.getBigDecimal("priceandtax");
			tax = entryObj.getBigDecimal("qty");
			taxAmount = BigDecimal.ZERO;
			BigDecimal discountAmount = entryObj.getBigDecimal("discountamount");
			taxAmount = tax.multiply(amount).subtract(discountAmount).setScale(this.amountPrecision, 4);
			entryObj.set("amountandtax", taxAmount);
		} else {
			amount = entryObj.getBigDecimal("amount");
			tax = entryObj.getBigDecimal("taxamount");
			taxAmount = amount.add(tax);
			entryObj.set("amountandtax", taxAmount);
		}

	}

	/**
	 * 下推单据
	 * @param oriBillId
	 * @param oriBillEntity
	 * @param desBillEntity
	 * @param ruleId
	 * @return
	 */
	private DynamicObject pushBill(String oriBillId, String oriBillEntity, String desBillEntity, String ruleId) {
		// 将源单ID，源单标识，目标单标识，目标单名称，下推规则作为参数
		// 返回目标单ID
		// 构建下推参数
		PushArgs pushArgs = new PushArgs();
		pushArgs.setSourceEntityNumber(oriBillEntity);  // 源单标志
		pushArgs.setTargetEntityNumber(desBillEntity);  // 目标单标志
		pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告
		pushArgs.setRuleId(ruleId);  // 固定下推规则
		String billName = "";
		if ("im_purinbill".equals(desBillEntity)) {
			billName = "采购入库单";
		}
		if ("im_saloutbill".equals(desBillEntity)) {
			billName = "销售出库单";
		}

		//需要下推的单据
        List<ListSelectedRow> selectedRows = new ArrayList<>();
        ListSelectedRow srcBill = new ListSelectedRow(oriBillId);
        selectedRows.add(srcBill);
        pushArgs.setSelectedRows(selectedRows);

        //调用下推引擎，下推目标单
        ConvertOperationResult pushResult = ConvertServiceHelper.push(pushArgs);

        //判断下推是否成功，如果失败，提炼失败消息
        if(!pushResult.isSuccess()){
            //错误摘要
            String errMessage = pushResult.getMessage();
            throw new KDException("下推"+billName+"失败" + errMessage);
        }

        //获取生成的目标单数据包
        MainEntityType targetMainType = EntityMetadataCache.getDataEntityType(desBillEntity);
        List<DynamicObject> targetBillObjs = pushResult.loadTargetDataObjects(new IRefrencedataProvider() {
            @Override
            public void fillReferenceData(Object[] objs, IDataEntityType dType) {
                BusinessDataReader.loadRefence(objs, dType);
            }
        }, targetMainType);

        return targetBillObjs.get(0);
	}

	/**
	 * 操作单据的保存、提交、审核，都会触发源单的业务规则
	 * @return 成功则返回单据ID，失败则增加返回失败原因（用于反写到源单），同时也记录到单据上billId+errorMsg
	 */
	private Map<String,String> operateTargetBill(String desBillEntity, DynamicObject targetBillObj) {
		Map<String,String> result = new HashMap<String,String>();
		String desBillId = "";
		String billName = "";
		if ("im_purinbill".equals(desBillEntity)) {
			billName = "采购入库单";
		}
		if ("im_saloutbill".equals(desBillEntity)) {
			billName = "销售出库单";
		}
		// 保存目标单据
        OperationResult saveResult = SaveServiceHelper.saveOperate(desBillEntity, new DynamicObject[]{targetBillObj}, OperateOption.create());
        if (!saveResult.isSuccess()) {
            // 错误摘要
            String errMessage = "保存"+billName+"失败：" + saveResult.getMessage();
//          throw new KDException("保存"+billName+"失败：" + errMessage);
            System.out.println(errMessage);
            result.put("billId", "");
            result.put("errorMsg", errMessage);
            return result;
        }

        // 提交目标单据
        List<Object> successPkIds = saveResult.getSuccessPkIds();
        for (Object successPkId : successPkIds) {
        	String pk = saveResult.getSuccessPkIds().get(0).toString();
        	QFilter qFilter1 = new QFilter("id", QCP.equals, pk);
        	Object billNo = BusinessDataServiceHelper.loadSingle(desBillEntity, "id,billno", qFilter1.toArray()).get("billno");
        	OperationResult submitResult = OperationServiceHelper.executeOperate("submit", desBillEntity, new Object[]{pk}, OperateOption.create());
        	if (!submitResult.isSuccess()) {
        		// 错误摘要
        		String errMessage = billName + billNo + "生成成功，但提交失败，请到"+billName+"手动提交，查看提交失败原因：" + submitResult.getMessage();
//        		throw new KDException(billName + billNo + "生成成功，但提交失败，请到"+billName+"手动提交，查看提交失败原因：" + errMessage);
        		System.out.println(errMessage);
                result.put("billId", "");
                result.put("errorMsg", errMessage);
                return result;
        	}
		}

        // 审核目标单据
        for (Object successPkId : successPkIds) {
        	String pk = saveResult.getSuccessPkIds().get(0).toString();
        	QFilter qFilter1 = new QFilter("id", QCP.equals, pk);
        	Object billNo = BusinessDataServiceHelper.loadSingle(desBillEntity, "id,billno", qFilter1.toArray()).get("billno");
        	OperationResult submitResult = OperationServiceHelper.executeOperate("audit", desBillEntity, new Object[]{pk}, OperateOption.create());
        	if (!submitResult.isSuccess()) {
        		// 错误摘要
        		String errMessage = billName + billNo + "提交成功，但审核失败，请到"+billName+"手动审核，查看提交失败原因：" + submitResult.getMessage();
//        		throw new KDException(billName + billNo + "提交成功，但审核失败，请到"+billName+"手动审核，查看提交失败原因：" + errMessage);
        		System.out.println(errMessage);
                result.put("billId", "");
                result.put("errorMsg", errMessage);
                return result;
        	} else {
        		desBillId = pk;
        		result.put("billId", desBillId);
                result.put("errorMsg", "");
                return result;
        	}
		}

		return result;
	}

	/**
	 * PCP销售出库单一盘货结算
	 * @author: hst
	 * @createDate  : 2022/10/28
	 * @param saleoutBill 单据
	 * @param isForward 是否是正向
	 * @return 是否成功
	 */
	@Override
	public ApiResult createPCPSettleBill(DynamicObject saleoutBill, boolean isForward) {
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 获取组织与默认仓库的关系
			Map<String, String> orgWarehouseMap = InternalSettleServiceHelper.getOrgWarehouseMap();
			// 源末级销售组织
			DynamicObject bizorg = saleoutBill.getDynamicObject("bizorg");
			// 最终的销售收货的客户
			DynamicObject finalCustomer = saleoutBill.getDynamicObject("customer");
			// 结算组织 2022/11/14
			DynamicObject settleOrg = saleoutBill.getDynamicObject("yd_settleorg");
			// 结算组织ID
			String oriSettleId = settleOrg.getPkValue().toString();
			// 最终的销售收货的客户ID
			String oriCustomerId = finalCustomer.getPkValue().toString();
			// update by hst 2022/11/28 校验客户基础资料里面的税率是否为空
			if (Objects.isNull(finalCustomer.get("taxrate"))) {
				throw new KDBizException("客户[" + finalCustomer.getString("name") + "]默认税率为空");
			}
			//结算路径 update by hst 2022/12/13 修改结算路径获取方式
			// update by hst 2023/03/22 合并开门红与线下一盘货
			Map<String,Object> settlePath = getPCPSettlePath(saleoutBill,isForward);
			LinkedList<String> orgRelList = (LinkedList<String>)settlePath.get("orgRelList");
			String outOrgId = settlePath.get("outOrgId").toString();
			// 校验物料是否都设置了组织交易价格和客户交易价格和仓库映射关系，其中一个没有就报错
			// update by hst 2022/12/14 修改校验代码
			// update by hst 2023/04/13 修复线下一盘货路径获取
			Map<String,String> internalCheckResult = InternalSettleServiceHelper.checkPCPSettleMsg(saleoutBill.getPkValue().toString());
			if (StringUtils.isEmpty(internalCheckResult.get("billId"))) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("单据"+saleoutBill.getString("billno")+"无法进行内部结算："+internalCheckResult.get("errorMsg"));
			}

			// 按品牌获取最终的客户
			//update by hst 2022/11/14
//			finalCustomer = BusinessDataServiceHelper.loadSingle(lastCustomerId, "bd_customer");
			// 根据组织与客户的交易价格关系重新对单据的单价进行赋值
			// 根据单据业务日期获取对应日期内的价格
			Date bizDate = saleoutBill.getDate("biztime");
			Map<String, BigDecimal> orgtoCusNewPriceMap = InternalSettleServiceHelper.getOrgtoCusNewPrice(outOrgId, finalCustomer.getPkValue().toString(), bizDate);
			// 重置单据的组织 update by hst 2022/11/10 若库存组织与销售组织不一致且销售组织等于末级组织，不会重置库存组织，数据有误
			DynamicObject lastOrg = BusinessDataServiceHelper.loadSingle(outOrgId, "bos_org");
			// update by hst -2023/03/22 存储原中间表的库存组织，用于重置结算时恢复,方便重置结算后线下一盘货再次找到正确的结算路径
			saleoutBill.set("yd_oriorg", saleoutBill.get("org"));
			saleoutBill.set("org", lastOrg);
			saleoutBill.set("bizorg", lastOrg);
			saleoutBill.set("bizdept", lastOrg);
			saleoutBill.set("dept", lastOrg);
			// update by hst -2022/11/14 存储原中间表的结算组织，用于重置结算时恢复,方便重置结算后再次找到正确的结算路径
			saleoutBill.set("yd_orisettleorg",saleoutBill.get("yd_settleorg"));
			saleoutBill.set("yd_settleorg",lastOrg);

			// 重置最终的客户
			saleoutBill.set("customer", finalCustomer);
			saleoutBill.set("yd_agencycustomer", finalCustomer);
			saleoutBill.set("yd_cuschannel", finalCustomer.getDynamicObject("yd_channel"));  // 客户所属渠道

			// 根据客户获取税率
			DynamicObject cusTaxRateObj = finalCustomer.getDynamicObject("taxrate");

			// 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
			DynamicObjectCollection oriSaleEntryCol = saleoutBill.getDynamicObjectCollection("billentry");
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
				// update by hst 2022/12/27 源销售出库单无需重置赠品标识
//				Boolean isGif = oriSaleEntry.getBoolean("ispresent");
//				if (isGif) {
//					oriSaleEntry.set("ispresent", false);
					cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
					oriSaleEntry.set("taxrateid", cusTaxRateObj);
					oriSaleEntry.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
//				}
				// 根据销售关系yd_orgcuspricebill获取物料价格，再重算金额
				String materialId = oriSaleEntry.getString("material.masterid.id");
				DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
				// 重置单据的组织 update by hst 2022/11/10 若库存组织与销售组织不一致且销售组织等于末级组织，不会重置库存组织，数据有误
				oriSaleEntry.set("entrysettleorg", lastOrg);  // 结算组织
				oriSaleEntry.set("outowner", lastOrg);  // 出库货主
				oriSaleEntry.set("outkeeper", lastOrg);  // 出库保管者
				// 保存一下源单的E3仓库
				oriSaleEntry.set("yd_e3oriwarehouse", oriSaleEntry.get("warehouse"));
				// 库存组织+物料+批次获取生产日期和到期日期
				String lot = oriSaleEntry.getString("yd_ph");
				String matNum = tempMat.getString("number");
				String wsNum = oriSaleEntry.getDynamicObject("warehouse").getString("number");
				if (isForward) {
					oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, false);
				} else {
					oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, true);
				}
				// 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）
				DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
				String isGeneralDate = "";
				if (isGeneralDateObj.length > 0) {
					isGeneralDate = isGeneralDateObj[0].getString("name");
				}
				if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
						&& oriSaleEntry.get("yd_dqr") == null) {
					// 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期）
					if (isForward) {
						oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, false);
					} else {
						oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, true);
					}
				}
				// 重置最终的客户
				oriSaleEntry.set("settlecustomer", finalCustomer);  // 应收客户
				oriSaleEntry.set("payingcustomer", finalCustomer);  // 付款客户
				oriSaleEntry.set("reccustomer", finalCustomer);  // 收货客户
			}
			// 标记源单为已生成内部结算单据标识
			saleoutBill.set("yd_internalsettle", true);
			// 如果只有一级结算组织，就在股份，则不会有下游单据，标记这种进行单独集成流程传递
			if (orgRelList.size() == 0) {
				saleoutBill.set("yd_singleSettle", true);
			}
			// 再次发起结算时，清空结算失败原因
			saleoutBill.set("yd_settleerror", "");
			// 对单据进行保存提交审核，拿到成功审核的单据ID
			String saleoutBillId = saleoutBill.getPkValue().toString();
			Map<String, String> opResult = operateTargetBill(saleoutBillId, "im_saloutbill", saleoutBill);
			saleoutBillId = opResult.get("billId");
			// 如果是第一组则重置多少倍退货数量(退货单据类型时)，标记当前单据为首单
			if (!isForward) {
				DynamicObject backSaleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
				DynamicObjectCollection backSaleEntryCol = backSaleoutBill.getDynamicObjectCollection("billentry");
				for (DynamicObject entry : backSaleEntryCol) {
					entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(new BigDecimal(orgRelList.size())));
					entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(new BigDecimal(orgRelList.size())));
				}
				backSaleoutBill.set("yd_firstinternalbill", true);
				SaveServiceHelper.save(new DynamicObject[]{backSaleoutBill});// 保存
				//重置源单上分录的仓库为组织对应的仓库
				resetBillWarehouse(saleoutBillId, "im_saloutbill", orgWarehouseMap);
			}
			String oriBillId = "";
			String desBillId = "";
			for (String orgRelMap : orgRelList) {
				String[] keys = orgRelMap.split("&");
				String srcOrg = keys[0];
				DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
				String desOrg = keys[1];
				DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");
				// 获取组织内部各个物料新的结算价格集合
				// 调整，根据单据业务日期获取对应日期范围内的价格
				Map<String, BigDecimal> matPriceMap;
				if (isForward) {
					matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);
				} else {
					matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(desOrg, srcOrg, bizDate);
				}
				if (orgRelMap.equals(orgRelList.getFirst())) {
					// 根据源销售出库单创建销售出库单(正向）
					// 根据源销售出库单创建本级采购入库单(退货)
					oriBillId = saleoutBillId;
					String oriBillEntity = isForward ? "im_saloutbill" : "im_saloutbill";
					String desBillEntity = isForward ? "im_saloutbill" : "im_purinbill";
					String ruleId = isForward ? "1553117535367359488" : "1553121011732342784";
					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = createOutBill(saleoutBillId,oriBillId,oriBillEntity,desBillEntity,ruleId,srcOrgObj,desOrgObj,matPriceMap,true,isForward);
					desBillId = opResult.get("billId");
					// 重置源单上分录的仓库为组织对应的仓库
					if (isForward) {
						resetBillWarehouse(oriBillId, "im_saloutbill", orgWarehouseMap);
					} else {
						resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
						// 第一组则重置多少倍退货数量
						DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_purinbill");
						DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
						for(DynamicObject entry : entryCol) {
							entry.set("returnqty", entry.getBigDecimal("qty").multiply(new BigDecimal(orgRelList.size())));
							entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(new BigDecimal(orgRelList.size())));
						}
						SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存
					}

					// 根据销售出库单创建采购入库单，到此就完结了(正向)
					// 根据采购入库单创建上级销售出库单，到此就完结了(逆向)
					oriBillId = desBillId;
					oriBillEntity = isForward ? "im_saloutbill" : "im_purinbill";
					desBillEntity = isForward ? "im_purinbill" : "im_saloutbill";
					ruleId = isForward ? "1553121315089573888" : "1553122468514785280";
					opResult = createOutBill(saleoutBillId,oriBillId,oriBillEntity,desBillEntity,ruleId,srcOrgObj,desOrgObj,matPriceMap,false,isForward);
					desBillId = opResult.get("billId");
					// 重置源单上分录的仓库为组织对应的仓库
					if (isForward) {
						resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
					} else {
						resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
						// 第一组则重置多少倍退货数量
						DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_saloutbill");
						DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
						for(DynamicObject entry : entryCol) {
							// update by hst 2023/03/22 当只有二级组织时，退货一级组织需要重置E3仓库
							if (orgRelMap.equals(orgRelList.getLast())) {
								entry.set("warehouse", entry.getDynamicObject("yd_e3oriwarehouse"));
							}
							entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(new BigDecimal(orgRelList.size())));
							entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(new BigDecimal(orgRelList.size())));
						}
						SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存
					}
					oriBillId = desBillId;
				}

				// 不是第一组，创建销售出和采购入
				if (!orgRelMap.equals(orgRelList.getFirst())) {
					// 根据上一级的采购入库单创建销售出库单
					String oriBillEntity = isForward ? "im_purinbill" : "im_saloutbill";
					String desBillEntity = isForward ? "im_saloutbill" : "im_purinbill";
					String ruleId = isForward ? "1553122087932029952" : "1553121011732342784";
					opResult = createOutBill(saleoutBillId,oriBillId,oriBillEntity,desBillEntity,ruleId,srcOrgObj,desOrgObj,matPriceMap,false,isForward);
					desBillId = opResult.get("billId");
					// 重置源单上分录的仓库为组织对应的仓库
					if (!isForward) {
						resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
					}
					// 根据销售出库单创建采购入库单，到此就完结了
					oriBillId = desBillId;
					oriBillEntity = isForward ? "im_saloutbill" : "im_purinbill";
					desBillEntity = isForward ? "im_purinbill" : "im_saloutbill";
					ruleId = isForward ? "1553121315089573888" : "1553122468514785280";
					opResult = createOutBill(saleoutBillId,oriBillId,oriBillEntity,desBillEntity,ruleId,srcOrgObj,desOrgObj,matPriceMap,false,isForward);
					desBillId = opResult.get("billId");
					// 重置源单上分录的仓库为组织对应的仓库
					if (isForward) {
						resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
					} else {
						resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
						// 最后一张销售出库单的仓库为源单E3仓库
						DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_saloutbill");
						DynamicObjectCollection saleoutEntryCol = resetBill.getDynamicObjectCollection("billentry");
						for (DynamicObject resetSaleEntry : saleoutEntryCol) {
							if (orgRelMap.equals(orgRelList.getLast())) {
								resetSaleEntry.set("warehouse", resetSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
							}
							// update by hstt 2022/11/10 最后一张出库单退货数量不用翻倍
							if (!orgRelMap.equals(orgRelList.getLast())) {
								resetSaleEntry.set("remainreturnqty", resetSaleEntry.getBigDecimal("qty").abs().multiply(new BigDecimal(orgRelList.size())));
								resetSaleEntry.set("remainreturnbaseqty", resetSaleEntry.getBigDecimal("baseqty").abs().multiply(new BigDecimal(orgRelList.size())));
							}
						}
						SaveServiceHelper.save(new DynamicObject[] {resetBill});// 保存
					}
					oriBillId = desBillId;
				}
				// 最后一组只执行创建销售出库单
				if (orgRelMap.equals(orgRelList.getLast())) {
					System.out.println("最后一组组织关系");
				}
			}
			saleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			// 标记源单为已生成内部结算单据标识
			saleoutBill.set("yd_internalsettle", true);
			SaveServiceHelper.save(new DynamicObject[] {saleoutBill});// 保存
		}  catch (Exception e) {
			// 记录错误信息
			logger.error(e);
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 获取结算路径
	 * @author: hst
	 * @createDate  : 2022/10/28
	 * @param brand 品牌
	 * @param customerId 客户id
	 * @param isForward 是否是正向
     * @param type  1、一盘货方式（一条结算路径对应一个客户)、2、直营店方式（一条路径对应多个客户）、3、直营店方式（一条路径对应多个结算组织）
	 * @return (path:结算路径, lastOrgNum:最终组织, lastCustomerId:最终客户
	 */
	public static Map<String,Object> getInventorySettlePath (DynamicObject brand, String customerId, boolean isForward,String type) {
		Map<String,Object> map = new HashMap<>();
        DataSet settleDataSet = null;
		if (Objects.nonNull(brand)) {
            // 获取品牌对应的内部结算关系以及最后一级的组织，如果是正向则从上到下，升序
		    if ("1".equals(type)) {
                QFilter qFilter = new QFilter("yd_brand.number", QCP.equals, brand.getString("number"));
                qFilter.and(new QFilter("yd_oricustomer.id", QCP.equals, customerId));
                qFilter.and(new QFilter("billstatus", QCP.equals, "C"));
                String orderby = isForward ? "entryentity.seq asc" : "entryentity.seq desc";
                settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_innerrelbill",
                        "yd_newcustomer.id newCusId,entryentity.yd_orglevel orglevel,entryentity.yd_org.id orgid," +
                                "entryentity.yd_org.number orgNum, yd_isoutsettle isOutSettle", qFilter.toArray(), orderby);
            } else if ("2".equals(type)) {
                QFilter qFilter = new QFilter("yd_brand.number", QCP.equals, brand.getString("number"));
                qFilter.and(new QFilter("yd_cusentry.yd_customer.id", QCP.equals, customerId));
                qFilter.and(new QFilter("status", QCP.equals, "C"));
                String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
                settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_directsettlerela",
                        "yd_cusentry.yd_customer.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
                                "yd_levelentry.yd_org.number orgNum, yd_isoutsettle isOutSettle", qFilter.toArray(), orderby);
            } else if ("3".equals(type)) {
				QFilter qFilter = new QFilter("yd_brand.number", QCP.equals, brand.getString("number"));
				qFilter.and(new QFilter("yd_settleentry.yd_settleorg.id", QCP.equals, customerId));
				qFilter.and(new QFilter("status", QCP.equals, "C"));
				String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
				settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_directsettlerela",
						"yd_settleentry.yd_settleorg.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
								"yd_levelentry.yd_org.number orgNum, yd_isoutsettle isOutSettle", qFilter.toArray(), orderby);
			}
			if (!settleDataSet.hasNext()) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("当前品牌" + brand.getString("name") + "没有配置内部结算关系，无法生成内部结算流程单据！");
			}
			LinkedList<String> orgRelList = new LinkedList<String>();
			// 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
			String outOrgId = "";
			for (Row settleRow : settleDataSet) {
				String curOrgId = settleRow.getString("orgid");
				if (StringUtils.isNotEmpty(outOrgId)) {
					String mapValue = outOrgId+"&"+curOrgId;
					orgRelList.add(mapValue);
				}
				outOrgId = curOrgId;
				if (isForward) {
					map.put("outOrgId",outOrgId);
					map.put("lastOrgNum",settleRow.getString("orgNum"));
					map.put("lastCustomerId",settleRow.getString("newCusId"));
				} else if (!isForward && map.size() == 0) {
					map.put("outOrgId", outOrgId);
					map.put("lastOrgNum", settleRow.getString("orgNum"));
					map.put("lastCustomerId", settleRow.getString("newCusId"));
				}
			}
			map.put("orgRelList",orgRelList);
		} else {
			throw new KDBizException("获取不到品牌信息，无法生成PCP结算流程单据！");
		}
		return map;
	}

	/**
	 * 保存、提交、审核单据
	 * @author: hst
	 * @createDate: 2022/10/28
	 * @param oriBillId
	 * @param desBillEntity
	 * @param targetBillObj
	 */
	public Map<String, String> operateTargetBill(String oriBillId, String desBillEntity, DynamicObject targetBillObj) {
		Map<String, String> opResult = operateTargetBill(desBillEntity, targetBillObj);
		if (StringUtils.isEmpty(opResult.get("billId"))) {
			// 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
			String error = opResult.get("errorMsg");
			errorSettleBill(oriBillId, "im_saloutbill", error);
			throw new KDBizException(error);
		}
		return opResult;
//		SaveServiceHelper.save(new DynamicObject[]{targetBillObj});
//		Map<String,String> opResult = new HashMap<>();
//		opResult.put("billId",targetBillObj.getPkValue().toString());
//		return opResult;
	}

	/**
	 * 下推生成销售出库单/采购入库单
	 * @author: hst
	 * @createDate: 2022/10/28
	 * @param saleoutBillId 销售出库单id
	 * @param oriBillId 下推单据id
	 * @param oriBillEntity 下推单据类型
	 * @param desBillEntity 目标单据类型
	 * @param ruleId 转换路线标识ID
	 * @param srcOrg 源组织
	 * @param destOrg 目标组织
	 * @param matPriceMap 内部校验价格
	 * @param isFirst 是否首单
	 * @param isForward 是否正向
	 * @return
	 */
	public Map<String,String> createOutBill(String saleoutBillId, String oriBillId, String oriBillEntity, String desBillEntity, String ruleId,
						   DynamicObject srcOrg, DynamicObject destOrg, Map<String, BigDecimal> matPriceMap, boolean isFirst, boolean isForward) {
		DynamicObject tempTargetBillObj = pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
		// 首笔销售出库单需要标记为首单，重置首单的信息，主组织应该为第一组组织中的前者
		// 需要赋值的字段：单据头.库存组织，单据头.销售组织，单据头.销售部门，单据头.库管部门，物料明细.结算组织，为组织关系中的前者
		DynamicObject org = null;
		if ("im_saloutbill".equals(oriBillEntity) && "im_saloutbill".equals(desBillEntity)
				|| "im_purinbill".equals(oriBillEntity) && "im_saloutbill".equals(desBillEntity)) {
			org = isForward ? srcOrg : destOrg;
			// 根据编码获取后者组织作为客户
			QFilter cusFilter = new QFilter("internal_company.number", QCP.equals,
					isForward ? destOrg.getString("number") : srcOrg.getString("number"));
			cusFilter.and(new QFilter("status", QCP.equals, "C"));
			DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel,taxrate", cusFilter.toArray());
			DynamicObject customer = null;
			if (customers.length > 0) {
				customer = customers[0];
			}
			tempTargetBillObj.set("customer", customer);
			tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道
			DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
			// update by hst 2022/12/27 根据客户重新查找税率
			DynamicObject cusTaxRateObj = customer.getDynamicObject("taxrate");
			cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
			for (DynamicObject entryObj : outEntryCol) {
				entryObj.set("entrysettleorg", org);  // 结算组织
				entryObj.set("outowner", org);  // 出库货主
				entryObj.set("outkeeper", org);  // 出库保管者
				entryObj.set("settlecustomer", customer);  // 应收客户
				entryObj.set("payingcustomer", customer);  // 付款客户
				entryObj.set("reccustomer", customer);  // 收货客户
				// update by hst 2022/12/27 根据客户重置税率
				entryObj.set("taxrateid", cusTaxRateObj);
				entryObj.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
				// update by hst 2022/12/27 非末级重置赠品标识
//				Boolean isGif = entryObj.getBoolean("ispresent");
//				if (isGif) {
//					entryObj.set("ispresent", false);
//				}
				// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
				String materialId = entryObj.getString("material.masterid.id");
				if (matPriceMap.containsKey(materialId)) {
					BigDecimal newPrice = matPriceMap.get(materialId);
					entryObj.set("priceandtax", newPrice);
					changePriceAndTax(tempTargetBillObj, entryObj);
					// update by hst 20230601 单价为0标记为赠品
					if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
						entryObj.set("ispresent", true);
					} else {
						entryObj.set("ispresent", false);
					}
				}
			}
		} else if ("im_saloutbill".equals(oriBillEntity) && "im_purinbill".equals(desBillEntity)) {
			org = isForward ? destOrg : srcOrg;
			// 获取供应商
			QFilter supFilter = new QFilter("internal_company.number", QCP.equals,
                    isForward ? srcOrg.getString("number") : destOrg.getString("number"));
			supFilter.and(new QFilter("status", QCP.equals, "C"));
			DynamicObject[] suppliers = BusinessDataServiceHelper.load("bd_supplier", "id,name,number", supFilter.toArray());
			DynamicObject supplier = null;
			if (suppliers.length > 0) {
				supplier = suppliers[0];
			}
			// 单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商、
			tempTargetBillObj.set("supplier", supplier);
			DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
			// update by hst 2022/12/27 库存组织作为客户
			QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, org.getString("number"));
			cusFilter.and(new QFilter("status", QCP.equals, "C"));
			DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel,taxrate", cusFilter.toArray());
			DynamicObject customer = null;
			if (customers.length > 0) {
				customer = customers[0];
			}
			DynamicObject cusTaxRateObj = customer.getDynamicObject("taxrate");
			cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
			for(DynamicObject entryObj : inEntryCol) {
				if (isForward) {
					entryObj.set("owner", org);
					entryObj.set("keeper", org);
					entryObj.set("entrysettleorg", org);
					entryObj.set("entryreqorg", org);
				}
				entryObj.set("providersupplier", supplier);
				entryObj.set("invoicesupplier", supplier);
				entryObj.set("receivesupplier", supplier);
				// update by hst 2022/12/27 根据客户重置税率
				entryObj.set("taxrateid", cusTaxRateObj);
				entryObj.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
				// update by hst 2022/12/27 非末级重置赠品标识
//				Boolean isGif = entryObj.getBoolean("ispresent");
//				if (isGif) {
//					entryObj.set("ispresent", false);
//				}
				// 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
				String materialId = entryObj.getString("material.masterid.id");
				if (matPriceMap.containsKey(materialId)) {
					BigDecimal newPrice = matPriceMap.get(materialId);
					entryObj.set("priceandtax", newPrice);
					changePriceAndTax(tempTargetBillObj, entryObj);
					// update by hst 20230601 单价为0标记为赠品
					if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
						entryObj.set("ispresent", true);
					} else {
						entryObj.set("ispresent", false);
					}
				}
			}
		}
		tempTargetBillObj.set("org", org);
		tempTargetBillObj.set("bizorg", org);
		tempTargetBillObj.set("bizdept", org);
		tempTargetBillObj.set("dept", org);
		tempTargetBillObj.set("yd_settleorg",org);
		if (isFirst & isForward) {
			// 标记为首单（正向），用于集成平台开始同步的单据
			tempTargetBillObj.set("yd_firstinternalbill", true);
		}
		// 对单据进行保存提交审核，拿到成功审核的单据ID
		return operateTargetBill(saleoutBillId, desBillEntity, tempTargetBillObj);
	}

	/**
	 * 获取结算路径（根据是否按品牌拆单）
	 * @author: hst
	 * @createDate  : 2022/12/13
	 * @param bill 单据
	 * @param isForward 是否是正向
	 * @return (path:结算路径)
	 */
	public static Map<String,Object> getPCPSettlePath (DynamicObject bill, boolean isForward) {
		Map<String, Object> map = new HashMap<>();
		DataSet settleDataSet = null;
		// update by hst 2023/03/22 合并开门红与线下一盘货链路获取
		boolean isStart = bill.getBoolean("yd_isinventory");
		boolean isOffline = bill.getBoolean("yd_isoffline");
		if (isStart) {
			settleDataSet = InternalSettleServiceHelper.getGoodStartSettlePath(bill);
		}
		if (isOffline) {
			settleDataSet = InternalSettleServiceHelper.getOfflineSettlePath(bill);
		}
		if (Objects.nonNull(settleDataSet)) {
			LinkedList<String> orgRelList = new LinkedList<String>();
			// 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
			String outOrgId = "";
			for (Row settleRow : settleDataSet) {
				String curOrgId = settleRow.getString("orgid");
				if (StringUtils.isNotEmpty(outOrgId)) {
					String mapValue = outOrgId + "&" + curOrgId;
					orgRelList.add(mapValue);
				}
				outOrgId = curOrgId;
				if (isForward) {
					map.put("outOrgId", outOrgId);
				}
				if (!isForward && map.size() == 0) {
					map.put("outOrgId", outOrgId);
				}
			}
			map.put("orgRelList", orgRelList);
		}
		return map;
	}
	
	/**
	 * 描述：根据股份出库批次结果重置整条流程的出入库单的批次
	 * 拿到股份的单，然后按物料+仓库记录批次+生产日期+到期日期+数量
	 * 原来的单，按物料+仓库保留原分录行信息到内存中待用
	 * 对原来的单进行拆分分录行，将第二步的信息都复制回去，价格重算等
	 *
	 * @createDate  : 2022-12-26
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor:
	 * @param lotBillIdSet 股份出库结果单ID集合
	 * @return 是否成功
	 */
	@Override
	public ApiResult resetSettleBillLot(Set<String> lotBillIdSet) {

		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		
		// 校验是否为委外单据，是的话需要根据分录源E3仓库对仓库字段进行重置,yzl,20230131
		refreshWarehouse(lotBillIdSet);
		
		for (String lotBIllId : lotBillIdSet) {
			DynamicObject obj = BusinessDataServiceHelper.loadSingle(lotBIllId, "yd_saleoutlotbill");
			// 拿到股份出库的结果单，然后按物料+仓库记录批次+生产日期+到期日期+数量
			DynamicObjectCollection gfSaleEnCol = obj.getDynamicObjectCollection("entryentity");
			Map<String, List<SaleOutEnLotVo>> finalLotMap = new HashMap<String, List<SaleOutEnLotVo>>();
			for(int i=0;i<gfSaleEnCol.size();i++) {
				DynamicObject gfEnObj = gfSaleEnCol.get(i);
				SaleOutEnLotVo vo = new SaleOutEnLotVo();
				String matInvId = gfEnObj.getDynamicObject("yd_material").getString("id");
				String matId = gfEnObj.getDynamicObject("yd_material").getString("masterid.id");
				vo.setMatId(matInvId);
				String wsId = gfEnObj.getDynamicObject("yd_warehouse").getString("id");
				vo.setWsId(wsId);
				vo.setLot(gfEnObj.getString("yd_lot"));
				vo.setBeginDate(gfEnObj.getDate("yd_begindate"));
				vo.setEndDate(gfEnObj.getDate("yd_enddate"));
				vo.setQty(gfEnObj.getBigDecimal("yd_qty"));
				
				String key = matInvId+"&"+wsId;
//				System.out.println(key);
				if (finalLotMap.containsKey(key)) {
					List<SaleOutEnLotVo> list = finalLotMap.get(key);
					list.add(vo);
					finalLotMap.put(key, list);
				} else {
					List<SaleOutEnLotVo> list = new ArrayList<SaleOutEnLotVo>();
					list.add(vo);
					finalLotMap.put(key, list);
				}
			}
			
			// 先重置末级出库单
			// 原来的单，按物料+仓库保留原分录行信息到内存中待用
			// 对原来的单进行拆分分录行，将第二步的信息都复制回去，价格重算等
			String gfSaleOutBillNum = obj.getString("yd_gfsaleoutbillno");
			QFilter gfFilter = new QFilter("billno", QCP.equals, gfSaleOutBillNum);
			DynamicObjectCollection gfBillCol = QueryServiceHelper.query("im_saloutbill", "id, yd_sourcebilltype", gfFilter.toArray());
			if (gfBillCol.size() > 0) {
				DynamicObject gfBillInfo = gfBillCol.get(0);
				String gfSaleOutBillId = gfBillInfo.getString("id");
				// 根据来源单据类型区分，如果类型为4，则为OCS库存调拨（PCP库存调拨），其它走原逻辑
				if (!StringUtils.equals(gfBillInfo.getString("yd_sourcebilltype"), "4")) {
					//				DynamicObject gfBillInfo = BusinessDataServiceHelper.loadSingle(gfBillCol.get(0).getString("id"), "im_saloutbill");
					// 获取上下游单据，正向的是上游一张单，下游N张单，通过获取末级结算组织出库单的结算分录
					QFilter filter = new QFilter("yd_internalentity.yd_internalbillid", QCP.equals, gfSaleOutBillId);
					DynamicObjectCollection relCol = QueryServiceHelper.query("im_saloutbill", "id,billno", filter.toArray());
					if (relCol.size() > 0) {
						String srcBillId = relCol.get(0).getString("id");  // 末级出库单ID
						DynamicObject srcSaleoutBill = BusinessDataServiceHelper.loadSingle(srcBillId, "im_saloutbill");
						copyBill("im_saloutbill", srcBillId, finalLotMap);

						DynamicObjectCollection internalCol = srcSaleoutBill.getDynamicObjectCollection("yd_internalentity");
						for (int i = 0; i < internalCol.size(); i++) {
							DynamicObject enInfo = internalCol.get(i);
							String billType = enInfo.getString("yd_internalbilltype");
							String billId = enInfo.getString("yd_internalbillid");
							//						if ("1581818803480723456".equalsIgnoreCase(billId)) continue;
							copyBill(billType, billId, finalLotMap);
						}
					} else {
						// 单组织结算的，直接标记已重置批次，20230118,yzl
						DynamicObject singleObj = BusinessDataServiceHelper.loadSingle(gfSaleOutBillId, "im_saloutbill");
						singleObj.set("yd_beresetlot", true);  // 设置为已重置批次，yzl,20230105
						SaveServiceHelper.save(new DynamicObject[]{singleObj});
					}
				}else {
					// 重置OCS（PCP）库存调拨生成的出入库单重置批次等信息
					ocsTransBillResetLot(gfSaleOutBillId, finalLotMap);
				}
			}
		}
		
		
		return apiResult;
	}

	/**
	 * OCS库存调拨单重置批次
	 * @param saleId 首张股份的销售出库单ID
	 * @param finalLotMap 需重置的批次集合
	 * <AUTHOR>
	 * @date 2023-3-17
	 */
	private void ocsTransBillResetLot(String saleId, Map<String, List<SaleOutEnLotVo>> finalLotMap) {
		DynamicObject info = BizHelper.getDynamicObjectById("im_saloutbill", saleId, "yd_internalentity,yd_internalentity.yd_internalbilltype,yd_internalentity.yd_internalbillid");
		DynamicObjectCollection internalCol = info.getDynamicObjectCollection("yd_internalentity");
		// 重置批次的集合
		// 包含销售出库单 / 采购入库单，分别对应的单据ID和单据类型
		Map<String, String> resetLotMap = new LinkedHashMap<>();
		resetLotMap.put(saleId, "im_saloutbill");
		for (DynamicObject internalInfo : internalCol) {
			resetLotMap.put(internalInfo.getString("yd_internalbillid"), internalInfo.getString("yd_internalbilltype"));
		}
		// 遍历需要执行的单号，更新批次号、生产日期、到期日期
		for (String billId : resetLotMap.keySet()) {
			copyBill(resetLotMap.get(billId), billId, finalLotMap);
		}
	}

	/**
	 * 如果是委外类的销售出库单需要重置分录的仓库字段，因为委外结算第一章不是股份的单
	 * @param lotBillIdSet
	 * <AUTHOR>
	 * @date 2023-01-31
	 */
	private void refreshWarehouse(Set<String> lotBillIdSet) {
		for (String lotBIllId : lotBillIdSet) {
			DynamicObject obj = BusinessDataServiceHelper.loadSingle(lotBIllId, "yd_saleoutlotbill");
			String gfSaleOutBillNum = obj.getString("yd_gfsaleoutbillno");
			QFilter gfFilter = new QFilter("billno", QCP.equals, gfSaleOutBillNum);
			DynamicObjectCollection gfBillCol = QueryServiceHelper.query("im_saloutbill", "id,yd_outsettletype", gfFilter.toArray());
			if (gfBillCol.size() > 0) {
				String outSettleType = gfBillCol.get(0).getString("yd_outsettletype");
				if (StringUtils.equalsIgnoreCase(outSettleType, "directOutSettle") || StringUtils.equalsIgnoreCase(outSettleType, "distributorOutSettle")) {
					DynamicObjectCollection gfSaleEnCol = obj.getDynamicObjectCollection("entryentity");
					Map<String,DynamicObject> wsSet = new HashMap<String,DynamicObject>();
					for (DynamicObject gfSaleEnObj : gfSaleEnCol) {
						String oriWsName = gfSaleEnObj.getString("yd_oriwarehousename");
						if (wsSet.containsKey(oriWsName)) {
							gfSaleEnObj.set("yd_warehouse", wsSet.get(oriWsName));
							continue;
						}
						
						QFilter wsFilter = new QFilter("name", QCP.equals, oriWsName);
						wsFilter.and(new QFilter("enable", QCP.equals, "1"));
						wsFilter.and(new QFilter("status", QCP.equals, "C"));
						DynamicObject[] wsCol = BusinessDataServiceHelper.load("bd_warehouse", "id,name,number", wsFilter.toArray());
						if (wsCol.length > 0) {
							gfSaleEnObj.set("yd_warehouse", wsCol[0]);
							wsSet.put(oriWsName, wsCol[0]);
						}
					}
					SaveServiceHelper.save(new DynamicObject[] {obj});
				}
			}
		}
	}

	private void copyBill(String billType, String billId, Map<String, List<SaleOutEnLotVo>> finalLotMap) {
		// update by hst 2023/12/15 是否增加维度
		boolean isAdd = false;
		DynamicObject isAddDimension = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
				"name", new QFilter("number", QCP.equals, "E3_IS_ADDDIMENSION").toArray());
		if (Objects.nonNull(isAddDimension)
				&& "true".equalsIgnoreCase(isAddDimension.getString("name"))) {
			isAdd = true;
		}
		String srcBillId = billId;  // 出库单ID
		DynamicObject srcSaleoutBill = BusinessDataServiceHelper.loadSingle(srcBillId, billType);
		// 处理末级组织的单据信息，拆分分录行
		Map<String,DynamicObject> srcEnMap = new HashMap<String,DynamicObject>();
		Map<String,BigDecimal> srcPriceQtyMap = new HashMap<String,BigDecimal>();  // 金额数量集合MAP
		DynamicObjectCollection srcEnCol = srcSaleoutBill.getDynamicObjectCollection("billentry");
//		System.out.println("------------");
		for (int i=0;i<srcEnCol.size();i++) {
			DynamicObject enObj = srcEnCol.get(i);
			String matInvId = enObj.getDynamicObject("material").get("id").toString();
			String wsId = enObj.getDynamicObject("yd_e3oriwarehouse").get("id").toString();
			BigDecimal qty = enObj.getBigDecimal("qty");
			BigDecimal price = enObj.getBigDecimal("priceandtax");
			// 物料+仓库+单价->数量，然后扣减数量
			String key = matInvId+"&"+wsId+"&"+price.setScale(10).toString();
			// update by hst 2023/12/15 增加分录序号维度，解决相同物料、相同仓库及相同金额会合并问题
			if (isAdd) {
				String seq = enObj.getString("seq");
				// 物料+仓库+单价->数量，然后扣减数量
				key = matInvId + "&" + wsId + "&" + seq + "&" + price.setScale(10).toString();
			}
//			System.out.println(key);
			srcEnMap.put(key, enObj);
			
			if (srcPriceQtyMap.containsKey(key)) {
				BigDecimal sumQty = srcPriceQtyMap.get(key);
				sumQty = sumQty.add(qty);
				srcPriceQtyMap.put(key, sumQty);
			} else {
				srcPriceQtyMap.put(key, qty);
			}
		}
		srcEnCol.clear();
		DynamicObjectType saleEnType = srcEnCol.getDynamicObjectType();
		Iterator<String> ite = finalLotMap.keySet().iterator();
		Set<String> ignoreKey = new HashSet<String>();ignoreKey.add("id");
		while (ite.hasNext()) {
			String key = ite.next();
			List<SaleOutEnLotVo> list = finalLotMap.get(key);
			for(int i=0;i<list.size();i++) {
				SaleOutEnLotVo vo = list.get(i);
				BigDecimal lotQty = vo.getQty();
				
				// 根据短key获取匹配长key从而找到srcEnObj，然后扣减数量，每次获取比当前出库结果大的源分录数据
				String finalKey = key;  // 先默认一个key，确保能获取到数据
				BigDecimal minQty = new BigDecimal(9999999);  // 先默认一个最小数量
				Iterator<String> priceIte = srcPriceQtyMap.keySet().iterator();
				
				Map<String,BigDecimal> matchPriceQtyMap = new HashMap<String,BigDecimal>();  // 复核的金额数量集合MAP，然后找最小
				while (priceIte.hasNext()) {
					String priceKey = priceIte.next();
					BigDecimal tmpQty = srcPriceQtyMap.get(priceKey);
					// 因为EAS只会拆分分录，所以EAS分录数大于等于中台分录数，数量也是中台的大，所以要匹配比EAS大的中台分录
					// 从小到大找分录进行扣减匹配，否则会被随机拆分而导致多拆分了分录
					if (priceKey.contains(key) && tmpQty.compareTo(lotQty) >= 0) {
//						finalKey = priceKey;
//						tmpQty = tmpQty.subtract(finalQty);
//						srcPriceQtyMap.put(priceKey, tmpQty);
//						break;
						matchPriceQtyMap.put(priceKey, tmpQty);  // 都是符合扣减的分录数据，放进去，然后再找最小
						if (tmpQty.compareTo(minQty) <= 0) {
							minQty = tmpQty;
						}
					}
				}
				
				// 找最小的那条记录去扣减
				Iterator<String> minIte = matchPriceQtyMap.keySet().iterator();
				while(minIte.hasNext()) {
					String minKey = minIte.next();
					if (matchPriceQtyMap.get(minKey).compareTo(minQty) == 0) {
						finalKey = minKey;
						BigDecimal tmpQty = srcPriceQtyMap.get(minKey);
						tmpQty = tmpQty.subtract(lotQty);
						srcPriceQtyMap.put(minKey, tmpQty);
						break;
					}
				}
				
				DynamicObject oriEnObj = srcEnMap.get(finalKey);
				if (oriEnObj == null) {
					throw new KDBizException("单据"+billId+"拆分失败"+finalKey);
				}
				DynamicObject newEnObj = new DynamicObject(saleEnType);
				CommonUtils.copyDynObj(newEnObj, oriEnObj, ignoreKey);
//				for (String tmpKey : copyKeys) {
////				System.out.println(tmpKey);
//					newEnObj.set(tmpKey, oriEnObj.get(tmpKey)==null?null:oriEnObj.get(tmpKey));
//				}
				// 重新复制批次、数量，并重新计算
				newEnObj.set("yd_ph", vo.getLot());
				newEnObj.set("yd_scrq", vo.getBeginDate());
				newEnObj.set("yd_dqr", vo.getEndDate());
				newEnObj.set("qty", vo.getQty());
				newEnObj.set("baseqty", vo.getQty());
				if ("im_saloutbill".equalsIgnoreCase(billType)) {
					newEnObj.set("remainjoinpriceqty", vo.getQty());
					newEnObj.set("remainjoinpricebaseqty", vo.getQty());
					newEnObj.set("remainreturnqty", vo.getQty());
					newEnObj.set("remainreturnbaseqty", vo.getQty());
					newEnObj.set("unverifyqty", vo.getQty());
					newEnObj.set("unverifybaseqty", vo.getQty());
					srcSaleoutBill.set("yd_beresetlot", true);  // 设置为已重置批次，yzl,20230105
				} else if ("im_purinbill".equalsIgnoreCase(billType)) {
					newEnObj.set("remainjoinpriceqty", vo.getQty());
					newEnObj.set("remainjoinpricebaseqty", vo.getQty());
					newEnObj.set("remainreturnqty", vo.getQty());
					newEnObj.set("remainreturnbaseqty", vo.getQty());
					newEnObj.set("unverifyqty", vo.getQty());
					newEnObj.set("unverifybaseqty", vo.getQty());
				}
				changePriceAndTax(srcSaleoutBill, newEnObj);
				srcEnCol.add(newEnObj);
			}
		}
		
		SaveServiceHelper.save(new DynamicObject[] {srcSaleoutBill});
	}

	/**
	 * PCP销售出库单虚体组织结算
	 * @author: hst
	 * @createDate  : 2023/02/21
	 * @param saleoutBill 单据
	 * @return 是否成功
	 */
	@Override
	public ApiResult createPCPVirtualSettleBill(DynamicObject saleoutBill) {
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 获取组织与默认仓库的关系
			Map<String, String> orgWarehouseMap = InternalSettleServiceHelper.getOrgWarehouseMap();
			//结算路径
			Map<String, Object> settlePath = getVirtualSettlePath(saleoutBill);
			LinkedList<String> orgRelList = (LinkedList<String>) settlePath.get("orgRelList");
			LinkedList<String> orgEmptyList = (LinkedList<String>) settlePath.get("orgEmptyList");
			DynamicObject finalCustomer = (DynamicObject) settlePath.get("finalCustomer");
			String outOrgId = settlePath.get("outOrgId").toString();
			// 校验物料是否都设置了组织交易价格和客户交易价格和仓库映射关系，其中一个没有就报错
			Map<String, String> internalCheckResult = InternalSettleServiceHelper.checkPCPSettleMsg(saleoutBill.getPkValue().toString(),"virtual");
			if (StringUtils.isEmpty(internalCheckResult.get("billId"))) {
				// 品牌没有配置对应的内部结算关系，不生成内部结算单据
				throw new KDBizException("单据" + saleoutBill.getString("billno") + "无法进行内部结算：" + internalCheckResult.get("errorMsg"));
			}
			// 获取源单的物料价格
//			Map<String,BigDecimal> sourceMatPrice = getSourceOrderPrice(saleoutBill);
			Date bizDate = saleoutBill.getDate("biztime");
			// 重置单据的最终客户（结算组织的下一级）
			saleoutBill.set("customer", finalCustomer);    // 应收客户
			saleoutBill.set("yd_agencycustomer", finalCustomer);    // 经销商客户
			saleoutBill.set("yd_dzdkh", finalCustomer);        // 对账客户
			saleoutBill.set("yd_cuschannel", finalCustomer.getDynamicObject("yd_channels"));  // 客户所属渠道
			// 校验客户是否维护默认税率
			DynamicObject cusTaxRateObj = finalCustomer.getDynamicObject("taxrate");
			if (Objects.isNull(cusTaxRateObj)) {
				throw new KDBizException("客户[" + finalCustomer.getString("name") + "]默认税率为空");
			}
			// 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
			DynamicObjectCollection oriSaleEntryCol = saleoutBill.getDynamicObjectCollection("billentry");
			Map<String, BigDecimal> priceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(saleoutBill.getDynamicObject("bizorg").getString("id"),
					outOrgId, bizDate);
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
				cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
				oriSaleEntry.set("taxrateid", cusTaxRateObj);
				oriSaleEntry.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
				String materialId = oriSaleEntry.getString("material.masterid.id");
				DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
				// 保存一下源单的E3仓库
				oriSaleEntry.set("yd_e3oriwarehouse", oriSaleEntry.get("warehouse"));
				// 库存组织+物料+批次获取生产日期和到期日期
				String lot = oriSaleEntry.getString("yd_ph");
				String matNum = tempMat.getString("number");
				String wsNum = oriSaleEntry.getDynamicObject("warehouse").getString("number");
				oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, false);
				// 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）
				DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
				String isGeneralDate = "";
				// 重算金额
				if (priceMap.containsKey(materialId)) {
					BigDecimal newPrice = priceMap.get(materialId);
					oriSaleEntry.set("priceandtax", newPrice);
					changePriceAndTax(saleoutBill, oriSaleEntry);
					// update by hst 20230601 单价为0标记为赠品
					if (priceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
						oriSaleEntry.set("ispresent", true);
					} else {
						oriSaleEntry.set("ispresent", false);
					}
				}
				if (isGeneralDateObj.length > 0) {
					isGeneralDate = isGeneralDateObj[0].getString("name");
				}
				if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
						&& oriSaleEntry.get("yd_dqr") == null) {
					// 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期）
					oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, false);
				}
				// 重置最终的客户
				oriSaleEntry.set("settlecustomer", finalCustomer);  // 应收客户
				oriSaleEntry.set("payingcustomer", finalCustomer);  // 付款客户
				oriSaleEntry.set("reccustomer", finalCustomer);  // 收货客户
			}
			// update by hst 2023/04/27 标记源单为首笔结算单,重置时校验是否符合重置条件
			saleoutBill.set("yd_firstinternalbill", true);
			// 再次发起结算时，清空结算失败原因
			saleoutBill.set("yd_settleerror", "");
			// 对单据进行保存提交审核，拿到成功审核的单据ID
			String saleoutBillId = saleoutBill.getPkValue().toString();
			Map<String, String> opResult = operateTargetBill(saleoutBillId, "im_saloutbill", saleoutBill);
			saleoutBillId = opResult.get("billId");
			String oriBillId = "";
			String desBillId = "";
			// 先生成股份到结算组织
			for (String orgRelMap : orgRelList) {
				String[] keys = orgRelMap.split("&");
				String srcOrg = keys[0];
				DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
				String desOrg = keys[1];
				DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");
				Map<String, BigDecimal> matPriceMap;
				// 获取组织内部各个物料新的结算价格集合，根据单据业务日期获取对应日期范围内的价格
				matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);
				if (orgRelMap.equals(orgRelList.getFirst())) {
					// 根据源销售出库单创建销售出库单
					oriBillId = saleoutBillId;
					String oriBillEntity = "im_saloutbill";
					String desBillEntity = "im_saloutbill";
					String ruleId = "1553117535367359488";
					// 对单据进行保存提交审核，拿到成功审核的单据ID
					opResult = createOutBill(saleoutBillId, oriBillId, oriBillEntity, desBillEntity, ruleId, srcOrgObj, desOrgObj, matPriceMap, true, true);
					desBillId = opResult.get("billId");
					// 根据销售出库单创建采购入库单，到此就完结了
					oriBillId = desBillId;
					oriBillEntity = "im_saloutbill";
					desBillEntity = "im_purinbill";
					ruleId = "1553121315089573888";
					opResult = createOutBill(saleoutBillId, oriBillId, oriBillEntity, desBillEntity, ruleId, srcOrgObj, desOrgObj, matPriceMap, false, true);
					desBillId = opResult.get("billId");
					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
					oriBillId = desBillId;
				}
				// 不是第一组，创建销售出和采购入
				if (!orgRelMap.equals(orgRelList.getFirst())) {
					// 根据上一级的采购入库单创建销售出库单
					String oriBillEntity = "im_purinbill";
					String desBillEntity = "im_saloutbill";
					String ruleId = "1553122087932029952";
					opResult = createOutBill(saleoutBillId,oriBillId,oriBillEntity,desBillEntity,ruleId,srcOrgObj,desOrgObj,matPriceMap,false,true);
					desBillId = opResult.get("billId");
					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(oriBillId, "im_saloutbill", orgWarehouseMap);
					// 根据销售出库单创建采购入库单，到此就完结了
					oriBillId = desBillId;
					oriBillEntity = "im_saloutbill";
					desBillEntity = "im_purinbill";
					ruleId = "1553121315089573888";
					opResult = createOutBill(saleoutBillId,oriBillId,oriBillEntity,desBillEntity,ruleId,srcOrgObj,desOrgObj,matPriceMap,false,true);
					desBillId = opResult.get("billId");
					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
					oriBillId = desBillId;
				}
			}
			// 结算组织生成到末级
			for (String orgRelMap : orgEmptyList) {
				String[] keys = orgRelMap.split("&");
				String srcOrg = keys[0];
				DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
				String desOrg = keys[1];
				DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");
				// 获取组织内部各个物料新的结算价格集合，根据单据业务日期获取对应日期范围内的价格
				Map<String, BigDecimal> matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);;
				if (orgRelMap.equals(orgEmptyList.getFirst())) {
					// 根据销售出库单创建采购入库单，到此就完结了
					oriBillId = saleoutBillId;
					String oriBillEntity = "im_saloutbill";
					String desBillEntity = "im_purinbill";
					String ruleId = "1553121315089573888";
					opResult = createOutBill(saleoutBillId, oriBillId, oriBillEntity, desBillEntity, ruleId, srcOrgObj, desOrgObj, matPriceMap, false, true);
					desBillId = opResult.get("billId");
					// 重置源单上分录的仓库为组织对应的仓库
					resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
					oriBillId = desBillId;
				}
				// 不是第一组，创建销售出,且不是最后一组，创建采购入
				if (!orgRelMap.equals(orgEmptyList.getFirst())) {
					// 根据上一级的采购入库单创建销售出库单
					String oriBillEntity = "im_purinbill";
					String desBillEntity = "im_saloutbill";
					String ruleId = "1553122087932029952";
					opResult = createOutBill(saleoutBillId,oriBillId,oriBillEntity,desBillEntity,ruleId,srcOrgObj,desOrgObj,matPriceMap,false,true);
					desBillId = opResult.get("billId");
					if (!orgRelMap.equals(orgEmptyList.getLast())) {
						// 根据销售出库单创建采购入库单，到此就完结了
						oriBillId = desBillId;
						oriBillEntity = "im_saloutbill";
						desBillEntity = "im_purinbill";
						ruleId = "1553121315089573888";
						opResult = createOutBill(saleoutBillId, oriBillId, oriBillEntity, desBillEntity, ruleId, srcOrgObj, desOrgObj, matPriceMap, false, true);
						desBillId = opResult.get("billId");
						// 重置源单上分录的仓库为组织对应的仓库
						resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
						oriBillId = desBillId;
					}
				}
			}
			// 重置源单上分录的仓库为组织对应的仓库
			resetBillWarehouse(saleoutBillId, "im_saloutbill", orgWarehouseMap);
			// update by hst 2023/04/27 最后才标记源单为已生成内部结算单据标识
			DynamicObject saleout = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			saleout.set("yd_internalsettle", true);
			SaveServiceHelper.update(new DynamicObject[]{saleout});
		} catch (Exception e) {
			// 记录错误信息
			logger.error(e);
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 获取结算路径（虚体组织结算）
	 * @author: hst
	 * @createDate  : 2023/02/17
	 * @param bill 单据
	 * @return (path:结算路径)
	 */
	public static Map<String,Object> getVirtualSettlePath (DynamicObject bill) {
		Map<String, Object> map = new HashMap<>();
		// 销售组织
		DynamicObject bizOrg = bill.getDynamicObject("bizorg");
		// 销售组织ID
		String bizOrgId = bizOrg.getPkValue().toString();
		DataSet settleDataSet = null;
		settleDataSet = InternalSettleServiceHelper.getSettlePath(bill,"2");
		LinkedList<String> orgRelList = new LinkedList<String>();
		LinkedList<String> orgEmptyList = new LinkedList<String>();
		// 标记是否切换下一路径 (分层生成，先生成股份到结算组织，再由结算组织生成到末级）
		boolean isNext = false;
		// 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
		String outOrgId = "";
		for (Row settleRow : settleDataSet) {
			String curOrgId = settleRow.getString("orgid");
			// 存储结算组织的下一级，作为本单据的客户
			if (StringUtils.isNotBlank(outOrgId) && outOrgId.equals(bizOrgId)) {
				map.put("outOrgId",settleRow.getString("orgid"));
				DynamicObject customer = InternalSettleServiceHelper.getInternalCP("bd_customer", settleRow.getString("orgNum"));
				if (Objects.nonNull(customer)) {
					map.put("finalCustomer", customer);
				}
				isNext = true;
			}
			if (StringUtils.isNotEmpty(outOrgId)) {
				String mapValue = outOrgId + "&" + curOrgId;
				if (!isNext) {
					orgRelList.add(mapValue);
				} else {
					orgEmptyList.add(mapValue);
				}
			}
			outOrgId = curOrgId;
		}
		map.put("orgRelList", orgRelList);
		map.put("orgEmptyList", orgEmptyList);
		return map;
	}

	/**
	 * 获取源单的物料价格
	 * @author: hst
	 * @createDate: 2023/02/20
	 * @param bill
	 * @return
	 */
	public Map<String,BigDecimal> getSourceOrderPrice (DynamicObject bill) {
		Map<String,BigDecimal> sourceOrderPrice = new HashMap<>();
		bill.getDynamicObjectCollection("billentry").stream().forEach(entry -> {
			DynamicObject material = entry.getDynamicObject("material");
			if (Objects.nonNull(material)) {
				DynamicObject materid = material.getDynamicObject("masterid");
				if (Objects.nonNull(materid)) {
					sourceOrderPrice.put(materid.getPkValue().toString(),
							entry.getBigDecimal("priceandtax"));
				}
			}
		});
		return sourceOrderPrice;
	}
	
	/**
	 * 根据股份出库批次结果重置整条流程的出入库单的批次(以一个字段作为key)
	 * @createDate  : 2024/01/31
	 * @author: hst
	 * @updateDate  :
	 * @updateAuthor:
	 * @param lotBillIdSet 股份出库结果单ID集合
	 * @return 是否成功
	 */
	@Override
	public ApiResult resetSettleBillLotByKey(Set<String> lotBillIdSet) {

		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);

		// 校验是否为委外单据，是的话需要根据分录源E3仓库对仓库字段进行重置
		refreshWarehouse(lotBillIdSet);

		for (String lotBIllId : lotBillIdSet) {
			DynamicObject obj = BusinessDataServiceHelper.loadSingle(lotBIllId, "yd_saleoutlotbill");
			// 拿到股份出库的结果单，然后按物料+仓库记录批次+生产日期+到期日期+数量
			DynamicObjectCollection gfSaleEnCol = obj.getDynamicObjectCollection("entryentity");
			List<SaleOutEnLotVo> finalLotList = new ArrayList<>();
			for(int i=0;i<gfSaleEnCol.size();i++) {
				DynamicObject gfEnObj = gfSaleEnCol.get(i);
				SaleOutEnLotVo vo = new SaleOutEnLotVo();
				String matInvId = gfEnObj.getDynamicObject("yd_material").getString("id");
				vo.setMatId(matInvId);
				String wsId = gfEnObj.getDynamicObject("yd_warehouse").getString("id");
				vo.setWsId(wsId);
				vo.setLot(gfEnObj.getString("yd_lot"));
				vo.setBeginDate(gfEnObj.getDate("yd_begindate"));
				vo.setEndDate(gfEnObj.getDate("yd_enddate"));
				vo.setQty(gfEnObj.getBigDecimal("yd_qty"));
				vo.setSourceEntryId(gfEnObj.getString("yd_sourceentryid"));
				vo.setSeq(gfEnObj.getString("seq"));
				finalLotList.add(vo);
			}

			// 先重置末级出库单
			// 原来的单，按物料+仓库保留原分录行信息到内存中待用
			// 对原来的单进行拆分分录行，将第二步的信息都复制回去，价格重算等
			String gfSaleOutBillNum = obj.getString("yd_gfsaleoutbillno");
			QFilter gfFilter = new QFilter("billno", QCP.equals, gfSaleOutBillNum);
			DynamicObjectCollection gfBillCol = QueryServiceHelper.query("im_saloutbill", "id, yd_sourcebilltype", gfFilter.toArray());
			if (gfBillCol.size() > 0) {
				DynamicObject gfBillInfo = gfBillCol.get(0);
				String gfSaleOutBillId = gfBillInfo.getString("id");
				// 根据来源单据类型区分，如果类型为4，则为OCS库存调拨（PCP库存调拨），其它走原逻辑
				if (!StringUtils.equals(gfBillInfo.getString("yd_sourcebilltype"), "4")) {
					// 获取上下游单据，正向的是上游一张单，下游N张单，通过获取末级结算组织出库单的结算分录
					QFilter filter = new QFilter("yd_internalentity.yd_internalbillid", QCP.equals, gfSaleOutBillId);

					// update by hst 2024/06/17 适配其他出库单
					String entityName = "im_saloutbill";
					DynamicObjectCollection relCol = QueryServiceHelper.query(entityName, "id,billno", filter.toArray());
					if (Objects.isNull(relCol) || relCol.size() == 0) {
						entityName = "im_otheroutbill";
						relCol = QueryServiceHelper.query(entityName, "id,billno", filter.toArray());
					}
					if (relCol.size() > 0) {
						String srcBillId = relCol.get(0).getString("id");  // 末级出库单ID
						DynamicObject srcSaleoutBill = BusinessDataServiceHelper.loadSingle(srcBillId, entityName);
						resetBillEntries(entityName, srcBillId, finalLotList);

						DynamicObjectCollection internalCol = srcSaleoutBill.getDynamicObjectCollection("yd_internalentity");
						for (int i = 0; i < internalCol.size(); i++) {
							DynamicObject enInfo = internalCol.get(i);
							String billType = enInfo.getString("yd_internalbilltype");
							String billId = enInfo.getString("yd_internalbillid");
							resetBillEntries(billType, billId, finalLotList);
						}
					} else {
						// 单组织结算的，直接标记已重置批次
						DynamicObject singleObj = BusinessDataServiceHelper.loadSingle(gfSaleOutBillId, "im_saloutbill");
						singleObj.set("yd_beresetlot", true);  // 设置为已重置批次
						SaveServiceHelper.save(new DynamicObject[]{singleObj});
					}
				}else {
					// 重置OCS（PCP）库存调拨生成的出入库单重置批次等信息
					ocsTransBillResetLotByKey(gfSaleOutBillId, finalLotList);
				}
			}
		}


		return apiResult;
	}

	/** 根据key对原分录进行重置
	 * @param billType
	 * @param billId
	 * @param finalLotList
	 * @author: hst
	 * @createDate: 2024/01/31
	 */
	private void resetBillEntries(String billType, String billId, List<SaleOutEnLotVo> finalLotList) {
		String srcBillId = billId;  // 出库单ID
		DynamicObject srcSaleoutBill = BusinessDataServiceHelper.loadSingle(srcBillId, billType);
		// 处理末级组织的单据信息，拆分分录行
		Map<String, DynamicObject> srcEnMap = new HashMap<String, DynamicObject>();
		DynamicObjectCollection srcEnCol = srcSaleoutBill.getDynamicObjectCollection("billentry");
		// 先将分录以<id，value>存储起来
		for (int i = 0; i < srcEnCol.size(); i++) {
			DynamicObject enObj = srcEnCol.get(i);
			String key = enObj.getString("yd_sourceentryid");
			if (StringUtils.isNotBlank(key)) {
				srcEnMap.put(key, enObj);
			}
		}
		srcEnCol.clear();
		DynamicObjectType saleEnType = srcEnCol.getDynamicObjectType();
		Set<String> ignoreKey = new HashSet<String>();
		ignoreKey.add("id");
		for (SaleOutEnLotVo vo : finalLotList) {
			// 来源分录ID
			String sourceEntryId = vo.getSourceEntryId();
			if (StringUtils.isNotBlank(sourceEntryId)) {
				DynamicObject oriEnObj = srcEnMap.get(sourceEntryId);

				if (oriEnObj == null) {
					throw new KDBizException("重置单据" + srcSaleoutBill.getString("billno")
							+ "时，结果单第" + vo.getSeq() + "行分录批次匹配失败");
				}

				DynamicObject newEnObj = new DynamicObject(saleEnType);
				CommonUtils.copyDynObj(newEnObj, oriEnObj, ignoreKey);
				// 重新复制批次、数量，并重新计算
				newEnObj.set("yd_ph", vo.getLot());
				newEnObj.set("yd_scrq", vo.getBeginDate());
				newEnObj.set("yd_dqr", vo.getEndDate());
				newEnObj.set("qty", vo.getQty());
				newEnObj.set("baseqty", vo.getQty());
				if ("im_saloutbill".equalsIgnoreCase(billType)) {
					newEnObj.set("remainjoinpriceqty", vo.getQty());
					newEnObj.set("remainjoinpricebaseqty", vo.getQty());
					newEnObj.set("remainreturnqty", vo.getQty());
					newEnObj.set("remainreturnbaseqty", vo.getQty());
					newEnObj.set("unverifyqty", vo.getQty());
					newEnObj.set("unverifybaseqty", vo.getQty());
					srcSaleoutBill.set("yd_beresetlot", true);
				} else if ("im_purinbill".equalsIgnoreCase(billType)) {
					newEnObj.set("remainjoinpriceqty", vo.getQty());
					newEnObj.set("remainjoinpricebaseqty", vo.getQty());
					newEnObj.set("remainreturnqty", vo.getQty());
					newEnObj.set("remainreturnbaseqty", vo.getQty());
					newEnObj.set("unverifyqty", vo.getQty());
					newEnObj.set("unverifybaseqty", vo.getQty());
				} else if ("im_otheroutbill".equals(billType)) {
					srcSaleoutBill.set("yd_beresetlot", true);
				}

				if ("im_saloutbill".equals(billType) || "im_purinbill".equals(billType)) {
					changePriceAndTax(srcSaleoutBill, newEnObj);
				}
				srcEnCol.add(newEnObj);
			} else {
				throw new KDBizException("重置单据" + srcSaleoutBill.getString("billno")
						+ "时，结果单第" + vo.getSeq() + "行分录批次匹配失败");
			}
		}

		SaveServiceHelper.save(new DynamicObject[]{srcSaleoutBill});
	}

	/**
	 * OCS库存调拨单重置批次(通过关键字）
	 * @param saleId 首张股份的销售出库单ID
	 * @param finalLotList 需重置的批次集合
	 * <AUTHOR>
	 * @createDate: 2024/01/31
	 */
	private void ocsTransBillResetLotByKey(String saleId, List<SaleOutEnLotVo> finalLotList) {
		DynamicObject info = BizHelper.getDynamicObjectById("im_saloutbill", saleId, "yd_internalentity,yd_internalentity.yd_internalbilltype,yd_internalentity.yd_internalbillid");
		DynamicObjectCollection internalCol = info.getDynamicObjectCollection("yd_internalentity");
		// 重置批次的集合
		// 包含销售出库单 / 采购入库单，分别对应的单据ID和单据类型
		Map<String, String> resetLotMap = new LinkedHashMap<>();
		resetLotMap.put(saleId, "im_saloutbill");
		for (DynamicObject internalInfo : internalCol) {
			resetLotMap.put(internalInfo.getString("yd_internalbillid"), internalInfo.getString("yd_internalbilltype"));
		}
		// 遍历需要执行的单号，更新批次号、生产日期、到期日期
		for (String billId : resetLotMap.keySet()) {
			resetBillEntries(resetLotMap.get(billId), billId, finalLotList);
		}
	}
}
