package kd.bos.tcbj.im.outbill.imp;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.outbill.mservice.SettleMservice;
import kd.bos.tcbj.im.util.SettleUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class SettleMserviceImpl implements SettleMservice {

    /* 内部实例化*/
    private final static SettleMserviceImpl settleMserviceImpl = new SettleMserviceImpl();
    /* 日志 */
    private static Log logger = LogFactory.getLog(SaleOutBillMserviceImpl.class.getName());
    /* 日期格式化字符串 */
    private final static SimpleDateFormat DATE_SDF = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 内部接口实现类实例
     *
     * @return
     * @createDate :
     * @createAuthor: hst
     * @updateDate :
     * @updateAuthor:
     */
    public static SettleMservice getInstance() {
        return settleMserviceImpl;
    }

    /**
     * 销售出库一盘货结算
     *
     * @param saleoutBill 单据
     * @param isForward   是否是正向
     * @return 是否成功
     * @author: hst
     * @createDate :
     */
    @Override
    public ApiResult createSaleOutSettleBill(DynamicObject saleoutBill, DynamicObject config, Boolean isForward) {
        ApiResult apiResult = new ApiResult();
        apiResult.setSuccess(true);
        try {
            Map<String, String> orgWarehouseMap = getOrgWarehouseMap();
            // 最终的销售收货的客户
            DynamicObject finalCustomer = saleoutBill.getDynamicObject("customer");
            // 业务日期
            Date bizDate = saleoutBill.getDate("biztime");
            // 校验客户基础资料里面的税率是否为空
            if (Objects.isNull(finalCustomer.get("taxrate"))) {
                throw new KDBizException("客户[" + finalCustomer.getString("name") + "]默认税率为空");
            }
            // 通过配置信息获取结算路径结算路径
            Map<String, Object> settlePath = getSettlePath(saleoutBill, config);
            LinkedList<String> orgReturnlList = (LinkedList<String>) settlePath.get("orgReturnlList");
            LinkedList<String> orgSettleList = (LinkedList<String>) settlePath.get("orgSettleList");
            // 校验是否配置对应的内部结算关系，没有则不生成结算单据
            Map<String, String> internalCheckResult = checkSettleMsg(saleoutBill.getPkValue().toString(), config);
            if (StringUtils.isEmpty(internalCheckResult.get("billId"))) {
                throw new KDBizException("单据" + saleoutBill.getString("billno") + "无法进行结算：" + internalCheckResult.get("errorMsg"));
            }
            // 根据配置信息判断是否需要重置单据的组织
            DynamicObject lastOrg = saleoutBill.getDynamicObject("org");
            if (config.getBoolean("yd_resetorg")) {
                String outOrgId = settlePath.get("outOrgId").toString();
                lastOrg = BusinessDataServiceHelper.loadSingle(outOrgId, "bos_org");
            }
            saleoutBill.set("yd_oriorg", saleoutBill.get("org"));
            saleoutBill.set("org", lastOrg);
            saleoutBill.set("bizorg", lastOrg);
            saleoutBill.set("bizdept", lastOrg);
            saleoutBill.set("dept", lastOrg);
            saleoutBill.set("yd_settleorg", lastOrg);
            // 存储原中间表的结算组织，用于重置结算时恢复,方便重置结算后再次找到正确的结算路径
            saleoutBill.set("yd_orisettleorg", saleoutBill.get("yd_settleorg"));
            // 重置最终的客户
            saleoutBill.set("customer", finalCustomer);
            saleoutBill.set("yd_agencycustomer", finalCustomer);
            // 客户所属渠道
            saleoutBill.set("yd_cuschannel", finalCustomer.getDynamicObject("yd_channel"));
            // 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
            resetUnitPriceOfSourceBill(saleoutBill);
            // 如果只有一级结算组织，就在股份，则不会有下游单据，标记这种进行单独集成流程传递
            if (orgSettleList.size() == 0) {
                saleoutBill.set("yd_singleSettle", true);
            }
            // 再次发起结算时，清空结算失败原因
            saleoutBill.set("yd_settleerror", "");
            // 标记源单为已生成内部结算单据标识
            saleoutBill.set("yd_internalsettle", true);
            // 对单据进行保存提交审核，拿到成功审核的单据ID
            String saleoutBillId = saleoutBill.getPkValue().toString();
            Map<String, String> opResult = operateTargetBill(saleoutBillId, BillTypeHelper.BILLTYPE_SALEOUTBILL,
                    BillTypeHelper.BILLTYPE_SALEOUTBILL, saleoutBill);
            saleoutBillId = opResult.get("billId");
            // 如果是第一组则重置多少倍退货数量(退货单据类型时)，标记当前单据为首单
            if (!isForward) {
                DynamicObject backSaleoutBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
                DynamicObjectCollection backSaleEntryCol = backSaleoutBill.getDynamicObjectCollection("billentry");
                for (DynamicObject entry : backSaleEntryCol) {
                    entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(new BigDecimal(orgSettleList.size())));
                    entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(new BigDecimal(orgSettleList.size())));
                }
                backSaleoutBill.set("yd_firstinternalbill", true);
                SaveServiceHelper.save(new DynamicObject[]{backSaleoutBill});// 保存
            }
            // 生成退货组织到库存组织的出入库单
            String oriBillId = createReturnOrgToStockOrg(saleoutBillId, BillTypeHelper.BILLTYPE_SALEOUTBILL,
                    orgReturnlList, orgSettleList, bizDate,orgWarehouseMap,config);
            // 生成库存组织到结算组织的出入库单
            createStockOrgToSettleLevelOrg(saleoutBillId, BillTypeHelper.BILLTYPE_SALEOUTBILL, oriBillId,
                    orgSettleList,bizDate,orgWarehouseMap,config,isForward,false);
            // 重置源单上分录的仓库为组织对应的仓库
            SettleUtils.resetBillWarehouse(saleoutBillId, "im_saloutbill", orgWarehouseMap);
            // 最后才标记源单为已生成内部结算单据标识
            DynamicObject saleout = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
            saleout.set("yd_internalsettle", true);
            SaveServiceHelper.update(new DynamicObject[]{saleout});

            // 使用关联关系将生成的下游单据保存到源销售出库单上
            InternalSettleServiceHelper.saveVirtualBotpBills(saleoutBill.getString("id"));
        } catch (Exception e) {
            // 记录错误信息
            logger.error(e);
            apiResult.setSuccess(false);
            apiResult.setMessage(e.getLocalizedMessage());
        }
        return apiResult;
    }

    /**
     * 获取结算路径
     *
     * @param bill   单据
     * @param config 配置信息
     * @return (path : 结算路径)
     * @author: hst
     * @createDate :
     */
    private static Map<String, Object> getSettlePath(DynamicObject bill, DynamicObject config) {
        Map<String, Object> map = new HashMap<>();
        // 销售组织
        DynamicObject bizOrg = bill.getDynamicObject("bizorg");
        // 销售组织ID
        String bizOrgId = bizOrg.getPkValue().toString();
        // 是否正向
        String biztype = bill.getString("biztype.number");
        boolean isForward = "210".equals(biztype) || "355".equals(biztype)? true : false;
        DataSet settleDataSet = null;
        LinkedList<String> orgReturnlList = new LinkedList<String>();
        // 通过配置信息获取退货路径
        settleDataSet = getReturnPathByConfig(bill, config);
        // 退货结算
        String outOrgId = "";
        if (Objects.nonNull(settleDataSet)) {
            for (Row settleRow : settleDataSet) {
                String curOrgId = settleRow.getString("orgid");
                if (StringUtils.isNotEmpty(outOrgId)) {
                    String mapValue = outOrgId + "&" + curOrgId;
                    orgReturnlList.add(mapValue);
                }
                outOrgId = curOrgId;
            }
        }
        map.put("orgReturnlList", orgReturnlList);
        // 通过配置信息获取结算路径
        settleDataSet = getSettlePathByConfig(bill, config);
        LinkedList<String> orgSettleList = new LinkedList<String>();
        // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
        // 正常结算
        outOrgId = "";
        for (Row settleRow : settleDataSet) {
            String curOrgId = settleRow.getString("orgid");
            // 存储当前单据组织的下一级，作为本单据的客户
            if (StringUtils.isNotBlank(outOrgId) && outOrgId.equals(bizOrgId)) {
                map.put("outOrgId", settleRow.getString("orgid"));
                DynamicObject customer = InternalSettleServiceHelper.getInternalCP("bd_customer", settleRow.getString("orgNum"));
                if (Objects.nonNull(customer)) {
                    map.put("finalCustomer", customer);
                }
            }
            if (StringUtils.isNotEmpty(outOrgId)) {
                String mapValue = outOrgId + "&" + curOrgId;
                orgSettleList.add(mapValue);
            }
            outOrgId = curOrgId;
            if (isForward) {
                map.put("outOrgId", outOrgId);
            }
            if (!isForward && map.size() == 0) {
                map.put("outOrgId", outOrgId);
            }
        }
        map.put("orgSettleList", orgSettleList);
        return map;
    }

    /**
     * 通过配置信息调用方法
     * @param param
     * @param className
     * @param methodName
     * @return
     */
    public static Object invokeMethodByConfig(Object param, String className, String methodName) {
        Object data = null;
        try {
            if (StringUtils.isNotBlank(className) && StringUtils.isNotBlank(methodName)) {
                Class pathClass = Class.forName(className);
                Method method = pathClass.getMethod(methodName, new Class[]{param.getClass()});
                data = method.invoke(pathClass, new Object[]{param});
            }
        } catch (ClassNotFoundException e) {
            throw new KDBizException("获取不到" + className + "对应类");
        } catch (NoSuchMethodException e) {
            throw new KDBizException("获取不到" + methodName + "对应方法");
        } catch (InvocationTargetException e) {
            throw new KDBizException(e.getTargetException().getMessage());
        } catch (IllegalAccessException e) {
            throw new KDBizException(e.getMessage());
        }
        return data;
    }

    /**
     * 根据配置信息获取结算路径
     *
     * @param bill
     * @param config
     * @return
     */
    private static DataSet getSettlePathByConfig(DynamicObject bill, DynamicObject config) {
        DataSet settleDataSet = null;
        Object data = invokeMethodByConfig(bill, config.getString("yd_classname"),config.getString("yd_method"));
        if (Objects.nonNull(data)) {
            settleDataSet = (DataSet) data;
        }
        return settleDataSet;
    }

    /**
     * 根据配置信息获取结算路径
     *
     * @param bill
     * @param config
     * @return
     */
    private static DataSet getReturnPathByConfig(DynamicObject bill, DynamicObject config) {
        DataSet settleDataSet = null;
        Object data = invokeMethodByConfig(bill, config.getString("yd_classname"),config.getString("yd_return_method"));
        if (Objects.nonNull(data)) {
            settleDataSet = (DataSet) data;
        }
        return settleDataSet;
    }

    /**
     * 获取结算路径
     *
     * @param bill
     * @return
     * @author: hst
     * @createDate: 2023/02/21
     */
    public static DataSet getVirtualSettlePath(DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        DynamicObject settleOrg = bill.getDynamicObject("yd_settleorg");
        DynamicObject biztype = bill.getDynamicObject("biztype");
        boolean isForward = "210".equals(biztype.getString("number")) ? true : false;
        if (Objects.isNull(settleOrg)) {
            throw new KDBizException("获取不到结算组织信息，无法生成PCP结算流程单据！");
        }
        // 过滤条件
        qFilters.add(new QFilter("yd_settleorg.id", QCP.equals, settleOrg.getPkValue()));
        qFilters.add(new QFilter("status", QCP.equals, "C"));
        // 排序字段
        String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
        // 错误提示
        String tip = "";
        // 按结算组织查找路径
        tip = "当前" + settleOrg.getString("name") + "没有配置内部结算关系，无法生成内部结算流程单据！";
        DataSet settleDataSet = null;

        // 虚体组织结算链路 (组织 + 客户)
        // 客户
        DynamicObject customer = bill.getDynamicObject("customer");
        if (Objects.nonNull(customer)) {
            qFilters.add(new QFilter("yd_customer.id", QCP.equals, customer.getPkValue()));
            settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_virtualbodytissue",
                    "yd_settleorg.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
                            "yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName", qFilters.toArray(new QFilter[qFilters.size()]), orderby);
        }


        if (Objects.isNull(settleDataSet) || !settleDataSet.hasNext()) {
            // 品牌没有配置对应的内部结算关系，不生成内部结算单据
            throw new KDBizException(tip);
        }
        return settleDataSet;
    }

    /**
     * 校验单据末级销售出库单对应的内部交易关系是否有已维护
     *
     * @param saleoutBillId 末级销售出库单
     * @param config
     * @return billId为空表示失败，errorMsg表示失败原因
     * @author: hst
     * @createDate:
     */
    public static Map<String, String> checkSettleMsg(String saleoutBillId, DynamicObject config) {
        Map<String, String> result = new HashMap<String, String>();
        // 源末级销售出库单
        DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
        DataSet settleDataSet = null;
        try {
            StringBuilder errMsg = new StringBuilder();
            // 通过配置信息获取结算路径
            settleDataSet = getSettlePathByConfig(saleoutBillObj, config);
            LinkedList<String> orgList = new LinkedList<String>();
            // 校验结算路径上的组织与仓库映射表
            errMsg.append(checkOrganizationWarehouse(settleDataSet.copy(), orgList));
            // 通过配置信息获取退货路径
            settleDataSet = getReturnPathByConfig(saleoutBillObj, config);
            LinkedList<String> returnOrgList = new LinkedList<String>();
            // 校验退货结算路径上的组织与仓库映射表
            if (Objects.nonNull(settleDataSet)) {
                errMsg.append(checkOrganizationWarehouse(settleDataSet.copy(), returnOrgList));
            }
            // 校验生产日期
            errMsg.append(checkGenerationDate(saleoutBillObj));
            // 校验退货结算路径上的内部商客
            errMsg.append(checkSupplyAndCustomer(saleoutBillObj, returnOrgList,false));
            // 校验结算路径上的内部商客
            errMsg.append(checkSupplyAndCustomer(saleoutBillObj, orgList,null));
            // 自定义校验（通过配置信息中的类名及方法名自定义添加校验）
            Object custom = invokeMethodByConfig(saleoutBillId,config.getString("yd_cusclass"),
                    config.getString("yd_cusmethod"));
            if (Objects.nonNull(custom)) {
                errMsg.append(custom);
            }
            if (errMsg.length() > 0) {
                result.put("billId", "");
                result.put("errorMsg", errMsg.toString());
            } else {
                result.put("billId", saleoutBillId);
                result.put("errorMsg", "");
            }
        } finally {
            if (Objects.nonNull(settleDataSet)) {
                settleDataSet.close();
            }
        }
        return result;
    }

    /**
     * 校验组织与仓库映射表
     *
     * @param settleDataSet 结算路径
     * @return
     * @author: hst
     * @createDate: 2022/12/14
     */
    private static String checkOrganizationWarehouse(DataSet settleDataSet, LinkedList<String> orgList) {
        String outOrgId = "";  // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
        // 校验组织与仓库映射表
        Map<String, String> orgWarehouseMap = getOrgWarehouseMap();
        String warehouseError = "以下销售出库组织缺少默认仓库映射：";
        StringBuffer warehouseErrorMsg = new StringBuffer();
        if (Objects.nonNull(settleDataSet)) {
            for (Row settleRow : settleDataSet) {
                String curOrgId = settleRow.getString("orgid");
                if (StringUtils.isNotEmpty(outOrgId)) {
                    String mapValue = outOrgId + "&" + curOrgId;
                    orgList.add(mapValue);
                }
                outOrgId = curOrgId;
                if (!orgWarehouseMap.containsKey(curOrgId)) {
                    warehouseErrorMsg.append(settleRow.getString("orgNum")+settleRow.getString("orgName")+"，");
                }
            }
            if (warehouseErrorMsg.length() > 0) {
                warehouseErrorMsg.insert(0, warehouseError);
                warehouseErrorMsg.deleteCharAt(warehouseErrorMsg.length() - 1).append("；");
            }
            return warehouseErrorMsg.toString();
        }
        return "";
    }

    /**
     * 校验生产日期
     *
     * @param bill
     * @return
     * @author: hst
     * @createDate: 2022/12/14
     */
    private static String checkGenerationDate(DynamicObject bill) {
        StringBuffer lotError = new StringBuffer();
        // 如果参数为true表示校验生产日期，为false表示不校验
        DynamicObject biztype = bill.getDynamicObject("biztype");
        boolean isControlLot = true; // 默认校验
        boolean isGeneralDate = true; // 默认为true，自动带日期即不要校验日期，退货类型的如果开通了此参数则不用校验批次
        // 从配置表中查找对应的值
        String isControlLotStr = BizHelper.getQueryOne("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isControlLot").toArray());
        String isGeneralDateStr = BizHelper.getQueryOne("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
        if (StringUtils.isNotBlank(isControlLotStr)) {
            isControlLot = StringUtils.equalsIgnoreCase("true", isControlLotStr);
        }
        if (StringUtils.isNotBlank(isGeneralDateStr)) {
            isGeneralDate = StringUtils.equalsIgnoreCase("true", isGeneralDateStr);
        }
        Boolean isReturn = "2101".equals(biztype.getString("number"));  // 是否退货
        DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("billentry");
        if (isControlLot) {
            for (DynamicObject entryObj : entryCol) {
                // 校验是否存在对应的批次数据
                String lot = entryObj.getString("yd_ph");
                String materialNum = entryObj.getString("material.masterid.number");
                String wsNum = entryObj.getDynamicObject("warehouse").getString("number");
                Date beginDate = entryObj.getDate("yd_scrq");
                Date endDate = entryObj.getDate("yd_dqr");
                if (wsNum == null) continue; // 如果不存在，只有委外中查找映射中不存在，这种情况异常已经记录，无须再次查找
                // 如果生产的日期为空，都需要从即时库存中查找
                if (beginDate == null || endDate == null) {
                    // 按批次+物料+仓库，如果没有查找到，正向执行提示异常，如果是红字，则需要再次查找按物料+仓库查找，没有则提示异常
                    boolean isHasLot = hasExpDate(lot, materialNum, wsNum, isReturn);
                    if (!isHasLot) {
                        if (isGeneralDate) {
                            boolean isHasRefundLot = hasLastExpDate(materialNum, wsNum, isReturn);
                            if (!isHasRefundLot) {
                                if (lotError.indexOf(materialNum + "的批次" + lot) < 0)
                                    lotError.append("仓库编码" + wsNum + "的" + materialNum + "的批次" + lot + "，");
                            }
                        } else {
                            if (lotError.indexOf(materialNum + "的批次" + lot) < 0)
                                lotError.append("仓库编码" + wsNum + "的" + materialNum + "的批次" + lot + "，");
                        }
                    }
                }
            }
            if (lotError.length() > 0)
                lotError.insert(0, "以下物料对应的批次不存在即时库存数据：").deleteCharAt(lotError.length() - 1).append("；");
            // 添加的异常信息中
        }
        return lotError.toString();
    }

    /**
     * 校验内部商客
     *
     * @param bill
     * @param orgRelList
     * @return
     * @author: hst
     * @createDate: 2022/12/14
     */
    private static String checkSupplyAndCustomer(DynamicObject bill, LinkedList<String> orgRelList, Boolean forward) {
        // 校验组织是否维护内部客商，一级组织校验供应商，二级组织校验客户和供应商，末级组织校验客户
        String biztype = bill.getString("biztype.number");
        boolean isForward = Objects.nonNull(forward) ? forward.booleanValue() :
                "210".equals(biztype) || "355".equals(biztype) ? true : false;
        String internalOrgError = "以下组织缺少设置对应的内部客商信息：";
        StringBuffer internalOrgErrorMsg = new StringBuffer();
        StringBuffer ErrorMsg = new StringBuffer();
        // 校验是否有维护组织间价格
        for (String orgRelMap : orgRelList) {
            String[] keys = orgRelMap.split("&");
            String srcOrg = isForward ? keys[0] : keys[1];
            DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
            String srcOrgName = srcOrgObj.getString("name");
            String desOrg = isForward ? keys[1] : keys[0];
            DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");
            String desOrgName = desOrgObj.getString("name");

            String orgPriceError = "销售出库组织（" + srcOrgName + "）与采购入库组织（" + desOrgName + "）缺失以下物料的价格：";
            StringBuffer matPriceError = new StringBuffer();

            // 获取组织内部各个物料新的结算价格集合
            Date bizDate = bill.getDate("biztime");
            Map<String, BigDecimal> matPriceMap = getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);
            DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("billentry");
            for (DynamicObject entryObj : entryCol) {
                String materialId = entryObj.getString("material.masterid.id");
                String materialNum = entryObj.getString("material.masterid.number");
                // 校验客户交易价格
                if (!matPriceMap.containsKey(materialId) && matPriceError.indexOf(materialNum) < 0) {
                    matPriceError.append(materialNum + "，");
                }
            }


            if (matPriceError.length() > 0) {
                matPriceError.insert(0, orgPriceError);
                matPriceError.deleteCharAt(matPriceError.length() - 1).append("；");
                ErrorMsg.append(matPriceError);
            }
            // 重复提示错误信息，需要清空
            internalOrgErrorMsg = new StringBuffer();
            // 根据编码获取后者组织作为客户
            // 校验结算路径上客户默认税率是否为空
            DynamicObject customer = InternalSettleServiceHelper.getInternalCP("bd_customer", desOrgObj.getString("number"));
            if (Objects.isNull(customer)) {
                internalOrgErrorMsg.append(desOrgObj.getString("number") + desOrgObj.getString("name") + "缺少对应客户，");
            } else {
                DynamicObject taxRate = customer.getDynamicObject("taxrate");
                if (Objects.isNull(taxRate)) {
                    internalOrgErrorMsg.append(desOrgObj.getString("number") + desOrgObj.getString("name") + "对应客户默认税率为空，");
                }
            }
            DynamicObject supplier = InternalSettleServiceHelper.getInternalCP("bd_supplier", srcOrgObj.getString("number"));
            if (Objects.isNull(supplier)) {
                internalOrgErrorMsg.append(srcOrgObj.getString("number") + srcOrgObj.getString("name") + "缺少对应供应商，");
            }
            if (internalOrgErrorMsg.length() > 0) {
                internalOrgErrorMsg.insert(0, internalOrgError);
                internalOrgErrorMsg.deleteCharAt(internalOrgErrorMsg.length() - 1).append("；");
                ErrorMsg.append(internalOrgErrorMsg);
            }
        }
        return ErrorMsg.toString();
    }

    /**
     * 获取组织与仓库的默认对应关系
     *
     * @author: hst
     * @createDate:
     */
    private static Map<String, String> getOrgWarehouseMap() {
        Map<String, String> orgWarehouseMap = new HashMap<String, String>();
        QFilter wsFilter = new QFilter("billstatus", QCP.equals, "C");
        DataSet orgWsSet = QueryServiceHelper.queryDataSet("ws", "yd_orgwarehouse",
                "entryentity.yd_org.id orgId,entryentity.yd_warehouse.id wsId", wsFilter.toArray(), null);
        for (Row row : orgWsSet) {
            orgWarehouseMap.put(row.getString("orgId"), row.getString("wsId"));
        }
        return orgWarehouseMap;
    }

    /**
     * 设置批次对应的生产日期和到期日
     *
     * @param lot
     * @param matNum
     * @param wsNum
     * @return
     */
    private static Boolean hasExpDate(String lot, String matNum, String wsNum, Boolean isReturn) {
        QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
        filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
        filter.and(new QFilter("yd_flot", QCP.equals, lot));
        // 如果是正向的需要查数量大于0的批次
        if (!isReturn) {
            filter.and(new QFilter("yd_qty", QCP.large_than, 0));
        }
        DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
                "yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
        if (dataSet.hasNext()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 设置物料+仓库获取对应最新的批次
     *
     * @param matNum
     * @param wsNum
     * @return
     */
    private static Boolean hasLastExpDate(String matNum, String wsNum, Boolean isReturn) {
        QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
        filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
        // 如果是正向的需要查数量大于0的批次
        if (!isReturn) {
            filter.and(new QFilter("yd_qty", QCP.large_than, 0));
        }
        DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
                "yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
        if (dataSet.hasNext()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据调出调入组织获取各个物料最新的价格
     *
     * @param srcOrg
     * @param desOrg
     * @param bizDate 出库单业务日期，作为过滤条件查对应日期范围内的价格，yzw20220419
     */
    private static Map<String, BigDecimal> getOrgtoOrgNewPrice(String srcOrg, String desOrg, Date bizDate) {
        Map<String, BigDecimal> matNewPriceMap = new HashMap<String, BigDecimal>();
        QFilter priceFilter = new QFilter("yd_outorg.id", QCP.equals, srcOrg);
        priceFilter.and(new QFilter("yd_inorg.id", QCP.equals, desOrg));
        priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
        String bizDateStr = DATE_SDF.format(bizDate);
        try {
            bizDate = DATE_SDF.parse(bizDateStr);
        } catch (ParseException e) {
            System.out.println(e.getLocalizedMessage());
        }
        priceFilter.and(new QFilter("entryentity.yd_begindate", QCP.less_equals, bizDate));  // 开始日期小于等于业务日期
        priceFilter.and(new QFilter("entryentity.yd_enddate", QCP.large_equals, bizDate));  // 结束日期大于等于业务日期
        DataSet priceSet = QueryServiceHelper.queryDataSet("price", "yd_pricerelbill",
                "entryentity.yd_materiel.id matId,entryentity.yd_price newprice", priceFilter.toArray(), null);
        for (Row row : priceSet) {
            matNewPriceMap.put(row.getString("matId"), row.getBigDecimal("newprice"));
        }
        return matNewPriceMap;
    }

    /**
     * 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
     *
     * @param bill
     * @author: hst
     * @createDate:
     */
    private static void resetUnitPriceOfSourceBill(DynamicObject bill) {
        // 正向/逆向
        DynamicObject biztype = bill.getDynamicObject("biztype");
        boolean isForward = "210".equals(biztype.getString("number")) ? true : false;
        // 客户
        DynamicObject customer = bill.getDynamicObject("customer");
        // 根据客户获取税率
        DynamicObject cusTaxRateObj = customer.getDynamicObject("taxrate");
        // 库存组织
        DynamicObject org = bill.getDynamicObject("org");
        // 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
        DynamicObjectCollection oriSaleEntryCol = bill.getDynamicObjectCollection("billentry");
        for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
            cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
            oriSaleEntry.set("taxrateid", cusTaxRateObj);
            oriSaleEntry.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
            // 根据销售关系yd_orgcuspricebill获取物料价格，再重算金额
            String materialId = oriSaleEntry.getString("material.masterid.id");
            DynamicObject tempMat = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
            // 重置单据的组织
            oriSaleEntry.set("entrysettleorg", org);  // 结算组织
            oriSaleEntry.set("outowner", org);  // 出库货主
            oriSaleEntry.set("outkeeper", org);  // 出库保管者
            // 保存一下源单的E3仓库
            oriSaleEntry.set("yd_e3oriwarehouse", oriSaleEntry.get("warehouse"));
            // 库存组织+物料+批次获取生产日期和到期日期
            String lot = oriSaleEntry.getString("yd_ph");
            String matNum = tempMat.getString("number");
            String wsNum = oriSaleEntry.getDynamicObject("warehouse").getString("number");
            if (isForward) {
                oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, false);
            } else {
                oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, true);
            }
            // 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）
            DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
            String isGeneralDate = "";
            if (isGeneralDateObj.length > 0) {
                isGeneralDate = isGeneralDateObj[0].getString("name");
            }
            if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
                    && oriSaleEntry.get("yd_dqr") == null) {
                // 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期）
                if (isForward) {
                    oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, false);
                } else {
                    oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, true);
                }
            }
            // 重置最终的客户
            oriSaleEntry.set("settlecustomer", customer);  // 应收客户
            oriSaleEntry.set("payingcustomer", customer);  // 付款客户
            oriSaleEntry.set("reccustomer", customer);  // 收货客户
            // update by hst 2024/01/31 携带源id
            oriSaleEntry.set("yd_sourceentryid",oriSaleEntry.getString("id"));
        }
    }

    /**
     * 设置批次对应的生产日期和到期日
     *
     * @param lot
     * @param matNum
     * @param wsNum
     * @param entry
     * @param isReturn 是否退货业务，退货业务取最新批次，销售业务取最旧并且有库存的批次
     * @return
     */
    private static DynamicObject setExpDate(String lot, String matNum, String wsNum, DynamicObject entry, Boolean isReturn) {
        QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
        filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
        filter.and(new QFilter("yd_flot", QCP.equals, lot));
        // 如果是正向的需要查数量大于0、并且有库存的批次
        String sort = "yd_mfgdate desc";
        if (!isReturn) {
            filter.and(new QFilter("yd_qty", QCP.large_than, 0));
            sort = "yd_mfgdate asc";
        }
        DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
                "yd_mfgdate,yd_expdate", filter.toArray(), sort);
        if (dataSet.hasNext()) {
            Row tempRow = dataSet.next();
            entry.set("yd_scrq", tempRow.getDate("yd_mfgdate"));  // 生产日期
            entry.set("yd_dqr", tempRow.getDate("yd_expdate"));  // 到期日
        }
        return entry;
    }

    /**
     * 设置物料+仓库获取最新的批次及日期
     *
     * @param matNum   物料编码
     * @param wsNum    仓库编码
     * @param entry    被处理的分录
     * @param isReturn 是否退货业务，退货业务取最新批次，销售业务取最旧并且有库存的批次
     * @return
     */
    private static DynamicObject setLastExpDate(String matNum, String wsNum, DynamicObject entry, Boolean isReturn) {
        QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
        filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
        // 如果是正向的需要查数量大于0、并且有库存的批次
        String sort = "yd_mfgdate desc";
        if (!isReturn) {
            filter.and(new QFilter("yd_qty", QCP.large_than, 0));
            sort = "yd_mfgdate asc";
        }
        DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
                "yd_flot,yd_mfgdate,yd_expdate", filter.toArray(), sort);
        if (dataSet.hasNext()) {
            Row tempRow = dataSet.next();
            entry.set("yd_ph", tempRow.getString("yd_flot"));  // 生产日期
            entry.set("yd_scrq", tempRow.getDate("yd_mfgdate"));  // 生产日期
            entry.set("yd_dqr", tempRow.getDate("yd_expdate"));  // 到期日
        }
        return entry;
    }

    /**
     * 保存、提交、审核单据
     *
     * @param oriBillId
     * @param desBillEntity
     * @param targetBillObj
     * @author: hst
     * @createDate: 2022/10/28
     */
    public static Map<String, String> operateTargetBill(String oriBillId, String oriEntityName, String desBillEntity, DynamicObject targetBillObj) {
        Map<String, String> opResult = SettleUtils.operateTargetBillOp(desBillEntity, targetBillObj);
        if (StringUtils.isEmpty(opResult.get("billId"))) {
            // 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
            String error = opResult.get("errorMsg");
            errorSettleBill(oriBillId, oriEntityName, error);
            throw new KDBizException(error);
        }
        return opResult;
    }

    /**
     * 将错误信息记录到源销售出库单上yd_settleerror
     *
     * @param saleoutBillId
     * @param billEntity
     * @param errorMsg
     * @author: hst
     * @createDate:
     */
    private static void errorSettleBill(String saleoutBillId, String billEntity, String errorMsg) {
        DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, billEntity);
        resetBill.set("yd_settleerror", errorMsg);
        SaveServiceHelper.save(new DynamicObject[]{resetBill});// 保存
    }

    /**
     * 生成退货组织到库存组织的出入库单
     *
     * @param saleoutBillId
     * @param orgReturnlList
     * @param orgSettleList
     * @param bizDate
     * @param config
     * @param orgWarehouseMap
     * @author: hst
     * @createDate:
     */
    private static String createReturnOrgToStockOrg(String saleoutBillId, String oriEntityName, LinkedList<String> orgReturnlList, LinkedList<String> orgSettleList,
                                            Date bizDate, Map<String, String> orgWarehouseMap, DynamicObject config) {
        String oriBillId = "";
        String desBillId = "";
        Map<String, String> opResult = new HashMap<>();
        for (String orgRelMap : orgReturnlList) {
            String[] keys = orgRelMap.split("&");
            String srcOrg = keys[0];
            DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
            String desOrg = keys[1];
            DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");
            Map<String, BigDecimal> matPriceMap;
            // 获取组织内部各个物料新的结算价格集合，根据单据业务日期获取对应日期范围内的价格
            matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(desOrg, srcOrg, bizDate);
            if (orgRelMap.equals(orgReturnlList.getFirst())) {
                // 根据源销售出库单创建采购入库单（红字）
                oriBillId = saleoutBillId;
                String oriBillEntity = "im_saloutbill";
                String desBillEntity = "im_purinbill";
                String ruleId = config.getString("yd_oriouttoin_re");
                // 对单据进行保存提交审核，拿到成功审核的单据ID
                opResult = createOutBill(saleoutBillId, oriEntityName, oriBillId, oriBillEntity,
                        desBillEntity, ruleId, srcOrgObj, desOrgObj, matPriceMap, true, false);
                desBillId = opResult.get("billId");
                // 第一组则重置多少倍退货数量
                DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_purinbill");
                DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                for(DynamicObject entry : entryCol) {
                    entry.set("returnqty", entry.getBigDecimal("qty").multiply(new BigDecimal(orgReturnlList.size())));
                    entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(new BigDecimal(orgReturnlList.size())));
                }
                SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存
                // 根据采购入库单（红字）创建销售出库单（红字），到此就完结了
                oriBillId = desBillId;
                oriBillEntity = "im_purinbill";
                desBillEntity = "im_saloutbill";
                ruleId = config.getString("yd_intoout_re");
                opResult = createOutBill(saleoutBillId, oriEntityName, oriBillId, oriBillEntity,
                        desBillEntity, ruleId, srcOrgObj, desOrgObj, matPriceMap, false, false);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                SettleUtils.resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
                backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_saloutbill");
                entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                for(DynamicObject entry : entryCol) {
                    entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(new BigDecimal(orgReturnlList.size())));
                    entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(new BigDecimal(orgReturnlList.size())));
                }
                SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存
                oriBillId = desBillId;
            }
            // 不是第一组，创建销售出和采购入
            if (!orgRelMap.equals(orgReturnlList.getFirst())) {
                // 根据上一级的销售出库单（红字）创建采购入库单（红字）
                String oriBillEntity = "im_saloutbill";
                String desBillEntity = "im_purinbill";
                String ruleId = config.getString("yd_oriouttoin_re");
                opResult = createOutBill(saleoutBillId, oriEntityName, oriBillId, oriBillEntity,
                        desBillEntity, ruleId, srcOrgObj, desOrgObj, matPriceMap, false, false);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                SettleUtils.resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
                // 根据采购入库单（红字）创建销售出库单（红字），到此就完结了
                oriBillId = desBillId;
                oriBillEntity = "im_purinbill";
                desBillEntity = "im_saloutbill";
                ruleId = config.getString("yd_intoout_re");
                opResult = createOutBill(saleoutBillId, oriEntityName, oriBillId, oriBillEntity,
                        desBillEntity, ruleId, srcOrgObj, desOrgObj, matPriceMap, false, false);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                SettleUtils.resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
                oriBillId = desBillId;
            }
        }
        return oriBillId;
    }

    /**
     * 生成库存组织到结算组织的出入库单
     *
     * @param saleoutBillId
     * @param billId
     * @param orgSettleList
     * @param bizDate
     * @param orgWarehouseMap
     * @param config
     * @param isOutSettle
     * @param isForward
     * @author: hst
     * @createDate:
     */
    private static void createStockOrgToSettleLevelOrg(String saleoutBillId, String oriEntityName, String billId, LinkedList<String> orgSettleList,
                                                       Date bizDate, Map<String, String> orgWarehouseMap, DynamicObject config,boolean isForward,
                                                       boolean isOutSettle) {
        String oriBillId = "";
        String desBillId = "";
        Map<String, String> opResult = new HashMap<>();
        for (String orgRelMap : orgSettleList) {
            String[] keys = orgRelMap.split("&");
            String srcOrg = keys[0];
            DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrg, "bos_org");
            String desOrg = keys[1];
            DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrg, "bos_org");
            // 获取组织内部各个物料新的结算价格集合，根据单据业务日期获取对应日期范围内的价格
            Map<String, BigDecimal> matPriceMap;
            if (isForward) {
                matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(srcOrg, desOrg, bizDate);
            } else {
                matPriceMap = InternalSettleServiceHelper.getOrgtoOrgNewPrice(desOrg, srcOrg, bizDate);
            }
            if (orgRelMap.equals(orgSettleList.getFirst())) {
                // 根据源销售出库单创建销售出库单(正向）
                // 根据源销售出库单创建本级采购入库单(退货)
                oriBillId = StringUtils.isNotBlank(billId) ? billId : saleoutBillId;
                String oriBillEntity = isForward ? oriEntityName : oriEntityName;
                String desBillEntity = isForward ? "im_saloutbill" : "im_purinbill";
                String ruleId = isForward ? StringUtils.isNotBlank(billId) ? config.getString("yd_reouttoout") :
                        config.getString("yd_oriouttoout") : config.getString("yd_oriothtoin_re");
                // 对单据进行保存提交审核，拿到成功审核的单据ID
                opResult = createOutBill(saleoutBillId,oriEntityName,oriBillId,oriBillEntity,
                        desBillEntity,ruleId,srcOrgObj,desOrgObj,matPriceMap, StringUtils.isBlank(billId),isForward);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                 if (isForward) {
                    if (StringUtils.isNotBlank(billId)) {
                        SettleUtils.resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
                    }
                } else {
                    SettleUtils.resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
                    // 第一组则重置多少倍退货数量
                    DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_purinbill");
                    DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                    for(DynamicObject entry : entryCol) {
                        entry.set("returnqty", entry.getBigDecimal("qty").multiply(new BigDecimal("2")));
                        entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(new BigDecimal("2")));
                    }
                    SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存
                }

                // 根据销售出库单创建采购入库单，到此就完结了(正向)
                // 根据采购入库单创建上级销售出库单，到此就完结了(逆向)
                oriBillId = desBillId;
                oriBillEntity = isForward ? "im_saloutbill" : "im_purinbill";
                desBillEntity = isForward ? "im_purinbill" : "im_saloutbill";
                ruleId = isForward ? config.getString("yd_outtoin") : config.getString("yd_intoout_re");
                opResult = createOutBill(saleoutBillId,oriEntityName,oriBillId,oriBillEntity,desBillEntity,
                        ruleId,srcOrgObj,desOrgObj,matPriceMap,false,isForward);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                if (isForward) {
                    SettleUtils.resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
                } else {
                    // 不是最后一组才重置
                    if (!orgRelMap.equals(orgSettleList.getLast())) {
                        SettleUtils.resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
                    }
                    // 第一组则重置多少倍退货数量
                    DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_saloutbill");
                    DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                    for(DynamicObject entry : entryCol) {
                        // update by hst 2023/03/22 当只有二级组织时，退货一级组织需要重置E3仓库
                        if (orgRelMap.equals(orgSettleList.getLast())) {
                            // 如果是委外，需要查询委外仓库映射
                            if (isOutSettle) {
                                String wsNum = entry.getString("yd_e3oriwarehouse.number");
                                QFilter qFilter =  QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=?",
                                        wsNum);
                                Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP,
                                        "yd_entryentity.yd_acturalwarehouse.id", qFilter.toArray());
                                entry.set("warehouse", actWarehouseId);
                            } else {
                                entry.set("warehouse", entry.getDynamicObject("yd_e3oriwarehouse"));
                            }
                        }
                        entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(new BigDecimal("2")));
                        entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(new BigDecimal("2")));
                    }
                    SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存
                }
                oriBillId = desBillId;
            }

            // 不是第一组，创建销售出和采购入
            if (!orgRelMap.equals(orgSettleList.getFirst())) {
                // 根据上一级的采购入库单创建销售出库单
                String oriBillEntity = isForward ? "im_purinbill" : "im_saloutbill";
                String desBillEntity = isForward ? "im_saloutbill" : "im_purinbill";
                String ruleId = isForward ? config.getString("yd_intoout") : config.getString("yd_oriouttoin_re");
                opResult = createOutBill(saleoutBillId,oriEntityName,oriBillId,oriBillEntity,desBillEntity,
                        ruleId,srcOrgObj,desOrgObj,matPriceMap,false,isForward);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                if (!isForward) {
                    SettleUtils.resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
                    if (!orgRelMap.equals(orgSettleList.getLast())) {
                        // 重置退货数量
                        DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, desBillEntity);
                        DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                        for (DynamicObject entry : entryCol) {
                            entry.set("returnqty", entry.getBigDecimal("qty").multiply(new BigDecimal("2")));
                            entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(new BigDecimal("2")));
                        }
                        SaveServiceHelper.save(new DynamicObject[]{backInwareBill});// 保存
                    }
                }
                // 根据销售出库单创建采购入库单，到此就完结了
                oriBillId = desBillId;
                oriBillEntity = isForward ? "im_saloutbill" : "im_purinbill";
                desBillEntity = isForward ? "im_purinbill" : "im_saloutbill";
                ruleId = isForward ? config.getString("yd_outtoin") : config.getString("yd_intoout_re");
                opResult = createOutBill(saleoutBillId,oriEntityName,oriBillId,oriBillEntity,desBillEntity,
                        ruleId,srcOrgObj,desOrgObj,matPriceMap,false,isForward);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                if (isForward) {
                    SettleUtils.resetBillWarehouse(desBillId, "im_purinbill", orgWarehouseMap);
                } else {
                    // 不是最后一组才重置
                    if (!orgRelMap.equals(orgSettleList.getLast())) {
                        SettleUtils.resetBillWarehouse(desBillId, "im_saloutbill", orgWarehouseMap);
                    }
                    // 最后一张销售出库单的仓库为源单E3仓库
                    DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(desBillId, "im_saloutbill");
                    DynamicObjectCollection saleoutEntryCol = resetBill.getDynamicObjectCollection("billentry");
                    for (DynamicObject resetSaleEntry : saleoutEntryCol) {
                        if (orgRelMap.equals(orgSettleList.getLast())) {
                            resetSaleEntry.set("warehouse", resetSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
                        }
                        // update by hstt 2022/11/10 最后一张出库单退货数量不用翻倍
                        if (!orgRelMap.equals(orgSettleList.getLast())) {
                            resetSaleEntry.set("remainreturnqty", resetSaleEntry.getBigDecimal("qty").abs().multiply(new BigDecimal("2")));
                            resetSaleEntry.set("remainreturnbaseqty", resetSaleEntry.getBigDecimal("baseqty").abs().multiply(new BigDecimal("2")));
                        }
                    }
                    SaveServiceHelper.save(new DynamicObject[] {resetBill});// 保存
                }
                oriBillId = desBillId;
            }
            // 最后一组只执行创建销售出库单
            if (orgRelMap.equals(orgSettleList.getLast())) {
                System.out.println("最后一组组织关系");
            }
        }
    }

    /**
     * 下推生成销售出库单/采购入库单
     *
     * @param billId 销售出库单id
     * @param oriBillId     下推单据id
     * @param oriBillEntity 下推单据类型
     * @param desBillEntity 目标单据类型
     * @param ruleId        转换路线标识ID
     * @param srcOrg        源组织
     * @param destOrg       目标组织
     * @param matPriceMap   内部校验价格
     * @param isFirst       是否首单
     * @param isForward     是否正向
     * @return
     * @author: hst
     * @createDate:
     */
    private static Map<String, String> createOutBill(String billId, String billName, String oriBillId, String oriBillEntity, String desBillEntity, String ruleId,
                                                     DynamicObject srcOrg, DynamicObject destOrg, Map<String, BigDecimal> matPriceMap, boolean isFirst, boolean isForward) {
        DynamicObject tempTargetBillObj = SettleUtils.pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);
        // 首笔销售出库单需要标记为首单，重置首单的信息，主组织应该为第一组组织中的前者
        // 需要赋值的字段：单据头.库存组织，单据头.销售组织，单据头.销售部门，单据头.库管部门，物料明细.结算组织，为组织关系中的前者
        DynamicObject org = null;
        if ((BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(oriBillEntity) && BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(desBillEntity))
                || (BillTypeHelper.BILLTYPE_PURINBILL.equals(oriBillEntity) && BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(desBillEntity))
                || (BillTypeHelper.BILLTYPE_OTHEROUTBILL.equals(oriBillEntity) && BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(desBillEntity))) {
            org = isForward ? srcOrg : destOrg;
            // 根据编码获取后者组织作为客户
            QFilter cusFilter = new QFilter("internal_company.number", QCP.equals,
                    isForward ? destOrg.getString("number") : srcOrg.getString("number"));
            cusFilter.and(new QFilter("status", QCP.equals, "C"));
            DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel,taxrate", cusFilter.toArray());
            DynamicObject customer = null;
            if (customers.length > 0) {
                customer = customers[0];
            }
            tempTargetBillObj.set("customer", customer);
            tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道
            DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");

            // 根据客户重新查找税率
            DynamicObject cusTaxRateObj = customer.getDynamicObject("taxrate");
            cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
            for (DynamicObject entryObj : outEntryCol) {
                entryObj.set("entrysettleorg", org);  // 结算组织
                entryObj.set("outowner", org);  // 出库货主
                entryObj.set("outkeeper", org);  // 出库保管者

                entryObj.set("settlecustomer", customer);  // 应收客户
                entryObj.set("payingcustomer", customer);  // 付款客户
                entryObj.set("reccustomer", customer);  // 收货客户

                // 根据客户重置税率
                entryObj.set("taxrateid", cusTaxRateObj);
                entryObj.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));
                // 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
                String materialId = entryObj.getString("material.masterid.id");
                if (matPriceMap.containsKey(materialId)) {
                    BigDecimal newPrice = matPriceMap.get(materialId);
                    entryObj.set("priceandtax", newPrice);
                    SettleUtils.changePriceAndTax(tempTargetBillObj, entryObj);
                    // 单价为0标记为赠品
                    if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
                        entryObj.set("ispresent", true);
                    } else {
                        entryObj.set("ispresent", false);
                    }
                }
            }
        } else if ((BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(oriBillEntity) && BillTypeHelper.BILLTYPE_PURINBILL.equals(desBillEntity))
                ||(BillTypeHelper.BILLTYPE_OTHEROUTBILL.equals(oriBillEntity) && BillTypeHelper.BILLTYPE_PURINBILL.equals(desBillEntity))) {
            org = isForward ? destOrg : srcOrg;
            // 获取供应商
            QFilter supFilter = new QFilter("internal_company.number", QCP.equals,
                    isForward ? srcOrg.getString("number") : destOrg.getString("number"));
            supFilter.and(new QFilter("status", QCP.equals, "C"));
            DynamicObject[] suppliers = BusinessDataServiceHelper.load("bd_supplier", "id,name,number", supFilter.toArray());
            DynamicObject supplier = null;
            if (suppliers.length > 0) {
                supplier = suppliers[0];
            }
            // 单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商、
            tempTargetBillObj.set("supplier", supplier);
            DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
            // 库存组织作为客户
            QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, org.getString("number"));
            cusFilter.and(new QFilter("status", QCP.equals, "C"));
            DynamicObject[] customers = BusinessDataServiceHelper.load("bd_customer", "id,name,number,yd_channel,taxrate", cusFilter.toArray());
            DynamicObject customer = null;
            if (customers.length > 0) {
                customer = customers[0];
            }

            DynamicObject cusTaxRateObj = customer.getDynamicObject("taxrate");
            cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
            for (DynamicObject entryObj : inEntryCol) {
                entryObj.set("owner", org);
                entryObj.set("keeper", org);
                entryObj.set("entrysettleorg", org);
                entryObj.set("entryreqorg", org);

                entryObj.set("providersupplier", supplier);
                entryObj.set("invoicesupplier", supplier);
                entryObj.set("receivesupplier", supplier);
                // 根据客户重置税率
                entryObj.set("taxrateid", cusTaxRateObj);
                entryObj.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));

                // 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
                String materialId = entryObj.getString("material.masterid.id");
                if (matPriceMap.containsKey(materialId)) {
                    BigDecimal newPrice = matPriceMap.get(materialId);
                    entryObj.set("priceandtax", newPrice);
                    SettleUtils.changePriceAndTax(tempTargetBillObj, entryObj);
                    // 单价为0标记为赠品
                    if (matPriceMap.get(materialId).compareTo(BigDecimal.ZERO) == 0) {
                        entryObj.set("ispresent", true);
                    } else {
                        entryObj.set("ispresent", false);
                    }
                }
            }
        }

        tempTargetBillObj.set("org", org);
        tempTargetBillObj.set("bizorg", org);
        tempTargetBillObj.set("bizdept", org);
        tempTargetBillObj.set("dept", org);
        tempTargetBillObj.set("yd_settleorg", org);

        if (isFirst & isForward) {
            // 标记为首单（正向），用于集成平台开始同步的单据
            tempTargetBillObj.set("yd_firstinternalbill", true);
        }

        //设置汇率信息
        if (Objects.isNull(tempTargetBillObj.get("exratetable"))) {
            // 获取核算组织本位币
            DynamicObject accountingSys = BusinessDataServiceHelper.loadSingle("bd_accountingsys_base",
                    new QFilter[]{new QFilter("baseacctorg.id", QFilter.equals,org.getPkValue())});

            if (Objects.nonNull(accountingSys)) {
                tempTargetBillObj.set("currency",accountingSys.getDynamicObject("basecurrrency"));
                tempTargetBillObj.set("settlecurrency",accountingSys.getDynamicObject("basecurrrency"));
                tempTargetBillObj.set("exratetable",accountingSys.getDynamicObject("exratetable"));
                tempTargetBillObj.set("exratedate",tempTargetBillObj.getDate("biztime"));
                tempTargetBillObj.set("exchangerate",1);
            }
        }

        // 对单据进行保存提交审核，拿到成功审核的单据ID
        return operateTargetBill(billId, billName, desBillEntity, tempTargetBillObj);
    }

    /**
     * 获取线下一盘货业务结算路径
     * @author: hst
     * @createDate: 2023/09/07
     * @param bill
     * @return
     */
    public static DataSet getOfflineSettlePath (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        DynamicObject stockOrg = bill.getDynamicObject("org");
        DynamicObject settleOrg = bill.getDynamicObject("yd_settleorg");
        DynamicObject biztype = bill.getDynamicObject("biztype");
        DynamicObjectCollection materials = bill.getDynamicObjectCollection("billentry");
        DynamicObject brand = materials.size() > 0 ? materials.get(0).getDynamicObject("yd_basedata_pinpai") : null;
        boolean isForward = "210".equals(biztype.getString("number")) ? true : false;
        if (Objects.isNull(stockOrg)) {
            throw new KDBizException("获取不到库存组织信息，无法生成PCP结算流程单据！");
        }
        if (Objects.isNull(settleOrg)) {
            throw new KDBizException("获取不到结算组织信息，无法生成PCP结算流程单据！");
        }
        if (Objects.isNull(brand)) {
            throw new KDBizException("获取不到物料品牌信息，无法生成PCP结算流程单据！");
        }
        // 库存组织
        qFilters.add(new QFilter("yd_stockorg.id", QCP.equals, stockOrg.getPkValue()));
        // 结算组织
        qFilters.add(new QFilter("yd_settleorg.id", QCP.equals, settleOrg.getPkValue()));
        // 审核状态
        qFilters.add(new QFilter("status", QCP.equals, "C"));
        // 品牌
        qFilters.add(new QFilter("yd_brandentity.yd_brand.number", QCP.equals, brand.getString("number")));
        // 排序字段
        String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
        // 错误提示
        String tip = "";
        tip = "当前库存组织：" + stockOrg.getString("name") + "与结算组织："
                + settleOrg.getString("name") + "的品牌：" + brand.getString("name")
                + "没有配置线下一盘货结算关系，无法生成内部结算流程单据！";
        DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_offlinesettle",
                "yd_settleorg.id newCusId,yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
                        "yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName", qFilters.toArray(new QFilter[qFilters.size()]), orderby);
        if (!settleDataSet.hasNext()) {
            // 品牌没有配置对应的内部结算关系，不生成内部结算单据
            throw new KDBizException(tip);
        }
        return settleDataSet;
    }

    /**
     * 获取线下一盘货业务退货路径
     * @author: hst
     * @createDate: 2023/09/07
     * @param bill
     * @return
     */
    public static DataSet getOfflineReturnPath (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        boolean isReturn = bill.getBoolean("yd_isreturn");
        if (isReturn) {
            DynamicObject stockOrg = bill.getDynamicObject("org");
            DynamicObject settleOrg = bill.getDynamicObject("yd_settleorg");
            DynamicObjectCollection materials = bill.getDynamicObjectCollection("billentry");
            DynamicObject brand = materials.size() > 0 ? materials.get(0).getDynamicObject("yd_basedata_pinpai") : null;
            if (Objects.isNull(stockOrg)) {
                throw new KDBizException("获取不到库存组织信息，无法生成PCP结算流程单据！");
            }
            if (Objects.isNull(settleOrg)) {
                throw new KDBizException("获取不到结算组织信息，无法生成PCP结算流程单据！");
            }
            if (Objects.isNull(brand)) {
                throw new KDBizException("获取不到物料品牌信息，无法生成PCP结算流程单据！");
            }
            // 库存组织
            qFilters.add(new QFilter("yd_stockorg.id", QCP.equals, stockOrg.getPkValue()));
            // 结算组织
            qFilters.add(new QFilter("yd_settleorg.id", QCP.equals, settleOrg.getPkValue()));
            // 审核状态
            qFilters.add(new QFilter("status", QCP.equals, "C"));
            // 品牌
            qFilters.add(new QFilter("yd_brandentity.yd_brand.number", QCP.equals, brand.getString("number")));
            // 退货结算
            qFilters.add(new QFilter("yd_isreturn", QCP.equals, true));
            // 排序字段
            String orderby = "yd_returnentity.seq desc";
            // 错误提示
            String tip = "";
            tip = "当前库存组织：" + stockOrg.getString("name") + "与结算组织："
                    + settleOrg.getString("name") + "的品牌：" + brand.getString("name")
                    + "没有配置线下一盘货退货结算关系，无法生成内部结算流程单据！";
            DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_offlinesettle",
                    "yd_settleorg.id newCusId,yd_returnentity.yd_returnlevel orglevel,yd_returnentity.yd_returnorg.id orgid," +
                            "yd_returnentity.yd_returnorg.number orgNum,yd_returnentity.yd_returnorg.name orgName", qFilters.toArray(new QFilter[qFilters.size()]), orderby);
            if (!settleDataSet.hasNext()) {
                // 品牌没有配置对应的退货结算关系，不生成内部结算单据
                throw new KDBizException(tip);
            }
            return settleDataSet;
        } else {
            return null;
        }
    }

    /**
     * 获取成品仓一盘货业务结算路径
     * @author: hst
     * @createDate: 2023/09/25
     * @param bill
     * @return
     */
    public static DataSet getWarehouseSettlePath (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        DynamicObject stockOrg = bill.getDynamicObject("org");
        DynamicObject customer = bill.getDynamicObject("customer");
        DynamicObject biztype = bill.getDynamicObject("biztype");
        DynamicObjectCollection materials = bill.getDynamicObjectCollection("billentry");
        DynamicObject brand = materials.size() > 0 ? materials.get(0).getDynamicObject("yd_basedata_pinpai") : null;
        boolean isForward = "210".equals(biztype.getString("number")) ? true : false;
        if (Objects.isNull(stockOrg)) {
            throw new KDBizException("获取不到库存组织信息，无法生成成品仓一盘货结算流程单据！");
        }
        if (Objects.isNull(customer)) {
            throw new KDBizException("获取不到客户信息，无法生成成品仓一盘货结算流程单据！");
        }
        if (Objects.isNull(brand)) {
            throw new KDBizException("获取不到物料品牌信息，无法生成成品仓一盘货结算流程单据！");
        }
        // 渠道组织
        qFilters.add(new QFilter("yd_canalorg.id", QCP.equals, stockOrg.getPkValue()));
        // 客户
        qFilters.add(new QFilter("yd_customentity.yd_custom.id", QCP.equals, customer.getPkValue()));
        // 审核状态
        qFilters.add(new QFilter("status", QCP.equals, "C"));
        // 品牌
        qFilters.add(new QFilter("yd_brandentity.yd_brand.number", QCP.equals, brand.getString("number")));
        // 排序字段
        String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
        // 错误提示
        String tip = "";
        tip = "当前库存组织：" + stockOrg.getString("name") + "与客户："
                + customer.getString("name") + "的品牌：" + brand.getString("name")
                + "没有配置成品仓一盘货结算关系，无法生成内部结算流程单据！";
        DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_warehouseinventory",
                "yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
                        "yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName", qFilters.toArray(new QFilter[qFilters.size()]), orderby);
        if (!settleDataSet.hasNext()) {
            // 品牌没有配置对应的内部结算关系，不生成内部结算单据
            throw new KDBizException(tip);
        }
        return settleDataSet;
    }

    /**
     * 获取成品仓一盘货业务退货路径
     * @author: hst
     * @createDate: 2023/09/07
     * @param bill
     * @return
     */
    public static DataSet getWarehouseReturnPath (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        boolean isReturn = bill.getBoolean("yd_isreturn");
        if (isReturn) {
            DynamicObject stockOrg = bill.getDynamicObject("org");
            DynamicObject customer = bill.getDynamicObject("customer");
            DynamicObjectCollection materials = bill.getDynamicObjectCollection("billentry");
            DynamicObject brand = materials.size() > 0 ? materials.get(0).getDynamicObject("yd_basedata_pinpai") : null;
            if (Objects.isNull(stockOrg)) {
                throw new KDBizException("获取不到库存组织信息，无法生成成品仓一盘货结算流程单据！");
            }
            if (Objects.isNull(customer)) {
                throw new KDBizException("获取不到客户信息，无法生成成品仓一盘货结算流程单据！");
            }
            if (Objects.isNull(brand)) {
                throw new KDBizException("获取不到物料品牌信息，无法生成成品仓一盘货结算流程单据！");
            }
            // 渠道组织
            qFilters.add(new QFilter("yd_canalorg.id", QCP.equals, stockOrg.getPkValue()));
            // 客户
            qFilters.add(new QFilter("yd_customentity.yd_custom.id", QCP.equals, customer.getPkValue()));
            // 审核状态
            qFilters.add(new QFilter("status", QCP.equals, "C"));
            // 品牌
            qFilters.add(new QFilter("yd_brandentity.yd_brand.number", QCP.equals, brand.getString("number")));
            // 退货结算
            qFilters.add(new QFilter("yd_isreturn", QCP.equals, true));
            // 排序字段
            String orderby = "yd_returnentity.seq desc";
            // 错误提示
            String tip = "";
            tip = "当前库存组织【" + stockOrg.getString("name") + "】与结算组织【"
                    + customer.getString("name") + "】的品牌【" + brand.getString("name")
                    + "】没有配置成品仓一盘货退货结算关系，无法生成内部结算流程单据！";
            DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_warehouseinventory",
                    "yd_returnentity.yd_returnlevel orglevel,yd_returnentity.yd_returnorg.id orgid," +
                            "yd_returnentity.yd_returnorg.number orgNum,yd_returnentity.yd_returnorg.name orgName", qFilters.toArray(new QFilter[qFilters.size()]), orderby);
            if (!settleDataSet.hasNext()) {
                // 品牌没有配置对应的退货结算关系，不生成内部结算单据
                throw new KDBizException(tip);
            }
            return settleDataSet;
        } else {
            return null;
        }
    }

    /**
     * 其他出库一盘货结算
     *
     * @param bill 单据
     * @param isForward   是否是正向
     * @return 是否成功
     * @author: hst
     * @createDate: 2024/06/15
     */
    @Override
    public ApiResult createOtherOutSettleBill(DynamicObject bill, DynamicObject config, Boolean isForward) {
        ApiResult apiResult = new ApiResult();
        apiResult.setSuccess(true);
        try {
            // 获取仓库映射
            Map<String, String> orgWarehouseMap = getOrgWarehouseMap();

            // 业务日期
            Date bizDate = bill.getDate("biztime");

            // 通过配置信息获取结算路径结算路径
            Map<String, Object> settlePath = getOtherSettlePath(bill, config);
            LinkedList<String> orgSettleList = (LinkedList<String>) settlePath.get("orgSettleList");

            // 校验是否配置对应的内部结算关系，没有则不生成结算单据
            Map<String, String> internalCheckResult = checkOtherSettleMsg(bill, config);
            if (StringUtils.isEmpty(internalCheckResult.get("billId"))) {
                throw new KDBizException("单据" + bill.getString("billno") + "无法进行结算：" + internalCheckResult.get("errorMsg"));
            }
            // 根据配置信息判断是否需要重置单据的组织
            DynamicObject lastOrg = bill.getDynamicObject("org");
            if (config.getBoolean("yd_resetorg")) {
                String outOrgId = settlePath.get("outOrgId").toString();
                lastOrg = BusinessDataServiceHelper.loadSingle(outOrgId, "bos_org");
            }
            bill.set("yd_oriorg", bill.get("org"));
            bill.set("org", lastOrg);
            bill.set("yd_bizorg", lastOrg);
            bill.set("bizdept", lastOrg);
            bill.set("dept", lastOrg);

            // 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
            resetSourceBill(bill);

            // 再次发起结算时，清空结算失败原因
            bill.set("yd_settleerror", "");
            // 标记源单为发起内部结算单据标识
            bill.set("yd_isbeginsettle", true);
            bill.set("yd_settletype",isForward.booleanValue() ? "1" : "0");
            String oriBillId = bill.getString("id");

            Map<String, String> opResult = operateTargetBill(oriBillId,BillTypeHelper.BILLTYPE_OTHEROUTBILL,
                    BillTypeHelper.BILLTYPE_OTHEROUTBILL,bill);

            if (orgSettleList.size() > 0) {
                oriBillId = bill.getString("id");
                boolean isOutSettle = bill.getBoolean("yd_isoutsettle");
                // 生成库存组织到结算组织的出入库单
                createStockOrgToSettleLevelOrg(oriBillId, BillTypeHelper.BILLTYPE_OTHEROUTBILL, "",
                        orgSettleList, bizDate, orgWarehouseMap, config, isForward,isOutSettle);
                // 重置源单上分录的仓库为组织对应的仓库
                SettleUtils.resetBillWarehouse(oriBillId,  BillTypeHelper.BILLTYPE_OTHEROUTBILL, orgWarehouseMap);
                // 最后才标记源单为已生成内部结算单据标识
                bill = BusinessDataServiceHelper.loadSingle(oriBillId,  BillTypeHelper.BILLTYPE_OTHEROUTBILL);
            } else {
                bill.set("yd_firstinternalbill",true);
            }

            bill.set("yd_issettled", true);
            bill.set("yd_settleerror", "");
            SaveServiceHelper.update(new DynamicObject[]{bill});

            // 使用关联关系将生成的下游单据保存到源销售出库单上
            SettleUtils.saveBotpBills(bill.getString("id"),BillTypeHelper.BILLTYPE_OTHEROUTBILL);
        } catch (Exception e) {
            // 记录错误信息
            logger.error(e);
            apiResult.setSuccess(false);
            apiResult.setMessage(e.getLocalizedMessage());
        }
        return apiResult;
    }

    /**
     * 获取其他出库结算路径
     *
     * @param bill   单据
     * @param config 配置信息
     * @return (path : 结算路径)
     * @author: hst
     * @createDate : 2024/06/15
     */
    private static Map<String, Object> getOtherSettlePath(DynamicObject bill, DynamicObject config) {
        Map<String, Object> map = new HashMap<>();
        // 销售组织
        String bizOrgId = bill.getString("yd_bizorg.id");

        // 是否正向
        String biztype = bill.getString("biztype.number");
        boolean isForward = "210".equals(biztype) || "355".equals(biztype)? true : false;

        // 通过配置信息获取结算路径
        DataSet settleDataSet = getSettlePathByConfig(bill, config);
        LinkedList<String> orgSettleList = new LinkedList<String>();
        // 记录下一行组织的ID，遍历完每一行时才赋值，然后遍历每一行时校验是否有上级，保存上级与下级的关系
        // 正常结算
        String outOrgId = "";
        for (Row settleRow : settleDataSet) {
            String curOrgId = settleRow.getString("orgid");
            // 存储当前单据组织的下一级，作为本单据的客户
            if (StringUtils.isNotBlank(outOrgId) && outOrgId.equals(bizOrgId)) {
                if (isForward || (!isForward && !map.containsKey("finalCustomer"))) {
                    DynamicObject customer = InternalSettleServiceHelper.getInternalCP("bd_customer", settleRow.getString("orgNum"));
                    if (Objects.nonNull(customer)) {
                        map.put("finalCustomer", customer);
                    }
                }
            }
            if (StringUtils.isNotEmpty(outOrgId)) {
                String mapValue = outOrgId + "&" + curOrgId;
                orgSettleList.add(mapValue);
            }
            outOrgId = curOrgId;
            if (isForward) {
                map.put("outOrgId", outOrgId);
            }
            if (!isForward && !map.containsKey("outOrgId")) {
                map.put("outOrgId", outOrgId);
            }
        }
        map.put("orgSettleList", orgSettleList);
        return map;
    }

    /**
     * 获取其他出库一盘货业务结算路径
     * @author: hst
     * @createDate: 2024/06/15
     * @param bill
     * @return
     */
    public static DataSet getOtherOutSettlePath (DynamicObject bill) {
        List<QFilter> qFilters = new ArrayList<>();
        DynamicObject stockOrg = bill.getDynamicObject("org");
        DynamicObject saleOrg = bill.getDynamicObject("yd_bizorg");
        String biztype = bill.getString("biztype.number");
        DynamicObjectCollection materials = bill.getDynamicObjectCollection("billentry");
        DynamicObject brand = materials.size() > 0 ? materials.get(0).getDynamicObject("yd_basedatafield_pp") : null;
        boolean isForward = "355".equals(biztype) ? true : false;
        if (Objects.isNull(stockOrg)) {
            throw new KDBizException("获取不到库存组织信息，无法获取一盘货结算关系！");
        }
        if (Objects.isNull(saleOrg)) {
            throw new KDBizException("获取不到销售组织信息，无法获取一盘货结算关系！");
        }
        if (Objects.isNull(brand)) {
            throw new KDBizException("获取不到物料品牌信息，无法获取一盘货结算关系！");
        }
        // 渠道组织
        qFilters.add(new QFilter("yd_invorg.id", QCP.equals, stockOrg.getPkValue()));
        // 渠道组织
        qFilters.add(new QFilter("yd_canalorg.id", QCP.equals, saleOrg.getPkValue()));
        // 审核状态
        qFilters.add(new QFilter("status", QCP.equals, "C"));
        // 品牌
        qFilters.add(new QFilter("yd_brandentity.yd_brand.number", QCP.equals, brand.getString("number")));
        // 排序字段
        String orderby = isForward ? "yd_levelentry.seq asc" : "yd_levelentry.seq desc";
        // 错误提示
        String tip = "";
        tip = "当前库存组织【" + stockOrg.getString("name") + "】与销售组织【"
                + saleOrg.getString("name") + "】的品牌【" + brand.getString("name")
                + "】没有配置一盘货结算关系，无法生成内部结算流程单据！";
        DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_warehouseinventory",
                "yd_levelentry.yd_orglevel orglevel,yd_levelentry.yd_org.id orgid," +
                        "yd_levelentry.yd_org.number orgNum,yd_levelentry.yd_org.name orgName, yd_isoutsettle",
                qFilters.toArray(new QFilter[qFilters.size()]), orderby);
        if (!settleDataSet.hasNext()) {
            // 品牌没有配置对应的内部结算关系，不生成内部结算单据
            throw new KDBizException(tip);
        }
        return settleDataSet;
    }

    /**
     * 校验单据末级销售出库单对应的内部交易关系是否有已维护
     *
     * @param bill 其他出库单
     * @param config
     * @return billId为空表示失败，errorMsg表示失败原因
     * @author: hst
     * @createDate: 2024/06/15
     */
    public static Map<String, String> checkOtherSettleMsg(DynamicObject bill, DynamicObject config) {
        Map<String, String> result = new HashMap<String, String>();
        // 源末级销售出库单
//        DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, "im_otheroutbill");
        DataSet settleDataSet = null;
        try {
            StringBuilder errMsg = new StringBuilder();
            // 通过配置信息获取结算路径
            settleDataSet = getSettlePathByConfig(bill, config);
            LinkedList<String> orgList = new LinkedList<String>();
            // 校验结算路径上的组织与仓库映射表
            errMsg.append(checkOrganizationWarehouse(settleDataSet.copy(), orgList));
            // 校验生产日期（判断是否委外结算）
            errMsg.append(checkGenerationDate(settleDataSet.copy(),bill));
            // 校验结算路径上的内部商客
            errMsg.append(checkSupplyAndCustomer(bill, orgList,null));
            // 自定义校验（通过配置信息中的类名及方法名自定义添加校验）
            Object custom = invokeMethodByConfig(bill.getString("id"),config.getString("yd_cusclass"),
                    config.getString("yd_cusmethod"));
            if (Objects.nonNull(custom)) {
                errMsg.append(custom);
            }
            if (errMsg.length() > 0) {
                result.put("billId", "");
                result.put("errorMsg", errMsg.toString());
            } else {
                result.put("billId", bill.getString("id"));
                result.put("errorMsg", "");
            }
        } finally {
            if (Objects.nonNull(settleDataSet)) {
                settleDataSet.close();
            }
        }
        return result;
    }

    /**
     * 如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
     *
     * @param bill
     * @author: hst
     * @createDate:
     */
    private static void resetSourceBill(DynamicObject bill) {
        // 正向/逆向
        String biztype = bill.getString("biztype.number");
//        boolean isForward = "355".equals(biztype) ? true : false;
        // 库存组织
        DynamicObject org = bill.getDynamicObject("org");
        // 对源销售出库单进行单价重置，如果与结算关系中的最后一级组织不同，则还需要重置库存组织等信息
        DynamicObjectCollection oriSaleEntryCol = bill.getDynamicObjectCollection("billentry");
        for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
            // 重置单据的组织
            oriSaleEntry.set("outowner", org);  // 出库货主
            oriSaleEntry.set("outkeeper", org);  // 出库保管者

            // 保存一下源单的E3仓库
            if (Objects.isNull(oriSaleEntry.get("yd_oriwarehouse"))) {
                oriSaleEntry.set("yd_oriwarehouse", oriSaleEntry.get("warehouse"));
            }

//            // 根据库存组织+物料+批次获取生产日期和到期日期
//            String matNum = oriSaleEntry.getString("material.masterid.number");
//            String lot = oriSaleEntry.getString("yd_ph");
//            String wsNum = oriSaleEntry.getString("warehouse.number");
//            if (isForward) {
//                oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, false);
//            } else {
//                oriSaleEntry = setExpDate(lot, matNum, wsNum, oriSaleEntry, true);
//            }
//
//            // 如果没有找到日期则默认一个日期，按参数（如果参数为true表示自动带出日期，为false表示不带出）
//            DynamicObject[] isGeneralDateObj = BusinessDataServiceHelper.load("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
//            String isGeneralDate = "";
//            if (isGeneralDateObj.length > 0) {
//                isGeneralDate = isGeneralDateObj[0].getString("name");
//            }
//            if ((StringUtils.isEmpty(isGeneralDate) || StringUtils.equalsIgnoreCase("true", isGeneralDate))
//                    && oriSaleEntry.get("yd_dqr") == null) {
//                // 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期）
//                if (isForward) {
//                    oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, false);
//                } else {
//                    oriSaleEntry = setLastExpDate(matNum, wsNum, oriSaleEntry, true);
//                }
//            }

            // 携带源id
            oriSaleEntry.set("yd_sourceentryid",oriSaleEntry.getString("id"));
        }
    }

    /**
     * 其他出库委外结算校验
     * @author: hst
     * @createDate: 2024/07/08
     */
    private static String checkGenerationDate (DataSet settlePath, DynamicObject bill) {
        StringBuffer errorMsg = new StringBuffer();
        // 如果参数为true表示校验生产日期，为false表示不校验
        DynamicObject biztype = bill.getDynamicObject("biztype");
        boolean isControlLot = true; // 默认校验
        boolean isGeneralDate = true; // 默认为true，自动带日期即不要校验日期，退货类型的如果开通了此参数则不用校验批次
        // 从配置表中查找对应的值
        String isControlLotStr = BizHelper.getQueryOne("yd_e3paramsetting", "name",
                new QFilter("number", QCP.equals, "isControlLot").toArray());
        String isGeneralDateStr = BizHelper.getQueryOne("yd_e3paramsetting", "name",
                new QFilter("number", QCP.equals, "isGeneralDate").toArray());

        if (StringUtils.isNotBlank(isControlLotStr)) {
            isControlLot = StringUtils.equalsIgnoreCase("true", isControlLotStr);
        }
        if (StringUtils.isNotBlank(isGeneralDateStr)) {
            isGeneralDate = StringUtils.equalsIgnoreCase("true", isGeneralDateStr);
        }
        // 是否退货
        Boolean isReturn = "3551".equals(biztype.getString("number"));

        boolean isOutSettle = false;
        if (Objects.nonNull(settlePath)) {
            isOutSettle = settlePath.next().getBoolean("yd_isoutsettle");
            if (isOutSettle) {
                bill.set("yd_isoutsettle",isOutSettle);

                DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("billentry");

                for (DynamicObject entryObj : entryCol) {
                    String wsNum = entryObj.getString("warehouse.number");
                    // 判断是否存在 共享仓+品牌+客户的仓库映射表
                    QFilter qFilter =  QFilter.of("status='C' and yd_entryentity.yd_sharewarehouse.number=?",
                                wsNum);
                    Long actWarehouseId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_OUTWAREHOUSEMAP,
                            "yd_entryentity.yd_acturalwarehouse.id", qFilter.toArray());

                    if (Objects.isNull(actWarehouseId)) {
                        errorMsg.append(String.format("委外结算缺少（%s）委外仓库映射表！", wsNum));
                    } else {
                        // 需要清空生产日期和结束日期，便于检查新的库存信息
                        entryObj.set("yd_oriwarehouse", entryObj.get("warehouse"));
                        DynamicObject wareHouse = BusinessDataServiceHelper.loadSingle(actWarehouseId, "bd_warehouse");
                        entryObj.set("warehouse", wareHouse);
                        entryObj.set("yd_scrq", null);
                        entryObj.set("yd_dqr", null);
                    }
                }
            } else {
                bill.set("yd_isoutsettle",false);
            }
        }

        if (errorMsg.length() > 0) {
            return errorMsg.toString();
        }

        DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("billentry");
        if (isControlLot) {
            for (DynamicObject entryObj : entryCol) {
                // 校验是否存在对应的批次数据
                String lot = entryObj.getString("yd_ph");
                String matNum = entryObj.getString("material.masterid.number");
                String wsNum = entryObj.getString("warehouse.number");
                Date beginDate = entryObj.getDate("yd_scrq");
                Date endDate = entryObj.getDate("yd_dqr");

                boolean isReset = true;
                // 如果不存在，只有委外中查找映射中不存在，这种情况异常已经记录，无须再次查找
                if (wsNum == null) continue;
                // 如果生产的日期为空，都需要从即时库存中查找
                if (beginDate == null || endDate == null) {
                    // 按批次+物料+仓库，如果没有查找到，正向执行提示异常，如果是红字，则需要再次查找按物料+仓库查找，没有则提示异常
                    boolean isHasLot = hasExpDate(lot, matNum, wsNum, isReturn);
                    if (!isHasLot) {
                        if (isGeneralDate) {
                            boolean isHasRefundLot = hasLastExpDate(matNum, wsNum, isReturn);
                            if (!isHasRefundLot) {
                                if (errorMsg.indexOf(matNum + "的批次" + lot) < 0) {
                                    errorMsg.append("仓库编码" + wsNum + "的" + matNum + "的批次" + lot + ";");
                                    isReset = false;
                                }
                            }
                        } else {
                            if (errorMsg.indexOf(matNum + "的批次" + lot) < 0) {
                                errorMsg.append("仓库编码" + wsNum + "的" + matNum + "的批次" + lot + ";");
                                isReset = false;
                            }
                        }
                    }
                    if (isReset) {
                        // 根据库存组织+物料+批次获取生产日期和到期日期
                        if (!isReturn) {
                            setExpDate(lot, matNum, wsNum, entryObj, false);
                        } else {
                            setExpDate(lot, matNum, wsNum, entryObj, true);
                        }

                        if ((StringUtils.isEmpty(isGeneralDateStr) || StringUtils.equalsIgnoreCase("true", isGeneralDateStr))
                                && entryObj.get("yd_dqr") == null) {
                            // 退货类的根据参数判断是否使用当前最新的批次进行入库（根据物料+仓库维度获取最新批次及日期）
                            if (!isReturn) {
                                setLastExpDate(matNum, wsNum, entryObj, false);
                            } else {
                                setLastExpDate(matNum, wsNum, entryObj, true);
                            }
                        }
                    }
                }
            }

            if (errorMsg.length() > 0) {
                errorMsg.insert(0, "以下物料对应的批次不存在即时库存数据：").deleteCharAt(errorMsg.length() - 1).append("；");
            }
        }
        return errorMsg.toString();
    }
}
