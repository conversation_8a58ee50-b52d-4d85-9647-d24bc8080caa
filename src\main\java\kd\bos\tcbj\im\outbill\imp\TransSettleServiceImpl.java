package kd.bos.tcbj.im.outbill.imp;

import com.kingdee.bos.util.backport.Arrays;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.exception.KDBizException;
import kd.bos.form.IFormView;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.common.enums.PcpTransBizTypeEnum;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.constants.SettleParamConstant;
import kd.bos.tcbj.im.outbill.helper.ABillServiceHelper;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import kd.bos.tcbj.im.outbill.mservice.SettleMservice;
import kd.bos.tcbj.im.outbill.mservice.TransSettleMservice;
import kd.bos.tcbj.im.util.SettleUtils;
import kd.bos.tcbj.im.vo.R;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Objects;

/**
 * 调拨结算实现类
 * @package: kd.bos.tcbj.im.outbill.imp.TransSettleServiceImpl
 * @className: TransSettleServiceImpl
 * @author: hst
 * @createDate: 2024/05/26
 * @version: v1.0
 */
public class TransSettleServiceImpl implements TransSettleMservice {

    /* 内部实例化*/
    private final static SettleMserviceImpl SETTLEMSERVICEIMPL = new SettleMserviceImpl();

    /**
     * 内部接口实现类实例
     *
     * @return
     * @createDate :
     * @createAuthor: hst
     * @updateDate :
     * @updateAuthor:
     */
    public static SettleMservice getInstance() { return SETTLEMSERVICEIMPL; }

    /**
     * 调拨中间表结算
     * @author: hst
     * @createDate  :
     * @param context 上下文
     * @param config 配置信息
     * @param bill 需结算单据
     * @return 是否成功
     */
    @Override
    public ApiResult doTransSettle_settle (HashMap context, DynamicObject config, DynamicObject bill) {
        // 生成退货组织到库存组织的出入库单
        createReturnOrgToStockOrg(context, bill, config);

        // 生成库存组织到结算组织的出入库单
        createStockOrgToSettleLevelOrg(context, bill, config);

        return null;
    }

    /**
     * 生成退货组织到库存组织的出入库单
     * @param context 上下文
     * @param bill 调拨中间表
     * @param config 配置信息
     * @author: hst
     * @createDate: 2024/05/30
     */
    private void createReturnOrgToStockOrg (HashMap context, DynamicObject bill, DynamicObject config) {
        String oriBillId = "";
        String desBillId = "";
        boolean isForward = SettleUtils.determineIsSaleOrReturn(bill);
        DynamicObject outOrg = isForward ? bill.getDynamicObject("yd_outorg") : bill.getDynamicObject("yd_inorg");
        DynamicObject inOrg = isForward ? bill.getDynamicObject("yd_inorg") : bill.getDynamicObject("yd_outorg");

        // 获取结算路径
        String key = outOrg.getString("id") + "&" + inOrg.getString("id")
                + "&" + bill.getString("yd_businesstype");
        Map<String,Object> settlePath = SettleUtils.getSettlePathByContext(context, key);
        // 退货路径
        LinkedList<String> returnOrgList = (LinkedList<String>) settlePath.get("orgReturnlList");
        Collections.reverse(returnOrgList);

        // 仓库映射
        Map<String, String> orgWarehouseMap = (Map<String, String>) context.get("warehouseMap");

        for (String returnOrgs : returnOrgList) {
            String[] keys = returnOrgs.split("&");
            String srcOrg = keys[0];
            DynamicObject srcOrgObj = getBaseDataInfo(srcOrg, "bos_org");
            String desOrg = keys[1];
            DynamicObject desOrgObj = getBaseDataInfo(desOrg, "bos_org");

            Map<String,BigDecimal> priceMap = SettleUtils.getOrgtoOrgNewPrice(Arrays.asList(new String[]{returnOrgs}),
                    bill.getDate("yd_bizdate"));

            if (returnOrgs.equals(returnOrgList.getFirst())) {
                // 先通过调拨中间表生成红字采购入库
                desBillId = createPurInFromMidTrans(bill, srcOrg, desOrg, priceMap);
                // 重置退货数量
                DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, BillTypeHelper.BILLTYPE_PURINBILL);
                DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                for(DynamicObject entry : entryCol) {
                    entry.set("returnqty", entry.getBigDecimal("qty").multiply(new BigDecimal(returnOrgList.size())));
                    entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(new BigDecimal(returnOrgList.size())));
                }
                SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存

                // 根据采购入库单（红字）创建销售出库单（红字），到此就完结了
                oriBillId = desBillId;
                String oriBillEntity = BillTypeHelper.BILLTYPE_PURINBILL;
                String desBillEntity = BillTypeHelper.BILLTYPE_SALEOUTBILL;
                // 转换规则
                String ruleId = config.getString(SettleParamConstant.INTOOUT_RE_FIELD);
                Map<String, String> opResult = createOutBill(oriBillId, oriBillEntity, desBillEntity,
                        ruleId, srcOrgObj, desOrgObj, priceMap);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                SettleUtils.resetBillWarehouse(desBillId, desBillEntity, orgWarehouseMap);
                // 重置退货数量
                backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, desBillEntity);
                entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                for(DynamicObject entry : entryCol) {
                    entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(new BigDecimal(returnOrgList.size())));
                    entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(new BigDecimal(returnOrgList.size())));
                }
                SaveServiceHelper.save(new DynamicObject[] {backInwareBill});// 保存

                oriBillId = desBillId;
            } else {
                // 根据上一级的销售出库单（红字）创建采购入库单（红字）
                String oriBillEntity = BillTypeHelper.BILLTYPE_SALEOUTBILL;
                String desBillEntity = BillTypeHelper.BILLTYPE_PURINBILL;
                String ruleId = config.getString(SettleParamConstant.ORIOUTTOIN_FIELD);
                Map<String, String> opResult = createOutBill(oriBillId, oriBillEntity, desBillEntity,
                        ruleId, srcOrgObj, desOrgObj, priceMap);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                SettleUtils.resetBillWarehouse(desBillId, desBillEntity, orgWarehouseMap);

                // 根据采购入库单（红字）创建销售出库单（红字），到此就完结了
                oriBillId = desBillId;
                oriBillEntity = BillTypeHelper.BILLTYPE_PURINBILL;
                desBillEntity = BillTypeHelper.BILLTYPE_SALEOUTBILL;
                ruleId = config.getString(SettleParamConstant.INTOOUT_RE_FIELD);
                opResult = createOutBill(oriBillId, oriBillEntity, desBillEntity,
                        ruleId, srcOrgObj, desOrgObj, priceMap);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                SettleUtils.resetBillWarehouse(desBillId, desBillEntity, orgWarehouseMap);
                oriBillId = desBillId;
            }
        }
    }

    /**
     * 生成库存组织到结算组织的出入库单
     * @param context
     * @param bill
     * @param config
     * @param config
     * @author: hst
     * @createDate: 2024/05/31
     */
    private void createStockOrgToSettleLevelOrg(HashMap context, DynamicObject bill, DynamicObject config) {
        String oriBillId = "";
        String desBillId = "";
        String bizType = bill.getString("yd_businesstype");

        boolean isForward = SettleUtils.determineIsSaleOrReturn(bill);
        DynamicObject outOrg = isForward ? bill.getDynamicObject("yd_outorg") : bill.getDynamicObject("yd_inorg");
        DynamicObject inOrg = isForward ? bill.getDynamicObject("yd_inorg") : bill.getDynamicObject("yd_outorg");

        // 获取结算路径
        String key = outOrg.getString("id") + "&" + inOrg.getString("id")
                + "&" + bill.getString("yd_businesstype");
        Map<String,Object> settlePath = SettleUtils.getSettlePathByContext(context, key);
        // 退货结算路径
        LinkedList<String> returnOrgList = (LinkedList<String>) settlePath.get("orgReturnlList");
        // 结算路径
        LinkedList<String> settleOrgList = (LinkedList<String>) settlePath.get("orgSettleList");
        // 如果是退货，反转路径
        boolean isReturn = bill.getBoolean("yd_isreturn");
        isForward = isForward || isReturn;
        if (!isForward) {
            Collections.reverse(settleOrgList);
        }

        // 仓库映射
        Map<String, String> orgWarehouseMap = (Map<String, String>) context.get("warehouseMap");

        Map<String, String> opResult = new HashMap<>();
        for (String settleOrgs : settleOrgList) {
            String[] keys = settleOrgs.split("&");
            String srcOrg = keys[0];
            DynamicObject srcOrgObj = getBaseDataInfo(srcOrg, "bos_org");
            String desOrg = keys[1];
            DynamicObject desOrgObj = getBaseDataInfo(desOrg, "bos_org");

            // 获取组织内部各个物料新的结算价格集合，根据单据业务日期获取对应日期范围内的价格
            Map<String,BigDecimal> priceMap = SettleUtils.getOrgtoOrgNewPrice(Arrays.asList(new String[]{settleOrgs}),
                    bill.getDate("yd_bizdate"));

            if (settleOrgs.equals(settleOrgList.getFirst())) {
                // [正向：调拨中间表生成销售出库单；逆向：调拨中间表生成红字采购入库]
                if (isForward) {
                    desBillId = createSaleOutFromMidTrans(bill, srcOrg, desOrg, priceMap,isForward);

                    if (returnOrgList.size() > 0) {
                        SettleUtils.resetBillWarehouse(desBillId, BillTypeHelper.BILLTYPE_SALEOUTBILL, orgWarehouseMap);
                    }
                } else {
                    desBillId = createPurInFromMidTrans(bill, srcOrg, desOrg, priceMap);
                    // 重置退货数量
                    DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, BillTypeHelper.BILLTYPE_PURINBILL);
                    DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                    for(DynamicObject entry : entryCol) {
                        entry.set("returnqty", entry.getBigDecimal("qty").multiply(new BigDecimal("2")));
                        entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(new BigDecimal("2")));
                    }
                    SaveServiceHelper.save(new DynamicObject[] {backInwareBill});
                }

                // [正向：销售出库单生成采购入库单；逆向：采购入库单（红字）生成销售出库单（红字）]
                oriBillId = desBillId;
                String oriBillEntity = isForward ? BillTypeHelper.BILLTYPE_SALEOUTBILL : BillTypeHelper.BILLTYPE_PURINBILL;
                String desBillEntity = isForward ? BillTypeHelper.BILLTYPE_PURINBILL : BillTypeHelper.BILLTYPE_SALEOUTBILL;
                String ruleId = isForward ? config.getString(SettleParamConstant.OUTTOIN_FIELD)
                        : config.getString(SettleParamConstant.INTOOUT_RE_FIELD);
                // 对单据进行保存提交审核，拿到成功审核的单据ID
                opResult = createOutBill(oriBillId,oriBillEntity,desBillEntity,ruleId,srcOrgObj,desOrgObj,priceMap);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                SettleUtils.resetBillWarehouse(desBillId, desBillEntity, orgWarehouseMap);
                if (!isForward) {
                    // 第一组则重置多少倍退货数量
                    DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, desBillEntity);
                    DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                    for(DynamicObject entry : entryCol) {
                        entry.set("remainreturnqty", entry.getBigDecimal("qty").abs().multiply(new BigDecimal("2")));
                        entry.set("remainreturnbaseqty", entry.getBigDecimal("baseqty").abs().multiply(new BigDecimal("2")));
                    }
                    SaveServiceHelper.save(new DynamicObject[] {backInwareBill});
                }
                oriBillId = desBillId;
            }

            // 不是第一组
            if (!settleOrgs.equals(settleOrgList.getFirst())) {
                // [正向：采购入库单生成销售出库单；逆向：销售出库单（红字）生成采购入库单（红字）]
                String oriBillEntity = isForward ? BillTypeHelper.BILLTYPE_PURINBILL : BillTypeHelper.BILLTYPE_SALEOUTBILL;
                String desBillEntity = isForward ? BillTypeHelper.BILLTYPE_SALEOUTBILL : BillTypeHelper.BILLTYPE_PURINBILL;
                String ruleId = isForward ? config.getString(SettleParamConstant.INTOOUT_FIELD) : config.getString(SettleParamConstant.ORIOUTTOIN_FIELD);
                opResult = createOutBill(oriBillId, oriBillEntity, desBillEntity, ruleId, srcOrgObj, desOrgObj, priceMap);
                desBillId = opResult.get("billId");
                // 重置源单上分录的仓库为组织对应的仓库
                if (!isForward) {
                    SettleUtils.resetBillWarehouse(desBillId, desBillEntity, orgWarehouseMap);
                    if (!settleOrgs.equals(settleOrgList.getLast())) {
                        // 重置退货数量
                        DynamicObject backInwareBill = BusinessDataServiceHelper.loadSingle(desBillId, desBillEntity);
                        DynamicObjectCollection entryCol = backInwareBill.getDynamicObjectCollection("billentry");
                        for (DynamicObject entry : entryCol) {
                            entry.set("returnqty", entry.getBigDecimal("qty").multiply(new BigDecimal("2")));
                            entry.set("returnbaseqty", entry.getBigDecimal("baseqty").multiply(new BigDecimal("2")));
                        }
                        SaveServiceHelper.save(new DynamicObject[]{backInwareBill});// 保存
                    }
                }

                // [正向：销售出库单生成采购入库单；逆向：采购入库单（红字）生成销售出库单（红字）]
                oriBillId = desBillId;
                oriBillEntity = isForward ? BillTypeHelper.BILLTYPE_SALEOUTBILL : BillTypeHelper.BILLTYPE_PURINBILL;
                desBillEntity = isForward ? BillTypeHelper.BILLTYPE_PURINBILL : BillTypeHelper.BILLTYPE_SALEOUTBILL;
                ruleId = isForward ? config.getString(SettleParamConstant.OUTTOIN_FIELD) : config.getString(SettleParamConstant.INTOOUT_RE_FIELD);
                opResult = createOutBill(oriBillId, oriBillEntity, desBillEntity, ruleId, srcOrgObj, desOrgObj, priceMap);
                desBillId = opResult.get("billId");
                // 若不是最后一张单
                if (!settleOrgs.equals(settleOrgList.getLast())) {
                    // 重置源单上分录的仓库为组织对应的仓库
                    SettleUtils.resetBillWarehouse(desBillId, desBillEntity, orgWarehouseMap);
                    if (!isForward) {
                        // 退货数量需要翻倍
                        DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(desBillId, desBillEntity);
                        DynamicObjectCollection saleoutEntryCol = resetBill.getDynamicObjectCollection("billentry");
                        for (DynamicObject resetSaleEntry : saleoutEntryCol) {
                            resetSaleEntry.set("remainreturnqty", resetSaleEntry.getBigDecimal("qty").abs().multiply(new BigDecimal("2")));
                            resetSaleEntry.set("remainreturnbaseqty", resetSaleEntry.getBigDecimal("baseqty").abs().multiply(new BigDecimal("2")));
                        }
                        SaveServiceHelper.save(new DynamicObject[]{resetBill});// 保存
                    }
                }
                oriBillId = desBillId;
            }
            // 若是最后一组，重置仓库为调拨出入库仓库
            if (settleOrgs.equals(settleOrgList.getLast())) {
                String billType = isForward ? BillTypeHelper.BILLTYPE_PURINBILL : BillTypeHelper.BILLTYPE_SALEOUTBILL;
                DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(oriBillId, billType);

                // [正向：重置采购入库单仓库为调拨单调入仓库；逆向：重置销售出库单出库为调入仓库]
                String wareHouseId = bill.getString("yd_inwarehouse.id");
                DynamicObject wareHouse = BusinessDataServiceHelper.loadSingle(wareHouseId, "bd_warehouse");
                DynamicObjectCollection resetEntryCol = resetBill.getDynamicObjectCollection("billentry");
                for (DynamicObject resetSaleEntry : resetEntryCol) {
                    resetSaleEntry.set("warehouse", wareHouse);
                }
                SaveServiceHelper.save(new DynamicObject[]{resetBill});// 保存
            }
        }
    }

    /**
     * 通过调拨中间表生成采购入库单
     * @param bill
     * @param outOrgId
     * @param inOrgId
     * @param priceMap
     * @return
     * @author: hst
     * @createDate: 2024/05/30
     */
    private String createPurInFromMidTrans(DynamicObject bill, String outOrgId, String inOrgId, Map<String,BigDecimal> priceMap) {
        IFormView purView = ABillServiceHelper.createAddView(BillTypeHelper.BILLTYPE_PURINBILL);

        try {
            IDataModel purModel = purView.getModel();

            // 获取组织间校验价格
            String key = outOrgId + "&" + inOrgId;

            DynamicObject inOrg = BusinessDataServiceHelper.loadSingle(inOrgId,"bos_org");
            // 组织信息
            purModel.setValue("org", inOrg);
            purModel.setValue("bizorg", inOrg);
            purModel.setValue("bizdept", inOrg);
            purModel.setValue("dept", inOrg);
            purModel.setValue("yd_settleorg", inOrg);

            // 单据类型（标准采购入库单）
            purModel.setItemValueByNumber("billtype", "im_PurInBill_STD_BT_S");

            if (Objects.isNull(purModel.getValue("currency"))) {
                // 获取核算组织本位币
                DynamicObject accountingSys = BusinessDataServiceHelper.loadSingle("bd_accountingsys_base",
                        new QFilter[]{new QFilter("baseacctorg.id", QFilter.equals,inOrgId)});

                if (Objects.nonNull(accountingSys)) {
                    purModel.setValue("currency",accountingSys.getDynamicObject("basecurrrency"));
                    purModel.setValue("settlecurrency",accountingSys.getDynamicObject("basecurrrency"));
                    purModel.setValue("exratetable",accountingSys.getDynamicObject("exratetable"));
                    purModel.setValue("exratedate",bill.getDate("yd_bizdate"));
                }
            }

            // 供应商
            DynamicObject toSupplierInfo = SettleUtils.getSupplierByInternalCompany(outOrgId);
            purModel.setValue("supplier", toSupplierInfo);

            // 已入库组织作为客户，获取税率
            DynamicObject customerInfo = SettleUtils.getCustomerByInternalCompany(inOrgId);

            // 业务类型（物料类采购退货）
            purModel.setItemValueByNumber("biztype", "1101");
            // 库存事务（普通采购退、补、换货）
            purModel.setItemValueByNumber("invscheme", "1101");
            // 业务日期
            purModel.setValue("biztime", bill.getDate("yd_bizdate"));
            // 备注
            purModel.setValue("comment", bill.getString("yd_description"));
            // 单据生成类型（手工生成：0 / 导入生成：1 / 后台生成：2 / webApi生成：3）
            purModel.setValue("billcretype", 2);
            // 上游单号 => 外部系统单号
            purModel.setValue("yd_platformorderno", bill.getString("yd_platformorderno"));
            purModel.setValue("yd_ocsresultnumber", bill.getString("yd_ocs_dsnumber"));
            // PCP业务类型
            purModel.setValue("yd_businesstype", bill.getString("yd_businesstype"));
            // 采购订单ID
            purModel.setValue("yd_purorderid", bill.getString("yd_purorderid"));
            // 采购退货申请单ID
            purModel.setValue("yd_purreturnid", bill.getString("yd_purreturnid"));
            // 销售订单ID
            purModel.setValue("yd_saleorderid", bill.getString("yd_saleorderid"));
            // 销售退货申请单ID
            purModel.setValue("yd_salereturnid", bill.getString("yd_salereturnid"));
            // 同步来源系统
            purModel.setValue("yd_tbly", "OCS".equals(bill.getString("yd_sourcefunction")) ?
                    "4" : bill.getString("yd_sourcefunction"));
            // 来源单据类型
            purModel.setValue("yd_sourcebilltype", "4");

            DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("entryentity");
            purModel.deleteEntryData("billentry");
            for (int i = 0; i < entryCol.size(); i++) {
                int index = purModel.createNewEntryRow("billentry");
                DynamicObject entryInfo = entryCol.get(i);
                // 物料
                purModel.setItemValueByNumber("material", entryInfo.getString("yd_material.number"), index);
                // E3批号
                purModel.setValue("yd_ph", entryInfo.getString("yd_batchno"), index);
                // 生产日期
                purModel.setValue("yd_scrq", entryInfo.getDate("yd_probegindate"), index);
                // 到期日
                purModel.setValue("yd_dqr", entryInfo.getDate("yd_proenddate"), index);
                // 数量
                BigDecimal qty = entryInfo.getBigDecimal("yd_quantity");
                // 单价
                BigDecimal taxPrice = priceMap.get(key + "&" + entryInfo.getString("yd_material.id"));
                // 总金额
                BigDecimal taxAmount = taxPrice.multiply(qty).setScale(2, 4);
                purModel.setItemValueByNumber("taxrateid", customerInfo.getString("taxrate.number"), index);
                purModel.setValue("qty", qty, index);
                purModel.setValue("priceandtax", taxPrice, index);
                purModel.setValue("amountandtax", taxAmount, index);
                // 单价为0标记为赠品
                if (taxPrice.compareTo(BigDecimal.ZERO) == 0) {
                    purModel.setValue("ispresent", true, index);
                } else {
                    purModel.setValue("ispresent", false, index);
                }
                // 仓库
                purModel.setValue("warehouse", bill.getDynamicObject("yd_outwarehouse"), index);
                // 来源系统单据分录ID
                purModel.setValue("srcsysbillentryid", entryInfo.getPkValue(), index);
                // 来源系统单据编号
                purModel.setValue("srcsysbillno", bill.getString("billno"), index);
                // 来源系统单据ID
                purModel.setValue("srcsysbillid", bill.getPkValue(), index);
                // 来源单据ID
                purModel.setValue("srcbillid", bill.getPkValue(), index);
                // 来源单据行ID
                purModel.setValue("srcbillentryid", entryInfo.getPkValue(), index);
                // 来源单据分录序号
                purModel.setValue("srcbillentryseq", entryInfo.get("seq"), index);
                // 来源单据分录序号
                purModel.setValue("srcbillnumber", bill.getString("billno"), index);
                // 来源分录id
                purModel.setValue("yd_sourceentryid", entryInfo.getString("id"), index);
                // 采购订单分录ID
                purModel.setValue("yd_purorderentryid", entryInfo.getString("yd_purorderentryid"), index);
                // 采购退货申请单分录ID
                purModel.setValue("yd_purreturnentryid", entryInfo.getString("yd_purreturnentryid"), index);
                // 销售订单分录ID
                purModel.setValue("yd_saleorderentryid", entryInfo.getString("yd_saleorderentryid"), index);
                // 销售退货申请单分录ID
                purModel.setValue("yd_salereturnentryid", entryInfo.getString("yd_salereturnentryid"), index);
                // 备注
                purModel.setValue("entrycomment", entryInfo.getString("yd_description_e"), index);
            }

            // 对生成的销售出库单进行数据保存、提交、审批
            R result = PCPServiceHelper.saveAndAuditBill(BillTypeHelper.BILLTYPE_PURINBILL, purView);
            if (result.getCode() == 0) {
                // 设置关联关系
                String billNumber = (String) purModel.getValue("billno");
                // 保存下游单据编码到单据中
                String oriNumber = bill.getString("yd_nextbillno");
                bill.set("yd_issettle", true);
                bill.set("yd_ishasnext", true);
                bill.set("yd_nextbillno", StringUtils.isNotBlank(oriNumber) ? oriNumber + "," + billNumber : billNumber);
                bill.set("yd_settleerror", "");

                BizHelper.save(bill);
                SettleUtils.saveRelation(bill.getLong("id"), purModel.getDataEntity().getLong("id"),
                        BillTypeHelper.BILLTYPE_MIDTRANSBILL,BillTypeHelper.BILLTYPE_PURINBILL);

                return result.get("billId").toString();
            } else {
                if (!"0".equals(purModel.getDataEntity().getString("id"))) {
                    bill.set("yd_issettle", true);
                    BizHelper.save(bill);
                    SettleUtils.saveRelation(bill.getLong("id"), purModel.getDataEntity().getLong("id"),
                            BillTypeHelper.BILLTYPE_MIDTRANSBILL,BillTypeHelper.BILLTYPE_PURINBILL);
                }
                throw new KDBizException(result.getMessage());
            }
        } finally {
            purView.close();
        }
    }

    /**
     * 通过调拨中间表生成销售出库单
     * @param bill
     * @param outOrgId
     * @param inOrgId
     * @param priceMap
     * @return
     * @author: hst
     * @createDate: 2024/05/30
     */
    private String createSaleOutFromMidTrans(DynamicObject bill, String outOrgId, String inOrgId, Map<String,BigDecimal> priceMap,
                                             boolean isForward) {
        IFormView saleView = ABillServiceHelper.createAddView(BillTypeHelper.BILLTYPE_SALEOUTBILL);
        try {
            IDataModel saleModel = saleView.getModel();

            // 获取组织间校验价格
            String key = outOrgId + "&" + inOrgId;

            // 组织信息
            saleModel.setItemValueByID("org", outOrgId);
            saleModel.setItemValueByID("bizorg", outOrgId);
            saleModel.setItemValueByID("bizdept", outOrgId);
            saleModel.setItemValueByID("dept", outOrgId);
            saleModel.setItemValueByID("yd_settleorg", outOrgId);

            // 单据类型（标准销售出库单）
            saleModel.setItemValueByNumber("billtype", "im_SalOutBill_STD_BT_S");

            // 客户
            DynamicObject toCustomerInfo = SettleUtils.getCustomerByInternalCompany(inOrgId);
            saleModel.setValue("customer", toCustomerInfo);
            saleModel.setValue("yd_dzdkh", toCustomerInfo); // 对账客户


            // 业务类型 [正向（物料类销售）:逆向（物料类销售退货）]
            saleModel.setItemValueByNumber("biztype", isForward ? "210" : "2101");
            // 库存事务 [正向（物料类销售）:逆向（物料类销售退货）]
            saleModel.setItemValueByNumber("invscheme", isForward ? "210" : "2101");
            // 业务日期
            saleModel.setValue("biztime", bill.getDate("yd_bizdate"));
            // 备注
            saleModel.setValue("comment", bill.getString("yd_description"));
            // 单据生成类型（手工生成：0 / 导入生成：1 / 后台生成：2 / webApi生成：3）
            saleModel.setValue("billcretype", 2);
            // 上游单号 => 外部系统单号
            saleModel.setValue("yd_platformorderno", bill.getString("yd_platformorderno"));
            saleModel.setValue("yd_ocsresultnumber", bill.getString("yd_ocs_dsnumber"));
            // PCP业务类型
            saleModel.setValue("yd_businesstype", bill.getString("yd_businesstype"));
            // 采购订单ID
            saleModel.setValue("yd_purorderid", bill.getString("yd_purorderid"));
            // 采购退货申请单ID
            saleModel.setValue("yd_purreturnid", bill.getString("yd_purreturnid"));
            // 销售订单ID
            saleModel.setValue("yd_saleorderid", bill.getString("yd_saleorderid"));
            // 销售退货申请单ID
            saleModel.setValue("yd_salereturnid", bill.getString("yd_salereturnid"));
            // 同步来源系统
            saleModel.setValue("yd_tbly", "OCS".equals(bill.getString("yd_sourcefunction")) ?
                    "4" : bill.getString("yd_sourcefunction"));
            // 来源单据类型
            saleModel.setValue("yd_sourcebilltype", "4");

            DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("entryentity");
            saleModel.deleteEntryData("billentry");
            for (int i = 0; i < entryCol.size(); i++) {
                DynamicObject entryInfo = entryCol.get(i);
                int index = saleModel.createNewEntryRow("billentry");

                // 物料
                saleModel.setItemValueByNumber("material", entryInfo.getString("yd_material.number"), index);
                // E3批号
                saleModel.setValue("yd_ph", entryInfo.getString("yd_batchno"), index);
                // 生产日期
                saleModel.setValue("yd_scrq", entryInfo.getDate("yd_probegindate"), index);
                // 到期日
                saleModel.setValue("yd_dqr", entryInfo.getDate("yd_proenddate"), index);
                // 数量
                BigDecimal qty = entryInfo.getBigDecimal("yd_quantity");
                // 单价
                BigDecimal taxPrice = priceMap.get(key + "&" + entryInfo.getString("yd_material.id"));
                // 总金额
                BigDecimal taxAmount = taxPrice.multiply(qty).setScale(2, 4);
                saleModel.setItemValueByNumber("taxrateid", toCustomerInfo.getString("taxrate.number"), index);
                saleModel.setValue("qty", qty, index);
                saleModel.setValue("priceandtax", taxPrice, index);
                saleModel.setValue("amountandtax", taxAmount, index);
                // 单价为0标记为赠品
                if (taxPrice.compareTo(BigDecimal.ZERO) == 0) {
                    saleModel.setValue("ispresent", true, index);
                } else {
                    saleModel.setValue("ispresent", false, index);
                }
                // 仓库
                saleModel.setValue("warehouse", bill.getDynamicObject("yd_outwarehouse"), index);
                // 来源系统单据分录ID
                saleModel.setValue("srcsysbillentryid", entryInfo.getPkValue(), index);
                // 来源系统单据编号
                saleModel.setValue("srcsysbillno", bill.getString("billno"), index);
                // 来源系统单据ID
                saleModel.setValue("srcsysbillid", bill.getPkValue(), index);
                // 来源单据ID
                saleModel.setValue("srcbillid", bill.getPkValue(), index);
                // 来源单据行ID
                saleModel.setValue("srcbillentryid", entryInfo.getPkValue(), index);
                // 来源单据分录序号
                saleModel.setValue("srcbillentryseq", entryInfo.get("seq"), index);
                // 来源单据分录序号
                saleModel.setValue("srcbillnumber", bill.getString("billno"), index);
                // 来源分录id
                saleModel.setValue("yd_sourceentryid", entryInfo.getString("id"), index);
                // 采购订单分录ID
                saleModel.setValue("yd_purorderentryid", entryInfo.getString("yd_purorderentryid"), index);
                // 采购退货申请单分录ID
                saleModel.setValue("yd_purreturnentryid", entryInfo.getString("yd_purreturnentryid"), index);
                // 销售订单分录ID
                saleModel.setValue("yd_easorderentryid", entryInfo.getString("yd_saleorderentryid"), index);
                // 销售退货申请单分录ID
                saleModel.setValue("yd_salereturnentryid", entryInfo.getString("yd_salereturnentryid"), index);
                // 备注
                saleModel.setValue("entrycomment", entryInfo.getString("yd_description_e"), index);
            }

            // 对生成的销售出库单进行数据保存、提交、审批
            R result = PCPServiceHelper.saveAndAuditBill(BillTypeHelper.BILLTYPE_SALEOUTBILL, saleView);
            if (result.getCode() == 0) {
                // 设置关联关系
                String billNumber = (String) saleModel.getValue("billno");
                // 保存下游单据编码到单据中
                String oriNumber = bill.getString("yd_nextbillno");
                bill.set("yd_issettle", true);
                bill.set("yd_ishasnext", true);
                bill.set("yd_nextbillno", StringUtils.isNotBlank(oriNumber) ? oriNumber + "," + billNumber : billNumber);
                bill.set("yd_settleerror", "");

                BizHelper.save(bill);
                SettleUtils.saveRelation(bill.getLong("id"), saleModel.getDataEntity().getLong("id"),
                        BillTypeHelper.BILLTYPE_MIDTRANSBILL, BillTypeHelper.BILLTYPE_SALEOUTBILL);

                return result.get("billId").toString();
            } else {
                if (!"0".equals(saleModel.getDataEntity().getString("id"))) {
                    bill.set("yd_issettle", true);
                    BizHelper.save(bill);
                    SettleUtils.saveRelation(bill.getLong("id"), saleModel.getDataEntity().getLong("id"),
                            BillTypeHelper.BILLTYPE_MIDTRANSBILL,BillTypeHelper.BILLTYPE_SALEOUTBILL);
                }
                throw new KDBizException(result.getMessage());
            }
        } finally {
            saleView.close();
        }
    }

    /**
     * 下推生成销售出库单/采购入库单
     * @param oriBillId     下推单据id
     * @param oriBillEntity 下推单据类型
     * @param desBillEntity 目标单据类型
     * @param ruleId        转换路线标识ID
     * @param srcOrg        源组织
     * @param destOrg       目标组织
     * @param matPriceMap   内部校验价格
     * @return
     * @author: hst
     * @createDate: 2024/05/29
     */
    private Map<String, String> createOutBill(String oriBillId, String oriBillEntity, String desBillEntity, String ruleId,
                                                     DynamicObject srcOrg, DynamicObject destOrg, Map<String, BigDecimal> matPriceMap) {
        DynamicObject tempTargetBillObj = SettleUtils.pushBill(oriBillId, oriBillEntity, desBillEntity, ruleId);

        if (BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(oriBillEntity) && BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(desBillEntity)
                || BillTypeHelper.BILLTYPE_PURINBILL.equals(oriBillEntity) && BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(desBillEntity)) {
            tempTargetBillObj.set("org", srcOrg);
            tempTargetBillObj.set("bizorg", srcOrg);
            tempTargetBillObj.set("bizdept", srcOrg);
            tempTargetBillObj.set("dept", srcOrg);
            tempTargetBillObj.set("yd_settleorg", srcOrg);

            // 根据编码获取后者组织作为客户
            QFilter cusFilter = new QFilter("internal_company.number", QCP.equals,
                    destOrg.getString("number"));
            cusFilter.and(new QFilter("status", QCP.equals, "C"));
            DynamicObject customer = BusinessDataServiceHelper.loadSingle("bd_customer",
                    "id,name,number,yd_channel,taxrate", cusFilter.toArray());

            tempTargetBillObj.set("customer", customer);
            tempTargetBillObj.set("yd_cuschannel", customer.getDynamicObject("yd_channel"));  // 客户所属渠道
            DynamicObjectCollection outEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");

            // 根据客户重新查找税率
            DynamicObject cusTaxRateObj = customer.getDynamicObject("taxrate");
            cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
            String key = srcOrg.getString("id") + "&" + destOrg.getString("id");
            for (DynamicObject entryObj : outEntryCol) {
                entryObj.set("entrysettleorg", srcOrg);  // 结算组织
                entryObj.set("outowner", srcOrg);  // 出库货主
                entryObj.set("outkeeper", srcOrg);  // 出库保管者
                entryObj.set("settlecustomer", customer);  // 应收客户
                entryObj.set("payingcustomer", customer);  // 付款客户
                entryObj.set("reccustomer", customer);  // 收货客户

                // 根据客户重置税率
                entryObj.set("taxrateid", cusTaxRateObj);
                entryObj.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));

                // 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
                String materialId = entryObj.getString("material.masterid.id");
                if (matPriceMap.containsKey(key + "&" + materialId)) {
                    BigDecimal newPrice = matPriceMap.get(key + "&" + materialId);
                    entryObj.set("priceandtax", newPrice);
                    SettleUtils.changePriceAndTax(tempTargetBillObj, entryObj);
                    // 单价为0标记为赠品
                    if (matPriceMap.get(key + "&" + materialId).compareTo(BigDecimal.ZERO) == 0) {
                        entryObj.set("ispresent", true);
                    } else {
                        entryObj.set("ispresent", false);
                    }
                }
            }
        } else if (BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(oriBillEntity) && BillTypeHelper.BILLTYPE_PURINBILL.equals(desBillEntity)) {
            tempTargetBillObj.set("org", destOrg);
            tempTargetBillObj.set("bizorg", destOrg);
            tempTargetBillObj.set("bizdept", destOrg);
            tempTargetBillObj.set("dept", destOrg);
            tempTargetBillObj.set("yd_settleorg", destOrg);

            // 获取供应商
            QFilter supFilter = new QFilter("internal_company.number", QCP.equals,
                    srcOrg.getString("number"));
            supFilter.and(new QFilter("status", QCP.equals, "C"));
            DynamicObject supplier = BusinessDataServiceHelper.loadSingle("bd_supplier",
                    "id,name,number", supFilter.toArray());

            // 单据头.供应商、物料明细.供货供应商、物料明细.出票供应商、物料明细.收款供应商、
            tempTargetBillObj.set("supplier", supplier);
            DynamicObjectCollection inEntryCol = tempTargetBillObj.getDynamicObjectCollection("billentry");
            // 库存组织作为客户
            QFilter cusFilter = new QFilter("internal_company.number", QCP.equals, destOrg.getString("number"));
            cusFilter.and(new QFilter("status", QCP.equals, "C"));
            DynamicObject customer = BusinessDataServiceHelper.loadSingle("bd_customer",
                    "id,name,number,yd_channel,taxrate", cusFilter.toArray());

            DynamicObject cusTaxRateObj = customer.getDynamicObject("taxrate");
            cusTaxRateObj = BusinessDataServiceHelper.loadSingle(cusTaxRateObj.getPkValue(), "bd_taxrate");
            String key = srcOrg.getString("id") + "&" + destOrg.getString("id");
            for (DynamicObject entryObj : inEntryCol) {
                entryObj.set("owner", destOrg);
                entryObj.set("keeper", destOrg);
                entryObj.set("entrysettleorg", destOrg);
                entryObj.set("entryreqorg", destOrg);
                entryObj.set("providersupplier", supplier);
                entryObj.set("invoicesupplier", supplier);
                entryObj.set("receivesupplier", supplier);

                // 根据客户重置税率
                entryObj.set("taxrateid", cusTaxRateObj);
                entryObj.set("taxrate", cusTaxRateObj.getBigDecimal("taxrate"));

                // 根据结算关系yd_pricerelbill获取物料价格，再重算金额，
                String materialId = entryObj.getString("material.masterid.id");
                if (matPriceMap.containsKey(key + "&" + materialId)) {
                    BigDecimal newPrice = matPriceMap.get(key + "&" + materialId);
                    entryObj.set("priceandtax", newPrice);
                    SettleUtils.changePriceAndTax(tempTargetBillObj, entryObj);
                    // 单价为0标记为赠品
                    if (matPriceMap.get(key + "&" + materialId).compareTo(BigDecimal.ZERO) == 0) {
                        entryObj.set("ispresent", true);
                    } else {
                        entryObj.set("ispresent", false);
                    }
                }
            }
        }

        // 对单据进行保存提交审核，拿到成功审核的单据ID
        return SettleUtils.operateTargetBill(desBillEntity, tempTargetBillObj);
    }

    /**
     * 获取基础资料信息
     * @author: hst
     * @createDate: 2024/08/02
     */
    public static DynamicObject getBaseDataInfo (String id, String entityName) {
        return BusinessDataServiceHelper.loadSingle(id, "bos_org");
    }
}
