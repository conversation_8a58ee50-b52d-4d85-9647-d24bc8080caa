package kd.bos.tcbj.im.outbill.imp;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import kd.bos.tcbj.im.helper.ABillServiceHelper;
import org.apache.commons.lang3.StringUtils;

import json.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.operate.result.IOperateInfo;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDException;
import kd.bos.form.IFormView;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.outbill.helper.WholeSaleReturnBillMserviceHelper;
import kd.bos.tcbj.im.outbill.mservice.WholeSaleReturnBillMservice;
import kd.bos.tcbj.im.transbill.service.E3Service;
import kd.bos.tcbj.im.util.BotpUtils;

/**
* @auditor yanzuwei
* @date 2022年1月10日
* 
*/
public class WholeSaleReturnBillMserviceImpl implements WholeSaleReturnBillMservice {
	
	/**
	 * 内部实例化
	 */
	private final static WholeSaleReturnBillMserviceImpl wholeSaleReturnBillMserviceImpl = new WholeSaleReturnBillMserviceImpl();

	/**
	 * (构造函数：私有化的批发退货单内部接口实现类)
	 * 
	 * @createDate  : 2021-12-08
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	public WholeSaleReturnBillMserviceImpl() {
		
	}
	
	/**
	 * 描述：获取批发退货单内部接口实现类实例
	 * 
	 * @createDate  : 2021-12-08
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @return 批发退货单内部接口实现类
	 */
	public static WholeSaleReturnBillMservice getInstance() {
		return wholeSaleReturnBillMserviceImpl;
	}
	
	/**
	 * 描述：查询E3批发退货单功能
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	@Override
	public ApiResult getWholeSaleReturnBill(JSONObject params) {
		System.out.println(111);
		
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			E3Service e3Service=new E3Service();
			e3Service.getWholeSaleRetNoticeData(params);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getCause() + e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：刷新单据数据
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param idSet 接口参数
	 * @return 是否成功
	 */
	@Override
	public ApiResult refreshBill(Set<String> idSet) {
		System.out.println(333);
		
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {

			// update by hst 2023/12/20 是否增加维度
			boolean isSingle = false;
			DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
					"name", new QFilter("number", QCP.equals, "E3_IS_SINGLE").toArray());
			if (Objects.nonNull(param)
					&& "true".equalsIgnoreCase(param.getString("name"))) {
				isSingle = true;
			}

			for(String billId : idSet) {
				StringBuffer errorMsg = new StringBuffer();
				DynamicObject oriBillObj = BusinessDataServiceHelper.loadSingle(billId, "yd_wholesalereturnbill");
				// 渠道是否存在映射
				String oriChannelNo = oriBillObj.getString("yd_channelno");
				QFilter orgMapFilter = new QFilter("billstatus", QCP.equals, "C");
				orgMapFilter.and(new QFilter("entryentity.yd_orichannelno", QCP.equals, oriChannelNo));
				DynamicObjectCollection orgMapCol = QueryServiceHelper.query("yd_channeltoorg", 
						"entryentity.yd_orichannelno e3num,entryentity.yd_org.id orgid,entryentity.yd_org.number orgNum,entryentity.yd_org.name orgName ", orgMapFilter.toArray());
				if (orgMapCol.size() == 0) {
					errorMsg.append("E3渠道编码"+oriChannelNo+"缺少E3渠道组织映射关系！");
					oriBillObj.set("yd_noorg", true);
				} else {
					oriBillObj.set("yd_noorg", false);
					oriBillObj.set("yd_orgnum", orgMapCol.get(0).getString("orgNum"));
					oriBillObj.set("yd_orgname", orgMapCol.get(0).getString("orgName"));
				}
				
				// 客户是否存在
				String oriCustomerNo = oriBillObj.getString("yd_customerno");
				QFilter cusFilter = new QFilter("number", QCP.equals, oriCustomerNo);
				DynamicObjectCollection cusCol = QueryServiceHelper.query("bd_customer", "id,number,enable", cusFilter.toArray());
				if (cusCol.size() == 0) {
					errorMsg.append("E3客户编码"+oriCustomerNo+"不存在对应的中台客户！");
					oriBillObj.set("yd_nocustomer", true);
				} else {
					oriBillObj.set("yd_nocustomer", false);
				}
				
				// 仓库是否存在
				String oriWarehouseNo = oriBillObj.getString("yd_warehouseno");
				QFilter wsMapFilter = new QFilter("yd_combofield_pt", QCP.equals, 1);
				wsMapFilter.and(new QFilter("billstatus", QCP.equals, "C"));
				wsMapFilter.and(new QFilter("yd_entryentity.yd_textfield", QCP.equals, oriWarehouseNo));
				DynamicObjectCollection wsMapCol = QueryServiceHelper.query("yd_ckdygx", 
						"yd_entryentity.yd_textfield e3num,yd_entryentity.yd_basedatafield.id warehouseid,yd_entryentity.yd_basedatafield.number warehousenum ", wsMapFilter.toArray());
				if (wsMapCol.size() == 0) {
					errorMsg.append("E3仓库编码"+oriWarehouseNo+"缺少仓库映射关系！");
					oriBillObj.set("yd_nowarehouse", true);
				} else {
					// 判断仓库是否已启用，已审核
					String wsId = wsMapCol.get(0).getString("warehouseid");
					QFilter wsFilter = new QFilter("enable", QCP.equals, "1");
					wsFilter.and(new QFilter("status", QCP.equals, "C"));
					wsFilter.and(new QFilter("id", QCP.equals, wsId));
					if (QueryServiceHelper.exists("bd_warehouse", wsFilter.toArray())) {
						oriBillObj.set("yd_nowarehouse", false);
						oriBillObj.set("yd_finalwarehouseno", wsMapCol.get(0).getString("warehousenum"));
					} else {
						errorMsg.append("仓库"+oriWarehouseNo+"为非启用或非已审核状态！");
						oriBillObj.set("yd_nowarehouse", true);
					}
				}
				
				// 是否排除直营店的仓库yd_excludewarehouse
			    QFilter excludeWsFilter = new QFilter("yd_lx", QCP.equals, 2);
			    excludeWsFilter.and(new QFilter("billstatus", QCP.equals, "C"));
			    excludeWsFilter.and(new QFilter("yd_entryentity.yd_textfield_bm", QCP.equals, oriWarehouseNo));
			    if (QueryServiceHelper.exists("yd_pcck", excludeWsFilter.toArray())) {
			    	oriBillObj.set("yd_excludewarehouse", true);
			    }
				
				// 物料是否存在
				DynamicObjectCollection oriEnCol = oriBillObj.getDynamicObjectCollection("entryentity");
				for (DynamicObject oriEn : oriEnCol) {
					String oriMatNum = oriEn.getString("yd_goodsnum");

					// 在排除物料表中的不需要处理并进行标记
					QFilter excludeFilter = new QFilter("yd_entryentity.yd_textfield_bm", QCP.equals, oriMatNum);
					excludeFilter.and(new QFilter("yd_lx", QCP.equals, "2"));
					if (QueryServiceHelper.exists("yd_pcwl", excludeFilter.toArray())) {
						oriEn.set("yd_beexclude", true);
						continue;
					}

					//update by hst 2023/11/22 先查找主商品拆分表中ToB类型的是否已维护组装品信息
					QFilter matComFilter = new QFilter("entryentity.yd_mainmatnum", QCP.equals, oriMatNum);
					matComFilter.and(new QFilter("billstatus", QCP.equals, "C"));
					// update by hst 2023/12/20 增加是否单品判断
					if (isSingle) {
						matComFilter.and(new QFilter("entryentity.yd_issingle", QFilter.equals, false));
					}
					// ToB类型
					matComFilter.and(new QFilter("entryentity.yd_mattype", QFilter.equals, "1"));
					DynamicObjectCollection matComCol = QueryServiceHelper.query("yd_matcombination",
							"id billId,entryentity.id enId,entryentity.yd_subentryentity.id subEnId,entryentity.yd_subentryentity.yd_material.number matNum,"
									+ "entryentity.yd_subentryentity.yd_qty qty",
							matComFilter.toArray());
					// update by hst 2023/12/20 增加单品判断
					if (matComCol.size() > 0) {
						if (Objects.nonNull(param) && !isSingle && matComCol.size() == 1) {
							if (matComCol.get(0).getInt("qty") > 1) {
								oriEn.set("yd_combinationtype", "1");  // 组装的方式
								oriEn.set("yd_materialno", matComCol.get(0).getString("matNum"));
								continue;
							}
						} else {
							oriEn.set("yd_combinationtype", "1");  // 组装的方式
							String matNums = "";
							for (DynamicObject matComObj : matComCol) {
								matNums += "," + matComObj.getString("matNum");
							}
							oriEn.set("yd_materialno", matNums);
							continue;
						}
					}
					QFilter goodsMapFilter = new QFilter("yd_combofield_pt", QCP.equals, 1);  // E3平台
					goodsMapFilter.and(new QFilter("billstatus", QCP.equals, "C"));
					goodsMapFilter.and(new QFilter("yd_entryentity.yd_textfield", QCP.equals, oriMatNum));
					DynamicObjectCollection goodsMapCol = QueryServiceHelper.query("yd_wldygx",
							"yd_entryentity.yd_textfield e3num,yd_entryentity.yd_basedatafield.id materialid,yd_entryentity.yd_basedatafield.number materialNum", goodsMapFilter.toArray());
					if (goodsMapCol.size() > 0) {
						oriEn.set("yd_materialno", goodsMapCol.get(0).getString("materialNum"));
						oriBillObj.set("yd_nomaterial", false);
						oriEn.set("yd_combinationtype", "2");  // 组装的方式
					} else {
						// 根据助记码再查一遍
						QFilter matFilter = new QFilter("status", QCP.equals, "C");
						matFilter.and(new QFilter("helpcode", QCP.equals, oriMatNum));
						DynamicObjectCollection matCol = QueryServiceHelper.query("bd_material", "id,number", matFilter.toArray());
						if (matCol.size() == 1) {
							oriEn.set("yd_materialno", matCol.get(0).getString("number"));
							oriEn.set("yd_combinationtype", "3");  // 组装的方式
						} else if (matCol.size() == 0) {
							oriBillObj.set("yd_nomaterial", true);
							errorMsg.append("E3物料编码" + oriMatNum + "缺少物料对应关系！");
						} else if (matCol.size() > 1) {
							oriBillObj.set("yd_matrepeat", true);
							errorMsg.append("E3物料助记码" + oriMatNum + "存在对应多个物料！");
						}
					}
				}
				
				// 最终校验是否物料都带出了，只校验非排除物料的分录
				int entrySize = oriEnCol.size();
				int excludeSize = 0;
				for (DynamicObject oriEn : oriEnCol) {
					if (!oriEn.getBoolean("yd_beexclude") && StringUtils.isEmpty(oriEn.getString("yd_materialno"))) {
						if (!oriBillObj.getBoolean("yd_matrepeat")) {
							oriBillObj.set("yd_nomaterial", true);
						}
						break;
					}
					if (oriEn.getBoolean("yd_beexclude")) {
						excludeSize++;
					}
					
					oriBillObj.set("yd_nomaterial", false);
					oriBillObj.set("yd_matrepeat", false);
				}
				
				// 是否整单排除
				if (entrySize == excludeSize) {
					oriBillObj.set("yd_allexclude", true);
				}
				
				// 保存单据信息
				String failMsg = errorMsg.toString();
				if (errorMsg.length() > 1500) {
					failMsg = errorMsg.substring(0, 1500);
				}
				oriBillObj.set("yd_failreason", failMsg);
				SaveServiceHelper.save(new DynamicObject[] {oriBillObj});  // 保存
			}
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：下推红字销售出库单
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param oriIdSet 单据ID集合
	 * @return 是否成功
	 */
	@Override
	public ApiResult pushToSaleoutBill(Set<String> oriIdSet) {
		System.out.println(444);
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 获取符合条件的单据进行下推，已审核，没有下游单号的
			QFilter billFilters = QFilter.of("yd_targetbillno=?", "");
			billFilters.and(QFilter.of("billstatus=?", "C"));
			
			billFilters.and(new QFilter("id", QCP.in, oriIdSet));
			DynamicObject[] oriBills = BusinessDataServiceHelper.load("yd_wholesalereturnbill", "id,entryentity.id", billFilters.toArray());
			if (oriBills.length == 0) {
				throw new KDException("没有满足条件（已审核，下游单号为空）的批发退货单数据！");
			}
			
			Set<String> idSet = new HashSet<String>();
			// 校验单据信息是否都配置了映射表：渠道对应组织，客户对应客户，仓库对应仓库
			for(DynamicObject oriPurNoticeBill : oriBills) {
				idSet.add(oriPurNoticeBill.getPkValue().toString());
			}
			
			WholeSaleReturnBillMserviceHelper.refreshBill(idSet);
			
			billFilters.and(new QFilter("yd_noorg", QCP.equals, false));  // 组织不存在 等于 否
			billFilters.and(new QFilter("yd_nowarehouse", QCP.equals, false));  // 仓库不存在 等于 否
			billFilters.and(new QFilter("yd_nocustomer", QCP.equals, false));  // 客户不存在 等于 否
			billFilters.and(new QFilter("yd_nomaterial", QCP.equals, false));  // 物料不存在 等于 否
			billFilters.and(new QFilter("yd_allexclude", QCP.equals, false));  // 整单排除 等于 否
			billFilters.and(new QFilter("yd_matrepeat", QCP.equals, false));  // 整单排除 等于 否
			billFilters.and(new QFilter("yd_excludewarehouse", QCP.equals, false));  // 排除仓库 等于 否
			oriBills = BusinessDataServiceHelper.load("yd_wholesalereturnbill", "id,entryentity.id", billFilters.toArray());
			if (oriBills.length == 0) {
				throw new KDException("没有满足条件的批发退货单数据！");
			}
			
			//需要下推的单据
			String formid = "im_saloutbill";  // 单据标识-销售出库单
			String billType = "im_SalOutBill_STD_BT_S";  // 单据类型-销售出库单
			String biztypenumber = "2101";  // 业务类型-物料类销售退货
			String invschemeNumber = "2101";  // 库存事务-普通销售退、补、换货
			for (DynamicObject oriBill : oriBills) {
				oriBill = BusinessDataServiceHelper.loadSingle(oriBill.getPkValue(), "yd_wholesalereturnbill");
				
				// 查询客户对应税率
				QFilter cusFilter = new QFilter("number", QCP.equals, oriBill.getString("yd_customerno"));
				DynamicObject[] customerObjs = BusinessDataServiceHelper.load("bd_customer", "taxrate.number",
						new QFilter[] { cusFilter });
				String customerRateNum = "";
				for (DynamicObject customer : customerObjs) {
					customerRateNum = customer.getString("taxrate.number");
				}
				
				// 创建新表单页面
				IFormView view = ABillServiceHelper.createAddView(formid);
				view.getModel().setItemValueByNumber("billtype", billType);
				view.getModel().setItemValueByNumber("org", oriBill.getString("yd_orgnum"));
				view.getModel().setItemValueByNumber("bizorg", oriBill.getString("yd_orgnum"));
				view.getModel().setItemValueByNumber("biztype", biztypenumber);  // 业务类型
				view.getModel().setItemValueByNumber("invscheme", invschemeNumber);  // 库存事务
				view.getModel().setValue("biztime", oriBill.getDate("yd_returndate"));
				view.getModel().setItemValueByNumber("customer", oriBill.getString("yd_customerno"));
				view.getModel().setValue("yd_tbly", 1);  // 来源系统
				view.getModel().setValue("yd_sourcebilltype", 1);  // 来源单据类型
				view.getModel().setValue("yd_dealcode", oriBill.getString("yd_noticebillno"));  // 通知单号
				
				int i = 0;  // 分录行下标
				// 创建销售出库单分录数据
				DynamicObjectCollection oriBillEnCol = oriBill.getDynamicObjectCollection("entryentity");  // 源单分录
				for (DynamicObject oriEn : oriBillEnCol) {
					// 排除物料不用下推
					if (oriEn.getBoolean("yd_beexclude")) {
						continue;
					}
					// 创建分录，第一行分录不用创建
					if (i > 0) {
						view.getModel().createNewEntryRow("billentry");
					}

					// update by hst 2023/11/22 如果是组合类型的物料需要拆多条分录
					if (StringUtils.equals("1", oriEn.getString("yd_combinationtype"))) {
						BigDecimal oriQty = oriEn.getBigDecimal("yd_qty");
						String oriMatNum = oriEn.getString("yd_goodsnum");
						DynamicObjectCollection combiMatCol = this.getCombiMatCol(oriMatNum,"1");
						for (DynamicObject combiMatObj : combiMatCol) {
							view.getModel().createNewEntryRow("billentry");

							view.getModel().setValue("entrycomment", oriMatNum, i);  // 将组装商品编码放到备注
							view.getModel().setItemValueByNumber("material", combiMatObj.getString("matNum"), i);
							view.getModel().setItemValueByNumber("warehouse", oriBill.getString("yd_finalwarehouseno"), i);
  							BigDecimal qty = combiMatObj.getBigDecimal("yd_qty").multiply(oriQty);
							view.getModel().setValue("qty", qty, i);  // 数量需要倍数乘积
							view.getModel().setValue("priceandtax", combiMatObj.getBigDecimal("yd_price"), i);
							view.getModel().setValue("amountandtax", combiMatObj.getBigDecimal("yd_price").multiply(qty), i);
							// 金额为0标识赠品
							if (combiMatObj.getBigDecimal("yd_price").compareTo(BigDecimal.ZERO) == 0) {
								view.getModel().setValue("ispresent", "1", i);
							}

							// 设置税率
							if (customerRateNum != null) {
								view.getModel().setItemValueByNumber("taxrateid", customerRateNum, i);
							}
							// 记录源单信息
							view.getModel().setValue("srcsysbillentryid", oriEn.getPkValue(), i);  // 来源系统单据分录ID
							view.getModel().setValue("srcsysbillno", oriBill.getString("billno"), i);  // 来源系统单据编号
							view.getModel().setValue("srcsysbillid", oriBill.getPkValue(), i);  // 来源系统单据ID
							view.getModel().setValue("srcbillid", oriBill.getPkValue(), i);  // 来源单据ID
							view.getModel().setValue("srcbillentryid", oriEn.getPkValue(), i);  // 来源单据行ID
							view.getModel().setValue("srcbillentryseq", oriEn.get("seq"), i);  // 来源单据分录序号
							view.getModel().setValue("srcbillnumber", oriBill.getString("billno"), i);  // 来源单据分录序号

							i++;
						}
					} else {

						view.getModel().setItemValueByNumber("material", oriEn.getString("yd_materialno"), i);
						view.getModel().setItemValueByNumber("warehouse", oriBill.getString("yd_finalwarehouseno"), i);
						view.getModel().setValue("qty", oriEn.getBigDecimal("yd_qty"), i);
						view.getModel().setValue("priceandtax", oriEn.getBigDecimal("yd_price"), i);
						view.getModel().setValue("amountandtax", oriEn.getBigDecimal("yd_amount"), i);
						// 金额为0标识赠品
						if (oriEn.getBigDecimal("yd_amount").compareTo(BigDecimal.ZERO) == 0) {
							view.getModel().setValue("ispresent", "1", i);
						}

						if (customerRateNum != null) {
							view.getModel().setItemValueByNumber("taxrateid", customerRateNum, i);
						}
						// 记录源单信息
						view.getModel().setValue("srcsysbillentryid", oriEn.getPkValue(), i);  // 来源系统单据分录ID
						view.getModel().setValue("srcsysbillno", oriBill.getString("billno"), i);  // 来源系统单据编号
						view.getModel().setValue("srcsysbillid", oriBill.getPkValue(), i);  // 来源系统单据ID
						view.getModel().setValue("srcbillid", oriBill.getPkValue(), i);  // 来源单据ID
						view.getModel().setValue("srcbillentryid", oriEn.getPkValue(), i);  // 来源单据行ID
						view.getModel().setValue("srcbillentryseq", oriEn.get("seq"), i);  // 来源单据分录序号
						view.getModel().setValue("srcbillnumber", oriBill.getString("billno"), i);  // 来源单据分录序号

						i++;
					}
				}
				
				
				// 有分录时才操作保存提交
				if (i > 0) {
					OperationResult operationResult = ABillServiceHelper.saveOperate(view);
					// 判断保存结果
					if (operationResult.isSuccess()) {
						// 反写源单的目标单号
						String newBillNo = "";
						Map<Object, String> map = operationResult.getBillNos();
						for (Map.Entry<Object, String> entry : map.entrySet()) {
							newBillNo = entry.getValue();
						}
						oriBill.set("yd_targetbillno", newBillNo);
						oriBill.set("yd_failreason", "");
						SaveServiceHelper.save(new DynamicObject[] {oriBill});
						
						// 保存两张单据的关联关系
						QFilter newBillFilter = new QFilter("billno", QCP.equals, newBillNo);
						DynamicObject newBillObj = BusinessDataServiceHelper.loadSingle(formid, "id,billno,yd_sftj", newBillFilter.toArray());
						
						// 进行提交操作
	//					IFormView modifyView = ABillServiceHelper.createModifyView(view.getEntityId(),
	//							operationResult.getSuccessPkIds().get(0).toString());
						operationResult = view.invokeOperation("submit");
						if (!operationResult.isSuccess()) {
							String errMessage = operationResult.getMessage() + ","; // 错误摘要
							// 提取保存详细错误
							for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
								errMessage += errInfo.getMessage() + ",";
							}
							if (errMessage.length() > 1500) {
								errMessage = errMessage.substring(0, 1500);
							}
							newBillObj.set("yd_sftj", errMessage);  // 提交失败原因
							SaveServiceHelper.save(new DynamicObject[] {newBillObj});
						}
						
						String newBillId = view.getModel().getDataEntity().getPkValue().toString();
						
						ABillServiceHelper.exitView(view);
						
						// 在退出界面之后再保存关联关系才行
						BotpUtils.createRelation("yd_wholesalereturnbill", Long.parseLong(oriBill.getPkValue().toString()), "im_saloutbill", Long.parseLong(newBillId));
					} else {  // 记录保存失败原因
						String errMessage = operationResult.getMessage() + ","; // 错误摘要
						// 保存详细错误
						for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
							errMessage += errInfo.getMessage() + ",";
						}
						if (errMessage.length() > 1500) {
							errMessage = errMessage.substring(0, 1500);
						}
						
						oriBill.set("yd_targetbillno", "");
						oriBill.set("yd_failreason", errMessage);
						SaveServiceHelper.save(new DynamicObject[] {oriBill});
						
						ABillServiceHelper.exitView(view);
					}
				} else {
					ABillServiceHelper.exitView(view);
				}
			}
			
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 根据E3组装物料编码获取拆分后的中台物料集合
	 * @param oriMatNum 主商品编码
	 * @param type 物料类型（ToB：1，ToC：2）
	 * @return 拆分后的中台物料集合
	 */
	public static DynamicObjectCollection getCombiMatCol(String oriMatNum, String type) {
		QFilter matComFilter = new QFilter("entryentity.yd_mainmatnum", QCP.equals, oriMatNum);
		matComFilter.and(new QFilter("billstatus", QCP.equals, "C"));
		matComFilter.and(new QFilter("entryentity.yd_mattype",QFilter.equals,type));
		DynamicObjectCollection matComCol = QueryServiceHelper.query("yd_matcombination",
				"id billId,entryentity.id enId,entryentity.yd_mainmatnum yd_mainmatnum,entryentity.yd_subentryentity.id subEnId,"
						+ "entryentity.yd_subentryentity.yd_material.number matNum,entryentity.yd_subentryentity.yd_qty yd_qty,entryentity.yd_subentryentity.yd_price yd_price",
				matComFilter.toArray());
		return matComCol;
	}
}
