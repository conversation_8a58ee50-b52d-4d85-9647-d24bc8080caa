package kd.bos.tcbj.im.outbill.mservice;

import java.util.Set;

import com.alibaba.fastjson.JSONArray;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;

/**
* 描述：采购通知单接口类
* PurNoticeBillMservice.java
* @auditor yanzuwei
* @date 2021年12月30日
* 
*/
public interface SaleOutBillMservice {
	
	/**
	 * 描述：正向内部结算流程
	 * 
	 * @createDate  : 2021-12-30
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param saleoutBillId 源末级销售出库单ID
	 * @return 是否成功
	 */
	public ApiResult createForwardBill(String saleoutBillId);
	
	/**
	 * 描述：退货内部结算流程
	 * 
	 * @createDate  : 2021-12-30
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param saleoutBillId 源末级销售出库单ID
	 * @return 是否成功
	 */
	public ApiResult createBackwardBill(String saleoutBillId);

	/**正向直营店流程*/
    public ApiResult createDirectForwardBill(String saleoutBillId);
    /**逆向直营店结算流程*/
	public ApiResult createDirectBackwardBill(String saleoutBillId);

	/**
	 * PCP销售出库单一盘货结算
	 * @author: hst
	 * @createDate  : 2022/10/28
	 * @param saleoutBill 单据
	 * @param isForward 是否是正向
	 * @return 是否成功
	 */
	public ApiResult createPCPSettleBill(DynamicObject saleoutBill, boolean isForward);
	
	/**
	 * 描述：根据股份出库批次结果重置整条流程的出入库单的批次
	 * 
	 * @createDate  : 2022-12-26
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param lotBillIdSet 股份出库结果单ID集合
	 * @return 是否成功
	 */
	public ApiResult resetSettleBillLot(Set<String> lotBillIdSet);

	/**
	 * PCP销售出库单虚体组织结算
	 * @author: hst
	 * @createDate  : 2023/02/16
	 * @param saleoutBill 单据
	 * @return 是否成功
	 */
	public ApiResult createPCPVirtualSettleBill(DynamicObject saleoutBill);

	/**
	 * 描述：根据股份出库批次结果重置整条流程的出入库单的批次(以一个字段作为key)
	 *
	 * @createDate  : 2024/01/31
	 * @author: hst
	 * @updateDate  :
	 * @updateAuthor:
	 * @param lotBillIdSet 股份出库结果单ID集合
	 * @return 是否成功
	 */
	public ApiResult resetSettleBillLotByKey(Set<String> lotBillIdSet);
}
