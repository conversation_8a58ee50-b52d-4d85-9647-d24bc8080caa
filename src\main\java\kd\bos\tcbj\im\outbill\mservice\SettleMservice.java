package kd.bos.tcbj.im.outbill.mservice;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;

public interface SettleMservice {

    /**
     * 销售出库一盘货结算
     * @author: hst
     * @createDate  :
     * @param saleoutBill 单据
     * @param config 配置信息
     * @param isForward 是否是正向
     * @return 是否成功
     */
    public ApiResult createSaleOutSettleBill(DynamicObject saleoutBill, DynamicObject config, Boolean isForward);

    /**
     * 其他出库一盘货结算
     * @author: hst
     * @createDate  :
     * @param saleoutBill 单据
     * @param config 配置信息
     * @param isForward 是否是正向
     * @return 是否成功
     */
    public ApiResult createOtherOutSettleBill(DynamicObject saleoutBill, DynamicObject config, Boolean isForward);
}
