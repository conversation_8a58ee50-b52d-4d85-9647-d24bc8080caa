package kd.bos.tcbj.im.outbill.mservice;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;

import java.util.HashMap;

public interface TransSettleMservice {

    /**
     * 调拨中间表结算
     *
     * @param context 上下文
     * @param config  配置信息
     * @param bill    需结算单据
     * @return 是否成功
     * @author: hst
     * @createDate :
     */
    public ApiResult doTransSettle_settle(HashMap context, DynamicObject config, DynamicObject bill);
}
