package kd.bos.tcbj.im.outbill.mservice;

import java.util.Set;

import json.JSONObject;
import kd.bos.entity.api.ApiResult;

/**
 * 批发通知单接口类
 * @auditor yanzuwei
 * @date 2022年5月12日
 * 
 */
public interface WholeSaleNoticeBillMservice {
	/**
	 * 描述：查询E3批发通知单功能
	 * 
	 * @createDate  : 2022-05-12
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public ApiResult getWholeSaleNoticeBill(JSONObject params);
	
	/**
	 * 描述：刷新单据数据
	 * 
	 * @createDate  : 2022-05-12
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public ApiResult refreshBill(Set<String> idSet);
	
	/**
	 * 描述：下推销售出库单
	 * 
	 * @createDate  : 2022-05-12
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param idSet 单据ID集合
	 * @return 是否成功
	 */
	public ApiResult pushToSaleoutBill(Set<String> idSet);

	/**
	 * 批发通知单下推生成销售出库单（拆单）
	 *
	 * @createDate  : 2022/09/20
	 * @author: 严祖威
	 * @updateDate  :
	 * @updateAuthor:
	 * @param idSet 单据ID集合
	 * @return 是否成功
	 */
	public ApiResult pushToSaleoutBill_split(Set<String> idSet);
}
