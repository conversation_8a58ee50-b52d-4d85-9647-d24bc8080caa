package kd.bos.tcbj.im.outbill.mservice;

import java.util.Set;

import json.JSONObject;
import kd.bos.entity.api.ApiResult;

/**
* 描述：批发退货单接口类
* WholeSaleReturnBillMservice.java
* @auditor yanzuwei
* @date 2022年01月10日
* 
*/
public interface WholeSaleReturnBillMservice {
	
	/**
	 * 描述：查询E3批发退货单功能
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public ApiResult getWholeSaleReturnBill(JSONObject params);
	
	/**
	 * 描述：刷新单据数据
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public ApiResult refreshBill(Set<String> idSet);
	
	/**
	 * 描述：下推红字销售出库单
	 * 
	 * @createDate  : 2022-01-10
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param idSet 单据ID集合
	 * @return 是否成功
	 */
	public ApiResult pushToSaleoutBill(Set<String> idSet);
}
