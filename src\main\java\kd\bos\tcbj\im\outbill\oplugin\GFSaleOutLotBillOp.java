package kd.bos.tcbj.im.outbill.oplugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.outbill.helper.SaleOutBillMserviceHelper;
import kd.bos.tcbj.im.util.CommonUtils;
import kd.bos.tcbj.im.vo.SaleOutEnLotVo;

/**
 * 股份出库单批次结果单
 * @auditor yanzuliang
 * @date 2022年12月26日
 * 
 */
public class GFSaleOutLotBillOp extends AbstractOperationServicePlugIn {
	
	/**
	 * 
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 * 
	 * @createDate  : 2021-12-17
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 加载单据数据包事件
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("id");
		e.getFieldKeys().add("billno");
		e.getFieldKeys().add("yd_gfsaleoutbillno");
		e.getFieldKeys().add("entryentity.yd_material");
		e.getFieldKeys().add("entryentity.yd_warehouse");
		e.getFieldKeys().add("entryentity.yd_lot");
		e.getFieldKeys().add("entryentity.yd_begindate");
		e.getFieldKeys().add("entryentity.yd_enddate");
		// update by hst 2024/01/31 增加源分录ID，作为key
		e.getFieldKeys().add("entryentity.yd_sourceentryid");
		e.getFieldKeys().add("entryentity.seq");
	}
	
	/**
	 * 股份的单子跑完集成后重置其他单子的批次信息，只有正向才需要,yzl
	 * 控制跑失败的单据才需要重新跑？手工执行
	 * 单独流程先跑股份的单，在同步流程中标记上同步成功，然后执行重置其他单据的批次，执行失败就反写失败原因
	 * 同步失败的单子才跑单独的流程，同步成功后将最新的批次信息记录到一个中间表yd_saleoutlotbill，并执行单据刷新逻辑，这样即使执行失败也可以保留关联关系，使得可以执行重置
	 */
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		if("resetSettle".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			Set<String> gfSaleOutBillIdSet = new HashSet<String>();
			for (DynamicObject obj : objs) {
				gfSaleOutBillIdSet.add(obj.getString("id"));
				SaleOutBillMserviceHelper.resetSettleBillLot(gfSaleOutBillIdSet);
			}
		} else if ("resetlot".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			Set<String> gfSaleOutBillIdSet = new HashSet<String>();
			for (DynamicObject obj : objs) {
				gfSaleOutBillIdSet.add(obj.getString("id"));
				ApiResult result = SaleOutBillMserviceHelper.resetSettleBillLotByKey(gfSaleOutBillIdSet);
				if (!result.getSuccess()) {
					throw new KDBizException(result.getMessage());
				}
			}
		}

	}
}
