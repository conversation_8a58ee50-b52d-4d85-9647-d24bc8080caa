package kd.bos.tcbj.im.outbill.oplugin;

import java.util.Date;

import org.apache.commons.lang3.time.DateFormatUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * InnerrelBillOp.java
 * 内部结算关系表操作插件
 * 
 * @auditor yanzuwei
 * @date 2022年4月7日
 * 
 */
public class InnerrelBillOp extends AbstractOperationServicePlugIn {
	
	/**
	 * 
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 * 
	 * @createDate  : 2022年4月7日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 加载单据数据包事件
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_brand");
		e.getFieldKeys().add("yd_oricustomer");
		e.getFieldKeys().add("yd_newcustomer");
		e.getFieldKeys().add("entryentity");
	}
	
	/**
	 * 
	 * 描述：开启事务，未提交数据库事件
	 * 1.审核时创建对应的价格表
	 * 
	 * @createDate  : 2022年4月7日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 开启事务，未提交数据库事件
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		String opKey=e.getOperationKey();
		if(StringUtil.equals("audit", opKey)) {
			DynamicObject[] dynamicObjectArray=e.getDataEntities();
			String creatorId = RequestContext.get().getUserId().toString();
			DynamicObject creator = BusinessDataServiceHelper.loadSingle(creatorId, "bos_user","id");
			for (DynamicObject innerrelBill : dynamicObjectArray) {
				DynamicObject newCustomer = innerrelBill.getDynamicObject("yd_newcustomer");
				// 遍历分录创建内部组织价格表
				String[] orgs = new String[5];  // 设置最大5级组织
				DynamicObjectCollection enCol = innerrelBill.getDynamicObjectCollection("entryentity");
				for(int i=0;i<enCol.size();i++) {
					DynamicObject enObj = enCol.get(i);
					orgs[enObj.getInt("yd_orglevel")-1] = enObj.getDynamicObject("yd_org").getPkValue().toString();
				}
				
				String lastOrgId = "";  // 末级组织
				for (int j=1;j<orgs.length;j++) {
					if (!StringUtil.isEmptyString(orgs[j])) {
						String outOrgId = orgs[j-1];
						String inOrgId = orgs[j];
						lastOrgId = inOrgId;
						QFilter priceFilter = new QFilter("yd_outorg.id", QCP.equals, outOrgId);
						priceFilter.and(new QFilter("yd_inorg.id", QCP.equals, inOrgId));
						priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
						if (!QueryServiceHelper.exists("yd_pricerelbill", priceFilter.toArray())) {
							DynamicObject newPriceBill = BusinessDataServiceHelper.newDynamicObject("yd_pricerelbill");
							newPriceBill.set("billno", DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS"));
							newPriceBill.set("billstatus", "C");
							newPriceBill.set("yd_outorg", BusinessDataServiceHelper.loadSingle(outOrgId, "bos_org", "id,name,number"));
							newPriceBill.set("yd_inorg", BusinessDataServiceHelper.loadSingle(inOrgId, "bos_org", "id,name,number"));
							newPriceBill.set("creator", creator);
							newPriceBill.set("modifier", creator);
							newPriceBill.set("auditor", creator);
							newPriceBill.set("createtime", new Date());
							newPriceBill.set("modifytime", new Date());
							newPriceBill.set("auditdate", new Date());
							SaveServiceHelper.save(new DynamicObject[] {newPriceBill});
						}
					}
				}
				
				// 查询经销商供货价格表
				if (!StringUtil.isEmptyString(lastOrgId)) {
					String customerId = newCustomer.getPkValue().toString();
					QFilter priceFilter = new QFilter("yd_org.id", QCP.equals, lastOrgId);
					priceFilter.and(new QFilter("yd_customer.id", QCP.equals, customerId));
					priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
					if (!QueryServiceHelper.exists("yd_orgcuspricebill", priceFilter.toArray())) {
						DynamicObject newPriceBill = BusinessDataServiceHelper.newDynamicObject("yd_orgcuspricebill");
						newPriceBill.set("billno", DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS"));
						newPriceBill.set("billstatus", "C");
						newPriceBill.set("yd_org", BusinessDataServiceHelper.loadSingle(lastOrgId, "bos_org", "id,name,number"));
						newPriceBill.set("yd_customer", BusinessDataServiceHelper.loadSingle(customerId, "bd_customer", "id,name,number"));
						newPriceBill.set("creator", creator);
						newPriceBill.set("modifier", creator);
						newPriceBill.set("auditor", creator);
						newPriceBill.set("createtime", new Date());
						newPriceBill.set("modifytime", new Date());
						newPriceBill.set("auditdate", new Date());
						SaveServiceHelper.save(new DynamicObject[] {newPriceBill});
					}
				}
				
			}
			
		}
	}
}
