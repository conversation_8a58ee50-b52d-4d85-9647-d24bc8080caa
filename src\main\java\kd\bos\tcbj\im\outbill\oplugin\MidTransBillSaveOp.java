package kd.bos.tcbj.im.outbill.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.entity.plugin.args.ReturnOperationArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.api.JobInfo;
import kd.bos.schedule.api.JobType;
import kd.bos.schedule.executor.JobClient;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.outbill.constants.SettleParamConstant;
import kd.bos.tcbj.im.outbill.schedulejob.TransSettleScheduleTask;
import kd.bos.tcbj.im.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * @package: kd.bos.tcbj.im.outbill.oplugin.MidTransBillSaveOp
 * @className MidTransBillSaveOp
 * @author: hst
 * @createDate: 2024/06/13
 * @description: 调拨结算保存插件
 * @version: v1.0
 */
public class MidTransBillSaveOp extends AbstractOperationServicePlugIn {

    // 保存操作标识
    private final static String SAVE_OP = "save";

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/06/13
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("billstatus");
        e.getFieldKeys().add("yd_isinter");
        e.getFieldKeys().add("yd_issettle");
    }

    /**
     * 事务提交之前
     * @param e
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        super.endOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case SAVE_OP : {
                this.signBillSettle(bills);
                break;
            }
        }
    }

    /**
     * 操作结束时触发此事件
     * @param e
     */
    @Override
    public void onReturnOperation(ReturnOperationArgs e) {
        super.onReturnOperation(e);
        boolean isSuccess = e.getOperationResult().isSuccess();
        List<Object> billIds = e.getOperationResult().getSuccessPkIds();
        if (isSuccess) {
            this.execBillSettle(billIds);
        }
    }

    /**
     * 标记执行调拨结算
     * @param bills
     * @author: hst
     * @createDate: 2024/06/13
     */
    private void signBillSettle (DynamicObject[] bills) {
        for (DynamicObject bill : bills) {
            String billStatus = bill.getString("billstatus");
            boolean isInter = bill.getBoolean("yd_isinter");
            boolean isSettle = bill.getBoolean("yd_issettle");
            if ("C".equals(billStatus) && isInter && !isSettle) {
                bill.set("yd_issettle",true);
                bill.set("yd_isauto",true);
            }
        }

        SaveServiceHelper.update(bills);
    }

    /**
     * 执行调拨结算
     * @param billIds
     * @author: hst
     * @createDate: 2024/06/13
     */
    private void execBillSettle (List<Object> billIds) {
        List<String> execBillIds = new ArrayList<>();
        DynamicObject[] bills = BusinessDataServiceHelper.load(billIds.toArray(new Object[billIds.size()]),
                BusinessDataServiceHelper.newDynamicObject(BillTypeHelper.BILLTYPE_MIDTRANSBILL).getDynamicObjectType());
        for (DynamicObject bill : bills) {
            String billStatus = bill.getString("billstatus");
            boolean isInter = bill.getBoolean("yd_isinter");
            boolean isAuto = bill.getBoolean("yd_isauto");
            boolean isHasNext = bill.getBoolean("yd_ishasnext");
            if ("C".equals(billStatus) && isInter && isAuto && !isHasNext) {
                execBillIds.add(bill.getString("id"));
            }
        }

        if (execBillIds.size() > 0) {
            this.createOnceJob(execBillIds);
        }
    }

    /**
     * 创建调度任务
     */
    private void createOnceJob(List<String> billIds) {
        // 获取调度执行用户
        DynamicObject param = QueryServiceHelper.queryOne(SettleParamConstant.MAIN_ENTITY,
                SettleParamConstant.USERID_FIELD,
                new QFilter[]{new QFilter("status",QFilter.equals,"C")
                        , new QFilter(SettleParamConstant.TYPE_FIELD, QFilter.equals, "yd_trans_settle")});

        if (Objects.nonNull(param)) {
            String userId = param.getString(SettleParamConstant.USERID_FIELD);

            if (StringUtils.isNotBlank(userId)) {
                Map<String, Object> jobParams = new HashMap<>();
                jobParams.put("key", "yd_trans_settle");

                QFilter qFilter = new QFilter("id", QFilter.in, billIds);
                jobParams.put("qFilter", qFilter);

                JobInfo jobInfo = new JobInfo();
                jobInfo.setNumber("im_yd_im_Deal_TransSettleJob");
                jobInfo.setName("库存调拨结算作业");
                jobInfo.setJobType(JobType.REALTIME);
                jobInfo.setId(UUID.randomUUID().toString());
                jobInfo.setRunByUserId(Long.valueOf(userId).longValue());

                //设置传给作业类的参数
                jobInfo.setParams(jobParams);
                //指定关联的应用
                jobInfo.setAppId("im");
                //设置调度作业类
                jobInfo.setTaskClassname(TransSettleScheduleTask.class.getName());
                //是否并行执行
                jobInfo.setRunConcurrently(false);

                //创建并执行一次性调度任务，不持久化调度作业，前台查询不到，只能看到调度任务
                JobClient.dispatch(jobInfo);
            }
        }
    }
}
