package kd.bos.tcbj.im.outbill.oplugin;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * SaleOutBillOp.java
 * 销售出库单操作插件
 * 
 * <AUTHOR> 严祖威
 * @updateAuthor
 * @createDate : 2021-12-17
 */
public class SaleOutBillOp extends AbstractOperationServicePlugIn {
	/**
	 * 
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 * 
	 * @createDate  : 2021-12-17
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 加载单据数据包事件
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("billentry");
	}
	
	/**
	 * 
	 * 描述：开启事务，未提交数据库事件
	 * 1.反审核时将源E3仓库（该字段在发起内部结算流程时进行保存赋值）赋值回标准仓库字段上
	 * 
	 * @createDate  : 2021-12-17
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 开启事务，未提交数据库事件
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		String opKey=e.getOperationKey();
		if(StringUtil.equals("unaudit", opKey)) {
			DynamicObject[] dynamicObjectArray=e.getDataEntities();
			for (DynamicObject saleoutBill : dynamicObjectArray) {
				saleoutBill.set("yd_internalsettle", false);  // 重置是否已内部结算为否
				DynamicObjectCollection entryCol = saleoutBill.getDynamicObjectCollection("billentry");
				for (DynamicObject entry : entryCol) {
					if (entry.getDynamicObject("yd_e3oriwarehouse") != null) {
						entry.set("warehouse", entry.getDynamicObject("yd_e3oriwarehouse"));
					}
				}
			}
			SaveServiceHelper.save(dynamicObjectArray);
		}
	}
	
}
