package kd.bos.tcbj.im.outbill.plugin;

import java.util.HashSet;
import java.util.Set;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.exception.KDBizException;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.list.IListView;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;

/**
* @auditor yanzuwei
* @date 2022年3月23日
* 
*/
public class InnerrelBillList extends AbstractListPlugin {
	
	@Override
	public void itemClick(ItemClickEvent evt) {
		String itemKey=evt.getItemKey();
		// 获取营销云价格功能
		if (StringUtil.equalsIgnoreCase("yd_bar_synxyx", itemKey)) {
			synPriceFromYXY();
//			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		
	}

	/**
	 * 获取销售方与采购方经销商的关系，更新的是经销商价格表
	 */
	private void synPriceFromYXY() {
		// 获取选择的数据，得到一个单据ID集合，调用工具类方法
		ListSelectedRowCollection selectedData = ((IListView)this.getView()).getSelectedRows(); 
		Set<String> idSet = new HashSet<String>();
		for (ListSelectedRow row : selectedData) {
			Object pk = row.getPrimaryKeyValue();
			idSet.add(pk.toString());
		}
		
		if(idSet.size() == 0) {
			throw new KDBizException("请选择数据！");
		}
		
		Set<String> finalIdSet = new HashSet<String>();
		QFilter filter = new QFilter("id", QCP.in, idSet);
		filter.and(new QFilter("yd_synfromyxy", QCP.equals, true));  // 是否需要同步营销云
		filter.and(new QFilter("billstatus", QCP.equals, "C"));  // 已审批
		DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_innerrelbill", "id", filter.toArray(), null);
		if (settleDataSet.hasNext()) {
			for (Row settleRow : settleDataSet) {
				finalIdSet.add(settleRow.getString("id"));
			}
		} else {
			throw new KDBizException("请选择已设置为需要同步营销云的内部结算关系！");
		}
		
		ApiResult result = PriceMServiceHelper.synPriceFromYXY(finalIdSet);
		if (!result.getSuccess()) {
			if (result.getMessage() != null) {
				this.getView().showMessage(result.getMessage());
			} else {
				this.getView().showMessage("执行失败，java.lang.NullPointerException！");
			}
		} else {
			this.getView().showMessage("执行完成，详情请查看经销商价格表！");
		}
	}

}
