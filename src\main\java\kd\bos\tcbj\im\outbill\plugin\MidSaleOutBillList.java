package kd.bos.tcbj.im.outbill.plugin;

import kd.bos.dlock.DLock;
import kd.bos.form.CloseCallBack;
import kd.bos.form.ShowType;
import kd.bos.form.control.Toolbar;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.helper.InterHelper;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import kd.bos.tcbj.im.plugin.KdepListPlugin;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.JsonUtils;
import kd.bos.tcbj.im.util.UIUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 销售出库单中间表
 *
 * <AUTHOR>
 * @Description: 获取PCP出库单
 * @date 2022-6-14
 */
public class MidSaleOutBillList extends KdepListPlugin {
    /**actionId标识：获取PCP销售出库单*/
    private static String ACTIONID_GETPCPSALEOUTBILL = "actionId_getPcpSaleOutBill";

    /**
     * 注册监听
     * @param e 触发的事件对象
     * <AUTHOR>
     * @date 2022-06-14
     */
    @Override
    public void registerListener(EventObject e) {
        // 菜单栏事件监听处理
        Toolbar toolbar = this.getView().getControl(UIUtils.getMenuBar(this));
        toolbar.addItemClickListener(this);
    }

    /**
     * 菜单按钮触发响应
     * @param evt 事件对象
     * <AUTHOR>
     * @date 2022-06-14
     */
    @Override
    public void beforeItemClick(BeforeItemClickEvent evt) {
        // 获取触发的控件
        String itemKey = evt.getItemKey();
        switch (itemKey) {
            case "yd_bar_getpcpsaleoutbill":
                this.showForm(BillTypeHelper.BILLTYPE_PCPPARAMFILTER, null, new CloseCallBack(this, ACTIONID_GETPCPSALEOUTBILL), ShowType.Modal);
                break;
            case "yd_bar_transsaleoutbill":
                actionTransSaleOutBill();
            default:
        }
    }

    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        if (StringUtils.equals(closedCallBackEvent.getActionId(), ACTIONID_GETPCPSALEOUTBILL)) {
            actionGetPcpSaleOutBill(closedCallBackEvent);
        }
    }

    /**
     * 获取PCP销售出库单
     * <AUTHOR>
     * @date 2022-6-14
     */
    private void actionGetPcpSaleOutBill(ClosedCallBackEvent closedCallBackEvent) {
        // 1、获取弹窗返回的日期
        String returnData = (String) closedCallBackEvent.getReturnData();
        if (StringUtils.isBlank(returnData)) return;
        // 将对应的数据转换成Map
        Map<String, String> returnDataMap = JsonUtils.toMap2(returnData);

        Date beginDate = DateTimeUtils.parse(returnDataMap.get("beginDate"), DateTimeUtils.SDF_TIME); // 开始日期
        Date endDate = DateTimeUtils.parse(returnDataMap.get("endDate"), DateTimeUtils.SDF_TIME); // 结束日期
        String billNo = returnDataMap.get("billNo"); // PCP出库结果单号
        // 2、根据日期调用PCP的api接口获取业务数据
        // 正向销售出库单
        boolean isPass1 = PCPServiceHelper.getPcpSaleOutBill("SALE", beginDate, endDate, billNo);
        // 退货销售出库单
        boolean isPass2 = PCPServiceHelper.getPcpSaleOutBill("SALE_RED", beginDate, endDate, billNo);
        if (isPass1 || isPass2) {
            this.getView().showSuccessNotification("成功获取PCP销售出库单!");
            // 3、刷新界面
            this.getView().invokeOperation("refresh");
        }else {
            this.getView().showTipNotification("暂无可获取的数据！");
        }
    }



    /**
     * 获取PCP销售出库单
     *
     * <AUTHOR>
     * @date 2022-6-20
     */
    private void actionTransSaleOutBill() {
        // 1、获取转换的ID
        UIUtils.checkSelected(this.getView(), "请勾选需要下推的数据！");
        List<Object> idList = UIUtils.getSelectIds(this.getView());
        PCPServiceHelper.transSaleOutBill(idList,null, false);
        this.getView().showMessage("执行完成，请查看处理结果！");
        this.getView().invokeOperation("refresh");
    }
}
