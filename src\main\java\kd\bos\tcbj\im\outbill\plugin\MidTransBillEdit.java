package kd.bos.tcbj.im.outbill.plugin;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.form.field.ComboEdit;
import kd.bos.form.field.ComboItem;
import kd.bos.servicehelper.permission.PermissionServiceHelper;
import kd.bos.tcbj.im.plugin.KdepBillPlugin;
import kd.bos.tcbj.im.util.UIUtils;

import java.util.ArrayList;
import java.util.EventObject;
import java.util.List;

/**
 * 库存调拨单中间表编辑界面
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-7-29
 */
public class MidTransBillEdit extends KdepBillPlugin {

    //角色ID,对应角色编码BR-00001020
    private static final String ROLE_ID = "2013119016247277568";

    @Override
    public void afterBindData(EventObject e) {
        if (!(<PERSON>olean) this.getModel().getValue("yd_isinter")) {
            UIUtils.setRequired(this.getView(), new String[]{"yd_outorg", "yd_inorg", "yd_outwarehouse", "yd_inwarehouse", "yd_material", "yd_batchno", "yd_probegindate", "yd_proenddate", "yd_quantity"});
        }

        //1.获取当前用户的角色
        List<Long> roleIds = PermissionServiceHelper.getBizRolesByUserID(RequestContext.get().getCurrUserId());
        //2.获取下拉框
        ComboEdit comboEdit = this.getControl("yd_businesstype");
        //3.构造下拉框数据
        List<ComboItem> comboItems = new ArrayList<>();

        ComboItem comboItem = new ComboItem();
        comboItem.setCaption(new LocaleString("跨组织调拨（包材、物料）"));
        comboItem.setValue("org_transfer_bc-wl");
        comboItems.add(comboItem);
        //4.根据角色过滤业务类型
        if (roleIds.contains(Long.valueOf(ROLE_ID))) {
            //5.动态给下拉列表赋值
            comboEdit.setComboItems(comboItems);
        }
    }
}
