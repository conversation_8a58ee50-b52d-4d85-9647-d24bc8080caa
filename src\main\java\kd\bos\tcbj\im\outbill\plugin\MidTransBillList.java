package kd.bos.tcbj.im.outbill.plugin;

import kd.bos.form.CloseCallBack;
import kd.bos.form.ShowType;
import kd.bos.form.control.Control;
import kd.bos.form.control.Toolbar;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import kd.bos.tcbj.im.plugin.KdepListPlugin;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.UIUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 库存调拨单中间表列表插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-7-19
 */
public class MidTransBillList extends KdepListPlugin {

    /**菜单按钮_获取PCP调拨单*/
    protected final static String KEY_MENU_PULLPCPTRANSBILL = "yd_btn_getpcptransbill";
    /**菜单按钮_执行结算状态*/
    protected final static String KEY_MENU_DOTRANSSETTLE = "yd_btn_dotranssettle";
    /**菜单按钮_重置结算状态*/
    protected final static String KEY_MENU_RESETTRANSSETTLESTATUS = "yd_btn_resettranssettlestatus";
    /**Action_获取PCP调拨单*/
    protected final static String ACTIONID_PULLPCPTRANSBILL = "ACTIONID_PULLPCPTRANSBILL";

    /**
     * 注册事件
     * <AUTHOR>
     * @date 2022-7-19
     */
    @Override
    public void registerListener(EventObject e) {
        Toolbar toolbar = this.getView().getControl(UIUtils.getMenuBar(this));
        toolbar.addItemClickListener(this);
    }

    @Override
    public void beforeItemClick(BeforeItemClickEvent evt) {
        String itemKey = evt.getItemKey();
        switch (itemKey) {
            case KEY_MENU_PULLPCPTRANSBILL:
                this.showForm(BillTypeHelper.BILLTYPE_RQCS, null, new CloseCallBack(this, ACTIONID_PULLPCPTRANSBILL), ShowType.Modal);
                break;
            case KEY_MENU_DOTRANSSETTLE:
                doTransSettle();
                break;
            case KEY_MENU_RESETTRANSSETTLESTATUS:
                PCPServiceHelper.resetTransSettleStatus(this.getView());
//                this.getView().showSuccessNotification("执行重置结算状态成功！");
                this.getView().invokeOperation("refresh");
                break;
        }
    }

    /**
     * 执行结算处理
     * <AUTHOR>
     * @date 2022-7-19
     */
    private void doTransSettle() {
        // 列表执行需获取执行的日期，获取日期对应的数据进行结算
        UIUtils.checkSelected(this.getView(), "请勾选需要下推的数据！");
        List<Object> idList = UIUtils.getSelectIds(this.getView());
        PCPServiceHelper.doTransSettle(idList, null, false);
        this.getView().showSuccessNotification("执行调拨结算成功！");
        this.getView().invokeOperation("refresh");
    }

    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        if (StringUtils.equals(closedCallBackEvent.getActionId(), ACTIONID_PULLPCPTRANSBILL)) {
            HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
            if (returnData == null || returnData.get("yd_date_wang") == null) return;
            Date beginDate = DateTimeUtils.format2(DateTimeUtils.parse(returnData.get("yd_date_wang"), "EEE MMM dd HH:mm:ss zzz yyyy", Locale.US));
            PCPServiceHelper.pullPcpTransBill(beginDate);
            this.getView().showTipNotification("该功能还在开发阶段，敬请期待！");
        }
    }
}
