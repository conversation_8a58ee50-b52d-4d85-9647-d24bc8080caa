package kd.bos.tcbj.im.outbill.plugin;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.form.ConfirmCallBackListener;
import kd.bos.form.IFormView;
import kd.bos.form.MessageBoxOptions;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.outbill.helper.SettleServiceHelper;
import kd.bos.tcbj.im.outbill.helper.TransSettleServiceHelper;
import kd.bos.tcbj.im.util.DynamicObjectUtil;
import kd.bos.tcbj.im.util.SettleUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.im.util.UIUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.im.outbill.plugin.OtherOutSettleServiceList
 * @className OtherOutSettleServiceList
 * @author: hst
 * @createDate: 2024/06/15
 * @description: 其他出库结算共性列表类
 * @version: v1.0
 */
public class OtherOutSettleServiceList extends AbstractListPlugin {

    /** 重置结算按钮标识 **/
    private final static String RESET_BTN = "yd_other_reset";
    /** 重置同步EAS状态按钮标识 **/
    private final static String RESTATUS_BTN = "yd_other_restatus";
    /** 重置同步EAS状态确认弹窗标识 **/
    private final static String RESTATUS_COM = "restatus_comfirm";

    @Override
    public void itemClick(ItemClickEvent evt) {
        String itemKey = evt.getItemKey();
        switch (itemKey) {
            case RESET_BTN : {
                this.resetTransSettle();
                break;
            }
            case RESTATUS_BTN : {
                this.getView().showConfirm("请确认是否清除选中单据及下游单据同步EAS状态标识？",
                        MessageBoxOptions.YesNo, new ConfirmCallBackListener(RESTATUS_COM, this));
                break;
            }
            default : {
                this.billSettle(itemKey);
                break;
            }
        }
    }

    /**
     * 确认弹窗点击确认后回调事件
     * @author: hst
     * @createDate: 2024/06/15
     * @param messageBoxClosedEvent
     */
    @Override
    public void confirmCallBack(MessageBoxClosedEvent messageBoxClosedEvent) {
        super.confirmCallBack(messageBoxClosedEvent);
        if (RESTATUS_COM.equals(messageBoxClosedEvent.getCallBackId())
                && messageBoxClosedEvent.getResult().getValue() == 6) {
            // 重置结算单据同步EAS状态
            this.resetTransSettleStatus();
        }
    }

    /**
     * 获取需结算数据并执行一盘货结算流程
     * @param key
     * @author: hst
     * @createDate: 2024/06/15
     */
    public void billSettle(String key) {
        // 查询是否有相应的配置信息
        DynamicObject config = QueryServiceHelper.queryOne("yd_settleconfig",
                String.join(",", DynamicObjectUtil.getAllField("yd_settleconfig")),
                new QFilter[]{QFilter.of("yd_type = ? and status = ?",key,"C")});
        if (Objects.nonNull(config)) {
            String filterStr = config.getString("yd_filter");
            String tip = config.getString("yd_prompt");
            QFilter filter = null;
            if (StringUtils.isNotBlank(filterStr)) {
                try {
                    filter = QFilter.of(filterStr);
                } catch (Exception e) {
                    throw new KDBizException("过滤条件转换异常");
                }
                // 获取已经选择的ID集合
                List<Object> idList = UIUtils.getSelectIds(this.getView());
                filter.and(new QFilter("id", QCP.in, idList));
                // 执行结算流程
                SettleServiceHelper.createSettleBill(key, BillTypeHelper.BILLTYPE_OTHEROUTBILL,config,filter,tip,true);
            }
            this.getView().showMessage("执行完成，请查看处理结果！");
            this.getView().invokeOperation("refresh");
        }
    }

    /**
     * 重置结算单据同步EAS状态
     * @author: hst
     * @createDate: 2024/06/15
     */
    private void resetTransSettleStatus () {
        // 获取勾选的数据的id
        UIUtils.checkSelected(this.getView(), "请选择数据！");
        List<Object> idList = UIUtils.getSelectIds(this.getView());

        //根据id获取单据
        String billType = BillTypeHelper.BILLTYPE_OTHEROUTBILL;
        DynamicObject template = BusinessDataServiceHelper.newDynamicObject(billType);
        DynamicObject[] bills = BusinessDataServiceHelper.load(idList.toArray(),template.getDynamicObjectType());

        StringBuffer errMsg = new StringBuffer();
        List<DynamicObject> successBills = new ArrayList<>();
        for (DynamicObject bill : bills) {
            try {
                String srcBillEntity = BillTypeHelper.BILLTYPE_OTHEROUTBILL;
                String tempId = bill==null?"":bill.getPkValue().toString();

                // 获取下游单据
                LinkedList<String> billRelList = new LinkedList<String>();
                SettleUtils.getDownStreamBill(tempId,srcBillEntity,billRelList);

                // 清除下游单据标识
                SettleUtils.clearBillToEASStatus(billRelList);

                // 执行成功，后续修改状态
                successBills.add(bill);
            } catch (Exception e) {
                errMsg.append(e.getMessage()).append("\n");
            }
        }

        if (errMsg.length() > 0) {
            this.getView().showErrorNotification(errMsg.substring(0,errMsg.length() - 1));
        } else {
            for (DynamicObject bill : successBills) {
                // 删除完后，重置其他出库单的单据信息
                bill.set("yd_checkboxfield01", false);
                bill.set("yd_checkboxfield02", false);
                bill.set("yd_checkboxfield03", false);
                bill.set("yd_istoeas", false);
                bill.set("yd_textareafield01", "");
                bill.set("yd_sftj", "");
                bill.set("yd_textareafield03", "");
                bill.set("yd_checkboxfield01", false);
                bill.set("yd_checkboxfield02", false);
                bill.set("yd_checkboxfield03", false);
                bill.set("yd_checkboxfield04", false);
                bill.set("yd_istoeas", false);
                bill.set("yd_textareafield01", "");
                bill.set("yd_sftj", "");
                bill.set("yd_textareafield03", "");
                bill.set("yd_textareafield","");
            }
            SaveServiceHelper.save(successBills.toArray(new DynamicObject[successBills.size()]));
            this.getView().showSuccessNotification("清除下游单据同步EAS状态成功！");
        }
        this.getView().invokeOperation("refresh");
    }

    /**
     * 重置结算单据
     * @author: hst
     * @createDate: 2024/06/16
     */
    private void resetTransSettle () {
        try {
            // 获取勾选的数据的id
            UIUtils.checkSelected(this.getView(), "请选择数据！");
            List<Object> idList = UIUtils.getSelectIds(this.getView());

            //根据id获取单据
            String billType_saleOutBill = BillTypeHelper.BILLTYPE_OTHEROUTBILL;
            DynamicObject template = BusinessDataServiceHelper.newDynamicObject(billType_saleOutBill);
            DynamicObject[] bills = BusinessDataServiceHelper.load(idList.toArray(), template.getDynamicObjectType());

            StringBuffer errMsg = new StringBuffer();
            List<DynamicObject> successBills = new ArrayList<>();
            for (DynamicObject bill : bills) {
                try {
                    // 校验单据是否符合重置结算条件
                    this.checkBillResetSettleStatus(bill);
                    // 重置结算
                    this.resetTransBillSettle(bill);
                    // 执行成功，后续修改状态
                    successBills.add(bill);
                } catch (Exception e) {
                    errMsg.append(e.getMessage()).append("\n");
                }
            }

            if (errMsg.length() > 0) {
                this.getView().showErrorNotification(errMsg.substring(0, errMsg.length() - 1));
            } else {
                for (DynamicObject bill : successBills) {
                    String billStatus = bill.getString("billstatus");
                    if ("C".equals(billStatus) || "B".equals(billStatus)) {
                        // 反审核/撤销
                        String operationKey = "C".equals(billStatus) ? "unaudit" : "unsubmit";
                        String tip = "C".equals(billStatus) ? "反审核" : "撤销";
                        OperationResult result = OperationServiceHelper.executeOperate(operationKey,
                                BillTypeHelper.BILLTYPE_OTHEROUTBILL, new DynamicObject[]{bill}, OperateOption.create());
                        if (!result.isSuccess()) {
                            // 错误摘要
                            throw new KDException("单据" + bill.getString("billno") + tip + "失败，请到其他出库单手动反审核，查看反审核失败原因：" + result.getMessage());
                        }
                    }

                    // 删除完后，重置中间表的单据信息
                    bill.set("yd_isbeginsettle", false);
                    bill.set("yd_settleerror", "");
                    bill.set("yd_issettled", false);
                    bill.set("yd_checkboxfield04", false);
                    bill.set("org", bill.get("yd_oriorg"));
                    bill.set("bizdept", bill.get("yd_oriorg"));
                    bill.set("dept", bill.get("yd_oriorg"));
                    bill.set("yd_oriorg", null);
                    bill.set("yd_settletype", "");
                    bill.set("yd_isoutsettle", false);
                    bill.set("yd_firstinternalbill", false);
                    bill.set("yd_beresetlot", false);
                    bill.set("yd_checkboxfield01", false);
                    bill.set("yd_checkboxfield02", false);
                    bill.set("yd_checkboxfield03", false);
                    bill.set("yd_checkboxfield04", false);
                    bill.set("yd_istoeas", false);
                    bill.set("yd_textareafield01", "");
                    bill.set("yd_sftj", "");
                    bill.set("yd_textareafield03", "");
                    bill.set("yd_textareafield", "");

                    // 对源销售出库单进行仓库重置
                    DynamicObjectCollection oriSaleEntryCol = bill.getDynamicObjectCollection("billentry");
                    for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
                        oriSaleEntry.set("warehouse", oriSaleEntry.getDynamicObject("yd_oriwarehouse"));
                        oriSaleEntry.set("yd_oriwarehouse", null);
                    }

                    // 清除结算分录
                    bill.getDynamicObjectCollection("yd_internalentity").clear();

                    SaveServiceHelper.save(new DynamicObject[]{bill});
                }

                this.getView().showSuccessNotification("执行重置结算状态成功！");
                this.getView().invokeOperation("refresh");
            }
        } catch (Exception e) {
            this.getView().showErrorNotification(e.getMessage());
            this.getView().invokeOperation("refresh");
        }
    }

    /**
     * 重置结算前校验单据状态
     * @param bill
     * @uathor: hst
     * @createDate: 2024/06/16
     */
    private static void checkBillResetSettleStatus (DynamicObject bill) {
        String billNo = bill.getString("billno");
        // 已发起结算
        if (!bill.getBoolean("yd_isbeginsettle")) {
            throw new KDBizException("其他出库单【" + billNo + "】的未发起结算，无需执行清理操作！");
        }
        // 未同步EAS
        if (bill.getBoolean("yd_checkboxfield03")) {
            throw new KDBizException("其他出库单【" + billNo + "】已同步EAS，无法执行清理操作！");
        }
    }

    /**
     * 重置结算
     * @param bill
     * @author: hst
     * @createDate: 2024/06/16
     */
    private static void resetTransBillSettle (DynamicObject bill) {
        String srcBillEntity = BillTypeHelper.BILLTYPE_OTHEROUTBILL;
        String tempId = bill==null?"":bill.getPkValue().toString();

        // 获取下游单据
        LinkedList<String> billRelList = new LinkedList<String>();
        SettleUtils.getDownStreamBill(tempId,srcBillEntity,billRelList);

        // 清除下游单据
        SettleUtils.clearDownStreamBill(billRelList);
    }
}
