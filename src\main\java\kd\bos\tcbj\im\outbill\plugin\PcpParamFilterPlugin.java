package kd.bos.tcbj.im.outbill.plugin;

import kd.bos.entity.datamodel.events.BizDataEventArgs;
import kd.bos.form.control.events.BeforeClickEvent;
import kd.bos.form.field.DateEdit;
import kd.bos.tcbj.im.plugin.KdepFormPlugin;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import scala.annotation.meta.param;

import java.util.Date;
import java.util.EventObject;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * PCP参数过滤插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-19
 */
public class PcpParamFilterPlugin extends KdepFormPlugin {

    /**确认按钮*/
    protected static final String KEY_BTN_OK = "btnok";
    /**取消按钮*/
    protected static final String KEY_BTN_CANCEL = "btncancel";
    /**开始日期*/
    protected static final String KEY_FIELD_BEGINDATE = "yd_begindate";
    /**结束日期*/
    protected static final String KEY_FIELD_ENDDATE = "yd_enddate";
    /**单据编码*/
    protected static final String KEY_FIELD_BILLNO = "yd_billno";

    @Override
    public void initialize() {
        this.addClickListeners(KEY_BTN_CANCEL, KEY_BTN_OK);
    }

    @Override
    public void beforeClick(BeforeClickEvent evt) {
        String key = this.getKey(evt);
        if (StringUtils.equals(key, KEY_BTN_OK)) {
            actionOk(evt);
        }else if (StringUtils.equals(key, KEY_BTN_CANCEL)) {
            actionCancel(evt);
        }
    }

    /**
     * 确认
     * <AUTHOR>
     * @date 2022-9-19
     */
    private void actionOk(BeforeClickEvent evt) {
        // 校验字段是否已经填写
        Date beginDate = (Date) this.getModel().getValue(KEY_FIELD_BEGINDATE);
        Date endDate = (Date) this.getModel().getValue(KEY_FIELD_ENDDATE);
        String billNo = (String) this.getModel().getValue(KEY_FIELD_BILLNO);
        if (beginDate == null) {
            this.getView().showTipNotification("开始日期不能为空！");
            evt.setCancel(true);
            return;
        }else if (endDate == null) {
            this.getView().showTipNotification("结束日期不能为空！");
            evt.setCancel(true);
            return;
        }else if (beginDate.after(endDate)) {
            this.getView().showTipNotification("开始日期不能比结束日期晚！");
            evt.setCancel(true);
            return;
        }

        // 将对应的数据转化为json返回
        Map<String, String> param = new LinkedHashMap<>();
        param.put("beginDate", DateTimeUtils.format(beginDate, DateTimeUtils.SDF_TIME));
        param.put("endDate", DateTimeUtils.format(endDate, DateTimeUtils.SDF_TIME));
        param.put("billNo", billNo);

        // 返回日期给到前端
        this.getView().returnDataToParent(JsonUtils.toJsonString(param));
        this.getView().close();
    }

    /**
     * 取消
     * <AUTHOR>
     * @date 2022-9-19
     */
    private void actionCancel(BeforeClickEvent evt) {
        this.getView().returnDataToParent(null);
        this.getView().close();
    }

    /**
     * 创建时数据初始化
     * <AUTHOR>
     * @date 2022-9-19
     */
    @Override
    public void afterCreateNewData(EventObject evt) {
        // 默认开始日期和结束日期为当前日期的开始和结束
        this.getModel().setValue(KEY_FIELD_BEGINDATE, DateTimeUtils.getCurrentBeginTime());
        this.getModel().setValue(KEY_FIELD_ENDDATE, DateTimeUtils.getCurrentEndTime());
    }
}
