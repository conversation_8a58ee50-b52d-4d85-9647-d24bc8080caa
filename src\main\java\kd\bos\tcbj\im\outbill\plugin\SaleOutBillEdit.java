package kd.bos.tcbj.im.outbill.plugin;

import java.util.EventObject;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillShowParameter;
import kd.bos.bill.OperationStatus;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.ShowType;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;

/**
* @auditor yanzuwei
* @date 2021年12月28日
* 
*/
public class SaleOutBillEdit extends AbstractBillPlugIn implements HyperLinkClickListener {
	
	@Override
	public void registerListener(EventObject e) {
		super.registerListener(e);
		
		EntryGrid entry = this.getView().getControl("yd_internalentity");
		entry.addHyperClickListener(this);
	}
	
	/**
	 * 单元格点击超链接事件
	 */
	@Override
	public void hyperLinkClick(HyperLinkClickEvent evt) {
//		System.out.println("点击分录超链接"+evt.getFieldName()+"；"+evt.getSource());
		if ("yd_internalbillno".equals(evt.getFieldName())) {
			int rowIndex = evt.getRowIndex();
			
			String billId = this.getModel().getValue("yd_internalbillid", rowIndex).toString();
			String billType = this.getModel().getValue("yd_internalbilltype", rowIndex).toString();
			
			// 打开单据界面
			BillShowParameter parameter = new BillShowParameter();
			parameter.setFormId(billType);
			parameter.setPkId(billId);
			parameter.setStatus(OperationStatus.VIEW);
			parameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
			parameter.setHasRight(true);
			getView().showForm(parameter);
		}
	}
}
