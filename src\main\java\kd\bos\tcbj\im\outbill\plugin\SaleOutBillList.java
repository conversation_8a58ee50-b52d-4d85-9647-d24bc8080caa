package kd.bos.tcbj.im.outbill.plugin;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kd.bos.tcbj.im.common.enums.OutSettleTypeEnum;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import kd.bos.tcbj.im.util.ORMUtils;
import kd.bos.tcbj.im.util.UIUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.botp.ConvertDataService;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.botp.runtime.BFRow;
import kd.bos.entity.botp.runtime.BFRowId;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.list.IListView;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.outbill.helper.SaleOutBillMserviceHelper;
import kd.epm.eb.ebBusiness.serviceHelper.MutexServiceHelper;

public class SaleOutBillList extends AbstractListPlugin {
	
	protected int amountPrecision = 2;
	protected int curAmountPrecision = 2;
	protected int pricePrecision = 10;
	protected int curPricePrecision = 10;
	protected BigDecimal ONEHUNDRED = new BigDecimal(100);

	@Override
	public void itemClick(ItemClickEvent evt) {
		String itemKey=evt.getItemKey();
		// 内部结算功能
		if (StringUtil.equalsIgnoreCase("yd_bar_internalsettle", itemKey)) {
			internalSettle();
			this.getView().showMessage("执行完成，请查看生成结果！");
			this.getView().invokeOperation("refresh");
		}
		// 清理内部结算单据功能
		if (StringUtil.equalsIgnoreCase("yd_bar_clearinternal", itemKey)) {
			clearInternal();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		
		// 重置单据标识使得用户可以操作重置内部结算，控制只能管理员才有权限
		if (StringUtil.equalsIgnoreCase("yd_bar_clearinternal_sys", itemKey)) {
			clearInternalBySys();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}

		// 发起直营店结算
		if (StringUtil.equalsIgnoreCase("yd_bar_directsettle", itemKey)) {
			directSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// 重置直营店结算
		else if (StringUtil.equalsIgnoreCase("yd_bar_resetdirect", itemKey)) {
			resetDirectSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}

		// 发起PCP销售结算
		if (StringUtil.equalsIgnoreCase("yd_bar_pcpsettle", itemKey)) {
			pcpSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// 重置PCP销售结算
		else if (StringUtil.equalsIgnoreCase("yd_bar_resetpcp", itemKey)) {
			resetPCPSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// 发起PCP虚体组织销售结算
		if (StringUtil.equalsIgnoreCase("yd_bar_virtualsettle", itemKey)) {
			pcpVirtualSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// 重置PCP虚体组织销售结算
		else if (StringUtil.equalsIgnoreCase("yd_bar_resetvirtual", itemKey)) {
			resetVirtualPCPSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// update by hst 2023/03/22 发起PCP线下一盘货销售结算
		if (StringUtil.equalsIgnoreCase("yd_bar_offlinesettle", itemKey)) {
			pcpOfflineSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// update by hst 2023/03/22 重置PCP线下一盘货销售结算
		else if (StringUtil.equalsIgnoreCase("yd_bar_resetoffline", itemKey)) {
			resetPCPOfflineSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// update by hst 2023/04/14 发起成品仓一盘货销售结算
		if (StringUtil.equalsIgnoreCase("yd_bar_waresettle", itemKey)) {
			warehouseSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// update by hst 2023/04/14 重置成品仓一盘货销售结算
		else if (StringUtil.equalsIgnoreCase("yd_bar_resetware", itemKey)) {
			resetWarehouseSettle();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
	}
	
	/**
	 * 重置单据标识使得用户可以操作重置内部结算，控制只能管理员才有权限
	 */
	private void clearInternalBySys() {
		// 获取勾选的数据
		ListSelectedRowCollection selectedData = ((IListView)this.getView()).getSelectedRows(); 
		Set<String> idSet = new HashSet<String>();
		for (ListSelectedRow row : selectedData) {
			Object pk = row.getPrimaryKeyValue();
			idSet.add(pk.toString());
		}
		
		if(idSet.size() == 0) {
			throw new KDBizException("请选择数据！");
		}
		
		DynamicObject[] bills = new DynamicObject[idSet.size()];
		int i=0;
		for (String billId : idSet) {
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(billId, "im_saloutbill");
			saleoutBillObj.set("yd_internaltoeas", false);
			bills[i] = saleoutBillObj;
			i++;
		}
		SaveServiceHelper.save(bills);// 保存
	}
	
	/**
	 * 清理内部结算单据
	 */
	private void clearInternal() {
		// 获取勾选的数据
		ListSelectedRowCollection selectedData = ((IListView)this.getView()).getSelectedRows(); 
		Set<String> idSet = new HashSet<String>();
		for (ListSelectedRow row : selectedData) {
			Object pk = row.getPrimaryKeyValue();
			idSet.add(pk.toString());
		}
		
		if(idSet.size() == 0) {
			throw new KDBizException("请选择数据！");
		}
		
		ConvertDataService dataService = new ConvertDataService();
		for (String billId : idSet) {
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(billId, "im_saloutbill");
			// 不是审核状态不允许
			String srcbillstatus = saleoutBillObj.getString("billstatus");
			Boolean isFirstBill = saleoutBillObj.getBoolean("yd_internalsettle");
			Boolean isInternalToEas = saleoutBillObj.getBoolean("yd_internaltoeas");  // 是否已成功传递EAS，已成功的需要手工回退
			Boolean isFromE3 = saleoutBillObj.getBoolean("yd_frome3");  // 是否来源E3
			String outSettleType = saleoutBillObj.getString("yd_outsettletype");  // 委外结算类型
			if (!isFromE3 && !OutSettleTypeEnum.DISTRIBUTOROUTSETTLE.getCode().equals(outSettleType)) {
				throw new KDBizException("源销售出库单"+saleoutBillObj.getString("billno")+"不是经销商一盘货结算或者经销商一盘货委外结算，不能使用该重置功能！");
			}
			if (isInternalToEas) {
				throw new KDBizException("源销售出库单"+saleoutBillObj.getString("billno")+"涉及的内部结算单据已全部传递EAS，需要在EAS与中台手工回退！");
			}
			if (!isFirstBill) {
				throw new KDBizException("源销售出库单"+saleoutBillObj.getString("billno")+"不是末级组织结算单，无法执行清理操作！");
			}
			if (!"C".equals(srcbillstatus)) {
				throw new KDBizException("源销售出库单"+saleoutBillObj.getString("billno")+"未发起过内部结算，无法执行清理操作！");
			}
			// 如果结算链路中存在EAS已审核的单据就不允许重置结算,yzl,20230206
			checkGFToEas(saleoutBillObj);
			
//			String srcBillEntity = saleoutBillObj.getDataEntityType().getExtendName();
			String srcBillEntity = "im_saloutbill";
			String tempId = billId;
			LinkedList<String> billRelList = new LinkedList<String>();
			while(StringUtils.isNotEmpty(tempId)) {
				// 遍历单据，获取下游单据数据
//				Map<String, HashSet<Long>> targetBill = BFTrackerServiceHelper.findTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
				Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
				if (dirTargetBill.size() == 1) {
					Long tempLongId = dirTargetBill.keySet().iterator().next();
					List<BFRow> targetRow = dirTargetBill.get(tempLongId);
					BFRowId targetBillRow = targetRow.get(0).getId();
					srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
					tempId = targetBillRow.getBillId().toString();
					billRelList.add(srcBillEntity+"&"+tempId);
				} else {
					tempId = "";
					srcBillEntity = "";
				}
				System.out.println(srcBillEntity+tempId);
			}
			
			// 从下到上删单
			for(int i=billRelList.size()-1;i>=0;i--) {
				String[] keys = billRelList.get(i).split("&");
				String entity = keys[0];
				String tempBillId = keys[1];
				DynamicObject billObj = BusinessDataServiceHelper.loadSingle(tempBillId, entity);
				String billstatus = billObj.getString("billstatus");
				String billName = "";
				if ("im_purinbill".equals(entity)) {
					billName = "采购入库单";
				}
				if ("im_saloutbill".equals(entity)) {
					billName = "销售出库单";
				}
				if ("B".equals(billstatus)) {
					// 撤销再删除
					OperationResult unsubmitResult = OperationServiceHelper.executeOperate("unsubmit", entity, new Object[]{tempBillId}, OperateOption.create());
					if (!unsubmitResult.isSuccess()) {
						// 错误摘要
						throw new KDException("单据"+billObj.getString("billno") + "撤销提交失败，请到"+billName+"手动撤销提交，查看撤销提交失败原因：" + unsubmitResult.getMessage());
					}
				} else if ("C".equals(billstatus)) {
					// 反审再删除
					OperationResult unauditResult = OperationServiceHelper.executeOperate("unaudit", entity, new Object[]{tempBillId}, OperateOption.create());
					if (!unauditResult.isSuccess()) {
						// 错误摘要
						throw new KDException("单据"+billObj.getString("billno") + "反审核失败，请到"+billName+"手动反审核，查看反审核失败原因：" + unauditResult.getMessage());
					}
				}
				
				// 删除单据
				OperationResult deleteResult = OperationServiceHelper.executeOperate("delete", entity, new Object[]{tempBillId}, OperateOption.create());
				if (!deleteResult.isSuccess()) {
					// 错误摘要
					throw new KDException("单据"+billObj.getString("billno") + "删除失败，请到"+billName+"手动删除，查看删除失败原因：" + deleteResult.getMessage());
				}
			}
			
			// 如果没有报错则重置反审核源销售出库单并重置客户、仓库、推送EAS标识、报错信息、是否结算
			// 反审再删除
			OperationResult unauditResult = OperationServiceHelper.executeOperate("unaudit", "im_saloutbill", new Object[]{billId}, OperateOption.create());
			if (!unauditResult.isSuccess()) {
				// 错误摘要
				throw new KDException("单据"+saleoutBillObj.getString("billno") + "反审核失败，请到销售出库单手动反审核，查看反审核失败原因：" + unauditResult.getMessage());
			}
			
			// 最终的销售收货的客户+品牌得到原客户
			DynamicObject finalCustomer = saleoutBillObj.getDynamicObject("customer");
			String finalCustomerId = finalCustomer.getPkValue().toString();  // 原汇总生成的客户ID
			
			// 获取第一行分录的品牌，然后再获取对应的内部交易关系
			DynamicObject oneEntryObj = saleoutBillObj.getDynamicObjectCollection("billentry").get(0);
			DynamicObject brandObj = oneEntryObj.getDynamicObject("yd_basedata_pinpai");
			String brandNo = brandObj.getString("number");  // 品牌编码
			// 获取品牌对应的内部结算关系以及最后一级的组织，如果是正向则从上到下，升序
			QFilter brandFilter = new QFilter("yd_brand.number", QCP.equals, brandNo);
			brandFilter.and(new QFilter("yd_newcustomer.id", QCP.equals, finalCustomerId));
			DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_innerrelbill", 
					"yd_oricustomer.id oriCusId", brandFilter.toArray(), null);
			
			DataSet copySet = settleDataSet.copy();
			Row firstRow = copySet.next();
			String oriCustomerId = firstRow.getString("oriCusId");  // 原客户ID
			DynamicObject oriCustomer = BusinessDataServiceHelper.loadSingle(oriCustomerId, "bd_customer");  // 原客户
			
			saleoutBillObj.set("yd_checkboxsf01", false);  // EAS是否保存
			saleoutBillObj.set("yd_checkboxsf02", false);  // EAS是否提交
			saleoutBillObj.set("yd_checkboxsf", false);  // EAS是否审核
			saleoutBillObj.set("yd_textareafield02", "");  // EAS保存失败原因
			saleoutBillObj.set("yd_sftj", "");  // EAS提交失败原因
			saleoutBillObj.set("yd_textareafield", "");  // EAS审核失败原因
			saleoutBillObj.set("yd_internalsettle", false);  // 是否已结算
			saleoutBillObj.set("yd_firstinternalbill", false);  // 是否首笔结算单
			saleoutBillObj.set("yd_settleerror", "");  // 结算失败原因
			
			saleoutBillObj.set("customer", oriCustomer);  // 客户
			saleoutBillObj.set("yd_cuschannel", oriCustomer.getDynamicObject("yd_channel"));  // 客户所属渠道
			saleoutBillObj.set("yd_frome3", true); // 是否来源E3
			saleoutBillObj.set("yd_outsettletype", null); // 委外结算类型
			// 对源销售出库单进行单价重置
			DynamicObjectCollection oriSaleEntryCol = saleoutBillObj.getDynamicObjectCollection("billentry");
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
				oriSaleEntry.set("warehouse", oriSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
				oriSaleEntry.set("reccustomer", oriCustomer);
				oriSaleEntry.set("settlecustomer", oriCustomer);
				oriSaleEntry.set("payingcustomer", oriCustomer);
			}
			
			// 清空内部结算单据分录
			DynamicObjectCollection internalCol = saleoutBillObj.getDynamicObjectCollection("yd_internalentity");
			internalCol.clear();
			
		    SaveServiceHelper.save(new DynamicObject[] {saleoutBillObj});// 保存
			System.out.println("结束清理内部结算流程");
		}
	}

	// 如果结算链路中存在EAS已审核的单据就不允许重置结算,yzl,20230206
	private void checkGFToEas(DynamicObject saleoutBillObj) {
		if (saleoutBillObj.getDynamicObjectCollection("yd_internalentity").size() > 0) {
			DynamicObjectCollection internalCol = saleoutBillObj.getDynamicObjectCollection("yd_internalentity");
			for (DynamicObject tmpObj : internalCol) {
				String srcBillId = tmpObj.getString("yd_internalbillid");
				String srcBillNum = tmpObj.getString("yd_internalbillno");
				String srcBillType = tmpObj.getString("yd_internalbilltype");
				QFilter filter = new QFilter("id", QCP.equals, srcBillId);
				filter.and(QFilter.of("yd_checkboxsf =?", "1"));
				if ("im_saloutbill".equals(srcBillType) && QueryServiceHelper.exists(srcBillType, filter.toArray())) {
					throw new KDBizException("销售出库单"+saleoutBillObj.getString("billno")+"结算链路中的"+srcBillNum+"出库单已同步EAS，请联系管理员清理标识后再执行重置结算！");
				}
			}
		}
	}

	/**
	 * 测试后台事务销售出库单生成销售出库单功能
	 * 获取billstatus状态为保存、yd_internalsettle是否完成内部结算流程为否、customer客户为《客户交易价格表》中的客户的销售出库单数据
	 */
	private void internalSettle() {
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");
		
		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
//		filter.and(QFilter.of("billentry.yd_ph!=?", ""));  // 批次号不为空
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "1"));  // 从E3过来
		filter.and(QFilter.of("yd_frome3 =?", "1"));  // 是否按品牌结算为是
		// 并且是新流程按品牌分单的销售出库单数据，才能获取到这个品牌对应的内部交易流程yd_innerrelbill
		DataSet cusDataSet = QueryServiceHelper.queryDataSet("jy", "yd_orgcuspricebill",
				"yd_customer.number customerNum", null, null);
		List<String> customerList = new ArrayList<String>();
		for (Row row : cusDataSet) {
			customerList.add(row.getString("customerNum"));
		}
		Map<String, Object> cusMap = new HashMap<>();
		cusMap.put("customers", customerList);
		filter.and(new QFilter("customer.number", QCP.in, customerList));  // 本次上线的客户
		
		ListSelectedRowCollection selectedData = ((IListView)this.getView()).getSelectedRows(); 
		Set<String> idSet = new HashSet<String>();
		for (ListSelectedRow row : selectedData) {
			Object pk = row.getPrimaryKeyValue();
			idSet.add(pk.toString());
		}
		
		if(idSet.size() == 0) {
			throw new KDBizException("请选择数据！");
		}
		
//		filter.and(QFilter.of("billno=?", billObj.getString("billno")));  // 测试单号
//		filter.and(QFilter.of("billno=?", "XSCK-211215-000014"));  // 测试单号
		filter.and(new QFilter("id", QCP.in, idSet));  // 测试单号
		
		DataSet saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", "im_saloutbill", "id,billno",
				new QFilter[] { filter }, null);
		
		if (!saleBillSet.hasNext()) {
			throw new KDBizException("所选单据都不满足内部结算条件（单据为暂存、来源系统为E3、未完成内部结算流程、已设置内部结算关系的销售出库单），无法生成内部结算流程单据！");
		}
		
		Set<String> doneBillSet = new HashSet<String>();
		for (Row row : saleBillSet) {
			// 源末级销售出库单ID
			String saleoutBillId = row.getString("id");
			System.out.println("销售出库单ID："+saleoutBillId);
			if (doneBillSet.contains(saleoutBillId)) {
				continue;
			}
			
			// 源末级销售出库单
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			
			ApiResult result =new ApiResult();
			// 如果是biztype业务类型是退货类型2101的需要走反方向，如果业务类型是销售类型210的走正方向
			DynamicObject biztype = saleoutBillObj.getDynamicObject("biztype");
			if ("210".equals(biztype.getString("number"))) {
				// 正向流程
//				createForwardBill(saleoutBillId);
				result = SaleOutBillMserviceHelper.createForwardBill(saleoutBillId);
			} else if ("2101".equals(biztype.getString("number"))) {
				// 逆向流程
//				createBackwardBill(saleoutBillId);
				result = SaleOutBillMserviceHelper.createBackwardBill(saleoutBillId);
			}
			if (!result.getSuccess()) {
				throw new KDBizException(result.getMessage());
			}
			System.out.println("结束内部流程");
			
			// 使用关联关系将生成的下游单据保存到源销售出库单上
			InternalSettleServiceHelper.saveBotpBills(saleoutBillId);
			
			doneBillSet.add(saleoutBillId);
		}
	}

	/**
	 * 发起直营店结算
	 * <AUTHOR>
	 * @date 2022-6-6
	 */
	public void directSettle() {

		String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;
		String lock_opKey = "directSettle"; // 解锁/锁定 的操作标识

		// 获取当前的日期时间
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");

		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "1"));  // 从E3过来
		filter.and(QFilter.of("yd_isdirectsettle =?", "1"));  // 是否直营结算为“是”

		UIUtils.checkSelected(this.getView(), "请选择数据！");
		// 获取已经选择的ID集合
		List<Object> idList = UIUtils.getSelectIds(this.getView());
		filter.and(new QFilter("id", QCP.in, idList));  // 测试单号

		DataSet saleBillSet = null;
		try {
			saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", billType_saleOutBill, "id,billno", filter.toArray(), null);
			if (!saleBillSet.hasNext()) {
				throw new KDBizException("所选单据都不满足直营结算条件（单据为暂存、来源系统为E3、未完成直营店结算流程、已设置内部结算关系的销售出库单），无法生成直营店结算流程单据！");
			}

			Set<String> doneBillSet = new HashSet<>();
			for (Row row : saleBillSet) {
				// 源末级销售出库单ID
				String saleoutBillId = row.getString("id");
				System.out.println("销售出库单ID：" + saleoutBillId);
				if (doneBillSet.contains(saleoutBillId)) {
					continue;
				}

				// 如果有互斥锁则提示不允许操作,yzw20220422
				Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(saleoutBillId, billType_saleOutBill, lock_opKey);
				if (lockInfo != null) {
					throw new KDBizException("系统定时的调度计划在执行中，请勿重复操作发起内部结算！");
				}

				// 对进行结算的出库单进行加锁,yzw20220422
				MutexServiceHelper.request(saleoutBillId, billType_saleOutBill, lock_opKey);

				try {
					// 源末级销售出库单
					DynamicObject saleOutInfo = BizHelper.getDynamicObjectById(billType_saleOutBill, saleoutBillId);

					ApiResult result = new ApiResult();
					// 如果是biztype业务类型是退货类型2101的需要走反方向，如果业务类型是销售类型210的走正方向
					DynamicObject biztype = saleOutInfo.getDynamicObject("biztype");
					if ("210".equals(biztype.getString("number"))) {
						// 正向流程
						result = SaleOutBillMserviceHelper.createDirectForwardBill(saleoutBillId);
					} else if ("2101".equals(biztype.getString("number"))) {
						// 逆向流程
						result = SaleOutBillMserviceHelper.createDirectBackwardBill(saleoutBillId);
					}
					if (!result.getSuccess()) {
						throw new KDBizException(result.getMessage());
					}
					System.out.println("结束直营店内部流程");

					// 使用关联关系将生成的下游单据保存到源销售出库单上
					InternalSettleServiceHelper.saveBotpBills(saleoutBillId);

					doneBillSet.add(saleoutBillId);
				}finally {
					// 释放锁，yzw20220422
					MutexServiceHelper.release(saleoutBillId, billType_saleOutBill, lock_opKey);
				}
			}
		}finally {
			// 释放DataSet
			ORMUtils.close(saleBillSet);
		}
	}

	/**
	 * 重置直营店结算
	 * <AUTHOR>
	 * @date 2022-6-6
	 */
	public void resetDirectSettle() {

		String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;

		// 获取勾选的数据
		UIUtils.checkSelected(this.getView(), "请选择数据！");
		List<Object> idList = UIUtils.getSelectIds(this.getView());

		ConvertDataService dataService = new ConvertDataService();
		for (Object billId : idList) {
			DynamicObject saleOutInfo = BizHelper.getDynamicObjectById(billType_saleOutBill, billId);
			// 不是审核状态不允许
			String srcbillstatus = saleOutInfo.getString("billstatus");
			Boolean isFirstBill = saleOutInfo.getBoolean("yd_internalsettle");
			Boolean isInternalToEas = saleOutInfo.getBoolean("yd_internaltoeas");  // 是否已成功传递EAS，已成功的需要手工回退
			String billNo = saleOutInfo.getString("billno");
			Boolean isDirectSettle = saleOutInfo.getBoolean("yd_isdirectsettle");  // 是否直营店结算
			String outSettleType = saleOutInfo.getString("yd_outsettletype");  // 委外结算类型
			if (!isDirectSettle && !OutSettleTypeEnum.DIRECTOUTSETTLE.getCode().equals(outSettleType)) {
				throw new KDBizException("源销售出库单"+billNo+"不是直营店一盘货结算或者直营店一盘货委外结算，不能使用该重置功能！");
			}
			if (isInternalToEas) {
				throw new KDBizException("源销售出库单"+billNo+"涉及的内部结算单据已全部传递EAS，需要在EAS与中台手工回退！");
			}
			if (!isFirstBill) {
				throw new KDBizException("源销售出库单"+billNo+"不是末级组织结算单，无法执行清理操作！");
			}
			if (!"C".equals(srcbillstatus)) {
				throw new KDBizException("源销售出库单"+billNo+"未发起过内部结算，无法执行清理操作！");
			}
			// 如果结算链路中存在EAS已审核的单据就不允许重置结算,yzl,20230206
			checkGFToEas(saleOutInfo);
			
//			String srcBillEntity = saleoutBillObj.getDataEntityType().getExtendName();
			String srcBillEntity = billType_saleOutBill;
			String tempId = billId==null?"":billId.toString();
			LinkedList<String> billRelList = new LinkedList<String>();
			while(StringUtils.isNotEmpty(tempId)) {
				// 遍历单据，获取下游单据数据
//				Map<String, HashSet<Long>> targetBill = BFTrackerServiceHelper.findTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
				Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
				if (dirTargetBill.size() == 1) {
					Long tempLongId = dirTargetBill.keySet().iterator().next();
					List<BFRow> targetRow = dirTargetBill.get(tempLongId);
					BFRowId targetBillRow = targetRow.get(0).getId();
					srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
					tempId = targetBillRow.getBillId().toString();
					billRelList.add(srcBillEntity+"&"+tempId);
				} else {
					tempId = "";
					srcBillEntity = "";
				}
				System.out.println(srcBillEntity+tempId);
			}

			// 从下到上删单
			for(int i=billRelList.size()-1;i>=0;i--) {
				String[] keys = billRelList.get(i).split("&");
				String entity = keys[0];
				String tempBillId = keys[1];
				DynamicObject billObj = BusinessDataServiceHelper.loadSingle(tempBillId, entity);
				String billstatus = billObj.getString("billstatus");
				String billName = "";
				if ("im_purinbill".equals(entity)) {
					billName = "采购入库单";
				}
				if ("im_saloutbill".equals(entity)) {
					billName = "销售出库单";
				}
				if ("B".equals(billstatus)) {
					// 撤销再删除
					OperationResult unsubmitResult = OperationServiceHelper.executeOperate("unsubmit", entity, new Object[]{tempBillId}, OperateOption.create());
					if (!unsubmitResult.isSuccess()) {
						// 错误摘要
						throw new KDException("单据"+billObj.getString("billno") + "撤销提交失败，请到"+billName+"手动撤销提交，查看撤销提交失败原因：" + unsubmitResult.getMessage());
					}
				} else if ("C".equals(billstatus)) {
					// 反审再删除
					OperationResult unauditResult = OperationServiceHelper.executeOperate("unaudit", entity, new Object[]{tempBillId}, OperateOption.create());
					if (!unauditResult.isSuccess()) {
						// 错误摘要
						throw new KDException("单据"+billObj.getString("billno") + "反审核失败，请到"+billName+"手动反审核，查看反审核失败原因：" + unauditResult.getMessage());
					}
				}

				// 删除单据
				OperationResult deleteResult = OperationServiceHelper.executeOperate("delete", entity, new Object[]{tempBillId}, OperateOption.create());
				if (!deleteResult.isSuccess()) {
					// 错误摘要
					throw new KDException("单据"+billObj.getString("billno") + "删除失败，请到"+billName+"手动删除，查看删除失败原因：" + deleteResult.getMessage());
				}
			}

			// 如果没有报错则重置反审核源销售出库单并重置客户、仓库、推送EAS标识、报错信息、是否结算
			// 反审再删除
			OperationResult unauditResult = OperationServiceHelper.executeOperate("unaudit", "im_saloutbill", new Object[]{billId}, OperateOption.create());
			if (!unauditResult.isSuccess()) {
				// 错误摘要
				throw new KDException("单据"+saleOutInfo.getString("billno") + "反审核失败，请到销售出库单手动反审核，查看反审核失败原因：" + unauditResult.getMessage());
			}

			// 最终的销售收货的客户+品牌得到原客户
			DynamicObject finalCustomer = saleOutInfo.getDynamicObject("customer");

			saleOutInfo.set("yd_checkboxsf01", false);  // EAS是否保存
			saleOutInfo.set("yd_checkboxsf02", false);  // EAS是否提交
			saleOutInfo.set("yd_checkboxsf", false);  // EAS是否审核
			saleOutInfo.set("yd_textareafield02", "");  // EAS保存失败原因
			saleOutInfo.set("yd_sftj", "");  // EAS提交失败原因
			saleOutInfo.set("yd_textareafield", "");  // EAS审核失败原因
			saleOutInfo.set("yd_internalsettle", false);  // 是否已结算
			saleOutInfo.set("yd_firstinternalbill", false);  // 是否首笔结算单
			saleOutInfo.set("yd_settleerror", "");  // 结算失败原因
			saleOutInfo.set("yd_isdirectsettle", true); // 是否直营店结算
			saleOutInfo.set("yd_outsettletype", null); // 委外结算类型

			// 对源销售出库单进行单价重置
			DynamicObjectCollection oriSaleEntryCol = saleOutInfo.getDynamicObjectCollection("billentry");
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
				oriSaleEntry.set("warehouse", oriSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
			}

			// 清空内部结算单据分录
			DynamicObjectCollection internalCol = saleOutInfo.getDynamicObjectCollection("yd_internalentity");
			internalCol.clear();

			SaveServiceHelper.save(new DynamicObject[] {saleOutInfo});// 保存
			System.out.println("结束清理内部结算流程");
		}
	}
	/**
	 * 发起PCP销售结算
	 * <AUTHOR>
	 * @date 2022/10/27
	 */
	public void pcpSettle() {
		DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
				new QFilter[]{new QFilter("number",QFilter.equals,"PCP_INVENTORY_CONTROL")});
		String value = param.getString("name");
		boolean isSettle = kd.bos.tcbj.im.util.StringUtils.isNotBlank(value) ? "true".equals(value) ? true : false : false;
		if (!isSettle) {
			this.getView().showTipNotification("当前PCP开门红结算功能已关闭，如需结算请前往参数配置表配置！");
			return;
		}
		String lock_opKey = "PCPSettle"; // 解锁/锁定 的操作标识
		// 获取当前的日期时间
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");
		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "4"));  // 从PCP过来
		filter.and(QFilter.of("yd_isinventory =?", true));  // 从一盘货结算
		UIUtils.checkSelected(this.getView(), "请选择数据！");
		// 获取已经选择的ID集合
		List<Object> idList = UIUtils.getSelectIds(this.getView());
		filter.and(new QFilter("id", QCP.in, idList));
		String tip = "所选单据都不满足PCP结算条件（单据为暂存、来源系统为PCP、未完成内部结算流程、一盘货结算），无法生成内部结算流程单据！";
		PCPServiceHelper.createPCPSettleBill(BillTypeHelper.BILLTYPE_SALEOUTBILL, lock_opKey, filter, tip);
	}

	/**
	 * 重置PCP一盘货结算
	 * <AUTHOR>
	 * @date 2022/10/31
	 */
	public void resetPCPSettle() {
		// 获取勾选的数据的id
		UIUtils.checkSelected(this.getView(), "请选择数据！");
		List<Object> idList = UIUtils.getSelectIds(this.getView());
		//根据id获取单据
		String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;
		DynamicObject template = BusinessDataServiceHelper.newDynamicObject(billType_saleOutBill);
		DynamicObject[] bills = BusinessDataServiceHelper.load(idList.toArray(),template.getDynamicObjectType());

		for (DynamicObject bill : bills) {
			//校验单据是否符合重置结算条件
			PCPServiceHelper.checkPCPBillResetSettle(bill);
			// update by hst 2023/03/22 将重置代码独立出来，以让开门红和线下一盘货共用
			this.resetPCPSettle(bill);
		}
	}
	
	/**
	 * 将结算信息重置
	 * @param bill
	 * @author: hst
	 * @createDate: 2023/03/22
	 */
	private void resetPCPSettle(DynamicObject bill) {
		String srcBillEntity = BillTypeHelper.BILLTYPE_SALEOUTBILL;
		String tempId = bill==null?"":bill.getPkValue().toString();
		//清除下游单据
		PCPServiceHelper.clearDownStreamBill(tempId,srcBillEntity);
		// 如果没有报错则重置反审核源销售出库单并重置客户、仓库、推送EAS标识、报错信息、是否结算
		// 反审再删除
		OperationResult unauditResult = OperationServiceHelper.executeOperate("unaudit", "im_saloutbill",
				new Object[]{bill.getPkValue()}, OperateOption.create());
		if (!unauditResult.isSuccess()) {
			// 错误摘要
			throw new KDException("单据"+bill.getString("billno") + "反审核失败，请到销售出库单手动反审核，查看反审核失败原因：" + unauditResult.getMessage());
		}
		// 最终的销售收货的客户+品牌得到原客户
		DynamicObject finalCustomer = bill.getDynamicObject("customer");
		bill.set("yd_checkboxsf01", false);  // EAS是否保存
		bill.set("yd_checkboxsf02", false);  // EAS是否提交
		bill.set("yd_checkboxsf", false);  // EAS是否审核
		bill.set("yd_textareafield02", "");  // EAS保存失败原因
		bill.set("yd_sftj", "");  // EAS提交失败原因
		bill.set("yd_textareafield", "");  // EAS审核失败原因
		bill.set("yd_internalsettle", false);  // 是否已结算
		bill.set("yd_firstinternalbill", false);  // 是否首笔结算单
		bill.set("yd_settleerror", "");  // 结算失败原因
		// update by hst -2022/11/14 重置结算时还原结算组织
		bill.set("yd_settleorg",bill.get("yd_orisettleorg"));
		bill.set("yd_orisettleorg",null);
		// update by hst -2023/03/22 重置结算时还原库存组织
		bill.set("org",bill.get("yd_oriorg"));
		bill.set("yd_oriorg",null);
		// 对源销售出库单仓库进行重置
		DynamicObjectCollection oriSaleEntryCol = bill.getDynamicObjectCollection("billentry");
		for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
			oriSaleEntry.set("warehouse", oriSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
		}
		// 清空内部结算单据分录
		DynamicObjectCollection internalCol = bill.getDynamicObjectCollection("yd_internalentity");
		internalCol.clear();
		// 保存
		SaveServiceHelper.save(new DynamicObject[] {bill});
	}
	
	/**
	 * 发起PCP虚体组织销售结算
	 * <AUTHOR>
	 * @date 2023/02/21
	 */
	public void pcpVirtualSettle() {
		String lock_opKey = "PCPVirtualSettle"; // 解锁/锁定 的操作标识
		// 获取当前的日期时间
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");
		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "4"));  // 从PCP过来
		filter.and(QFilter.of("yd_isvirtual =?", true));  // 虚体组织结算
		UIUtils.checkSelected(this.getView(), "请选择数据！");
		// 获取已经选择的ID集合
		List<Object> idList = UIUtils.getSelectIds(this.getView());
		filter.and(new QFilter("id", QCP.in, idList));
		String tip = "所选单据都不满足PCP结算条件（单据为暂存、来源系统为PCP、未完成内部结算流程、虚体组织结算），无法生成内部结算流程单据！";
		PCPServiceHelper.createPCPVirtualSettleBill(BillTypeHelper.BILLTYPE_SALEOUTBILL, lock_opKey, filter, tip);
	}

	/**
	 * 重置PCP虚体组织结算
	 * <AUTHOR>
	 * @date 2023/02/21
	 */
	public void resetVirtualPCPSettle() {
		// 获取勾选的数据的id
		UIUtils.checkSelected(this.getView(), "请选择数据！");
		List<Object> idList = UIUtils.getSelectIds(this.getView());
		//根据id获取单据
		String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;
		DynamicObject template = BusinessDataServiceHelper.newDynamicObject(billType_saleOutBill);
		DynamicObject[] bills = BusinessDataServiceHelper.load(idList.toArray(),template.getDynamicObjectType());
		StringBuffer errorStr = new StringBuffer();
		for (DynamicObject bill : bills) {
			//校验单据是否符合重置结算条件
			PCPServiceHelper.checkPCPVirtualResetSettle(bill);
			// 如果结算链路中存在EAS已审核的单据就不允许重置结算
			checkGFToEas(bill);
			String srcBillEntity = billType_saleOutBill;
			String tempId = bill==null?"":bill.getPkValue().toString();
			//清除下游单据
//			try {
				PCPServiceHelper.clearVirtualDownStreamBill(tempId, srcBillEntity);
//			} catch (Exception e) {
//				errorStr.append(e.getMessage());
//				continue;
//			}
			// 如果没有报错则重置反审核源销售出库单并重置客户、仓库、推送EAS标识、报错信息、是否结算
			// 反审再删除
			OperationResult unauditResult = OperationServiceHelper.executeOperate("unaudit", "im_saloutbill",
					new Object[]{bill.getPkValue()}, OperateOption.create());
			if (!unauditResult.isSuccess()) {
				// 错误摘要
				throw new KDException("单据"+bill.getString("billno") + "反审核失败，请到销售出库单手动反审核，查看反审核失败原因：" + unauditResult.getMessage());
			}
			// 最终的销售收货的客户+品牌得到原客户
			DynamicObject finalCustomer = bill.getDynamicObject("customer");
			bill.set("yd_checkboxsf01", false);  // EAS是否保存
			bill.set("yd_checkboxsf02", false);  // EAS是否提交
			bill.set("yd_checkboxsf", false);  // EAS是否审核
			bill.set("yd_textareafield02", "");  // EAS保存失败原因
			bill.set("yd_sftj", "");  // EAS提交失败原因
			bill.set("yd_textareafield", "");  // EAS审核失败原因
			bill.set("yd_internalsettle", false);  // 是否已结算
			bill.set("yd_firstinternalbill", false);  // 是否首笔结算单
			bill.set("yd_settleerror", "");  // 结算失败原因
			bill.set("yd_orisettleorg",null);
			// update by hst 2023/04/27 清除首单标识
			bill.set("yd_firstinternalbill",false);
			// 对源销售出库单仓库进行重置
			DynamicObjectCollection oriSaleEntryCol = bill.getDynamicObjectCollection("billentry");
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
				oriSaleEntry.set("warehouse", oriSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
			}
			// 清空内部结算单据分录
			DynamicObjectCollection internalCol = bill.getDynamicObjectCollection("yd_internalentity");
			internalCol.clear();
			// 保存
			SaveServiceHelper.save(new DynamicObject[] {bill});
		}
		if (errorStr.length() > 0) {
			this.getView().showErrorNotification("存在重置失败的单据，原因如下：" + errorStr.toString());
		}
	}
	
	/**
	 * 发起PCP线下一盘货销售结算
	 * <AUTHOR>
	 * @date 2023/03/22
	 */
	public void pcpOfflineSettle() {
		String lock_opKey = "PCPSettle"; // 解锁/锁定 的操作标识
		// 获取当前的日期时间
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");
		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "4"));  // 从PCP过来
		filter.and(QFilter.of("yd_isoffline =?", true));  // 从一盘货结算
		UIUtils.checkSelected(this.getView(), "请选择数据！");
		// 获取已经选择的ID集合
		List<Object> idList = UIUtils.getSelectIds(this.getView());
		filter.and(new QFilter("id", QCP.in, idList));
		String tip = "所选单据都不满足PCP结算条件（单据为暂存、来源系统为PCP、未完成内部结算流程、线下一盘货结算），无法生成内部结算流程单据！";
		PCPServiceHelper.createPCPSettleBill(BillTypeHelper.BILLTYPE_SALEOUTBILL, lock_opKey, filter, tip);
	}

	/**
	 * 重置PCP线下一盘货结算
	 * <AUTHOR>
	 * @date 2023/03/22
	 */
	public void resetPCPOfflineSettle() {
		// 获取勾选的数据的id
		UIUtils.checkSelected(this.getView(), "请选择数据！");
		List<Object> idList = UIUtils.getSelectIds(this.getView());
		//根据id获取单据
		String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;
		DynamicObject template = BusinessDataServiceHelper.newDynamicObject(billType_saleOutBill);
		DynamicObject[] bills = BusinessDataServiceHelper.load(idList.toArray(),template.getDynamicObjectType());

		for (DynamicObject bill : bills) {
			//校验单据是否符合重置结算条件
			PCPServiceHelper.checkPCPOfflineResetSettle(bill);
			this.resetPCPSettle(bill);
		}
	}
	
	/**
	 * 发起成品仓一盘货结算
	 * @author: hst
	 * @createDate: 2023/04/17
	 */
	public void warehouseSettle() {

		String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;
		String lock_opKey = "warehouseSettle"; // 解锁/锁定 的操作标识

		// 获取当前的日期时间
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");

		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "1"));  // 从E3过来
		filter.and(QFilter.of("yd_sourcebilltype =?", "2"));  // 来源单据类型为批发通知单

		UIUtils.checkSelected(this.getView(), "请选择数据！");
		// 获取已经选择的ID集合
		List<Object> idList = UIUtils.getSelectIds(this.getView());
		filter.and(new QFilter("id", QCP.in, idList));  // 测试单号

		DataSet saleBillSet = null;
		try {
			saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", billType_saleOutBill, "id,billno", filter.toArray(), null);
			if (!saleBillSet.hasNext()) {
				throw new KDBizException("所选单据都不满足成品仓一盘货结算条件（单据为暂存、来源系统为E3、来源单据类型为批发通知单的销售出库单），无法生成成品仓一盘货结算流程单据！");
			}

			Set<String> doneBillSet = new HashSet<>();
			for (Row row : saleBillSet) {
				// 源末级销售出库单ID
				String saleoutBillId = row.getString("id");
				System.out.println("销售出库单ID：" + saleoutBillId);
				if (doneBillSet.contains(saleoutBillId)) {
					continue;
				}

				// 如果有互斥锁则提示不允许操作
				Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(saleoutBillId, billType_saleOutBill, lock_opKey);
				if (lockInfo != null) {
					throw new KDBizException("系统定时的调度计划在执行中，请勿重复操作发起内部结算！");
				}

				// 对进行结算的出库单进行加锁
				MutexServiceHelper.request(saleoutBillId, billType_saleOutBill, lock_opKey);

				try {
					// 源末级销售出库单
					DynamicObject saleOutInfo = BizHelper.getDynamicObjectById(billType_saleOutBill, saleoutBillId);

					ApiResult result = new ApiResult();
					// 成品仓一盘货结算只有正向流程
					DynamicObject biztype = saleOutInfo.getDynamicObject("biztype");
					if ("210".equals(biztype.getString("number"))) {
						// 正向流程
						result = SaleOutBillMserviceHelper.createDirectForwardBill(saleoutBillId);
					}
					if (!result.getSuccess()) {
						throw new KDBizException(result.getMessage());
					}

					// 使用关联关系将生成的下游单据保存到源销售出库单上
					InternalSettleServiceHelper.saveBotpBills(saleoutBillId);

					doneBillSet.add(saleoutBillId);
				}finally {
					// 释放锁
					MutexServiceHelper.release(saleoutBillId, billType_saleOutBill, lock_opKey);
				}
			}
		}finally {
			// 释放DataSet
			ORMUtils.close(saleBillSet);
		}
	}

	/**
	 * 重置成品仓一盘货结算
	 * <AUTHOR>
	 * @createDate 2022/04/27
	 */
	public void resetWarehouseSettle() {

		String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;

		// 获取勾选的数据
		UIUtils.checkSelected(this.getView(), "请选择数据！");
		List<Object> idList = UIUtils.getSelectIds(this.getView());

		ConvertDataService dataService = new ConvertDataService();
		for (Object billId : idList) {
			DynamicObject saleOutInfo = BizHelper.getDynamicObjectById(billType_saleOutBill, billId);
			// 不是审核状态不允许
			String srcbillstatus = saleOutInfo.getString("billstatus");
			Boolean isFirstBill = saleOutInfo.getBoolean("yd_internalsettle");
			Boolean isInternalToEas = saleOutInfo.getBoolean("yd_internaltoeas");  // 是否已成功传递EAS，已成功的需要手工回退
			String billNo = saleOutInfo.getString("billno");
			String billType = saleOutInfo.getString("yd_sourcebilltype");  // 来源单据类型
			if (!"2".equals(billType)) {
				throw new KDBizException("源销售出库单"+billNo+"不是批发通知单，不能使用该重置功能！");
			}
			if (isInternalToEas) {
				throw new KDBizException("源销售出库单"+billNo+"涉及的内部结算单据已全部传递EAS，需要在EAS与中台手工回退！");
			}
			if (!isFirstBill) {
				throw new KDBizException("源销售出库单"+billNo+"不是末级组织结算单，无法执行清理操作！");
			}
			if (!"C".equals(srcbillstatus)) {
				throw new KDBizException("源销售出库单"+billNo+"未发起过内部结算，无法执行清理操作！");
			}
			// 如果结算链路中存在EAS已审核的单据就不允许重置结算,yzl,20230206
			checkGFToEas(saleOutInfo);

//			String srcBillEntity = saleoutBillObj.getDataEntityType().getExtendName();
			String srcBillEntity = billType_saleOutBill;
			String tempId = billId==null?"":billId.toString();
			LinkedList<String> billRelList = new LinkedList<String>();
			while(StringUtils.isNotEmpty(tempId)) {
				// 遍历单据，获取下游单据数据
//				Map<String, HashSet<Long>> targetBill = BFTrackerServiceHelper.findTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
				Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
				if (dirTargetBill.size() == 1) {
					Long tempLongId = dirTargetBill.keySet().iterator().next();
					List<BFRow> targetRow = dirTargetBill.get(tempLongId);
					BFRowId targetBillRow = targetRow.get(0).getId();
					srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
					tempId = targetBillRow.getBillId().toString();
					billRelList.add(srcBillEntity+"&"+tempId);
				} else {
					tempId = "";
					srcBillEntity = "";
				}
				System.out.println(srcBillEntity+tempId);
			}

			// 从下到上删单
			for(int i=billRelList.size()-1;i>=0;i--) {
				String[] keys = billRelList.get(i).split("&");
				String entity = keys[0];
				String tempBillId = keys[1];
				DynamicObject billObj = BusinessDataServiceHelper.loadSingle(tempBillId, entity);
				String billstatus = billObj.getString("billstatus");
				String billName = "";
				if ("im_purinbill".equals(entity)) {
					billName = "采购入库单";
				}
				if ("im_saloutbill".equals(entity)) {
					billName = "销售出库单";
				}
				if ("B".equals(billstatus)) {
					// 撤销再删除
					OperationResult unsubmitResult = OperationServiceHelper.executeOperate("unsubmit", entity, new Object[]{tempBillId}, OperateOption.create());
					if (!unsubmitResult.isSuccess()) {
						// 错误摘要
						throw new KDException("单据"+billObj.getString("billno") + "撤销提交失败，请到"+billName+"手动撤销提交，查看撤销提交失败原因：" + unsubmitResult.getMessage());
					}
				} else if ("C".equals(billstatus)) {
					// 反审再删除
					OperationResult unauditResult = OperationServiceHelper.executeOperate("unaudit", entity, new Object[]{tempBillId}, OperateOption.create());
					if (!unauditResult.isSuccess()) {
						// 错误摘要
						throw new KDException("单据"+billObj.getString("billno") + "反审核失败，请到"+billName+"手动反审核，查看反审核失败原因：" + unauditResult.getMessage());
					}
				}

				// 删除单据
				OperationResult deleteResult = OperationServiceHelper.executeOperate("delete", entity, new Object[]{tempBillId}, OperateOption.create());
				if (!deleteResult.isSuccess()) {
					// 错误摘要
					throw new KDException("单据"+billObj.getString("billno") + "删除失败，请到"+billName+"手动删除，查看删除失败原因：" + deleteResult.getMessage());
				}
			}

			// 如果没有报错则重置反审核源销售出库单并重置客户、仓库、推送EAS标识、报错信息、是否结算
			// 反审再删除
			OperationResult unauditResult = OperationServiceHelper.executeOperate("unaudit", "im_saloutbill", new Object[]{billId}, OperateOption.create());
			if (!unauditResult.isSuccess()) {
				// 错误摘要
				throw new KDException("单据"+saleOutInfo.getString("billno") + "反审核失败，请到销售出库单手动反审核，查看反审核失败原因：" + unauditResult.getMessage());
			}

			// 最终的销售收货的客户+品牌得到原客户
			DynamicObject finalCustomer = saleOutInfo.getDynamicObject("customer");

			saleOutInfo.set("yd_checkboxsf01", false);  // EAS是否保存
			saleOutInfo.set("yd_checkboxsf02", false);  // EAS是否提交
			saleOutInfo.set("yd_checkboxsf", false);  // EAS是否审核
			saleOutInfo.set("yd_textareafield02", "");  // EAS保存失败原因
			saleOutInfo.set("yd_sftj", "");  // EAS提交失败原因
			saleOutInfo.set("yd_textareafield", "");  // EAS审核失败原因
			saleOutInfo.set("yd_internalsettle", false);  // 是否已结算
			saleOutInfo.set("yd_firstinternalbill", false);  // 是否首笔结算单
			saleOutInfo.set("yd_settleerror", "");  // 结算失败原因
			saleOutInfo.set("org", saleOutInfo.get("yd_oriorg"));  // 库存组织
			saleOutInfo.set("bizorg", saleOutInfo.get("yd_oriorg"));  // 销售组织
			saleOutInfo.set("bizdept", saleOutInfo.get("yd_oriorg"));  // 销售部门
			saleOutInfo.set("yd_oriorg", null);  // 原库存组织

			// 对源销售出库单进行单价重置
			DynamicObjectCollection oriSaleEntryCol = saleOutInfo.getDynamicObjectCollection("billentry");
			for (DynamicObject oriSaleEntry : oriSaleEntryCol) {
				oriSaleEntry.set("warehouse", oriSaleEntry.getDynamicObject("yd_e3oriwarehouse"));
			}

			// 清空内部结算单据分录
			DynamicObjectCollection internalCol = saleOutInfo.getDynamicObjectCollection("yd_internalentity");
			internalCol.clear();

			SaveServiceHelper.save(new DynamicObject[] {saleOutInfo});// 保存
			System.out.println("结束清理内部结算流程");
		}
	}
}
