package kd.bos.tcbj.im.outbill.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDBizException;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.outbill.helper.SettleServiceHelper;
import kd.bos.tcbj.im.util.DynamicObjectUtil;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.im.util.UIUtils;

import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.im.outbill.plugin.SettleServiceBillList
 * @className SettleServiceBillList
 * @author: hst
 * @createDate: 2023/09/11
 * @description: 结算共性列表类
 * @version: v1.0
 */
public class SettleServiceBillList extends AbstractListPlugin {

    @Override
    public void itemClick(ItemClickEvent evt) {
        String itemKey=evt.getItemKey();
        this.billSettle(itemKey);
    }

    /**
     * 获取需结算数据并执行一盘货结算流程
     * @param key
     * @author: hst
     * @createDate: 2023/05/12
     */
    public void billSettle(String key) {
        // 查询是否有相应的配置信息
        DynamicObject config = QueryServiceHelper.queryOne("yd_settleconfig",
                String.join(",", DynamicObjectUtil.getAllField("yd_settleconfig")),
                new QFilter[]{QFilter.of("yd_type = ? and status = ?",key,"C")});
        if (Objects.nonNull(config)) {
            String filterStr = config.getString("yd_filter");
            String tip = config.getString("yd_prompt");
            QFilter filter = null;
            if (StringUtils.isNotBlank(filterStr)) {
                try {
                    filter = QFilter.of(filterStr);
                } catch (Exception e) {
                    throw new KDBizException("过滤条件转换异常");
                }
                // 获取已经选择的ID集合
                List<Object> idList = UIUtils.getSelectIds(this.getView());
                filter.and(new QFilter("id", QCP.in, idList));
                // 执行一盘货结算流程
                SettleServiceHelper.createSettleBill(key, BillTypeHelper.BILLTYPE_SALEOUTBILL,config,filter,tip,true);
            }
            this.getView().showMessage("执行完成，请查看处理结果！");
            this.getView().invokeOperation("refresh");
        }
    }
}
