package kd.bos.tcbj.im.outbill.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.tcbj.im.plugin.KdepBillPlugin;
import org.apache.commons.lang3.StringUtils;

/**
 * 库存调拨结算关系编辑界面
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-7-28
 */
    public class TransSettleRalaEdit extends KdepBillPlugin {

    /**
     * 操作前插件处理
     * <AUTHOR>
     * @date 2022-7-28
     */
    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs evt) {
        String opKey = this.getOpKey(evt);
        if (StringUtils.equals("save", opKey) || StringUtils.equals("submit", opKey)) {
            beforeSave(evt);
        }
    }

    /**
     * 保存前校验
     * <AUTHOR>
     * @date 2022-7-28
     */
    private void beforeSave(BeforeDoOperationEventArgs evt) {
        int entryCount = this.getModel().getEntryRowCount("yd_entryentity");
        if (entryCount >0 ) {
            DynamicObject outOrgInfo = (DynamicObject) this.getModel().getValue("yd_outorg"); // 调出组织
            DynamicObject inOrgInfo = (DynamicObject) this.getModel().getValue("yd_inorg"); // 调出组织
            DynamicObject outEntryOrgInfo = (DynamicObject) this.getModel().getValue("yd_org", 0);
            DynamicObject inEntryOrgInfo = (DynamicObject) this.getModel().getValue("yd_org", entryCount-1);
            if (!outOrgInfo.getPkValue().equals(outEntryOrgInfo.getPkValue())) {
                this.getView().showErrorNotification("表头调出组织与分录一级组织不一致！");
                evt.setCancel(true);
            }else if (!inOrgInfo.getPkValue().equals(inEntryOrgInfo.getPkValue())) {
                this.getView().showErrorNotification("表头调入组织与分录最后一级组织不一致！");
                evt.setCancel(true);
            }
        }
    }
}
