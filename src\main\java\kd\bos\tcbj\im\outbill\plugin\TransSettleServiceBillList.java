package kd.bos.tcbj.im.outbill.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDBizException;
import kd.bos.form.ConfirmCallBackListener;
import kd.bos.form.MessageBoxOptions;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.outbill.helper.TransSettleServiceHelper;
import kd.bos.tcbj.im.util.DynamicObjectUtil;
import kd.bos.tcbj.im.util.SettleUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.im.util.UIUtils;

import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.im.outbill.plugin.TransSettleServiceBillList
 * @className TransSettleServiceBillList
 * @author: hst
 * @createDate: 2024/05/27
 * @description: 调拨结算共性列表类
 * @version: v1.0
 */
public class TransSettleServiceBillList extends AbstractListPlugin {

    /** 重置结算按钮标识 **/
    private final static String RESET_BTN = "yd_trans_reset";
    /** 重置同步EAS状态按钮标识 **/
    private final static String RESTATUS_BTN = "yd_trans_restatus";
    /** 重置同步EAS状态确认弹窗标识 **/
    private final static String RESTATUS_COM = "restatus_comfirm";

    @Override
    public void itemClick(ItemClickEvent evt) {
        String itemKey = evt.getItemKey();
        switch (itemKey) {
            case RESET_BTN : {
                this.resetTransSettle();
                break;
            }
            case RESTATUS_BTN : {
                this.getView().showConfirm("请确认是否清除选中单据及下游单据同步EAS状态标识？",
                        MessageBoxOptions.YesNo, new ConfirmCallBackListener(RESTATUS_COM, this));
                break;
            }
            default : {
                this.billSettle(itemKey);
                break;
            }
        }
    }

    /**
     * 确认弹窗点击确认后回调事件
     * @author: hst
     * @createDate: 2024/06/04
     * @param messageBoxClosedEvent
     */
    @Override
    public void confirmCallBack(MessageBoxClosedEvent messageBoxClosedEvent) {
        super.confirmCallBack(messageBoxClosedEvent);
        if (RESTATUS_COM.equals(messageBoxClosedEvent.getCallBackId())
                && messageBoxClosedEvent.getResult().getValue() == 6) {
            // 重置结算单据同步EAS状态
            this.resetTransSettleStatus();
        }
    }

    /**
     * 获取需结算数据并执行一盘货结算流程
     * @param key
     * @author: hst
     * @createDate: 2023/05/12
     */
    public void billSettle(String key) {
        // 查询是否有相应的配置信息
        DynamicObject config = QueryServiceHelper.queryOne("yd_settleconfig",
                String.join(",",DynamicObjectUtil.getAllField("yd_settleconfig")),
                new QFilter[]{QFilter.of("yd_type = ? and status = ?",key,"C")});
        if (Objects.nonNull(config)) {
            String filterStr = config.getString("yd_filter");
            String tip = config.getString("yd_prompt");
            QFilter filter = null;
            if (StringUtils.isNotBlank(filterStr)) {
                try {
                    filter = QFilter.of(filterStr);
                } catch (Exception e) {
                    throw new KDBizException("过滤条件转换异常");
                }
                // 获取已经选择的ID集合
                List<Object> idList = UIUtils.getSelectIds(this.getView());
                filter.and(new QFilter("id", QCP.in, idList));
                // 执行结算流程
                new TransSettleServiceHelper().createSettleBill(key,config,filter,tip,true);
            }
            this.getView().showMessage("执行完成，请查看处理结果！");
            this.getView().invokeOperation("refresh");
        }
    }

    /**
     * 重置结算单据
     * @author: hst
     * @createDate: 2024/06/02
     */
    private void resetTransSettle () {
        SettleUtils.resetTransSettle(this.getView());
        this.getView().invokeOperation("refresh");
    }

    /**
     * 重置结算单据同步EAS状态
     * @author: hst
     * @createDate: 2024/06/02
     */
    private void resetTransSettleStatus () {
        SettleUtils.resetTransSettleStatus(this.getView(), BillTypeHelper.BILLTYPE_MIDTRANSBILL);
        this.getView().invokeOperation("refresh");
    }
}
