package kd.bos.tcbj.im.outbill.plugin;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import json.JSONObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.exception.KDBizException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.list.IListView;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.tcbj.im.outbill.helper.WholeSaleReturnBillMserviceHelper;

/**
 * 批发退货单列表插件类
* @auditor yanzuwei
* @date 2022年1月10日
* 
*/
public class WholeSaleReturnBillList extends AbstractListPlugin {
	
	private final static SimpleDateFormat DATE_SDF =new SimpleDateFormat("yyyy-MM-dd");
	
	/** 获取E3批发退货单按钮名称 */
	private final static String GETWHOLESALERETURNBILL_BOTTON = "yd_getWholeSaleReturnBill";
	/** 下推红字红字销售出库单按钮名称 */
	private final static String PUSHTOSALEOUTBILL_BOTTON = "yd_pushToSaleoutbill";
	
	/** 参数选择页面关闭事件ID */
	private final static String CALLBACKID_SELECTPARAMS = "closeCallBack_selectParams";
	
	/** 参数选择页面标志 */
	private final static String SELECTPARAMSFORM_ID = "yd_wholesalereturnparamse";
	
	@Override
	public void itemClick(ItemClickEvent evt) {
		String itemKey=evt.getItemKey();
		// 获取E3批发退货单功能
		if(StringUtil.equalsIgnoreCase(GETWHOLESALERETURNBILL_BOTTON, itemKey)) {
			getWholeSaleReturnBill();
			this.getView().invokeOperation("refresh");
		}
		// 关联生成红字销售出库单功能
		if(StringUtil.equalsIgnoreCase(PUSHTOSALEOUTBILL_BOTTON, itemKey)) {
			pushToSaleoutBill();
			this.getView().invokeOperation("refresh");
		}
		// 测试保存关联关系
		if(StringUtil.equalsIgnoreCase("yd_testfunction", itemKey)) {
//			DynamicObject loadSingle = BusinessDataServiceHelper.loadSingle("1330453863030851584", "yd_wholesalereturnbill");
//			DynamicObject loadSingle2 = BusinessDataServiceHelper.loadSingle("1331433455547844608", "im_saloutbill");
//			BotpUtils.createRelation("yd_wholesalereturnbill", 1330453863030851584L, "im_saloutbill", 1331920882049744896L);
			this.getView().invokeOperation("refresh");
		}
		
	}

	/**
	 * 下推红字销售出库单功能
	 */
	private void pushToSaleoutBill() {
		// 获取勾选的数据
		ListSelectedRowCollection selectedData = ((IListView)this.getView()).getSelectedRows(); 
		Set<String> oriIdSet = new HashSet<String>();
		for (ListSelectedRow row : selectedData) {
			Object pk = row.getPrimaryKeyValue();
			oriIdSet.add(pk.toString());
		}
		
		if(oriIdSet.size() == 0) {
			throw new KDBizException("请选择数据！");
		}
		
		ApiResult result = WholeSaleReturnBillMserviceHelper.pushToSaleoutBill(oriIdSet);
		if (result.getSuccess()) {
			this.getView().showMessage("执行完成，详情请查看单据数据！");
		} else {
			this.getView().showErrorNotification(result.getMessage());
		}
	}

	/**
	 * 调用接口获取E3批发退货单
	 */
	private void getWholeSaleReturnBill() {
		// 弹出选择查询参数的动态表单页面
		FormShowParameter showParameter = new FormShowParameter();
		showParameter.setFormId(SELECTPARAMSFORM_ID);
		showParameter.setCloseCallBack(new CloseCallBack(this, CALLBACKID_SELECTPARAMS));
		showParameter.getOpenStyle().setShowType(ShowType.Modal);
		this.getView().showForm(showParameter);
	}
	
	/**
	 * 描述：子界面关闭时，触发父界面的closedCallBack事件
	 * 1.选择查询日期和查询客户编号后获取填写的数据，然后执行调用E3接口功能
	 * 
	 * @createDate  : 2022-01-10
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param evt 批发退货单列表界面的closedCallBack事件
	 */
	@Override
	public void closedCallBack(ClosedCallBackEvent evt) {
		if (StringUtil.equals(CALLBACKID_SELECTPARAMS, evt.getActionId())){  // 关闭参数选择界面后返回
			if (evt.getReturnData() != null) {
				Map<String, Object> searchData = (Map<String, Object>) evt.getReturnData();
				Date searchDate = (Date) searchData.get("searchDate");
				String customerNo = searchData.get("customerNo").toString();
				
				JSONObject postJson=new JSONObject();
				postJson.put("rq_start", DATE_SDF.format(searchDate)+" 00:00:00");
				postJson.put("rq_end", DATE_SDF.format(searchDate)+" 23:59:59");
				if (!StringUtil.isEmptyString(customerNo)) {
					postJson.put("kh_code", customerNo);
				}
				// 调用统一方法进行获取
				ApiResult apiResult = WholeSaleReturnBillMserviceHelper.getWholeSaleReturnBill(postJson);
				if (!apiResult.getSuccess()) {
					this.getView().showMessage(apiResult.getMessage());
				} else {
					this.getView().showMessage("数据获取完成，请查看列表数据详情！");
				}
				
			}
		}
		this.getView().invokeOperation("refresh");
	}
}
