package kd.bos.tcbj.im.outbill.plugin;

import java.util.Date;
import java.util.Map;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;

public class WholeSaleReturnParamsPlugin extends AbstractFormPlugin {
	
	/** 确认按钮操作标识 */
	private final static String OK="ok";
	/** 查询日期字段标识 */
	private final static String FIELD_SEARCHDATE = "yd_searchdate";
	/** 查询客户编码字段标识 */
	private final static String FIELD_CUSTOMERNO = "yd_customerno";
	
	/**
	 * 
	 * 描述：操作后事件
	 * 1.点击确认按钮后,获取所选日期和查询单号（会为空）
	 * 
	 * @createDate  : 2021-01-10
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 操作后事件
	 */
	@Override
	public void afterDoOperation(AfterDoOperationEventArgs e) {
		super.afterDoOperation(e);
		String opKey=e.getOperateKey();
		if(StringUtil.equals(OK,opKey)) {
			Date searchDate = (Date) this.getModel().getValue(FIELD_SEARCHDATE);
			Object customerNo=this.getModel().getValue(FIELD_CUSTOMERNO);
			Map<String, Object> paramMap = getView().getFormShowParameter().getCustomParams();
			
			paramMap.put("searchDate",searchDate);
			paramMap.put("customerNo",customerNo);
			getView().returnDataToParent(paramMap);
		}
	}
}
