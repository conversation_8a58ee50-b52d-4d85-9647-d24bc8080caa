package kd.bos.tcbj.im.outbill.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基础同步任务处理
 * <AUTHOR>
 * @Description:
 * @date 2021-12-27
 */
public abstract class BaseTask extends AbstractTask {

    private List<Object> list = new ArrayList<>();

    /**
     * 同步任务处理
     * <AUTHOR>
     * @date 2021-12-27
     */
    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        if (map == null) map = new HashMap<>();
        beforeExecute(requestContext, map, list);
        afterExecute(requestContext, map, list);
    }

    /**
     * 请求数据前数据准备处理
     * @param list 待处理的集合
     * <AUTHOR>
     * @date 2021-12-27
     */
    protected abstract void beforeExecute(RequestContext requestContext, Map<String, Object> map, List<Object> list);

    /**
     * 执行后处理的数据
     * @param list 待处理的集合
     * @return
     * <AUTHOR>
     * @date 2021-12-27
     */
    protected abstract void afterExecute(RequestContext requestContext, Map<String, Object> map, List<Object> list);
}
