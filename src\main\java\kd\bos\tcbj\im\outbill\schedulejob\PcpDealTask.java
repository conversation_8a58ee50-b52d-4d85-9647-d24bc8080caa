package kd.bos.tcbj.im.outbill.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDBizException;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.tcbj.im.common.enums.PcpDealTaskTypeEnum;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.im.util.UIUtils;

import java.util.*;

/**
 * PCP处理调度任务
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-6-22
 */
public class PcpDealTask extends BaseTask {

    /**
     * 执行前处理
     * @param list 待处理的集合
     */
    @Override
    protected void beforeExecute(RequestContext requestContext, Map<String, Object> map, List<Object> list) {
        String dealType = (String) map.get("type");
        if (StringUtils.isBlank(dealType)) throw new KDBizException("调度任务的参数[type]不能为空！");
    }

    /**
     * 执行后处理
     * @param list 待处理的集合
     */
    @Override
    protected void afterExecute(RequestContext requestContext, Map<String, Object> map, List<Object> list) {
        String dealType = (String) map.get("type");
        // 根据不同的处理方式处理
        // 获取PCP销售出库单（当天内的数据）
        if (StringUtils.equals(dealType, PcpDealTaskTypeEnum.GETPCPSALEOUTBILL.getCode())) {
            actionGetPcpSaleOutBill();
        }
        // 获取PCP销售出库单（一周内未获取的数据）
        else if (StringUtils.equals(dealType, PcpDealTaskTypeEnum.GETPCPSALEOUTBILLBYWEEK.getCode())) {
            actionGetPcpSaleOutBillByWeek();
        }
        // 下推转换生成销售出库单（当天内的数据）
        else if (StringUtils.equals(dealType, PcpDealTaskTypeEnum.TRANSSALEOUTBILL.getCode())) {
            actionTransSaleOutBill();
        }
        // 下推转换生成销售出库单（还未转换生成的数据）
        else if (StringUtils.equals(dealType, PcpDealTaskTypeEnum.TRANSSALEOUTBILLBYNOTRANS.getCode())) {
            actionTransSaleOutBillByNoPush();
        }
        // 销售结果数据回传
        else if (StringUtils.equals(dealType, PcpDealTaskTypeEnum.PUSHSALERESULTTOPCP.getCode())) {
            actionPushSaleResultToPcp();
        }
        // 获取PCP调拨单
        else if (StringUtils.equals(dealType, PcpDealTaskTypeEnum.GETPCPTRANSBILL.getCode())) {
            actionGetPcpTransBill();
        }
        // PCP调拨单结算
        else if (StringUtils.equals(dealType, PcpDealTaskTypeEnum.PCPTRANSSETTLE.getCode())) {
            actionPcpTransSettle();
        }
    }

    /**
     * 从PCP接口获取调拨单中间表
     * <AUTHOR>
     * @date 2022-7-28
     */
    @Deprecated
    private void actionGetPcpTransBill() {
        // TODO 待处理
    }

    /**
     * 根据调拨单中间表，根据调拨结算关系生成对应的出入库单
     * <AUTHOR>
     * @date 2022-7-28
     */
    private void actionPcpTransSettle() {
        PCPServiceHelper.doTransSettle(null, null, true);
    }

    /**
     * 获取PCP销售出库单
     * <AUTHOR>
     * @date 2022-6-14
     */
    private void actionGetPcpSaleOutBill() {
        Date beginDate = DateTimeUtils.format2(new Date());
        Date endDate = DateTimeUtils.addDate(beginDate, 1);
        // 2、根据日期调用PCP的api接口获取业务数据
        // 正向销售出库单
        PCPServiceHelper.getPcpSaleOutBill("SALE", beginDate, endDate, null);
        // 退货销售出库单
        PCPServiceHelper.getPcpSaleOutBill("SALE_RED", beginDate, endDate, null);
    }

    /**
     * 获取PCP销售出库单（处理一周内未获取的数据）
     * <AUTHOR>
     * @date 2022-6-14
     */
    private void actionGetPcpSaleOutBillByWeek() {
        // 获取数据为一周前~昨天的数据
        Date currentDate = DateTimeUtils.format2(new Date());
        Date beginDate = DateTimeUtils.addDate(currentDate, -7);
        // 2、根据日期调用PCP的api接口获取业务数据
        // 正向销售出库单
        PCPServiceHelper.getPcpSaleOutBill("SALE", beginDate, currentDate, null);
        // 退货销售出库单
        PCPServiceHelper.getPcpSaleOutBill("SALE_RED", beginDate, currentDate, null);
    }

    /**
     * 将PCP销售出库单结果回传给PCP系统
     * <AUTHOR>
     * @date 2022-8-15
     */
    private void actionPushSaleResultToPcp() {
        PCPServiceHelper.pushSaleResultToPcp();
    }

    /**
     * 获取PCP销售出库单
     *
     * <AUTHOR>
     * @date 2022-6-20
     */
    private void actionTransSaleOutBill() {
        // 默认日期为当天
        Date currentDate = new Date();
        PCPServiceHelper.transSaleOutBill(null, currentDate, true);
    }

    /**
     * 获取PCP销售出库单
     *
     * <AUTHOR>
     * @date 2022-6-20
     */
    private void actionTransSaleOutBillByNoPush() {
        // 查找所有没有下推的数据
        PCPServiceHelper.transSaleOutBill(null, null, true);
    }
}
