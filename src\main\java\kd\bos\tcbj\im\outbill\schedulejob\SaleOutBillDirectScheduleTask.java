package kd.bos.tcbj.im.outbill.schedulejob;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.outbill.helper.SaleOutBillMserviceHelper;
import kd.epm.eb.ebBusiness.serviceHelper.MutexServiceHelper;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 描述：销售出库直营店内部结算流程处理类
 * @createDate  : 2022-06-07
 * @createAuthor: lzp
 * @updateDate  : 
 * @updateAuthor: 
 */
public class SaleOutBillDirectScheduleTask extends AbstractTask {
	
	protected int amountPrecision = 2;
	protected int curAmountPrecision = 2;
	protected int pricePrecision = 10;
	protected int curPricePrecision = 10;
	protected BigDecimal ONEHUNDRED = new BigDecimal(100);

	/**
	 * 描述：每30分钟执行一次，获取当天需要走直营店内部结算的销售出库单
	 * 保存状态的，未生成内部结算的yd_internalsettle、来源于E3系统的yd_tbly，按直营店结算的yd_isdirectsettl，
	 * 
	 * @createDate  : 2021-12-09
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	@Override
	public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {

		String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;
		String lock_opKey = "directSettle"; // 解锁/锁定 的操作标识

		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");
		
		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
//		filter.and(QFilter.of("billentry.yd_ph!=?", ""));  // 批次号不为空
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "1"));  // 从E3过来
		filter.and(QFilter.of("yd_isdirectsettle =?", "1"));  // 是否直营结算为“是”
		
		// 后台事务用于测试单笔单据时使用
		if (params!=null&&params.get("billno")!=null) {
			String billNo = params.get("billno").toString();
			filter.and(QFilter.of("billno=?", billNo));  // 测试单号
		}
		
		// 检查是否有满足条件的单据
		DataSet saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", billType_saleOutBill, "id,billno",
				new QFilter[] { filter }, null);
		if (!saleBillSet.hasNext()) {
			throw new KDBizException("目前暂无单据满足内部结算条件：单据为暂存、来源系统为E3、未完成内部结算流程、已设置内部结算关系的销售出库单！");
		}
		
		// 本次处理的销售出库单
		Set<String> doneBillSet = new HashSet<>();
		for (Row row : saleBillSet) {
			// 源末级销售出库单ID
			String saleoutBillId = row.getString("id");
			System.out.println("销售出库单ID："+saleoutBillId);
			if (doneBillSet.contains(saleoutBillId)) {
				continue;
			}
			
			// 如果有互斥锁则跳过不处理这笔单,yzw20220422
			Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(saleoutBillId, billType_saleOutBill, lock_opKey);
			if (lockInfo != null) {
				continue;
			}
			
			// 对进行结算的出库单进行加锁,yzw20220422
			MutexServiceHelper.request(saleoutBillId, billType_saleOutBill, lock_opKey);
			
			ApiResult result =new ApiResult();
			// 源末级销售出库单
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			// 如果是biztype业务类型是退货类型2101的需要走反方向，如果业务类型是销售类型210的走正方向
			DynamicObject biztype = saleoutBillObj.getDynamicObject("biztype");
			if ("210".equals(biztype.getString("number"))) {
				// 正向流程
				result = SaleOutBillMserviceHelper.createDirectForwardBill(saleoutBillId);
			} else if ("2101".equals(biztype.getString("number"))) {
				// 逆向流程
				result = SaleOutBillMserviceHelper.createDirectBackwardBill(saleoutBillId);
			}
			String errorMsg = "";
			if (!result.getSuccess()) {
				// 释放锁，yzw20220422
				MutexServiceHelper.release(saleoutBillId, billType_saleOutBill, lock_opKey);
				errorMsg = result.getMessage();
			}
			DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			resetBill.set("yd_settleerror", errorMsg);
			SaveServiceHelper.save(new DynamicObject[] {resetBill});// 保存
			System.out.println("结束内部流程");
			
			// 使用关联关系将生成的下游单据保存到源销售出库单上
			InternalSettleServiceHelper.saveBotpBills(saleoutBillId);
			
			doneBillSet.add(saleoutBillId);
			
			// 释放锁，yzw20220422
			MutexServiceHelper.release(saleoutBillId, billType_saleOutBill, lock_opKey);
		}
	}
}
