package kd.bos.tcbj.im.outbill.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import kd.bos.tcbj.im.outbill.helper.SettleServiceHelper;
import kd.bos.tcbj.im.util.DynamicObjectUtil;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.im.util.UIUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 描述：销售出库PCP一盘货结算流程处理类
 * @createDate  : 2022/10/31
 * @createAuthor: hst
 */
public class SaleOutBillPCPScheduleTask extends AbstractTask {

	private final Log logger = LogFactory.getLog(this.getClass().getName());
	/* 开门红 */
	private final static String GOODSTART_KEY = "GOOD_START";
	/* 线下一盘货 */
	private final static String OFFLINE_KEY = "OFFLINE";

	/**
	 * 描述：每30分钟执行一次，获取当天需要走PCP一盘货结算的销售出库单
	 * @createDate  : 2022/10/31
	 * @createAuthor: hst
	 */
	@Override
	public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
		String key = params.get("key").toString();
		String entityName = params.containsKey("entityname")
				? params.get("entityname").toString() : BillTypeHelper.BILLTYPE_SALEOUTBILL;
		switch (key) {
			case GOODSTART_KEY : {
				this.PCPGoodStartSchedule();
				break;
			}
			case OFFLINE_KEY : {
				this.PCPOfflineSchedule();
				break;
			}
			default : {
				this.PCPSettleSchedule(key,entityName);
				break;
			}
		}
	}

	/**
	 * 开门红业务处理
	 * @author: hst
	 * @createDate: 2023/03/24
	 */
	private void PCPGoodStartSchedule() {
		DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
				new QFilter[]{new QFilter("number",QFilter.equals,"PCP_INVENTORY_CONTROL")});
		String value = param.getString("name");
		boolean isSettle = StringUtils.isNotBlank(value) ? "true".equals(value) ? true : false : false;
		if (isSettle) {
			// 解锁/锁定 的操作标识
			String lock_opKey = "PCPSettle";
			// 获取当前的日期时间
			Date now = Calendar.getInstance().getTime();
			String today = DateFormatUtils.format(now, "yyyyMMdd");
			QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
			filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
			filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
			filter.and(QFilter.of("yd_tbly =?", "4"));  // 从PCP过来
			filter.and(QFilter.of("yd_isinventory =?", true));  // 一盘货
			String tip = "";
			PCPServiceHelper.createPCPSettleBill(BillTypeHelper.BILLTYPE_SALEOUTBILL, lock_opKey, filter, tip);
		} else {
			throw new KDBizException("当前PCP结算功能已关闭，如需结算请前往参数配置表配置！");
		}
	}

	/**
	 * 线下一盘货业务处理
	 * @author: hst
	 * @createDate: 2023/03/24
	 */
	private void PCPOfflineSchedule() {
		// 解锁/锁定 的操作标识
		String lock_opKey = "PCPOfflineSettle";
		// 获取当前的日期时间
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");
		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "4"));  // 从PCP过来
		filter.and(QFilter.of("yd_isoffline =?", true));  // 线下一盘货
		String tip = "";
		PCPServiceHelper.createPCPSettleBill(BillTypeHelper.BILLTYPE_SALEOUTBILL, lock_opKey, filter, tip);
	}

	/**
	 * 一盘货业务处理
	 * @author: hst
	 * @createDate: 2023/09/08
	 */
	private void PCPSettleSchedule(String key, String entityName) {
		// 查询是否有相应的配置信息
		DynamicObject config = QueryServiceHelper.queryOne("yd_settleconfig",
				String.join(",", DynamicObjectUtil.getAllField("yd_settleconfig")),
				new QFilter[]{QFilter.of("yd_type = ? and status = ?",key,"C")});
		if (Objects.nonNull(config)) {
			String filterStr = config.getString("yd_filter");
			String tip = config.getString("yd_prompt");
			QFilter filter = null;
			if (StringUtils.isNotBlank(filterStr)) {
				try {
					filter = QFilter.of(filterStr);
				} catch (Exception e) {
					throw new KDBizException("过滤条件转换异常");
				}

				// 执行结算流程
				try {
					SettleServiceHelper.createSettleBill(key,entityName,config,filter,tip,false);
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
		}
	}
}
