package kd.bos.tcbj.im.outbill.schedulejob;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.operate.FormOperate;
import kd.bos.list.IListView;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.outbill.helper.SaleOutBillMserviceHelper;
import kd.bos.tcbj.im.transbill.helper.PurNoticeBillMserviceHelper;

/**
 * 描述：销售出库内部结算流程处理类
 * SaleOutBillScheduleTask.java
 * 
 * @createDate  : 2021-12-11
 * @createAuthor: 严祖威
 * @updateDate  : 
 * @updateAuthor: 
 */
public class SaleOutBillScheduleTask extends AbstractTask {
	
	protected int amountPrecision = 2;
	protected int curAmountPrecision = 2;
	protected int pricePrecision = 10;
	protected int curPricePrecision = 10;
	protected BigDecimal ONEHUNDRED = new BigDecimal(100);

	/**
	 * 描述：每30分钟执行一次，获取当天需要走内部结算的销售出库单
	 * 保存状态的、有批次号的yd_ph，未生成内部结算的yd_internalsettle、来源于E3系统的yd_tbly，按品牌汇总生成的yd_frome3，属于客户交易价格中客户的单据，
	 * 
	 * @createDate  : 2021-12-09
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	@Override
	public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyyMMdd");
		
		QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
//		filter.and(QFilter.of("billentry.yd_ph!=?", ""));  // 批次号不为空
		filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
		filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
		filter.and(QFilter.of("yd_tbly =?", "1"));  // 从E3过来
		filter.and(QFilter.of("yd_frome3 =?", "1"));  // 是否按品牌结算为是
		// 本次上线的客户
		DataSet cusDataSet = QueryServiceHelper.queryDataSet("jy", "yd_orgcuspricebill", 
				"yd_customer.number customerNum", null, null);
		List<String> customerList = new ArrayList<String>();
		for (Row row : cusDataSet) {
			customerList.add(row.getString("customerNum"));
		}
		Map<String, Object> cusMap = new HashMap<>();
		cusMap.put("customers", customerList);
		filter.and(new QFilter("customer.number", QCP.in, customerList));
		
		// 后台事务用于测试单笔单据时使用
		if (params!=null&&params.get("billno")!=null) {
			String billNo = params.get("billno").toString();
			filter.and(QFilter.of("billno=?", billNo));  // 测试单号
		}
		
		// 检查是否有满足条件的单据
		DataSet saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", "im_saloutbill", "id,billno",
				new QFilter[] { filter }, null);
		if (!saleBillSet.hasNext()) {
			throw new KDBizException("目前暂无单据满足内部结算条件：单据为暂存、来源系统为E3、未完成内部结算流程、已设置内部结算关系的销售出库单！");
		}
		
		// 本次处理的销售出库单
		Set<String> doneBillSet = new HashSet<String>();
		for (Row row : saleBillSet) {
			// 源末级销售出库单ID
			String saleoutBillId = row.getString("id");
			System.out.println("销售出库单ID："+saleoutBillId);
			if (doneBillSet.contains(saleoutBillId)) {
				continue;
			}
			
			ApiResult result =new ApiResult();
			// 源末级销售出库单
			DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			// 如果是biztype业务类型是退货类型2101的需要走反方向，如果业务类型是销售类型210的走正方向
			DynamicObject biztype = saleoutBillObj.getDynamicObject("biztype");
			if ("210".equals(biztype.getString("number"))) {
				// 正向流程
				result = SaleOutBillMserviceHelper.createForwardBill(saleoutBillId);
			} else if ("2101".equals(biztype.getString("number"))) {
				// 逆向流程
				result = SaleOutBillMserviceHelper.createBackwardBill(saleoutBillId);
			}
			String errorMsg = "";
			if (!result.getSuccess()) {
				errorMsg = result.getMessage();
			}
			DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(saleoutBillId, "im_saloutbill");
			if (errorMsg.length() > 2000) {
				errorMsg = errorMsg.substring(0, 1999);
			}
			resetBill.set("yd_settleerror", errorMsg);
			SaveServiceHelper.save(new DynamicObject[] {resetBill});// 保存
			System.out.println("结束内部流程");
			
			// 使用关联关系将生成的下游单据保存到源销售出库单上
			InternalSettleServiceHelper.saveBotpBills(saleoutBillId);
			
			doneBillSet.add(saleoutBillId);
		}
		
	}

}
