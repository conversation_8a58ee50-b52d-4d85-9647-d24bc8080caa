package kd.bos.tcbj.im.outbill.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * 描述：销售出库PCP虚体组织结算调度任务类
 * @createDate  : 2023/02/20
 * @createAuthor: hst
 */
public class SaleOutBillVirtualScheduleTask  extends AbstractTask {

    private final static String SETTLE_VIRTUAL = "SETTLE_VIRTUAL";

    /**
     * 描述：每30分钟执行一次，获取当天需要走PCP虚体组织结算结算的销售出库单
     * @createDate  : 2023/02/20
     * @createAuthor: hst
     */
    @Override
    public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
        if (params.containsKey("key")) {
            String key = params.get("key").toString();
            switch (key) {
                case SETTLE_VIRTUAL : {
                    // 解锁/锁定 的操作标识
                    String lock_opKey = "PCPVirtualSettle";
                    // 获取当前的日期时间
                    Date now = Calendar.getInstance().getTime();
                    String today = DateFormatUtils.format(now, "yyyyMMdd");
                    QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
                    filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
                    filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
                    filter.and(QFilter.of("yd_tbly =?", "4"));  // 从PCP过来
                    filter.and(QFilter.of("yd_isvirtual =?", true));  // 虚体组织结算
                    String tip = "";
                    PCPServiceHelper.createPCPVirtualSettleBill(BillTypeHelper.BILLTYPE_SALEOUTBILL, lock_opKey, filter, tip);
                    break;
                }
            }
        }
    }
}
