package kd.bos.tcbj.im.outbill.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.outbill.helper.TransSettleServiceHelper;
import kd.bos.tcbj.im.util.DynamicObjectUtil;
import kd.bos.tcbj.im.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 描述：库存调拨中间表结算类
 * @package: kd.bos.tcbj.im.outbill.schedulejob.TransSettleScheduleTask
 * @className: TransSettleScheduleTask
 * @createDate: 2024/06/04
 * @createAuthor: hst
 */
public class TransSettleScheduleTask  extends AbstractTask {

    private final Log logger = LogFactory.getLog(this.getClass().getName());;

    /**
     * 描述：执行结算任务
     * @createDate: 2024/06/04
     * @createAuthor: hst
     */
    @Override
    public void execute (RequestContext requestContext, Map<String, Object> map) throws KDException {
        String key = map.get("key").toString();
        QFilter qFilter = map.containsKey("qFilter") ? (QFilter) map.get("qFilter") : null;
        this.settleSchedule(key, qFilter);
    }

    /**
     * 执行结算
     * @param key
     * @param qFilter
     * @author: hst
     * @createDate: 2024/06/04
     */
    private void settleSchedule (String key, QFilter qFilter) {
        // 查询是否有相应的配置信息
        DynamicObject config = QueryServiceHelper.queryOne("yd_settleconfig",
                String.join(",", DynamicObjectUtil.getAllField("yd_settleconfig")),
                new QFilter[]{QFilter.of("yd_type = ? and status = ?",key,"C")});
        if (Objects.nonNull(config)) {
            String filterStr = config.getString("yd_filter");
            String tip = config.getString("yd_prompt");
            if (Objects.isNull(qFilter)) {
                if (StringUtils.isNotBlank(filterStr)) {
                    try {
                        qFilter = QFilter.of(filterStr);
                    } catch (Exception e) {
                        throw new KDBizException("过滤条件转换异常");
                    }
                } else {
                    throw new KDBizException("未设置过滤条件，请联系管理员！");
                }
            }

            // 执行结算流程
            try {
                new TransSettleServiceHelper().createSettleBill(key, config, qFilter, tip, false);
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
    }
}
