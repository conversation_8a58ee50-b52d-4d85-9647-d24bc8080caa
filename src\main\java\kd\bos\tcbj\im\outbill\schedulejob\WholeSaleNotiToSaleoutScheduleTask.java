package kd.bos.tcbj.im.outbill.schedulejob;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.im.outbill.helper.WholeSaleNoticeBillMserviceHelper;

/**
 * 批发通知单生成销售出库单后台事务类
 * WholeSaleNotiToSaleoutScheduleTask.java
 * @auditor yanzuwei
 * @date 2022年5月22日
 * 
 */
public class WholeSaleNotiToSaleoutScheduleTask extends AbstractTask {

	/**
	 * 描述：每30分钟执行一次，获取系统中已审核，不存在下游单据的批发通知单，进行下推
	 * 
	 * @createDate  : 2022-05-20
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	@Override
	public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
		QFilter billFilters = QFilter.of("yd_targetbillno=?", "");
		billFilters.and(QFilter.of("billstatus=?", "C"));
		DynamicObject[] wholeSaleReturnBills = BusinessDataServiceHelper.load("yd_wholesalenoticebill", "id", billFilters.toArray());
		if (wholeSaleReturnBills.length == 0) {
			throw new KDException("没有满足条件的批发通知单数据！");
		}
		
		Set<String> idSet = new HashSet<String>();
		for(DynamicObject tempObj : wholeSaleReturnBills) {
			idSet.add(tempObj.getPkValue().toString());
		}
		
		WholeSaleNoticeBillMserviceHelper.pushToSaleoutBill(idSet);
	}

}
