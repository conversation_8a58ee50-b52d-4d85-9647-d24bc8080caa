package kd.bos.tcbj.im.outbill.schedulejob;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang3.time.DateFormatUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import json.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.im.outbill.helper.WholeSaleNoticeBillMserviceHelper;

/**
 * 批发通知单调度计划类
 * 定时调用E3接口获取E3批发通知单
 * WholeSaleNoticeBilllScheduleTask.java
 * @auditor yanzuwei
 * @date 2022年5月13日
 * 
 */
public class WholeSaleNoticeBilllScheduleTask extends AbstractTask {

	/**
	 * 描述：每30分钟执行一次，获取当天的单据数据
	 * 
	 * @createDate  : 2022-05-13
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	@Override
	public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
		String diffDayStr = "0";
		if (params.containsKey("diffDays")) {
			diffDayStr = params.get("diffDays").toString();
		}
		
		Calendar cal = Calendar.getInstance();
		Date endDate = cal.getTime();
		// 不传默认查当天
		if (!StringUtil.isEmptyString(diffDayStr)) {
			int diffDays = Integer.parseInt(diffDayStr);
			cal.add(5, diffDays*-1);
		}
		Date beginDate = cal.getTime();
		
		String beginDateStr = DateFormatUtils.format(beginDate, "yyyy-MM-dd");
		String endDateStr = DateFormatUtils.format(endDate, "yyyy-MM-dd");
		JSONObject postJson=new JSONObject();
		postJson.put("rq_start", beginDateStr+" 00:00:00");
		postJson.put("rq_end", endDateStr+" 23:59:59");
		
		// 调用统一方法进行获取
		ApiResult apiResult = WholeSaleNoticeBillMserviceHelper.getWholeSaleNoticeBill(postJson);
		if (!apiResult.getSuccess()) {
			throw new KDBizException(apiResult.getMessage());
		}
	}

}
