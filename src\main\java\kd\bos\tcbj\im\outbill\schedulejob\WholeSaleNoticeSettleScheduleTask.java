package kd.bos.tcbj.im.outbill.schedulejob;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.outbill.helper.SaleOutBillMserviceHelper;
import kd.bos.tcbj.im.util.ORMUtils;
import kd.bos.tcbj.im.util.UIUtils;
import kd.epm.eb.ebBusiness.serviceHelper.MutexServiceHelper;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.*;

/**
 * 成品仓一盘货调度作业类
 * @createDate  : 2023/04/17
 * @createAuthor: hst
 */
public class WholeSaleNoticeSettleScheduleTask  extends AbstractTask {

    private final static String SETTLE_WHOLESALE = "SETTLE_WHOLESALE";
    private final static String RELOT_WHOLESALE = "RELOT_WHOLESALE";

    /**
     * @createDate  : 2023/04/17
     * @createAuthor: hst
     */
    @Override
    public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
        String key = params.get("key").toString();
        switch (key) {
            case SETTLE_WHOLESALE : {
                this.warehouseSettle();
                break;
            }
            case RELOT_WHOLESALE : {
                this.resetSettleBillLot();
                break;
            }
        }
    }

    /**
     * 发起成品仓一盘货结算
     * @author: hst
     * @createDate: 2023/04/17
     */
    public void warehouseSettle() {

        String billType_saleOutBill = BillTypeHelper.BILLTYPE_SALEOUTBILL;
        String lock_opKey = "warehouseSettle"; // 解锁/锁定 的操作标识

        QFilter filter = new QFilter("billstatus", QCP.equals, "A");  // 暂存
        filter.and(QFilter.of("yd_internalsettle =?", "0"));  // 是否已完成内部结算流程为否
        filter.and(QFilter.of("yd_internaltoeas =?", "0"));  // 是否发送EAS结算为否
        filter.and(QFilter.of("yd_tbly =?", "1"));  // 从E3过来
        filter.and(QFilter.of("yd_sourcebilltype =?", "2"));  // 来源单据类型为批发通知单

        DataSet saleBillSet = null;
        try {
            saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", billType_saleOutBill, "id,billno", filter.toArray(), null);

            Set<String> doneBillSet = new HashSet<>();
            for (Row row : saleBillSet) {
                // 源末级销售出库单ID
                String saleoutBillId = row.getString("id");
                System.out.println("销售出库单ID：" + saleoutBillId);
                if (doneBillSet.contains(saleoutBillId)) {
                    continue;
                }

                // 如果有互斥锁则提示不允许操作
                Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(saleoutBillId, billType_saleOutBill, lock_opKey);
                if (lockInfo != null) {
                    throw new KDBizException("系统定时的调度计划在执行中，请勿重复操作发起内部结算！");
                }

                // 对进行结算的出库单进行加锁
                MutexServiceHelper.request(saleoutBillId, billType_saleOutBill, lock_opKey);

                try {
                    // 源末级销售出库单
                    DynamicObject saleOutInfo = BizHelper.getDynamicObjectById(billType_saleOutBill, saleoutBillId);

                    ApiResult result = new ApiResult();
                    // 成品仓一盘货结算只有正向流程
                    DynamicObject biztype = saleOutInfo.getDynamicObject("biztype");
                    if ("210".equals(biztype.getString("number"))) {
                        // 正向流程
                        result = SaleOutBillMserviceHelper.createDirectForwardBill(saleoutBillId);
                    }
                    if (!result.getSuccess()) {
                        // 记录异常信息反写到单据字段信息中
                        DynamicObject BillInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_SALEOUTBILL, saleoutBillId, "billno,yd_settleerror");
                        String errMessage = result.getMessage();
                        // 如果字段过长，则截取长度
                        if (errMessage.length() > 2000) {
                            errMessage = errMessage.substring(0, 2000);
                        }
                        BillInfo.set("yd_settleerror", errMessage);
                        SaveServiceHelper.save(new DynamicObject[]{BillInfo});
                    }

                    // 使用关联关系将生成的下游单据保存到源销售出库单上
                    InternalSettleServiceHelper.saveBotpBills(saleoutBillId);

                    doneBillSet.add(saleoutBillId);
                }finally {
                    // 释放锁
                    MutexServiceHelper.release(saleoutBillId, billType_saleOutBill, lock_opKey);
                }
            }
        }finally {
            // 释放DataSet
            ORMUtils.close(saleBillSet);
        }
    }

    /**
     * 重置批次
     * @author: hst
     * @createDate: 2023/04/17
     */
    private void resetSettleBillLot() {
        // 批发通知单、首笔结算单、未重置批次
        QFilter filter = QFilter.of("yd_sourcebilltype = ? and yd_firstinternalbill = ? " +
                        "and yd_beresetlot = ?", "2",1,0);
        DataSet bills = QueryServiceHelper.queryDataSet(this.getClass().getName(),"im_saloutbill",
                "id",filter.toArray(),null);
        Set<String> ids = new HashSet<>();
        for (Row row : bills) {
            ids.add(row.getString("id"));
        }
        if (ids.size() > 0) {
            QFilter idFilter = new QFilter("yd_gfsaleoutbillid",QFilter.in,ids);
            DataSet lotbills = QueryServiceHelper.queryDataSet(this.getClass().getName(),"yd_saleoutlotbill",
                    "id",idFilter.toArray(),null);
            Set<String> idSet = new HashSet<>();
            for (Row row : lotbills) {
                idSet.add(row.getString("id"));
            }
            if (idSet.size() > 0) {
                SaleOutBillMserviceHelper.resetSettleBillLot(idSet);
            }
        }
    }
}
