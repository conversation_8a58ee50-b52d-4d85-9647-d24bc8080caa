package kd.bos.tcbj.im.plugin;

import kd.bos.bill.IBillPlugin;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.db.DB;
import kd.bos.entity.EntryType;
import kd.bos.exception.KDBizException;
import kd.bos.mvc.bill.BillModel;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;

/**
 * 	编辑界面通用类
 * <AUTHOR>
 *
 */
public class KdepBillPlugin extends KdepFormPlugin implements IBillPlugin {
	
	/**
	 * 	初始化方法
	 */
	public void initialize() {
		super.initialize();
		addItemClickListeners(new String[] { "tbmain" });
	}

	/**
	 * 	新增分录
	 * @param entryId 分录标识
	 * @return int 分录索引
	 */
	public int createNewEntryRow(String entryId) {
		DynamicObject newEntry = BusinessDataServiceHelper.newDynamicObject(getModelName())
				.getDynamicObjectCollection(entryId).addNew();
		long genLongId = DB.genLongId(((EntryType) newEntry.getDataEntityType()).getAlias());
		newEntry.set("id", Long.valueOf(genLongId));
		return getModel().createNewEntryRow(entryId, newEntry);
	}
	
	/**
	 * 	获取分录根据index
	 * @param entryId 分录标识
	 * @param index 索引
	 * @return DynamicObject
	 */
	public DynamicObject getEntryObject(String entryId,int index) {
		return getModel().getEntryRowEntity(entryId, index);
	}

	/**
	 * 	重新装载数据
	 * @param pk Object
	 */
	public void reLoadPage(Object pk) {
		if (!QueryServiceHelper.exists(getModelName(), pk)) {
			throw new KDBizException("数据已不存在，可能被其他人删除了");
		}
		BillModel model = (BillModel) getModel();
		model.setPKValue(pk);
		model.load(pk);
		getView().updateView();
	}

	/**
	 * 	重新装载数据
	 */
	public void reLoadPage() {
		Object pkValue = ((BillModel) getModel()).getPKValue();
		reLoadPage(pkValue);
	}
}
