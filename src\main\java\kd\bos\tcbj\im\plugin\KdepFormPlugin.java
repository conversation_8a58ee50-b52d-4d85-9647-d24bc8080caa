package kd.bos.tcbj.im.plugin;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.EventObject;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import kd.bos.bill.OperationStatus;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.ILocaleString;
import kd.bos.entity.datamodel.IBillModel;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.form.ClientActions;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.IClientViewProxy;
import kd.bos.form.ShowType;
import kd.bos.form.container.Tab;
import kd.bos.form.control.Control;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.control.events.RowClickEventListener;
import kd.bos.form.control.events.TabSelectListener;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.PreOpenFormEventArgs;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.events.BeforeF7SelectEvent;
import kd.bos.form.field.events.BeforeF7SelectListener;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.orm.query.QFilter;

/**
 * 编辑界面通用父类
 * 
 * <AUTHOR>
 *
 */
public class KdepFormPlugin extends AbstractFormPlugin {

	/**
	 * 构造方法
	 */
	public KdepFormPlugin() {
		triggerChangeEvent = true;
	}

	/**
	 * 获取当前页面标识
	 * 
	 * @return String
	 */
	public String getModelName() {
		if (modelName == null)
			modelName = getModel().getDataEntityType().getName();
		return modelName;
	}

	/**
	 * 是否导入
	 * 
	 * @return boolean
	 */
	public boolean isFromImport() {
		IDataModel model = getModel();
		if (model instanceof IBillModel)
			return ((IBillModel) model).isFromImport();
		else
			return false;
	}

	/**
	 * 打开前事件
	 * 
	 * @param e PreOpenFormEventArgs
	 */
	public void preOpenForm(PreOpenFormEventArgs e) {
		super.preOpenForm(e);
		afterPreOpenForm(e);
	}

	/**
	 * 打开后事件
	 * 
	 * @param e PreOpenFormEventArgs
	 */
	public void afterPreOpenForm(PreOpenFormEventArgs e) {
		
	}

	/**
	 * 为F7控件添加监听
	 * 
	 * @param form   BeforeF7SelectListener BeforeF7SelectListener
	 * @param f7Name String[]
	 */
	public void addF7Listener(BeforeF7SelectListener form, String[] f7Name) {
		BasedataEdit f7 = null;
		int i = 0;
		for (int len = f7Name.length; i < len; i++) {
			f7 = (BasedataEdit) getControl(f7Name[i]);
			if (f7 != null)
				f7.addBeforeF7SelectListener(form);
		}

	}

	/**
	 * 行点击监听
	 * 
	 * @param form RowClickEventListener
	 * @param keys String
	 */
	public void addRowClickListener(RowClickEventListener form, String[] keys) {
		EntryGrid grid = null;
		int i = 0;
		for (int len = keys.length; i < len; i++) {
			grid = (EntryGrid) getControl(keys[i]);
			grid = (EntryGrid) getControl(keys[i]);
			if (grid != null)
				grid.addRowClickListener(form);
		}

	}

	/**
	 * 标签选择监听事件
	 * 
	 * @param form    TabSelectListener
	 * @param tabName String
	 */
	public void addTabSelectListener(TabSelectListener form, String[] tabName) {
		Tab tab = null;
		int i = 0;
		for (int len = tabName.length; i < len; i++) {
			tab = (Tab) getControl(tabName[i]);
			if (tab != null)
				tab.addTabSelectListener(form);
		}

	}

	/**
	 * 设置F7过滤
	 * 
	 * @param f7Name String
	 * @param filter QFilter
	 */
	public void setF7Filter(String f7Name, QFilter filter) {
		BasedataEdit f7 = (BasedataEdit) getView().getControl(f7Name);
		f7.setQFilter(filter);
	}

	/**
	 * 获取F7主键
	 * 
	 * @param f7Name String
	 * @return Object
	 */
	public Object getF7PKValue(String f7Name) {
		DynamicObject val = getF7Value(f7Name);
		if (val == null)
			return null;
		else
			return val.get("id");
	}

	/**
	 * 获取F7对象
	 * 
	 * @param f7Name String
	 * @return DynamicObject
	 */
	public DynamicObject getF7Value(String f7Name) {
		return getModel().getDataEntity().getDynamicObject(f7Name);
	}

	/**
	 * 获取文本值
	 * 
	 * @param fieldname String
	 * @return String
	 */
	public String getTextFieldValue(String fieldname) {
		return getModel().getDataEntity().getString(fieldname);
	}

	/**
	 * 获取日期值
	 * 
	 * @param fieldname String
	 * @return Date
	 */
	public Date getDateFieldValue(String fieldname) {
		return getModel().getDataEntity().getDate(fieldname);
	}

	/**
	 * 设置日期值
	 * 
	 * @param fieldname String
	 * @param value     Date
	 */
	public void setDateFieldValue(String fieldname, Date value) {
		getModel().setValue(fieldname, value);
	}

	/**
	 * 获取分录的F7值
	 * 
	 * @param entryTB String
	 * @param f7Name  String
	 * @param index   int
	 * @return DynamicObject
	 */
	public DynamicObject getEntryF7Value(String entryTB, String f7Name, int index) {
		return getRowInfo(entryTB, index).getDynamicObject(f7Name);
	}

	/**
	 * 获取分录行对象
	 * 
	 * @param entryTB String
	 * @param index int
	 * @return DynamicObject
	 */
	public DynamicObject getRowInfo(String entryTB, int index) {
		return getModel().getEntryRowEntity(entryTB, index);
	}

	/**
	 * 获取当前分录行对象
	 * 
	 * @param entryTB String
	 * @return DynamicObject
	 */
	public DynamicObject getCurrentRowInfo(String entryTB) {
		return getRowInfo(entryTB, getCurrentRowIndex(entryTB));
	}

	/**
	 * 获取分录的F7PK值
	 * 
	 * @param entryTB String
	 * @param f7Name  String
	 * @param index   int
	 * @return Object
	 */
	public Object getEntryF7PKValue(String entryTB, String f7Name, int index) {
		DynamicObject val = getEntryF7Value(entryTB, f7Name, index);
		if (val == null)
			return null;
		else
			return val.get("id");
	}

	/**
	 * 获取改变后分录行的index
	 * 
	 * @param e PropertyChangedArgs
	 * @return int
	 */
	public int getRowIndex(PropertyChangedArgs e) {
		return e.getChangeSet()[0].getRowIndex();
	}

	/**
	 * 获取表格index
	 * 
	 * @param tbName String
	 * @return int
	 */
	public int getCurrentRowIndex(String tbName) {
		return getModel().getEntryCurrentRowIndex(tbName);
	}

	/**
	 * 设置不可用
	 * 
	 * @param fieldName String
	 */
	public void setUnEnable(String[] fieldName) {
		getView().setEnable(Boolean.valueOf(false), fieldName);
	}

	/**
	 * 设置可用
	 * 
	 * @param fieldName String[]
	 */
	public void setEnable(String[] fieldName) {
		getView().setEnable(Boolean.valueOf(true), fieldName);
	}

	/**
	 * 设置不可用
	 * 
	 * @param rowIndex  int
	 * @param fieldName String
	 */
	public void setUnEnable(int rowIndex, String[] fieldName) {
		getView().setEnable(Boolean.valueOf(false), rowIndex, fieldName);
	}

	/**
	 * 设置可用
	 * 
	 * @param rowIndex  int
	 * @param fieldName String
	 */
	public void setEnable(int rowIndex, String[] fieldName) {
		getView().setEnable(Boolean.valueOf(true), rowIndex, fieldName);
	}

	/**
	 * 设置不可见
	 * 
	 * @param fieldName String
	 */
	public void setDisVisible(String[] fieldName) {
		getView().setVisible(Boolean.valueOf(false), fieldName);
	}

	/**
	 * 设置可见
	 * 
	 * @param fieldName String[]
	 */
	public void setVisible(String[] fieldName) {
		getView().setVisible(Boolean.valueOf(true), fieldName);
	}

	/**
	 * 设置背景颜色
	 * 
	 * @param key   String
	 * @param color String
	 */
	public void setBackgroudColor(String key, String color) {
		Map dataMap = new HashMap();
		dataMap.put("bc", color);
		setStyle(key, dataMap);
	}

	/**
	 * 	设置边框
	 * @param key String
	 * @param style String
	 */
	public void setborder(String key, String style) {
		Map dataMap = new HashMap();
		Map b = new HashMap();
		b.put("b", style);
		b.put("l", style);
		b.put("r", style);
		b.put("t", style);
		Map s = new HashMap();
		s.put("b", b);
		dataMap.put("s", s);
		setStyle(key, dataMap);
	}

	/**
	 * 	设置颜色
	 * @param key String
	 * @param color String
	 */
	public void setColor(String key, String color) {
		Map dataMap = new HashMap();
		dataMap.put("fc", color);
		setStyle(key, dataMap);
	}

	/**
	 * 设置样式
	 * 
	 * @param key String
	 * @param dataMap Map
	 */
	public void setStyle(String key, Map dataMap) {
		List list = new ArrayList();
		list.add(key);
		list.add(dataMap);
		IClientViewProxy proxy = (IClientViewProxy) getView().getService(IClientViewProxy.class);
		proxy.addAction("updateControlMetadata", list);
	}
	
	
	/**
	 * 
	 * (方法描述：设置表格的颜色)
	 * 
	 * @createDate  : 2020年3月18日
	 * @createAuthor: czy
	 * @updateDate  : 2020年3月18日
	 * @updateAuthor: XXX
	 * @param key 表格标识
	 * @param color 颜色
	 * @param rowIndexes 行索引
	 */
	public void setRowColor(String key,String color, int[] rowIndexes) {
		IClientViewProxy proxy = (IClientViewProxy) getView().getService(IClientViewProxy.class);
		ClientActions.createRowStyleBuilder().setRows(rowIndexes).setForeColor(color).buildStyle().build().invokeControlMethod(proxy,key);
	}

	/**
	 * 设置行的值
	 * 
	 * @param colName String
	 * @param value Object
	 * @param rowIndex int
	 */
	public void setRowValue(String colName, Object value, int rowIndex) {
		getModel().setValue(colName, value, rowIndex);
	}

	/**
	 * 是否新增
	 * 
	 * @return boolean
	 */
	public boolean isNewCreate() {
		DynamicObject dyObject=getModel().getDataEntity();
		return !dyObject.getDataEntityState().getFromDatabase();
	}

	/**
	 * 获取当前单据id
	 * 
	 * @return Object
	 */
	public Object getId() {
		return getModel().getDataEntity().get("id");
	}

	/**
	 * 是否查看状态
	 * 
	 * @return boolean
	 */
	public boolean isView() {
		return OperationStatus.VIEW.equals(getView().getFormShowParameter().getStatus());
	}

	/**
	 * 是否编辑状态
	 * 
	 * @return boolean
	 */
	public boolean isEdit() {
		return OperationStatus.EDIT.equals(getView().getFormShowParameter().getStatus());
	}

	/**
	 * 是否新增状态
	 * 
	 * @return boolean
	 */
	public boolean isAddNew() {
		return OperationStatus.ADDNEW.equals(getView().getFormShowParameter().getStatus());
	}

	/**
	 * 判断是否有PKValue
	 * 
	 * @param obj DynamicObject
	 * @return boolean
	 */
	public boolean hasPKValue(DynamicObject obj) {
		return obj != null && obj.get("id") != null;
	}

	/**
	 * 清楚表格数据
	 * 
	 * @param tbNames String[]
	 */
	public void clearTB(String[] tbNames) {
		String[] as = tbNames;
		int i = as.length;
		for (int j = 0; j < i; j++) {
			String name = as[j];
			getModel().deleteEntryData(name);
		}

	}

	/**
	 * 获取值
	 * 
	 * @param key String
	 * @return Object
	 */
	public Object getValue(String key) {
		return getModel().getValue(key);
	}

	/**
	 * 获取String值
	 * 
	 * @param key String
	 * @return String
	 */
	public String getStringValue(String key) {
		Object value = getValue(key);
		if (value instanceof String)
			return (String) value;
		if (value instanceof ILocaleString)
			return ((ILocaleString) value).getLocaleValue();
		else
			throw new KDBizException((new StringBuilder()).append(key).append(":找不到该标识！！！").toString());
	}

	/**
	 * 获取Boolean值
	 * 
	 * @param key String
	 * @return boolean
	 */
	public boolean getBooleanValue(String key) {
		Object value = getValue(key);
		if (value instanceof Boolean)
			return ((Boolean) value).booleanValue();
		else
			throw new KDBizException((new StringBuilder()).append(key)
					.append(":该值类型不是boolean类型").toString());
	}

	/**
	 * 获取值
	 * 
	 * @param key String
	 * @param i   int
	 * @return Object
	 */
	public Object getValue(String key, int i) {
		return getModel().getValue(key, i);
	}

	/**
	 * 设置值
	 * 
	 * @param key   String
	 * @param value Object
	 */
	public void setValue(String key, Object value) {
		getModel().setValue(key, value);
	}

	/**
	 * 设置值
	 * 
	 * @param key                  String
	 * @param value                Object
	 * @param isTriggerChangeEvent boolean
	 */
	public void setValue(String key, Object value, boolean isTriggerChangeEvent) {
		triggerChangeEvent = isTriggerChangeEvent;
		getModel().setValue(key, value);
	}

	/**
	 * 设置值
	 * 
	 * @param key   String
	 * @param value Object
	 * @param i     int
	 */
	public void setValue(String key, Object value, int i) {
		getModel().setValue(key, value, i);
	}

	/**
	 * 设置值
	 * 
	 * @param key                  String
	 * @param value                Object
	 * @param i                    int
	 * @param isTriggerChangeEvent boolean
	 */
	public void setValue(String key, Object value, int i, boolean isTriggerChangeEvent) {
		triggerChangeEvent = isTriggerChangeEvent;
		getModel().setValue(key, value, i);
	}

	/**
	 * showForm
	 * 
	 * @param formId        String
	 * @param params        Map
	 * @param closeCallBack CloseCallBack
	 * @param showType      ShowType
	 */
	public void showForm(String formId, Map params, CloseCallBack closeCallBack, ShowType showType) {
		FormShowParameter param = new FormShowParameter();
		param.getOpenStyle().setShowType(showType);
		param.setFormId(formId);
		if (params != null)
			param.setCustomParams(params);
		if (closeCallBack != null)
			param.setCloseCallBack(closeCallBack);
		getView().showForm(param);
	}

	/**
	 * 取消F7树形
	 * 
	 * @param e BeforeF7SelectEvent
	 */
	public void setNoTreeF7(BeforeF7SelectEvent e) {
		e.getFormShowParameter().setFormId("bos_listf7");
	}

	/**
	 * 获取操作类型
	 * 
	 * @param e BeforeDoOperationEventArgs
	 * @return String
	 */
	protected String getOpKey(BeforeDoOperationEventArgs e) {
		String key = ((AbstractOperate) e.getSource()).getOperateKey();
		return key;
	}

	/**
	 * 获取操作类型
	 * 
	 * @param e BeforeDoOperationEventArgs
	 * @return String
	 */
	protected String getOpKey(AfterDoOperationEventArgs e) {
		String key = ((AbstractOperate) e.getSource()).getOperateKey();
		return key;
	}

	/**
	 * 属性值改变
	 * 
	 * @param e PropertyChangedArgs
	 */
	public void propertyChanged(PropertyChangedArgs e) {
		super.propertyChanged(e);
		String key = e.getProperty().getName();
		if (!triggerChangeEvent) {
			return;
		} else {
			triggerChangeEvent = false;
			propertyChanged(e, key);
			reSetTriggerChangeEvent4UnitTest();
			return;
		}
	}

	/**
	 * 属性改变
	 * 
	 * @param propertychangedargs PropertyChangedArgs
	 * @param s                   String
	 */
	public void propertyChanged(PropertyChangedArgs propertychangedargs, String s) {
	}

	/**
	 * reSetTriggerChangeEvent4UnitTest
	 */
	protected void reSetTriggerChangeEvent4UnitTest() {
		String flag = getPageCache().get("flag_unittest");
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(flag))
			triggerChangeEvent = true;
	}

	/**
	 * 获取打开界面参数
	 * 
	 * @param value Object
	 * @return Object
	 */
	protected Object getParamValue(Object value) {
		FormShowParameter fsp = getView().getFormShowParameter();
		Map customParams = fsp.getCustomParams();
		return customParams.get(value);
	}

	/**
	 * 获取事件key
	 * 
	 * @param evt EventObject
	 * @return String
	 */
	protected String getKey(EventObject evt) {
		Control source = (Control) evt.getSource();
		String key = source.getKey();
		return key;
	}

	/**
	 * 获取操作后结果
	 * 
	 * @param e AfterDoOperationEventArgs
	 * @return boolean
	 */
	protected boolean getOperationResult(AfterDoOperationEventArgs e) {
		OperationResult result = e.getOperationResult();
		return result == null || result.isSuccess();
	}

	/**
	 * ZERO
	 */
	public static final BigDecimal ZERO;
	
	/**
	 * ONE
	 */
	public static final BigDecimal ONE;
	
	/**
	 * triggerChangeEvent
	 */
	protected volatile boolean triggerChangeEvent;
	
	/**
	 * modelName
	 */
	private String modelName;

	static {
		ZERO = BigDecimal.ZERO;
		ONE = BigDecimal.ONE;
	}
}
