package kd.bos.tcbj.im.plugin;

import java.util.*;
import kd.bos.dataentity.entity.*;
import kd.bos.entity.datamodel.*;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.form.*;
import kd.bos.form.container.Tab;
import kd.bos.form.control.events.TabSelectListener;
import kd.bos.form.events.*;
import kd.bos.form.field.events.BeforeFilterF7SelectEvent;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.list.*;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;

/**
 * 	列表界面通用父类
 * 
 * <AUTHOR>
 *
 */
public class KdepListPlugin extends AbstractListPlugin {

	/**
	 * f7选择过滤前事件
	 * @param e BeforeFilterF7SelectEvent
	 */
	public void filterContainerBeforeF7Select(BeforeFilterF7SelectEvent e) {
		super.filterContainerBeforeF7Select(e);
	}

	/**
	 * 	获取Model名
	 * @return String
	 */
	public String getModelName() {
		if (modelName == null)
			modelName = getListModel().getDataEntityType().getName();
		return modelName;
	}

	/**
	 * 	列表界面打开事件
	 * @param e PreOpenFormEventArgs
	 */
	public void preOpenForm(PreOpenFormEventArgs e) {
		super.preOpenForm(e);
		afterPreOpenForm(e);
	}


	/**
	 * 	获取表视图
	 * @return IListView
	 */
	protected IListView getListView() {
		IFormView formview = getView();
		IListView listview = (IListView) formview;
		return listview;
	}

	
	/**
	 * 	获取表模型
	 * @return IListModel
	 */
	protected IListModel getListModel() {
		return getListView().getListModel();
	}

	
	/**
	 * 	列表打开后事件
	 * @param e PreOpenFormEventArgs
	 */
	public void afterPreOpenForm(PreOpenFormEventArgs e) {
		
	}

	
	/**
	 * 	isLookup
	 * @return boolean
	 */
	public boolean isLookup() {
		boolean isLookup = false;
		if (getView().getFormShowParameter() instanceof ListShowParameter) {
			ListShowParameter listShowParameter = (ListShowParameter) getView().getFormShowParameter();
			isLookup = listShowParameter.isLookUp();
		}
		return isLookup;
	}

	
	/**
	 * 	 操作执行后结果
	 * @param e AfterDoOperationEventArgs
	 * @return boolean
	 */
	protected boolean getOperationResult(AfterDoOperationEventArgs e) {
		OperationResult result = e.getOperationResult();
		return result == null || result.isSuccess();
	}

	/**
	 * 	操作后事件
	 * @param e AfterDoOperationEventArgs
	 */
	public void afterDoOperation(AfterDoOperationEventArgs e) {
		super.afterDoOperation(e);
		if ("default".equals(e.getOperateKey()) || "undefault".equals(e.getOperateKey()))
			((BillList) getControl("billlistap")).refresh();
	}


	/**
	 * 	List转map类型
	 * @param fs List fs
	 * @return Map
	 */
	protected Map filter2Map(List fs) {
		Map fsMap = new HashMap();
		QFilter f;
		for (Iterator iterator = fs.iterator(); iterator.hasNext(); fsMap.put(f.getProperty(), f))
			f = (QFilter) iterator.next();

		return fsMap;
	}

	
	/**
	 * 	获取选中的id集合
	 * @return Object[]
	 */
	protected Object[] getSelectIds() {
		BillList billList = (BillList) getControl("billlistap");
		ListSelectedRowCollection selectedRows = billList.getSelectedRows();
		Object[] ids = selectedRows.getPrimaryKeyValues();
		return ids;
	}

	
	/**
	 * 	打开表单事件
	 * @param formId String 
	 * @param params Map
	 * @param closeCallBack CloseCallBack
	 * @param showType ShowType
	 */
	protected void showForm(String formId, Map params, CloseCallBack closeCallBack, ShowType showType) {
		FormShowParameter param = new FormShowParameter();
		param.getOpenStyle().setShowType(showType);
		param.setFormId(formId);
		if (params != null)
			param.setCustomParams(params);
		if (closeCallBack != null)
			param.setCloseCallBack(closeCallBack);
		getView().showForm(param);
	}

	/**
	 * 	是否检查修改
	 * @param entityName String
	 * @param statusField String
	 * @param allowedField String
	 * @return boolean
	 */
	public boolean isCheckModify(String entityName, String statusField, String allowedField) {
		Object[] selectIds = getSelectIds();
		if (selectIds.length > 1)
			throw new KDBizException("选择的行数超过1行!!");
		QFilter qFilter = new QFilter("id", "=", selectIds[0]);
		DynamicObject query = QueryServiceHelper.queryOne(entityName, statusField, qFilter.toArray());
		return allowedField.equals(query.getString(statusField));
	}

	/**
	 * 	增加Tab监听
	 * @param form TabSelectListener
	 * @param tabName String
	 */
	public void addTabSelectListener(TabSelectListener form, String[] tabName) {
		Tab tab = null;
		int i = 0;
		for (int len = tabName.length; i < len; i++) {
			tab = (Tab) getControl(tabName[i]);
			if (tab != null)
				tab.addTabSelectListener(form);
		}

	}

	
	/**
	 * 	设置不可用
	 * @param fieldName String[]
	 */
	public void setUnEnable(String[] fieldName) {
		getView().setEnable(Boolean.valueOf(false), fieldName);
	}

	/**
	 * 	设置可用
	 * @param fieldName String[]
	 */
	public void setEnable(String[] fieldName) {
		getView().setEnable(Boolean.valueOf(true), fieldName);
	}

	
	/**
	 * 	设置不可用
	 * @param rowIndex int
	 * @param fieldName String[]
	 */
	public void setUnEnable(int rowIndex, String[] fieldName) {
		getView().setEnable(Boolean.valueOf(false), rowIndex, fieldName);
	}

	
	/**
	 * 	设置可用
	 * @param rowIndex int
	 * @param fieldName String[]
	 */
	public void setEnable(int rowIndex, String[] fieldName) {
		getView().setEnable(Boolean.valueOf(true), rowIndex, fieldName);
	}

	/**
	 * 	设置不可见
	 * @param fieldName String[]
	 */
	public void setDisVisible(String[] fieldName) {
		getView().setVisible(Boolean.valueOf(false), fieldName);
	}

	/**
	 * 	设置可见
	 * @param fieldName String[]
	 */
	public void setVisible(String[] fieldName) {
		getView().setVisible(Boolean.valueOf(true), fieldName);
	}

	/**
	 * 	获取值
	 * @param key String
	 * @return Object
	 */
	public Object getValue(String key) {
		return getModel().getValue(key);
	}

	
	/**
	 * 	获取选中行ID的结合
	 * @param isSingle 是否单选
	 * @return Object[]
	 */
	protected Object[] getSelectedIds(boolean isSingle) {
		Object[] ids = getSelectIds();
		int size = ids.length;
		if (size == 0)
			throw new KDBizException("请先选择一行!!");
		if (isSingle && size > 1)
			throw new KDBizException("选中超过一行!!");
		else
			return ids;
	}

	/**
	 * modelName
	 */
	private String modelName;
}