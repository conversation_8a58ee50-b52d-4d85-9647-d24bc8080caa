package kd.bos.tcbj.im.price.constants;

/**
 * @package: kd.bos.tcbj.im.price.constants.InterPriceSettingConstantt
 * @className InterPriceSettingConstant
 * @author: hst
 * @createDate: 2024/06/14
 * @version: v1.0
 * @descrition: 内部交易价格同步设置常量类
 */
public class InterPriceSettingConstant {

    // 单据标识
    public static String MAIN_ENTITY = "yd_interprice_setting";
    // 销售组织
    public static String SALEORG_FIELD = "yd_saleorg";
    // 品牌分录
    public static String BRAND_ENTRY = "yd_brandentity";
    // 品牌
    public static String BRAND_COLUMN = "yd_brand";
    // 采购组织分录
    public static String PURORG_ENTRY = "yd_purorgentity";
    // 采购组织
    public static String PURORG_COLUMN = "yd_purorg";

    /**
     * 获取字段
     * @return
     * @author: hst
     * @createDate: 2024/06/14
     */
    public static String getFields () {
        return SALEORG_FIELD + "," + BRAND_ENTRY + "." + BRAND_COLUMN + ","
                + PURORG_ENTRY + "." + PURORG_COLUMN;
    }
}
