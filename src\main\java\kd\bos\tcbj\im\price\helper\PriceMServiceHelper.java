package kd.bos.tcbj.im.price.helper;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.price.constants.InterPriceSettingConstant;
import kd.bos.tcbj.im.price.imp.PriceMserviceImpl;
import kd.bos.tcbj.im.price.mservice.PriceMservice;
import kd.bos.tcbj.im.util.StringUtils;

/**
 * 描述：价格同步管理工具类
 * PriceMServiceHelper.java
* @auditor yanzuwei
* @date 2022年3月16日
* 
*/
public class PriceMServiceHelper {
	/**
	 * 描述：根据MAP价格数据更新内部结算交易价格表
	 * 
	 * @createDate  : 2022-03-15
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param outOrgId 销售方组织
	 * @param inOrgId 采购方组织
	 * @param matPriceMap 物料价格表
	 * @return 是否成功
	 */
	public static ApiResult updateOrgPrice(String outOrgId, String inOrgId, Map<String, BigDecimal> matPriceMap) {
		PriceMservice priceMservice = PriceMserviceImpl.getInstance();
		return priceMservice.updateOrgPrice(outOrgId, inOrgId, matPriceMap);
	}
	
	/**
	 * 描述：根据MAP价格数据更新经销商交易价格表
	 * 
	 * @createDate  : 2022-03-15
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param outOrgId 销售方组织
	 * @param customerId 经销商客户
	 * @param matPriceMap 物料价格表
	 * @return 是否成功
	 */
	public static ApiResult updateCustomerPrice(String outOrgId, String customerId, Map<String, BigDecimal> matPriceMap) {
		PriceMservice priceMservice = PriceMserviceImpl.getInstance();
		return priceMservice.updateCustomerPrice(outOrgId, customerId, matPriceMap);
	}
	
	/**
	 * 描述：根据销售方与经销商关系调用营销云接口
	 * 
	 * @createDate  : 2022-04-19
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param billIdSet 交易关系集合
	 * @return 是否成功
	 */
	public static ApiResult synPriceFromYXY(Set<String> billIdSet) {
		PriceMservice priceMservice = PriceMserviceImpl.getInstance();
		return priceMservice.synPriceFromYXY(billIdSet);
	}

	/**
	 * 同步组织间交易价格
	 * @author: hst
	 * @createDate: 2024/06/14
	 */
	public static void synPriceFromMatPrice (DynamicObject[] bills) {
		Map<String, Map<String, BigDecimal>> priceMap = new HashMap<String, Map<String, BigDecimal>>();
		StringBuffer errStr = new StringBuffer();

		Set<String> saleorgIds = new HashSet<>();
		for (DynamicObject bill : bills) {
			String matId = bill.getString("yd_material.id");
			BigDecimal price = bill.getBigDecimal("yd_focusprice");
			String saleOrgId = bill.getString("yd_saleorg.id");

			if (StringUtils.isNotBlank(saleOrgId) && StringUtils.isNotBlank(matId)) {

				if (priceMap.containsKey(saleOrgId)) {
					// 将中间表中的价格数据解析成一个物料-价格的map
					Map<String, BigDecimal> matPriceMap = priceMap.get(saleOrgId);
					matPriceMap.put(matId, price);
					priceMap.put(saleOrgId, matPriceMap);
				} else {
					Map<String, BigDecimal> matPriceMap = new HashMap<String, BigDecimal>();
					matPriceMap.put(matId, price);
					priceMap.put(saleOrgId, matPriceMap);
				}

				saleorgIds.add(saleOrgId);
			}
		}

		// 获取获取内部交易价同步设置
		Set<String> orgSet = getInterPriceSetting(saleorgIds,null);

		for (String key : orgSet) {
			String saleOrgId = key.split("&")[0];
			String purOrgId = key.split("&")[1];
			Map<String, BigDecimal> matPriceMap = new HashMap<>();

			if (priceMap.containsKey(saleOrgId)) {
				matPriceMap.putAll(priceMap.get(saleOrgId));

				// 对比物料价格是否发生改变并更新价格表，抽象出公共方法，传Map进去
				if (matPriceMap.size() > 0) {
					ApiResult result = PriceMServiceHelper.updateOrgPrice(saleOrgId, purOrgId, matPriceMap);
					if (!result.getSuccess()) {
						errStr.append(result.getMessage() + "\n");
					}
				}
			}
		}
	}

	/**
	 * 获取内部交易价同步设置
	 * @param saleOrgIds
	 * @author: hst
	 * @createDate: 2024/06/14
	 */
	public static Set<String> getInterPriceSetting (Set<String> saleOrgIds,Set<String> purOrgIds) {
		Set<String> result = new HashSet<>();

		QFilter qFilter = new QFilter("billstatus",QFilter.equals,"C");
		if (Objects.nonNull(saleOrgIds) && saleOrgIds.size() > 0) {
			qFilter = qFilter.and(new QFilter(InterPriceSettingConstant.SALEORG_FIELD + ".id",
					QFilter.in,saleOrgIds));
		}
		if (Objects.nonNull(purOrgIds) && purOrgIds.size() > 0) {
			qFilter = qFilter.and(new QFilter(InterPriceSettingConstant.PURORG_ENTRY + "."
					+ InterPriceSettingConstant.PURORG_COLUMN + ".id",QFilter.in,purOrgIds));
		}

		DataSet dataSet = QueryServiceHelper.queryDataSet("getInterPriceSetting",
				InterPriceSettingConstant.MAIN_ENTITY,InterPriceSettingConstant.getFields(),
				qFilter.toArray(),null);

		for (Row row : dataSet) {
			String saleOrg = row.getString(InterPriceSettingConstant.SALEORG_FIELD);
			String purOrg = row.getString(InterPriceSettingConstant.PURORG_ENTRY + "." + InterPriceSettingConstant.PURORG_COLUMN);

			if (StringUtils.isNotBlank(saleOrg) && StringUtils.isNotBlank(purOrg)) {
				String key = saleOrg + "&" + purOrg;
				result.add(key);
			}
		}

		return result;
	}
}
