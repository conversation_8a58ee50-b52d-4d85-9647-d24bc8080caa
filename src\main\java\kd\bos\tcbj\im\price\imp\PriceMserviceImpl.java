package kd.bos.tcbj.im.price.imp;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kd.bos.tcbj.im.util.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;

import json.JSON;
import json.JSONArray;
import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.dataentity.utils.OrmUtils;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.monitor.log.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;
import kd.bos.tcbj.im.price.mservice.PriceMservice;
import kd.bos.tcbj.im.transbill.service.ApiHelper;
import kd.bos.tcbj.im.transbill.service.E3Service;

/**
* @auditor yanzuwei
* @date 2022年3月15日
* 
*/
public class PriceMserviceImpl implements PriceMservice {
	
	/**
	 * 内部实例化
	 */
	private final static PriceMserviceImpl priceMserviceImpl = new PriceMserviceImpl();
	
	private final static SimpleDateFormat DATE_SDF =new SimpleDateFormat("yyyy-MM-dd");
	
	/**
	 * (构造函数：私有化的价格管理内部接口实现类)
	 * 
	 * @createDate  : 2022-03-15
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	public PriceMserviceImpl() {
		
	}
	
	/**
	 * 描述：获取价格管理内部接口实现类实例
	 * 
	 * @createDate  : 2022-03-15
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @return 价格管理内部接口实现类
	 */
	public static PriceMservice getInstance() {
		return priceMserviceImpl;
	}

	/**
	 * 描述：根据MAP价格数据更新内部结算交易价格表
	 * 
	 * @createDate  : 2022-03-15
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param outOrgId 销售方组织
	 * @param inOrgId 采购方组织
	 * @param matPriceMap 物料价格表
	 * @return 是否成功
	 */
	@Override
	public ApiResult updateOrgPrice(String outOrgId, String inOrgId, Map<String, BigDecimal> matPriceMap) {

		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 根据两个组织找到对应的内部交易价格表，遍历分录价格表并对比形成历史价格表记录
			QFilter priceFilter = new QFilter("yd_outorg", QCP.equals, outOrgId);
			priceFilter.and(new QFilter("yd_inorg", QCP.equals, inOrgId));
			priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
			DynamicObject[] priceBills = BusinessDataServiceHelper.load("yd_pricerelbill",
					"id,entryentity.yd_materiel,entryentity.yd_price,entryentity.yd_begindate,entryentity.yd_enddate,entryentity.yd_unsyned", priceFilter.toArray());
			DynamicObject priceBill = null;
			// update by hst 2024/06/14 优化内部价格同步
			if (priceBills.length > 0) {
				priceBill = priceBills[0];
			} else {
				priceBill = BusinessDataServiceHelper.newDynamicObject("yd_pricerelbill");
				priceBill.set("billno", DateTimeUtils.format(new Date(),"yyyyMMddhhmmss"));
				priceBill.set("billstatus","C");
				DynamicObject outOrg = BusinessDataServiceHelper.loadSingle(outOrgId,"bos_org");
				DynamicObject inOrg = BusinessDataServiceHelper.loadSingle(inOrgId,"bos_org");
				priceBill.set("yd_outorg",outOrg);
				priceBill.set("yd_inorg",inOrg);
			}
			DynamicObjectCollection priceEnCol = priceBill.getDynamicObjectCollection("entryentity");
			DynamicObjectType enType = priceEnCol.getDynamicObjectType();
			// 对数据进行对比，有新增价格的就增行，有价格更新的就更新行和新增行
			DynamicObjectCollection newRowCol = new DynamicObjectCollection();  // 新增行记录
			DynamicObjectCollection delRowCol = new DynamicObjectCollection();  // 要删除的行记录

			String nowStr = DATE_SDF.format(new Date());
			Date now = DATE_SDF.parse(nowStr);
			Calendar cal = Calendar.getInstance();
			cal.setTime(now);
			cal.add(5, -1);
			Date beforeDay = cal.getTime();

			// 先遍历价格表中已有的物料，看看map中是否有，有的话就进行对比更新并从map中移除，一个物料可能存在多条分录记录，最后map剩余的就都是要新加的物料分录行
			Set<String> enMatSet = new HashSet<String>();
			int len = priceEnCol.size();
			for (int i = 0; i < len; i++) {
				DynamicObject enObj = priceEnCol.get(i);
				DynamicObject matObj = enObj.getDynamicObject("yd_materiel");
				String matId = matObj.getPkValue().toString();
				if (enObj.getBoolean("yd_unsyned")) {  // 如果标记为不需要同步，则不执行更新单价逻辑
					enMatSet.add(matId);
					continue;
				}

				if (matPriceMap.containsKey(matId)) {
					enMatSet.add(matId);
					BigDecimal oriPrice = enObj.getBigDecimal("yd_price");
					BigDecimal newPrice = matPriceMap.get(matId);
					if (oriPrice.compareTo(newPrice) == 0) {
						continue;
					}
					// 有则对比生效日期是否有差异，将分录中原有的生效日期和当前日期进行对比
					// 只处理到期日比今天大的分录行记录，其他的是同一天多次修改或历史记录，每2个月清理一次
					String oriEndDateStr = DATE_SDF.format(enObj.getDate("yd_enddate"));
					Date oriEndDate = DATE_SDF.parse(oriEndDateStr);
					if (oriEndDate.after(now) || oriEndDate.equals(now)) {
						// 在当前时间之后的，表示当前最新的记录，看看是否为同一天多次修改，是的话直接更新，不是的话保存为历史记录，并且复制一行作为最新记录
						String oriBeginDateStr = DATE_SDF.format(enObj.getDate("yd_begindate"));
						Date oriBeginDate = DATE_SDF.parse(oriBeginDateStr);
						if (StringUtils.equals(nowStr, oriBeginDateStr)) {
							enObj.set("yd_price", newPrice);
						} else {
							DynamicObject newLine = (DynamicObject) OrmUtils.clone(enObj, false, true);
							enObj.set("yd_enddate", beforeDay);
							newLine.set("yd_begindate", now);
							newLine.set("yd_price", newPrice);
							newRowCol.add(newLine);
						}

					} else {
						// 比今天更前，表示是历史价格，看看是否距今两个月，是的话清理掉
						cal.setTime(beforeDay);
						cal.add(2, -2);
						Date twoMonDay = cal.getTime();
						if (oriEndDate.before(twoMonDay)) {
							delRowCol.add(enObj);
						}
					}
				}
			}

			// 移除已经处理的物料
			Iterator<String> enMatIte = enMatSet.iterator();
			while (enMatIte.hasNext()) {
				matPriceMap.remove(enMatIte.next());
			}

			// 遍历剩余新的价格记录，创建分录
			Iterator<String> ite = matPriceMap.keySet().iterator();
			int in = 0;
			while (ite.hasNext()) {
				in++;
				// 没有该物料就新建一行分录
				DynamicObject newRow = new DynamicObject(enType);
				String matId = ite.next();
				newRow.set("yd_materiel", BusinessDataServiceHelper.loadSingle(matId, "bd_material", "id,number,name,yd_basedatafield"));  // 物料
				newRow.set("yd_price", matPriceMap.get(matId));  // 价格
				newRow.set("yd_begindate", now);  // 生效日期--今天
				newRow.set("yd_enddate", DATE_SDF.parseObject("2199-12-31"));  // 到期日--2199-12-31
				newRowCol.add(newRow);
			}

			priceEnCol.removeAll(delRowCol);
			priceEnCol.addAll(newRowCol);
			SaveServiceHelper.save(new DynamicObject[]{priceBill});

		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：根据MAP价格数据更新经销商交易价格表
	 * 
	 * @createDate  : 2022-03-15
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param outOrgId 销售方组织
	 * @param customerId 经销商客户
	 * @param matPriceMap 物料价格表
	 * @return 是否成功
	 */
	@Override
	public ApiResult updateCustomerPrice(String outOrgId, String customerId, Map<String, BigDecimal> matPriceMap) {
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 找到对应的经销商交易价格表，遍历分录价格表并对比形成历史价格表记录
			QFilter priceFilter = new QFilter("yd_org", QCP.equals, outOrgId);
			priceFilter.and(new QFilter("yd_customer", QCP.equals, customerId));
			priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
			DynamicObject[] priceBills = BusinessDataServiceHelper.load("yd_orgcuspricebill", 
					"id,entryentity.yd_materiel,entryentity.yd_saleprice,entryentity.yd_begindate,entryentity.yd_enddate,entryentity.yd_unsyned", priceFilter.toArray());
			if (priceBills.length > 0) {
				DynamicObject priceBill = priceBills[0];
				DynamicObjectCollection priceEnCol = priceBill.getDynamicObjectCollection("entryentity");
				DynamicObjectType enType = priceEnCol.getDynamicObjectType();
				// 对数据进行对比，有新增价格的就增行，有价格更新的就更新行和新增行
				DynamicObjectCollection newRowCol = new DynamicObjectCollection();  // 新增行记录
				DynamicObjectCollection delRowCol = new DynamicObjectCollection();  // 要删除的行记录
				
				String nowStr = DATE_SDF.format(new Date());
				Date now = DATE_SDF.parse(nowStr);
				Calendar cal = Calendar.getInstance();
				cal.setTime(now);
				cal.add(5, -1);
				Date beforeDay = cal.getTime();
				
				// 先遍历价格表中已有的物料，看看map中是否有，有的话就进行对比更新并从map中移除，一个物料可能存在多条分录记录，最后map剩余的就都是要新加的物料分录行
				Set<String> enMatSet = new HashSet<String>();
				int len = priceEnCol.size();
				for (int i=0;i<len;i++) {
					DynamicObject enObj = priceEnCol.get(i);
					DynamicObject matObj = enObj.getDynamicObject("yd_materiel");
					String matId = matObj.getPkValue().toString();
					if (enObj.getBoolean("yd_unsyned")) {  // 如果标记为不需要同步，则不执行更新单价逻辑
						enMatSet.add(matId);
						continue;
					}
					
					if (matPriceMap.containsKey(matId)) {
						enMatSet.add(matId);
						BigDecimal oriPrice = enObj.getBigDecimal("yd_saleprice");
						BigDecimal newPrice = matPriceMap.get(matId);
						if (oriPrice.compareTo(newPrice)==0) {
							continue;
						}
						// 有则对比生效日期是否有差异，将分录中原有的生效日期和当前日期进行对比
						// 只处理到期日比今天大的分录行记录，其他的是同一天多次修改或历史记录，每2个月清理一次
						String oriEndDateStr = DATE_SDF.format(enObj.getDate("yd_enddate"));
						Date oriEndDate = DATE_SDF.parse(oriEndDateStr);
						if (oriEndDate.after(now) || oriEndDate.equals(now)) {
							// 在当前时间之后的，表示当前最新的记录，看看是否为同一天多次修改，是的话直接更新，不是的话保存为历史记录，并且复制一行作为最新记录
							String oriBeginDateStr = DATE_SDF.format(enObj.getDate("yd_begindate"));
							Date oriBeginDate = DATE_SDF.parse(oriBeginDateStr);
							if (StringUtils.equals(nowStr, oriBeginDateStr)) {
								enObj.set("yd_saleprice", newPrice);
							} else {
								// 最新记录的开始时间不是今天，就是今天之前的日期，价格还不一致，要形成历史价格，PS：会不会更新未来的价格记录？
								DynamicObject newLine = (DynamicObject) OrmUtils.clone(enObj, false, true);
								enObj.set("yd_enddate", beforeDay);
								newLine.set("yd_begindate", now);
								newLine.set("yd_saleprice", newPrice);
								newRowCol.add(newLine);
							}
							
						} else {
							// 比今天更前，表示是历史价格，看看是否距今两个月，是的话清理掉
							cal.setTime(beforeDay);
							cal.add(2, -2);
							Date twoMonDay = cal.getTime();
							if (oriEndDate.before(twoMonDay)) {
								delRowCol.add(enObj);
							}
						}
					}
				}
				
				// 移除已经处理的物料
				Iterator<String> enMatIte = enMatSet.iterator();
				while (enMatIte.hasNext()) {
					matPriceMap.remove(enMatIte.next());
				}
				
				// 遍历剩余新的价格记录，创建分录
				Iterator<String> ite = matPriceMap.keySet().iterator();
				int in=0;
				while(ite.hasNext()) {
					in++;
					// 没有该物料就新建一行分录
					DynamicObject newRow = new DynamicObject(enType);
					String matId = ite.next();
					newRow.set("yd_materiel", BusinessDataServiceHelper.loadSingle(matId, "bd_material","id,number,name,yd_basedatafield"));  // 物料
					newRow.set("yd_saleprice", matPriceMap.get(matId));  // 价格
					newRow.set("yd_begindate", now);  // 生效日期--今天
					newRow.set("yd_enddate", DATE_SDF.parseObject("2199-12-31"));  // 到期日--2199-12-31
					newRowCol.add(newRow);
				}
				
				priceEnCol.removeAll(delRowCol);
				priceEnCol.addAll(newRowCol);
				SaveServiceHelper.save(new DynamicObject[] {priceBill});
				
			}
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}
	
	/**
	 * 描述：根据销售方与经销商关系调用营销云接口
	 * 
	 * @createDate  : 2022-03-23
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param billIdSet 交易关系集合
	 * @return 是否成功
	 */
	@Override
	public ApiResult synPriceFromYXY(Set<String> billIdSet) {
		SimpleDateFormat DATETIME_SDF =new SimpleDateFormat("yyyyMMddHHmmssSSS");
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			// 遍历数据，获取末级组织与表头客户的关系
			String creatorId = RequestContext.get().getUserId().toString();
			DynamicObject creator = BusinessDataServiceHelper.loadSingle(creatorId, "bos_user","id");
			DynamicObject[] midBills = new DynamicObject[billIdSet.size()];
			int i=0;
			for (String billId : billIdSet) {
				QFilter filter = new QFilter("id", QCP.equals, billId);
				filter.and(new QFilter("yd_synfromyxy", QCP.equals, true));  // 是否需要同步营销云
				DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_innerrelbill", 
						"id,yd_synfromyxy,yd_newcustomer.id newCusId,yd_newcustomer.number newCusNum,entryentity.yd_orglevel orglevel,entryentity.yd_org.id orgid,entryentity.yd_org.yd_eas_number orgNum,entryentity.yd_org.name orgName", 
						filter.toArray(), "entryentity.seq desc");
				// 不是的就跳过处理下一张单据
				if (!settleDataSet.hasNext()) {
					continue;
				}
				Row lastRow = settleDataSet.next();
				String orgId = lastRow.getString("orgid");
				String orgNum = lastRow.getString("orgNum");
				String newCusId = lastRow.getString("newCusId");
				String newCusNum = lastRow.getString("newCusNum");
				
				// 调用营销云接口获取价格数据，保存到营销云价格中间表中，再用单独方法try-catch跑价格同步逻辑
				JSONObject postJson=new JSONObject();
				JSONObject req=new JSONObject();
				req.put("tenantId", orgNum);
//				req.put("tenantId", "b6e7931006ea430594f9d21e6a1c7738");
				JSONArray partnersList = new JSONArray();
				partnersList.add(newCusNum);
//				partnersList.add("4028e47876cc61a00176d0b0ff753097");
				req.put("partners", partnersList);
				postJson.put("request", req);
				
				// 查询是否存在中间表
				DynamicObject midBillObj = new DynamicObject();
				QFilter midFilter = new QFilter("yd_org.yd_eas_number", QCP.equals, orgNum);
				midFilter.and(new QFilter("yd_customer.number", QCP.equals, newCusNum));
				midFilter.and(new QFilter("yd_besyned", QCP.equals, 0));  // 加个标识区分是否已经同步，这样可以保存历史接口数据
				DynamicObjectCollection col = QueryServiceHelper.query("yd_marketingprice", "id", midFilter.toArray());
				if (col.size() > 0) {
					String midBillId = col.get(0).getString("id");
					midBillObj = BusinessDataServiceHelper.loadSingle(midBillId, "yd_marketingprice");
				} else {
					midBillObj = BusinessDataServiceHelper.newDynamicObject("yd_marketingprice");
					midBillObj.set("billno", "AUTO"+DATETIME_SDF.format(new Date()));
					midBillObj.set("billstatus", "C");
					midBillObj.set("yd_org", BusinessDataServiceHelper.loadSingle(orgId, "bos_org", "is,name,number"));
					midBillObj.set("yd_customer", BusinessDataServiceHelper.loadSingle(newCusId, "bd_customer", "is,name,number"));
					midBillObj.set("creator", creator);
					midBillObj.set("modifier", creator);
					midBillObj.set("auditor", creator);
					midBillObj.set("createtime", new Date());
					midBillObj.set("modifytime", new Date());
					midBillObj.set("auditdate", new Date());
				}
				
				midBillObj = getYXYInterfacePrice(postJson, midBillObj);
				
				midBills[i] = midBillObj;
				i++;
			}
			
			// 保存单据
			if (midBills.length > 0) {
				SaveServiceHelper.save(midBills);
			}
			
			// 根据营销云价格中间表更新经销商供货价格表
			for (DynamicObject midBillObj : midBills) {
				try {
					String saleOrgId = midBillObj.getDynamicObject("yd_org").getPkValue().toString();
					String customerId = midBillObj.getDynamicObject("yd_customer").getPkValue().toString();
					
					DynamicObjectCollection enCol = midBillObj.getDynamicObjectCollection("entryentity");
					Map<String,BigDecimal> matPriceMap = new HashMap<String,BigDecimal>();  // 最终要更新的物料价格Map
					Map<String,DynamicObject> matObjMap = new HashMap<String,DynamicObject>();  // 物料价格对比集合，用于对比去重
					
					for (int j=0,len=enCol.size(); j<len; j++) {
						DynamicObject newPriceObj = enCol.get(j);
						// 因为同步时会因为不存在映射而有空值物料，所以要过滤一下
						if (newPriceObj.getDynamicObject("yd_material") == null) {
							continue;
						}
						String matId = newPriceObj.getDynamicObject("yd_material").getPkValue().toString();
						// 如果有重复记录的需要将新旧记录进行对比，如果原记录的生效日期更晚就用原纪录
						if (matObjMap.containsKey(matId)) {
							DynamicObject oriObj = matObjMap.get(matId);
							Date oriDate = oriObj.getDate("yd_createdate");
							Date newDate = newPriceObj.getDate("yd_createdate");
							if (newDate.before(oriDate)) {
								matObjMap.put(matId, oriObj);
							} else {
								matObjMap.put(matId, newPriceObj);
							}
						} else {
							matObjMap.put(matId, newPriceObj);
						}
						
						DynamicObject finalPriceObj = matObjMap.get(matId);  // 循环到当前的最新物料价格
						
						BigDecimal price = finalPriceObj.getBigDecimal("yd_price");
						matPriceMap.put(matId, price);
					}
					
					// 对比物料价格是否发生改变并更新价格表，抽象出公共方法，传Map进去
					ApiResult result = PriceMServiceHelper.updateCustomerPrice(saleOrgId, customerId, matPriceMap);
					if (!result.getSuccess()) {
						throw new KDBizException(result.getMessage());
					} else {
						midBillObj.set("yd_besyned", true);
					}
					
					
				} catch (Exception e) {
					System.out.println(e.getLocalizedMessage());
				}
			}
			
			// 保存单据，这里的保存是保存上面重置的标识
			if (midBills.length > 0) {
				SaveServiceHelper.save(midBills);
			}
			
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 调用营销云接口获取价格数据并保存到中间表
	 * @param postJson 请求接口参数
	 * @param midBillObj 中间表单据
	 */
	private DynamicObject getYXYInterfacePrice(JSONObject postJson, DynamicObject midBillObj) {
		DynamicObjectCollection enCol = midBillObj.getDynamicObjectCollection("entryentity");
		enCol.clear();  // 如果是刚刚获取完还没同步，想要再次获取就先清空分录数据
		
		DynamicObject yxyIpObj = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "TCBJ_YXY_IP").toArray());
		String yxyIp = yxyIpObj.getString("name");
		DynamicObject yxyPathObj = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "TCBJ_YXY_PATH").toArray());
		String yxyPath = yxyPathObj.getString("name");
		String result = ApiHelper.post(yxyIp+yxyPath, postJson.toJSONString());
		
		JSONObject returnJson = JSONObject.parseObject(result);
		System.out.println("返回结果:"+returnJson.toString());
		JSONArray dataList = returnJson.get("data")!=null?returnJson.getJSONArray("data"):new JSONArray();
		for (int i = 0; i < dataList.size(); i++) {
			JSONObject priceObj = dataList.getJSONObject(i);
			String orgId = priceObj.getString("orgId");
			String partnerId = priceObj.getString("partnerId");
			String productId = priceObj.getString("productId");
			String productNo = priceObj.getString("productNo");
			String productCsn = priceObj.getString("productCsn");
			BigDecimal price = priceObj.getBigDecimal("price");
			String year = priceObj.getString("year");
			String month = priceObj.getString("month");
			String createTime = priceObj.getString("createTime");
			
			DynamicObject enObj = new DynamicObject(enCol.getDynamicObjectType());
			enObj.set("yd_yxyorgid", orgId);
			enObj.set("yd_partnerid", partnerId);
			enObj.set("yd_productid", productId);
			enObj.set("yd_productno", productNo);
			enObj.set("yd_price", price);
			enObj.set("yd_year", year);
			enObj.set("yd_month", month);
			enObj.set("yd_createtime", createTime);
			enObj.set("yd_productcsn", productCsn);
			
			// 根据物料编码查一遍物料
			QFilter matFilter = new QFilter("status", QCP.equals, "C");
			matFilter.and(new QFilter("number", QCP.equals, productCsn));
			DynamicObjectCollection matCol = QueryServiceHelper.query("bd_material", "id,number,name", matFilter.toArray());
			if (matCol.size() > 0 ) {
				enObj.set("yd_material", BusinessDataServiceHelper.loadSingle(matCol.get(0).getString("id"), "bd_material","id"));
			}
			
			enObj.set("yd_createdate", new Date(Long.parseLong(createTime)));
			
			enCol.add(enObj);
		}
		
		return midBillObj;
	}

}
