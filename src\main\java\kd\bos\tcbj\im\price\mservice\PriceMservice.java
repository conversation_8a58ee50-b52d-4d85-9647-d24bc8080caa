package kd.bos.tcbj.im.price.mservice;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

import kd.bos.entity.api.ApiResult;

/**
 * 描述：价格更新接口类
 * 
* @auditor yanzuwei
* @date 2022年3月15日
* 
*/
public interface PriceMservice {
	/**
	 * 描述：根据MAP价格数据更新内部结算交易价格表
	 * 
	 * @createDate  : 2022-03-15
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param outOrgId 销售方组织
	 * @param inOrgId 采购方组织
	 * @param matPriceMap 物料价格表
	 * @return 是否成功
	 */
	public ApiResult updateOrgPrice(String outOrgId, String inOrgId, Map<String, BigDecimal> matPriceMap);
	
	/**
	 * 描述：根据MAP价格数据更新经销商交易价格表
	 * 
	 * @createDate  : 2022-03-15
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param outOrgId 销售方组织
	 * @param customerId 经销商客户
	 * @param matPriceMap 物料价格表
	 * @return 是否成功
	 */
	public ApiResult updateCustomerPrice(String outOrgId, String customerId, Map<String, BigDecimal> matPriceMap);
	
	
	/**
	 * 描述：根据销售方与经销商关系调用营销云接口
	 * 
	 * @createDate  : 2022-03-23
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param billIdSet 交易关系集合
	 * @return 是否成功
	 */
	public ApiResult synPriceFromYXY(Set<String> billIdSet);
}
