package kd.bos.tcbj.im.price.oplugin;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;

/**
 * MatPriceTypeBillOp.java
 * 物料计价信息中间表操作插件
 * 
 * @auditor yanzuwei
 * @date 2022年4月12日
 * 
 */
public class MatPriceTypeBillOp extends AbstractOperationServicePlugIn {
	
	/**
	 * 
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 * 
	 * @createDate  : 2022年4月12日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 加载单据数据包事件
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_matid");
		e.getFieldKeys().add("yd_material");
		e.getFieldKeys().add("yd_focusprice");
		e.getFieldKeys().add("yd_pricetop");
		e.getFieldKeys().add("yd_saleorg");
	}
	
	/**
	 * 
	 * 描述：开启事务，未提交数据库事件
	 * 1.根据物料计价信息中间表数据更新股份卖给各个子公司的内部交易价格表
	 * 物料按品牌分组，再找到对应的内部关系表，再找到对应的价格表
	 * 
	 * @createDate  : 2022年4月12日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 开启事务，未提交数据库事件
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		String opKey=e.getOperationKey();
		if(StringUtil.equalsIgnoreCase("updateOrgPrice", opKey)) {
			
			DynamicObject[] dynamicObjectArray=e.getDataEntities();
			// 获取所有的物料计价信息，数据集成方案中运行会一条数据进一次，列表用再click事件处理全部
//			DynamicObject[] dynamicObjectArray = BusinessDataServiceHelper.load("yd_matpricemid", "yd_matid,yd_material,yd_focusprice,yd_pricetop,yd_saleorg", null);
//			Map<String,Map<String,BigDecimal>> brandPriceMap = new HashMap<String,Map<String,BigDecimal>>();
			
			Set<String> saleOrgIdSet = new HashSet<String>();  // 销售组织ID集合
//			Map<String,DynamicObject> matObjMap = new HashMap<String,DynamicObject>();  // 物料价格对比集合，用于对比去重
			for (DynamicObject matPriceBill : dynamicObjectArray) {
				
				// 因为同步时会因为不存在映射而有空值物料，所以要过滤一下
				if (matPriceBill.getDynamicObject("yd_material") == null) {
					continue;
				}
				if (matPriceBill.getDynamicObject("yd_material").getDynamicObject("yd_basedatafield") == null) {
					continue;
				}
				String matId = matPriceBill.getDynamicObject("yd_material").getPkValue().toString();
				BigDecimal price = matPriceBill.getBigDecimal("yd_focusprice");
				
				// 存放销售组织ID
				String saleOrgId = matPriceBill.getDynamicObject("yd_saleorg").getPkValue().toString();
				saleOrgIdSet.add(saleOrgId);
				
				// 根据品牌分组
				String brandId = matPriceBill.getDynamicObject("yd_material").getDynamicObject("yd_basedatafield").getPkValue().toString();
				
				Map<String, BigDecimal> matPriceMap = new HashMap<String, BigDecimal>();
				matPriceMap.put(matId, price);
				
				List<String> orgMapList = InternalSettleServiceHelper.getBrandOrgPriceMap(brandId, saleOrgIdSet);
				for (String orgMap : orgMapList) {
					String outOrgId = orgMap.split("&")[0];
					String inOrgId = orgMap.split("&")[1];
					// 对比物料价格是否发生改变并更新价格表，抽象出公共方法，传Map进去
					ApiResult result = PriceMServiceHelper.updateOrgPrice(outOrgId, inOrgId, matPriceMap);
					if (!result.getSuccess()) {
						throw new KDBizException(result.getMessage());
					}
				}
			}
			
//			SaveServiceHelper.save(dynamicObjectArray);
		} else if ("updateprice".equals(opKey)) {
			// 同步组织间交易价格
			PriceMServiceHelper.synPriceFromMatPrice(e.getDataEntities());
		}
	}
}
