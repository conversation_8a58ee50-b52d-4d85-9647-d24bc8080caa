package kd.bos.tcbj.im.price.oplugin;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;

/**
 * PurPriceBillOp.java
 * 采购价目表操作插件
 * 
* @auditor yanzuwei
* @date 2022年3月15日
* 
*/
public class PurPriceBillOp extends AbstractOperationServicePlugIn {
	/**
	 * 
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 * 
	 * @createDate  : 2022年3月15日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 加载单据数据包事件
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_outorg");
		e.getFieldKeys().add("yd_inorg");
		e.getFieldKeys().add("entryentity");
		e.getFieldKeys().add("entryentity.yd_material");
		e.getFieldKeys().add("entryentity.yd_begindate");
		e.getFieldKeys().add("entryentity.yd_enddate");
		e.getFieldKeys().add("entryentity.yd_price");
	}
	
	/**
	 * 
	 * 描述：开启事务，未提交数据库事件
	 * 1.根据采购价目表数据更新药业卖给麦优的内部交易价格表
	 * 
	 * @createDate  : 2022年3月15日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 开启事务，未提交数据库事件
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		String opKey=e.getOperationKey();
		if(StringUtil.equalsIgnoreCase("updateprice", opKey)) {
			DynamicObject[] dynamicObjectArray=e.getDataEntities();
			for (DynamicObject purPriceBill : dynamicObjectArray) {
				String outOrgId = purPriceBill.getDynamicObject("yd_outorg").getPkValue().toString();
				String inOrgId = purPriceBill.getDynamicObject("yd_inorg").getPkValue().toString();
				QFilter priceFilter = new QFilter("yd_outorg", QCP.equals, outOrgId);
				priceFilter.and(new QFilter("yd_inorg", QCP.equals, inOrgId));
				priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
				if (QueryServiceHelper.exists("yd_pricerelbill", priceFilter.toArray())) {
					// 将中间表中的价格数据解析成一个物料-价格的map
					DynamicObjectCollection newPriceCol = purPriceBill.getDynamicObjectCollection("entryentity");
					Map<String,BigDecimal> matPriceMap = new HashMap<String,BigDecimal>();  // 最终要更新的物料价格Map
					Map<String,DynamicObject> matObjMap = new HashMap<String,DynamicObject>();  // 物料价格对比集合，用于对比去重
					for (int i=0,len=newPriceCol.size();i<len;i++) {
						DynamicObject newPriceObj = newPriceCol.get(i);
						// 因为同步时会因为不存在映射而有空值物料，所以要过滤一下
						if (newPriceObj.getDynamicObject("yd_material") == null) {
							continue;
						}
						String matId = newPriceObj.getDynamicObject("yd_material").getPkValue().toString();
						// 如果有重复记录的需要将新旧记录进行对比，如果原记录的生效日期更晚就用原纪录
						if (matObjMap.containsKey(matId)) {
							DynamicObject oriObj = matObjMap.get(matId);
							Date oriDate = oriObj.getDate("yd_begindate");
							Date newDate = newPriceObj.getDate("yd_begindate");
							if (newDate.before(oriDate)) {
								matObjMap.put(matId, oriObj);
							} else {
								matObjMap.put(matId, newPriceObj);
							}
						} else {
							matObjMap.put(matId, newPriceObj);
						}
						
						DynamicObject finalPriceObj = matObjMap.get(matId);  // 循环到当前的最新物料价格
						
						BigDecimal price = finalPriceObj.getBigDecimal("yd_price");
						matPriceMap.put(matId, price);
					}
					
					// 对比物料价格是否发生改变并更新价格表，抽象出公共方法，传Map进去
					ApiResult result = PriceMServiceHelper.updateOrgPrice(outOrgId, inOrgId, matPriceMap);
					if (!result.getSuccess()) {
						throw new KDBizException(result.getMessage());
					}
					
				}
			}
			SaveServiceHelper.save(dynamicObjectArray);
		}
	}
}
