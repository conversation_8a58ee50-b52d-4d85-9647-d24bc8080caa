package kd.bos.tcbj.im.price.oplugin;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;

/**
 * SalePriceBillOp.java
 * 销售价目表操作插件
 * 
* @auditor yanzuwei
* @date 2022年3月18日
* 
*/
public class SalePriceBillOp extends AbstractOperationServicePlugIn {
	
	/**
	 * 
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 * 
	 * @createDate  : 2022年3月15日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 加载单据数据包事件
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("billno");
		e.getFieldKeys().add("yd_org");
		e.getFieldKeys().add("yd_customer");
		e.getFieldKeys().add("entryentity");
		e.getFieldKeys().add("entryentity.yd_material");
		e.getFieldKeys().add("entryentity.yd_begindate");
		e.getFieldKeys().add("entryentity.yd_enddate");
		e.getFieldKeys().add("entryentity.yd_price");
	}
	
	/**
	 * 
	 * 描述：开启事务，未提交数据库事件
	 * 1.根据销售价目表数据更新麦优卖给南京佰健的经销商交易价格表
	 * 
	 * @createDate  : 2022年3月15日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 开启事务，未提交数据库事件
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		String opKey=e.getOperationKey();
		if(StringUtil.equalsIgnoreCase("updateprice", opKey)) {
			DynamicObject[] dynamicObjectArray=e.getDataEntities();
			for (DynamicObject salePriceBill : dynamicObjectArray) {
				// 获取销售价目表的适用范围表，遍历分录的客户得到经销商价目表
				QFilter usedFilter = new QFilter("yd_salepricebillno", QCP.equals, salePriceBill.getString("billno"));
//				DynamicObject[] usedBills = BusinessDataServiceHelper.load("yd_salepriceused", "id,billno", usedFilter.toArray());
				DynamicObjectCollection usedBills = QueryServiceHelper.query("yd_salepriceused", "id,billno", usedFilter.toArray());
				if (usedBills.size() == 0) {
					continue;
				}
				
				for (DynamicObject usedBillInfo : usedBills) {
					usedBillInfo = BusinessDataServiceHelper.loadSingle(usedBillInfo.getString("id"), "yd_salepriceused");
					String outOrgId = usedBillInfo.getDynamicObject("yd_saleorg").getPkValue().toString();
					
					DynamicObjectCollection enCol = usedBillInfo.getDynamicObjectCollection("entryentity");
					for (DynamicObject enInfo : enCol) {
						String cusId = enInfo.getDynamicObject("yd_customer").getPkValue().toString();
						QFilter priceFilter = new QFilter("yd_org", QCP.equals, outOrgId);
						priceFilter.and(new QFilter("yd_customer", QCP.equals, cusId));
						priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
						if (QueryServiceHelper.exists("yd_orgcuspricebill", priceFilter.toArray())) {
							// 将中间表中的价格数据解析成一个物料-价格的map
							DynamicObjectCollection newPriceCol = salePriceBill.getDynamicObjectCollection("entryentity");
							Map<String,BigDecimal> matPriceMap = new HashMap<String,BigDecimal>();  // 最终要更新的物料价格Map
							Map<String,DynamicObject> matObjMap = new HashMap<String,DynamicObject>();  // 物料价格对比集合，用于对比去重
							for (int i=0,len=newPriceCol.size();i<len;i++) {
								DynamicObject newPriceObj = newPriceCol.get(i);
								// 因为同步时会因为不存在映射而有空值物料，所以要过滤一下
								if (newPriceObj.getDynamicObject("yd_material") == null) {
									continue;
								}
								String matId = newPriceObj.getDynamicObject("yd_material").getPkValue().toString();
								// 如果有重复记录的需要将新旧记录进行对比，如果原记录的生效日期更晚就用原纪录
								if (matObjMap.containsKey(matId)) {
									// TODO：咨询营销云接口返回的物料清单会不会有重复物料记录
									DynamicObject oriObj = matObjMap.get(matId);
									Date oriDate = oriObj.getDate("yd_begindate");
									Date newDate = newPriceObj.getDate("yd_begindate");
									if (newDate.before(oriDate)) {
										matObjMap.put(matId, oriObj);
									} else {
										matObjMap.put(matId, newPriceObj);
									}
								} else {
									matObjMap.put(matId, newPriceObj);
								}
								
								DynamicObject finalPriceObj = matObjMap.get(matId);  // 循环到当前的最新物料价格
								
								BigDecimal price = finalPriceObj.getBigDecimal("yd_price");
								matPriceMap.put(matId, price);
							}
							
							// 对比物料价格是否发生改变并更新价格表，抽象出公共方法，传Map进去
							ApiResult result = PriceMServiceHelper.updateCustomerPrice(outOrgId, cusId, matPriceMap);
							if (!result.getSuccess()) {
								throw new KDBizException(result.getMessage());
							}
							
						}
					}
				}
				
			}
			SaveServiceHelper.save(dynamicObjectArray);
		}
	}
}
