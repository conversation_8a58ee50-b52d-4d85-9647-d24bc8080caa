package kd.bos.tcbj.im.price.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.entity.MainEntityType;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.im.price.plugin.ChooseInfoFormPlugin
 * @className ChooseInfoFormPlugin
 * @author: hst
 * @createDate: 2024/06/14
 * @description: 同步信息选择动态表单插件
 * @version: v1.0
 */
public class ChooseInfoFormPlugin extends AbstractFormPlugin {

    /**
     * 操作执行后
     * @param e
     * @author: hst
     * @createDate: 2024/06/14
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs e) {
        super.afterDoOperation(e);
        if ("confirm".equals(e.getOperateKey()) && e.getOperationResult().isSuccess()) {
            this.packageReturnData();
        }
    }

    /**
     * 封装回传数据
     * @author: hst
     * @createDate: 2024/06/14
     */
    private void packageReturnData () {
        Map<String, Object> returnData = new HashMap<>();
        DynamicObject dynamicObject = this.getModel().getDataEntity();
        MainEntityType entityType = (MainEntityType) dynamicObject.getDataEntityType();
        Map<String, IDataEntityProperty> fieldMap = entityType.getFields();
        for (String key : fieldMap.keySet()) {
            if (Objects.nonNull(dynamicObject.get(key))) {
                returnData.put(key, dynamicObject.get(key));
            }
        }
        this.getView().returnDataToParent(returnData);
        this.getView().close();
    }
}
