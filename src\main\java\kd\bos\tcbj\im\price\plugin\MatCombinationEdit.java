package kd.bos.tcbj.im.price.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.tcbj.im.plugin.KdepBillPlugin;
import kd.bos.tcbj.im.util.BigDecimalUtil;
import kd.bos.tcbj.im.util.UIUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 主商品拆分编辑界面插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-10
 */
public class MatCombinationEdit extends KdepBillPlugin {

    @Override
    public void beforeItemClick(BeforeItemClickEvent evt) {
        String opKey = evt.getOperationKey();
        if (StringUtils.equals(opKey, "save") || StringUtils.equals(opKey, "submit")) {
            // 校验子单据提的明细合计是否为100%
            DynamicObjectCollection entries = this.getModel().getEntryEntity("entryentity");
            int index = 0;
            for (DynamicObject entry : entries) {
                String matType = entry.getString("yd_mattype");
                if (StringUtils.isNotBlank(matType) && StringUtils.equals("2",matType)) {
                    BigDecimal sumNum = BigDecimalUtil.ZERO;
                    for (DynamicObject subentry : entry.getDynamicObjectCollection("yd_subentryentity")) {
                        sumNum = sumNum.add((BigDecimal) subentry.get("yd_rate"));
                    }
                    // 判断每个分录合计值是否等于100%
                    if (sumNum.compareTo(BigDecimalUtil.ONE_HUNDRED) != 0) {
                        this.getView().showTipNotification(String.format("第%s行分录比例合计不为100%%", index + 1));
                        evt.setCancel(true);
                    }
                }
            }
        }
    }
}
