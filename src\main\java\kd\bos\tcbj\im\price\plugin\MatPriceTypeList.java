package kd.bos.tcbj.im.price.plugin;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.serialization.SerializationUtils;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.api.JobInfo;
import kd.bos.schedule.api.JobType;
import kd.bos.schedule.api.TaskInfo;
import kd.bos.schedule.form.JobForm;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.price.constants.InterPriceSettingConstant;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.plugin.AuditResultListPlugin;

/**
 * MatPriceTypeList.java
 * 物料计价信息中间表列表插件类
 * 
 * @auditor yanzuwei
 * @date 2022年4月15日
 * 
 */
public class MatPriceTypeList extends AbstractListPlugin {

	@Override
	public void itemClick(ItemClickEvent evt) {
		String itemKey=evt.getItemKey();
		// 获取营销云价格功能
		if (StringUtil.equalsIgnoreCase("yd_updateorgprice", itemKey)) {
			synPriceFromMatPriceType();
			this.getView().showMessage("执行完成，请查看处理结果！");
			this.getView().invokeOperation("refresh");
		}
		// 更新组织间交易价格
		if (StringUtil.equalsIgnoreCase("yd_updateprice", itemKey)) {
			// 打开组织填写页面
			this.openChooseInfoFillPage();
		}
	}

	/**
	 * 子页面关闭回调事件
	 * @param closedCallBackEvent
	 * @author: hst
	 * @createDate: 2024/06/14
	 */
	@Override
	public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
		super.closedCallBack(closedCallBackEvent);
		String actionId = closedCallBackEvent.getActionId();
		Object returndata = closedCallBackEvent.getReturnData();
		if (Objects.nonNull(returndata)) {
			switch (actionId) {
				case "updateTransPrice" : {
					// 同步价格
					this.synPriceFromMatPrice(((HashMap) returndata));
					break;
				}
				case "taskcloseback" : {
					// 任务执行完回调
					this.taskCallBack(closedCallBackEvent.getReturnData());
				}
			}
		}
	}

	/**
	 * 获取所有的物料计价信息进行数据更新
	 */
	private void synPriceFromMatPriceType() {
		// 获取所有的物料计价信息，数据集成方案中运行会一条数据进一次，列表用再click事件处理全部
		DynamicObject[] dynamicObjectArray = BusinessDataServiceHelper.load("yd_matpricemid", "yd_matid,yd_material,yd_focusprice,yd_pricetop,yd_saleorg", null);
		Map<String,Map<String,BigDecimal>> brandPriceMap = new HashMap<String,Map<String,BigDecimal>>();
		
		Set<String> saleOrgIdSet = new HashSet<String>();  // 销售组织ID集合
//		Map<String,DynamicObject> matObjMap = new HashMap<String,DynamicObject>();  // 物料价格对比集合，用于对比去重
		for (DynamicObject matPriceBill : dynamicObjectArray) {
			
			// 因为同步时会因为不存在映射而有空值物料，所以要过滤一下
			if (matPriceBill.getDynamicObject("yd_material") == null) {
				continue;
			}
			if (matPriceBill.getDynamicObject("yd_material").getDynamicObject("yd_basedatafield") == null) {
				continue;
			}
			String matId = matPriceBill.getDynamicObject("yd_material").getPkValue().toString();
			BigDecimal price = matPriceBill.getBigDecimal("yd_focusprice");
			
			// 存放销售组织ID
			saleOrgIdSet.add(matPriceBill.getDynamicObject("yd_saleorg").getPkValue().toString());
			
			// 根据品牌分组
			String brandId = matPriceBill.getDynamicObject("yd_material").getDynamicObject("yd_basedatafield").getPkValue().toString();
			
			if (brandPriceMap.containsKey(brandId)) {
				// 将中间表中的价格数据解析成一个物料-价格的map
				Map<String, BigDecimal> matPriceMap = brandPriceMap.get(brandId);
				matPriceMap.put(matId, price);
				brandPriceMap.put(brandId, matPriceMap);
			} else {
				Map<String, BigDecimal> matPriceMap = new HashMap<String, BigDecimal>();
				matPriceMap.put(matId, price);
				brandPriceMap.put(brandId, matPriceMap);
			}
			
		}
		
		// 根据物料+销售组织ID获取组织交易关系
		Iterator<String> ite = brandPriceMap.keySet().iterator();
		while(ite.hasNext()) {
			String matBrandId = ite.next();
			List<String> orgMapList = InternalSettleServiceHelper.getBrandOrgPriceMap(matBrandId, saleOrgIdSet);
			for (String orgMap : orgMapList) {
				String outOrgId = orgMap.split("&")[0];
				String inOrgId = orgMap.split("&")[1];
				// 对比物料价格是否发生改变并更新价格表，抽象出公共方法，传Map进去
				ApiResult result = PriceMServiceHelper.updateOrgPrice(outOrgId, inOrgId, brandPriceMap.get(matBrandId));
				if (!result.getSuccess()) {
					throw new KDBizException(result.getMessage());
				}
			}
		}
		
	}

	/**
	 * 打开组织填写页面
	 * @author: hst
	 * @createDate: 2024/06/14
	 */
	private void openChooseInfoFillPage () {
		FormShowParameter showParameter = new FormShowParameter();
		showParameter.setFormId("yd_chooseinfo");
		showParameter.setCloseCallBack(new CloseCallBack(this, "updateTransPrice"));
		showParameter.getOpenStyle().setShowType(ShowType.Modal);
		this.getView().showForm(showParameter);
	}

	/**
	 * 同步组织间价格
	 * @param returnData
	 * @author: hst
	 * @createDate: 2024/06/14
	 */
	private void synPriceFromMatPrice (HashMap returnData) {
		Object saleOrgObj = returnData.get("yd_saleorg");
		Object purOrgObj = returnData.get("yd_purorg");
		String saleOrgId = "";
		String purOrgId = "";
		if (Objects.nonNull(saleOrgObj)) {
			saleOrgId = ((DynamicObject) saleOrgObj).getString("id");
		}
		if (Objects.nonNull(purOrgObj)) {
			purOrgId = ((DynamicObject) purOrgObj).getString("id");
		}

		this.dispatch(saleOrgId,purOrgId);
	}

	/**
	 * 创建任务目标，发布新任务
	 * @param saleOrgId
	 * @param purOrgId
	 * @author: hst
	 * @createDate: 2024/06/14
	 */
	private void dispatch(String saleOrgId, String purOrgId) {
		// 创建任务目标
		JobInfo jobInfo = new JobInfo();
		jobInfo.setAppId("bos");
		jobInfo.setJobType(JobType.REALTIME);
		jobInfo.setName("test job");
		jobInfo.setId(UUID.randomUUID().toString());
		jobInfo.setTaskClassname("kd.bos.tcbj.im.price.schedulejob.MatPriceTypeTask");

		// 自定义参数
		Map<String,Object> params = new HashMap<>();
		params.put("key", "update_price");
		params.put("saleOrgId", saleOrgId);
		params.put("purOrgId", purOrgId);
		jobInfo.setParams(params);
		// 回调参数，设置一个回调处理标识(actionId)
		CloseCallBack closeCallBack = new CloseCallBack(this, "taskcloseback");
		// 发布任务，并显示进度
		JobForm.dispatch(jobInfo, this.getView(), closeCallBack);
	}

	/**
	 * 任务完成后的回调处理
	 * @param returnData
	 * @author: hst
	 * @createDate: 2024/06/14
	 */
	private void taskCallBack(Object returnData) {
		if (returnData == null) {
			return;
		}
		if (returnData instanceof Map<?, ?>) {
			@SuppressWarnings("unchecked")
			Map<String, Object> result = (Map<String, Object>)returnData;
			if (result.containsKey("taskinfo")) {
				String taskInfoStr = (String)result.get("taskinfo");
				if (StringUtils.isNotBlank(taskInfoStr)) {
					TaskInfo taskInfo = SerializationUtils.fromJsonString(taskInfoStr, TaskInfo.class);
					if (taskInfo.isTaskEnd()) {
						// 获取任务执行完毕，生成的内容
						JSONObject data = JSONObject.parseObject(taskInfo.getData());
						if (Objects.nonNull(data)) {
							if (data.containsKey("success") && "true".equals(data.getString("success"))) {

							}
						}
					}
				}
			}
		}
	}
}
