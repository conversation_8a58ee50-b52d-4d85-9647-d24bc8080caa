package kd.bos.tcbj.im.price.schedulejob;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;

/**
 * 描述：营销云价格同步调度计划处理类
 * MarketingPriceSysScheduleTask.java
 * 
 * @auditor yanzuwei
 * @date 2022年4月19日
 * 
 */
public class MarketingPriceSysScheduleTask extends AbstractTask {

	@Override
	public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
		// 获取所有需要同步营销云的内部结算关系表，再调用接口工具类
		Set<String> finalIdSet = new HashSet<String>();
		QFilter filter = new QFilter("billstatus", QCP.in, "C");  // 已审核
		filter.and(new QFilter("yd_synfromyxy", QCP.equals, true));  // 是否需要同步营销云
		DataSet settleDataSet = QueryServiceHelper.queryDataSet("jy", "yd_innerrelbill", "id", filter.toArray(), null);
		if (settleDataSet.hasNext()) {
			for (Row settleRow : settleDataSet) {
				finalIdSet.add(settleRow.getString("id"));
			}
		} else {
			throw new KDBizException("系统不存在已设置为需要同步营销云的内部结算关系！");
		}
		
		ApiResult result = PriceMServiceHelper.synPriceFromYXY(finalIdSet);
		if (!result.getSuccess()) {
			if (result.getMessage() != null) {
				throw new KDBizException(result.getMessage());
			} else {
				throw new KDBizException("执行失败，java.lang.NullPointerException！");
			}
		}
	}

}
