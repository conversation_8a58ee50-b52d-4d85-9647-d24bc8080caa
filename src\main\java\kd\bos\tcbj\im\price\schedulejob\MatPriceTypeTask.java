package kd.bos.tcbj.im.price.schedulejob;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.im.price.schedulejob.MatPriceTypeTask
 * @className MatPriceTypeTask
 * @author: hst
 * @createDate: 2024/06/14
 * @description: 物料计价信息中间表同步组织间交易价格调度任务
 * @version: v1.0
 */
public class MatPriceTypeTask extends AbstractTask {

    private static final Log log = LogFactory.getLog(MatPriceTypeTask.class);
    private final static String UPDATE_PRICE = "update_price";

    /**
     * 执行任务
     * @param requestContext
     * @param map
     */
    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        String key = map.containsKey("key") ? map.get("key").toString() : "";
        switch (key) {
            case UPDATE_PRICE : {
                String saleOrgId = map.get("saleOrgId").toString();
                String purOrgId = map.get("purOrgId").toString();
                this.synPriceFromMatPrice(saleOrgId,purOrgId);
                break;
            }
            default : {}
        }
    }

    /**
     * 同步组织间交易价格
     * @param saleOrgId
     * @param purOrgId
     */
    private void synPriceFromMatPrice (String saleOrgId, String purOrgId) {
        Set<String> saleOrgIds = new HashSet<>();
        Set<String> purOrgIds = new HashSet<>();

        if (StringUtils.isNotBlank(saleOrgId)) {
            saleOrgIds.add(saleOrgId);
        }

        if (StringUtils.isNotBlank(purOrgId)) {
            purOrgIds.add(purOrgId);
        }

        this.synPriceFromMatPrice(saleOrgIds,purOrgIds);
    }

    /**
     * 同步组织间交易价格
     * @author: hst
     * @createDate: 2024/06/14
     */
    private void synPriceFromMatPrice (Set<String> saleOrgIds, Set<String> purOrgIds) {
        // 任务执行完毕，生成执行结果输出
        HashMap<String, Object> result = new HashMap<>();
        result.put("success", "true");

        // 任务开始，输出当前进度及提示
        feedbackProgress(0, "已经进入任务执行环节，开始执行任务", null);

        Map<String,Map<String, BigDecimal>> priceMap = new HashMap<String,Map<String,BigDecimal>>();
        StringBuffer errStr = new StringBuffer();

        // 获取获取内部交易价同步设置
        Set<String> orgSet = PriceMServiceHelper.getInterPriceSetting(saleOrgIds,purOrgIds);
        List<String> orgIds = orgSet.stream().map(key -> key.split("&")[0]).distinct().collect(Collectors.toList());

        try {
            int progress = 0;
            int time = orgIds.size();
            for (int i = 0; i < orgIds.size(); i++) {

                // 反馈进度
                String desc = String.format("开始进行第  %s / %s 次循环", i+1, time);
                progress = (100 * i) / time;
                feedbackProgress(progress, desc, null);

                // 判断前端是否下达了终止任务的指令
                if (isStop()) {
                    stop();
                }

                String saleOrgId = orgIds.get(i);
                priceMap = new HashMap<>();

                DataSet dataSet = this.getMaterialPriceInfo(saleOrgId);

                for (Row row : dataSet) {
                    String matId = row.getString("yd_matId");
                    BigDecimal price = row.getBigDecimal("yd_focusprice");
                    if (StringUtils.isNotBlank(matId)) {
                        if (priceMap.containsKey(saleOrgId)) {
                            // 将中间表中的价格数据解析成一个物料-价格的map
                            Map<String, BigDecimal> matPriceMap = priceMap.get(saleOrgId);
                            matPriceMap.put(matId, price);
                            priceMap.put(saleOrgId, matPriceMap);
                        } else {
                            Map<String, BigDecimal> matPriceMap = new HashMap<String, BigDecimal>();
                            matPriceMap.put(matId, price);
                            priceMap.put(saleOrgId, matPriceMap);
                        }
                    }
                }

                for (String key : orgSet) {
                    if (key.contains(saleOrgId + "&")) {
                        String purOrgId = key.split("&")[1];
                        Map<String, BigDecimal> matPriceMap = new HashMap<>();

                        if (priceMap.containsKey(saleOrgId)) {
                            matPriceMap.putAll(priceMap.get(saleOrgId));

                            // 对比物料价格是否发生改变并更新价格表，抽象出公共方法，传Map进去
                            if (matPriceMap.size() > 0) {
                                ApiResult apiResult = PriceMServiceHelper.updateOrgPrice(saleOrgId, purOrgId, matPriceMap);
                                if (!apiResult.getSuccess()) {
                                    errStr.append(apiResult.getMessage() + "\n");
                                }
                            }
                        }
                    }
                }
            }

            if (errStr.length() > 0) {
                result.put("success", "false");
                result.put("errMsg",errStr.toString());
            }
        } catch (Exception e) {
            result.put("success", "false");
            result.put("errMsg",e.getMessage());
        }

        // 输出定制结果
        feedbackCustomdata(result);
    }

    /**
     * 获取物料计价信息
     * @return
     * @author: hst
     * @createDate: 2024/09/13
     */
    private DataSet getMaterialPriceInfo (String saleOrgId) {
        return QueryServiceHelper.queryDataSet("synPriceFromMatPrice",
                "yd_matpricemid", "yd_material.id yd_matId, yd_material.yd_basedatafield.id yd_brandId," +
                        "yd_focusprice,yd_pricetop,yd_saleorg", new QFilter[]{new QFilter("yd_saleorg.id", QFilter.equals, saleOrgId),
                        new QFilter("yd_material", QFilter.is_notnull, null)}, null);
    }
}
