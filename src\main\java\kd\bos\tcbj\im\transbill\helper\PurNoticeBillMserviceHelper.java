package kd.bos.tcbj.im.transbill.helper;

import java.util.Set;

import json.JSONObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.tcbj.im.transbill.imp.PurNoticeBillMserviceImpl;
import kd.bos.tcbj.im.transbill.mservice.PurNoticeBillMservice;

/**
 * 描述：采购通知单工具类
 * PurNoticeBillMserviceHelper.java
 * 
 * @createDate  : 2021-12-08
 * @createAuthor: 严祖威
 * @updateDate  : 
 * @updateAuthor: 
 */
public class PurNoticeBillMserviceHelper {
	
	/**
	 * 描述：查询E3采购通知单功能
	 * 
	 * @createDate  : 2020-09-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public static ApiResult getPurNoticeBill(JSONObject params) {
		PurNoticeBillMservice mservice = PurNoticeBillMserviceImpl.getInstance();
		return mservice.getPurNoticeBill(params);
	}
	
	/**
	 * 描述：查询E3采购退货通知单功能
	 * 
	 * @createDate  : 2021-12-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public static ApiResult getPurReturnNoticeBill(JSONObject params) {
		PurNoticeBillMservice mservice = PurNoticeBillMserviceImpl.getInstance();
		return mservice.getPurReturnNoticeBill(params);
	}
	
	/**
	 * 描述：刷新采购通知单功能
	 * 
	 * @createDate  : 2021-12-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public static ApiResult refreshBill(Set<String> idSet) {
		PurNoticeBillMservice mservice = PurNoticeBillMserviceImpl.getInstance();
		return mservice.refreshBill(idSet);
	}
	
	/**
	 * 描述：刷新采购通知单功能
	 * 
	 * @createDate  : 2021-12-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public static ApiResult pushToTransBill(Set<String> idSet) {
		PurNoticeBillMservice mservice = PurNoticeBillMserviceImpl.getInstance();
		return mservice.pushToTransBill(idSet);
	}
}
