package kd.bos.tcbj.im.transbill.imp;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import json.JSONObject;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.transbill.helper.PurNoticeBillMserviceHelper;
import kd.bos.tcbj.im.transbill.mservice.PurNoticeBillMservice;
import kd.bos.tcbj.im.transbill.service.E3Service;

public class PurNoticeBillMserviceImpl implements PurNoticeBillMservice {
	
	/**
	 * 内部实例化
	 */
	private final static PurNoticeBillMserviceImpl purNoticeBillMserviceImpl = new PurNoticeBillMserviceImpl();
	
	/**
	 * (构造函数：私有化的采购通知单内部接口实现类)
	 * 
	 * @createDate  : 2021-12-08
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	public PurNoticeBillMserviceImpl() {
		
	}
	
	/**
	 * 描述：获取采购通知单内部接口实现类实例
	 * 
	 * @createDate  : 2021-12-08
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @return 采购通知单内部接口实现类
	 */
	public static PurNoticeBillMservice getInstance() {
		return purNoticeBillMserviceImpl;
	}

	/**
	 * 描述：查询E3采购通知单功能
	 * 
	 * @createDate  : 2020-09-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	@Override
	public ApiResult getPurNoticeBill(JSONObject params) {
		System.out.println(111);
		
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			E3Service e3Service=new E3Service();
			e3Service.getPurNoticeData(params);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：查询E3采购退货通知单功能
	 * 
	 * @createDate  : 2021-12-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	@Override
	public ApiResult getPurReturnNoticeBill(JSONObject params) {
		System.out.println(222);
		
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			E3Service e3Service=new E3Service();
			e3Service.getOrderRetNoticeData(params);
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：刷新单据数据
	 * 
	 * @createDate  : 2021-12-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	@Override
	public ApiResult refreshBill(Set<String> idSet) {
		System.out.println(333);
		
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		try {
			for(String billId : idSet) {
				DynamicObject oriPurNoticeBill = BusinessDataServiceHelper.loadSingle(billId, "yd_purnoticebill");
				// 已有下游单据的不处理
				if (StringUtils.isNotEmpty(oriPurNoticeBill.getString("yd_targetbillno"))) {
					continue;
				}
				oriPurNoticeBill.set("yd_failreason", "");
				oriPurNoticeBill.set("yd_nowarehouse", false);
				oriPurNoticeBill.set("yd_nomaterial", false);
				
				StringBuffer errorMsg = new StringBuffer();
				String e3WsNo = oriPurNoticeBill.getString("yd_e3warehousecode");
				// 根据仓库获取仓库F7
				QFilter wsMapFilter = new QFilter("yd_combofield_pt", QCP.equals, 1);
				wsMapFilter.and(new QFilter("billstatus", QCP.equals, "C"));
				wsMapFilter.and(new QFilter("yd_entryentity.yd_textfield", QCP.equals, e3WsNo));
				DynamicObjectCollection wsMapCol = QueryServiceHelper.query("yd_ckdygx", 
						"yd_entryentity.yd_textfield e3num,yd_entryentity.yd_basedatafield.id warehouseid ", wsMapFilter.toArray());
				if (wsMapCol.size() == 0) {
					errorMsg.append("E3仓库编码"+e3WsNo+"缺少仓库映射关系！");
					oriPurNoticeBill.set("yd_nowarehouse", true);
				}
				
				// 如果是正向则根据调入仓库带出调出仓库，退货则根据调出仓库带出调入仓库
				Boolean isReturn = oriPurNoticeBill.getBoolean("yd_bepurreturn");
				if (!isReturn) {
					if (wsMapCol.size() > 0) {
						oriPurNoticeBill.set("yd_nowarehouse", false);
						String inWsId = wsMapCol.get(0).getString("warehouseid");
						oriPurNoticeBill.set("yd_inwarehouse", BusinessDataServiceHelper.loadSingle(inWsId, "bd_warehouse"));
						// 带出调出仓库
						QFilter wsTransMapFilter = new QFilter("billstatus", QCP.equals, "C");
						wsTransMapFilter.and(new QFilter("entryentity.yd_inwarehouse.id", QCP.equals, inWsId));
						DynamicObjectCollection wsTransMapCol = QueryServiceHelper.query("yd_transferrelbill", 
								"entryentity.yd_outwarehouse.id outWsId", wsTransMapFilter.toArray());
						if (wsTransMapCol.size() > 0) {
							String outWsId = wsTransMapCol.get(0).getString("outWsId");
							oriPurNoticeBill.set("yd_outwarehouse", BusinessDataServiceHelper.loadSingle(outWsId, "bd_warehouse"));  // 调出仓库
							oriPurNoticeBill.set("yd_nowarehouse", false);
						} else {
							oriPurNoticeBill.set("yd_nowarehouse", true);
							errorMsg.append("E3调入仓库编码"+e3WsNo+"缺少调出仓库调拨对应关系！");
						}
					}
				} else {
					if (wsMapCol.size() > 0) {
						String outWsId = wsMapCol.get(0).getString("warehouseid");
						oriPurNoticeBill.set("yd_outwarehouse", BusinessDataServiceHelper.loadSingle(outWsId, "bd_warehouse"));
						// 获取调拨仓库关系表的调入仓库
						QFilter wsTransMapFilter = new QFilter("billstatus", QCP.equals, "C");
						wsTransMapFilter.and(new QFilter("entryentity.yd_outwarehouse.id", QCP.equals, outWsId));
						DynamicObjectCollection wsTransMapCol = QueryServiceHelper.query("yd_transferrelbill", 
								"entryentity.yd_inwarehouse.id inWsId", wsTransMapFilter.toArray());
						if (wsTransMapCol.size() > 0) {
							String inWsId = wsTransMapCol.get(0).getString("inWsId");
							oriPurNoticeBill.set("yd_inwarehouse", BusinessDataServiceHelper.loadSingle(inWsId, "bd_warehouse"));  // 调入仓库
							oriPurNoticeBill.set("yd_nowarehouse", false);
						} else {
							oriPurNoticeBill.set("yd_nowarehouse", true);
							errorMsg.append("E3调出仓库编码"+e3WsNo+"缺少调入仓库调拨对应关系！");
						}
					}
				}
				
				// 检查仓库是否存在
				if (oriPurNoticeBill.get("yd_inwarehouse") == null || oriPurNoticeBill.get("yd_outwarehouse") == null) {
					oriPurNoticeBill.set("yd_nowarehouse", true);
				}
				
				// 重新查分录物料数据
				DynamicObjectCollection entryCol = oriPurNoticeBill.getDynamicObjectCollection("yd_purnoticeentry");
				for (DynamicObject entryObj : entryCol) {
					String matNum = entryObj.getString("yd_goodsnum");
					// 根据E3的物料对照表查一遍yd_wldygx
					QFilter goodsMapFilter = new QFilter("yd_combofield_pt", QCP.equals, 1);  // E3平台
					goodsMapFilter.and(new QFilter("billstatus", QCP.equals, "C"));
					goodsMapFilter.and(new QFilter("yd_entryentity.yd_textfield", QCP.equals, matNum));
					DynamicObjectCollection goodsMapCol = QueryServiceHelper.query("yd_wldygx", 
							"yd_entryentity.yd_textfield e3num,yd_entryentity.yd_basedatafield.id materialid", goodsMapFilter.toArray());
					if (goodsMapCol.size() > 0) {
						String materialid = goodsMapCol.get(0).getString("materialid");
						DynamicObject materialObj = BusinessDataServiceHelper.loadSingle(materialid, "bd_material");
						entryObj.set("yd_material", materialObj);
						entryObj.set("yd_unit", materialObj.getDynamicObject("baseunit"));
						oriPurNoticeBill.set("yd_nomaterial", false);
					} else {
						// 根据助记码再查一遍
						QFilter matFilter = new QFilter("status", QCP.equals, "C");
						matFilter.and(new QFilter("helpcode", QCP.equals, matNum));
						DynamicObjectCollection matCol = QueryServiceHelper.query("bd_material", "id,number", matFilter.toArray());
						if (matCol.size() == 1) {
							String materialid = matCol.get(0).getString("id");
							DynamicObject materialObj = BusinessDataServiceHelper.loadSingle(materialid, "bd_material");
							entryObj.set("yd_material", materialObj);
							entryObj.set("yd_unit", materialObj.getDynamicObject("baseunit"));
							oriPurNoticeBill.set("yd_nomaterial", false);
						} else if (matCol.size() == 0) {
							oriPurNoticeBill.set("yd_nomaterial", true);
							errorMsg.append("E3物料编码"+matNum+"缺少物料对应关系！");
						} else if (matCol.size() > 1) {
							oriPurNoticeBill.set("yd_matrepeat", true);
							errorMsg.append("E3物料助记码"+matNum+"存在对应多个物料！");
						}
//						oriPurNoticeBill.set("yd_nomaterial", true);
//						errorMsg.append("E3物料编码"+matNum+"缺少物料对应关系！");
					}
				}
				
				// 检查是否都带出了物料
				for (DynamicObject entryObj : entryCol) {
					if (entryObj.get("yd_material") == null) {
						oriPurNoticeBill.set("yd_nomaterial", true);
						break;
					}
				}
				
				oriPurNoticeBill.set("yd_failreason", errorMsg.toString());
				SaveServiceHelper.save(new DynamicObject[] {oriPurNoticeBill});  // 保存
			}
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	/**
	 * 描述：下推直接调拨单
	 * 
	 * @createDate  : 2021-12-30
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param idSet 单据ID集合
	 * @return 是否成功
	 */
	@Override
	public ApiResult pushToTransBill(Set<String> oriIdSet) {
		System.out.println(444);
		ApiResult apiResult =new ApiResult();
		apiResult.setSuccess(true);
		DynamicObject failPurNoticeBill = null;
		try {
			//下游单号 为空 and 仓库不存在 等于 否 and 物料不存在 等于 否 and 调出仓库.名称 不为空 and 调入仓库.名称 不为空
			QFilter billFilters = QFilter.of("yd_targetbillno=?", "");
			billFilters.and(QFilter.of("billstatus=?", "C"));
			
			billFilters.and(new QFilter("id", QCP.in, oriIdSet));
			DynamicObject[] purNoticeBills = BusinessDataServiceHelper.load("yd_purnoticebill", "id,yd_purnoticeentry.id", billFilters.toArray());
			if (purNoticeBills.length == 0) {
				throw new KDException("没有满足条件（已审核，下游单号为空）的采购通知单数据！");
			}
			
			Set<String> idSet = new HashSet<String>();
			// 重新带出采购通知单上的信息：调出仓库、调入仓库、分录物料、分录计量单位
			for(DynamicObject oriPurNoticeBill : purNoticeBills) {
				idSet.add(oriPurNoticeBill.getPkValue().toString());
			}
			
			// 刷新单据数据
			PurNoticeBillMserviceHelper.refreshBill(idSet);
			
			billFilters.and(new QFilter("yd_nowarehouse", QCP.equals, false));  // 仓库不存在 等于 否
			billFilters.and(new QFilter("yd_nomaterial", QCP.equals, false));  // 物料不存在 等于 否
			billFilters.and(QFilter.of("yd_outwarehouse is not null"));  // 调出仓库不能为空
			billFilters.and(QFilter.of("yd_inwarehouse is not null"));  // 调入仓库不能为空
			purNoticeBills = BusinessDataServiceHelper.load("yd_purnoticebill", "id,yd_purnoticeentry.id", billFilters.toArray());
			if (purNoticeBills.length == 0) {
				throw new KDException("没有满足条件的采购通知单数据！");
			}
			
			// 构建下推参数
			PushArgs pushArgs = new PushArgs();
			pushArgs.setSourceEntityNumber("yd_purnoticebill");  // 源单标志
			pushArgs.setTargetEntityNumber("im_transdirbill");  // 目标单标志
			pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告
			pushArgs.setRuleId("1305859001497434112");  // 固定下推规则
			
			//需要下推的单据
	        for (DynamicObject purNoticeBill : purNoticeBills) {
	        	purNoticeBill = BusinessDataServiceHelper.loadSingle(purNoticeBill.getPkValue(), "yd_purnoticebill");
	        	failPurNoticeBill = purNoticeBill;
	        	List<ListSelectedRow> selectedRows = new ArrayList<>();
	        	ListSelectedRow srcBill = new ListSelectedRow(purNoticeBill.getPkValue());
	        	selectedRows.add(srcBill);
	        	pushArgs.setSelectedRows(selectedRows);
	        	//调用下推引擎，下推目标单
	        	ConvertOperationResult pushResult = ConvertServiceHelper.push(pushArgs);
	        	
	        	//判断下推是否成功，如果失败，提炼失败消息
	        	if(!pushResult.isSuccess()){
	        		//错误摘要
	        		String errMessage = pushResult.getMessage();
	        		purNoticeBill.set("yd_failreason", "下推库存调拨单失败" + errMessage);
	        		SaveServiceHelper.save(new DynamicObject[] {purNoticeBill});  // 保存
	        		continue;
//	        		throw new KDException("下推库存调拨单失败" + errMessage);
	        	}
	        	
	        	//获取生成的目标单数据包
	        	MainEntityType targetMainType = EntityMetadataCache.getDataEntityType("im_transdirbill");
	        	List<DynamicObject> targetBillObjs = pushResult.loadTargetDataObjects(new IRefrencedataProvider() {
	        		@Override
	        		public void fillReferenceData(Object[] objs, IDataEntityType dType) {
	        			BusinessDataReader.loadRefence(objs, dType);
	        		}
	        	}, targetMainType);
	        	
	        	// 保存目标单据
	        	OperationResult saveResult = SaveServiceHelper.saveOperate("im_transdirbill", targetBillObjs.toArray(new DynamicObject[]{}), OperateOption.create());
	        	if (!saveResult.isSuccess()) {
	        		// 错误摘要
	        		String errMessage = saveResult.getMessage();
	        		purNoticeBill.set("yd_failreason", "保存库存调拨单失败！");
	        		SaveServiceHelper.save(new DynamicObject[] {purNoticeBill});  // 保存
	        		continue;
//	        		throw new KDException("保存库存调拨单失败：" + errMessage);
	        	}
	        	
	        	// 提交目标单据
	        	String pk = saveResult.getSuccessPkIds().get(0).toString();
	        	QFilter qFilter1 = new QFilter("id", QCP.equals, pk);
	        	Object billNo = BusinessDataServiceHelper.loadSingle("im_transdirbill", "id,billno", qFilter1.toArray()).get("billno");
	        	OperationResult submitResult = OperationServiceHelper.executeOperate("submit", "im_transdirbill", new Object[]{pk}, OperateOption.create());
	        	if (!submitResult.isSuccess()) {
	        		// 错误摘要
	        		String errMessage = submitResult.getMessage();
	        		purNoticeBill.set("yd_failreason", "库存调拨单" + billNo + "生成成功但提交失败！");
	        		SaveServiceHelper.save(new DynamicObject[] {purNoticeBill});  // 保存
	        		continue;
//	        		throw new KDException("库存调拨单" + billNo + "生成成功，但提交失败，请到库存调拨单手动提交，查看提交失败原因。"+submitResult.getMessage());
	        	}
			}
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
//    		SaveServiceHelper.save(new DynamicObject[] {failPurNoticeBill});  // 保存
		}
		return apiResult;
	}

}
