package kd.bos.tcbj.im.transbill.mservice;

import java.util.Set;

import json.JSONObject;
import kd.bos.entity.api.ApiResult;

/**
 * 描述：采购通知单接口类
 * PurNoticeBillMservice.java
 * 
 * @createDate  : 2021-12-08
 * @createAuthor: 严祖威
 * @updateDate  : 
 * @updateAuthor: 
 */
public interface PurNoticeBillMservice {
	
	/**
	 * 描述：查询E3采购通知单功能
	 * 
	 * @createDate  : 2020-09-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public ApiResult getPurNoticeBill(JSONObject params);
	
	/**
	 * 描述：查询E3采购退货通知单功能
	 * 
	 * @createDate  : 2021-12-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public ApiResult getPurReturnNoticeBill(JSONObject params);
	
	/**
	 * 描述：刷新单据数据
	 * 
	 * @createDate  : 2021-12-08
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param params 接口参数
	 * @return 是否成功
	 */
	public ApiResult refreshBill(Set<String> idSet);
	
	/**
	 * 描述：下推直接调拨单
	 * 
	 * @createDate  : 2021-12-30
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param idSet 单据ID集合
	 * @return 是否成功
	 */
	public ApiResult pushToTransBill(Set<String> idSet);
	
}
