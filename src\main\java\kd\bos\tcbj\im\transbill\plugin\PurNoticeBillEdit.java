package kd.bos.tcbj.im.transbill.plugin;

import java.util.HashSet;
import java.util.Set;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.exception.KDBizException;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.list.IListView;
import kd.bos.tcbj.im.transbill.helper.PurNoticeBillMserviceHelper;

/**
* @auditor yanzuwei
* @date 2021年12月30日
* 
*/
public class PurNoticeBillEdit extends AbstractBillPlugIn {
	/** 下推直接调拨单按钮名称 */
	private final static String PUSHTOTRANSBILL_BOTTON = "yd_pushToTransbill";
	
	/**
	 * 描述：菜单栏点击事件
	 * 1.获取E3采购通知单功能：调用E3采购通知单接口获取数据
	 * 2.获取E3采购退货通知单功能：调用E3采购退货通知单接口获取数据
	 * 
	 * <AUTHOR>
	 * @createDate 2021-12-08
	 * @param e 菜单栏点击事件
	 */
	@Override
	public void itemClick(ItemClickEvent evt) {
		String itemKey=evt.getItemKey();
		// 下推生成直接调拨单
		if (StringUtil.equalsIgnoreCase(PUSHTOTRANSBILL_BOTTON, itemKey)) {
			pushToTransbill();
		}
	}
	
	/**
	 * 下推直接调拨单并提交
	 */
	private void pushToTransbill() {
		// 获取勾选的数据
		Set<String> oriIdSet = new HashSet<String>();
		String billId = this.getModel().getDataEntity().getPkValue().toString();
		if (StringUtil.isEmptyString(billId)) {
			this.getView().showMessage("请保存并审核单据！");
			return;
		}
		
		oriIdSet.add(billId);
		ApiResult result = PurNoticeBillMserviceHelper.pushToTransBill(oriIdSet);
		if (result.getSuccess()) {
			this.getView().showMessage("执行完成，详情请查看单据数据！");
		} else {
			this.getView().showErrorNotification(result.getMessage());
		}
	}
}
