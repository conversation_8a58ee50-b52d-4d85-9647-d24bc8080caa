package kd.bos.tcbj.im.transbill.plugin;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.operate.FormOperate;
import kd.bos.list.IListView;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.transbill.helper.PurNoticeBillMserviceHelper;
import kd.scmc.im.enums.DiscountTypeEnum;

/**
 * 描述：采购通知单列表插件类
 * PurNoticeBillList.java
 * 
 * @createDate  : 2021-12-08
 * @createAuthor: 严祖威
 * @updateDate  : 
 * @updateAuthor: 
 */
public class PurNoticeBillList extends AbstractListPlugin {
	
	private final static SimpleDateFormat DATE_SDF =new SimpleDateFormat("yyyy-MM-dd");
	
	/** 获取E3采购通知单按钮名称 */
	private final static String GETPURNOTICEBILL_BOTTON = "yd_getPurNoticeBill";
	/** 获取E3采购退货通知单按钮名称 */
	private final static String GETPURRETURNNOTICEBILL_BOTTON = "yd_getPurReturnNoticeBill";
	/** 下推直接调拨单按钮名称 */
	private final static String PUSHTOTRANSBILL_BOTTON = "yd_pushToTransbill";
	
	/** 参数选择页面关闭事件ID */
	private final static String CALLBACKID_SELECTPARAMS = "closeCallBack_selectParams";
	
	/** 参数选择页面标志 */
	private final static String SELECTPARAMSFORM_ID = "yd_purnoticeparamselect";
	
	/**
	 * 描述：菜单栏点击事件
	 * 1.获取E3采购通知单功能：调用E3采购通知单接口获取数据
	 * 2.获取E3采购退货通知单功能：调用E3采购退货通知单接口获取数据
	 * 
	 * <AUTHOR>
	 * @createDate 2021-12-08
	 * @param e 菜单栏点击事件
	 */
	@Override
	public void itemClick(ItemClickEvent evt) {
		String itemKey=evt.getItemKey();
		// 获取E3采购通知单功能
		if(StringUtil.equalsIgnoreCase(GETPURNOTICEBILL_BOTTON, itemKey)) {
			getPurNoticeBill(false);
			this.getView().invokeOperation("refresh");
		}
		
		// 获取E3采购退货通知单功能
		if (StringUtil.equalsIgnoreCase(GETPURRETURNNOTICEBILL_BOTTON, itemKey)) {
			getPurNoticeBill(true);
			this.getView().invokeOperation("refresh");
		}
		
		// 销售出库单生成销售出库单功能测试
		if (StringUtil.equals("yd_testfunction", itemKey)) {
			QFilter filter = QFilter.of("billno=?", "XSCK-211214-000003");  // 测试单号
			
			DataSet saleBillSet = QueryServiceHelper.queryDataSet("SaleOutBillScheduleTask", "im_saloutbill", "id,billno",
					new QFilter[] { filter }, null);
			for (Row row : saleBillSet) {
				// 销售出库单ID
				String saleoutBillId = row.getString("id");
				System.out.println(saleoutBillId);
			}
		}
		
		// 下推生成直接调拨单
		if (StringUtil.equalsIgnoreCase(PUSHTOTRANSBILL_BOTTON, itemKey)) {
			pushToTransbill();
			this.getView().invokeOperation("refresh");
		}
	}

	/**
	 * 下推直接调拨单并提交
	 */
	private void pushToTransbill() {
		// 获取勾选的数据
		ListSelectedRowCollection selectedData = ((IListView)this.getView()).getSelectedRows(); 
		Set<String> oriIdSet = new HashSet<String>();
		for (ListSelectedRow row : selectedData) {
			Object pk = row.getPrimaryKeyValue();
			oriIdSet.add(pk.toString());
		}
		
		if(oriIdSet.size() == 0) {
			throw new KDBizException("请选择数据！");
		}
		
		ApiResult result = PurNoticeBillMserviceHelper.pushToTransBill(oriIdSet);
		if (result.getSuccess()) {
			this.getView().showMessage("执行完成，详情请查看单据数据！");
		} else {
			this.getView().showErrorNotification(result.getMessage());
		}
	}

	@Override
	public void beforeDoOperation(BeforeDoOperationEventArgs event) {
		super.beforeDoOperation(event);
		
		FormOperate operate = (FormOperate) event.getSource();
		if(StringUtil.equals("push", operate.getOperateKey())) {
			ListSelectedRowCollection selectedData = event.getListSelectedData();
			Set<String> idSet = new HashSet<String>();
			for (ListSelectedRow row : selectedData) {
				Object pk = row.getPrimaryKeyValue();
				idSet.add(pk.toString());
			}
			// 刷新单据数据
			PurNoticeBillMserviceHelper.refreshBill(idSet);
		}
	}

	/**
	 * 描述：获取E3采购通知单功能
	 * 弹出参数选择界面，选择日期（不可为空，默认当天），填写单号（可为空）
	 * 
	 * @createDate  : 2021-12-08
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param isReturnGoods 是否是退货，如果是退货则调用退货接口，如果不是退货则调用通知接口
	 */
	private void getPurNoticeBill(Boolean isReturnGoods) {
		// 弹出选择查询参数的动态表单页面
		FormShowParameter showParameter = new FormShowParameter();
		showParameter.setFormId(SELECTPARAMSFORM_ID);
		showParameter.setCloseCallBack(new CloseCallBack(this, CALLBACKID_SELECTPARAMS));
		showParameter.getOpenStyle().setShowType(ShowType.Modal);
		showParameter.setCustomParam("isReturnGoods", isReturnGoods);
		this.getView().showForm(showParameter);
	}
	
	/**
	 * 描述：子界面关闭时，触发父界面的closedCallBack事件
	 * 1.选择查询日期和查询单号后获取填写的数据，然后执行调用E3接口功能
	 * 
	 * @createDate  : 2021-12-08
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param evt 采购通知单列表界面的closedCallBack事件
	 */
	@Override
	public void closedCallBack(ClosedCallBackEvent evt) {
		if (StringUtil.equals(CALLBACKID_SELECTPARAMS, evt.getActionId())){  // 关闭参数选择界面后返回
			if (evt.getReturnData() != null) {
				Map<String, Object> searchData = (Map<String, Object>) evt.getReturnData();
				Boolean isReturnGoods = (Boolean) searchData.get("isReturnGoods");
				Date searchDate = (Date) searchData.get("searchDate");
				String billNo = searchData.get("billNo").toString();
				
				JSONObject postJson=new JSONObject();
				postJson.put("rq_start", DATE_SDF.format(searchDate)+" 00:00:00");
				postJson.put("rq_end", DATE_SDF.format(searchDate)+" 23:59:59");
				if (!StringUtil.isEmptyString(billNo)) {
					postJson.put("cgjrd_djbh", billNo);
				}
				// 调用统一方法进行获取
				ApiResult apiResult = null;
				if (!isReturnGoods) {
					apiResult = PurNoticeBillMserviceHelper.getPurNoticeBill(postJson);
				} else {
					apiResult = PurNoticeBillMserviceHelper.getPurReturnNoticeBill(postJson);
				}
				if (!apiResult.getSuccess()) {
					this.getView().showMessage(apiResult.getMessage());
				} else {
					this.getView().showMessage("数据获取完成，请查看列表数据详情！");
				}
				
			}
		}
		this.getView().invokeOperation("refresh");
	}
	
}
