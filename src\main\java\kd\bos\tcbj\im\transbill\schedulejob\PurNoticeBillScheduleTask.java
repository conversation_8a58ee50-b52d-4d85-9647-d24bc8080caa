package kd.bos.tcbj.im.transbill.schedulejob;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang3.time.DateFormatUtils;

import json.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.im.transbill.helper.PurNoticeBillMserviceHelper;

/**
 * 描述：采购通知单获取定时事务处理类
 * PurNoticeBillScheduleTask.java
 * 
 * @createDate  : 2021-12-09
 * @createAuthor: 严祖威
 * @updateDate  : 
 * @updateAuthor: 
 */
public class PurNoticeBillScheduleTask extends AbstractTask {

	/**
	 * 描述：每30分钟执行一次，获取当天的单据数据，因为E3接口不支持按小时查
	 * 
	 * @createDate  : 20201-12-09
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	@Override
	public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
		Date now = Calendar.getInstance().getTime();
		String today = DateFormatUtils.format(now, "yyyy-MM-dd");
		JSONObject postJson=new JSONObject();
		postJson.put("rq_start", today+" 00:00:00");
		postJson.put("rq_end", today+" 23:59:59");
		
		// 判断要获取的单据类型，purNoticeBill-采购通知单，purReturnNoticeBill-采购退货通知单
		String bePurReturn = params.get("billType").toString();
		if ("purNoticeBill".equals(bePurReturn)) {
			PurNoticeBillMserviceHelper.getPurNoticeBill(postJson);
		} else if ("purReturnNoticeBill".equals(bePurReturn)) {
			PurNoticeBillMserviceHelper.getPurReturnNoticeBill(postJson);
		}
		
		// 记录日志
		
	}

}
