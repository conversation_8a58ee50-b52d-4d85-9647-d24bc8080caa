package kd.bos.tcbj.im.transbill.schedulejob;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.time.DateFormatUtils;

import kd.bos.context.RequestContext;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.transbill.helper.PurNoticeBillMserviceHelper;

/**
 * 描述：采购通知单生成直接调拨单定时事务处理类
 * PurNoticeToTransBillScheduleTask.java
 * 
 * @createDate  : 2021-12-09
 * @createAuthor: 严祖威
 * @updateDate  : 
 * @updateAuthor: 
 */
public class PurNoticeToTransBillScheduleTask extends AbstractTask {
	/**
	 * 描述：每20分钟执行一次，获取两个月内还未生成库存调拨单的采购通知单
	 * 下游单号yd_targetbillno为空且仓库不存在yd_nowarehouse为否且物料不存在yd_nomaterial为否
	 * 
	 * @createDate  : 20201-12-09
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	@Override
	public void execute(RequestContext ctx, Map<String, Object> params) throws KDException {
		// 源单据数据集
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.set(5, 1);
		cal.add(2, -1);
		Date firstDate = cal.getTime();
		String firstDateStr = DateFormatUtils.format(firstDate, "yyyy-MM-dd");
		QFilter billFilters = QFilter.of("yd_targetbillno=?", "");
		billFilters.and(QFilter.of("billstatus=?", "C"));
		billFilters.and(new QFilter("yd_bizdate", QCP.large_equals, firstDateStr));
//		billFilters.and(new QFilter("yd_nowarehouse", QCP.equals, false));
//		billFilters.and(new QFilter("yd_nomaterial", QCP.equals, false));
		DynamicObject[] purNoticeBills = BusinessDataServiceHelper.load("yd_purnoticebill", "id,yd_purnoticeentry.id", billFilters.toArray());
		if (purNoticeBills.length == 0) {
			throw new KDException("没有满足条件的采购通知单数据！");
		}
		
		DynamicObjectCollection finalPurNoticeCol = new DynamicObjectCollection();
		Set<String> idSet = new HashSet<String>();
		// 重新带出采购通知单上的信息：调出仓库、调入仓库、分录物料、分录计量单位
		for(DynamicObject oriPurNoticeBill : purNoticeBills) {
			idSet.add(oriPurNoticeBill.getPkValue().toString());
			finalPurNoticeCol.add(oriPurNoticeBill);
		}
		
		PurNoticeBillMserviceHelper.pushToTransBill(idSet);
	}
}
