package kd.bos.tcbj.im.transbill.service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import json.JSONArray;
import json.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

public class E3Service 
{
	private static int pageSize=100;//每页条数
	
	private static String ip="http://*************/e3/webopm/web/";
	private static String path="/webopm/web/?app_act=api/ec&app_mode=func"; 
	
	private static String key = "BSAKeqbv9Rd5Q2t1eqtL";
	private static String secret = "BSAK3oeNcZz7AXoQ1Tua";
	private static String version = "3.0";
	
	/** 采购通知单单据标识 */
	private static String PURNOTICEBILL_ENTITYNAME = "yd_purnoticebill";
	/** 批发退货单单据标识 */
	private static String WHOLESALERETURNBILL_ENTITYNAME = "yd_wholesalereturnbill";
	/** 批发通知单单据标识 */
	private static String WHOLESALENOTICEBILL_ENTITYNAME = "yd_wholesalenoticebill";

	static
	{
		//初始化参数
		/*
		ip=(String)SystemParamServiceHelper.loadPublicParameterFromCache("TCBJ_E3_IP");
		key=(String)SystemParamServiceHelper.loadPublicParameterFromCache("TCBJ_E3_KEY");
		secret=(String)SystemParamServiceHelper.loadPublicParameterFromCache("TCBJ_E3_SECRET"); 
		*/
	}
	
	// 初始化参数，写在static中，改了参数也会不生效，所以写在构造方法中
	public E3Service() {
		DynamicObject ipObj = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "TCBJ_E3_IP").toArray());
		ip = ipObj.getString("name");
		DynamicObject keyObj = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "TCBJ_E3_KEY").toArray());
		key = keyObj.getString("name");
		DynamicObject secretObj = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "TCBJ_E3_SECRET").toArray());
		secret = secretObj.getString("name");
	}
	 
	/**
	 * @desc 获取采购订单通知，获取采购入库单
	 **/
	public void getPurNoticeData(JSONObject postJson) throws Exception
	{
		String serviceType="cgtzd.get";
//		JSONObject postJson=new JSONObject();
//		postJson.put("rq_start", "20211201");
//		postJson.put("rq_end", "20211214");
		 
		getCommonBillList(postJson,serviceType,"cgtzdListGet",new SaveHandler() {
			public void saveBill(JSONArray billList) throws Exception 
			{
			  saveOrderBill(billList); 
			}
		}); 
		 
	}
	/**
	 * @desc 获取采购退货通知 
	 **/
	public void getOrderRetNoticeData(JSONObject postJson) throws Exception
	{
		String serviceType="cgthtzd.get";
//		JSONObject postJson=new JSONObject(); 
//		postJson.put("rq_start", "20201001");
//		postJson.put("rq_end", "20211204");
		getCommonBillList(postJson,serviceType,"cgthtzdListGet",new SaveHandler() {
			public void saveBill(JSONArray billList) throws Exception 
			{
			  saveOrderRetBill(billList); 
			}
		}); 
	}
	
	 
	/**
	 * @desc 获取批发退货通知
	 **/
     public void getWholeSaleRetNoticeData(JSONObject postJson) throws Exception
	 {
			String serviceType="pfthd.list.get";
//			JSONObject postJson=new JSONObject(); 
//			postJson.put("rq_start", "20220101");
//			postJson.put("rq_end", "20220201");
//			postJson.put("djbh", "PFTHD2019080500001");
			getCommonBillList(postJson,serviceType,"pfthdListGet",new SaveHandler() {
				public void saveBill(JSONArray billList) throws Exception 
				{
				  saveWholeSaleRetBill(billList); 
				}
			}); 
	}
	
     /**
 	 * @desc 获取批发通知单
 	 **/
      public void getWholeSaleNoticeData(JSONObject postJson) throws Exception
 	 {
 			String serviceType="pftzd.get";
// 			JSONObject postJson=new JSONObject(); 
// 			postJson.put("rq_start", "20211201");
// 			postJson.put("rq_end", "20211220");
 			postJson.put("djzt", "16");
// 			postJson.put("djbh", "PFTHD2019080500001");
 			getCommonBillList(postJson,serviceType,"pftzdListGet",new SaveHandler() {
 				@Override
 				public void saveBill(JSONArray billList) throws Exception 
 				{
 				  saveWholeSaleNoticeBill(billList); 
 				}
 			}); 
 	}
    
    
	
	void saveOrderBill(JSONArray billList)
	{//保存才采购订单通知
	 System.out.println("保存按采购订单:"+billList);
	 	List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < billList.size(); i++) {
			JSONObject billObj = billList.getJSONObject(i);
			
			String billNo = billObj.get("djbh").toString();  // 采购通知单单号
			QFilter qFilter = new QFilter("billno", QCP.equals, billNo);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle(PURNOTICEBILL_ENTITYNAME, "billno", new QFilter[] { qFilter });
			if (dObject == null) {
				// 不存在单据则新增
				String bizdateStr = billObj.get("rq").toString();  // 业务日期
				Date bizDate = new Date();
				SimpleDateFormat biaDateFormat = new SimpleDateFormat("yyyy-MM-dd");
				bizDate = new Date(Long.parseLong(bizdateStr) * 1000L);
				
				String E3SourceBillNum = billObj.get("ydjh")==null?"":billObj.get("ydjh").toString();  // E3原单号
				String e3warehouseId = billObj.get("ck_id").toString();  // E3仓库ID
				String e3warehousecode = billObj.get("ck_code").toString();  // E3仓库编码
				
				// E3结算币别
				String settlecurrencyNum = billObj.get("jsbb")==null?"":billObj.get("jsbb").toString();
				QFilter curFilter = new QFilter("number", QCP.equals, settlecurrencyNum);
				curFilter = curFilter.or(new QFilter("number", QCP.equals, "BB01"));
				DynamicObject[] curCol = BusinessDataServiceHelper.load("bd_currency", "number,name", new QFilter[] { curFilter });
				
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject(PURNOTICEBILL_ENTITYNAME);
				// 设置单据属性
				bill.set("billno", billNo); // 单据编号为平台_采购通知单单号
				bill.set("billstatus", "C");  // 单据状态已审核
				bill.set("yd_bizdate", bizDate);  // 业务日期
				Date now = new Date();
				String creatorId=RequestContext.get().getUserId().toString();
				DynamicObject creator=BusinessDataServiceHelper.loadSingle(creatorId, "bos_user","id");
				bill.set("creator", creator);// 创建人
				bill.set("createtime", now);  // 创建时间
				bill.set("modifier", creator);// 修改人
				bill.set("modifytime", now);  // 修改时间
				bill.set("auditor", creator);// 审核人
				bill.set("auditdate", now);  // 审核时间
				
				bill.set("yd_e3sourcebillnum", E3SourceBillNum);  // E3原单号
				bill.set("yd_e3warehouseid", e3warehouseId);  // E3仓库ID
				bill.set("yd_e3warehousecode", e3warehousecode);  // E3仓库编码
				bill.set("yd_bepurreturn", false);  // 是否退货为否
				bill.set("yd_settlecurrencynum", settlecurrencyNum);
				if (curCol.length > 0) {
					bill.set("yd_settlecurrency", curCol[0]);
				}
				
				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_purnoticeentry");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray entryObjList = billObj.getJSONArray("mx_list");
				for (int j = 0; j < entryObjList.size(); j++) {
					DynamicObject entry = new DynamicObject(type);
					// 设置单据体属性
					JSONObject entryObj = entryObjList.getJSONObject(j);
					String goodsId = entryObj.get("goods_id").toString();  // 商品编号
					String goodsNum = entryObj.get("goods_sn").toString();  // 商品货号
					
					String batchNo = entryObj.get("batch_no").toString();  // 批次号
					String createDateStr = entryObj.get("create_date")==null?"":entryObj.get("create_date").toString();  // 生成日期
					Date createDate = bizDate;
					try {
						createDate = biaDateFormat.parse(createDateStr);
					} catch (ParseException e) {
						e.printStackTrace();
					}
					
					String safedays = entryObj.get("safedays").toString();  // 有效期
					String expirationDateStr = entryObj.get("expiration_date")==null?"":entryObj.get("expiration_date").toString();  // 到期日
					Date expirationDate = bizDate;
					try {
						expirationDate = biaDateFormat.parse(expirationDateStr);
					} catch (ParseException e) {
						e.printStackTrace();
					}
					
					String qty = entryObj.get("sl").toString();  // 通知数量
					String price = entryObj.get("dj").toString();  // 单价
					String amt = entryObj.get("je").toString();  // 金额
					entry.set("yd_e3goodsid", goodsId);
					entry.set("yd_goodsnum", goodsNum);
					entry.set("yd_batchno", batchNo);
					entry.set("yd_createdate", createDate);
					entry.set("yd_safedays", safedays);
					entry.set("yd_expirationdate", expirationDate);
					entry.set("yd_qty", qty);
					entry.set("yd_price", price);
					entry.set("yd_amount", amt);
					
					entrys.add(entry);
				}
				
				objs.add(bill);
			}
		}
		
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存采购通知单", "sm", PURNOTICEBILL_ENTITYNAME);
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
	
	
	void saveOrderRetBill(JSONArray billList)
	{//保存采购退货单通知
		System.out.println("保存采购退货:"+billList);
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < billList.size(); i++) {
			JSONObject billObj = billList.getJSONObject(i);
			
			String billNo = billObj.get("djbh").toString();  // 采购通知单单号
			QFilter qFilter = new QFilter("billno", QCP.equals, billNo);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle(PURNOTICEBILL_ENTITYNAME, "billno", new QFilter[] { qFilter });
			if (dObject == null) {
				// 不存在单据则新增
				String bizdateStr = billObj.get("rq").toString();  // 业务日期
				Date bizDate = new Date();
				SimpleDateFormat biaDateFormat = new SimpleDateFormat("yyyy-MM-dd");
				bizDate = new Date(Long.parseLong(bizdateStr) * 1000L);
				
				String E3SourceBillNum = billObj.get("ydjh")==null?"":billObj.get("ydjh").toString();  // E3原单号
				String e3warehouseId = billObj.get("ck_id").toString();  // E3仓库ID
				String e3warehousecode = billObj.get("ck_code").toString();  // E3仓库编码，退货是调出仓库，带出调入仓库
				// E3结算币别
				String settlecurrencyNum = billObj.get("jsbb")==null?"":billObj.get("jsbb").toString(); 
				QFilter curFilter = new QFilter("number", QCP.equals, settlecurrencyNum);
				curFilter = curFilter.or(new QFilter("number", QCP.equals, "BB01"));
				DynamicObject[] curCol = BusinessDataServiceHelper.load("bd_currency", "number,name", new QFilter[] { curFilter });
				
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject(PURNOTICEBILL_ENTITYNAME);
				// 设置单据属性
				bill.set("billno", billNo); // 单据编号为平台_采购通知单单号
				bill.set("billstatus", "C");  // 单据状态已审核
				bill.set("yd_bizdate", bizDate);  // 业务日期
				Date now = new Date();
				String creatorId=RequestContext.get().getUserId().toString();
				DynamicObject creator=BusinessDataServiceHelper.loadSingle(creatorId, "bos_user","id");
				bill.set("creator", creator);// 创建人
				bill.set("createtime", now);  // 创建时间
				bill.set("modifier", creator);// 修改人
				bill.set("modifytime", now);  // 修改时间
				bill.set("auditor", creator);// 审核人
				bill.set("auditdate", now);  // 审核时间
				
				bill.set("yd_e3sourcebillnum", E3SourceBillNum);  // E3原单号
				bill.set("yd_e3warehouseid", e3warehouseId);  // E3仓库ID
				bill.set("yd_e3warehousecode", e3warehousecode);  // E3仓库编码
				bill.set("yd_bepurreturn", true);  // 是否退货为否
				bill.set("yd_settlecurrencynum", settlecurrencyNum);
				if (curCol.length > 0) {
					bill.set("yd_settlecurrency", curCol[0]);
				}
				
				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_purnoticeentry");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray entryObjList = billObj.getJSONArray("mx_list");
				for (int j = 0; j < entryObjList.size(); j++) {
					DynamicObject entry = new DynamicObject(type);
					// 设置单据体属性
					JSONObject entryObj = entryObjList.getJSONObject(j);
					String goodsId = entryObj.get("goods_id").toString();  // 商品编号
					String goodsNum = entryObj.get("goods_sn").toString();  // 商品货号
					
					String batchNo = entryObj.get("batch_no").toString();  // 批次号
					String createDateStr = entryObj.get("create_date")==null?"":entryObj.get("create_date").toString();  // 生成日期
					Date createDate = bizDate;
					try {
						createDate = biaDateFormat.parse(createDateStr);
					} catch (ParseException e) {
						e.printStackTrace();
					}
					
					String safedays = entryObj.get("safedays").toString();  // 有效期
					String expirationDateStr = entryObj.get("expiration_date")==null?"":entryObj.get("expiration_date").toString();  // 到期日
					Date expirationDate = bizDate;
					try {
						expirationDate = biaDateFormat.parse(expirationDateStr);
					} catch (ParseException e) {
						e.printStackTrace();
					}
					
					String qty = entryObj.get("sl").toString();  // 通知数量
					String price = entryObj.get("dj").toString();  // 单价
					String amt = entryObj.get("je").toString();  // 金额
					entry.set("yd_e3goodsid", goodsId);
					entry.set("yd_goodsnum", goodsNum);
					entry.set("yd_batchno", batchNo);
					entry.set("yd_createdate", createDate);
					entry.set("yd_safedays", safedays);
					entry.set("yd_expirationdate", expirationDate);
					entry.set("yd_qty", qty);
					entry.set("yd_price", price);
					entry.set("yd_amount", amt);
					
					entrys.add(entry);
				}
				
				objs.add(bill);
			}
		}
		
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存采购通知单", "sm", PURNOTICEBILL_ENTITYNAME);
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
	
	void saveWholeSaleRetBill(JSONArray billList)
	{//保存批发退货单通知
		System.out.println("保存批发退货:"+billList);
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < billList.size(); i++) {
			JSONObject billObj = billList.getJSONObject(i);
			
			String billNo = billObj.get("djbh").toString();  // 批发退货单单号
			QFilter qFilter = new QFilter("billno", QCP.equals, billNo);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle(WHOLESALERETURNBILL_ENTITYNAME, "billno", new QFilter[] { qFilter });
			if (dObject == null) {
				// 不存在单据则新增
				String channelNo = billObj.getString("qd_code").toString();  // E3渠道编码
				String channelName = billObj.getString("qd_name").toString();  // E3渠道编码
				
				String bizdateStr = billObj.get("rq").toString();  // 业务日期
				Date bizDate = new Date();
				SimpleDateFormat biaDateFormat = new SimpleDateFormat("yyyy-MM-dd");
				bizDate = new Date(Long.parseLong(bizdateStr) * 1000L);
				
				String customerNo = billObj.getString("kh_code");  // E3客户编码
				String customerName = billObj.getString("kh_name");  // E3客户编码
				
				String warehouseNo = billObj.getString("ck_code");  // E3仓库编码
				String warehouseName = billObj.getString("ck_name");  // E3仓库名称

				String noticebillno = billObj.getString("ydjh");  // E3通知单号
				
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject(WHOLESALERETURNBILL_ENTITYNAME);
				// 设置单据属性
				bill.set("billno", billNo); // 单据编号为批发退货单单号
				bill.set("billstatus", "C");  // 单据状态已审核
				bill.set("yd_returndate", bizDate);  // 业务日期
				Date now = new Date();
				String creatorId=RequestContext.get().getUserId().toString();
				DynamicObject creator=BusinessDataServiceHelper.loadSingle(creatorId, "bos_user","id");
				bill.set("creator", creator);// 创建人
				bill.set("createtime", now);  // 创建时间
				bill.set("modifier", creator);// 修改人
				bill.set("modifytime", now);  // 修改时间
				bill.set("auditor", creator);// 审核人
				bill.set("auditdate", now);  // 审核时间
				
				bill.set("yd_channelno", channelNo);  // E3渠道编码
				bill.set("yd_channelname", channelName);  // E3渠道名称
				bill.set("yd_customerno", customerNo);  // E3客户编码
				bill.set("yd_customername", customerName);  // E3客户名称
				bill.set("yd_warehouseno", warehouseNo);  // E3仓库编码
				bill.set("yd_warehousename", warehouseName);  // E3仓库名称
				bill.set("yd_noticebillno", noticebillno);  // E3通知单号
				
				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("entryentity");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray entryObjList = billObj.getJSONArray("mx_list");
				for (int j = 0; j < entryObjList.size(); j++) {
					DynamicObject entry = new DynamicObject(type);
					// 设置单据体属性
					JSONObject entryObj = entryObjList.getJSONObject(j);
					String goodsNum = entryObj.get("sku").toString();  // 商品货号
					
					String qty = entryObj.get("sl").toString();  // 通知数量
					String price = entryObj.get("dj").toString();  // 单价
					String amt = entryObj.get("je").toString();  // 金额
					entry.set("yd_goodsnum", goodsNum);
					entry.set("yd_qty", qty);
					entry.set("yd_price", price);
					entry.set("yd_amount", amt);
					
					entrys.add(entry);
				}
				
				objs.add(bill);
			}
		}
		
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存批发退货单", "sm", WHOLESALERETURNBILL_ENTITYNAME);
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
			
	void saveWholeSaleNoticeBill(JSONArray billList)
	{//保存批发通知单
		System.out.println("保存批发通知:"+billList);
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < billList.size(); i++) {
			JSONObject billObj = billList.getJSONObject(i);
			
			String billNo = billObj.get("djbh").toString();  // 批发通知单单号
			QFilter qFilter = new QFilter("billno", QCP.equals, billNo);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle(WHOLESALENOTICEBILL_ENTITYNAME, "billno", new QFilter[] { qFilter });
			if (dObject == null) {
				// 不存在单据则新增
				try {
					String channelNo = billObj.getString("qd_code").toString();  // E3渠道编码
					String channelName = billObj.getString("qd_name").toString();  // E3渠道编码
					
					String bizdateStr = billObj.get("rq").toString();  // 业务日期
					Date bizDate = new Date();
					SimpleDateFormat biaDateFormat = new SimpleDateFormat("yyyy-MM-dd");
					bizDate = new Date(Long.parseLong(bizdateStr) * 1000L);
					
					String customerNo = billObj.getString("kh_code").toString();  // E3客户编码
					String customerName = billObj.getString("kh_name").toString();  // E3客户编码
					
					String warehouseNo = billObj.get("ck_code").toString();  // E3仓库编码
					String warehouseName = billObj.get("ck_name").toString();  // E3仓库名称
					
					// 创建单据对象
					DynamicObject bill = BusinessDataServiceHelper.newDynamicObject(WHOLESALENOTICEBILL_ENTITYNAME);
					// 设置单据属性
					bill.set("billno", billNo); // 单据编号为批发通知单单号
					bill.set("billstatus", "C");  // 单据状态已审核
					bill.set("yd_bizdate", bizDate);  // 业务日期
					Date now = new Date();
					String creatorId=RequestContext.get().getUserId().toString();
					DynamicObject creator=BusinessDataServiceHelper.loadSingle(creatorId, "bos_user","id");
					bill.set("creator", creator);// 创建人
					bill.set("createtime", now);  // 创建时间
					bill.set("modifier", creator);// 修改人
					bill.set("modifytime", now);  // 修改时间
					bill.set("auditor", creator);// 审核人
					bill.set("auditdate", now);  // 审核时间
					
					bill.set("yd_channelno", channelNo);  // E3渠道编码
					bill.set("yd_channelname", channelName);  // E3渠道名称
					bill.set("yd_customerno", customerNo);  // E3客户编码
					bill.set("yd_customername", customerName);  // E3客户名称
					bill.set("yd_warehouseno", warehouseNo);  // E3仓库编码
					bill.set("yd_warehousename", warehouseName);  // E3仓库名称
					bill.set("yd_noticebillno", billNo); // 单据编号为批发通知单单号
					
					// 获取单据体集合
					DynamicObjectCollection entrys = bill.getDynamicObjectCollection("entryentity");
					// 获取单据体的Type
					DynamicObjectType type = entrys.getDynamicObjectType();
					// 根据Type创建单据体对象
					JSONArray entryObjList = billObj.getJSONArray("mx_list");
					for (int j = 0; j < entryObjList.size(); j++) {
						DynamicObject entry = new DynamicObject(type);
						// 设置单据体属性
						JSONObject entryObj = entryObjList.getJSONObject(j);
						String goodsNum = entryObj.get("sku").toString();  // 商品货号
						String qty = entryObj.get("sl1") != null ? entryObj.get("sl1").toString() : "0";  // 通知数量
						String price = entryObj.get("dj") != null ? entryObj.get("dj").toString() : "0";  // 单价
//						String amt = entryObj.get("je1") != null ? entryObj.get("je1").toString() : "0";  // 金额
						//modify by mairq 20250303 原来取je1改为取金额
						String amt = entryObj.get("je") != null ? entryObj.get("je").toString() : "0";  // 金额
						entry.set("yd_goodsnum", goodsNum);
						entry.set("yd_qty", qty);
						entry.set("yd_price", price);
						entry.set("yd_amount", amt);
						
						entrys.add(entry);
					}
					
					objs.add(bill);
				} catch (Exception e) {
					System.out.println("处理单据 " + billNo + " 时发生异常: " + e.getMessage());
					e.printStackTrace();
					// 继续处理下一个单据
					continue;
				}
			}
		}
		
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存批发通知单", "sm", WHOLESALENOTICEBILL_ENTITYNAME);
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
	
	
	    
	String doPost(String url,Map<String,String> param)
	{
		return ApiHelper.doPost(url, param); 
	}
	
	String getMd5(String data)
	{
		return ApiHelper.getMd5(data);
	}
	
	/***
	 * @fun 计算签名信息
	 * **/
	String  calSign(String serviceType, String postData)
	{
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String params = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postData;
		String sign = getMd5(params);
		return sign;
	}
	
	
	
	//获取查询数据
	 public void getCommonBillList(JSONObject postJson,String serviceType,String dataField,SaveHandler handler)
			 throws Exception
			{ 
		      
				postJson.put("pageNo", 1);// 页码
				postJson.put("pageSize", pageSize);// 每页数量 
				JSONObject returnJson = post(postJson,serviceType); 
				String status = returnJson.getString("status");
				if (StringUtils.equals(status,"api-success")) 
				 { 
					JSONObject data = returnJson.getJSONObject("data");
					JSONArray orderListGets = data.getJSONArray(dataField); 
					if(orderListGets!=null)
					{
					  handler.saveBill(orderListGets);
					}
					//如有分页数据，继续获取数据
					JSONObject page = data.getJSONObject("page");
					int pageTotal=-1;
					if(page!=null)
					{
					  pageTotal = page.getIntValue("pageTotal");// 页数
					}
					for (int i = 2; i <= pageTotal; i++)
					{
                       JSONObject subPostJson =JSONObject.parseObject(postJson.toJSONString());
						subPostJson.put("pageNo", i);
						JSONObject  subReturnJson = post(subPostJson,serviceType);
					    String subStatus = subReturnJson.getString("status");
						if (StringUtils.equals(subStatus,"api-success")) 
						{
                            JSONObject subData = subReturnJson.getJSONObject("data");
							JSONArray subOrderListGets = subData.getJSONArray(dataField);
							if(subOrderListGets!=null)
							{
							  handler.saveBill(subOrderListGets);
							}
						}
					}
				 }
			}
			
			
	/**
	 * @fun 获取单据列表/详情
	 * @param servieType: cgtzd.get 采购订单通知 ,cgthtzd.get 采购退货通知
	 * @param requestData
	 * 
djbh	string	单号
djzt	string	单据状态(0 初始,1 已确认未数量审核,2 已数量审核,3 已金额审核,4 已执行,5 已终审,8 已终止)
cgjrd_djbh	string	采购订单编号
rq_start	number	业务开始日期
rq_end	number	业务结束日期
ck_code	string	仓库代码

	 * **/
	public JSONObject post(JSONObject requestData ,String serviceType )
			throws Exception
	{
		    String postData=requestData.toJSONString();
			String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
			String sign = calSign(serviceType,postData); 
			String url = ip+path + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
					+ "&serviceType=" + serviceType + "&sign=" + sign;
			Map<String, String> map = new HashMap<>();
			map.put("data", postData);
			String result = doPost(url, map);
			JSONObject returnJson = JSONObject.parseObject(result);
			System.out.println("返回结果:"+returnJson.toString());
		 return returnJson;
	}
	
	
	
	public static void main(String[] args) throws Exception
	{
		E3Service s=new E3Service();
		JSONObject req=new JSONObject();
//		 s.getPurNoticeData(req);
		 // s.getOrderRetNoticeData();
		 s.getWholeSaleRetNoticeData(req);
		s.getWholeSaleNoticeData(req);
	}

}

interface SaveHandler
{
	 void saveBill(JSONArray billList)throws Exception;
}
