package kd.bos.tcbj.im.util;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.botp.runtime.TableDefine;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.eye.api.log.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * botp工具类
 */
public class BotpUtils {

    /**
     * @param srcEntityNumber 源单实体标识
     * @param srcBillId 源单单据id
     * @param targetEntityNumber 目标单实体标识
     * @param targetBillId 目标单单据id
     * 
     * 创建下游单据与源单的关联关系
     */
    public static void createRelation(String srcEntityNumber, Long srcBillId, 
    		String targetEntityNumber, Long targetBillId) {

        // 读取需修复的目标单
        DynamicObject targetBillObj = BusinessDataServiceHelper.loadSingle(targetBillId, targetEntityNumber);

        // 获取源单单据体的表格定义：记录关联关系时，需要用到此对象中的tableId值，用一个长整数值唯一标识源单及单据体
        TableDefine srcTableDefine = EntityMetadataCache.loadTableDefine(srcEntityNumber, srcEntityNumber);

        // 根据源单编号，读取源单数据
        DynamicObject sourceBillObj = BusinessDataServiceHelper.loadSingle(srcBillId, srcEntityNumber);

        //判断是否已存在该关系
        String lkEntryKey = "billhead_lk"; 
        QFilter qFilter = new QFilter("id", QCP.equals, targetBillObj.getPkValue())
        		.and(lkEntryKey + "." + lkEntryKey + "_sid", QCP.equals, sourceBillObj.getPkValue())
        		.and(lkEntryKey + "." + lkEntryKey + "_sbillid", QCP.equals, sourceBillObj.getPkValue())
        		.and(lkEntryKey + "." + lkEntryKey + "_stableid", QCP.equals, srcTableDefine.getTableId());
		boolean exists = QueryServiceHelper.exists(targetBillObj.getDynamicObjectType().getName(), qFilter.toArray());
        if(!exists) {
        	createLinkEntity(lkEntryKey, targetBillObj, srcTableDefine, sourceBillObj);
        	// 调用目标单的保存操作，保存维护好的关联子实体行数据，并自动调用反写引擎，创建关联关系及反写：
        	OperationResult saveOperate = SaveServiceHelper.saveOperate(
        			targetEntityNumber,							// 目标单主实体编码
        			new DynamicObject[] {targetBillObj},		// 目标单数据包
        			OperateOption.create());					// 操作参数，可通过option传入各种自定义参数
        	if (!saveOperate.isSuccess()) {
        		throw new KDException("保存单据失败：" + saveOperate.getMessage());
        	}
        }

    }

    /**
     * @param lkEntryKey 固定值billhead_lk
     * @param targetBillObj 目标单obj
     * @param srcTableDefine table定义实体
     * @param sourceBillObj 源单obj
     * 只适用关联实体为单据头的关联关系
     */
    private static void createLinkEntity(String lkEntryKey, DynamicObject targetBillObj,
            TableDefine srcTableDefine, DynamicObject sourceBillObj) {
    	// 获取下级_lk子实体行
    	DynamicObjectCollection linkRows = targetBillObj.getDynamicObjectCollection(lkEntryKey);
    	linkRows.clear();
    	// 找到了匹配的行，创建一条_lk子实体上数据，记录源单内码
    	DynamicObject linkRow = new DynamicObject(linkRows.getDynamicObjectType());
    	linkRows.add(linkRow);
    	// 在lk行中，记录源单分录表格编码、源单内码、源单分录内码
    	linkRow.set(lkEntryKey + "_stableid", srcTableDefine.getTableId());        // 源单分录表格编码：以此标识源单类型及单据体
    	linkRow.set(lkEntryKey + "_sbillid", sourceBillObj.getPkValue());        // 源单内码
    	linkRow.set(lkEntryKey + "_sid", sourceBillObj.getPkValue());            // 源单分录行内码    
    }
}
