package kd.bos.tcbj.im.util;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.db.DB;
import kd.bos.entity.property.EntryProp;
import kd.bos.entity.property.SubEntryProp;
import kd.bos.entity.report.CellStyle;
import kd.bos.form.ClientProperties;
import kd.bos.form.control.EntryGrid;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.permission.PermissionServiceHelper;
import kd.bos.servicehelper.workflow.MessageCenterServiceHelper;
import kd.bos.workflow.engine.msg.info.MessageInfo;

/**
 * 通用工具类
 */
public class CommonUtils {
	// ======================IERP strat======================
	/**
	 * 判断是否有权限
	 * 
	 * @param formId 实体KEY
	 * @param permissionItemId 权限标识
	 * @return 是否
	 */
	public static boolean checkFunctionPermission(String formId, String permissionItemId) {
		long userId = Long.parseLong(RequestContext.get().getUserId());
		int result = PermissionServiceHelper.checkFunctionPermission(userId, RequestContext.get().getOrgId(), formId,
				permissionItemId);
		return result == 1;
	}

	/**
	 * 复制
	 * 
	 * @param srcDObj 复制对象
	 * @param tarDObj 被复制对象
	 * @param ignoreKeySet 忽略key值
	 */
	public static void copyDynObj(DynamicObject srcDObj, DynamicObject tarDObj, Set<String> ignoreKeySet) {
		Set<String> srcEntityAllProperties = getEntityExistProperties(srcDObj, ignoreKeySet);//取所有属性
		Set<String> tarEntityAllProperties = getEntityExistProperties(tarDObj, ignoreKeySet); //取对象现有属性
		//取交集
		Set<String> propSet = new HashSet<>(srcEntityAllProperties);//存源单属性和目标单属性的交集
		propSet.retainAll(tarEntityAllProperties);
		//copy（id以外的属性）
		for (String key : propSet) {
			if("id".equals(key)) continue;
			//赋值
			String[] splitKey = key.split("\\.");
			//表头
			if(splitKey.length == 1) {
				srcDObj.set(key, tarDObj.get(key));
			} else if(splitKey.length == 2) { //分录.属性
				Object tarValueObjColl = null;
				try {
					tarValueObjColl = tarDObj.get(splitKey[0]);
				} catch (Exception e) {
					continue;//子单据体报错
				}
				if(tarValueObjColl instanceof DynamicObjectCollection) {
					DynamicObjectCollection srcValueDObjColl = (DynamicObjectCollection)srcDObj.get(splitKey[0]);//源单分录
					DynamicObjectCollection tarValueDObjColl = (DynamicObjectCollection)tarValueObjColl;//目标单分录
					for (int i = 0; i < tarValueDObjColl.size(); i++) {
						if("id".equals(splitKey[1])) continue;//附件不复制
						DynamicObject tarValueDObj = tarValueDObjColl.get(i);
						DynamicObject srcValueDObj = null;
						if(srcValueDObjColl.size() >= tarValueDObjColl.size()) {
							srcValueDObj = srcValueDObjColl.get(i);
						}else {
							srcValueDObj = srcValueDObjColl.addNew();
						}
						srcValueDObj.set(splitKey[1], tarValueDObj.get(splitKey[1]));
					}
					//删除多的行
					List<DynamicObject> delDObj = new ArrayList<>();
					int delnum = srcValueDObjColl.size() - tarValueDObjColl.size();
					for (int i = 0; i < delnum; i++) {
						DynamicObject dynamicObject = srcValueDObjColl.get(srcValueDObjColl.size() - i - 1);
						delDObj.add(dynamicObject);
					}
					if(delDObj.size() > 0) {
						srcValueDObjColl.removeAll(delDObj);
					}
				}
			} else if(splitKey.length == 3) {
				Object tarValueObjColl = tarDObj.get(splitKey[0]);
				if(tarValueObjColl instanceof DynamicObjectCollection) {
					//获取子分录数据
					DynamicObject newTarDObj = BusinessDataServiceHelper.loadSingle(tarDObj.get("id"), tarDObj.getDataEntityType().getName(),
							splitKey[0]+","+splitKey[0]+"."+splitKey[1]+","+splitKey[1]+"."+splitKey[2]);
					DynamicObjectCollection srcValueDObjColl = (DynamicObjectCollection)srcDObj.get(splitKey[0]);//源单分录
					DynamicObjectCollection tarValueDObjColl = (DynamicObjectCollection)newTarDObj.get(splitKey[0]);//目标单分录
					for (int i = 0; i < tarValueDObjColl.size(); i++) {
						DynamicObject tarValueDObj = tarValueDObjColl.get(i);
						DynamicObject srcValueDObj = null;
						if(srcValueDObjColl.size() >= tarValueDObjColl.size()) {
							srcValueDObj = srcValueDObjColl.get(i);
						}else {
							srcValueDObj = srcValueDObjColl.addNew();
						}
						//插入子分录
						if(tarValueDObj.get(splitKey[1]) instanceof DynamicObjectCollection) {
							DynamicObjectCollection srcValueObjSubColl = (DynamicObjectCollection)srcValueDObj.get(splitKey[1]);//源单分录
							DynamicObjectCollection tarValueObjSubColl = (DynamicObjectCollection)tarValueDObj.get(splitKey[1]);//源单子分录
							for (int j = 0; j < tarValueObjSubColl.size(); j++) {
								DynamicObject tarValueSubDObj = tarValueObjSubColl.get(j);
								DynamicObject srcValueSubDObj = null;
								if(srcValueObjSubColl.size() >= tarValueObjSubColl.size()) {
									srcValueSubDObj = srcValueObjSubColl.get(j);
								}else {
									srcValueSubDObj = srcValueObjSubColl.addNew();
								}
								if("id".equals(splitKey[2])) {
									srcValueSubDObj.set(splitKey[2], null);
									continue;//id不复制
								}
								srcValueSubDObj.set(splitKey[2], tarValueSubDObj.get(splitKey[2]));
							}
						}
					}
					//删除多的行
					List<DynamicObject> delDObj = new ArrayList<>();
					int delnum = srcValueDObjColl.size() - tarValueDObjColl.size();
					for (int i = 0; i < delnum; i++) {
						DynamicObject dynamicObject = srcValueDObjColl.get(srcValueDObjColl.size() - i - 1);
						delDObj.add(dynamicObject);
					}
					if(delDObj.size() > 0) {
						srcValueDObjColl.removeAll(delDObj);
					}
				}
			} else { //未遇到
				
			}
		}

	}

	/**
	 * 获取该实体全部属性
	 * 
	 * @param entityName 实体标识
	 * @param ignoreKeySet 忽略字段
	 * @return 所有属性set
	 */
	public static Set<String> getEntityAllProperties(String entityName, Set<String> ignoreKeySet) {
		Set<String> propSet = new HashSet<>();
		DynamicObject newDynamicObject = BusinessDataServiceHelper.newDynamicObject(entityName);
		IDataEntityType dataEntityType = newDynamicObject.getDataEntityType();
		DataEntityPropertyCollection properties = dataEntityType.getProperties();
		setProperties2List(properties, propSet, ignoreKeySet, "");
		return propSet;
	}

	/**
	 * 获取实体存在的属性
	 * 
	 * @param dobj 实体对象
	 * @param ignoreKeySet 忽略字段
	 * @return 所有属性set
	 */
	public static Set<String> getEntityExistProperties(DynamicObject dobj, Set<String> ignoreKeySet) {
		Set<String> propList = new HashSet<>();
		IDataEntityType dataEntityType = dobj.getDataEntityType();
		DataEntityPropertyCollection properties = dataEntityType.getProperties();
		setProperties2List(properties, propList, ignoreKeySet, "");
		return propList;
	}

	/**
	 * 设置属性值
	 * 
	 * @param properties 属性集合
	 * @param propSet 存储set
	 * @param ignoreKeySet 忽略key值
	 * @param entityName 实体key
	 */
	private static void setProperties2List(DataEntityPropertyCollection properties, Set<String> propSet,
			Set<String> ignoreKeySet, String entityName) {
		for (IDataEntityProperty iDataEntityProperty : properties) {
			if(iDataEntityProperty instanceof SubEntryProp) {
				SubEntryProp subEntryProp = (SubEntryProp)iDataEntityProperty;
				DynamicObjectType dynamicCollectionItemPropertyType = subEntryProp.getDynamicCollectionItemPropertyType();
				DataEntityPropertyCollection entryProperties = dynamicCollectionItemPropertyType.getProperties();
				setProperties2List(entryProperties, propSet, ignoreKeySet, subEntryProp.getParent().getName()+"."+subEntryProp.getName() + ".");
			}else if(iDataEntityProperty instanceof EntryProp) {
				EntryProp entryProp = (EntryProp)iDataEntityProperty;
				DynamicObjectType dynamicCollectionItemPropertyType = entryProp.getDynamicCollectionItemPropertyType();
				DataEntityPropertyCollection entryProperties = dynamicCollectionItemPropertyType.getProperties();
				setProperties2List(entryProperties, propSet, ignoreKeySet, entryProp.getName() + ".");
			}else {
				String key = iDataEntityProperty.getName();
				String[] split = entityName.split("\\.");
				if(split.length == 2) {
					propSet.add(split[1]+"."+key);
				}
				key = entityName + key;
				//排除
				if((ignoreKeySet != null && ignoreKeySet.contains(key)) //忽略
					|| "id".equals(key) || "seq".equals(key) //不复制
					|| "multilanguagetext".equals(key) //会导致报错
					|| key.endsWith("_id") //不知何用的属性
					|| key.startsWith("billhead_") //不要关联关系
					) {
					continue;
				}
				
				//赋值
				propSet.add(key);
			}
		}
	}

	/**
	 * 发送消息到消息中心
	 * 
	 * @param type 消息类型 MessageInfo.TYPE_WARNING等
	 * @param receivers 接收人
	 * @param senderName 发送者名称
	 * @param entityNumber 业务单据标识KEY
	 * @param opName 业务单据操作属性名
	 * @param bizBillId 业务单据ID
	 * @param tag 消息标签
	 * @param source 来源
	 * @param title 消息标题
	 * @param content 消息内容
	 * @return 返回消息中心ID
	 */
	public static Long sendMsg2MsgCenter(String type, List<Long> receivers, String senderName, String entityNumber,
			String opName, String bizBillId, String tag, String source, String title, String content) {
		String userId = RequestContext.get().getUserId();
		MessageInfo message = new MessageInfo();
		message.setType(MessageInfo.TYPE_WARNING);
		message.setTitle("消息中心来信！");
		receivers.add(Long.parseLong(userId));
		message.setUserIds(receivers);
		message.setSenderName("sender");
		// message.setSenderId(Long.parseLong(userId));
		message.setEntityNumber("wf_tripreqbill_edit");
		message.setOperation("save");
		message.setBizDataId(Long.parseLong("477206338401754112"));
		message.setTag("重要,必读");
		// message.setPubaccNumber("flowassist");
		// message.setMobContentUrl("http://localhost:8080/ierp/ierp/index.html?formId=wf_deadletterjob&pkId=531736201699273728");
		// message.setContentUrl("http://localhost:8080/ierp/ierp/index.html?formId=wf_deadletterjob&pkId=531736201699273728");
		message.setContent("通知消息");
		Long msgId = MessageCenterServiceHelper.sendMessage(message);
		return msgId;
	}

	// ======================IERP end======================

	// ======================String strat======================
	/**
	 * 占位符print
	 * 
	 * @param str 字符串，例：{0}你好
	 * @param data 占位符值
	 * @return 字符串
	 */
	public static String f(String str, Object... data) {
		if (StringUtils.isBlank(str) && data.length == 1)
			return data.toString();
		return MessageFormat.format(str, data);
	}

	/**
	 * 创建list
	 * 
	 * @param <T> 泛型
	 * @param params 值
	 * @return list
	 */
	public static <T> List<T> toList(T... params) {
		List<T> list = new ArrayList<T>();
		for (int i = 0; i < params.length; i++) {
			list.add(params[i]);
		}
		return list;
	}

	/**
	 * 创建set
	 * 
	 * @param <T> 泛型
	 * @param params 值
	 * @return list
	 */
	public static <T> Set<T> toSet(T... params) {
		Set<T> set = new HashSet<T>();
		for (int i = 0; i < params.length; i++) {
			set.add(params[i]);
		}
		return set;
	}

	/**
	 * 返回数组
	 * 
	 * @param <T> 泛型
	 * @param params 值
	 * @return 数组
	 */
	public static <T> T[] toArray(T... params) {
		return params;
	}

	/**
	 * 插入字符
	 * 
	 * @param joinStr 需要插入的字符
	 * @param strs 数组
	 * @return 字符串
	 */
	public static String join(String joinStr, String... strs) {
		StringBuilder result = new StringBuilder();
		for (int i = 0; i < strs.length; i++) {
			result.append(strs[i]);
			if (i < strs.length - 1) {
				result.append(joinStr);
			}
		}
		return result.toString();
	}
	// ======================String end======================

	public static DynamicObject copyAttachment(DynamicObject attDObj) {
		if (attDObj == null)
			return null;
		DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject(attDObj.getDataEntityType().getName());
		DataEntityPropertyCollection properties = attDObj.getDataEntityType().getProperties();
		for (IDataEntityProperty p1 : properties) {
			// id 和 multilanguagetext不用设置 设置了会导致字段的重复
			if (!p1.getName().equals("id") && !p1.getName().equals("multilanguagetext")) {
				// uid加了索引，因此不能重复 粗略可以使用下面的策略 原本的生成策略没有找到
				if (p1.getName().equals("uid")) {
					newObj.set("uid", DB.genLongId(""));
				} else {
					Object value = attDObj.get(p1);
					newObj.set(p1, value);
				}
			}
		}
		newObj.set("status", "B");
		return newObj;
	}
	
	/**
	  * 改变分录单元格颜色
	  * @param grid 分录
	  * @param rowIndex 行下标
	  * @param key 字段key
	  * @param color 颜色
	  */
	 protected void setEntityCellColor(EntryGrid grid, int rowIndex, String key, String color) {
	  if (grid == null || StringUtils.isBlank(key)) {
	   return;
	  }
	  if ("id".equals(key)) {
	   // ID不需要设置背景色
	   return;
	  }
	  CellStyle style = new CellStyle();
	  style.setRow(rowIndex);
	  style.setFieldKey(key);
	  style.setForeColor(color);
	  List<CellStyle> styleList = new ArrayList<CellStyle>();
	  styleList.add(style);
	  grid.setCellStyle(styleList);
	 }

	 /**
	  * 设置标签颜色
	  * @param key 字段标识
	  * @param color 颜色
	  */
	 protected void setForeColor(String key, String color) {
	  Map<String, Object> map = new HashMap<>();
	  map.put(ClientProperties.ForeColor, color);
//	  this.getView().updateControlMetadata(key, map);

	 }
}
