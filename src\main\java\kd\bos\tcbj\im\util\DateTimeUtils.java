package kd.bos.tcbj.im.util;

import kd.bos.exception.KDBizException;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Title: 时间处理工具类
 * @Description:
 * @date 2021-12-9
 */
public class DateTimeUtils {

    /**格式：yyyy-MM-dd */
    public final static String SDF_DATE = "yyyy-MM-dd";
    /**格式：yyyy-MM-dd HH:mm:ss*/
    public final static String SDF_TIME = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式化，默认为 yyyy-MM-dd HH:mm:ss
     * @param date 日期
     * @return 日期格式化字符串
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static String format(Date date) {
        return format(date, SDF_TIME);
    }

    /**
     * 日期格式化
     * @param date 日期
     * @param format 格式化样式
     * @return 日期格式化字符串
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static String format(Date date, String format) {
        if (date == null) return null;
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 将Date日期格式化为yyyy-MM-dd的形式返回Date格式的日期
     * @param date riq
     * @return 返回格式化后的日期
     * <AUTHOR>
     * @date 2021-12-16
     */
    public static Date format2(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 时
        calendar.set(Calendar.MINUTE, 0); // 分
        calendar.set(Calendar.SECOND, 0); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        return calendar.getTime();
    }

    /**
     * 字符串按格式转化为日期
     * @param dateStr 日期字符串
     * @return 格式化的日期
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date parse(String dateStr) {
        return parse(dateStr, SDF_TIME);
    }

    /**
     * 获取上上个月第一天
     * @return 上上个月第一天
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date getFirstDayOfLastLastMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, 1); // 时
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 时
        calendar.set(Calendar.MINUTE, 0); // 分
        calendar.set(Calendar.SECOND, 0); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        calendar.add(Calendar.MONTH, -2); // +1月份
        return calendar.getTime();
    }

    /**
     * 获取上个月份第一天
     * @return 月份第一天
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date getFirstDayOfLastMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, 1); // 时
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 时
        calendar.set(Calendar.MINUTE, 0); // 分
        calendar.set(Calendar.SECOND, 0); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        calendar.add(Calendar.MONTH, -1); // -1月份
        return calendar.getTime();
    }

    /**
     * 获取当前日期的月份第一天
     * @return 月份第一天
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date getFirstDayOfThisMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, 1); // 时
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 时
        calendar.set(Calendar.MINUTE, 0); // 分
        calendar.set(Calendar.SECOND, 0); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        return calendar.getTime();
    }

    /**
     * 获取下个月第一天
     * @return 下个月第一天
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date getFirstDayOfNextMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, 1); // 时
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 时
        calendar.set(Calendar.MINUTE, 0); // 分
        calendar.set(Calendar.SECOND, 0); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        calendar.add(Calendar.MONTH, 1); // +1月份
        return calendar.getTime();
    }

    /**
     * 获取上上个月第一天
     * @return 上上个月第一天
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date getLastDayOfLastLastMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, 1); // 天
        calendar.add(Calendar.MONTH, -1); // -1月份
        calendar.add(Calendar.DATE, -1); // -1天
        calendar.set(Calendar.HOUR_OF_DAY, 23); // 时
        calendar.set(Calendar.MINUTE, 59); // 分
        calendar.set(Calendar.SECOND, 59); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        return calendar.getTime();
    }

    /**
     * 获取上个月最后一天
     * @return 上个月最后一天
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date getLastDayOfLastMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, 1); // 天
        calendar.add(Calendar.DATE, -1); // -1天
        calendar.set(Calendar.HOUR_OF_DAY, 23); // 时
        calendar.set(Calendar.MINUTE, 59); // 分
        calendar.set(Calendar.SECOND, 59); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        return calendar.getTime();
    }

    /**
     * 获取当前日期的月份最后一天
     * @return 月份最后一天
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date getLastDayOfThisMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, 1); // 天
        calendar.add(Calendar.MONTH, 1); // +1月份
        calendar.add(Calendar.DATE, -1); // -1天
        calendar.set(Calendar.HOUR_OF_DAY, 23); // 时
        calendar.set(Calendar.MINUTE, 59); // 分
        calendar.set(Calendar.SECOND, 59); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        return calendar.getTime();
    }

    /**
     * 获取当天开始时间点
     * <AUTHOR>
     * @date 2022-09-21
     */
    public static Date getCurrentBeginTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 时
        calendar.set(Calendar.MINUTE, 0); // 分
        calendar.set(Calendar.SECOND, 0); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        return calendar.getTime();
    }

    /**
     * 获取当天最后时间点
     * <AUTHOR>
     * @date 2022-09-21
     */
    public static Date getCurrentEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 23); // 时
        calendar.set(Calendar.MINUTE, 59); // 分
        calendar.set(Calendar.SECOND, 59); // 秒
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒
        return calendar.getTime();
    }

    /**
     * 字符串按格式转化为日期
     * @param dateStr 日期字符串
     * @param format 格式化样式
     * @return 格式化的日期
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date parse(String dateStr, String format) {
        return parse(dateStr, format, Locale.getDefault(Locale.Category.FORMAT));
    }

    /**
     * 字符串按格式转化为日期
     * @param dateStr 日期字符串
     * @param format 格式化样式
     * @return 格式化的日期
     * <AUTHOR>
     * @date 2021-12-13
     */
    public static Date parse(String dateStr, String format, Locale locale) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format, locale);
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            throw new KDBizException("时间格式化异常！详情："+e.getMessage());
        }
    }

    /**
     * 判断time1是否大于time2
     * @param time1 日期1
     * @param time2 日期2
     * @return time1 > time2
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static boolean gt(Date time1, Date time2) {
        if (time1 == null || time2 == null) return false;
        return time1.getTime() > time2.getTime();
    }

    /**
     * 判断time1是否大于等于time2
     * @param time1 日期1
     * @param time2 日期2
     * @return time1 >= time2
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static boolean ge(Date time1, Date time2) {
        if (time1 == null || time2 == null) return false;
        return time1.getTime() >= time2.getTime();
    }

    /**
     * 判断time1是否小于time2
     * @param time1 日期1
     * @param time2 日期2
     * @return time1 < time2
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static boolean lt(Date time1, Date time2) {
        if (time1 == null || time2 == null) return false;
        return time1.getTime() < time2.getTime();
    }

    /**
     * 判断time1是否小于等于time2
     * @param time1 日期1
     * @param time2 日期2
     * @return time1 <= time2
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static boolean le(Date time1, Date time2) {
        if (time1 == null || time2 == null) return false;
        return time1.getTime() <= time2.getTime();
    }

    /**
     * 判断time1是否等于time2
     * @param time1 日期1
     * @param time2 日期2
     * @return time1 == time2
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static boolean eq(Date time1, Date time2) {
        if (time1 == null || time2 == null) return false;
        return time1.getTime() == time2.getTime();
    }

    /**
     * 日期天数追加/追减
     * @param time 日期
     * @param date 追加/追减 天数
     * @return i天后的日期
     * <AUTHOR>
     * @date 2022-1-7
     */
    public static Date addDate(Date time, int date) {
        return addTime(time, 0, 0 , date);
    }

    /**
     * 月份追加/追减
     * @param time 日期
     * @param month 追加/追减 月份
     * @return i月后的日期
     * <AUTHOR>
     * @date 2022-1-7
     */
    public static Date addMonth(Date time, int month) {
        return addTime(time, 0, month , 0);
    }

    /**
     * 年追加/追减
     * @param time 日期
     * @param year 追加/追减 年
     * @return i年后的日期
     * <AUTHOR>
     * @date 2022-1-7
     */
    public static Date addYear(Date time, int year) {
        return addTime(time, year, 0 , 0);
    }

    /**
     * 年追加/追减
     * @param time 日期
     * @param year 追加/追减 年
     * @param month 追加/追减 月
     * @param date 追加/追减 日
     * @return i年后的日期
     * <AUTHOR>
     * @date 2022-1-7
            */
    public static Date addTime(Date time, int year, int month, int date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.add(Calendar.YEAR, year); // 年份累加year
        cal.add(Calendar.MONTH, month); // 月份累加month
        cal.add(Calendar.DATE, date); // 日期累加date
        return cal.getTime();
    }

    /**
     * 获取指定 年/月/日/时/分/秒 的日期
     * @param year 年
     * @param month 月（1~12）
     * @param date 日（1~31）
     * @param hour 时（0~24）
     * @param minute 分（0~60）
     * @param second 秒（0~60）
     * @return 日期
     * <AUTHOR>
     * @date 2022-1-7
     */
    public static Date setTime(int year, int month, int date, int hour, int minute, int second) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month-1);
        cal.set(Calendar.DATE, date);
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.SECOND, second);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取两个日期的差异天数
     * @param date1 日期1
     * @param date2 日期2
     * @return 差异天数
     * <AUTHOR>
     * @date 2022-10-21
     */
    public static long diffDay(Date date1, Date date2) {
        Objects.requireNonNull(date1);
        Objects.requireNonNull(date2);

        long diffTime = Math.abs(date2.getTime() - date1.getTime());
        long dateNum = diffTime/86400000; // 86400000 = 60 * 60 * 24 * 1000 = 1天
        if (diffTime > dateNum*86400000) {
            dateNum = dateNum+1;
        }
        return dateNum;
    }
}
