package kd.bos.tcbj.im.util;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.ICollectionProperty;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.db.DB;
import kd.bos.entity.EntityType;
import kd.bos.entity.property.BasedataProp;
import kd.bos.entity.property.EntryProp;
import kd.bos.entity.property.MulBasedataProp;
import kd.bos.servicehelper.BusinessDataServiceHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实体工具类
 */
public class DynamicObjectUtil {

    /**
     * 将实体转换为Map（字段值映射)
     * @author: hst
     * @createDate: 2022/11/1
     * @param dynamicObject
     * @return
     */
    public static Map<String, Object> objectMapByQuery(DynamicObject dynamicObject) {
        Map<String, Object> objectMap = new HashMap();
        if (dynamicObject != null) {
            DataEntityPropertyCollection dynamicObjects = dynamicObject.getDataEntityType().getProperties();
            Iterator iterator = dynamicObjects.iterator();

            while(iterator.hasNext()) {
                IDataEntityProperty iDataEntityProperty = (IDataEntityProperty)iterator.next();
                String name = iDataEntityProperty.getName();
                Object object = dynamicObject.get(name);
                if (iDataEntityProperty instanceof BasedataProp) {
                    if (object instanceof DynamicObject) {
                        objectMap.put(name.toLowerCase(), getBDIdOfValue((DynamicObject)object));
                    } else if (object instanceof Long) {
                        objectMap.put(name.toLowerCase(), object);
                    }
                } else if (!(iDataEntityProperty instanceof EntryProp)) {
                    if (iDataEntityProperty instanceof MulBasedataProp) {
                        if (object instanceof DynamicObjectCollection) {
                            objectMap.put(name.toLowerCase(), getMulBaseDataOfId(dynamicObject.getDynamicObjectCollection(name)));
                        }
                    } else {
                        objectMap.put(name.toLowerCase(), object);
                    }
                }
            }
        }
        return objectMap;
    }

    protected static Object getBDIdOfValue(DynamicObject dynamicObject) {
        return dynamicObject != null ? dynamicObject.get("id") : null;
    }

    protected static Object[] getMulBaseDataOfId(DynamicObjectCollection dynamicObjects) {
        List<Object> reList = new ArrayList();
        Iterator iterator = dynamicObjects.iterator();
        while(iterator.hasNext()) {
            DynamicObject dynamicObject = (DynamicObject)iterator.next();
            if (null != dynamicObject && dynamicObject.toString().length() != 0) {
                DynamicObject obj = dynamicObject.getDynamicObject(1);
                if (null != obj) {
                    reList.add(dynamicObject.getDynamicObject(1).getPkValue());
                }
            }
        }
        return reList.toArray();
    }

    /**
     * 获取实体所有字段
     * @author: hst
     * @createDate: 2022/11/1
     * @param entityName
     * @return
     */
    public static List<String> getAllField (String entityName) {
        DynamicObject entity = BusinessDataServiceHelper.newDynamicObject(entityName);
        return getAllField(entity.getDynamicObjectType());
    }

    /**
     * 获取实体所有字段
     * @author: hst
     * @createDate: 2022/11/1
     * @param dynamicObjectType
     * @return
     */
    public static List<String> getAllField (DynamicObjectType dynamicObjectType) {
        StringBuffer fieldStr = new StringBuffer();
        List<String> fieldList = new ArrayList<>();
        if (dynamicObjectType != null) {
            EntityType ObjectType = (EntityType) dynamicObjectType;
            Map<String, IDataEntityProperty> fields = ObjectType.getFields();
            for (Map.Entry<String, IDataEntityProperty> field : fields.entrySet()) {
                fieldStr.append(field.getValue().getName() + ",");
            }
            fieldList = Arrays.stream(fieldStr.deleteCharAt(fieldStr.length() - 1).toString().split(",")).collect(Collectors.toList());
        }
        return fieldList;
    }

    /**
     * 描述：设置动态对象值改变状态
     * @author: hst
     * @createDate: 2024/04/11
     * @param dataEntity 	对象
     * @param isChanged	对象属性值是否改变
     * @param propKeys	对象属性
     */
    public static void setBizChanged(DynamicObject dataEntity, boolean isChanged, String... propKeys) {
        DataEntityPropertyCollection properties = dataEntity.getDataEntityType().getProperties();
        for (IDataEntityProperty property : properties) {
            if (propKeys.length > 0) {
                if (Arrays.asList(propKeys).contains(property.getName())) {
                    dataEntity.getDataEntityState().setBizChanged(property.getOrdinal(), isChanged);
                } else {
                    dataEntity.getDataEntityState().setBizChanged(property.getOrdinal(), !isChanged);
                }
            } else {
                dataEntity.getDataEntityState().setBizChanged(property.getOrdinal(), isChanged);
            }
        }

        List<ICollectionProperty> collectionProperties = properties.getCollectionProperties(false);
        for (ICollectionProperty collectionPropertie : collectionProperties) {
            DynamicObjectCollection entryColl = dataEntity.getDynamicObjectCollection(collectionPropertie.getName());
            DataEntityPropertyCollection treeEntryProperties = entryColl.getDynamicObjectType().getProperties();
            // 分录存在属性
            for (String propKey : propKeys) {
                IDataEntityProperty property = treeEntryProperties.get(propKey);
                if (property != null) {
                    for (DynamicObject entry : entryColl) {
                        entry.getDataEntityState().setBizChanged(property.getOrdinal(), isChanged);
                    }
                }
            }
        }
    }

    public static DynamicObject copyAttach(DynamicObject att_bd) {

        System.out.println(att_bd.getDataEntityType().getName());
        DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject(att_bd.getDataEntityType().getName());
        DataEntityPropertyCollection properties = att_bd.getDataEntityType().getProperties();
        for(IDataEntityProperty p1:properties) {
            //id 和 multilanguagetext不用设置 设置了会导致字段的重复

            if(!p1.getName().equals("id") &&  !p1.getName().equals("multilanguagetext")) {
                //uid加了索引，因此不能重复    粗略可以使用下面的策略       原本的生成策略没有找到
                if(p1.getName().equals("uid")) {
                    newObj.set("uid", DB.genLongId(""));
                }else {
                    Object value = att_bd.get(p1);
                    newObj.set(p1, value);
                }
            }
        }
        return newObj;
    }
}
