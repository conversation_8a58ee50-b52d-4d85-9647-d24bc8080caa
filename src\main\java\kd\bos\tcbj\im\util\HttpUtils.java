package kd.bos.tcbj.im.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Http工具类
 * <AUTHOR>
 * @Description:
 * @date 2021-12-10
 */
public class HttpUtils {

    /**
     * 链接超时时间
     */
    private static final int COUN_TIME_OUT = 60 * 1000;
    /**
     * 读取超时时间
     */
    private static final int READ_TIME_OUT = 60 * 1000;

    /**
     * POST请求数据
     * @param u 请求地址
     * @param json 请求的Json数据
     * @return 响应回传的结果
     */
    public static String sendPost(String u, String json) {
        return sendPost(u, null, json);
    }

    /**
     * POST请求数据
     * @param u 请求地址
     * @param headMap 附加的请求头
     * @param json 请求的Json数据
     * @return 响应回传的结果
     */
    public static String sendPost(String u, Map<String, String> headMap, String json) {
        StringBuffer sbf = new StringBuffer();
        try {
            URL url = new URL(u);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
//            connection.addRequestProperty("role", "Admin");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept-Charset", "UTF-8");
            connection.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            // update by hst 2024/05/21 设置超时时间
            connection.setConnectTimeout(COUN_TIME_OUT);
            connection.setReadTimeout(READ_TIME_OUT);

            if (headMap != null) {
                for (String key: headMap.keySet()) {
                    connection.setRequestProperty(key, headMap.get(key)); // 添加附加的请求头
                }
            }

            // 连接
            connection.connect();

//            DataOutputStream out = new DataOutputStream(connection.getOutputStream());
            PrintWriter out = new PrintWriter(new OutputStreamWriter(connection.getOutputStream(), StandardCharsets.UTF_8));

            if (!"".equals(json)) {
//                out.writeBytes(json);
                out.write(json);
            }
            out.flush();
            out.close();

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
            String lines;
            while ((lines = reader.readLine()) != null) {
                sbf.append(lines);
            }
//            System.out.println(sbf);
            reader.close();
            // 断开连接
            connection.disconnect();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sbf.toString();
    }

    /**
     * 通过GET请求调用接口
     * @param u 访问的URL地址
     * @return 返回相应的数据
     */
    public static String sendGet(String u) {
        StringBuffer sbf = new StringBuffer();
        try {
            URL url = new URL(u);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//            connection.addRequestProperty("role", "Admin");
            connection.addRequestProperty("Content-Type", "application/json");
            // update by hst 2024/05/21 设置超时时间
            connection.setConnectTimeout(COUN_TIME_OUT);
            connection.setReadTimeout(READ_TIME_OUT);

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String lines;
            while ((lines = reader.readLine()) != null) {
                lines = new String(lines.getBytes(), StandardCharsets.UTF_8);
                sbf.append(lines);
            }
//            System.out.println(sbf);
            reader.close();
            // 断开连接
            connection.disconnect();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sbf.toString();
    }
}
