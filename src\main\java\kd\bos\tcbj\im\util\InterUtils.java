package kd.bos.tcbj.im.util;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.exception.KDBizException;
import kd.bos.tcbj.im.vo.RequestMap;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 接口工具类
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-6-14
 */
public class InterUtils {

    public static SimpleDateFormat sdf_date = new SimpleDateFormat("yyyy-MM-dd");
    public static SimpleDateFormat sdf_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 判断是否为空，并返回String
     * @param val 判断的值
     * @param fieldName 节点名称
     * @return 判断是否为空，并返回String
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static String isEmpty(Object val,String fieldName) throws KDBizException {
        if(val == null || StringUtils.isBlank(val.toString())) throw new KDBizException(fieldName+"不能为空！");
        return val.toString();
    }

    /**
     * 判断值是否不为空
     * @param val 判断的值
     * @return 是否不为空
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static boolean isNotBlank(Object val) throws KDBizException {
        return val != null && !StringUtils.isBlank(val.toString());
    }
    /**
     * 判断值是否为空
     * @param val 判断的值
     * @return 是否为空
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static boolean isBlank(Object val) throws KDBizException {
        return val == null || StringUtils.isBlank(val.toString());
    }
    /**
     * 获取字符串
     * @param val 判断的值
     * @return 获取字符串信息
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static String getString(Object val) throws KDBizException {
        if(val == null || StringUtils.isBlank(val.toString())) return "";
        return val.toString();
    }
    /**
     * 获取字符串
     * @param val 判断的值
     * @param fieldName 节点名称
     * @return 不为空的字符串
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static String getString(Object val,String fieldName) throws KDBizException {
        return isEmpty(val, fieldName);
    }
    /**
     * 获取布尔值
     * @param val 判断的值
     * @param fieldName 节点名称
     * @param isAllowBlank 是否为空
     * @return 布尔值
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static boolean getBoolean(Object val, String fieldName, boolean isAllowBlank) throws KDBizException {
        if (isAllowBlank && isBlank(val)) return false;
        String s = getString(val, fieldName);
        if (s.equalsIgnoreCase("true")||s.equalsIgnoreCase("false")) {
            return Boolean.parseBoolean(s);
        }
        return s.equalsIgnoreCase("1") || s.equalsIgnoreCase("1.0");
    }

    /**
     * 获取Int
     * @param val 判断的值
     * @param fieldName 节点名称
     * @param isAllowBlank 是否为空
     * @return int
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static int getInt(Object val, String fieldName, boolean isAllowBlank) {
        try {
            if(isAllowBlank && (val == null || StringUtils.isBlank(val.toString()))) return 0;
            if (val instanceof Double) {
                return ((Double) val).intValue();
            }
            return Integer.parseInt(val.toString());
        } catch (Exception err) {
            err.printStackTrace();
            throw new KDBizException(fieldName+"格式错误！必须为int型数字类型");
        }
    }

    /**
     * 获取Int
     * @param val 判断的值
     * @return int
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static int getInt(Object val) {
        try {
            if (val instanceof Double) {
                return ((Double) val).intValue();
            }else if (val instanceof Integer) {
                return (Integer) val;
            }
            return Integer.parseInt(val.toString());
        } catch (Exception err) {
            throw new KDBizException("格式错误！必须为int型数字类型");
        }
    }

    /**
     * 获取日期信息
     * @param val 判断的值
     * @param fieldName 节点名称
     * @return 日期
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static Date getDate(Object val, String fieldName) throws KDBizException {
        try {
            if(val == null || StringUtils.isBlank(val.toString())) return null;
            return sdf_date.parse(val.toString());
        } catch (ParseException e) {
            try {
                return sdf_time.parse(val.toString());
            } catch (ParseException e1) {
                e1.printStackTrace();
                throw new KDBizException(fieldName+"格式错误！（日期格式：yyyy-MM-dd或yyyy-MM-dd HH:mm:ss）");
            }
        }
    }

    /**
     * 获取日期信息
     * @param val 判断的值
     * @param fieldName 节点名称
     * @param isAllowBlank 是否为空
     * @return 日期
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static Date getDate(Object val, String fieldName, boolean isAllowBlank) throws KDBizException {
        if(!isAllowBlank) isEmpty(val, fieldName);
        else if(StringUtils.isBlank(val.toString())) return null;
        try {
            return sdf_date.parse(val.toString());
        } catch (ParseException e) {
            try {
                return sdf_time.parse(val.toString());
            } catch (ParseException e1) {
                e1.printStackTrace();
                throw new KDBizException(fieldName+"格式错误！（日期格式：yyyy-MM-dd或yyyy-MM-dd HH:mm:ss）");
            }
        }
    }

    /**
     * 获取BigDecimal
     * @param val 判断的值
     * @param fieldName 节点名称
     * @param isAllowBlank 是否允许为空
     * @return 获取值对应的BigDecimal，为空返回null
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static BigDecimal getBigDecimal(Object val, String fieldName, boolean isAllowBlank)  throws KDBizException {
        if(!isAllowBlank) isEmpty(val, fieldName);
        else if(val == null || StringUtils.isBlank(val.toString())) return null;
        try {
            return new BigDecimal(val.toString());
        } catch (Exception e) {
            throw new KDBizException(fieldName+"格式错误！");
        }
    }

    /**
     * 获取BigDecimal
     * @param val 判断的值
     * @param fieldName 节点名称
     * @return 为空默认为0的BigDecimal
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static BigDecimal getBigDecimal(Object val,String fieldName)  throws KDBizException {
        try {
            if(val == null || StringUtils.isBlank(val.toString())) return new BigDecimal(0);
            return new BigDecimal(val.toString());
        } catch (Exception e) {
            throw new KDBizException(fieldName+"格式错误！");
        }
    }

    /**
     * 获取BigDecimal
     * @param val 判断的值
     * @param fieldName 节点名称
     * @return 不为空和不为0的BigDecimal
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static BigDecimal getBigDecimalAndNotZero(Object val,String fieldName)  throws KDBizException {
        try {
            if(val == null || StringUtils.isBlank(val.toString()) || new BigDecimal(val.toString()).compareTo(new BigDecimal(0)) == 0)
                throw new KDBizException(fieldName+"不能为空或为0！");
            return new BigDecimal(val.toString());
        } catch (KDBizException e) {
            throw new KDBizException(e.getMessage());
        } catch (Exception e) {
            throw new KDBizException(fieldName+"格式错误！");
        }
    }

    /**
     * 获取分录信息
     * @param val 判断的值
     * @param fieldName 节点名称
     * @param isAllowBlank 是否为空
     * @return 分录集合信息
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static List<RequestMap> getEntry(Object val, String fieldName, boolean isAllowBlank) throws KDBizException {
        return getList(val, fieldName, isAllowBlank);
    }

    /**
     * 获取分录信息
     * @param val 判断的值
     * @param fieldName 节点名称
     * @param isAllowBlank 是否为空
     * @return 分录集合信息
     * <AUTHOR>
     * @date 2021-12-24
     */
    public static List<RequestMap> getList(Object val, String fieldName, boolean isAllowBlank) throws KDBizException {
        if (!isAllowBlank) isEmpty(val, fieldName);
        List<Map<String, Object>> entryList = (List<Map<String, Object>>) val;
        return entryList.stream().map(RequestMap::to).collect(Collectors.toList());
    }

    /**
     * 校验字段是否为空
     * @param info 对象
     * @param keyFields info的校验是否为空的字段
     * @return 字段信息为空的错误信息
     * <AUTHOR>
     * @date 2022-6-16
     */
    public static String verifyFieldIsNull(DynamicObject info, String[] keyFields) {
        // 获取属性映射
        DataEntityPropertyCollection propCol = info.getDataEntityType().getProperties();
        Map<String, String> propMap = propCol.stream().collect(Collectors.toMap(item -> item.getName(), item -> String.valueOf(item.getDisplayName()), (v1, v2) -> v1));
        // 遍历所有字段判断是否为空
        StringBuilder errBuilder = new StringBuilder();
        for (int index=0,length=keyFields.length; index<length; index++) {
            String keyField = keyFields[index];
            Object keyValue = info.get(keyField);
            String keyFieldAlias = propMap.get(keyField);
            if (keyValue instanceof String) {
                if (StringUtils.isEmpty((String)keyValue)) errBuilder.append(keyFieldAlias).append("、");
            }else if (keyValue instanceof Long) {
                if ((Long)keyValue == 0) errBuilder.append(keyFieldAlias).append("、");
            }else {
                if (keyValue == null) errBuilder.append(keyFieldAlias).append("、");
            }
        }
        if (errBuilder.length() > 0) {
            return errBuilder.delete(errBuilder.length()-1, errBuilder.length()).insert(0, "表头字段[").append("]不能为空！").toString();
        }
        return "";
    }

    /**
     * 校验分录字段是否为空
     * @param info 对象
     * @param entryFiled 分录标识
     * @param keyFields info的校验是否为空的字段
     * @return 字段信息为空的错误信息
     * <AUTHOR>
     * @date 2022-6-16
     */
    public static String verifyFieldIsNull(DynamicObject info, String entryFiled, String[] keyFields) {

        DynamicObjectCollection entryCol = info.getDynamicObjectCollection(entryFiled);
        // 获取属性映射
        DataEntityPropertyCollection propCol = entryCol.getDynamicObjectType().getProperties();
        Map<String, String> propMap = propCol.stream().collect(Collectors.toMap(item -> item.getName(), item -> String.valueOf(item.getDisplayName()), (v1, v2) -> v1));
        // 遍历所有字段判断是否为空
        StringBuilder errBuilder = new StringBuilder();
        Set<String> dumpSet = new HashSet<>();
        for (DynamicObject entryInfo : entryCol) {
            for (int index=0,length=keyFields.length; index<length; index++) {
                String keyField = keyFields[index];
                if (dumpSet.contains(keyField)) continue;
                Object keyValue = entryInfo.get(keyField);
                String keyFieldAlias = propMap.get(keyField); // 别名
                if (keyValue instanceof String) {
                    if (StringUtils.isEmpty((String)keyValue)) {
                        dumpSet.add(keyField);
                        errBuilder.append(keyFieldAlias).append("、");
                    }
                }else if (keyValue instanceof Long) {
                    if ((Long)keyValue == 0) {
                        dumpSet.add(keyField);
                        errBuilder.append(keyFieldAlias).append("、");
                    }
                }else {
                    if (keyValue == null) {
                        dumpSet.add(keyField);
                        errBuilder.append(keyFieldAlias).append("、");
                    }
                }
            }
        }
        if (errBuilder.length() > 0) {
            return errBuilder.delete(errBuilder.length()-1, errBuilder.length()).insert(0, "分录字段[").append("]不能为空！").toString();
        }
        return "";
    }

    /**
     * 校验字段是否为空
     * @param info 对象
     * @param keyFields info的校验是否为空的字段
     * @param keyFieldNames info的校验是否为空的字段名称
     * @return 字段信息为空的错误信息
     * <AUTHOR>
     * @date 2022-6-16
     */
    public static String verifyFieldIsNull(DynamicObject info, String[] keyFields, String[] keyFieldNames) {
        if (keyFields.length != keyFieldNames.length) throw new KDBizException("传递参数keyFields和keyFieldNames个数不一致！");
        StringBuilder errBuilder = new StringBuilder();
        for (int index=0,length=keyFields.length; index<length; index++) {
            String keyField = keyFields[index];
            Object keyValue = info.get(keyField);
            if (keyValue instanceof String) {
                if (org.apache.commons.lang3.StringUtils.isEmpty((String)keyValue)) errBuilder.append(keyFieldNames[index]).append("、");
            }else if (keyValue instanceof Long) {
                if ((Long)keyValue == 0) errBuilder.append(keyFieldNames[index]).append("、");
            }else {
                if (keyValue == null) errBuilder.append(keyFieldNames[index]).append("、");
            }
        }
        if (errBuilder.length() > 0) {
            return errBuilder.delete(errBuilder.length()-1, errBuilder.length()).insert(0, "表头字段[").append("]不能为空！").toString();
        }
        return "";
    }

    /**
     * 校验分录字段是否为空
     * @param info 对象
     * @param entryFiled 分录标识
     * @param keyFields info的校验是否为空的字段
     * @param keyFieldNames info的校验是否为空的字段名称
     * @return 字段信息为空的错误信息
     * <AUTHOR>
     * @date 2022-6-16
     */
    public static String verifyFieldIsNull(DynamicObject info, String entryFiled, String[] keyFields, String[] keyFieldNames) {
        if (keyFields.length != keyFieldNames.length) throw new KDBizException("传递参数keyFields和keyFieldNames个数不一致！");
        DynamicObjectCollection entryCol = info.getDynamicObjectCollection(entryFiled);
        StringBuilder errBuilder = new StringBuilder();
        Set<String> dumpSet = new HashSet<>();
        for (DynamicObject entryInfo : entryCol) {
            for (int index=0,length=keyFields.length; index<length; index++) {
                String keyField = keyFields[index];
                if (dumpSet.contains(keyField)) continue;
                Object keyValue = entryInfo.get(keyField);
                if (keyValue instanceof String) {
                    if (org.apache.commons.lang3.StringUtils.isEmpty((String)keyValue)) {
                        dumpSet.add(keyField);
                        errBuilder.append(keyFieldNames[index]).append("、");
                    }
                }else if (keyValue instanceof Long) {
                    if ((Long)keyValue == 0) {
                        dumpSet.add(keyField);
                        errBuilder.append(keyFieldNames[index]).append("、");
                    }
                }else {
                    if (keyValue == null) {
                        dumpSet.add(keyField);
                        errBuilder.append(keyFieldNames[index]).append("、");
                    }
                }
            }
        }
        if (errBuilder.length() > 0) {
            return errBuilder.delete(errBuilder.length()-1, errBuilder.length()).insert(0, "分录字段[").append("]不能为空！").toString();
        }
        return "";
    }
}
