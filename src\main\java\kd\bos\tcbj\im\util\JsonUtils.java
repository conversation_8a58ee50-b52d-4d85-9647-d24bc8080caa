package kd.bos.tcbj.im.util;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.List;
import java.util.Map;

/**
 * Json处理工具类
 * <AUTHOR>
 * @Description:
 * @date 2021-12-10
 */
public class JsonUtils {

    /**
     * 用Gson将Json字符串转换成Map对象
     * @param data Json字符串
     * @return map对象
     * <AUTHOR>
     * @date 2021-12-10
     */
    public static <T> Map<String, T> toMap(String data) {
        return new Gson().fromJson(data, new TypeToken<Map<String, T>>(){}.getType());
    }

    public static Map<String, String> toMap2(String data) {
        return new Gson().fromJson(data, new TypeToken<Map<String, String>>(){}.getType());
    }

    public static <T> List<T> toList(String data) {
        return new Gson().fromJson(data, new TypeToken<List<T>>(){}.getType());
    }

    public static <T> List<Map<String, T>> toList2(String data) {
        return new Gson().fromJson(data, new TypeToken<List<Map<String, T>>>(){}.getType());
    }

    /**
     * 用Gson将Json字符串解析为对象
     * @param data Json字符串
     * @param cls class对象
     * @return 对象
     * <AUTHOR>
     * @date 2021-12-10
     */
    public static <T> T to(String data, Class<T> cls) {
        return new Gson().fromJson(data, cls);
    }

    /**
     * 用Gson将对象转换成Json字符串
     * @param obj pojo对象
     * @return Json字符串
     * <AUTHOR>
     * @date 2021-12-10
     */
    public static String toJsonString(Object obj) {
        return new Gson().toJson(obj);
    }
}
