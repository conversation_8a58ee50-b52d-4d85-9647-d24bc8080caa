package kd.bos.tcbj.im.util;

import kd.bos.dataentity.entity.LocaleString;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.EmailInfo;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.EmailHandler;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.servicehelper.workflow.MessageCenterServiceHelper;
import kd.bos.workflow.engine.msg.info.MessageAttachment;
import kd.bos.workflow.engine.msg.info.MessageInfo;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 消息处理
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-7-24
 */
public class MessageHelper {

    /**
     * 发送消息平台消息
     * @param title 标题
     * @param content 消息内容
     * @param tagMsg 标签
     * @param receives 接收人（苍穹的人员ID集合）
     * <AUTHOR>
     * @date 2022-7-24
     */
    public static long sendMessageCenter(String title, String content, String tagMsg, List<Long> receives) {
        if (receives == null || receives.size() == 0) return -1;
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setMessageTitle(new LocaleString(title)); // 标题
        messageInfo.setMessageContent(new LocaleString(content)); // 内容
        messageInfo.setUserIds(receives); // 接收人
        messageInfo.setType(MessageInfo.TYPE_MESSAGE);
        messageInfo.setTag(tagMsg); // 标签
        //发送消息
        return MessageCenterServiceHelper.sendMessage(messageInfo);
    }

    /**
     * 只发送消息，不携带附件
     * @param title 标题
     * @param content 消息内容
     * @param receiveEmails 接收邮件人
     * <AUTHOR>
     * @date 2022-7-24
     */
    public static Map<String, Object> sendEmail(String title, String content, List<String> receiveEmails) {
        return sendEmailWithAttachment(title, content, null, null, receiveEmails);
    }

    /**
     * 携带附件发送消息
     * @param title 标题
     * @param content 消息内容
     * @param attachPath 附件路径
     * @param fileName 附件名称
     * @param receiveEmails 接收邮件人
     * <AUTHOR>
     * @date 2022-7-24
     */
    public static Map<String, Object> sendEmailWithAttachment(String title, String content, String attachPath, String fileName, List<String> receiveEmails) {
        if (receiveEmails == null || receiveEmails.size() == 0) return null;
        EmailInfo emailInfo = new EmailInfo();
        emailInfo.setTitle(title); // 标题
        emailInfo.setContent(content); // 内容
        emailInfo.setReceiver(receiveEmails); // 接收人
        // 将附件的内容解析成流进行发送
        if (StringUtils.isNotEmpty(attachPath) && StringUtils.isNotEmpty(fileName)) {
            MessageAttachment attachment = new MessageAttachment();
            List<byte[]> dataList = getBytesByFile(attachPath);
            attachment.setAttachments(dataList);
            List<String> attachmentNames = new ArrayList<>();
            attachmentNames.add(fileName);
            emailInfo.setAttachments(dataList);
            emailInfo.setAttachmentNames(attachmentNames);
        }
        return EmailHandler.sendEmail(emailInfo);
    }

    /**
     * 发送短信
     * @param content 发送内容
     * @param signature 短信签名
     * @param receivePhones 接收人手机号码
     * <AUTHOR>
     * @date 2022-7-24
     */
    public static Map<String, Object> sendSMS(String content, String signature, List<String> receivePhones) {
        if (receivePhones == null || receivePhones.size() == 0) return null;
        ShortMessageInfo messageInfo = new ShortMessageInfo();
        messageInfo.setPhone(receivePhones); // 需要接收人的手机号码
        messageInfo.setCountryCode(CountryCode.CN); // 国家代码，中国86
        messageInfo.setMessage(content); // 发送的消息内容
        messageInfo.setSignature(signature); // 短信签名
        return MessageHandler.sendShortMessage(messageInfo);
    }

    private static List<byte[]> getBytesByFile(String path) {
        File file = new File(path);
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] bytes = new byte[1000];
            int n;
            while ((n = fis.read(bytes)) != -1) {
                bos.write(bytes, 0, n);
            }
            fis.close();
            byte[] data = bos.toByteArray();
            List<byte[]> datas = new ArrayList<>();
            datas.add(data);
            bos.close();
            return datas;
        }catch (Exception err) {
            err.printStackTrace();
        }
        return null;
    }
}
