package kd.bos.tcbj.im.util;

import kd.bos.exception.KDBizException;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 反射类
 * @package: kd.bos.tcbj.im.util.MethodUtils
 * @className: MethodUtils
 * @author: hst
 * @createDate: 2024/05/26
 * @version: v1.0
 */
public class MethodUtils {

    /**
     * 通过配置信息调用方法
     * @param params
     * @param className
     * @param methodName
     * @return
     */
    public static Object invokeMethodByConfig(String className, String methodName, Object... params) {
        Object data = null;
        try {
            if (StringUtils.isNotBlank(className) && StringUtils.isNotBlank(methodName)) {
                Class pathClass = Class.forName(className);

                List<Class> classes = new ArrayList<>();
                List<Object> arges = new ArrayList<>();
                for (Object param : params) {
                    classes.add(param.getClass());
                    arges.add(param);
                }
                Method method = pathClass.getMethod(methodName, classes.toArray(new Class[classes.size()]));
                data = method.invoke(pathClass.newInstance(), arges.toArray(new Object[arges.size()]));
            }
        } catch (ClassNotFoundException e) {
            throw new KDBizException("获取不到" + className + "对应类");
        } catch (NoSuchMethodException e) {
            throw new KDBizException("获取不到" + methodName + "对应方法");
        } catch (InvocationTargetException e) {
            throw new KDBizException(e.getTargetException().getMessage());
        } catch (IllegalAccessException e) {
            throw new KDBizException(e.getMessage());
        } catch (InstantiationException e) {
            throw new KDBizException(className + "未实例化对应方法");
        }
        return data;
    }
}
