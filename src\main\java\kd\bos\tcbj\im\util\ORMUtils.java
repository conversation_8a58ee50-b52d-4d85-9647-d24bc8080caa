package kd.bos.tcbj.im.util;

import kd.bos.algo.DataSet;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.ORM;

/**
 * ORM工具类
 * <AUTHOR>
 * @Description: Algo 采用内存的方式计算，减轻数据库压力
 * @date 2021-12-30
 */
public class ORMUtils {

    /**
     * 采用对象关系映射查询数据
     * 调用方式实例：query("select top 10 from gl_voucher where period.id=? and accountView.number=?", 1, 1);
     * @param oql 对象关系映射语句
     * @param params 占位符的值
     * @return 查询结果
     * <AUTHOR>
     * @date 2021-12-30
     */
    public static DynamicObjectCollection query(String oql, Object[] params) {
        return ORM.create().query(oql, params);
    }

    /**
     * 采用对象关系映射查询数据
     * 调用方式实例：query("select top 10 from gl_voucher where period.id=? and accountView.number=?", 1, 1);
     * @param algoKey algo标识，用于监控追踪，通常为 当前类/当前类+方法/当前类+实体
     * @param oql 对象关系映射语句
     * @param params 占位符的值
     * @return 查询结果
     * <AUTHOR>
     * @date 2021-12-30
     */
    public static DataSet query(String algoKey, String oql, Object[] params) {
        return ORM.create().queryDataSet(algoKey, oql, params);
    }
    
    /**
     * 关闭DataSet
     * @param 
     * @return 
     * <AUTHOR>
     * @date 2021-12-30
     */
    public static void close(DataSet dataSet) {
        if (dataSet != null) dataSet.close();
    }
}
