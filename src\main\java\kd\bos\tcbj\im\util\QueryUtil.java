package kd.bos.tcbj.im.util;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.property.EntryProp;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询/过滤 工具类
 * <AUTHOR>
 * @Description:
 * @date 2021-12-9
 */
public class QueryUtil {

    /**
     * 返回查询的QFilter
     * @param prop 属性
     * @param value 值
     * @return 查询QFilter集合
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static QFilter qf(String prop, Object value) {
        return qf(prop, QCP.equals, value);
    }

    /**
     * 返回查询的QFilter
     * @param prop 属性
     * @param compareType 比较类型
     * @param value 值
     * @return 查询QFilter集合
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static QFilter qf(String prop, String compareType, Object value) {
        return new QFilter(prop, compareType, value);
    }

    /**
     * 返回查询的集合
     * @param prop 属性
     * @param value 值
     * @return 查询QFilter集合
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static QFilter[] q(String prop, Object value) {
        return q(prop, QCP.equals, value);
    }

    /**
     * 返回查询的集合
     * @param prop 属性
     * @param compareType 比较类型
     * @param value 值
     * @return 查询QFilter集合
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static QFilter[] q(String prop, String compareType, Object value) {
        return new QFilter(prop, compareType, value).toArray();
    }

    /**
     * 返回查询的集合
     * @param arr 子集合(查询字段, 操作符号, 查询值) / (查询字段, 查询值)
     * @return 查询QFilter集合
     * <AUTHOR>
     * @date 2021-12-9
     *
     * 实例：
     * QueryUtil.q(new Object[][] {
     *    {"number", number}
     *    , {"beginDate", ">=", "2021-01-01"}
     *    , {"creator.id = auditor.id"}
     * });
     */
    public static QFilter[] q(Object[][] arr) {
        if (arr == null || arr.length == 0) return new QFilter[0];
        QFilter[] qArr = new QFilter[arr.length];
        for (int i = 0; i < arr.length; i++) {
            Object[] param = arr[i];
            if (param.length > 2) {
                qArr[i] = new QFilter((String)param[0], (String) param[1], param[2]);
            }else if (param.length == 2) {
                qArr[i] = new QFilter((String)param[0], QCP.equals, param[1]);
            }else if (param.length == 1) {
                if (param[0] instanceof String) {
                    qArr[i] = QFilter.of((String) param[0]);
                }else if (param[0] instanceof QFilter){
                    qArr[i] = (QFilter) param[0];
                }
            }
        }
        return qArr;
    }

    /**
     * 获取单据所有字段selector
     * @param entityName 实体标识
     * @param isContainLeaf 是否包含下级
     * @return
     * <AUTHOR>
     * @date 2021-12-26
     */
    public static String getSelectAllCols(String entityName, boolean isContainLeaf) {
        return StringUtils.join(getSelectAllColList(entityName, isContainLeaf), ",");
    }

    /**
     * 校验实体中是否包含此属性
     * @param entityName 实体名称
     * @param prop 属性
     * @return 是否包含属性
     * <AUTHOR>
     * @date 2022-2-17
     */
    public static boolean isContainProp(String entityName, String prop) {
        DynamicObjectType type = EntityMetadataCache.getDataEntityType(entityName);
        DataEntityPropertyCollection propCol = type.getProperties();
        return propCol.containsKey(prop);
    }

    /**
     * 获取单据所有字段selector
     * @param entityName 实体标识
     * @param isContainLeaf 是否包含下级
     * @return
     * <AUTHOR>
     * @date 2021-12-26
     */
    public static List<String> getSelectAllColList(String entityName, boolean isContainLeaf) {
        DynamicObjectType type = EntityMetadataCache.getDataEntityType(entityName);
        DataEntityPropertyCollection propCol = type.getProperties();
        List<String> keyList = new ArrayList<>();
        for (IDataEntityProperty propInfo : propCol) {
            String propName = propInfo.getName();
            Class<?> propertyType = propInfo.getPropertyType();
            if (propName.equals("multilanguagetext") || propName.equals("masterid") || propName.contains("_id")) continue;
            if (DynamicObject.class.equals(propertyType)) {
                if (isContainLeaf) {
                    keyList.add(propName+".*");
                }else {
                    keyList.add(propName+".id");
                }
            }else if (propertyType == null) {
                if ("entryentity".equals(propName)) {
                    DataEntityPropertyCollection entryPropCol = ((EntryProp) propInfo).getDynamicCollectionItemPropertyType().getProperties();
                    System.out.println("1");
                    for (IDataEntityProperty entryPropInfo : entryPropCol) {
                        String entryPropName = entryPropInfo.getName();
                        Class<?> entryPropType = entryPropInfo.getPropertyType();
                        if ("modifierfield".equals(entryPropName) || "modifydatefield".equals(entryPropName) || entryPropName.contains("_id")) continue;
                        keyList.add(propName+"."+entryPropName);
                    }
                }
            }else {
                keyList.add(propName);
            }
        }
        return keyList;
    }

    /**
     * 查询的字段集合通过“，”拼接
     * @param colName 字段集合
     * @return 返回拼接后的字符串
     * <AUTHOR>
     * @date 2021-12-9
     */
    public static String getSelectCols(String... colName) {
        return StringUtils.join(colName, ",");
    }
}
