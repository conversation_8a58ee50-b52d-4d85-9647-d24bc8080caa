package kd.bos.tcbj.im.util;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.botp.ConvertDataService;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.botp.runtime.BFRow;
import kd.bos.entity.botp.runtime.BFRowId;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.form.IFormView;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.common.enums.PcpTransBizTypeEnum;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.outbill.helper.PCPServiceHelper;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结算工具类
 * @package: kd.bos.tcbj.im.util.SettleUtils
 * @className: SettleUtils
 * @author: hst
 * @createDate: 2024/05/27
 * @version: v1.0
 */
public class SettleUtils {

    private final static int amountPrecision = 2;
    private final static int curAmountPrecision = 2;
    private final static int pricePrecision = 10;
    private final static BigDecimal ONEHUNDRED = new BigDecimal(100);

    /**
     * 通过PCP组织编码获取组织信息
     * @author: hst
     * @createDate: 2024/05/27
     */
    public static DynamicObject getOrgByPcpOrg(String pcpOrgNumber) {
        Long orgId = BizHelper.getQueryOne(BillTypeHelper.BILLTYPE_PCPORGRELATION, "entryentity.yd_org.id", QFilter.of("billstatus='C' and entryentity.yd_pcporgnumber = ?", pcpOrgNumber).toArray());
        if (orgId == null || orgId == 0) return null;
        return BizHelper.getDynamicObjectByIdFromCache(BillTypeHelper.BILLTYPE_ORG, orgId);
    }

    /**
     * 通过仓库编码获取仓库信息
     * @author: hst
     * @createDate: 2024/05/27
     */
    public static DynamicObject getWarehouseByNumber(String warehouseNumber) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_WAREHOUSE, "id, number", QFilter.of("status='C' and number = ?", warehouseNumber).toArray());
    }

    /**
     * 通过物料编码查找物料基础资料
     * @author: hst
     * @createDate: 2024/05/27
     */
    public static DynamicObject getMaterialByNumber(String materialNumber) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_MATERIAL, "id, number", QFilter.of("status='C' and number = ?", materialNumber).toArray());
    }

    /**
     * 获取供应商信息
     * @param internalCompanyId
     * @return
     * @author: hst
     * @createDate: 2024/05/30
     */
    public static DynamicObject getSupplierByInternalCompany(String internalCompanyId) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_SUPPLIER,
                "id,number,name,taxrate.number",
                QFilter.of("status='C' and internal_company.id=?", internalCompanyId).toArray());
    }

    /**
     * 获取供应商信息
     * @param internalCompanyId
     * @return
     * @author: hst
     * @createDate: 2024/05/30
     */
    public static DynamicObject getCustomerByInternalCompany(String internalCompanyId) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_CUSTOMER,
                "id,number,name,taxrate.number",
                QFilter.of("status='C' and internal_company.id=?", internalCompanyId).toArray());
    }

    /**
     * 根据配置信息获取结算路径
     *
     * @param param
     * @param className
     * @param methodName
     * @return
     * @author: hst
     * @createDate: 2024/05/27
     */
    public static DataSet getSettlePathByConfig(Object param, String className, String methodName) {
        DataSet settleDataSet = null;
        Object data = MethodUtils.invokeMethodByConfig(className,methodName,param);
        if (Objects.nonNull(data)) {
            settleDataSet = (DataSet) data;
        }
        return settleDataSet;
    }

    /** 从上下文中获取基础资料信息
     * @param key
     * @param number
     * @param context
     * @return
     * @author: hst
     * @createDate: 2024/05/27
     */
    public static Object getBaseInfoByContext (String key, String number, Map<String,Object> context) {
        Map<String,Object> infos = null;
        if (context.containsKey(key)) {
            infos = (Map<String, Object>) context.get(key);
        } else {
            infos = new HashMap<String, Object>();
        }

        Object value = null;
        if (infos.containsKey(number)) {
            value = infos.get(number);
        } else {
            value = getBaseInfo(key,number);
        }

        infos.put(number,value);
        context.put(key,infos);
        return value;
    }

    /**
     * 获取基础资料
     * @param key
     * @param number
     * @return
     * @author: hst
     * @createDate: 2024/05/27
     */
    private static Object getBaseInfo (String key, String number) {
        Object value = null;
        switch (key) {
            case "bos_org" : {
                value = SettleUtils.getOrgByPcpOrg(number);
                break;
            }
            case "bd_warehouse" : {
                value = SettleUtils.getWarehouseByNumber(number);
                break;
            }
            case "bd_material" : {
                value = SettleUtils.getMaterialByNumber(number);
                break;
            }
        }
        return value;
    }

    /**
     * 获取业务类型名称
     * @param type
     * @return
     * @author: hst
     */
    public static String getBussinessTypeName (String type) {
        PcpTransBizTypeEnum[] enums = PcpTransBizTypeEnum.values();
        for (PcpTransBizTypeEnum bizType : enums) {
            if (bizType.getCode().equals(type)) {
                return bizType.getName();
            }
        }

        throw new KDBizException("can't deserialize enum");
    }

    /**
     * 判断是销售还是退货
     * @param bill
     * @return
     * @author: hst
     * @createDate: 2024/05/27
     */
    public static boolean determineIsSaleOrReturn (DynamicObject bill) {
        String bizType = bill.getString("yd_settletype");
        if (StringUtils.isNotBlank(bizType)) {
            return "1".equals(bizType) ? true : false;
        }

        // 通过结算路径判断结算类型
        boolean isForward = determineBizTypeByPath(bill);
        bill.set("yd_settletype", isForward ? "1" : "0");

        return isForward;
    }

    /**
     * 通过结算路径判断结算类型
     * @param bill
     * @return
     * @author: hst
     * @createDate: 2024/06/01
     */
    private static boolean determineBizTypeByPath (DynamicObject bill) {
        boolean isForward = true;
        String billType = bill.getString("yd_businesstype");
        // 先通过正向查找
        QFilter qFilter = QFilter.of("yd_outorg.id = ? and yd_inorg.id = ? and yd_businesstype like '%," + billType + ",%' and status = 'C'",
                bill.getString("yd_outorg.id"),bill.getString("yd_inorg.id"));

        boolean isExist = QueryServiceHelper.exists(BillTypeHelper.BILLTYPE_TRANSSETTLERELA, new QFilter[]{qFilter});

        if (!isExist) {
            isForward = false;

            qFilter = QFilter.of("yd_outorg.id = ? and yd_inorg.id = ? and yd_businesstype like '%," +  billType + ",%' and status = 'C'",
                    bill.getString("yd_inorg.id"),bill.getString("yd_outorg.id"));
            isExist = QueryServiceHelper.exists(BillTypeHelper.BILLTYPE_TRANSSETTLERELA, new QFilter[]{qFilter});

            if (!isExist) {
                throw new KDBizException("当前调出组织：" + bill.getString("yd_outorg.name") + "与调入组织："
                        + bill.getString("yd_inorg.name") + "没有配置"
                        + getBussinessTypeName(bill.getString("yd_businesstype")) + "类型结算关系");
            }
        }

        return isForward;
    }

    /**
     * 校验组织与仓库映射表
     *
     * @param settlePath 结算路径
     * @return
     * @author: hst
     * @createDate: 2022/12/14
     */
    public static String checkOrganizationWarehouse(HashMap context, Map<String,Object> settlePath) {
        // 退货路径
        List<String> returnOrgList = (LinkedList<String>) settlePath.get("orgReturnlList");
        // 正常结算路径
        List<String> orgList = (LinkedList<String>) settlePath.get("orgSettleList");

        // 校验组织与仓库映射表
        Map<String, String> orgWarehouseMap = getOrgWarehouseMap(context);
        List<String> errOrgs = new ArrayList<>();
        if (returnOrgList.size() > 0) {
            for (int i = 0; i < returnOrgList.size(); i++) {
                String orgIds = returnOrgList.get(i);
                String outOrgId = orgIds.split("&")[0];
                String inOrgId = orgIds.split("&")[1];
                if (!orgWarehouseMap.containsKey(outOrgId)) {
                    errOrgs.add(outOrgId);
                }
                // 退货路径末级组织无需校验仓库
                if ((i != returnOrgList.size() -1) && !orgWarehouseMap.containsKey(inOrgId)) {
                    errOrgs.add(inOrgId);
                }
            }
        }
        if (orgList.size() > 0) {
            for (int i = 0; i < orgList.size(); i++) {
                String orgIds = orgList.get(i);
                String outOrgId = orgIds.split("&")[0];
                String inOrgId = orgIds.split("&")[1];
                // 若不存在退货路径，一级组织需要校验
                if ((returnOrgList.size() > 0 || i != 0) && !orgWarehouseMap.containsKey(outOrgId)) {
                    errOrgs.add(outOrgId);
                }
                if ((i != orgList.size() - 1) && !orgWarehouseMap.containsKey(inOrgId)) {
                    errOrgs.add(inOrgId);
                }
            }
        }

        if (errOrgs.size() > 0) {
            DynamicObjectCollection orgs = QueryServiceHelper.query("bos_adminorg","number,name",
                    new QFilter[]{new QFilter("id",QFilter.in,errOrgs)});
            StringBuffer warehouseError = new StringBuffer("以下销售出库组织缺少默认仓库映射：");
            for (DynamicObject org : orgs) {
                warehouseError = warehouseError.append(org.getString("number") + org.getString("name") + "、");
            }
            return warehouseError.substring(0,warehouseError.length() - 1) + ";\n";
        }
        return "";
    }

    /**
     * 获取组织与仓库的默认对应关系
     *
     * @author: hst
     * @createDate:
     */
    public static Map<String, String> getOrgWarehouseMap(HashMap context) {
        if (context.containsKey("warehouseMap")) {
             return (Map<String, String>) context.get("warehouseMap");
        } else {
            Map<String, String> orgWarehouseMap = new HashMap<String, String>();
            QFilter wsFilter = new QFilter("billstatus", QCP.equals, "C");
            DataSet orgWsSet = QueryServiceHelper.queryDataSet("ws", "yd_orgwarehouse",
                    "entryentity.yd_org.id orgId,entryentity.yd_warehouse.id wsId", wsFilter.toArray(), null);
            for (Row row : orgWsSet) {
                orgWarehouseMap.put(row.getString("orgId"), row.getString("wsId"));
            }
            context.put("warehouseMap",orgWarehouseMap);
            return orgWarehouseMap;
        }
    }

    /**
     * 校验内部商客
     * @param settlePath
     * @return
     * @author: hst
     * @createDate: 2024/05/28
     */
    public static String checkSupplyAndCustomer(HashMap context, Map<String,Object> settlePath, DynamicObject bill) {
        // 退货路径
        List<String> returnOrgList = (LinkedList<String>) settlePath.get("orgReturnlList");
        // 正常结算路径
        List<String> orgList = (LinkedList<String>) settlePath.get("orgSettleList");

        StringBuffer errMsg = new StringBuffer();
        // 退货路径交易价格校验
        errMsg.append(checkOrgtoOrgNewPrice(returnOrgList,bill));
        // 正常结算路径交易价格校验
        errMsg.append(checkOrgtoOrgNewPrice(orgList,bill));
        // 退货路径校验客户信息
        errMsg.append(checkCustomerInfo(returnOrgList));
        // 正常结算路径校验客户信息
        errMsg.append(checkCustomerInfo(orgList));
        // 退货路径校验客户信息
        errMsg.append(checkSupplierInfo(returnOrgList));
        // 正常结算路径校验客户信息
        errMsg.append(checkSupplierInfo(orgList));

        return errMsg.toString();
    }

    /**
     * 校验组织间交易价格
     * @param orgList
     * @param bill
     * @return
     */
    private static String checkOrgtoOrgNewPrice (List<String> orgList, DynamicObject bill) {
        Date bizDate = bill.getDate("yd_bizdate");
        DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("entryentity");
        // 获取组织内部各个物料新的结算价格集合
        Map<String, BigDecimal> matPriceMap = getOrgtoOrgNewPrice(orgList, bizDate);
        // 错误信息
        Map<String, String> errMap = new HashMap<>();

        for (String orgId : orgList) {
            for (DynamicObject entry : entryCol) {
                String key = orgId + "&" + entry.getString("yd_material.id");
                if (!matPriceMap.containsKey(key)) {
                    String matNumber = entry.getString("yd_material.number");
                    if (errMap.containsKey(orgId)) {
                        errMap.put(orgId, errMap.get(orgId) + "、" + matNumber);
                    } else {
                        errMap.put(orgId, matNumber);
                    }
                }
            }
        }

        if (errMap.size() > 0) {
            StringBuffer errMsg = new StringBuffer();
            for (Map.Entry<String,String> errEntry : errMap.entrySet()) {
                String srcOrgId = errEntry.getKey().split("&")[0];
                String desOrgId = errEntry.getKey().split("&")[1];
                DynamicObject srcOrgObj = BusinessDataServiceHelper.loadSingle(srcOrgId, "bos_org");
                DynamicObject desOrgObj = BusinessDataServiceHelper.loadSingle(desOrgId, "bos_org");
                errMsg.append("销售出库组织【" + srcOrgObj.getString("name") +
                        "】与采购入库组织【" + desOrgObj.getString("name") + "】缺失以下物料的价格：" + errEntry.getValue() + ";\n");
            }
            return errMsg.toString();
        }
        return "";
    }

    /**
     * 根据调出调入组织获取各个物料最新的价格
     * @param orgs
     * @param bizDate 业务日期
     * @author: hst
     * @createDate: 2024/05/28
     */
    public static Map<String, BigDecimal> getOrgtoOrgNewPrice( List<String> orgs, Date bizDate) {
        Map<String, BigDecimal> matNewPriceMap = new HashMap<String, BigDecimal>();
        List<String> outOrgs = orgs.stream().map(org -> org.split("&")[0]).collect(Collectors.toList());
        List<String> inOrgs = orgs.stream().map(org -> org.split("&")[1]).collect(Collectors.toList());

        QFilter priceFilter = new QFilter("yd_outorg.id", QCP.in, outOrgs);
        priceFilter.and(new QFilter("yd_inorg.id", QCP.in, inOrgs));
        priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
        String bizDateStr = new SimpleDateFormat("yyyy-MM-dd").format(bizDate);
        try {
            bizDate = new SimpleDateFormat("yyyy-MM-dd").parse(bizDateStr);
        } catch (ParseException e) {
            System.out.println(e.getLocalizedMessage());
        }
        // 开始日期小于等于业务日期
        priceFilter.and(new QFilter("entryentity.yd_begindate", QCP.less_equals, bizDate));
        // 结束日期大于等于业务日期
        priceFilter.and(new QFilter("entryentity.yd_enddate", QCP.large_equals, bizDate));

        DataSet priceSet = QueryServiceHelper.queryDataSet("price", "yd_pricerelbill",
                "yd_outorg.id outOrgId,yd_inorg.id inOrgId,entryentity.yd_materiel.id matId,entryentity.yd_price newprice",
                priceFilter.toArray(), null);
        for (Row row : priceSet) {
            matNewPriceMap.put(row.getString("outOrgId") + "&" + row.getString("inOrgId") + "&" + row.getString("matId"),
                    row.getBigDecimal("newprice"));
        }

        return matNewPriceMap;
    }

    /**
     * 校验客户信息
     * @param orgList
     * @return
     * @author: hst
     * @createDate: 2024/05/28
     */
    private static String checkCustomerInfo (List<String> orgList) {
        List<String> cusInterIds= new ArrayList<>();
        for (int i = 0; i < orgList.size(); i++) {
            cusInterIds.add(orgList.get(i).split("&")[1]);
        }

        // 错误信息
        StringBuffer errMsg = new StringBuffer();
        List<String> errOrgs = new ArrayList<>();
        // 获取客户信息
        DynamicObjectCollection customers = getInternalCP("bd_customer",cusInterIds);
        for (String cusInterId : cusInterIds) {
            boolean isExist = false;
            for (DynamicObject customer : customers) {
                if (cusInterId.equals(customer.getString("internal_company.id"))) {
                    isExist = true;
                    if (Objects.isNull(customer.get("taxrate"))) {
                        errMsg.append(customer.getString("number") + customer.getString("name") + "对应客户默认税率为空;\n");
                        break;
                    }
                }
            }
            if (!isExist) {
                errOrgs.add(cusInterId);
            }
        }

        if (errOrgs.size() > 0) {
            DynamicObjectCollection orgs = QueryServiceHelper.query("bos_org","number,name",
                    new QFilter[]{new QFilter("id",QFilter.in,errOrgs)});
            for (DynamicObject org : orgs) {
                errMsg.append(org.getString("number") + org.getString("name") + "、");
            }
            return errMsg.substring(0,errMsg.length() - 1) + "缺少对应客户;\n";
        }
        return "";
    }

    /**
     * 校验客户信息
     * @param orgList
     * @return
     * @author: hst
     * @createDate: 2024/05/28
     */
    private static String checkSupplierInfo (List<String> orgList) {
        List<String> supInterIds= new ArrayList<>();
        for (int i = 0; i < orgList.size(); i++) {
            supInterIds.add(orgList.get(i).split("&")[0]);
        }

        // 错误信息
        StringBuffer errMsg = new StringBuffer();
        List<String> errOrgs = new ArrayList<>();
        // 获取客户信息
        DynamicObjectCollection suppliers = getInternalCP("bd_supplier",supInterIds);
        for (String supInterId : supInterIds) {
            boolean isExist = false;
            for (DynamicObject supplier : suppliers) {
                if (supInterId.equals(supplier.getString("internal_company.id"))) {
                    isExist = true;
                }
            }
            if (!isExist) {
                errOrgs.add(supInterId);
            }
        }

        if (errOrgs.size() > 0) {
            DynamicObjectCollection orgs = QueryServiceHelper.query("bos_org","number,name",
                    new QFilter[]{new QFilter("id",QFilter.in,errOrgs)});
            for (DynamicObject org : orgs) {
                errMsg.append(org.getString("number") + org.getString("name") + "、");
            }
            return errMsg.substring(0,errMsg.length() - 1) + "缺少对应客户;\n";
        }
        return "";
    }

    /**
     * 校验组织是否有对应的内部客户，返回对象，校验税率
     * @author: hst
     * @createDate: 2024/05/28
     * @param entity
     * @param bizId
     * @return
     */
    public static DynamicObjectCollection getInternalCP(String entity, List<String> bizId) {
        QFilter cusFilter = new QFilter("internal_company.id", QCP.in, bizId);
        cusFilter.and(new QFilter("status", QCP.equals, "C"));
        DynamicObjectCollection customers = QueryServiceHelper.query(entity,
                "id,name,number,taxrate,internal_company.id",
                cusFilter.toArray());
        return customers;
    }

    /**
     * 校验生产日期
     *
     * @param bill
     * @return
     * @author: hst
     * @createDate: 2024/05/28
     */
    public static String checkGenerationDate(DynamicObject bill) {
        StringBuffer lotError = new StringBuffer();
        boolean isForward = determineIsSaleOrReturn(bill);

        // 如果参数为true表示校验生产日期，为false表示不校验
        boolean isControlLot = true; // 默认校验
        boolean isGeneralDate = true; // 默认为true，自动带日期即不要校验日期，退货类型的如果开通了此参数则不用校验批次
        // 从配置表中查找对应的值
        String isControlLotStr = BizHelper.getQueryOne("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isControlLot").toArray());
        String isGeneralDateStr = BizHelper.getQueryOne("yd_e3paramsetting", "name", new QFilter("number", QCP.equals, "isGeneralDate").toArray());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(isControlLotStr)) {
            isControlLot = org.apache.commons.lang3.StringUtils.equalsIgnoreCase("true", isControlLotStr);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(isGeneralDateStr)) {
            isGeneralDate = org.apache.commons.lang3.StringUtils.equalsIgnoreCase("true", isGeneralDateStr);
        }
        DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("entryentity");
        if (isControlLot) {
            for (DynamicObject entryObj : entryCol) {
                // 校验是否存在对应的批次数据
                String lot = entryObj.getString("yd_batchno");
                String materialNum = entryObj.getString("yd_material.number");
                String wsNum = bill.getString("yd_outwarehouse.number");
                Date beginDate = entryObj.getDate("yd_probegindate");
                Date endDate = entryObj.getDate("yd_proenddate");
                if (wsNum == null) continue; // 如果不存在，只有委外中查找映射中不存在，这种情况异常已经记录，无须再次查找
                // 如果生产的日期为空，都需要从即时库存中查找
                if (beginDate == null || endDate == null) {
                    // 按批次+物料+仓库，如果没有查找到，正向执行提示异常，如果是红字，则需要再次查找按物料+仓库查找，没有则提示异常
                    boolean isHasLot = hasExpDate(lot, materialNum, wsNum, entryObj, isForward);
                    if (!isHasLot) {
                        if (isGeneralDate) {
                            boolean isHasRefundLot = hasLastExpDate(materialNum, wsNum, entryObj, isForward);
                            if (!isHasRefundLot) {
                                if (lotError.indexOf(materialNum + "的批次" + lot) < 0)
                                    lotError.append("仓库编码" + wsNum + "的" + materialNum + "的批次" + lot + "，");
                            }
                        } else {
                            if (lotError.indexOf(materialNum + "的批次" + lot) < 0)
                                lotError.append("仓库编码" + wsNum + "的" + materialNum + "的批次" + lot + "，");
                        }
                    }
                }
            }
            if (lotError.length() > 0)
                lotError.insert(0, "以下物料对应的批次不存在即时库存数据：").deleteCharAt(lotError.length() - 1).append("；");
        }
        return lotError.toString();
    }

    /**
     * 设置批次对应的生产日期和到期日
     *
     * @param lot
     * @param matNum
     * @param wsNum
     * @return
     */
    private static Boolean hasExpDate(String lot, String matNum, String wsNum, DynamicObject entry, Boolean isReturn) {
        QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
        filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
        filter.and(new QFilter("yd_flot", QCP.equals, lot));
        // 如果是正向的需要查数量大于0的批次
        if (!isReturn) {
            filter.and(new QFilter("yd_qty", QCP.large_than, 0));
        }
        DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
                "yd_mfgdate,yd_expdate", filter.toArray(), "yd_mfgdate desc");
        if (dataSet.hasNext()) {
            Row tempRow = dataSet.next();
            entry.set("yd_probegindate", tempRow.getDate("yd_mfgdate"));  // 生产日期
            entry.set("yd_proenddate", tempRow.getDate("yd_expdate"));  // 到期日
            return true;
        } else {
            return false;
        }
    }

    /**
     * 设置物料+仓库获取对应最新的批次
     *
     * @param matNum
     * @param wsNum
     * @return
     */
    private static Boolean hasLastExpDate(String matNum, String wsNum, DynamicObject entry, Boolean isReturn) {
        QFilter filter = new QFilter("yd_wsnum", QCP.equals, wsNum);
        filter.and(new QFilter("yd_matnum", QCP.equals, matNum));
        // 如果是正向的需要查数量大于0的批次
        if (!isReturn) {
            filter.and(new QFilter("yd_qty", QCP.large_than, 0));
        }
        DataSet dataSet = QueryServiceHelper.queryDataSet("inv", "yd_easinventory",
                "yd_mfgdate,yd_expdate,yd_flot", filter.toArray(), "yd_mfgdate desc");
        if (dataSet.hasNext()) {
            Row tempRow = dataSet.next();
            entry.set("yd_batchno", tempRow.getString("yd_flot"));  // 批次号
            entry.set("yd_probegindate", tempRow.getDate("yd_mfgdate"));  // 生产日期
            entry.set("yd_proenddate", tempRow.getDate("yd_expdate"));  // 到期日
            return true;
        } else {
            return false;
        }
    }

    /**
     * 通过上下文获取结算路径
     * @param context
     * @param key
     * @author: hst
     * @return
     * @author: hst
     * @createDate: 2024/05/30
     */
    public static Map<String, Object> getSettlePathByContext (HashMap context, String key) {
        Map<String,Object> settlePaths = (Map<String,Object>) context.get("settlePath");
        if (Objects.nonNull(settlePaths) && settlePaths.containsKey(key)) {
            return (Map<String, Object>) settlePaths.get(key);
        }
        return null;
    }

    /**
     * 下推单据
     *
     * @param oriBillId
     * @param oriBillEntity
     * @param desBillEntity
     * @param ruleId
     * @return
     * @author: hst 2024/05/30
     */
    public static DynamicObject pushBill(String oriBillId, String oriBillEntity, String desBillEntity, String ruleId) {
        // 将源单ID，源单标识，目标单标识，目标单名称，下推规则作为参数
        // 返回目标单ID
        // 构建下推参数
        PushArgs pushArgs = new PushArgs();
        pushArgs.setSourceEntityNumber(oriBillEntity);  // 源单标志
        pushArgs.setTargetEntityNumber(desBillEntity);  // 目标单标志
        pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告
        pushArgs.setRuleId(ruleId);  // 固定下推规则
        String billName = "";
        if ("im_purinbill".equals(desBillEntity)) {
            billName = "采购入库单";
        }
        if ("im_saloutbill".equals(desBillEntity)) {
            billName = "销售出库单";
        }

        //需要下推的单据
        List<ListSelectedRow> selectedRows = new ArrayList<>();
        ListSelectedRow srcBill = new ListSelectedRow(oriBillId);
        selectedRows.add(srcBill);
        pushArgs.setSelectedRows(selectedRows);

        //调用下推引擎，下推目标单
        ConvertOperationResult pushResult = ConvertServiceHelper.push(pushArgs);

        //判断下推是否成功，如果失败，提炼失败消息
        if (!pushResult.isSuccess()) {
            //错误摘要
            String errMessage = pushResult.getMessage();
            throw new KDException("下推" + billName + "失败" + errMessage);
        }

        //获取生成的目标单数据包
        MainEntityType targetMainType = EntityMetadataCache.getDataEntityType(desBillEntity);
        List<DynamicObject> targetBillObjs = pushResult.loadTargetDataObjects(new IRefrencedataProvider() {
            @Override
            public void fillReferenceData(Object[] objs, IDataEntityType dType) {
                BusinessDataReader.loadRefence(objs, dType);
            }
        }, targetMainType);

        return targetBillObjs.get(0);
    }

    /**
     * 重置分录含税单价后重新金额字段
     */
    public static void changePriceAndTax(DynamicObject tempTargetBillObj, DynamicObject entryObj) {
        boolean isTax = tempTargetBillObj.getBoolean("istax");
        // 计算单价
        BigDecimal taxPrice = entryObj.getBigDecimal("priceandtax");
        BigDecimal taxRate = entryObj.getBigDecimal("taxrate");
        BigDecimal price = BigDecimal.ZERO;
        BigDecimal ONEHUNDRED = new BigDecimal(100);
        if (taxPrice != null) {
            BigDecimal zero = BigDecimal.ZERO;
            BigDecimal one = BigDecimal.ONE;
            BigDecimal oneHundred = ONEHUNDRED;
            taxRate = taxRate == null ? zero : taxRate.divide(oneHundred, taxRate.scale() + 2, 4);
            price = taxPrice.divide(one.add(taxRate), pricePrecision, 4);
        }
        entryObj.set("price", price);

        if (isTax) {
            calAmountAndTax(entryObj, isTax);
            calTaxAmount(entryObj, isTax);
            calAmount(entryObj, isTax);
        } else {
            calAmount(entryObj, isTax);
            calTaxAmount(entryObj, isTax);
            calAmountAndTax(entryObj, isTax);
        }

        if (isTax) {
            calCurAmountAndTax(tempTargetBillObj, entryObj, isTax);
            calCurTaxAmount(tempTargetBillObj, entryObj, isTax);
            calCurAmount(tempTargetBillObj, entryObj, isTax);
        } else {
            calCurAmount(tempTargetBillObj, entryObj, isTax);
            calCurTaxAmount(tempTargetBillObj, entryObj, isTax);
            calCurAmountAndTax(tempTargetBillObj, entryObj, isTax);
        }

        BigDecimal discountAmount = entryObj.getBigDecimal("discountamount");
        BigDecimal amount;
        BigDecimal amountAndTax;
        if (BigDecimal.ZERO.compareTo(discountAmount) == 0) {
            amount = entryObj.getBigDecimal("price");
            entryObj.set("actualprice", amount);
            amountAndTax = entryObj.getBigDecimal("priceandtax");
            entryObj.set("actualtaxprice", amountAndTax);
        } else {
            amount = entryObj.getBigDecimal("amount");
            BigDecimal qty = entryObj.getBigDecimal("qty");
            if (BigDecimal.ZERO.compareTo(amount) != 0 && BigDecimal.ZERO.compareTo(qty) != 0) {
                BigDecimal tempPrice = amount.divide(qty, pricePrecision, 4);
                entryObj.set("actualprice", tempPrice);
            } else {
                entryObj.set("actualprice", BigDecimal.ZERO);
            }
            amountAndTax = entryObj.getBigDecimal("amountandtax");
            if (BigDecimal.ZERO.compareTo(amountAndTax) != 0 && BigDecimal.ZERO.compareTo(qty) != 0) {
                BigDecimal temptaxPrice = amountAndTax.divide(qty, pricePrecision, 4);
                entryObj.set("actualtaxprice", temptaxPrice);
            } else {
                entryObj.set("actualtaxprice", BigDecimal.ZERO);
            }
        }

    }

    private static void calCurAmount(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
        BigDecimal amount;
        BigDecimal exchangerate;
        if (isTax) {
            amount = entryObj.getBigDecimal("curamountandtax");
            exchangerate = entryObj.getBigDecimal("curtaxamount");
            entryObj.set("curamount", amount.subtract(exchangerate));
        } else {
            amount = entryObj.getBigDecimal("amount");
            exchangerate = tempTargetBillObj.getBigDecimal("exchangerate");
            BigDecimal localTaxAmount = amount.multiply(exchangerate).setScale(curAmountPrecision, 4);
            entryObj.set("curamount", localTaxAmount);
        }

    }

    private static void calCurTaxAmount(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
        BigDecimal tax = entryObj.getBigDecimal("taxamount");
        BigDecimal exchangerate = tempTargetBillObj.getBigDecimal("exchangerate");
        BigDecimal localTax = tax.multiply(exchangerate).setScale(curAmountPrecision, 4);
        entryObj.set("curtaxamount", localTax);
    }

    private static void calCurAmountAndTax(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
        BigDecimal localAmount;
        BigDecimal localTax;
        if (isTax) {
            localAmount = entryObj.getBigDecimal("amountandtax");
            localTax = tempTargetBillObj.getBigDecimal("exchangerate");
            BigDecimal localTaxAmount = localAmount.multiply(localTax).setScale(curAmountPrecision, 4);
            entryObj.set("curamountandtax", localTaxAmount);
        } else {
            localAmount = entryObj.getBigDecimal("curamount");
            localTax = entryObj.getBigDecimal("curtaxamount");
            entryObj.set("curamountandtax", localAmount.add(localTax));
        }

    }

    private static void calAmount(DynamicObject entryObj, boolean isTax) {
        BigDecimal price;
        BigDecimal qty;
        BigDecimal amount;
        if (isTax) {
            price = entryObj.getBigDecimal("amountandtax");
            qty = entryObj.getBigDecimal("taxamount");
            amount = price.subtract(qty);
            entryObj.set("amount", amount);
        } else {
            price = entryObj.getBigDecimal("price");
            qty = entryObj.getBigDecimal("qty");
            amount = qty.multiply(price).setScale(amountPrecision, 4);

            entryObj.set("amount", amount);
        }

    }

    private static void calTaxAmount(DynamicObject entryObj, boolean isTax) {
        BigDecimal amount;
        BigDecimal taxRate;
        BigDecimal tax;
        if (isTax) {
            amount = entryObj.getBigDecimal("amountandtax");
            taxRate = entryObj.getBigDecimal("taxrate");
            tax = BigDecimal.ZERO;
            if (amount.compareTo(BigDecimal.ZERO) != 0 && taxRate.compareTo(BigDecimal.ZERO) != 0) {
                taxRate = taxRate.divide(ONEHUNDRED, taxRate.scale() + 2, 4);
                tax = amount.multiply(taxRate).divide(taxRate.add(BigDecimal.ONE), amountPrecision, 4);
            }

            entryObj.set("taxamount", tax);
        } else {
            amount = entryObj.getBigDecimal("amount");
            taxRate = entryObj.getBigDecimal("taxrate");
            tax = BigDecimal.ZERO;
            if (amount.compareTo(BigDecimal.ZERO) != 0 && taxRate.compareTo(BigDecimal.ZERO) != 0) {
                taxRate = taxRate.divide(ONEHUNDRED, taxRate.scale() + 2, 4);
                tax = amount.multiply(taxRate).setScale(amountPrecision, 4);
            }

            entryObj.set("taxamount", tax);
        }

    }

    private static void calAmountAndTax(DynamicObject entryObj, boolean isTax) {
        BigDecimal amount;
        BigDecimal tax;
        BigDecimal taxAmount;
        if (isTax) {
            amount = entryObj.getBigDecimal("priceandtax");
            tax = entryObj.getBigDecimal("qty");
            taxAmount = BigDecimal.ZERO;
            BigDecimal discountAmount = entryObj.getBigDecimal("discountamount");
            taxAmount = tax.multiply(amount).subtract(discountAmount).setScale(amountPrecision, 4);
            entryObj.set("amountandtax", taxAmount);
        } else {
            amount = entryObj.getBigDecimal("amount");
            tax = entryObj.getBigDecimal("taxamount");
            taxAmount = amount.add(tax);
            entryObj.set("amountandtax", taxAmount);
        }

    }

    /**
     * 保存、提交、审核单据
     * @param desBillEntity
     * @param targetBillObj
     * @author: hst
     * @createDate: 2022/10/28
     */
    public static Map<String, String> operateTargetBill(String desBillEntity, DynamicObject targetBillObj) {
        Map<String, String> opResult = operateTargetBillOp(desBillEntity, targetBillObj);
        if (StringUtils.isEmpty(opResult.get("billId"))) {
            // 单号为空，表示失败了，记录失败原因并记录到源单上，停止执行后续的生成逻辑，执行下一张单据
            String error = opResult.get("errorMsg");
            throw new KDBizException(error);
        }
        return opResult;
    }

    /**
     * 操作单据的保存、提交、审核，都会触发源单的业务规则
     *
     * @return 成功则返回单据ID，失败则增加返回失败原因（用于反写到源单），同时也记录到单据上billId+errorMsg
     */
    public static Map<String, String> operateTargetBillOp(String desBillEntity, DynamicObject targetBillObj) {
        Map<String, String> result = new HashMap<String, String>();
        String desBillId = "";
        String billName = "";
        if (desBillEntity.equals(BillTypeHelper.BILLTYPE_PURINBILL)) {
            billName = "采购入库单";
        } else if (desBillEntity.equals(BillTypeHelper.BILLTYPE_SALEOUTBILL)) {
            billName = "销售出库单";
        } else if (desBillEntity.equals(BillTypeHelper.BILLTYPE_TRANSDIRBILL)) {
            billName = "直接调拨单";
        }

        // 保存目标单据
        OperationResult saveResult = SaveServiceHelper.saveOperate(desBillEntity, new DynamicObject[]{targetBillObj}, OperateOption.create());
        if (!saveResult.isSuccess()) {
            // 错误摘要
            String errMessage = "保存" + billName + "失败：" + saveResult.getMessage();
            String detailMsg = String.join(";",saveResult.getAllErrorInfo().stream().map(info -> info.getMessage()).collect(Collectors.toList()));
            result.put("billId", "");
            result.put("errorMsg", errMessage + (StringUtils.isNotBlank(detailMsg) ? (";" + detailMsg) : ""));
            return result;
        }

        // 提交目标单据
        String pk = saveResult.getSuccessPkIds().get(0).toString();
        QFilter qFilter = new QFilter("id", QCP.equals, pk);
        Object billNo = BusinessDataServiceHelper.loadSingle(desBillEntity, "id,billno", qFilter.toArray()).get("billno");
        OperationResult submitResult = OperationServiceHelper.executeOperate("submit", desBillEntity, new Object[]{pk}, OperateOption.create());
        if (!submitResult.isSuccess()) {
            // 错误摘要
            String errMessage = billName + billNo + "生成成功，但提交失败，请到" + billName + "手动提交，查看提交失败原因：" + submitResult.getMessage();
            result.put("billId", "");
            result.put("errorMsg", errMessage);
            return result;
        }


        // 审核目标单据
        pk = saveResult.getSuccessPkIds().get(0).toString();
        qFilter = new QFilter("id", QCP.equals, pk);
        billNo = BusinessDataServiceHelper.loadSingle(desBillEntity, "id,billno", qFilter.toArray()).get("billno");
        OperationResult auditResult = OperationServiceHelper.executeOperate("audit", desBillEntity, new Object[]{pk}, OperateOption.create());
        if (!auditResult.isSuccess()) {
            // 错误摘要
            String errMessage = billName + billNo + "提交成功，但审核失败，请到" + billName + "手动审核，查看提交失败原因：" + auditResult.getMessage();
            result.put("billId", "");
            result.put("errorMsg", errMessage);
            return result;
        } else {
            desBillId = pk;
            result.put("billId", desBillId);
            result.put("errorMsg", "");
            return result;
        }
    }

    /**
     * 重置单据上的仓库为组织指定的仓库
     *
     * @param billId
     * @param billEntity
     * @param orgWarehouseMap
     */
    public static void resetBillWarehouse(String billId, String billEntity, Map<String, String> orgWarehouseMap) {
        DynamicObject resetBill = BusinessDataServiceHelper.loadSingle(billId, billEntity);
        if (orgWarehouseMap.containsKey(resetBill.getString("org.id"))) {
            String newWs = orgWarehouseMap.get(resetBill.getString("org.id"));
            DynamicObjectCollection resetSaleEntryCol = resetBill.getDynamicObjectCollection("billentry");
            DynamicObject wareHouse = BusinessDataServiceHelper.loadSingle(newWs, "bd_warehouse");
            for (DynamicObject resetSaleEntry : resetSaleEntryCol) {
                resetSaleEntry.set("warehouse", wareHouse);
            }
        }
        SaveServiceHelper.save(new DynamicObject[]{resetBill});// 保存
    }

    /**
     * 保存上下游关联关系
     * @param billId 销售出库单中间表ID
     * @param targetId 销售出库单ID
     * <AUTHOR>
     * @date 2024/06/01
     */
    public static void saveRelation(long billId, long targetId, String billType, String targetType) {
        BotpUtils.createRelation(billType, billId, targetType, targetId);
    }

    /**
     * 重置结算单据
     * @param formView
     * @author: hst
     * @createDate: 2024/06/02
     */
    public static void resetTransSettle (IFormView formView) {
        // 获取勾选的数据的id
        UIUtils.checkSelected(formView, "请选择数据！");
        List<Object> idList = UIUtils.getSelectIds(formView);

        //根据id获取单据
        String billType_saleOutBill = BillTypeHelper.BILLTYPE_MIDTRANSBILL;
        DynamicObject template = BusinessDataServiceHelper.newDynamicObject(billType_saleOutBill);
        DynamicObject[] bills = BusinessDataServiceHelper.load(idList.toArray(),template.getDynamicObjectType());

        StringBuffer errMsg = new StringBuffer();
        List<DynamicObject> successBills = new ArrayList<>();
        for (DynamicObject bill : bills) {
            try {
                // 校验单据是否符合重置结算条件
                checkBillResetSettleStatus(bill);
                // 重置结算
                resetTransBillSettle(bill);
                // 执行成功，后续修改状态
                successBills.add(bill);
            } catch (Exception e) {
                errMsg.append(e.getMessage()).append("\n");
            }
        }

        if (errMsg.length() > 0) {
            formView.showErrorNotification(errMsg.substring(0,errMsg.length() - 1));
        } else {
            for (DynamicObject bill : successBills) {
                // 删除完后，重置中间表的单据信息
                bill.set("yd_issettle", false);
                bill.set("yd_settleerror", "");
                bill.set("yd_ishasnext", false);
                bill.set("yd_nextbillno", "");
                bill.set("yd_issuccess",false);
                bill.set("yd_settletype","");
                bill.set("yd_isreturn",false);
            }
            SaveServiceHelper.save(successBills.toArray(new DynamicObject[successBills.size()]));
            formView.showSuccessNotification("执行重置结算状态成功！");
        }
    }

    /**
     * 重置结算前校验单据状态
     * @param bill
     * @uathor: hst
     * @createDate: 2024/06/02
     */
    private static void checkBillResetSettleStatus (DynamicObject bill) {
        String billNo = bill.getString("billno");
        // 已发起结算
        if (!bill.getBoolean("yd_issettle")) {
            throw new KDBizException("库存调拨单中间表【" + billNo + "】的未发起结算，无需执行清理操作！");
        }
        // 未同步EAS
        if (bill.getBoolean("yd_istoeas")) {
            throw new KDBizException("库存调拨单中间表【" + billNo + "】已同步EAS，无法执行清理操作！");
        }
    }

    /**
     * 重置结算
     * @param bill
     * @author: hst
     * @createDate: 2024/06/02
     */
    private static void resetTransBillSettle (DynamicObject bill) {
        String srcBillEntity = BillTypeHelper.BILLTYPE_MIDTRANSBILL;
        String tempId = bill==null?"":bill.getPkValue().toString();

        // 获取下游单据
        LinkedList<String> billRelList = new LinkedList<String>();
        getDownStreamBill(tempId,srcBillEntity,billRelList);

        // 清除下游单据
        clearDownStreamBill(billRelList);
    }

    /**
     * 获取下游单据
     * @param id
     * @param srcBillEntity
     * @param billRelList
     * @author: hst
     * @createDate: 2024/06/02
     */
    public static void getDownStreamBill (String id, String srcBillEntity, LinkedList<String> billRelList) {
        ConvertDataService dataService = new ConvertDataService();
        StringBuffer errorStr = new StringBuffer();
        // 遍历单据，获取下游单据数据
        Map<Long, List<BFRow>> dirTargetBills = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity,
                new Long[]{Long.parseLong(id)});
        if (dirTargetBills.size() == 1) {
            Long tempLongId = dirTargetBills.keySet().iterator().next();
            List<BFRow> targetRows = dirTargetBills.get(tempLongId);

            if (targetRows.size() > 0) {
                for (BFRow targetRow : targetRows) {
                    BFRowId targetBillRow = targetRow.getId();
                    String tarBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
                    billRelList.add(tarBillEntity + "&" + targetBillRow.getBillId());
                    getDownStreamBill(targetBillRow.getBillId().toString(), tarBillEntity, billRelList);
                }
            }
        }
    }

    /**
     * 清除下游单据
     * @param billRelList
     * @author: hst
     * @createDate: 2024/06/02
     */
    public static void clearDownStreamBill (LinkedList<String> billRelList) {
        for (int i = billRelList.size() - 1; i >= 0; i--) {
            String[] keys = billRelList.get(i).split("&");
            String billType = keys[0];
            String billId = keys[1];

            // 查找数据
            DynamicObject info = BizHelper.getDynamicObjectById(billType, billId);
            // 校验下游单据同步EAS状态
            boolean isAllowed = checkIfDeletionIsAllowed(info,billType);
            if (!isAllowed) {
                throw new KDBizException(String.format("下游单据[%s] EAS已审核成功，请联系管理员进行删除！",
                        info.getString("billno")));
            }
            // 校验状态，进行反审核、撤销提交
            deleteBillWithCheckStatus(billType, info);
        }
    }

    /**
     * 校验单据是否允许删除
     * @param bill
     * @param billType
     * @return
     * @author: hst
     * @createDate: 2024/06/02
     */
    private static boolean checkIfDeletionIsAllowed (DynamicObject bill, String billType) {
        boolean isAllowed = true;
        // 销售出库单
        if (BillTypeHelper.BILLTYPE_SALEOUTBILL.equals(billType)) {
            boolean isAudit = bill.getBoolean("yd_checkboxsf");
            if (isAudit) {
                isAllowed = false;
            }
        } else if (BillTypeHelper.BILLTYPE_PURINBILL.equals(billType)
                || BillTypeHelper.BILLTYPE_TRANSSETTLERELA.equals(billType)) {
            String easStatus = bill.getString("yd_easstatus");
            boolean isToEas = bill.getBoolean("yd_issyneas");
            if (isToEas && "审核".equals(easStatus)) {
                isAllowed = false;
            }
        }
        return isAllowed;
    }

    /**
     * 取消审批、撤销提交，再删除数据
     * @param billType 需要执行的单据类型
     * @param info 执行的对象信息
     * @return 返回执行后的结果
     * <AUTHOR>
     * @date 2024/06/02
     */
    private static void deleteBillWithCheckStatus(String billType, DynamicObject info) {
        // 保存目标单据
        String billTypeName = BizHelper.getBillTypeName(billType);
        // 单据状态
        String billStatus = info.getString("billstatus");
        Object billId = info.getPkValue(); // 单据ID
        String billNumber = info.getString("billno"); // 单据编码

        OperationResult result;
        if ("B".equals(billStatus)) {
            result = OperationServiceHelper.executeOperate("unsubmit", billType, new Object[]{billId}, OperateOption.create());
            String errorMessage = BizHelper.getOperationErrorMessage(result);
            if (StringUtils.isNotBlank(errorMessage)) {
                throw new KDBizException("单据"+billNumber+ "撤销提交失败，请到"+billTypeName+"手动撤销提交，查看撤销提交失败原因：" + errorMessage);
            }
        }else if ("C".equals(billStatus)) {
            result = OperationServiceHelper.executeOperate("unaudit", billType, new Object[]{billId}, OperateOption.create());
            String errorMessage = BizHelper.getOperationErrorMessage(result);
            if (StringUtils.isNotBlank(errorMessage)) {
                throw new KDBizException("单据"+billNumber+ "反审核失败，请到"+billTypeName+"手动反审核，查看反审核失败原因：" + errorMessage);
            }
        }
        result = OperationServiceHelper.executeOperate("delete", billType, new Object[]{billId}, OperateOption.create());
        String errorMessage = BizHelper.getOperationErrorMessage(result);
        if (StringUtils.isNotBlank(errorMessage)) {
            throw new KDBizException("单据"+billNumber+ "删除失败，请到"+billTypeName+"手动删除，查看删除失败原因：" + errorMessage);
        }
    }

    /**
     * 重置结算单据同步EAS状态
     * @param formView
     * @author: hst
     * @createDate: 2024/06/04
     */
    public static void resetTransSettleStatus (IFormView formView,String entityName) {
        // 获取勾选的数据的id
        UIUtils.checkSelected(formView, "请选择数据！");
        List<Object> idList = UIUtils.getSelectIds(formView);

        //根据id获取单据
        String billType = entityName;
        DynamicObject template = BusinessDataServiceHelper.newDynamicObject(billType);
        DynamicObject[] bills = BusinessDataServiceHelper.load(idList.toArray(),template.getDynamicObjectType());

        StringBuffer errMsg = new StringBuffer();
        List<DynamicObject> successBills = new ArrayList<>();
        for (DynamicObject bill : bills) {
            try {
                String srcBillEntity = entityName;
                String tempId = bill==null?"":bill.getPkValue().toString();

                // 获取下游单据
                LinkedList<String> billRelList = new LinkedList<String>();
                getDownStreamBill(tempId,srcBillEntity,billRelList);

                // 清除下游单据标识
                clearBillToEASStatus(billRelList);

                // 执行成功，后续修改状态
                successBills.add(bill);
            } catch (Exception e) {
                errMsg.append(e.getMessage()).append("\n");
            }
        }

        if (errMsg.length() > 0) {
            formView.showErrorNotification(errMsg.substring(0,errMsg.length() - 1));
        } else {
            for (DynamicObject bill : successBills) {
                // 删除完后，重置中间表的单据信息
                bill.set("yd_istoeas", false);
                bill.set("yd_forwardsuccess", false);
                bill.set("yd_returnsuccess", false);
                bill.set("yd_toeasdate", null);
                bill.set("yd_totaltime", 0);
            }
            SaveServiceHelper.save(successBills.toArray(new DynamicObject[successBills.size()]));
            formView.showSuccessNotification("清除下游单据同步EAS状态成功！");
        }
    }

    /**
     * 重置下游单据同步EAS状态
     * @param billRelList
     * @author: hst
     * @createDate: 2024/06/04
     */
    public static void clearBillToEASStatus (LinkedList<String> billRelList) {
        List<DynamicObject> saleOutBills = new ArrayList<>();
        List<DynamicObject> purInBills = new ArrayList<>();
        List<DynamicObject> transBills = new ArrayList<>();
        for (int i = billRelList.size() - 1; i >= 0; i--) {
            String[] keys = billRelList.get(i).split("&");
            String billType = keys[0];
            String billId = keys[1];

            // 查找数据
            DynamicObject info = BizHelper.getDynamicObjectById(billType, billId);

            if (billType.equals(BillTypeHelper.BILLTYPE_SALEOUTBILL)) {
                info.set("yd_checkboxsf01",false);
                info.set("yd_checkboxsf02",false);
                info.set("yd_checkboxsf",false);
                info.set("yd_textareafield02","");
                info.set("yd_sftj","");
                info.set("yd_textareafield","");
                saleOutBills.add(info);
            } else if (billType.equals(BillTypeHelper.BILLTYPE_PURINBILL)){
                info.set("yd_issyneas",false);
                info.set("yd_easstatus","");
                info.set("yd_errmsg","");
                purInBills.add(info);
            } else if (billType.equals(BillTypeHelper.BILLTYPE_TRANSDIRBILL)) {
                info.set("yd_issyneas",false);
                info.set("yd_easstatus","");
                info.set("yd_errmsg","");
                transBills.add(info);
            }
        }

        SaveServiceHelper.save(saleOutBills.toArray(new DynamicObject[saleOutBills.size()]));
        SaveServiceHelper.save(purInBills.toArray(new DynamicObject[purInBills.size()]));
        SaveServiceHelper.save(transBills.toArray(new DynamicObject[transBills.size()]));
    }

    /**
     * 根据关联关系保存下游单据，保存前先清理
     * @author: hst
     * @createDate: 2024/06/17
     * @param billId
     * @param entityName
     */
    public static void saveBotpBills(String billId, String entityName) {
        DynamicObject saleoutBillObj = BusinessDataServiceHelper.loadSingle(billId, entityName);
        DynamicObjectCollection internalCol = saleoutBillObj.getDynamicObjectCollection("yd_internalentity");
        internalCol.clear();

        DynamicObjectType entryType = internalCol.getDynamicObjectType();

        ConvertDataService dataService = new ConvertDataService();
        String srcBillEntity = entityName;
        String tempId = billId;
        int seq = 1;
        // 遍历单据，获取下游单据数据
        Map<Long, List<BFRow>> dirTargetBills = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(tempId)});
        if (dirTargetBills.size() > 0) {
            for (BFRow targetRow : dirTargetBills.get(Long.valueOf(tempId))) {
                BFRowId targetBillRow = targetRow.getId();
                srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
                tempId = targetBillRow.getBillId().toString();
                DynamicObject entry = new DynamicObject(entryType);
                entry.set("yd_internalseq", seq);
                String billName = "";
                if ("im_purinbill".equals(srcBillEntity)) {
                    billName = "采购入库单";
                }
                if ("im_saloutbill".equals(srcBillEntity)) {
                    billName = "销售出库单";
                }
                entry.set("yd_internalbillname", billName);
                entry.set("yd_internalbilltype", srcBillEntity);
                entry.set("yd_internalbillid", tempId);
                DynamicObject tempBillObj = BusinessDataServiceHelper.loadSingle(tempId, srcBillEntity, "billno,org.name");
                entry.set("yd_internalbillno", tempBillObj.getString("billno"));
                entry.set("yd_internalorg", tempBillObj.getString("org.name"));
                internalCol.add(entry);
                seq++;

                saveDownStreamBill(tempId, srcBillEntity, seq, internalCol, entryType);
            }
            SaveServiceHelper.save(new DynamicObject[]{saleoutBillObj});// 保存
        }
    }

    /**
     * 保存下游单据
     * @author: hst
     * @createDate: 2024/06/17
     * @param id
     * @param srcBillEntity
     */
    public static void saveDownStreamBill (String id, String srcBillEntity, int seq, DynamicObjectCollection internalCol, DynamicObjectType entryType) {
        ConvertDataService dataService = new ConvertDataService();
        while(StringUtils.isNotEmpty(id)) {
            // 遍历单据，获取下游单据数据
            Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(id)});
            if (dirTargetBill.size() == 1) {
                Long tempLongId = dirTargetBill.keySet().iterator().next();
                List<BFRow> targetRow = dirTargetBill.get(tempLongId);
                BFRowId targetBillRow = targetRow.get(0).getId();
                srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
                id = targetBillRow.getBillId().toString();

                DynamicObject entry = new DynamicObject(entryType);
                entry.set("yd_internalseq", seq);
                String billName = "";
                if ("im_purinbill".equals(srcBillEntity)) {
                    billName = "采购入库单";
                }
                if ("im_saloutbill".equals(srcBillEntity)) {
                    billName = "销售出库单";
                }
                entry.set("yd_internalbillname", billName);
                entry.set("yd_internalbilltype", srcBillEntity);
                entry.set("yd_internalbillid", id);
                DynamicObject tempBillObj = BusinessDataServiceHelper.loadSingle(id, srcBillEntity, "billno,org.name");
                entry.set("yd_internalbillno", tempBillObj.getString("billno"));
                entry.set("yd_internalorg", tempBillObj.getString("org.name"));
                internalCol.add(entry);
            } else {
                id = "";
                srcBillEntity = "";
            }
            seq++;
        }
    }

    /**
     * 校验单据是否有下游单
     * @param id
     * @param srcBillEntity
     * @author: hst
     * @createDate: 2024/06/25
     */
    public static boolean checkBillHasDownStream (String id, String srcBillEntity) {
        // 获取下游单据数据
        Map<Long, List<BFRow>> dirTargetBills = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity,
                new Long[]{Long.parseLong(id)});
        if (Objects.nonNull(dirTargetBills) && dirTargetBills.size() > 0) {
            return true;
        }

        return false;
    }

    /**
     * 通过物料编码查找物料库存信息基础资料
     * @author: hst
     * @createDate: 2024/09/04
     */
    public static DynamicObject getMatInventoryById(String materialId) {
        return BizHelper.getSingleDynamicObjectFromCache(BillTypeHelper.BILLTYPE_MATINVENTORY, "id, number",
                QFilter.of("status='C' and enable = '1' and masterid = ?", materialId).toArray());
    }
}
