package kd.bos.tcbj.im.util;

import kd.bos.bill.IBillPlugin;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.exception.KDBizException;
import kd.bos.form.ClientProperties;
import kd.bos.form.IFormView;
import kd.bos.form.control.Control;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.control.Label;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.FieldEdit;
import kd.bos.form.field.LargeTextEdit;
import kd.bos.form.field.TextEdit;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.list.IListView;
import kd.bos.list.plugin.IListPlugin;
//import kd.bos.report.plugin.IReportFormPlugin;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * UI界面操作类
 * <AUTHOR>
 * @Description:
 * @date 2021-12-6
 */
public class UIUtils {

    /**
     * 设置表格冻结
     * @param formView 界面对象
     * @param entryKey 分录标识
     * @param columns 冻结字段
     * <AUTHOR>
     * @date 2022-9-27
     */
    public static void setFreeze(IFormView formView, String entryKey, String[] columns) {
        setColumnProp(formView, entryKey, columns, ClientProperties.IsFixed, true);
    }

    /**
     * 设置表格冻结
     * @param formView 界面对象
     * @param entryKey 分录标识
     * @param columns 冻结字段
     * <AUTHOR>
     * @date 2022-9-27
     */
    public static void setFreeze(IFormView formView, String entryKey, String[] columns, boolean isFreeze) {
        setColumnProp(formView, entryKey, columns, ClientProperties.IsFixed, isFreeze);
    }

    /**
     * 设置列字段锁定
     * @param formView 界面对象
     * @param columns 冻结字段
     * <AUTHOR>
     * @date 2022-10-19
     */
    public static void setLock(IFormView formView, String[] columns, boolean isLock) {
//        setColumnProp(formView, entryKey, columns, ClientProperties.Editable, isLock);
        for (String column : columns) {
            formView.setEnable(!isLock, -1, column);
        }
    }

    /**
     * 设置表格属性
     * @param formView 界面对象
     * @param entryKey 分录标识
     * @param columns 待处理字段
     * @param prop 属性值
     * <AUTHOR>
     * @date 2022-9-27
     */
    public static void setColumnProp(IFormView formView, String entryKey, String[] columns, String prop, Object value) {
        // 必填项控制
        Objects.requireNonNull(formView);
        Objects.requireNonNull(entryKey);
        Objects.requireNonNull(columns);

        // formView.updateControlMetadata("panel", Map<String（ClientProperties）, Object>); 可以通过updateControlMetadata的方式更新属性
        Control control = formView.getControl(entryKey);
        // 如果属于表格控件，才设置冻结列
        if (control instanceof EntryGrid) {
            EntryGrid entryGrid = ((EntryGrid) control);
            // 对需要设置冻结的列都进行冻结
            for (String column : columns) {
                entryGrid.setColumnProperty(column, prop, value);
            }
        }
    }

    /**
     * 获取菜单栏标识
     * @param plugin 插件
     * @return
     */
    public static String getMenuBar(AbstractFormPlugin plugin) {
        if (plugin instanceof IListPlugin) {
            return "toolbarap";
        }else if (plugin instanceof IBillPlugin) {
            return "tbmain";
//        }else if (plugin instanceof IReportFormPlugin) {
//            return "toolbarap";
        }
        return null;
    }

    /**
     * 获取某个分录的某个字段的数据集合
     * @param iDataModel 数据模型
     * @param entryId 分录标识
     * @param key 分录字段标识
     * @return 分录字段值集合
     * <AUTHOR>
     * @date 2022-1-5
     */
    public static List<Object> getEntryRowValues(IDataModel iDataModel, String entryId, String key) {
        int rowCount = iDataModel.getEntryRowCount(entryId);
        List<Object> list = new ArrayList<>();
        for (int i = 0; i < rowCount; i++) {
            Object value = iDataModel.getValue(key, i);
            list.add(value);
        }
        return list;
    }

    /**
     * 获取某个分录的某个字段的数据集合
     * @param iDataModel 数据模型
     * @param entryId 分录标识
     * @param key 分录字段标识
     * @return 分录字段值集合
     * <AUTHOR>
     * @date 2022-1-5
     */
    public static BigDecimal sumEntryRowValues(IDataModel iDataModel, String entryId, String key) {
        int rowCount = iDataModel.getEntryRowCount(entryId);
        return sumEntryRowValues(iDataModel, entryId, key, 0, rowCount);
    }

    /**
     * 获取某个分录的某个字段的数据集合
     * @param iDataModel 数据模型
     * @param entryId 分录标识
     * @param key 分录字段标识
     * @param begin 开始位置
     * @param end 结束位置
     * @return 分录字段值集合
     * <AUTHOR>
     * @date 2022-1-5
     */
    public static BigDecimal sumEntryRowValues(IDataModel iDataModel, String entryId, String key, int begin, int end) {
        BigDecimal totalValue = BigDecimalUtil.ZERO;
        for (int i = begin; i < end; i++) {
            BigDecimal value = BigDecimalUtil.getValue(iDataModel.getValue(key, i));
            totalValue = totalValue.add(value);
        }
        return totalValue;
    }

    public static int getRowCount(IDataModel iDataModel, String entryId) {
        return iDataModel.getEntryRowCount(entryId);
    }

    /**
     * 获取已经选择的值
     * @param iFormView 列表
     * <AUTHOR>
     * @date 2022-1-5
     */
    public static boolean checkSelected(IFormView iFormView) {
        return ((IListView) iFormView).getSelectedRows().size() != 0;
    }

    /**
     * 标签设置值
     * @param view view对象
     * @param fieldKey 标签字段
     * @param text 赋值内容
     * <AUTHOR>
     * @date 2022-7-23
     */
    public static void setLabelValue(IFormView view, String fieldKey, String text) {
        ((Label) view.getControl(fieldKey)).setText(text);
    }

    /**
     * 获取已经选择的值
     * @param iFormView 列表
     * <AUTHOR>
     * @date 2022-1-5
     */
    public static void checkSelected(IFormView iFormView, String errMsg) {
        if (!checkSelected(iFormView)) throw new KDBizException(errMsg);
    }

    public static List<Object> getSelectIds(IFormView iFormView) {
        return ((IListView) iFormView).getSelectedRows().stream().map(ListSelectedRow::getPrimaryKeyValue).distinct().collect(Collectors.toList());
    }

    /**
     * 设置字段必填性
     * @param iFormView 界面控件
     * @param fieldKeys 字段集合
     * <AUTHOR>
     * @date 2022-7-29
     */
    public static void setRequired(IFormView iFormView, String[] fieldKeys) {
        for (String fieldKey : fieldKeys) {
            Control control = iFormView.getControl(fieldKey);
            if (control instanceof FieldEdit) {
                ((FieldEdit) control).setMustInput(true);
            }
        }
    }
}
