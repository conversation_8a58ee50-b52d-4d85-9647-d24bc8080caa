package kd.bos.tcbj.im.vo;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 请求Map
 * <AUTHOR>
 * @Description: 便于构建请求信息
 * @date 2021-12-24
 */
public class RequestMap extends LinkedHashMap {
    private static final long serialVersionUID = 1L;

    public RequestMap() {}

    public RequestMap put(String key, Object value) {
        super.put(key, value);
        return this;
    }

    public static RequestMap to(Map map) {
        RequestMap requestMap = new RequestMap();
        requestMap.putAll(map);
        return requestMap;
    }
}
