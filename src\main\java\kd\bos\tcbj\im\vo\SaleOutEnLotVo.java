package kd.bos.tcbj.im.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @auditor yanzuli<PERSON>
 * @date 2022年12月21日
 * 
 */
public class SaleOutEnLotVo {
	private String matId;
	private String wsId;
	private String lot;
	private Date beginDate;
	private Date endDate;
	private BigDecimal qty;
	/** update by hst 2023/01/31 来源分录ID **/
	private String sourceEntryId;
	/** update by hst 2023/01/31 分录序号 **/
	private String seq;
	
	public SaleOutEnLotVo() {
		
	}
	
	public String getMatId() {
		return matId;
	}
	public void setMatId(String matId) {
		this.matId = matId;
	}
	public String getWsId() {
		return wsId;
	}
	public void setWsId(String wsId) {
		this.wsId = wsId;
	}
	public String getLot() {
		return lot;
	}
	public void setLot(String lot) {
		this.lot = lot;
	}
	public Date getBeginDate() {
		return beginDate;
	}
	public void setBeginDate(Date beginDate) {
		this.beginDate = beginDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public BigDecimal getQty() {
		return qty;
	}
	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}
	public String getSourceEntryId() {
		return sourceEntryId;
	}
	public void setSourceEntryId(String sourceEntryId) {
		this.sourceEntryId = sourceEntryId;
	}
	public String getSeq() {
		return seq;
	}
	public void setSeq(String seq) {
		this.seq = seq;
	}
}
