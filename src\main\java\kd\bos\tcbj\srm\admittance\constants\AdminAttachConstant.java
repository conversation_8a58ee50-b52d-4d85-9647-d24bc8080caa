package kd.bos.tcbj.srm.admittance.constants;

/**
 * @package: kd.bos.tcbj.srm.admittance.constants.AdminitAttachConstant
 * @className AdminitAttachConstant
 * @author: hst
 * @createDate: 2024/07/10
 * @description: 资质类型常量类
 * @version: v1.0
 */
public class AdminAttachConstant {
    /** 元数据标识 **/
    public static String ENTITY_NAME = "yd_srm_aptitudetype";
    /** 名称 **/
    public static String NAME_FIELD = "name";
    /** 资质类型 **/
    public static String TYPE_FIELD = "yd_typemap";
    // 业务单据附件枚举值
    public static String TYPEENUM_FIELD = "yd_typeenum";
    // 附件库资质类型枚举值
    public static String APTITUDEENUM_FIELD = "yd_aptitudeenum";
    // 是否显示
    public static String ISSHOW_FIELD = "yd_isshow";
}
