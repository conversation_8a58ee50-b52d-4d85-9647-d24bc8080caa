package kd.bos.tcbj.srm.admittance.constants;

/**
 * @package: kd.bos.tcbj.srm.admittance.constants.AdminAttachMapConstant
 * @className AdminAttachMapConstant
 * @author: hst
 * @createDate: 2024/07/10
 * @description: 资质分类映射常量类
 * @version: v1.0
 */
public class AdminAttachMapConstant {
    /** 元数据标识 **/
    public static String ENTITY_NAME = "yd_srm_aptitudemap";
    /** 资质类型枚举值 **/
    public static String TYPE_FIELD = "yd_sorttype";
    /** 匹配字段 **/
    public static String MATCH_FIELD = "yd_matchfield";
    /** 业务单据分录标识 **/
    public static String BILLENTRY_FIELD = "yd_billentryname";
    /** 业务单据附件类型字段名 **/
    public static String FIELDNAME_FIELD = "yd_fieldname";
    // 业务单据附件字段名
    public static String ATTACHFIELD_FIELD = "yd_attachfield";
    // 发证日期字段名
    public static String EFFECTNAME_FIELD = "yd_effectname";
    // 有效日期字段名
    public static String UNEFFECTNAME_FIELD = "yd_uneffectname";
    // 附件库元数据标识
    public static String ENTITYNAME_FIELD = "yd_entityname";
    // 附件库附件分录标识
    public static String ENTRYNAME_FILED = "yd_entryname";
    // 附件库分类字段名
    public static String TYPENAME_FIELD = "yd_typeename";
    // 附件库资质类型字段名
    public static String APTITUDETYPE_FIELD = "yd_aptitudetype";
    // 附件库资质名称字段名
    public static String APTITUDENAME_FIELD = "yd_aptitudename";
    // 附件库附件字段名
    public static String ATTACHNAME_FIELD = "yd_attachname";
    // 附件库签发日期字段名
    public static String ISSUENAME_FIELD = "yd_issuename";
    // 附件库有效日期至字段名
    public static String DATETONAME_FIELD = "yd_datetoname";
    // 重命名取值基础资料字段
    public static String RENAME_FIELD = "yd_renamefield";
    // 归属基础资料字段名
    public static String PARENTDATA_FIELD = "yd_parentdataname";
    // 来源单据ID字段
    public static String ORIBILL_FIELD = "yd_oribillid";
    // 来源分录ID字段
    public static String ORIENTRY_FIELD = "yd_orientryid";
    // 路径名称
    public static String PATHNAME_FIELD = "yd_pathname";
    // 来源单据类型
    public static String BILLTYPE_FIELD = "yd_billtype";

    public static String getBaseDataFields (String fieldName) {
        return fieldName + ".name sortname, "
                + fieldName + "." + TYPE_FIELD + " " + TYPE_FIELD + ", "
                + fieldName + "." + MATCH_FIELD + " " + MATCH_FIELD + ", "
                + fieldName + "." + BILLENTRY_FIELD + " " + BILLENTRY_FIELD + ", "
                + fieldName + "." + FIELDNAME_FIELD + " " + FIELDNAME_FIELD + ", "
                + fieldName + "." + ATTACHFIELD_FIELD + " " + ATTACHFIELD_FIELD + ", "
                + fieldName + "." + EFFECTNAME_FIELD + " " + EFFECTNAME_FIELD + ", "
                + fieldName + "." + UNEFFECTNAME_FIELD + " " + UNEFFECTNAME_FIELD + ", "
                + fieldName + "." + ENTITYNAME_FIELD + " " + ENTITYNAME_FIELD + ", "
                + fieldName + "." + ENTRYNAME_FILED + " " + ENTRYNAME_FILED + ", "
                + fieldName + "." + TYPENAME_FIELD + " " + TYPENAME_FIELD + ", "
                + fieldName + "." + APTITUDETYPE_FIELD + " " + APTITUDETYPE_FIELD + ", "
                + fieldName + "." + ATTACHNAME_FIELD + " " + ATTACHNAME_FIELD + ", "
                + fieldName + "." + ISSUENAME_FIELD + " " + ISSUENAME_FIELD + ", "
                + fieldName + "." + DATETONAME_FIELD + " " + DATETONAME_FIELD + ", "
                + fieldName + "." + APTITUDENAME_FIELD + " " + APTITUDENAME_FIELD + ", "
                + fieldName + "." + RENAME_FIELD + " " + RENAME_FIELD + ", "
                + fieldName + "." + PARENTDATA_FIELD + " " + PARENTDATA_FIELD + ", "
                + fieldName + "." + PATHNAME_FIELD + " " + PATHNAME_FIELD + ", "
                + fieldName + "." + ORIBILL_FIELD + " " + ORIBILL_FIELD + ", "
                + fieldName + "." + ORIENTRY_FIELD + " " + ORIENTRY_FIELD + ", "
                + fieldName + "." + BILLTYPE_FIELD + " " + BILLTYPE_FIELD;
    }
}
