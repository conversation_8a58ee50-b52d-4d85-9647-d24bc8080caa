package kd.bos.tcbj.srm.admittance.constants;

/**
 * @package（包）: kd.bos.tcbj.srm.admittance.constants.SumBillTempConstant
 * @className（类名称）: SumBillTempConstant
 * @description（类描述）: 临时采购申请常量类
 * @author（创建人）: hst
 * @createDate（创建时间）: 2024/05/25
 * @version（版本）: v1.0
 */
public class SumBillTempConstant {

    // 单据标识
    public static String MAIN_ENTITY = "yd_supsumbill_temp";
    // 单据编号
    public static String BILLNO_FIELD = "billno";
    // 单据状态
    public static String BILLSTATUS_FIELD = "billstatus";
    // 创建人
    public static String CREATOR_FIELD = "creator";
    // 创建时间
    public static String CREATETIME_FIELD = "createtime";
    // 单据类型
    public static String BILLTYPE_FIELD = "yd_billtype";
    // 物料
    public static String MATERIAL_FIELD = "yd_material";
    // 供应商
    public static String SUPPLIER_FILED = "yd_supplier";
    // 生产商
    public static String PRONAME_FILED = "yd_supname";
    // 合格供应商台账编码
    public static String BIZNO_FIELD = "yd_billno";
    // 源合格供应商目录ID
    public static String BIZID_FIELD = "yd_sourceid";
    // 旧供应商状态
    public static String OLDSTATUS_FIELD = "yd_supstatus";
    // 新合格供应商目录状态
    public static String NEWSTATUS_FIELD = "yd_newstatus";
    // 申请说明
    public static String EXPLAIN_FIELD = "yd_explain";
    // 是否已关闭
    public static String ISCLOSE_FIELD = "yd_isclose";

    public static String getField() {
        return BILLNO_FIELD + "," + BILLSTATUS_FIELD + "," + CREATOR_FIELD + "," + BILLTYPE_FIELD
                + "," + MATERIAL_FIELD + "," + SUPPLIER_FILED + "," + PRONAME_FILED
                + "," + BIZNO_FIELD + "," + BIZID_FIELD + "," + OLDSTATUS_FIELD + "," + NEWSTATUS_FIELD
                + "," + EXPLAIN_FIELD + "," + ISCLOSE_FIELD;
    }
}
