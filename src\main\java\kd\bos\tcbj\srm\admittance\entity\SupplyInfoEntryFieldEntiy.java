package kd.bos.tcbj.srm.admittance.entity;

import java.io.Serializable;

/**
 * 资料补充函分录字段实体
 * @auditor yanzu<PERSON>
 * @date 2022年6月27日
 * 
 */
public class SupplyInfoEntryFieldEntiy implements Serializable{
	/* 单据体标识 */
	private String entryEntityName;
	/* 附件字段_字段名 */
	private String fileField;
	/* 生效日期_字段名 */
	private String effectField;
	/* 失效日期_字段名 */
	private String unEffectField;
	/* 附件类型_字段名 */
	private String fileTypeField;
	/* 是否重命名_字段名 */
	private String renameField;
	/* 是否已归档_字段名 */
	private String isUploadField;
	/* 来源单据ID */
	private String oriBillIdField;
	/* 来源单据分录ID */
	private String oriEntryIdField;
	/* 来源单据类型 */
	private String billType;

	public SupplyInfoEntryFieldEntiy(String entryEntityName, String fileField, String fileTypeField,
                                     String effectField, String unEffectField) {
		this.entryEntityName = entryEntityName;
		this.fileField = fileField;
		this.fileTypeField = fileTypeField;
		this.effectField = effectField;
		this.unEffectField = unEffectField;
	}

	public SupplyInfoEntryFieldEntiy(String entryEntityName, String fileField, String fileTypeField,
                                     String effectField, String unEffectField, String renameField,
                                     String isUploadField, String oriBillIdField, String oriEntryIdField,
									 String billType) {
		this.entryEntityName = entryEntityName;
		this.fileField = fileField;
		this.fileTypeField = fileTypeField;
		this.effectField = effectField;
		this.unEffectField = unEffectField;
		this.renameField = renameField;
		this.isUploadField = isUploadField;
		this.oriBillIdField = oriBillIdField;
		this.oriEntryIdField = oriEntryIdField;
		this.billType = billType;
	}

	public String getFileTypeField() {return fileTypeField;}
	public void setFileTypeField(String fileTypeField) {this.fileTypeField = fileTypeField;}
	public String getEntryEntityName() {return entryEntityName;}
	public void setEntryEntityName(String entryEntityName) {this.entryEntityName = entryEntityName;}
	public String getFileField() {return fileField;}
	public void setFileField(String fileField) {this.fileField = fileField;}
	public String getEffectField() {return effectField;}
	public void setEffectField(String effectField) {this.effectField = effectField;}
	public String getUnEffectField() {return unEffectField;}
	public void setUnEffectField(String unEffectField) {this.unEffectField = unEffectField;}

	public String getOriBillIdField() {return oriBillIdField;}

	public void setOriBillIdField(String oriBillIdField) {this.oriBillIdField = oriBillIdField;}

	public String getRenameField() {return renameField;}

	public void setRenameField(String renameField) {this.renameField = renameField;}

	public String getIsUploadField() {return isUploadField;}

	public void setIsUploadField(String isUploadField) {this.isUploadField = isUploadField;}

	public String getOriEntryIdField() {return oriEntryIdField;}

	public void setOriEntryIdField(String oriEntryIdField) {this.oriEntryIdField = oriEntryIdField;}

	public String getBillType() {return billType;}

	public void setBillType(String billType) {this.billType = billType;}
}
