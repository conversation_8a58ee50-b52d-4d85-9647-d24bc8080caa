package kd.bos.tcbj.srm.admittance.enums;
/**
 * @auditor yanzu<PERSON>
 * @date 2022年6月24日
 * 
 */
public enum AgentTypeEnum {
	
	   A("营业执照","A"),
	   B("经营许可证","B"),
	   C("代理证明","C"),
	   D("其他","D"); 
	  private String name=null;
	  private String value=null;
	  public String getValue() {
		  return this.value;
	  }
	  public String getName()
	  {
		  return this.name;
	  } 	  
	  AgentTypeEnum(String name, String val) 
	  {
		 this.name = name;
		 this.value = val;
	  }  
	  public static AgentTypeEnum fromVal(String val) {
	    for (AgentTypeEnum status : values()) {
	      if (val.equals(status.getValue()))
	        return status; 
	    } 
	    return null;
	  }
}
