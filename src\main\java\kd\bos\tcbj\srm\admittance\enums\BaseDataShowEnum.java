package kd.bos.tcbj.srm.admittance.enums;

import java.util.Arrays;

/**
 * @package: kd.bos.tcbj.srm.admittance.common.enums.BaseDataShowEnum
 * @className BaseDataShowEnum
 * @author: hst
 * @createDate: 2024/07/22
 * @description: 基础资料映射单据枚举
 * @version: v1.0
 */
public enum BaseDataShowEnum {
    // 资料补充函
    SUP_INFO_BASE("yd_supinfobase","yd_supplyinformationbill"),
    // 原辅料及供应商新增/变更申请
    RAW_MAT_SUP_ACCESS("yd_rawmatsupaccessbase","yd_rawmatsupaccessbill"),
    // 新物料需求及供应商评价
    NEW_MAT_REQ_BASE("yd_newreqbase","yd_newmatreqbill");

    private String code;
    private String value;

    public static BaseDataShowEnum getInstance(String code){
        return Arrays.stream(BaseDataShowEnum.values())
                .filter(currencyEnum -> currencyEnum.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    BaseDataShowEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
