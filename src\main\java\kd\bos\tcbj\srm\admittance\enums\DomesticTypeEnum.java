package kd.bos.tcbj.srm.admittance.enums;

import java.util.Objects; 
/**
 * @auditor ya<PERSON><PERSON><PERSON>
 * @date 2022年6月24日
 * 
 */
public enum DomesticTypeEnum
{
	   A("营业执照","A"),
	   B("药品级生产许可","B"),
	   C("食品级生产许可","C"),
	   D("保健食品的提取物生产许可","D"),
	   E("其他","E"); 
	
	  private String name=null;
	  private String value=null;
	  public String getValue() {
		  return this.value;
	  }
	  public String getName()
	  {
		  return this.name;
	  } 	  
	  DomesticTypeEnum(String name, String val) 
	  {
		 this.name = name;
		 this.value = val;
	  }  
	  public static DomesticTypeEnum fromVal(String val) {
	    for (DomesticTypeEnum status : values()) {
	      if (val.equals(status.getValue()))
	        return status; 
	    } 
	    return null;
	  }
}
