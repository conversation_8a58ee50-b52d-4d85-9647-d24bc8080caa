package kd.bos.tcbj.srm.admittance.enums;

import java.util.Objects; 
/**
 * @auditor yanzu<PERSON>
 * @date 2022年6月24日
 * 
 */
public enum ForeignTypeEnum
{
	   A("食品级","A"),
	   B("药品级","B"),
	   C("其他","C"); 
	
	  private String name=null;
	  private String value=null;
	  public String getValue() {
		  return this.value;
	  }
	  public String getName()
	  {
		  return this.name;
	  } 	  
	  ForeignTypeEnum(String name, String val) 
	  {
		 this.name = name;
		 this.value = val;
	  }  
	  public static ForeignTypeEnum fromVal(String val) {
	    for (ForeignTypeEnum status : values()) {
	      if (val.equals(status.getValue()))
	        return status; 
	    } 
	    return null;
	  }
}
