package kd.bos.tcbj.srm.admittance.enums;
/**
 * @auditor yanzuwei
 * @date 2022年6月24日
 * 
 */

import java.util.HashMap;
import java.util.Map;

public enum ProcessfitintoTypeEnum 
{
	   A("产品稳定性报告","A"),
	   B("第三方型式检验报告","B"),
	   C("包装标签模板","C"),
	   D("转基因原料类","D"),
	   E("菌种/藻类","E"),
	   F("微生物高风险物料","F"), 
	   G("植物物原料","G"), 
	   H("动物原料","H"), 
	   I("食品原料","I"),  
	   J("其他","J");  
	
	  private String name=null;
	  private String value=null;
	  public String getValue() {
		  return this.value;
	  }
	  public String getName()
	  {
		  return this.name;
	  } 	  
	  ProcessfitintoTypeEnum(String name, String val) 
	  {
		 this.name = name;
		 this.value = val;
	  }  
	  public static ProcessfitintoTypeEnum fromVal(String val) {
	    for (ProcessfitintoTypeEnum status : values()) {
	      if (val.equals(status.getValue()))
	        return status; 
	    } 
	    return null;
	  }

}
