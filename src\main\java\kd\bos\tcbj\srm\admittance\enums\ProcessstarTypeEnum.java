package kd.bos.tcbj.srm.admittance.enums;
/**
 * @auditor yanzuwei
 * @date 2022年6月24日
 * 
 */
public enum ProcessstarTypeEnum 
{

	   A("供应商书面调查表","A"),
	   B("物料描述表","B"),
	   C("3个批次样品 COA","C"),
	   D("样品我司检测报告","D"),
	   E("生产工艺流程图","E"),
	   F("厂家企业标准","F"), 
	   G("其他","G"); 
	 
	
	  private String name=null;
	  private String value=null;
	  public String getValue() {
		  return this.value;
	  }
	  public String getName()
	  {
		  return this.name;
	  } 	  
	  ProcessstarTypeEnum(String name, String val) 
	  {
		 this.name = name;
		 this.value = val;
	  }  
	  public static ProcessstarTypeEnum fromVal(String val) {
	    for (ProcessstarTypeEnum status : values()) {
	      if (val.equals(status.getValue()))
	        return status; 
	    } 
	    return null;
	  }
}
