package kd.bos.tcbj.srm.admittance.enums;
/**
 * @auditor yanzuwei
 * @date 2022年6月24日
 * 
 */
public enum SupDicEnum 
{
       A("批准使用","1"),
	   B("紧急时使用","2"),
	   C("要启用再审批","3"),
	   D("暂不启用","4"),
	   A5("资质评估","5"),
	   A6("试产物料采购","6"),
	   A7("小试评估","7"),
	   A9("批准使用","9"),  
	   A12("暂停采购","12"),
	   A13("停止采购","13"),
	   A14("确认中试信息","14"),
	   A15("确认供应商书面信息","15"),
	   A16("书面审核结论","16"),
	   A17("试产物料采购阶段","17"),
	   A18("中试反馈","18"),
	   A19("新增目录","19"),
	   A20("研发新增不通过","20"),
	   A21("研发合格供应商","21"),
	   A22("资质评估","22"),
	   A23("试产物料采购-小试","23"),
	   A24("小试评估","24"),
	   A25("试产物料采购-中试","25"),
	   A26("中试评估","26"),
	   A27("稳定性考察","27"),
	   A28("流程审批","28"),
	   A29("备用供应商","29");

	
	  private String name=null;
	  private String value=null;
	  public String getValue() {
		  return this.value;
	  }
	  public String getName()
	  {
		  return this.name;
	  } 	  
	  SupDicEnum(String name, String val) 
	  {
		 this.name = name;
		 this.value = val;
	  }  
	  public static SupDicEnum fromVal(String val) {
	    for (SupDicEnum status : values()) {
	      if (val.equals(status.getValue()))
	        return status; 
	    } 
	    return null;
	  }
}
