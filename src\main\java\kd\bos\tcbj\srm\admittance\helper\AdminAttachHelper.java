package kd.bos.tcbj.srm.admittance.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.fileservice.FileItem;
import kd.bos.fileservice.FileService;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.attachment.AttachmentFieldServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachConstant;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachMapConstant;
import kd.bos.tcbj.srm.admittance.entity.SupplyInfoEntryFieldEntiy;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.admittance.vo.AdminAttachInfoVo;
import kd.bos.tcbj.srm.admittance.vo.AdminAttachSetVo;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper
 * @className AdminAttachHelper
 * @author: hst
 * @createDate: 2024/07/10
 * @description: 资质类型业务类
 * @version: v1.0
 */
public class AdminAttachHelper {

    private static Log logger = LogFactory.getLog(AdminAttachHelper.class.getName());

    /**
     * 更新附件库中准入附件
     * @param bills
     * @author: hst
     * @createDate: 2024/07/10
     */
    public static void addAdminAttach (DynamicObject[] bills) {
        Map<String, AdminAttachSetVo> typeMap = getTypeMap();
        for (DynamicObject bill : bills) {
            // 提取分录资质信息
            Map<String, AdminAttachInfoVo> entryDetails = getAdminAttachInfoVo(bill, typeMap);

            // 已反写的记录
            Set<String> records = new HashSet<>();
            // 需要反写的附件库
            Map<String, DynamicObject> baseDatas = new HashMap<>();
            for (Map.Entry<String, AdminAttachInfoVo> infoVo : entryDetails.entrySet()) {
                String[] keys = infoVo.getKey().split("&");
                // 基础资料字段
                String baseField = keys[0];
                // 附件库标识
                String tarEntityField = keys[1];

                DynamicObject baseData = null;
                if (!baseDatas.containsKey(baseField)) {
                    DynamicObject object = bill.getDynamicObject(baseField);
                    if (Objects.nonNull(object)) {
                        baseData = BusinessDataServiceHelper.loadSingle(object.getString("id"), tarEntityField);
                        if (Objects.nonNull(baseData)) {
                            baseDatas.put(baseField, baseData);
                        } else {
                            throw new KDBizException(baseField + "获取不到对应基础资料信息");
                        }
                    }
                } else {
                    baseData = baseDatas.get(baseField);
                }

                if (Objects.nonNull(baseData)) {
                    DynamicObjectCollection entries = baseData.getDynamicObjectCollection(infoVo.getValue().getTarEntry());
                    for (DynamicObject entry : entries) {
                        records.add(entry.getString("yd_billkey") + "&" + entry.getString("yd_billentrykey"));
                    }
                }
            }

            // 资质归档，保存到附件库
            qualificationFiling(bill, entryDetails, baseDatas, records, "yd_supinfobase");
        }
        SaveServiceHelper.save(bills);
    }

    /**
     * 资质重命名
     * @param bills
     * @author: hst
     * @createDate: 2024/07/21
     */
    public static void reNameAdminAttach(DynamicObject[] bills) {
        Map<String, AdminAttachSetVo> typeMap = getTypeMap();
        for (DynamicObject bill : bills) {
            // 提取分录资质信息
            Map<String, AdminAttachInfoVo> entryDetails = getAdminAttachInfoVo(bill, typeMap);

            Map<String, String> sortNames = new HashMap<>();
            Map<String, String> sortNumbers = new HashMap<>();
            for (Map.Entry<String, AdminAttachInfoVo> infoVo : entryDetails.entrySet()) {
                AdminAttachInfoVo info = infoVo.getValue();

                String baseField = info.getReNameField();
                if (!sortNames.containsKey(baseField)) {
                    DynamicObject object = bill.getDynamicObject(baseField);
                    if (Objects.nonNull(object)) {
                        DynamicObject baseData = BusinessDataServiceHelper.loadSingle(object.getString("id"), object.getDynamicObjectType());
                        if (Objects.nonNull(baseData)) {
                            String name = baseData.getString("name");
                            String number = baseData.getString("number");
                            sortNames.put(baseField, name);
                            sortNumbers.put(baseField, number);
                        } else {
                            throw new KDBizException(baseField + "获取不到对应基础资料名称");
                        }
                    }
                }
            }

            // 附件重命名
            reSetAttachmentName(bill, entryDetails, sortNames, sortNumbers);
        }

        SaveServiceHelper.save(bills);
    }

    /**
     * 删除资质库附件
     * @param bills
     * @author: hst
     * @createDate: 2024/07/21
     */
    public static void deleteAdminAttach(DynamicObject[] bills) {
        Map<String, AdminAttachSetVo> typeMap = getTypeMap();
        for (DynamicObject bill : bills) {
            String billId = bill.getString("id");
            Map<String, Set<String>> entryDetails = new HashMap<>();

            // 先获取所有相关的分录
            Set<String> entryKeys = typeMap.entrySet().stream().map(entry -> {
                String[] types = entry.getKey().split("&");
                AdminAttachSetVo setVo = entry.getValue();
                return types[0] + "&" + types[1] + "&" + types[2] + "&" +setVo.getEntryName() + "&" + types[4];
            }).collect(Collectors.toSet());
            // 需要删除附件记录的附件库
            Set<String> deleteEntryKeys = new HashSet<>();
            for (String entryKey : entryKeys) {
                String[] keys = entryKey.split("&");
                DynamicObjectCollection details = bill.getDynamicObjectCollection(entryKey.split("&")[2]);
                for (DynamicObject detail : details) {
                    // 附件库标识
                    String tarEntity = keys[1];
                    // 附件库分录标识
                    String tarEntry = keys[3];
                    String entryId = detail.getString("id");

                    if (entryDetails.containsKey(tarEntity + "&" + tarEntry)) {
                        Set<String> temp = entryDetails.get(tarEntity + "&" + tarEntry);
                        temp.add(billId + "&" + entryId);
                        entryDetails.put(tarEntity + "&" + tarEntry, temp);
                    } else {
                        Set<String> temp = new HashSet<>();
                        temp.add(billId + "&" + entryId);
                        entryDetails.put(tarEntity + "&" + tarEntry, temp);
                    }

                    deleteEntryKeys.add(entryKey);
                    detail.set(keys[4] + "_up", false);
                }
            }

            Map<String, DynamicObject> baseDatas = new HashMap<>();
            for (String entryKey : deleteEntryKeys) {
                String[] keys = entryKey.split("&");
                // 基础资料字段名
                String baseField = keys[0];
                // 附件库标识
                String tarEntity = keys[1];
                // 附件库分录标识
                String tarEntry = keys[3];

                if (!baseDatas.containsKey(baseField)) {
                    DynamicObject object = bill.getDynamicObject(baseField);
                    if (Objects.nonNull(object)) {
                        DynamicObject baseData = BusinessDataServiceHelper.loadSingle(object.getString("id"), tarEntity);
                        if (Objects.nonNull(baseData)) {
                            baseDatas.put(tarEntity + "&" + tarEntry, baseData);
                        } else {
                            throw new KDBizException(baseField + "获取不到对应基础资料信息");
                        }
                    } else {
                        throw new KDBizException(baseField + "不能为空");
                    }
                }
            }

            // 删除附件记录
            for (Map.Entry<String, DynamicObject> baseData : baseDatas.entrySet()) {
                String[] keys = baseData.getKey().split("&");
                // 附件库标识
                String tarEntity = keys[0];
                // 附件库分录标识
                String tarEntry = keys[1];
                DynamicObject data = baseData.getValue();
                Set<String> details = entryDetails.get(tarEntity + "&" + tarEntry);

                DynamicObjectCollection entries = data.getDynamicObjectCollection(tarEntry);
                DynamicObjectCollection deleteEntries = new DynamicObjectCollection(entries.getDynamicObjectType(),
                        entries.getParent());
                for (DynamicObject entry : entries) {
                    String oribillId = entry.getString("yd_billkey");
                    String oriEntryId = entry.getString("yd_billentrykey");

                    if (Objects.nonNull(details) && details.contains(oribillId + "&" + oriEntryId)) {
                        deleteEntries.add(entry);
                    }
                }

                entries.removeAll(deleteEntries);
            }

            // 保存数据
            for (Map.Entry<String,DynamicObject> baseData : baseDatas.entrySet()) {
                OperationResult result = saveBaseData(baseData.getValue());

                if (!result.isSuccess()) {
                    throw new KDBizException("删除" + baseData.getKey() + "类型附件失败，原因：" + result.getMessage());
                }
            }
        }

        SaveServiceHelper.save(bills);
    }

    /**
     * 获取资质类型配置信息
     * @return
     * @author: hst
     * @createDate: 2024/07/10
     */
    public static Map<String, AdminAttachSetVo> getTypeMap () {
        DataSet aptitudeTypes = getTypeMapInfo(null);
        if (aptitudeTypes.hasNext()) {
            Map<String, AdminAttachSetVo> typeMap = new HashMap<>();
            for (Row row : aptitudeTypes) {
                // 匹配字段
                String matchName = row.getString(AdminAttachMapConstant.MATCH_FIELD);
                // 附件库元数据标识
                String entityName = row.getString(AdminAttachMapConstant.ENTITYNAME_FIELD);
                // 资料补充函附件分录标识'
                String billEntryName = row.getString(AdminAttachMapConstant.BILLENTRY_FIELD);
                // 资料补充附件类型字段名
                String fieldName = row.getString(AdminAttachMapConstant.FIELDNAME_FIELD);
                // 资料补充函附件字段名
                String attachField = row.getString(AdminAttachMapConstant.ATTACHFIELD_FIELD);
                // 资料补充函附件枚举值
                String typeEnum = row.getString(AdminAttachConstant.TYPEENUM_FIELD);

                String key = matchName + "&" + entityName + "&" + billEntryName + "&" + fieldName + "&" + attachField + "&" + typeEnum;

                if (!typeMap.containsKey(key)) {
                    AdminAttachSetVo adminAttachVo = new AdminAttachSetVo();
                    // 匹配字段
                    adminAttachVo.setMatchField(row.getString(AdminAttachMapConstant.MATCH_FIELD));
                    // 资质类型
                    adminAttachVo.setType(row.getString(AdminAttachMapConstant.TYPE_FIELD));
                    // 资质名称
                    adminAttachVo.setName(row.getString(AdminAttachConstant.NAME_FIELD));
                    // 发证日期字段名
                    adminAttachVo.setEffectName(row.getString(AdminAttachMapConstant.EFFECTNAME_FIELD));
                    // 有效日期字段名
                    adminAttachVo.setUnEffectName(row.getString(AdminAttachMapConstant.UNEFFECTNAME_FIELD));
                    // 附件库附件分录标识
                    adminAttachVo.setEntryName(row.getString(AdminAttachMapConstant.ENTRYNAME_FILED));
                    // 附件库分类字段名
                    adminAttachVo.setTypeName(row.getString(AdminAttachMapConstant.TYPENAME_FIELD));
                    // 附件库资质类型字段名
                    adminAttachVo.setAptitudeType(row.getString(AdminAttachMapConstant.APTITUDETYPE_FIELD));
                    // 附件库资质名称字段名
                    adminAttachVo.setAptitudeName(row.getString(AdminAttachMapConstant.APTITUDENAME_FIELD));
                    // 附件库元数据标识
                    adminAttachVo.setEntityName(row.getString(AdminAttachMapConstant.ENTITYNAME_FIELD));
                    // 附件库资质类型枚举值
                    adminAttachVo.setAptitudeEnum(row.getString(AdminAttachConstant.APTITUDEENUM_FIELD));
                    // 附件库附件字段名
                    adminAttachVo.setAttachName(row.getString(AdminAttachMapConstant.ATTACHNAME_FIELD));
                    // 附件库签发日期字段名
                    adminAttachVo.setIsSueName(row.getString(AdminAttachMapConstant.ISSUENAME_FIELD));
                    // 附件库有效日期至字段名
                    adminAttachVo.setDateToName(row.getString(AdminAttachMapConstant.DATETONAME_FIELD));
                    // 重命名取值基础资料字段
                    adminAttachVo.setReName(row.getString(AdminAttachMapConstant.RENAME_FIELD));
                    // 归属基础资料字段名
                    adminAttachVo.setParentDataField(row.getString(AdminAttachMapConstant.PARENTDATA_FIELD));
                    // 路径名称
                    adminAttachVo.setPathName(row.getString(AdminAttachMapConstant.PATHNAME_FIELD));

                    typeMap.put(key, adminAttachVo);
                }
            }
            return typeMap;
        } else {
            throw new KDBizException("未查询到资质类型配置信息，请联系管理员");
        }
    }

    /**
     * 获取资质类型信息
     * @param qFilter
     * @author: hst
     * @createDate: 2024/07/11
     */
    public static DataSet getTypeMapInfo (QFilter qFilter) {
        QFilter filter = new QFilter("status", QFilter.equals, "C");
        if (Objects.nonNull(qFilter)) {
            filter = filter.and(qFilter);
        }

        String mapFields = AdminAttachMapConstant.getBaseDataFields(AdminAttachConstant.TYPE_FIELD);
        DataSet aptitudeTypes = QueryServiceHelper.queryDataSet(AdminAttachHelper.class.getName(),AdminAttachConstant.ENTITY_NAME,
                String.join(",", DynamicObjectUtil.getAllField(AdminAttachConstant.ENTITY_NAME)) + "," + mapFields,
                new QFilter[]{filter}, null);

        return aptitudeTypes;
    }

    /**
     * 字段赋值，判断字段名是否为空
     * @author: hst
     * @createDate: 2024/07/11
     */
    private static void setValue (DynamicObject dynamicObject, String field, Object value) {
        if (StringUtils.isNotBlank(field)) {
            dynamicObject.set(field, value);
        }
    }

    /**
     * 复制附件
     * @param oriCollection
     * @param tarCollection
     * @author: hst
     * @createDate: 2024/07/11
     */
    private static void copyAttach (DynamicObjectCollection oriCollection, DynamicObjectCollection tarCollection) {
        for (DynamicObject oriDynamicObject : oriCollection) {
            tarCollection.addNew().set("fbasedataid", oriDynamicObject.get("fbasedataid"));
        }
    }

    /**
     * 提取分录资质信息
     * @param bill
     * @param typeMap
     * @author: hst
     * @createDate: 2024/07/21
     */
    private static Map<String, AdminAttachInfoVo> getAdminAttachInfoVo(DynamicObject bill, Map<String, AdminAttachSetVo> typeMap) {
        Map<String, AdminAttachInfoVo> entryDetails = new HashMap<>();

        // 先获取所有相关的分录
        Set<String> entryKeys = typeMap.keySet().stream().map(type -> {
            String[] types = type.split("&");
            return types[0] + "&" + types[1] + "&" + types[2] + "&" + types[3] + "&" + types[4];
        }).collect(Collectors.toSet());

        for (String entryKey : entryKeys) {
            String[] keys = entryKey.split("&");
            DynamicObjectCollection details = bill.getDynamicObjectCollection(entryKey.split("&")[2]);
            for (DynamicObject detail : details) {
                String type = detail.getString(keys[3]);

                AdminAttachSetVo setVo = typeMap.get(entryKey + "&" + type);

                AdminAttachInfoVo infoVo = new AdminAttachInfoVo();
                infoVo.setTarEntity(keys[1]);
                infoVo.setMatchField(setVo.getMatchField());
                infoVo.setBillId(bill.getString("id"));
                infoVo.setBillNo(bill.getString("billno"));
                infoVo.setEntryId(detail.getString("id"));
                infoVo.setTarEntry(setVo.getEntryName());
                infoVo.setTarSortField(setVo.getTypeName());
                infoVo.setTarSort(setVo.getType());
                infoVo.setTarAptField(setVo.getAptitudeType());
                infoVo.setTarAptSort(setVo.getAptitudeEnum());
                infoVo.setTarAptNameField(setVo.getAptitudeName());
                infoVo.setTarAptName(setVo.getName());
                infoVo.setTarAttachField(setVo.getAttachName());
                infoVo.setAttachCollection(detail.getDynamicObjectCollection(keys[4]));
                infoVo.setIsSueDateField(setVo.getIsSueName());
                infoVo.setIsSueDate(detail.getDate(setVo.getEffectName()));
                infoVo.setDateToField(setVo.getDateToName());
                infoVo.setDateTo(detail.getDate(setVo.getUnEffectName()));
                infoVo.setReNameField(setVo.getReName());
                infoVo.setPathName(setVo.getPathName());
                infoVo.setParentDataField(setVo.getParentDataField());
                infoVo.setOriEntry(keys[2]);
                infoVo.setIsReNameField(keys[4] + "_re");
                infoVo.setIsAlreadField(keys[4] + "_up");
                infoVo.setOriSeq(detail.getInt("seq"));

                entryDetails.put(entryKey + "&" +detail.getString("seq"), infoVo);
            }
        }

        return entryDetails;
    }

    /**
     * 提取归档资质信息
     * @param bill
     * @param typeMap
     * @author: hst
     * @createDate: 2024/07/21
     */
    public static Map<String, AdminAttachInfoVo> getDocFillInfoVo(DynamicObject bill, Map<String, AdminAttachSetVo> typeMap) {
        Map<String, AdminAttachInfoVo> entryDetails = new HashMap<>();

        DynamicObjectCollection details = bill.getDynamicObjectCollection("yd_doucmententry");
        for (DynamicObject detail : details) {
            boolean isFile = detail.getBoolean("yd_isfile");

            if (!isFile) {
                continue;
            }

            String type = detail.getString("yd_nextlevel");

            if (typeMap.containsKey(type)) {
                String[] keys = type.split("-");
                AdminAttachSetVo setVo = typeMap.get(type);

                AdminAttachInfoVo infoVo = new AdminAttachInfoVo();
                infoVo.setTarEntity(keys[0]);
                infoVo.setMatchField(setVo.getMatchField());
                infoVo.setBillId(bill.getString("id"));
                infoVo.setBillNo(bill.getString("billno"));
                infoVo.setEntryId(detail.getString("id"));
                infoVo.setTarEntry(setVo.getEntryName());
                infoVo.setTarSortField(setVo.getTypeName());
                infoVo.setTarSort(setVo.getType());
                infoVo.setTarAptField(setVo.getAptitudeType());
                infoVo.setTarAptSort(setVo.getAptitudeEnum());
                infoVo.setTarAptNameField(setVo.getAptitudeName());
                infoVo.setTarAptName(setVo.getName());
                infoVo.setTarAttachField(setVo.getAttachName());
                infoVo.setAttachCollection(detail.getDynamicObjectCollection("yd_document"));
                infoVo.setIsSueDateField(setVo.getIsSueName());
                infoVo.setIsSueDate(detail.getDate(setVo.getEffectName()));
                infoVo.setDateToField(setVo.getDateToName());
                infoVo.setDateTo(detail.getDate(setVo.getUnEffectName()));
                infoVo.setReNameField(setVo.getReName());
                infoVo.setPathName(setVo.getPathName());
                infoVo.setParentDataField(setVo.getParentDataField());
                infoVo.setOriEntry("yd_doucmententry");
                infoVo.setIsReNameField("yd_isrename");
                infoVo.setIsAlreadField("yd_isalready");
                infoVo.setOriSeq(detail.getInt("seq"));
                infoVo.setExt1(detail.getString("yd_orientryid"));

                entryDetails.put(type + "&" +detail.getString("seq"), infoVo);
            }
        }

        return entryDetails;
    }

    /**
     * 格式化附件名称
     * @param attachment
     * @param fileName
     * @param sort
     * @param type
     * @author: hst
     * @createDate: 2024/07/21
     */
    public static boolean formatAdminAttachName (DynamicObject attachment, String fileName, String type, String sort,
                                                 String aptNum, boolean isDelete) {
        boolean isSuccess = true;

        DynamicObject baseData = attachment.getDynamicObject("fbasedataid");
        String baseDataId = baseData.getString("id");

        // 获取文件类型
        String extName = baseData.getString("type");

        // 文件路径
        String url = baseData.getString("url");
        String path = getPathFromDownloadUrl(url);

        // 校验文件是否存在
        FileService fs = FileServiceFactory.getAttachmentFileService();
        boolean isExist = fs.exists(fs.getFileServiceExt().getRealPath(path));
        if (!isExist) {
            throw new KDBizException("重命名" + fileName + "资质文件失败，文件不存在，请联系管理员");
        }

        InputStream inputStream = fs.getInputStream(path);
        // 生成文件路径-上传附件时远程服务器需要存储文件的位置
        String pathParam = String.format("/%s/%s/%s/%s/%s/%s/%s/%s", RequestContext.get().getTenantId(), RequestContext.get().getAccountId(), "srm",
                formatPath(type), formatPath(sort), aptNum, baseDataId, formatPath(fileName + "." + extName));

        FileItem fi = new FileItem(fileName, pathParam, inputStream);
        fi.setCreateNewFileWhenExists(false);
        String tarPath = fs.upload(fi);

        // 校验文件是否存在
        isExist = fs.exists(fs.getFileServiceExt().getRealPath(tarPath));
        if (StringUtils.isNotBlank(tarPath) && isExist) {
            // 修改附件名
            AttachmentFieldServiceHelper.rename(baseData.getString("uid"), fileName + "." + extName);

            // 修改附件路径映射关系
            reUrl(path, tarPath);

            if (isDelete && !fs.getFileServiceExt().getRealPath(path)
                    .equals(fs.getFileServiceExt().getRealPath(tarPath))) {
                // 删除附件
                fs.delete(path);
            }
        } else {
            throw new KDBizException("重命名" + fileName + "资质文件失败，新文件保存失败，请联系管理员");
        }

        return isSuccess;
    }

    /**
     * 获取原始下载路径
     * @param url
     * @return
     * @author: hst
     * @createDate: 2024/07/21
     */
    public static String getPathFromDownloadUrl (String url) {
        try {
            String newUrl = URLDecoder.decode(url, "UTF-8");
            String path = newUrl.contains("path=") ? StringUtils.substringAfter(newUrl, "path=") : newUrl;
            path = path.replace("/", "");
            return path;
        } catch (Exception e) {
            throw new KDBizException("附件解码失败，获取原始下载路径失败");
        }
    }

    /**
     * 格式化路径
     * @param path
     * @return
     * @author: hst
     * @createDate: 2024/07/21
     */
    public static String formatPath (String path) {
        try {
            String newPath = path.replaceAll("[&#$%^!/:<>\"|*?]", "_").replace("\\","_");
            return newPath;
        } catch (Exception e) {
            throw new KDBizException(path + "路径格式化失败");
        }
    }

    /**
     * 修改附件路径映射关系
     * @param oldUrl
     * @param newUrl
     * @author: hst
     * @createDate: 2024/07/21
     */
    public static void reUrl(String oldUrl, String newUrl) {
        QFilter[] filters = new QFilter[]{new QFilter("url", "=", oldUrl)};
        DynamicObject[] atts = BusinessDataServiceHelper.load("bd_attachment", "url", filters);
        for (DynamicObject att : atts) {
            att.set("url", new LocaleString(newUrl));
        }
        SaveServiceHelper.update(atts);
    }

    /**
     * 附件重命名
     * @param entryDetails
     * @param sortNames
     * @param sortNumbers
     * @author: hst
     * @createDate: 2024/07/22
     */
    public static void reSetAttachmentName (DynamicObject bill, Map<String, AdminAttachInfoVo> entryDetails,
                                             Map<String, String> sortNames, Map<String, String> sortNumbers) {
        // update by hst -2024/11/16 是否删除原附件
        boolean isDelete = false;
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"SRM_IS_DELETE")});
        if (Objects.nonNull(param) && StringUtils.isNotBlank(param.getString("name"))) {
            isDelete = Boolean.valueOf(param.getString("name"));
        }

        Map<String, Integer> attachNums = new HashMap<>();

        for (Map.Entry<String, AdminAttachInfoVo> entryDetail : entryDetails.entrySet()) {
            AdminAttachInfoVo attInfo = entryDetail.getValue();

            DynamicObjectCollection attachments = attInfo.getAttachCollection();

            for (DynamicObject attachment : attachments) {
                String baseName = sortNames.get(attInfo.getReNameField());
                String baseNumber = sortNumbers.get(attInfo.getReNameField());
                if (StringUtils.isNotBlank(baseName) && StringUtils.isNotBlank(baseNumber)) {
                    String aptName = attInfo.getTarAptName();
                    String aptNum = attInfo.getTarAptSort();
                    String pathName = attInfo.getPathName();

                    String startDate = DateUtil.date2str(attInfo.getIsSueDate(), "yyyyMMdd");
                    if (StringUtils.isBlank(startDate)) {
                        startDate = DateUtil.date2str(attachment.getDynamicObject("fbasedataid").getDate("createtime"), "yyyyMMdd");
                    }
                    String endDate = DateUtil.date2str(attInfo.getDateTo(), "yyyyMMdd");

                    String fileName = baseName + "-" + aptName + (Objects.nonNull(startDate) ? "-" + startDate : "")
                            + (Objects.nonNull(endDate) ? "-" + endDate : "");

                    // 是否已有重复的命名
                    if (attachNums.containsKey(fileName)) {
                        int index = attachNums.get(fileName);
                        attachNums.put(fileName, index + 1);

                        fileName = fileName + "_" + index;
                    } else {
                        attachNums.put(fileName, 1);
                    }

                    // 格式化附件名称
                    boolean isSuccess = formatAdminAttachName(attachment, fileName, pathName, baseNumber, aptNum, isDelete);

                    if (!isSuccess) {
                        throw new KDBizException("附件：" + baseName + "-" + aptName + "重命名失败");
                    } else {
                        bill.getDynamicObjectCollection(attInfo.getOriEntry()).get(attInfo.getOriSeq() - 1).set(attInfo.getIsReNameField(), true);
                    }
                }
            }
        }
    }

    /**
     * 资质归档，保存到附件库
     * @param bill
     * @param entryDetails
     * @param baseDatas
     * @param records
     */
    public static void qualificationFiling (DynamicObject bill, Map<String, AdminAttachInfoVo> entryDetails,
                                      Map<String, DynamicObject> baseDatas, Set<String> records, String billType) {
        // 归档
        for (Map.Entry<String, AdminAttachInfoVo> entryDetail : entryDetails.entrySet()) {
            AdminAttachInfoVo infoVo = entryDetail.getValue();

            String billId = infoVo.getBillId();
            String entryId = infoVo.getEntryId();

            // 基础资料字段
            String baseField = entryDetail.getValue().getMatchField();

            DynamicObject baseData = baseDatas.get(baseField);

            // 如果已归档，则跳过
            if (Objects.nonNull(baseData)) {
                if (!records.contains(billId + "&" + entryId)) {
                    DynamicObject newDetail = baseData.getDynamicObjectCollection(infoVo.getTarEntry()).addNew();

                    // 附件库分类枚举值
                    setValue(newDetail, infoVo.getTarSortField(), infoVo.getTarSort());
                    // 附件库资质类型枚举值
                    setValue(newDetail, infoVo.getTarAptField(), infoVo.getTarAptSort());
                    // 附件库资质名称
                    setValue(newDetail, infoVo.getTarAptNameField(), infoVo.getTarAptName());
                    // 签发日期
                    setValue(newDetail, infoVo.getIsSueDateField(), infoVo.getIsSueDate());
                    // 有效日期
                    setValue(newDetail, infoVo.getDateToField(), infoVo.getDateTo());
                    // 复制附件
                    copyAttach(infoVo.getAttachCollection(),
                            newDetail.getDynamicObjectCollection(infoVo.getTarAttachField()));

                    newDetail.set("seq", baseData.getDynamicObjectCollection(infoVo.getTarEntry()).size());

                    // 业务单据ID
                    setValue(newDetail, "yd_bill", billId);
                    // 业务单据类型
                    setValue(newDetail, "yd_billtype", billType);
                    // 业务单据号
                    setValue(newDetail, "yd_billkey", billId);
                    // 业务单据分录ID
                    setValue(newDetail, "yd_billentrykey", entryId);

                    String oriDataName = infoVo.getParentDataField();
                    if (StringUtils.isNotBlank(oriDataName)) {
                        String oriDataId = bill.getString(infoVo.getParentDataField() + ".id");
                        setValue(newDetail, "yd_oridata", oriDataId);
                    }
                }

                // 标记已更新附件库
                bill.getDynamicObjectCollection(infoVo.getOriEntry()).get(infoVo.getOriSeq() - 1).set(infoVo.getIsAlreadField(), true);
            }
        }

        // 保存数据
        for (Map.Entry<String,DynamicObject> baseData : baseDatas.entrySet()) {
            OperationResult result = saveBaseData(baseData.getValue());

            if (!result.isSuccess()) {
                throw new KDBizException("保存" + baseData.getKey() + "类型附件失败，原因：" + result.getMessage());
            }
        }
    }

    /**
     * 保存基础资料
     * @return
     * @author: hst
     * @createDate: 2024/07/22
     */
    private static OperationResult saveBaseData (DynamicObject baseData) {
        OperationResult result = SaveServiceHelper.saveOperate(baseData.getDynamicObjectType().getName(),
                new DynamicObject[]{baseData});
        return result;
    }

    /**
     * 获取字段映射
     *
     * @return
     * @author: hongsitao
     * @createDate: 2024/11/23
     */
    public static List<SupplyInfoEntryFieldEntiy> getFieldMap() {
        List<SupplyInfoEntryFieldEntiy> zzEntryAttachFieldList = new ArrayList<SupplyInfoEntryFieldEntiy>();
        zzEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entrydomestic",
                "yd_domesattachment", "yd_domesattachmenttype", "yd_domeseffectdate",
                "yd_domesuneffectdate", "yd_domesattachment_re", "yd_domesattachment_up",
                "yd_oribillid_dome", "yd_orientryid_dome", "yd_domesattachment_ty"));
        zzEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entryforeign",
                "yd_foreigattachment", "yd_foreigattachmenttype", "yd_foreigeffectdate",
                "yd_foreiguneffectdate", "yd_foreigattachment_re", "yd_foreigattachment_up",
                "yd_oribillid_forei", "yd_orientryid_forei", "yd_foreigattachment_ty"));
        zzEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entryagent",
                "yd_agentattachment", "yd_agentattachmenttype", "yd_agenteffectdate",
                "yd_agentuneffectdate", "yd_agentattachment_re", "yd_agentattachment_up",
                "yd_oribillid_agent", "yd_orientryid_agent", "yd_agentattachment_ty"));
        zzEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entryprocessstar",
                "yd_pstarattachment", "yd_pstarattachmenttype", "yd_pstareffectdate",
                "yd_pstaruneffectdate", "yd_pstarattachment_re", "yd_pstarattachment_up",
                "yd_oribillid_pstar", "yd_orientryid_pstar", "yd_pstarattachment_ty"));
        zzEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entryprocessfitinto",
                "yd_pfitienattachment", "yd_pfitienattachmenttype", "yd_pfitieneffectdate",
                "yd_pfitienuneffectdate", "yd_pfitienattachment_re", "yd_pfitienattachment_up",
                "yd_oribillid_pfiti", "yd_orientryid_pfiti", "yd_pfitienattachment_ty"));

        return zzEntryAttachFieldList;
    }
}
