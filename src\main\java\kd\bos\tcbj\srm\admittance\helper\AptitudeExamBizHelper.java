package kd.bos.tcbj.srm.admittance.helper;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import kd.bos.coderule.api.CodeRuleInfo;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.coderule.CodeRuleServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 *资质审查单业务工具类 
 * @auditor liefengWu
 * @date 2022年6月27日
 * 
 */
public class AptitudeExamBizHelper {
	
	/**
	 * 根据srm供应商id与物料id获取对应资质审查记录
	 * @param srmSupplierId
	 * @param materialId
	 * @return
	 */
	public DynamicObject getAptitudeExamBySrmSupplierAndMaterial(String srmSupplierId,String materialId,QFilter otherFilter) {
		DynamicObject result = null;
		if(StringUtils.isEmpty(srmSupplierId)
				|| StringUtils.isEmpty(materialId))
			return result;
		QFilter qFilter = new QFilter("supplier", QCP.equals, srmSupplierId)
				.and("entryentity.material", QCP.equals, materialId);
		if(otherFilter != null)
			qFilter.and(otherFilter);
		
		result = BusinessDataServiceHelper.loadSingle("srm_aptitudeexam",
			"id,entryentity,entryentity.material,entryentity.category,entryentity.categorytype,group,examresult,entertype,"
			+ "isscene,issample,ismaterial,isapprove,ischgflow,auditstatus,billno,ispurorg,iscategory,billstatus,biztype,bizpartner,supplier,org,"
				+"creator,createtime,lastupdateuser,lastupdatetime,billdate,type"	, new QFilter[] {qFilter});
		
	
	
		
		return result;
	}
	
	/**
	 * 根据srm供应商id与条件  获取对应资质审查单
	 * @param srmSupplierId
	 * @param otherFilter
	 * @return
	 */
	public DynamicObject getAptitudeExamBySrmSupplier(String srmSupplierId,QFilter otherFilter) {
		DynamicObject result = null;
		if(StringUtils.isEmpty(srmSupplierId))
			return result;
		QFilter qFilter = new QFilter("supplier", QCP.equals, srmSupplierId);
		if(otherFilter != null)
			qFilter.and(otherFilter);
		result = BusinessDataServiceHelper.loadSingle("srm_aptitudeexam",
				"id,entryentity,entryentity.material,entryentity.category,entryentity.categorytype,group,examresult,entertype,"
				+ "isscene,issample,ismaterial,isapprove,ischgflow,auditstatus,billno,ispurorg,iscategory,billstatus,biztype,bizpartner,supplier,org,"
					+"creator,createtime,lastupdateuser,lastupdatetime,billdate,type"	, new QFilter[] {qFilter});
		
		return result;
	}
	
	/**
	 * 获取或者更新对应的资质审查单，仅对保存的数据进行处理
	 * @param billId 资质审查单id
	 * @param aptitudeExamInfo 资质审查单对象
	 * @param supplierGroupNumber 供应商类型编码：如为空，则默认赋值 waitgroup
	 * @param srmSupplierId  srm供应商id
	 * @param materialId 物料id
	 * @param orgNumber 组织编码：如为空，默认赋值为000002
	 * @param srmBizType 准入类型：如为空，默认赋值为002
	 * @return
	 */
	public DynamicObject addnewOrUpdateAptitudeExam(String billId, DynamicObject aptitudeExamInfo, 
			String supplierGroupNumber,String srmSupplierId,String materialId,String orgNumber,String srmBizType) {
		if(aptitudeExamInfo != null && 
				!"A".equals(GeneralFormatUtils.getString(aptitudeExamInfo.getString("auditstatus"))) 
				){//仅对保存数据进行操作
			return aptitudeExamInfo;
		}
		boolean isAddNew = false;
		Long userId = Long.valueOf(RequestContext.get().getUserId());
		
		DynamicObject userInfo = BusinessDataServiceHelper.loadSingle(userId, "bos_user", "id,number,name");
		
		if(StringUtils.isEmpty(billId) || aptitudeExamInfo == null) {//当单据id为空  或者对应对象为空的话  进行新增实例对象
			aptitudeExamInfo = BusinessDataServiceHelper.newDynamicObject("srm_aptitudeexam");
			isAddNew = true;
			aptitudeExamInfo.set("auditstatus", "A");
			aptitudeExamInfo.set("ispurorg", true);
			aptitudeExamInfo.set("iscategory", true);
			aptitudeExamInfo.set("billstatus", "A");
			aptitudeExamInfo.set("biztype", "2");
			aptitudeExamInfo.set("creator", userInfo);
			aptitudeExamInfo.set("createtime", new Date());
			aptitudeExamInfo.set("type","1");
			
			
		}
		
		aptitudeExamInfo.set("lastupdateuser", userInfo);
		aptitudeExamInfo.set("lastupdatetime", new Date());
		
		if(StringUtils.isEmpty(aptitudeExamInfo.getString("billno"))) {
			CodeRuleInfo codeRule = CodeRuleServiceHelper.getCodeRule(aptitudeExamInfo.getDataEntityType().getName(),
					aptitudeExamInfo, null);
			String billno = CodeRuleServiceHelper.getNumber(codeRule, aptitudeExamInfo);
			aptitudeExamInfo.set("billno", billno);
		}
		
		srmBizType = StringUtils.isEmpty(srmBizType) ? "002" : srmBizType;
		aptitudeExamInfo.set("examresult", "0");
		//准入类型赋值
		DynamicObject[] srmBizTypeColl = BusinessDataServiceHelper.load("srm_biztype", 
				"id,number,name,isaptitude,isscene,issample,ismaterial,isapprove,ischgflow", 
				new QFilter[] {new QFilter("number", QCP.equals, "002")}	);
		if(srmBizTypeColl.length > 0) {
			DynamicObject srmBizTypeInfo = srmBizTypeColl[0];
			aptitudeExamInfo.set("entertype", srmBizTypeInfo);
			aptitudeExamInfo.set("isscene", srmBizTypeInfo.get("isscene"));
			aptitudeExamInfo.set("issample", srmBizTypeInfo.get("issample"));
			aptitudeExamInfo.set("ismaterial", srmBizTypeInfo.get("ismaterial"));
			aptitudeExamInfo.set("isapprove", srmBizTypeInfo.get("isapprove"));
			aptitudeExamInfo.set("ischgflow", srmBizTypeInfo.get("ischgflow"));
		
		}
		//供应商类型赋值
		supplierGroupNumber = StringUtils.isEmpty(supplierGroupNumber) ? "waitgroup" : supplierGroupNumber;//如果为空  赋值为待分类
		DynamicObject[] supplierGroupColl = BusinessDataServiceHelper.load("bd_suppliergroup", "id,number,name", 
				new QFilter[] {new QFilter("number", QCP.equals, supplierGroupNumber)}	);
		aptitudeExamInfo.set("group", supplierGroupColl.length > 0 ? supplierGroupColl[0] : null);
		//物料分录赋值
		DynamicObjectCollection aptitudeEntryColl = aptitudeExamInfo.getDynamicObjectCollection("entryentity");
		DynamicObject aptitudeEntryInfo = aptitudeEntryColl != null && aptitudeEntryColl.size() > 0 ?
											aptitudeEntryColl.get(0) : new DynamicObject(aptitudeEntryColl.getDynamicObjectType());
		if(aptitudeEntryInfo != null) {
			DynamicObject materialObj = BusinessDataServiceHelper.loadSingle(materialId, "bd_material");
			aptitudeEntryInfo.set("material", materialObj);
			aptitudeEntryInfo.set("categorytype", "A");
			if(materialObj != null &&materialObj.getPkValue() != null) {
				materialObj = BusinessDataServiceHelper.loadSingle(materialObj.getPkValue(), "bd_material");
				aptitudeEntryInfo.set("category", materialObj.get("group"));
			}
		}
		aptitudeEntryColl.clear();
		aptitudeEntryColl.add(aptitudeEntryInfo);
		aptitudeExamInfo.set("entryentity", aptitudeEntryColl);
		aptitudeExamInfo.set("billdate", new Date());
		
		
		if(isAddNew) {
			//采购组织赋值
			orgNumber = StringUtils.isEmpty(orgNumber) ? "000002" : orgNumber;
			aptitudeExamInfo.set("org", new BizBillHelper().getObjByBillNumber("bos_adminorg", orgNumber));
			//供应商赋值
			aptitudeExamInfo.set("supplier", BusinessDataServiceHelper.loadSingle(srmSupplierId, "srm_supplier", "id,number,name"));
			aptitudeExamInfo.set("bizpartner", new BizPartnerBizHelper().getBizPartnerInfoBySrmSupplierId(srmSupplierId));
		}
		
		OperationResult opResult = SaveServiceHelper.saveOperate("save", "srm_aptitudeexam", new DynamicObject[] {aptitudeExamInfo});//执行保存逻辑
		
		if (!opResult.isSuccess()) {
			// 错误摘要
			throw new KDException("资质审查单保存失败，查看失败原因：" + opResult.getMessage());
		}
		return aptitudeExamInfo;
	}
}
