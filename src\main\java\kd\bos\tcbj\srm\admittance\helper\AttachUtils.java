package kd.bos.tcbj.srm.admittance.helper;
 
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kd.bos.orm.query.QFilter;
import org.apache.commons.lang3.StringUtils;

import kd.bos.cache.CacheFactory;
import kd.bos.cache.TempFileCache;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.serialization.SerializationUtils;
import kd.bos.db.DB;
import kd.bos.entity.MainEntityType;
import kd.bos.fileservice.FileService;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.form.IPageCache;
import kd.bos.servicehelper.AttachmentDto;
import kd.bos.servicehelper.AttachmentServiceHelper;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.MetadataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.session.EncreptSessionUtils;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * @auditor yanzuwei
 * @date 2022年6月22日
 * 
 */
public class AttachUtils 
{
    private static final String ATT_BD_ID="fbasedataid";
    
	public static Map getAttMap()
	{
		//附件类型枚举值 map
		Map<String,Map<String,String>> attachFieldMap = new HashMap<String,Map<String,String>>();
		
		Map<String,String> entrydomesticType = new HashMap<String,String>();
		entrydomesticType.put("A", "营业执照");
		entrydomesticType.put("B", "药品级生产许可");
		entrydomesticType.put("C", "食品级生产许可");
		entrydomesticType.put("D", "保健食品的提取物生产许可");
		entrydomesticType.put("E", "其他");
		
		
		Map<String,String> entryforeignType = new HashMap<String,String>();
		entryforeignType.put("A", "食品级");
		entryforeignType.put("B", "药品级");
		entryforeignType.put("C", "其他");
		
		
		Map<String,String> entryagentType = new HashMap<String,String>();
		entryagentType.put("A", "营业执照");
		entryagentType.put("B", "经营许可证");
		entryagentType.put("C", "代理证明");
		entryagentType.put("D", "其他");
		
		
		Map<String,String> entryprocessstarType = new HashMap<String,String>();
		entryprocessstarType.put("A", "供应商书面调查表");
		entryprocessstarType.put("B", "物料描述表");
		entryprocessstarType.put("C", "3个批次样品 COA");
		entryprocessstarType.put("D", "样品我司检测报告");
		entryprocessstarType.put("E", "生产工艺流程图");
		entryprocessstarType.put("F", "厂家企业标准");
		entryprocessstarType.put("G", "其他");
		
		Map<String,String> entryprocessfitintoType = new HashMap<String,String>();
		entryprocessfitintoType.put("A", "产品稳定性报告");
		entryprocessfitintoType.put("B", "第三方型式检验报告");
		entryprocessfitintoType.put("C", "包装标签模板");
		entryprocessfitintoType.put("D", "转基因原料类");
		entryprocessfitintoType.put("E", "菌种/藻类");
		entryprocessfitintoType.put("F", "微生物高风险物料");
		entryprocessfitintoType.put("G", "植物物原料");
		entryprocessfitintoType.put("H", "动物原料");
        // update by hst 2024/07/19 增加附件类型
        entryprocessfitintoType.put("L", "兽药残留检验报告");
        entryprocessfitintoType.put("M", "EDQM证书");
        entryprocessfitintoType.put("N", "初加工的动物原料品种鉴定证明");
        entryprocessfitintoType.put("O", "检验检疫证明");
		entryprocessfitintoType.put("I", "食品原料");
		entryprocessfitintoType.put("J", "体系认证文件");
        // update by hst 增加附件类型
        entryprocessfitintoType.put("K", "非辐照证明");
        entryprocessfitintoType.put("P", "MSDS");
        entryprocessfitintoType.put("Q", "文件清单");
        entryprocessfitintoType.put("R", "设备清单");
        entryprocessfitintoType.put("S", "排污许可证");

		attachFieldMap.put("yd_entrydomestic", entrydomesticType);
		attachFieldMap.put("yd_entryforeign", entryforeignType);
		attachFieldMap.put("yd_entryagent", entryagentType);
		attachFieldMap.put("yd_entryprocessstar", entryprocessstarType);
		attachFieldMap.put("yd_entryprocessfitinto", entryprocessfitintoType);
		
		return attachFieldMap;
		
	}
	
	public static void copyAttachField(DynamicObject srcInfo,String srcAttField,
			DynamicObject tarInfo,String tarAttField,boolean isClearTarAttach)
    {
		 
		DynamicObjectCollection srcAttList=srcInfo.getDynamicObjectCollection(srcAttField);
		DynamicObjectCollection tarAttList=tarInfo.getDynamicObjectCollection(tarAttField);
		
		if(srcAttList==null||srcAttList.isEmpty())
		{
			return;
		}
		
		if(isClearTarAttach)
		{
		   tarAttList.clear();
		}
		for(DynamicObject srcAttInfo:srcAttList)
		{
			DynamicObject tarAttInfo=tarAttList.addNew();
			tarAttInfo.set(ATT_BD_ID,AttachUtils.copyAttachBaseData(null,srcAttInfo.getDynamicObject(ATT_BD_ID))); 
		}
	}

    /**
     *
     * @param attName
     * @param att_bd
     * @return
     * @author: hst
     * @createDate: 2023/06/28
     */
    public static DynamicObject copyAttachBaseData(String attName,DynamicObject att_bd) {
        return copyAttachBaseData(attName,att_bd,false);
    }

   public static DynamicObject copyAttachBaseData(String attName,DynamicObject att_bd,boolean isCopyFile)
   {
		 
		DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject(att_bd.getDataEntityType().getName());
		DataEntityPropertyCollection properties = att_bd.getDataEntityType().getProperties();
		for(IDataEntityProperty p1:properties) {
			//id 和 multilanguagetext不用设置 设置了会导致字段的重复
			
			//System.out.println(p1.getName()+"----"+att_bd.get(p1));
			if(!p1.getName().equals("id") &&  !p1.getName().equals("multilanguagetext")) {
				//uid加了索引，因此不能重复    粗略可以使用下面的策略       原本的生成策略没有找到
				if(p1.getName().equals("uid")) {
					newObj.set("uid",DB.genLongId(""));
				}else {
					Object value = att_bd.get(p1);
					newObj.set(p1, value);
				} 
			}
		}
		
		newObj.set("status", "B");
		
		if(StringUtils.isNotEmpty(attName))
		{
		 String type = GeneralFormatUtils.getString(newObj.get("type"));
		 newObj.set("name", attName +"."+type); 
		}
		
		/*
		 
		 String oldUrl=att_bd.getString("url");
		 InputStream inputStream = FileServiceFactory.getAttachmentFileService().getInputStream(att_bd.getString("url"));
		 String saveUrl = CacheFactory.getCommonCacheFactory().getTempFileCache().saveAsFullUrl(att_bd.getString("name"), new BufferedInputStream(inputStream),2*3600);
		 System.out.println("oldUrl="+oldUrl);
		 System.out.println("saveUrl="+saveUrl);
		 
		*/
		SaveServiceHelper.save(new DynamicObject[] {newObj});//将复制的附件存入系统 
		return BusinessDataServiceHelper.loadSingle(newObj.getPkValue(), att_bd.getDataEntityType().getName());
	}
   

	/**
	 * @fun 附件面板附件拷贝
	 * **/
	public static void copyAttachPanel(
			String fromFromId,Object fromBillPk,String fromAttPanelId,
			String toFromId,Object toBillPk,String toAttPanelId
			)
	{
		
		List<Map<String, Object>> attachmentData = AttachmentServiceHelper.getAttachments(fromFromId,
				fromBillPk,fromAttPanelId);
		//System.out.println(attachmentData); 
		 uploadTargetAttachments(toFromId, toBillPk, toAttPanelId, attachmentData);

	}
	
	
	
   /**
    * 从页面缓存中获取附件，当附件上传未保存时使用
    */
   public static List<Map<String, Object>> getTempAttachments(IPageCache pageCache, String pageId) {
       String cacheJsonString = pageCache.get("TampAttCache" + pageId);
       // 所有附件面板的所有附件文件
       Map<String, Object> allAttachmentsInfo = null;
       if (!StringUtils.isEmpty(cacheJsonString)) {
           allAttachmentsInfo = SerializationUtils.fromJsonString(cacheJsonString, Map.class);
       }
       if (!(allAttachmentsInfo != null && allAttachmentsInfo.size() > 0)) {
           return null;
       }
       // 一个附件面板中的所有附件文件
       List<Map<String, Object>> attachments = null;
       for (String attachmentPanelKey : allAttachmentsInfo.keySet()) {
           attachments = (List<Map<String, Object>>) allAttachmentsInfo.get(attachmentPanelKey);
       }
       return attachments;
   }

   /**
    * 附件拷贝到目标单 附件存储在文件服务器，实体表中有记录
    *
    * @param targetBillNo 目标单标识
    * @param targetPkid   目标单pkid
    * @param attachKey    目标单附件面板标识
    * @param attachments  源附件信息集合
    */
   public static void uploadTargetAttachments(String targetBillNo, Object targetPkid, String attachKey, List<Map<String, Object>> attachments) {
       for (Map<String, Object> attachItem : attachments) {
           //获取附件文件的对象
           DynamicObject obj = AttachmentServiceHelper.getAttCreatorByUID((String) attachItem.get("uid"));
           //获取附件相关信息
           AttachmentDto attachmentDto = AttachmentServiceHelper.getAttachmentInfoByAttPk(obj.getPkValue());
           //获取文件的物理路径
           String resourcePath = attachmentDto.getResourcePath();
           //获取临时文件缓存
           TempFileCache cache = CacheFactory.getCommonCacheFactory().getTempFileCache();
           //将文件转换成输入流
           FileService fs = FileServiceFactory.getAttachmentFileService();
           InputStream inputStream = fs.getInputStream(resourcePath);
           //将文件流存入临时文件缓存（拷贝完成）（最后一个参数为缓存有效期，7200秒）
           String tempUrl = cache.saveAsUrl((String) attachItem.get("name"), inputStream, 7200);
           //获取文件的缓存路径
           tempUrl = EncreptSessionUtils.encryptSession(tempUrl);
           //获取域名前缀
           String address = RequestContext.get().getClientFullContextPath();
           if (!address.endsWith("/")) {
               address = address + "/";
           }
           //拼接url路径
           String tempUrl3 = address + tempUrl;
           //获取appId
           MainEntityType dataEntityType = MetadataServiceHelper.getDataEntityType(targetBillNo);
           String appId = dataEntityType.getAppId();
           //获取附件名称
           String name = (String) attachItem.get("name");
           //将文件缓存中的附件文件上传到正式文件服务器
           String path = AttachmentServiceHelper.saveTempToFileService(tempUrl3, appId, targetBillNo, targetPkid, name);
           //将新文件的物理路径存入map
           attachItem.put("url", path);
           //修改时间格式处理 
           attachItem.put("lastModified", new Date().getTime());
           //备注
           Object description = attachItem.get("description");
           attachItem.put("description", description);
       }
       //维护单据和附件的关系（非文件上传）
       AttachmentServiceHelper.upload(targetBillNo, targetPkid, attachKey, attachments);
   }

   /**
    * 附件拷贝到目标单 附件存储在缓存中
    *
    * @param targetBillNo 目标单标识
    * @param targetPkid   目标单pkid
    * @param attachKey    目标单附件面板标识
    * @param attachments  源附件信息集合
    */
   public static void uploadTargetTempAttachments(String targetBillNo, Object targetPkid, String attachKey, List<Map<String, Object>> attachments) {
       for (Map<String, Object> attachItem : attachments) {
           //获取url路径
           String tempUrl =attachItem.get("url").toString();
           //获取appId
           MainEntityType dataEntityType = MetadataServiceHelper.getDataEntityType(targetBillNo);
           String appId = dataEntityType.getAppId();
           //获取附件名称
           String name = (String) attachItem.get("name");
           //将文件缓存中的附件文件上传到正式文件服务器
           String path = AttachmentServiceHelper.saveTempToFileService(tempUrl, appId, targetBillNo, targetPkid, name);
           //将新文件的物理路径存入map
           attachItem.put("url", path);
           //修改时间格式处理
           Timestamp lastModified = new Timestamp((long) attachItem.get("lastModified"));
           attachItem.put("lastModified", lastModified.getTime());
           //备注
           Object description = attachItem.get("description");
           attachItem.put("description", description);
       }
       //维护单据和附件的关系（非文件上传）
       AttachmentServiceHelper.upload(targetBillNo, targetPkid, attachKey, attachments);
   }

   /**
    * 复制单个附件文件
    *
    * @param attachment 源附件文件
    * @return
    */
   public static Map<String, Object> copyAttachment(Map<String, Object> attachment) {
       TempFileCache tempFileCache = CacheFactory.getCommonCacheFactory().getTempFileCache();
       String tempUrl = String.valueOf(attachment.get("url"));
       String uid = String.valueOf(attachment.get("uid"));
       String fileName = String.valueOf(attachment.get("name"));
       InputStream inputStream = (InputStream) tempFileCache.getInputStream(tempUrl);
       // ------ 复制文件 ------
       Map<String, Object> newAttachment = new HashMap<String, Object>();
       // lastModified:时间戳
       long time = new java.util.Date().getTime();
       newAttachment.put("lastModified", time);
       // name:文件名(含文件格式)
       StringBuffer newNameBuffer = new StringBuffer(fileName);
       newNameBuffer.insert(fileName.lastIndexOf("."), "-新");
       newAttachment.put("name", newNameBuffer.toString());
       // size:文件大小
       try {
           newAttachment.put("size", inputStream.available());
       } catch (IOException e) {
           // ignore
       }
       newAttachment.put("status", "success");
       // type:文件类型
       newAttachment.put("type", String.valueOf(attachment.get("type")));
       // uid
       StringBuffer newUid = new StringBuffer("rc-upload-");
       newUid.append(time);
       newUid.append("-");
       String uidIndex = null;
       int index = uid.lastIndexOf("-");
       if (index > 0) {
           uidIndex = uid.substring(index + 1);
       }
       newUid.append(uidIndex);
       newAttachment.put("uid", newUid.toString());
       // url:附件在附件服务器上的位置
       StringBuffer newUrl = new StringBuffer(RequestContext.get().getClientFullContextPath());
       if (!newUrl.toString().endsWith("/")) {
           newUrl.append("/");
       }
       String tempNewUrl = tempFileCache.saveAsUrl(newNameBuffer.toString(), inputStream, 2 * 60 * 60);
       tempNewUrl = EncreptSessionUtils.encryptSession(tempNewUrl);
       newUrl.append(tempNewUrl);
       newAttachment.put("url", newUrl.toString());
       return newAttachment;
   }
}
