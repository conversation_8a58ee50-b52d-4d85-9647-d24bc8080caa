package kd.bos.tcbj.srm.admittance.helper;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;

/**
 * 单据业务工具类
 * @auditor liefeng<PERSON>u
 * @date 2022年6月14日
 * 
 */
public class BizBillHelper {

	
	public DynamicObject getObjByBillNumber(String entity,String number,String selector) {
		DynamicObject result = null;
		
		DynamicObject[] objColl = BusinessDataServiceHelper.load(entity, 
				//"id,number,name", 
				selector,
				new QFilter[] {new QFilter("number", QCP.equals, number)}	);
		result = objColl != null && objColl.length > 0 && objColl[0] != null ? objColl[0] : null;
		
		return result;
	}
	
	/**
	 * 通过实体与单据编号以及selector查询对应对象返回数据
	 * @param entity
	 * @param number
	 * @return
	 */
	public DynamicObject getObjByBillNumber(String entity,String number) {
		DynamicObject result = getObjByBillNumber(entity, number, "id,number,name");
		return result;
	}

}
