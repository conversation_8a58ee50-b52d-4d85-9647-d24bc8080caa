package kd.bos.tcbj.srm.admittance.helper;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import kd.bos.tcbj.im.helper.BizHelper;
import org.apache.commons.lang3.StringUtils;

import kd.bos.algo.DataSet;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 商务伙伴工具类实现
 * @auditor liefengWu 
 * @date 2022年6月7日
 * 
 */
public class BizPartnerBizHelper {

	
	/**
	 * 根据上下文对应用户id  获取当前商务伙伴id
	 * @return
	 */
	public Set<Long> getCurrentBizPartnerIds() {
		HashSet pkIds = new HashSet();
		DataSet dataSet = QueryServiceHelper.queryDataSet("BizPartnerUtil", "bos_bizpartneruser", "bizpartner",
				new QFilter[]{new QFilter("user", "=", Long.valueOf(RequestContext.get().getUserId()))},
//				new QFilter[]{new QFilter("user", "=", Long.valueOf("1543380184198035456"))},
				
				"bizpartner");
		Throwable arg1 = null;

		try {
			dataSet.forEach((row) -> {
				pkIds.add(row.getLong("bizpartner"));
			});
		} catch (Throwable arg10) {
			arg1 = arg10;
			throw arg10;
		} finally {
			if (dataSet != null) {
				if (arg1 != null) {
					try {
						dataSet.close();
					} catch (Throwable arg9) {
						arg1.addSuppressed(arg9);
					}
				} else {
					dataSet.close();
				}
			}

		}
		return pkIds;
	}
	
	/**
	 * 根据当前上下文获取当前登录用户关联的srm供应商数据id 
	 * @return
	 */
	public Set<String> getCurrentSrmSupplierId(){
		HashSet pkIds = new HashSet();
		Set<Long> bizPartnerIds = getCurrentBizPartnerIds();//获取商务伙伴id
		if(bizPartnerIds == null || bizPartnerIds.size() == 0)
			return pkIds;
		//根据商务伙伴id 获取到对应srm_supplier（通过bizpartner字段映射对应srm供应商数据）
		QFilter qFilter = new QFilter("bizpartner", QCP.in, bizPartnerIds);
		DynamicObjectCollection queryColl = QueryServiceHelper.query("srm_supplier", "id,number", new QFilter[] {qFilter});
		for(int index = 0 ; index < queryColl.size() ; index ++) {
			DynamicObject entryInfo = queryColl.get(index);
			if(entryInfo != null)
				pkIds.add(GeneralFormatUtils.getString(entryInfo.get("id")));
		};
		return pkIds;
	}
	
	/**
	 * 根据对应srm供应商返回对应供应商用户对象
	 * @param srmSupplierId
	 * @return
	 */
	public DynamicObject getSrmSupplierUserBySrmSupplierId(String srmSupplierId) {
		DynamicObject result = null;
		if(StringUtils.isEmpty(srmSupplierId))
			return result;
		QFilter qFilter = new QFilter("id", QCP.equals, srmSupplierId);//根据id = srm供应商id过滤封装
		DynamicObjectCollection srmSupplierColl = QueryServiceHelper.query("srm_supplier", "id,number,bizpartner,bizpartner.id", new QFilter[] {qFilter});
		if(srmSupplierColl == null || srmSupplierColl.size() == 0 || srmSupplierColl.get(0) == null)
			return result;
		String bizPartnerId = GeneralFormatUtils.getString(srmSupplierColl.get(0).get("bizpartner"));
		
		if(StringUtils.isEmpty(bizPartnerId))
			return result;
		
		qFilter = new QFilter("bizpartner", QCP.equals, bizPartnerId);  //供应商用户.商务伙伴id判断过滤封装
		// update by hst 2024/08/22 增加使用状态过滤
		qFilter = qFilter.and(new QFilter("enable", QFilter.equals, "1"));
		DynamicObjectCollection supplierUserColl = QueryServiceHelper.
											query("pur_supuser", "id,user.phone,user.email,user.username,bizpartner,bizpartner.id,user,user.id", 
				new QFilter[] {qFilter});
		result = supplierUserColl != null && supplierUserColl.size() > 0 && supplierUserColl.get(0) != null ? 
					supplierUserColl.get(0) : null;	
		return result;
	}
	
	/**
	 * 判断当前用户是否对应商务伙伴，如果为是，返回true  反之 返回false
	 * @return
	 */
	public boolean isBizPartnerCurrentUser() {
		Set<Long> bizPartnerIds = getCurrentBizPartnerIds();
		return bizPartnerIds != null && bizPartnerIds.size() > 0;
	}
	
	
	public Long getBizPartnerIdBySrmSupplierId(String srmSupplierId) {
		Long result = null;
		if(StringUtils.isEmpty(srmSupplierId))
			return result;
		QFilter qFilter = new QFilter("id", QCP.equals, srmSupplierId);//根据id = srm供应商id过滤封装
		DynamicObjectCollection srmSupplierColl = QueryServiceHelper.query("srm_supplier", "id,number,bizpartner,bizpartner.id", new QFilter[] {qFilter});
		if(srmSupplierColl == null || srmSupplierColl.size() == 0 || srmSupplierColl.get(0) == null)
			return result;
		result = GeneralFormatUtils.getLong(srmSupplierColl.get(0).get("bizpartner"));
		return result;
	}
	
	
	
	public DynamicObject getBizPartnerInfoBySrmSupplierId(String srmSupplierId) {
		DynamicObject result = null;
		Long billId =  getBizPartnerIdBySrmSupplierId(srmSupplierId);
		if(billId == null)
			return result;
		result = BusinessDataServiceHelper.loadSingle(billId, "bd_bizpartner","id,number,name");
		return result;
	}
	
	
	
	/**
	 * 根据对应srm供应商返回对应主数据用户对象
	 * @param srmSupplierId
	 * @return
	 */
	public DynamicObject getBdUserBySrmSupplierId(String srmSupplierId) {
		DynamicObject result = null;
		if(StringUtils.isEmpty(srmSupplierId))
			return result;
		QFilter qFilter = new QFilter("id", QCP.equals, srmSupplierId);//根据id = srm供应商id过滤封装
		DynamicObjectCollection srmSupplierColl = QueryServiceHelper.query("srm_supplier", "id,number,bizpartner,bizpartner.id", new QFilter[] {qFilter});
		if(srmSupplierColl == null || srmSupplierColl.size() == 0 || srmSupplierColl.get(0) == null)
			return result;
		String bizPartnerId = GeneralFormatUtils.getString(srmSupplierColl.get(0).get("bizpartner"));
		
		if(StringUtils.isEmpty(bizPartnerId))
			return result;
		
		qFilter = new QFilter("bizpartner", QCP.equals, bizPartnerId);  //供应商用户.商务伙伴id判断过滤封装
		// update by hst 2024/08/22 增加使用状态过滤
		qFilter = qFilter.and(new QFilter("enable", QFilter.equals, "1"));
		DynamicObjectCollection supplierUserColl = QueryServiceHelper.
											query("pur_supuser", "id,user.phone,user.email,user.username,bizpartner,bizpartner.id,user,user.id", 
				new QFilter[] {qFilter});
		DynamicObject srmUserInfo = supplierUserColl != null && supplierUserColl.size() > 0 && supplierUserColl.get(0) != null ? 
					supplierUserColl.get(0) : null;	
		if(srmUserInfo != null) {
			String userId = GeneralFormatUtils.getString(srmUserInfo.get("user.id"));
			if(!StringUtils.isEmpty(userId)) {
				result = BusinessDataServiceHelper.loadSingle(userId, "bos_user");
			}
		}
		return result;
	}
	
	/**
	 * 根据对应srm供应商返回对应主数据用户对象，返回多个
	 * <AUTHOR>
	 * @date 2023-04-25
	 * @param srmSupplierId
	 * @return
	 */
	public DynamicObjectCollection getMulBdUserBySrmSupplierId(String srmSupplierId) {
		DynamicObjectCollection result = new DynamicObjectCollection();
		if(StringUtils.isEmpty(srmSupplierId))
			return result;
		QFilter qFilter = new QFilter("id", QCP.equals, srmSupplierId);//根据id = srm供应商id过滤封装
		DynamicObjectCollection srmSupplierColl = QueryServiceHelper.query("srm_supplier", "id,number,bizpartner,bizpartner.id", new QFilter[] {qFilter});
		if(srmSupplierColl == null || srmSupplierColl.size() == 0 || srmSupplierColl.get(0) == null)
			return result;
		String bizPartnerId = GeneralFormatUtils.getString(srmSupplierColl.get(0).get("bizpartner"));
		
		if(StringUtils.isEmpty(bizPartnerId))
			return result;
		
		qFilter = new QFilter("bizpartner", QCP.equals, bizPartnerId);  //供应商用户.商务伙伴id判断过滤封装
		// update by hst 2024/08/22 增加使用状态过滤
		qFilter = qFilter.and(new QFilter("enable", QFilter.equals, "1"));
		DynamicObjectCollection supplierUserColl = QueryServiceHelper.
											query("pur_supuser", "id,user.phone,user.email,user.username,bizpartner,bizpartner.id,user,user.id", 
				new QFilter[] {qFilter});
		if (supplierUserColl == null || supplierUserColl.size() == 0 || supplierUserColl.get(0) == null) {
			return result;
		}
		
		for (DynamicObject srmUserInfo : supplierUserColl) {
			if(srmUserInfo != null) {
				String userId = GeneralFormatUtils.getString(srmUserInfo.get("user.id"));
				if(!StringUtils.isEmpty(userId)) {
					result.add(BusinessDataServiceHelper.loadSingle(userId, "bos_user"));
				}
			}
		}
		
		return result;
	}

	/**
	 * 获取当前用户对应的基础资料供应商
	 * <AUTHOR>
	 * @date 2022-10-31
	 */
	public Set<String> getCurrentBdSupplierId() {
		Set<String> srmSupIdSet = getCurrentSrmSupplierId();
		if (srmSupIdSet == null || srmSupIdSet.size() == 0) return srmSupIdSet;
		// 通过供应商库中的字段查找供应商
		Set<Object> supIdSet = BizHelper.getQueryCol("srm_supplier", "supplier.id", new QFilter("id", QCP.in, srmSupIdSet).toArray());
		if (supIdSet == null) return null;
		return supIdSet.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
	}
	
	/**
	 * 获取当前用户对应的基础资料供应商编码
	 * <AUTHOR>
	 * @date 2022-10-31
	 */
	public Set<String> getCurrentBdSupplierNums() {
		Set<String> srmSupIdSet = getCurrentSrmSupplierId();
		if (srmSupIdSet == null || srmSupIdSet.size() == 0) return srmSupIdSet;
		// 通过供应商库中的字段查找供应商
		Set<Object> supNumSet = BizHelper.getQueryCol("srm_supplier", "supplier.number", new QFilter("id", QCP.in, srmSupIdSet).toArray());
		if (supNumSet == null) return null;
		return supNumSet.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
	}

	/**
	 * 根据对应srm供应商返回对应主数据用户对象，返回多个
	 * @author: hst
	 * @createDate: 2023/08/22
	 * @param supplierId
	 * @return
	 */
	public DynamicObjectCollection getMulBdUserBySupplierId(String supplierId) {
		DynamicObjectCollection result = new DynamicObjectCollection();
		if(StringUtils.isEmpty(supplierId))
			return result;
		QFilter qFilter = new QFilter("supplier.id", QCP.equals, supplierId);//根据id = srm供应商id过滤封装
		DynamicObjectCollection srmSupplierColl = QueryServiceHelper.query("srm_supplier", "id,number,bizpartner,bizpartner.id", new QFilter[] {qFilter});
		if(srmSupplierColl == null || srmSupplierColl.size() == 0 || srmSupplierColl.get(0) == null)
			return result;
		String bizPartnerId = GeneralFormatUtils.getString(srmSupplierColl.get(0).get("bizpartner"));

		if(StringUtils.isEmpty(bizPartnerId))
			return result;

		qFilter = new QFilter("bizpartner", QCP.equals, bizPartnerId);  //供应商用户.商务伙伴id判断过滤封装
		// update by hst 2024/08/22 增加使用状态过滤
		qFilter = qFilter.and(new QFilter("enable", QFilter.equals, "1"));
		DynamicObjectCollection supplierUserColl = QueryServiceHelper.
				query("pur_supuser", "id,user.phone,user.email,user.username,bizpartner,bizpartner.id,user,user.id",
						new QFilter[] {qFilter});
		if (supplierUserColl == null || supplierUserColl.size() == 0 || supplierUserColl.get(0) == null) {
			return result;
		}

		for (DynamicObject srmUserInfo : supplierUserColl) {
			if(srmUserInfo != null) {
				String userId = GeneralFormatUtils.getString(srmUserInfo.get("user.id"));
				if(!StringUtils.isEmpty(userId)) {
					result.add(BusinessDataServiceHelper.loadSingle(userId, "bos_user"));
				}
			}
		}

		return result;
	}
}
