package kd.bos.tcbj.srm.admittance.helper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.message.api.EmailInfo;
import kd.bos.message.service.handler.EmailHandler;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 邮箱业务工具类定义
 * @auditor liefengWu
 * @date 2022年6月7日
 * 
 */
public class EmailBizHelper {
	
	/**
	 * 根据邮箱地址，主题，内容发送到指定邮箱
	 * add by liefengWu 20220607
	 * @param receiver 收件人邮箱地址
	 * @param title 邮件主题
	 * @param content 邮件正文
	 * @return
	 */
	public ApiResult sendEmail(String receiver, String title,
			String content) {
		List<String> receiverList = new ArrayList<String>();
		receiverList.add(receiver);
		return sendEmail(receiverList, title, content);
	}
	
	/**
	 * 根据多个邮箱地址，主题，内容发送到指定邮箱
	 * add by liefengWu 20220607
	 * @param receiverList 收件人邮箱地址集合
	 * @param title 邮件主题
	 * @param content 邮件正文
	 * @return
	 */
	public ApiResult sendEmail(List<String> receiverList, String title,
			String content) {
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			
			//邮箱为空校验
			List<String> resultReceiverList = new ArrayList<String>();
			if(receiverList != null) {
				for(String receiver : receiverList) {
					if(!StringUtils.isEmpty(receiver))
						resultReceiverList.add(receiver);
				}
			}
			if(resultReceiverList == null || resultReceiverList.size() == 0) {
				throw new KDBizException("邮箱为空，请核实！");
			}
			
			EmailInfo emailInfo = new EmailInfo();
			emailInfo.setTitle(title);
			emailInfo.setContent(content);
			emailInfo.setReceiver(resultReceiverList);
			Map<String,Object> sendResult = EmailHandler.sendEmail(emailInfo);
			if(sendResult != null) {
				String code = GeneralFormatUtils.getString(sendResult.get("code"));
				if(!"0".equals(code)) {
					apiResult.setMessage(GeneralFormatUtils.getString(sendResult.get("description")));
					apiResult.setSuccess(false);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getMessage());
		}
		return apiResult;
	}

}
