package kd.bos.tcbj.srm.admittance.helper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kd.bos.servicehelper.DispatchServiceHelper;

/**
 * @auditor yanzuwei
 * @date 2022年6月29日
 * 
 */
public class ExecEASHelper 
{
	private static final String cn_number="EAS86";
	
	public static Object invokeFacade(String facadeName,String method,Map params)
	{
		 String facadeServeice="facade://"+facadeName+":"+method; 
	     Object data = DispatchServiceHelper.invokeBizService("isc", "iscb", "ISCDataCopyService", "callFacadeService",
		    		new Object[]{cn_number, facadeServeice, params}); 
		return data;
	}
	public static Object executeQuerySQL(String sql, List<Object> values, List<Integer> types, long limit)
	{
		 Object data = DispatchServiceHelper.invokeBizService("isc", "iscb", "ISCDataCopyService", "executeQuerySQL",
		    		new Object[]{cn_number, sql, values,types,limit}); 
		
		return data;
	}
	

}
