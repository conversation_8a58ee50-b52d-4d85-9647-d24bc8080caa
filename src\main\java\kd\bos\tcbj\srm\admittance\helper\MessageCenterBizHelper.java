package kd.bos.tcbj.srm.admittance.helper;

import java.util.List;

import kd.bos.entity.api.ApiResult;
import kd.bos.servicehelper.workflow.MessageCenterServiceHelper;
import kd.bos.workflow.engine.msg.info.MessageInfo;

/**
 * 消息中心发送信息业务工具定义
 * @auditor liefengWu
 * @date 2022年6月8日
 * 
 */
public class MessageCenterBizHelper {

	
	public ApiResult sendMessageCenter(List<Long> userIds, String title,
			String content,String operation,Long billId,String entityNumber,String billUrl) {
	//	MessageCenterServiceHelper.sendMessage(MessageInfo messageInfo) 
		MessageInfo messageInfo = new MessageInfo();
		messageInfo.setTitle(title);
		messageInfo.setUserIds(userIds);
		messageInfo.setContent(content);
		messageInfo.setOperation(operation);
		messageInfo.setContentUrl(billUrl);
		messageInfo.setBizDataId(billId);
		messageInfo.setEntityNumber(entityNumber);
		
		MessageCenterServiceHelper.sendMessage(messageInfo); 
		return null;
	}
	
	
}
