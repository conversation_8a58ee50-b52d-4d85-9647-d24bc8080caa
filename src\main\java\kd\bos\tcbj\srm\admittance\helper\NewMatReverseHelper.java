package kd.bos.tcbj.srm.admittance.helper;

import java.util.*;
import java.util.stream.Collectors;

import kd.bos.tcbj.srm.admittance.entity.SupplyInfoEntryFieldEntiy;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.workflow.WorkflowServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * @auditor yanzuwei
 * @date 2022年7月27日
 * @fun 研发准入流程，附件反写功能
 */
public class NewMatReverseHelper
{
	
	/*** 
	 * @fun 已审核的最新的资料补充函中的附件复制到新创建的资料补充函； 
	 * ***/
	public static void copyAttDataSupInfo(DynamicObject info)
	{
		if(info.getDynamicObject("yd_newmatreq")!=null 
				&& info.getDynamicObject("yd_supplier")!=null)
		{
			Object supId=info.getDynamicObject("yd_supplier").getPkValue();
			Object billId=info.getDynamicObject("yd_newmatreq").getPkValue();
			DynamicObjectCollection  rs=QueryServiceHelper.query("yd_supplyinformationbill", "id,billno",
					QFilter.of(" billstatus='C' and  yd_newmatreq=? and yd_supplier=?", new Object[] {billId,supId}).toArray(),
					"createtime desc");
			if(rs.size()>0)
			{
				Long id=rs.get(0).getLong("id");
				copyAttEntryData(id,info);
			}
		} 
	}
	
	private static void copyAttEntryData(Long id,DynamicObject tarInfo)
	{
		DynamicObject srcInfo=BusinessDataServiceHelper.loadSingle(id, EntityMetadataCache.getDataEntityType("yd_supplyinformationbill"));
		
		String[][] cols= 
		{
			{"yd_entrydomestic","yd_domesattachment","yd_domesattachmenttype","yd_domeseffectdate","yd_domesuneffectdate"},
			{"yd_entryforeign","yd_foreigattachment","yd_foreigattachmenttype","yd_foreigeffectdate","yd_foreiguneffectdate"},
			{"yd_entryagent","yd_agentattachment","yd_agentattachmenttype","yd_agenteffectdate","yd_agentuneffectdate"},
			{"yd_entryprocessstar","yd_pstarattachment","yd_pstarattachmenttype","yd_pstareffectdate","yd_pstaruneffectdate"},
			{"yd_entryprocessfitinto","yd_pfitienattachment","yd_pfitienattachmenttype","yd_pfitieneffectdate","yd_pfitienuneffectdate"},	
		};
		for(int i=0;i<cols.length;i++)
		{
			String[] fields=cols[i];
			String entryKey=fields[0];
			DynamicObjectCollection  srcEntrys=srcInfo.getDynamicObjectCollection(entryKey);
			DynamicObjectCollection  tarEntrys=tarInfo.getDynamicObjectCollection(entryKey);
			tarEntrys.clear();
			if(srcEntrys!=null && srcEntrys.size()>0)
			{
				for(DynamicObject srcEntryInfo:srcEntrys)
				{
					 
					DynamicObject tarEntryInfo=tarEntrys.addNew(); 
					//拷贝非附件字段
					for(int k=2;k<fields.length;k++)
					{
						String key=fields[k];
						tarEntryInfo.set(key, srcEntryInfo.get(key)); 
					} 
					//拷贝附件
					String attKey=fields[1]; 
					DynamicObjectCollection srcAtts =srcEntryInfo.getDynamicObjectCollection(attKey); 
					DynamicObjectCollection tarAtts =tarEntryInfo.getDynamicObjectCollection(attKey); 
					tarAtts.clear();
					if(srcAtts!=null)
					{
				     for(DynamicObject srcAttInfo:srcAtts)
					  {
						  DynamicObject newAttInfo= AttachUtils.copyAttachBaseData(null,srcAttInfo.getDynamicObject("fbasedataid"));
						  DynamicObject tarAttInfo=tarAtts.addNew();
						  tarAttInfo.set("fbasedataid", newAttInfo); 
					  }
					}
				} 
			} 
		}
	}
	
	
	
 
	/**
	 * @fun 按研发准入单ID+供应商ID获取系统中是否存在流程中的资料补充函
	 * 
	 * ***/ 
	public static void  checkHasSupInfo(Long billId,Long supId,Long prodId)
	throws KDBizException
	{
		//yd_supplyinformationbill
		DynamicObjectCollection  rs=QueryServiceHelper.query("yd_supplyinformationbill", "id,billno",
				QFilter.of(" yd_newmatreq=? and yd_supplier=? and yd_producers=?", new Object[] {billId,supId,prodId}).toArray());
		for(DynamicObject row:rs)
		{
			Long id=row.getLong("id");
			String billNo=row.getString("billno");
			if(WorkflowServiceHelper.inProcess(id.toString()))
			{
				throw new KDBizException("当前所选供应商已有在审批中的资料补充函【单号"+billNo+"】，可以通知供应商在该资料补充函上继续补充资料！");
				
			} 
		} 
	}
	
	
	
	/*** 
	 * @fun 【新物料需求及供应商评价】生成【原辅料及供应商 】;新物料生成的资料补充函附件带到原辅料及供应商
	 * ***/
	public static void copyAttDataRawMatInfo(Long newMatId,DynamicObject rawMatinfo) {
		DynamicObject oriBill = BusinessDataServiceHelper.loadSingle(newMatId, "yd_newmatreqbill");
		long supId = oriBill.getLong("yd_selectsup.id");
		long proId = oriBill.getLong("yd_selectprod.id");
		DynamicObject entry = oriBill.getDynamicObjectCollection("yd_file1entry").stream().filter(info ->
				supId == info.getLong("yd_csup.id") && proId == info.getLong("yd_cprod.id"))
				.findFirst().orElse(null);

		if (Objects.nonNull(entry)) {
			copyAttToSupAccBill(String.valueOf(newMatId), entry, rawMatinfo);
		}
	}
	
	//资料补充函，附件携带到原辅料准入单
	private static void copyAttToSupAccBill(String srcId, DynamicObject srcInfo, DynamicObject tarInfo) {
		List<SupplyInfoEntryFieldEntiy> zzEntryAttachFieldList = AdminAttachHelper.getFieldMap();

		for (SupplyInfoEntryFieldEntiy filedEntity : zzEntryAttachFieldList) {//处理对应目标附件字段处理逻辑
			if (filedEntity == null) {
				continue;
			}

			String entryKey = filedEntity.getEntryEntityName();
			/* 目标单附件分录 */
			DynamicObjectCollection mainKeyColl = tarInfo.getDynamicObjectCollection(entryKey);
			Set<String> existIds = mainKeyColl.stream().map(temp -> temp.getString(filedEntity.getOriEntryIdField())).collect(Collectors.toSet());
			/* 源单附件分录 */
			DynamicObjectCollection fileEntryColl = srcInfo.getDynamicObjectCollection(entryKey);

			if (fileEntryColl != null && fileEntryColl.size() > 0) {
				for (int index = 0; index < fileEntryColl.size(); index++) {
					DynamicObject fileEntryInfo = fileEntryColl.get(index);

					if (existIds.contains(fileEntryInfo.getString("id"))) {
						continue;
					}

					DynamicObject tarEntry = mainKeyColl.addNew();
					/* 资质类型 */
					tarEntry.set(filedEntity.getFileTypeField(), fileEntryInfo.get(filedEntity.getFileTypeField()));
					/* 发证日期 */
					tarEntry.set(filedEntity.getEffectField(), fileEntryInfo.get(filedEntity.getEffectField()));
					/* 有效日期 */
					tarEntry.set(filedEntity.getUnEffectField(), fileEntryInfo.get(filedEntity.getUnEffectField()));
					/* 是否已归档 */
					tarEntry.set(filedEntity.getIsUploadField(), fileEntryInfo.get(filedEntity.getIsUploadField()));
					/* 是否重命名 */
					tarEntry.set(filedEntity.getRenameField(), fileEntryInfo.get(filedEntity.getRenameField()));
					/* 序号 */
					tarEntry.set("seq", index + 1);

					if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getOriBillIdField()))) {
						/* 原单据ID */
						tarEntry.set(filedEntity.getOriBillIdField(), fileEntryInfo.get(filedEntity.getOriBillIdField()));
					} else {
						tarEntry.set(filedEntity.getOriBillIdField(), srcId);
					}
					if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getOriEntryIdField()))) {
						/* 原分录ID */
						tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.get(filedEntity.getOriEntryIdField()));
					} else {
						/* 原分录ID */
						tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.getString("id"));
					}
					if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getBillType()))) {
						/* 来源单据类型 */
						tarEntry.set(filedEntity.getBillType(), fileEntryInfo.get(filedEntity.getBillType()));
					} else {
						tarEntry.set(filedEntity.getBillType(), "yd_newreqbase");
					}

					/* 获取对应分录附件字段 多选基础资料数据 */
					DynamicObjectCollection fileAttachColl = fileEntryInfo.getDynamicObjectCollection(filedEntity.getFileField());
					if (fileAttachColl == null || fileAttachColl.size() == 0)
						continue;

					DynamicObjectCollection tarAttachColl = tarEntry.getDynamicObjectCollection(filedEntity.getFileField());
					for (int jIndex = 0; jIndex < fileAttachColl.size(); jIndex++) {
						DynamicObject att_bd = fileAttachColl.get(jIndex);//原附件
						DynamicObject bd = att_bd.getDynamicObject("fbasedataid");//复制附件

						//创建附件字段信息  (对应的是附件字段的表结构)
						DynamicObject attField = tarAttachColl.addNew();
						attField.set("fbasedataid", bd);
					}
				}
			}
		}
	}

	private static void saveAttData(String tarAttId,DynamicObject srcInfo,DynamicObject tarInfo,String [][] srcAttIds)
	 {
		 DynamicObjectCollection tarAttDataList= tarInfo.getDynamicObjectCollection(tarAttId);

		 for(int i=0;i<srcAttIds.length;i++)
		 {     
			 String[] row= srcAttIds[i];
			   String entryKey=row[0];
			   String attKey=row[1];
			   
			   DynamicObjectCollection  entrys= srcInfo.getDynamicObjectCollection(entryKey); 
			   for(int j=0;j<entrys.size();j++)
			   {
				     DynamicObject entryInfo= entrys.get(j);
					  
				     DynamicObjectCollection atts= entryInfo.getDynamicObjectCollection(attKey);
				     for(int k=0;k<atts.size();k++)
				     {
				    	 DynamicObject attInfo= atts.get(k);

				    	 DynamicObject tarAttData=tarAttDataList.addNew();
				    	 tarAttData.set("fbasedataid", attInfo.getDynamicObject("fbasedataid"));
				     }
			   }
		 }
	 }
	
	 /** 
	  * @fun 资料补充审核后，如果是新物料需求及供应商评价生成，反写附件到相应字段 
	  * ***/
	public static void reverseNewBatAttInfo(Long supInfoId)
	{
		 DynamicObject supInfo=BusinessDataServiceHelper.loadSingle(supInfoId, "yd_supplyinformationbill","id,yd_newmatreq");
		 DynamicObject newmatInfo= supInfo.getDynamicObject("yd_newmatreq");
		 if(newmatInfo==null)
		 {  return; 
		 }
		 newmatInfo=BusinessDataServiceHelper.loadSingle(newmatInfo.getPkValue(), EntityMetadataCache.getDataEntityType("yd_newmatreqbill"));
		 copyAttToNewMatbill(supInfo.getLong("id"),newmatInfo);
		 SaveServiceHelper.save(new DynamicObject[] {newmatInfo});
	}
	 
	//资料补充函附件携带到 新物料需求及供应商评价
	private static void copyAttToNewMatbill(Long supInfoId,DynamicObject tarInfo) {
		DynamicObject srcInfo = BusinessDataServiceHelper.loadSingle(supInfoId, EntityMetadataCache.getDataEntityType("yd_supplyinformationbill"));
		DynamicObject supInfo = srcInfo.getDynamicObject("yd_supplier");
		DynamicObject prodInfo = srcInfo.getDynamicObject("yd_producers");
		//目标分录字段，目标供应商，目标更新字段，目标附件字段，源分录字段，源附件类型，附件类型值，源附件字段
		String[][] dateFields = new String[][]
				{
						{"yd_file1entry", "yd_csup", "yd_cprod", "yd_spc1", "yd_nmr_att1", "yd_entryprocessstar", "yd_pstarattachmenttype", "A", "yd_pstarattachment"},//供应商书面调查表
						{"yd_file1entry", "yd_csup", "yd_cprod", "yd_spc2", "yd_nmr_att1", "yd_entryprocessstar", "yd_pstarattachmenttype", "B", "yd_pstarattachment"},//物料描述表
						{"yd_file1entry", "yd_csup", "yd_cprod", "yd_spc3", "yd_nmr_att1", "yd_entryprocessstar", "yd_pstarattachmenttype", "C", "yd_pstarattachment"},//3个批次样品 COA
						{"yd_file1entry", "yd_csup", "yd_cprod", "yd_spc4", "yd_nmr_att1", "yd_entryprocessstar", "yd_pstarattachmenttype", "D", "yd_pstarattachment"},//样品我司检测报告
						{"yd_file1entry", "yd_csup", "yd_cprod", "yd_spc5", "yd_nmr_att1", "yd_entryprocessstar", "yd_pstarattachmenttype", "E", "yd_pstarattachment"},//生产工艺流程图
						{"yd_file1entry", "yd_csup", "yd_cprod", "yd_spc6", "yd_nmr_att1", "yd_entryprocessstar", "yd_pstarattachmenttype", "F", "yd_pstarattachment"},//厂家企业标准
				};

		Map typeMap = AttachUtils.getAttMap();

		//物料分录信息,所有附件反写该分录的附件字段
		DynamicObject tarMatEntryInfo = getEntry(tarInfo, "yd_materialentry", "yd_asup", "yd_aprod", supInfo, prodInfo);
		DynamicObjectCollection allAttList = new DynamicObjectCollection();
		if (tarMatEntryInfo != null) {
			//物料分录附件字段
			allAttList = tarMatEntryInfo.getDynamicObjectCollection("yd_qadoc_att1");
			allAttList.clear();//清空历史物料附件
		}

		Set clearEntryAttSet = new HashSet();//分录附件清空集合

		for (int i = 0; i < dateFields.length; i++) {
			String tarEntryKey = dateFields[i][0];
			String tarSupKey = dateFields[i][1];
			String tarProdKey = dateFields[i][2];
			DynamicObject tarEntryInfo = getEntry(tarInfo, tarEntryKey, tarSupKey, tarProdKey, supInfo, prodInfo);
			if (tarEntryInfo == null) {
				continue;
			}

			String srcEntryKey = dateFields[i][5];
			String srcTypeKey = dateFields[i][6];
			String srcValue = dateFields[i][7];
			DynamicObjectCollection srcEntrys = srcInfo.getDynamicObjectCollection(srcEntryKey);
			Map attMap = (Map) typeMap.get(srcEntryKey);
			for (DynamicObject srcEntryInfo : srcEntrys) {
				String attType = srcEntryInfo.getString(srcTypeKey);
				if (StringUtils.equals(srcValue, attType)) {
					// update by hst 2023/10/31 修改赋值规则
					String typeLabel = null;
					if (attMap != null) {
						typeLabel = (String) attMap.get(srcValue);
					}

					String tarAttKey = dateFields[i][3];
					DynamicObjectCollection tarAttList = tarEntryInfo.getDynamicObjectCollection(tarAttKey);

					if (!clearEntryAttSet.contains(tarEntryInfo.getPkValue())) {//首次清空历史上次的附件
						tarAttList.clear();
						clearEntryAttSet.add(tarEntryInfo.getPkValue());
					}

					String srcAttKey = dateFields[i][8];
					DynamicObjectCollection srcAttList = srcEntryInfo.getDynamicObjectCollection(srcAttKey);
					if (srcAttList.size() > 0) {
						copyAttData4NewMat(srcAttList, tarAttList, typeLabel);//更新字段的分录附件复制
						if (tarMatEntryInfo != null) {
							copyAttData4NewMat(srcAttList, allAttList, typeLabel); //所有分录附件复制
						}
					}

				}
			}
		}
	}
	
	
	private static void copyAttData4NewMat(DynamicObjectCollection srcAttList,DynamicObjectCollection tarAttList,String attName)
	{
		 int i=0;
		 for(DynamicObject srcAttInfo:srcAttList)
	     {
			 i++;
	    	 DynamicObject newAttInfo= AttachUtils.copyAttachBaseData(attName+"-"+i,srcAttInfo.getDynamicObject("fbasedataid"));
	    	 DynamicObject tarAttData=tarAttList.addNew();
	    	 tarAttData.set("fbasedataid", newAttInfo);
	     } 
	}
	
	
	private static DynamicObject getEntry(DynamicObject info,String entryKey,String supKey,String prodKey,DynamicObject supInfo,DynamicObject prodInfo)
	{
		DynamicObjectCollection entrys= info.getDynamicObjectCollection(entryKey);
		for(DynamicObject entry:entrys)
		{
			DynamicObject tmpSupInfo=entry.getDynamicObject(supKey);
			DynamicObject tmpProdInfo=entry.getDynamicObject(prodKey);
			if(tmpSupInfo!=null && tmpProdInfo!=null)
			{
				if(supInfo.getPkValue().equals(tmpSupInfo.getPkValue()) && prodInfo.getPkValue().equals(tmpProdInfo.getPkValue()))
				{
					return entry;
				}
				
			} 
		}
		return null;
	}
	
	public static void createSupplier(Object newMatbillId)
	{//创建供应商
		//yd_newmatreqbill
		  DynamicObject billInfo=BusinessDataServiceHelper.loadSingle(newMatbillId, "yd_newmatreqbill","org,yd_gsupentry.yd_gsup,yd_gsupentry.yd_supdir");
		  String version = billInfo.getString("yd_version");
		  if ("1".equals(version)) {
			  DynamicObjectCollection entrys = billInfo.getDynamicObjectCollection("yd_admitentity");
			  for (DynamicObject entryInfo : entrys) {
				  if ("1".equals(entryInfo.getString("yd_isqualify"))) {
					  createSupplier(entryInfo.getDynamicObject("yd_admitsup"), billInfo.getDynamicObject("org"));
				  }
			  }
		  } else {
			  DynamicObjectCollection entrys = billInfo.getDynamicObjectCollection("yd_gsupentry");
			  for (DynamicObject entryInfo : entrys) {
				  if ("1".equals(entryInfo.getString("yd_supdir"))) {
					  createSupplier(entryInfo.getDynamicObject("yd_gsup"), billInfo.getDynamicObject("org"));
				  }
			  }
		  }
	}
	private static void createSupplier(DynamicObject srmSupInfo,DynamicObject org)
	{
		if(srmSupInfo==null||org==null)
		{
			return ;
		} 
		srmSupInfo=BusinessDataServiceHelper.loadSingle(srmSupInfo.getPkValue(), "srm_supplier","id,name,societycreditcode");
		org = BusinessDataServiceHelper.loadSingle(org.getPkValue(), "bos_adminorg", "id,number,name"); 
		
		String supplierGroupNumber = "waitgroup";//供应商组别
		SupplierBizHelper bizHelper=new SupplierBizHelper();
		DynamicObject bdSupplierInfo = bizHelper.getBdSupplier(GeneralFormatUtils.getString(srmSupInfo.get("societycreditcode")),"研发");
		if(bdSupplierInfo == null) 
		{//如无存在临时供应商数据   则进行 创建临时供应商数据
			bdSupplierInfo = bizHelper.createBdSupplier(
					srmSupInfo.getString("name")+"_研发", null, srmSupInfo.getString("societycreditcode"), supplierGroupNumber, 
								   org.getString("number")  //创建组织赋值
										, null) ;
		}
		
		if(bdSupplierInfo != null && !"1".equals(bdSupplierInfo.getString("enable"))) 
		{//如果对应供应商禁用  对供应商进行启用
			ABillServiceHelper.executeOperate("enable", "bd_supplier",
										new Object[] {bdSupplierInfo.getPkValue()}, OperateOption.create());
		}
		
		 
	}

	/**
	 * 【研发准入单】下推【原辅料准入单】时，携带下游的【现场审核反馈单】信息到【原辅料准入单】上
	 * @param newMatId
	 * @param rawMatinfo
	 * @author: hst
	 * @createDate: 2023/08/25
	 */
	public static void carryOsFeedBackInfo(Long newMatId,DynamicObject rawMatinfo) {
		QFilter idFilter = new QFilter("yd_newmatbill.id",QFilter.equals,newMatId);
		DynamicObject product = rawMatinfo.getDynamicObject("yd_tempproducers");
		DynamicObject supplier = rawMatinfo.getDynamicObject("yd_supplier");
		if (Objects.nonNull(product) && Objects.nonNull(supplier)) {
			QFilter supFilter = new QFilter("yd_sup.id",QFilter.equals,supplier.getPkValue());
			QFilter proFilter = new QFilter("yd_producers.id",QFilter.equals,product.getPkValue());
			// 现场审核反馈单
			DynamicObject bill = BusinessDataServiceHelper.loadSingle("yd_osfeedbackbill","yd_auditresult," +
					"yd_mattype,yd_matsupgrade,yd_pksupgrade,yd_auditdate,yd_auditscore,yd_att1," +
					"yd_auditresulttype,yd_evalevel,yd_tigsampletype",new QFilter[]{idFilter,supFilter,proFilter});
			if (Objects.nonNull(bill)) {
				rawMatinfo.set("yd_auditresulttype",bill.get("yd_auditresult"));
				String matType = bill.getString("yd_mattype");
				if ("1".equals(matType) || "2".equals(matType)) {
					rawMatinfo.set("yd_evalevel",bill.get("yd_pksupgrade"));
				} else {
					rawMatinfo.set("yd_evalevel",bill.get("yd_matsupgrade"));
				}
				rawMatinfo.set("yd_spotaccessdate",bill.get("yd_auditdate"));
				rawMatinfo.set("yd_spotaccessscore",bill.get("yd_auditscore"));
				rawMatinfo.set("yd_osfeedback",BusinessDataServiceHelper.loadSingle(bill.getPkValue(),"yd_osfeedbackf7"));
				DynamicObjectCollection billAttachs = bill.getDynamicObjectCollection("yd_att1");
				DynamicObjectCollection rawMatAttachs = rawMatinfo.getDynamicObjectCollection("yd_mataccatt5");
				for (DynamicObject billAttach : billAttachs) {
					DynamicObject rawMatAttach = rawMatAttachs.addNew();
					rawMatAttach.set("fbasedataid",billAttach.get("fbasedataid"));
				}
			}
		}

	}

	/**
	 * 【研发准入单】下推【原辅料准入单】时，携带基础信息到【原辅料准入单】上
	 * @param newMatId
	 * @param rawMatinfo
	 * @author: hst
	 * @createDate: 2023/10/30
	 */
	public static void bringBaseInfo(Long newMatId,DynamicObject rawMatinfo) {
		DynamicObject product = rawMatinfo.getDynamicObject("yd_tempproducers");
		DynamicObject supplier = rawMatinfo.getDynamicObject("yd_supplier");
		String matLevel = "";

		if (Objects.nonNull(product) && Objects.nonNull(supplier)) {
			DynamicObject bill = BusinessDataServiceHelper.loadSingle(newMatId, "yd_newmatreqbill");
			if (Objects.nonNull(bill)) {
				String version = bill.getString("yd_version");
				if ("1".equals(version)) {
					DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_admitentity");
					for (DynamicObject entry : entries) {
						DynamicObject tempPro = entry.getDynamicObject("yd_admitpro");
						DynamicObject tempSup = entry.getDynamicObject("yd_admitsup");
						if (Objects.nonNull(tempSup) && Objects.nonNull(tempPro)) {
							if (product.getString("number").equals(tempPro.getString("number"))
									&& supplier.getString("number").equals(tempSup.getString("number"))) {
								rawMatinfo.set("yd_matcode", Objects.nonNull(entry.getDynamicObject("yd_admitmaterial"))
										? entry.getString("yd_admitmaterial.number") : "");
								rawMatinfo.set("yd_material", entry.getDynamicObject("yd_admitmaterial"));
								rawMatinfo.set("yd_matlevtype", entry.getString("yd_enteradmitlevel"));
								rawMatinfo.set("yd_isslevel", entry.getString("yd_isadmitslevel"));
								rawMatinfo.set("yd_beinvolvekeymat", entry.getString("yd_beinvolvekeymat"));
								rawMatinfo.set("yd_matcat", entry.getString("yd_admitmatcat"));

								matLevel = entry.getString("yd_enteradmitlevel");
							}
						}
					}
				} else {
					rawMatinfo.set("yd_matcode", Objects.nonNull(bill.getDynamicObject("yd_material"))
							? bill.getString("yd_material.number") : "");
					rawMatinfo.set("yd_material", bill.getDynamicObject("yd_material"));
					rawMatinfo.set("yd_matlevtype", bill.getString("yd_enterlevel"));
					rawMatinfo.set("yd_isslevel", bill.getString("yd_isslevel"));
					rawMatinfo.set("yd_beinvolvekeymat", bill.getString("yd_isinvolve"));
					rawMatinfo.set("yd_matcat", bill.getString("yd_matcat"));

					matLevel = bill.getString("yd_enterlevel");
				}

				DynamicObjectCollection newEntries = rawMatinfo.getDynamicObjectCollection("entryentity");
				DynamicObject entry = newEntries.size() > 0 ? newEntries.get(newEntries.size() - 1)
						: newEntries.addNew();
				entry.set("yd_suptype", "new");
				entry.set("yd_producers", product);
				entry.set("yd_dealer", supplier);
				entry.set("yd_matlevel", matLevel.equals("a") ? "A类" : matLevel.equals("b") ? "B类" :
						matLevel.equals("c") ? "C类" : matLevel.equals("d") ? "D类" : "");

				DynamicObject material = rawMatinfo.getDynamicObject("yd_material");
				if (Objects.nonNull(product) && Objects.nonNull(supplier) && Objects.nonNull(material)) {
					QFilter qFilter = QFilter.of("yd_material.id = ? and yd_agent = ? and yd_producers = ?",
							material.get("id"), supplier.get("id"), product.get("id"));
					DynamicObject rawBill = BusinessDataServiceHelper.loadSingle("yd_rawsupsumbill", qFilter.toArray());

					if (Objects.nonNull(rawBill)) {
						rawMatinfo.set("yd_spec",rawBill.get("yd_spec"));
						rawMatinfo.set("yd_weight",rawBill.get("yd_weight"));
						rawMatinfo.set("yd_unit",rawBill.get("yd_unit"));
						rawMatinfo.set("yd_minunit",rawBill.get("yd_minunit"));
						rawMatinfo.set("yd_packmodel",rawBill.get("yd_specs"));
						rawMatinfo.set("yd_storagecon",rawBill.get("yd_storagecon"));
						rawMatinfo.set("yd_shelflife",rawBill.get("yd_manmonth"));
						rawMatinfo.set("yd_matsource",rawBill.get("yd_matsource"));
						rawMatinfo.set("yd_isexplosive",rawBill.get("yd_isexplosive"));
						rawMatinfo.set("yd_matsourceexp",rawBill.get("yd_matsourceexp"));
						rawMatinfo.set("yd_hasallergen",rawBill.get("yd_hasallergen"));
						rawMatinfo.set("yd_allergysource",rawBill.get("yd_allergysource"));
						rawMatinfo.set("yd_matlevtype",rawBill.get("yd_matleveltype"));
						rawMatinfo.set("yd_matcat",rawBill.get("yd_matcat"));
						rawMatinfo.set("yd_beinvolvekeymat",rawBill.get("yd_beinvolvekeymat"));
						rawMatinfo.set("yd_isslevel",rawBill.get("yd_isslevel"));
						entry.set("yd_manufacturerplace", rawBill.getString("yd_address"));
						entry.set("yd_origin", rawBill.getString("yd_origin"));
						entry.set("yd_matlevel", rawBill.getString("yd_matleveltype"));
						entry.set("yd_supmatname", rawBill.getString("yd_supmatname"));
						entry.set("yd_supplierstate", getState(rawBill.getDynamicObject("yd_supplieraccesspot")));
						entry.set("yd_manufacturerstate", getState(rawBill.getDynamicObject("yd_supplieraccesspot")));
					}
				}

				DynamicObjectCollection infos = bill.getDynamicObjectCollection("yd_materialentry");
				infos.forEach(info -> {
					DynamicObject tempPro = info.getDynamicObject("yd_aprod");
					DynamicObject tempSup = info.getDynamicObject("yd_asup");
					if (Objects.nonNull(tempSup) && Objects.nonNull(tempPro)) {
						if (product.getString("number").equals(tempPro.getString("number"))
								&& supplier.getString("number").equals(tempSup.getString("number"))) {
							entry.set("yd_origin",info.get("yd_origin"));
							entry.set("yd_prdlicense",info.get("yd_prodlicense"));
							entry.set("yd_impstd",info.get("yd_execstd"));
						}
					}
				});

				infos = bill.getDynamicObjectCollection("yd_file1entry");
				infos.forEach(info -> {
					DynamicObject tempPro = info.getDynamicObject("yd_cprod");
					DynamicObject tempSup = info.getDynamicObject("yd_csup");
					if (Objects.nonNull(tempSup) && Objects.nonNull(tempPro)) {
						if (product.getString("number").equals(tempPro.getString("number"))
								&& supplier.getString("number").equals(tempSup.getString("number"))) {
							entry.set("yd_supmatname",info.get("yd_supmatname"));
						}
					}
				});
			}
		}
	}

	/**
	 * 【研发准入单】下推【原辅料准入单】时，携带现场审核反馈信息到【原辅料准入单】上
	 * @param newMatId
	 * @param rawMatinfo
	 * @author: hst
	 * @createDate: 2023/10/30
	 */
	public static void bringOsFeedBackInfo(Long newMatId,DynamicObject rawMatinfo) {
		DynamicObject product = rawMatinfo.getDynamicObject("yd_tempproducers");
		DynamicObject supplier = rawMatinfo.getDynamicObject("yd_supplier");
		if (Objects.nonNull(product) && Objects.nonNull(supplier)) {
			DynamicObject bill = BusinessDataServiceHelper.loadSingle(newMatId, "yd_newmatreqbill");
			if (Objects.nonNull(bill)) {
				DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_hsupentry");
				entries.forEach(entry -> {
					DynamicObject tempPro = entry.getDynamicObject("yd_hprod");
					DynamicObject tempSup = entry.getDynamicObject("yd_hsup");
					if (Objects.nonNull(tempSup) && Objects.nonNull(tempPro)) {
						if (product.getString("number").equals(tempPro.getString("number"))
								&& supplier.getString("number").equals(tempSup.getString("number"))) {
							rawMatinfo.set("yd_auditresulttype",entry.get("yd_auditresulttype"));
							rawMatinfo.set("yd_evalevel",entry.get("yd_supevalevel"));
							rawMatinfo.set("yd_tigsampletype",entry.get("yd_tigsampletype"));
							rawMatinfo.set("yd_cmpresulttype",entry.get("yd_cmpresulttype"));
							rawMatinfo.set("yd_wftitle",entry.get("yd_wftitle"));
							rawMatinfo.set("yd_spotaccessdate",entry.get("yd_osauditbegindate"));
							rawMatinfo.set("yd_spotenddate",entry.get("yd_osauditdate"));
							rawMatinfo.set("yd_spotaccessscore",entry.get("yd_osauditscore"));
							rawMatinfo.set("yd_osfeedback",entry.get("yd_osfeedback"));
							rawMatinfo.set("yd_tighten","2".equals(entry.getString("yd_tigsampletype"))
									? "0" : entry.getString("yd_tigsampletype"));
							rawMatinfo.set("yd_tightenreason",entry.get("yd_tightenreason"));
							rawMatinfo.set("yd_stridate",entry.get("yd_stridate"));
							DynamicObjectCollection oriAtts = entry.getDynamicObjectCollection("yd_mataccatt");
							DynamicObjectCollection tarAtts = rawMatinfo.getDynamicObjectCollection("yd_mataccatt5");
							tarAtts.clear();
							for (DynamicObject oriAtt : oriAtts) {
								if (Objects.nonNull(oriAtt)) {
									DynamicObject newAttInfo = AttachUtils.copyAttachBaseData("", oriAtt.getDynamicObject("fbasedataid"));
									DynamicObject tempAtt = tarAtts.addNew();
									tempAtt.set("fbasedataid",newAttInfo);
								}
							}
						}
					}
				});
			}
		}
	}

	/**
	 * 【研发准入单】下推【原辅料准入单】时，携带书面评分信息到【原辅料准入单】上
	 * @param newMatId
	 * @param rawMatinfo
	 * @author: hst
	 * @createDate: 2023/10/30
	 */
	public static void bringScoreInfo(Long newMatId,DynamicObject rawMatinfo) {
		DynamicObject product = rawMatinfo.getDynamicObject("yd_tempproducers");
		DynamicObject supplier = rawMatinfo.getDynamicObject("yd_supplier");
		if (Objects.nonNull(product) && Objects.nonNull(supplier)) {
			DynamicObject bill = BusinessDataServiceHelper.loadSingle(newMatId, "yd_newmatreqbill");
			if (Objects.nonNull(bill)) {
				DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_scoreentry");
				entries.forEach(entry -> {
					DynamicObject tempPro = entry.getDynamicObject("yd_eprod");
					DynamicObject tempSup = entry.getDynamicObject("yd_esup");
					if (Objects.nonNull(tempSup) && Objects.nonNull(tempPro)) {
						if (product.getString("number").equals(tempPro.getString("number"))
								&& supplier.getString("number").equals(tempSup.getString("number"))) {
							rawMatinfo.set("yd_credscore",entry.get("yd_credibility"));
							rawMatinfo.set("yd_matriskscore",entry.get("yd_charisk"));
							rawMatinfo.set("yd_prdprcscore",entry.get("yd_prdprcrisk"));
							rawMatinfo.set("yd_sysmrgscore",entry.get("yd_mrgrisk"));
							rawMatinfo.set("yd_writtenaccessscore",entry.get("yd_qascore"));
							rawMatinfo.set("yd_ismix",entry.get("yd_ismix"));
							rawMatinfo.set("yd_mixreason",entry.get("yd_mixreason"));
							rawMatinfo.set("yd_issterilizate",entry.get("yd_issterilizate"));
							rawMatinfo.set("yd_sterreason",entry.get("yd_sterreason"));
						}
					}
				});
			}
		}
	}

	/**
	 * 【研发准入单】下推【原辅料准入单】时，携带物料详细信息到【原辅料准入单】上
	 * @param newMatId
	 * @param rawMatinfo
	 * @author: hst
	 * @createDate: 2023/10/30
	 */
	public static void bringMaterialDetailInfo(Long newMatId,DynamicObject rawMatinfo) {
		DynamicObject product = rawMatinfo.getDynamicObject("yd_tempproducers");
		if (Objects.nonNull(product)) {
			DynamicObject bill = BusinessDataServiceHelper.loadSingle(newMatId, "yd_newmatreqbill");
			if (Objects.nonNull(bill)) {
				DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_materialentry");
				entries.forEach(entry -> {
					String supName = entry.getString("yd_anewsupname");
					if (StringUtils.isNotBlank(supName)) {
						if (supName.equals(product.getString("name"))) {
							rawMatinfo.set("yd_spec",entry.get("yd_spec"));
							rawMatinfo.set("yd_weight",entry.get("yd_weight"));
							rawMatinfo.set("yd_unit",entry.get("yd_unit"));
							rawMatinfo.set("yd_minunit",entry.get("yd_minunit"));
							rawMatinfo.set("yd_packmodel",entry.get("yd_model"));
							rawMatinfo.set("yd_storagecon",entry.get("yd_storagecon"));
							rawMatinfo.set("yd_capacity",entry.get("yd_capacity"));
							rawMatinfo.set("yd_shelflife",entry.get("yd_shelflife"));
							rawMatinfo.set("yd_delcycle",entry.get("yd_deliverycyc"));
							rawMatinfo.set("yd_packaging",entry.get("yd_packing"));
							rawMatinfo.set("yd_hasallergen",entry.get("yd_hasallergen"));
							rawMatinfo.set("yd_allergysource",entry.get("yd_allergysource"));
							rawMatinfo.set("yd_matsource",entry.get("yd_matsource"));
//							rawMatinfo.set("yd_location",entry.get("yd_location"));
						}
					}
				});
			}
		}
	}

	/**
	 * 【研发准入单】下推【原辅料准入单】时，携带等级评估信息到【原辅料准入单】上
	 * @param newMatId
	 * @param rawMatinfo
	 * @author: hst
	 * @createDate: 2023/10/30
	 */
	public static void bringGradeAssessInfo(Long newMatId,DynamicObject rawMatinfo) {
		DynamicObject product = rawMatinfo.getDynamicObject("yd_tempproducers");
		DynamicObject supplier = rawMatinfo.getDynamicObject("yd_supplier");
		if (Objects.nonNull(product) && Objects.nonNull(supplier)) {
			DynamicObject bill = BusinessDataServiceHelper.loadSingle(newMatId, "yd_newmatreqbill");
			if (Objects.nonNull(bill)) {
				DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_auditentry");
				entries.forEach(entry -> {
					DynamicObject tempPro = entry.getDynamicObject("yd_fprod");
					DynamicObject tempSup = entry.getDynamicObject("yd_fsup");
					if (Objects.nonNull(tempSup) && Objects.nonNull(tempPro)) {
						if (product.getString("number").equals(tempPro.getString("number"))
								&& supplier.getString("number").equals(tempSup.getString("number"))) {
							rawMatinfo.set("yd_allfilecomplete",entry.get("yd_allfilecomplete"));
							rawMatinfo.set("yd_supevtype",entry.get("yd_evalevel"));
							rawMatinfo.set("yd_onsiteaudit",entry.get("yd_onsiteaudit"));
							rawMatinfo.set("yd_hasspotaccess",entry.get("yd_hasspotaccess"));
							rawMatinfo.set("yd_onsitbegindate",entry.get("yd_compbegindate"));
							rawMatinfo.set("yd_onsitdate",entry.get("yd_compenddate"));
							rawMatinfo.set("yd_noauditres",entry.get("yd_noauditres"));
							rawMatinfo.set("yd_stopdevres",entry.get("yd_stopdevres"));
							rawMatinfo.set("yd_pqtype",entry.get("yd_pqtype"));
							rawMatinfo.set("yd_pqtypereason",entry.get("yd_pqreason"));
							rawMatinfo.set("yd_stabletype",entry.get("yd_stabletype"));
							rawMatinfo.set("yd_stablereason",entry.get("yd_stablereason"));
							rawMatinfo.set("yd_opiniontype",entry.get("yd_opiniontype"));
							rawMatinfo.set("yd_opinionreason",entry.get("yd_opinionreason"));
							rawMatinfo.set("yd_protype",entry.get("yd_suptype"));
							rawMatinfo.set("yd_reason",entry.get("yd_reason"));
							rawMatinfo.set("yd_location",entry.get("yd_location"));
						}
					}
				});
			}
		}
	}

	/**
	 * 获取供应商状态
	 * @param accessPot
	 * @return
	 */
	public static String getState (DynamicObject accessPot) {
		Map<String, String> map = new HashMap<String, String>() {{
			put("00", "2");
			put("05", "2");
			put("10", "2");
			put("15", "2");
			put("20", "2");
			put("25", "2");
			put("30", "2");
			put("35", "2");
			put("40", "2");
			put("50", "3");
			put("55", "4");
			put("60", "4");
			put("65", "4");
			put("70", "4");
			put("75", "4");
			put("80", "4");
			put("85", "4");
			put("90", "1");
			put("95", "1");
			put("100", "5");
			put("105", "6");
			put("110", "6");
			put("115", "7");
			put("120", "8");
			put("45", "9");
		}};

		if (Objects.nonNull(accessPot)) {
			return map.get(accessPot.getString("number"));
		} else {
			return "";
		}
	}
}
