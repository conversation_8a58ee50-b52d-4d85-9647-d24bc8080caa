package kd.bos.tcbj.srm.admittance.helper;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

import kd.bos.algo.DataSet;
import kd.bos.servicehelper.QueryServiceHelper;
import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.outbill.imp.SaleOutBillMserviceImpl;
import kd.bos.tcbj.srm.admittance.utils.BotpUtils;
import kd.bos.tcbj.srm.admittance.utils.CommonUtils;

/**
 * @auditor yanzuwei
 * @date 2022年8月10日
 * 
 */
public class OsFeedBackHelper 
{
    /**
     * @fun 【现场审核反馈单】审批通过后，根据原辅料供应商查询台账，更新字段：现场审核（日期），取反馈单上的审核日期（yd_auditdate）； 
     * **/
    public static void updateRssInfo4Audit(DynamicObject info)
    {
    	DynamicObject fbInfo=BusinessDataServiceHelper.loadSingle(info.getPkValue(),"yd_osfeedbackbill");
		 // update by hst 2024/04/26 调整为取结束日期
		 Date auditDate=fbInfo.getDate("yd_endauditdate");
//    	 Date auditDate=fbInfo.getDate("yd_auditdate");
    	 if(auditDate==null)
    	 {
    		 return;
    	 }
		 DynamicObject supInfo=(DynamicObject)fbInfo.get("yd_sup");
		 DynamicObject prodInfo=fbInfo.getDynamicObject("yd_producers");
		 QFilter f=QFilter.of("yd_agent=? and yd_producers=?", new Object[] {supInfo.getPkValue(),prodInfo.getPkValue()}); 
	     DynamicObject[]  rssList=BusinessDataServiceHelper.load("yd_rawsupsumbill", "id,yd_oadate", f.toArray());
	     if(rssList==null||rssList.length==0)
	     {
	    	 return;
	     }
	     for(DynamicObject rssInfo:rssList)
	     {
	    	 rssInfo.set("yd_oadate",auditDate); 
	     }
		 SaveServiceHelper.save(rssList);
    }
    /**
     * @fun "现场审核反馈单提交时反写字段：现场审核日期（取审核日期yd_auditdate）、现场审核单号F7（要改造字段，变成F7，取反馈单ID）、本次准入是否现场审核（改为是）  
     * **/
    public static void reverseSaveRowSup4Submit(DynamicObject info)
    {
    	DynamicObject fbInfo=BusinessDataServiceHelper.loadSingle(info.getPkValue(),"yd_osfeedbackbill");
    	DynamicObject rawInfo=fbInfo.getDynamicObject("yd_rawmataccbill");
    	if(rawInfo==null)
    	{
    		return;
    	}
        rawInfo=BusinessDataServiceHelper.loadSingle(rawInfo.getPkValue(),"yd_rawmatsupaccessbill");
        rawInfo.set("yd_osfeedback",fbInfo);
        rawInfo.set("yd_hasspotaccess", true);
        rawInfo.set("yd_spotaccessdate",fbInfo.getDate("yd_auditdate"));
		rawInfo.set("yd_spotenddate",fbInfo.getDate("yd_endauditdate"));
        SaveServiceHelper.save(new DynamicObject[] {rawInfo});
    }
    
    /**
     * @fun 现场审核反馈单流程结束时反写字段：现场审核结论（取审核反馈yd_feedback）、现场审核得分（取得分yd_score）  
     * 质量等级（如果物料类型yd_mattype是原辅料，则取质量等级yd_matsupgrade）,yzw
     * **/
    public static void reverseSaveRowSup4Audit(DynamicObject info)
    {
    	DynamicObject fbInfo=BusinessDataServiceHelper.loadSingle(info.getPkValue(),"yd_osfeedbackbill");
    	DynamicObject rawInfo=fbInfo.getDynamicObject("yd_rawmataccbill");
    	if(rawInfo==null)
    	{
    		return;
    	}
        rawInfo=BusinessDataServiceHelper.loadSingle(rawInfo.getPkValue(),"yd_rawmatsupaccessbill");
        rawInfo.set("yd_wftitle", fbInfo.getString("yd_feedback"));
        rawInfo.set("yd_spotaccessscore",fbInfo.getString("yd_auditscore"));
        if ("3".equals(fbInfo.getString("yd_mattype"))||"5".equals(fbInfo.getString("yd_mattype"))) {
        	rawInfo.set("yd_evalevel",fbInfo.get("yd_matsupgrade"));
        }
		// update by hst 2024/06/12 增加附件反写
		DynamicObjectCollection oriAtts = fbInfo.getDynamicObjectCollection("yd_att1");
		DynamicObjectCollection tarAtts = rawInfo.getDynamicObjectCollection("yd_mataccatt5");
		tarAtts.clear();
		for (DynamicObject oriAtt : oriAtts) {
			if (Objects.nonNull(oriAtt)) {
				DynamicObject newAttInfo = AttachUtils.copyAttachBaseData("", oriAtt.getDynamicObject("fbasedataid"));
				DynamicObject tempAtt = tarAtts.addNew();
				tempAtt.set("fbasedataid",newAttInfo);
			}
		}
        SaveServiceHelper.save(new DynamicObject[] {rawInfo});
    }
    
    static DynamicObject getSupEntry(DynamicObject billInfo,String entryField,String supField, String prodField,Object supId,Object prodId)
    {
    	DynamicObjectCollection entrys=billInfo.getDynamicObjectCollection(entryField);
    	for(DynamicObject entryInfo:entrys)
    	{
    		DynamicObject supInfo=entryInfo.getDynamicObject(supField);
    		DynamicObject prodInfo=entryInfo.getDynamicObject(prodField);
    		if(supInfo!=null&&supInfo.getPkValue().equals(supId)&&prodInfo.getPkValue().equals(prodId))
    		{
    			return entryInfo;
    		}
    	}
    	return null;
    }
    
    /**
     * @fun "现场审核反馈单提交时反写《供应商现场审核记录》分录，
     * 先查询分录中是否已经存在反馈单上的供应商，存在则反写字段，
     * 不存在则新建一行分录，需反写字段：现场审核日期（取审核日期yd_auditdate）、现场审核单号F7（要改造字段，变成F7，取反馈单ID）、本次准入是否现场审核（改为是）
     * **/
    public static void reverseSaveNewMat4Submit(DynamicObject info)
    {
    	DynamicObject fbInfo=BusinessDataServiceHelper.loadSingle(info.getPkValue(),"yd_osfeedbackbill");
    	DynamicObject srcInfo=fbInfo.getDynamicObject("yd_newmatbill");
    	if(srcInfo==null)
    	{
    		return;
    	}
    	srcInfo=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_newmatreqbill");
    	DynamicObject supInfo=fbInfo.getDynamicObject("yd_sup");
    	DynamicObject prodInfo=fbInfo.getDynamicObject("yd_producers");
    	if(supInfo==null||prodInfo==null)
    	{
    		return;
    	}
    	Date auditDate=fbInfo.getDate("yd_auditdate");
		Date endDate=fbInfo.getDate("yd_endauditdate");
    	DynamicObject entryInfo=getSupEntry(srcInfo,"yd_hsupentry","yd_hsup","yd_hprod",supInfo.getPkValue(),prodInfo.getPkValue());
    	if(entryInfo==null)
    	{
    		entryInfo=srcInfo.getDynamicObjectCollection("yd_hsupentry").addNew();
    		entryInfo.set("yd_hsup", supInfo);
    		entryInfo.set("yd_hprod", prodInfo);
    	}
    	entryInfo.set("yd_osauditbegindate", auditDate);
		entryInfo.set("yd_osauditdate", endDate);
    	entryInfo.set("yd_isosaudit", "1");
    	entryInfo.set("yd_osfeedback", info);
    	 SaveServiceHelper.save(new DynamicObject[] {srcInfo}); 
    	
    	
    }
    /**
     * @fun 现场审核反馈单审核时反写《供应商现场审核记录》分录，先查询分录中是否已经存在反馈单上的供应商，存在则反写字段，
     * 不存在则新建一行分录，需反写字段：现场审核结论（取审核反馈yd_feedback）、现场审核得分（取得分yd_score）"
     * **/
    public static void reverseSaveNewMat4Audit(DynamicObject info)
    {
    	DynamicObject fbInfo=BusinessDataServiceHelper.loadSingle(info.getPkValue(),"yd_osfeedbackbill");
    	DynamicObject srcInfo=fbInfo.getDynamicObject("yd_newmatbill");
    	if(srcInfo==null)
    	{
    		return;
    	}
    	srcInfo=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_newmatreqbill");
    	DynamicObject supInfo=fbInfo.getDynamicObject("yd_sup");
    	DynamicObject prodInfo=fbInfo.getDynamicObject("yd_producers");
    	if(supInfo==null||prodInfo==null)
    	{
    		return;
    	} 
    	DynamicObject entryInfo=getSupEntry(srcInfo,"yd_hsupentry","yd_hsup","yd_hprod",supInfo.getPkValue(),prodInfo.getPkValue());
    	if(entryInfo==null)
    	{
    		entryInfo=srcInfo.getDynamicObjectCollection("yd_hsupentry").addNew();
    		entryInfo.set("yd_hsup", supInfo);
    		entryInfo.set("yd_hprod", prodInfo);
    	}
    	entryInfo.set("yd_osauditcon", fbInfo.getString("yd_auditresult"));
    	entryInfo.set("yd_osauditscore",  fbInfo.getBigDecimal("yd_auditscore"));
		// update by hst 2023/10/30 新增反写字段
		entryInfo.set("yd_osfeedback",fbInfo.getPkValue());
		entryInfo.set("yd_wftitle",fbInfo.getString("yd_feedback"));
		entryInfo.set("yd_auditresulttype",fbInfo.getString("yd_auditresult"));
		if ("3".equals(fbInfo.getString("yd_mattype"))||"5".equals(fbInfo.getString("yd_mattype"))) {
			entryInfo.set("yd_supevalevel", fbInfo.getString("yd_matsupgrade"));
		}
		DynamicObjectCollection oriAtts = fbInfo.getDynamicObjectCollection("yd_att1");
		DynamicObjectCollection tarAtts = entryInfo.getDynamicObjectCollection("yd_mataccatt");
		tarAtts.clear();
		for (DynamicObject oriAtt : oriAtts) {
			if (Objects.nonNull(oriAtt)) {
				DynamicObject newAttInfo = AttachUtils.copyAttachBaseData("", oriAtt.getDynamicObject("fbasedataid"));
				DynamicObject tempAtt = tarAtts.addNew();
				tempAtt.set("fbasedataid",newAttInfo);
			}
		}
        SaveServiceHelper.save(new DynamicObject[] {srcInfo}); 
    	
    	
    }
	
    //根据供应商获取最新的现场审核反馈单记录,yzw
	  public static DynamicObject findLastOsFeedbackBill(Long supId)
		{
		    QFilter f=QFilter.of("yd_sup=? and billstatus='C'", new Object[] {supId}); 
			DynamicObject[]  infos=BusinessDataServiceHelper.load("yd_osfeedbackbill", "id", f.toArray());
			if(infos!=null&&infos.length>0)
			{
				return BusinessDataServiceHelper.loadSingle(infos[0].getPkValue(),"yd_osfeedbackbill","id,billno,yd_auditdate,yd_auditscore");
			}
		    return null;
		}
	  
	  //生成整改单，并保存提交
	 public static void  genCorrectionBill(DynamicObject info)
	 {
		 DynamicObject tarInfo= BotpUtils.pushBill(info.getPkValue().toString(), "yd_osfeedbackbill", "yd_correctionbill","1484049378195800064");
		 CommonUtils.doSaveAndSubmit("yd_correctionbill", tarInfo);
		  
	 }

	/**
	 * 新增准入单时，如果无需再次现场审核时，需要抓取旧现场审核反馈单信息
	 * @author: hst
	 * @createDate: 2023/01/17
	 * @param bill
	 */
	public static void getNewestSiteAudit (DynamicObject bill) {
		DynamicObject supplier = bill.getDynamicObject("yd_supplier");
		DynamicObject producer = bill.getDynamicObject("yd_tempproducers");
		if (Objects.nonNull(supplier) && Objects.nonNull(producer)) {
			// 代理商+生产商匹配现场审核单的供应商名称+生产商并且是审核日期最新的审核状态的现场审核单
			QFilter supFilter = new QFilter("yd_sup.number",QFilter.equals,supplier.getString("number"));
			QFilter proFilter = new QFilter("yd_producers.number",QFilter.equals,producer.getString("number"));
			QFilter stuFilter = new QFilter("billstatus",QFilter.equals,"C");
			DataSet data = QueryServiceHelper.queryDataSet("OsFeedBackHelper","yd_osfeedbackbill",
					"id",new QFilter[]{supFilter,proFilter,stuFilter},"yd_auditdate desc",1);
			if (data.hasNext()) {
				DynamicObject feedBack = BusinessDataServiceHelper.loadSingle(data.next().getString("id"),"yd_osfeedbackbill");
				if (Objects.nonNull(feedBack)) {
					bill.set("yd_osfeedback",feedBack);
//					bill.set("yd_hasspotaccess", true);
					bill.set("yd_spotaccessdate",feedBack.getDate("yd_auditdate"));
					bill.set("yd_wftitle", feedBack.getString("yd_feedback"));
					bill.set("yd_spotaccessscore",feedBack.getString("yd_auditscore"));
					if ("3".equals(feedBack.getString("yd_mattype"))||"5".equals(feedBack.getString("yd_mattype"))) {
						bill.set("yd_evalevel",feedBack.get("yd_matsupgrade"));
					}
				}
			}
		}
	}
}
