package kd.bos.tcbj.srm.admittance.helper;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.oabill.utils.OABillManageUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import com.google.gson.Gson;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.ImportBillUtils;

/**
 * @auditor yanzuwei
 * @date 2022年8月15日
 * @ desc 包装物料试机打样申请单
 */
public class PackmatApplyHelper
{
	private static Map parseJson(String json)
	{
	   Map jsonMap=new Gson().fromJson(json, Map.class);
	   return jsonMap;
	}
	
	public static Map transMap(Map oaMap)
	{//OA格式转换为普通格式
		Map tarMap=new HashMap();
		List fields=(List)oaMap.get("fields");
		for(int i=0;i<fields.size();i++)
		{
			Map fieldMap=(Map)fields.get(i);
			System.out.println(fieldMap);
			String type=(String)fieldMap.get("type");
			String item=(String)fieldMap.get("item");
			
			if(type.equals("List"))
			{//分录
				 List vList=(List)fieldMap.get("val");
				 List tarList=new ArrayList();
				 if(vList!=null)
				 {
				  for(int j=0;j<vList.size();j++)
				   {
					  Map entryMap=(Map)vList.get(j);
					  List entryList=(List)entryMap.get("line");
					  if(entryList!=null)
					  {
						  Map tarEntMap=new HashMap();
						  for(int k=0;k<entryList.size();k++)
						  {
							 Map entMap= (Map)entryList.get(k);
							  tansItem(entMap,tarEntMap); 
						  }
						  tarList.add(tarEntMap);
					  }
				   }
				 }
				 tarMap.put(item, tarList);
				 
			}else
			{//基础数据类型
			     tansItem(fieldMap,tarMap);
			}
			
		}
		
		return tarMap;
	}
	private static void tansItem(Map fieldMap,Map tarMap)
	{
		String type=(String)fieldMap.get("type");
		String item=(String)fieldMap.get("item");
		Object value=null;
		if(type.equals("Enumer"))
		{
			Map vMap=(Map)fieldMap.get("val");
			if(vMap!=null)
			{
				Iterator it=vMap.values().iterator();
				StringBuffer bf=new StringBuffer();
				while(it.hasNext())
				{
					Object emValue=it.next();
					bf.append(emValue).append(",");
				} 
				if(bf.length()>0)
				{
					bf.deleteCharAt(bf.length()-1);
				}
				value=bf.toString();
			}
			
		} else  
		{
		   value=fieldMap.get("val");  
		} 
		tarMap.put(item, value);
		 
	}
	public static Object  saveBill(String json) {
		DynamicObject info = BusinessDataServiceHelper.newDynamicObject("yd_packmatcommissbill");
		try {
			Map formatMap = new HashMap();
			Map oaMap = OABillManageUtil.parseJson(json);
			Map cosmicMap = OABillManageUtil.transJsonToMap(oaMap, formatMap);
			// update by hst 2023/05/05 字段映射修改为可配置
			String formId = oaMap.get("fdTemplateId").toString();
			// update by hst 2024/07/01 增加单据类型
			Map<String, Object> result = getFieldMapping(formId);
			if (Objects.isNull(result) || result.size() == 0) {
				throw new KDBizException("formId:" + formId + "未配置字段映射");
			} else {
				Map<String, String> cfgFormat = (Map<String, String>) result.get("yd_format_tag");
				Object[][] cfgMap = (Object[][]) result.get("yd_mapping_tag");
				// OA传递数据封装
				cosmicMap = formatOAData(cosmicMap, cfgFormat);
				ImportBillUtils.convertBill(info, cosmicMap, cfgMap, null);
				info.set("yd_beused", "true");  // 是否已解析
			}
		} catch (Exception e) {
			if (StringUtils.isNotBlank(e.getMessage())) {
				String errMsg = e.getMessage();
				info.set("yd_errmsg", errMsg.length() > 500 ? errMsg.substring(0,500) : errMsg);
			}
		}
		String num = DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS");
		info.set("billno", "编码" + num);
		info.set("billstatus", "A");
		info.set("yd_oacallbackstate", "已审核");  // OA单据状态-对应准入节点编码
		info.set("yd_oadata", "OA表单数据");
		info.set("yd_oadata_tag", json);
		SaveServiceHelper.saveOperate("yd_packmatcommissbill", new DynamicObject[]{info});
		return info.getPkValue();
	}

	/**
	 * 解析字段映射
	 * @author: hst
	 * @createDate: 2024/04/23
	 */
	public static Map<String,Object> getFieldMapping (String formId) {
		Map<String,Object> resutls = new HashMap<>();
		//获取参数配置
		QFilter typeFilter = new QFilter("yd_bill",QFilter.equals,"3");
		QFilter formFilter = new QFilter("yd_formid",QFilter.equals,formId);
		QFilter statusFilter = new QFilter("billstatus",QFilter.equals,"C");
		// update by hst 2024/04/23 增加单据类型
		DataSet configs = QueryServiceHelper.queryDataSet("OABillManageHelper","yd_paramconfigure",
				"yd_mapping_tag,yd_billtype,yd_format_tag",new QFilter[]{typeFilter,formFilter,statusFilter}, null);
		if (configs.hasNext()) {
			Row row = configs.next();
			String type = row.getString("yd_billtype");
			String mapping = row.getString("yd_mapping_tag");
			String format = row.getString("yd_format_tag");
			Object[][] mappings = OABillManageUtil.jsonArrayToArray(mapping);
			Map<String, String> formats = OABillManageUtil.jsonArrayToMap(format);
			resutls.put("billtype",type);
			resutls.put("yd_mapping_tag",mappings);
			resutls.put("yd_format_tag",formats);
			return resutls;
		}
		return resutls;
	}

	/**
	 * 封装OA数据
	 * @param cosmicMap
	 * @param cfgFormat
	 * @return
	 * @author: hst
	 * @createDate: 2024/07/19
	 */
	private static Map formatOAData (Map cosmicMap, Map<String, String> cfgFormat) {
		if (Objects.nonNull(cfgFormat) && cfgFormat.size() > 0) {
			Map newMap = new HashMap();
			cosmicMap.forEach((key, value) -> {
				if (cfgFormat.containsKey(key)) {
					String entryId = cfgFormat.get(key);

					List<Map> details;
					if (newMap.containsKey(entryId)) {
						details = (List<Map>) newMap.get(entryId);
					} else {
						details = new ArrayList<>();
						newMap.put(entryId, details);
					}

					String dataStr = value.toString().replace("[","");
					dataStr = dataStr.replace("]","");

					String[] datas = dataStr.split(", ");

					for (int i = 0; i < datas.length; i++) {
						String data = datas[i];
						Map detail;

						if (i >= details.size()) {
							detail = new HashMap();
							details.add(detail);
						} else {
							detail = details.get(i);
						}

						detail.put(key, data.replace(" ", ""));
						details.set(i, detail);
					}
				} else {
					newMap.put(key, value);
				}
			});

			return newMap;
		}
		return cosmicMap;
	}
}
