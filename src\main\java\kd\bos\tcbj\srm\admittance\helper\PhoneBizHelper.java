package kd.bos.tcbj.srm.admittance.helper;

import java.util.List;

import kd.bos.entity.api.ApiResult;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.servicehelper.workflow.MessageCenterServiceHelper;

/**
 * 手机发送信息业务工具定义
 * @auditor liefeng<PERSON>u
 * @date 2022年6月8日
 * 
 */
public class PhoneBizHelper {
	
	/**
	 * 根据手机号与内容发送短信
	 * @param phone
	 * @param content
	 * @return
	 */
	public ApiResult sendPhone(List<String> phone, String content) {
	//	MessageCenterServiceHelper.sendMessage(MessageInfo messageInfo) 
		ShortMessageInfo messageInfo = new ShortMessageInfo();
		messageInfo.setPhone(phone);
		messageInfo.setCountryCode(CountryCode.CN);
		messageInfo.setMessage(content);
		//short
		
		MessageHandler.sendShortMessage(messageInfo); 
		return null;
	}
}
