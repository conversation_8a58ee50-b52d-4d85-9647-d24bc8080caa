package kd.bos.tcbj.srm.admittance.helper;

import com.alibaba.fastjson.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.utils.HttpUtils;

import java.util.*;

/**
 * 生产商业务类
 * <AUTHOR>
 * @Description:
 * @date 2024-03-15
 */
public class ProducerHelper {

    /**
     * 发送生产商至WMS
     * @return
     * @author: hst
     * @createDate: 2024/03/15
     */
    public ApiResult sendProducerToWMS (Map<String,String> param) {
        ApiResult apiResult = new ApiResult();

        // 获取WMS生产商同步接口地址
        String url = this.getparamConfigureUrl();

        // 同步WMS
        String strResult = HttpUtils.postParams(url, param);

        JSONObject resutl = JSONObject.parseObject(strResult);
        Boolean errorCode = resutl.getBoolean("errorCode");
        if (!errorCode.booleanValue()) {
            String errorMessage = resutl.getString("errorMessage");
            apiResult.setSuccess(false);
            apiResult.setMessage(errorMessage);
        } else {
            apiResult.setSuccess(true);
            apiResult.setMessage("同步成功!");
        }
        return apiResult;
    }

    /**
     * 获取参数配置表中参数值
     * @return
     * @author: hst
     * @createDate: 2024/03/15
     */
    private String getparamConfigureUrl () {
        QFilter qFilter = new QFilter("number",QFilter.equals,"WMS_PRO_SEND");
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
                new QFilter[]{qFilter});
        if (Objects.nonNull(param)) {
            return param.getString("name");
        }
        return "";
    }
}
