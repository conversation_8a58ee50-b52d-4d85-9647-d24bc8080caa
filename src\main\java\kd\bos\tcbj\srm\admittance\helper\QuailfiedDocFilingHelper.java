package kd.bos.tcbj.srm.admittance.helper;

import java.util.HashMap;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.admittance.helper.QuailfiedDocFilingHelper
 * @className QuailfiedDocFilingHelper
 * @author: hst
 * @createDate: 2024/04/24
 * @description: 资质文件归档业务类
 * @version: v1.0
 */
public class QuailfiedDocFilingHelper {

    /** 一级类别 **/
    public final static String SUPERLEVEL_FIELD = "yd_superlevel";
    /** 二级类别 **/
    public final static String NEXTLEVEL_FIELD = "yd_nextlevel";
    /** 类型映射 **/
    public static Map<String,String> mapping = new HashMap<>();
    /** 提示映射 **/
    public static Map<String,String> tipMapping = new HashMap<>();
    static {
        mapping.put("3","A,B,C,D,E,F");
        mapping.put("2","A,B,C,D,E,F");
        mapping.put("1","G,H,I,J,K,L");
        mapping.put("4","M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA,AB,AC,AD,AE");
        tipMapping.put("3","一级类别为国内生产商,二级类别只能选择营业执照、药品级生产许可、食品级生产许可、保健食品的提取物生产许可、食品级、药品级");
        tipMapping.put("2","一级类别为国外生产商,二级类别只能选择营业执照、药品级生产许可、食品级生产许可、保健食品的提取物生产许可、食品级、药品级");
        tipMapping.put("1","一级类别为代理商，二级类别只能选择供应商书面调查表、物料描述表、、3个批次样品 COA、样品我司检测报告、生产工艺流程图、厂家企业标准");
        tipMapping.put("4","一级类别为物料，二级类别只能选择第三方型式检验报告、包装标签模板、非辐照证明、产品稳定性考察报告、转基因原料类、" +
                "菌种/藻类、微生物高风险物料、植物原料、初加工的动物原料品种鉴定证明、兽药残留检验报告、EDQM证书、初加工的动物原料品种鉴定证明、" +
                "检验检疫证明、食品原料、体系认证文件、MSDS、文件清单、设备清单、排污许可证");
    }
}
