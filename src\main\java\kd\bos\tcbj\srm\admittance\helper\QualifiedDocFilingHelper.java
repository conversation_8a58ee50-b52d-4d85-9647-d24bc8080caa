package kd.bos.tcbj.srm.admittance.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.db.DB;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.fileservice.FileItem;
import kd.bos.fileservice.FileService;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.attachment.AttachmentFieldServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachConstant;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachMapConstant;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.admittance.vo.AdminAttachInfoVo;
import kd.bos.tcbj.srm.admittance.vo.AdminAttachSetVo;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.helper.QualifiedDocFilingHelper
 * @className QualifiedDocFilingHelper
 * @author: hst
 * @createDate: 2024/11/23
 * @description: 资质文件归档业务类
 * @version: v1.0
 */
public class QualifiedDocFilingHelper {

    /**
     * 提取分录资质信息
     * @param bill
     * @param billId
     * @param formId
     * @param typeMap
     * @author: hst
     * @createDate: 2024/07/21
     */
    public static Map<String, AdminAttachInfoVo> getAdminAttachInfoVo(DynamicObject bill, String billId, String formId,
                                                                Map<String, AdminAttachSetVo> typeMap) {
        /* 基础资料标识 */
        String baseType = "yd_newmatreqbill".equals(formId)
                ? "yd_newreqbase" : "yd_rawmatsupaccessbill".equals(formId) ? "yd_rawmatsupaccessbase" : "";
        Map<String, AdminAttachInfoVo> entryDetails = new HashMap<>();

        // 先获取所有相关的分录
        Set<String> entryKeys = typeMap.keySet().stream().map(type -> {
            String[] types = type.split("&");
            return types[0] + "&" + types[1] + "&" + types[2] + "&" + types[3] + "&" + types[4];
        }).collect(Collectors.toSet());

        for (String entryKey : entryKeys) {
            String[] keys = entryKey.split("&");
            DynamicObjectCollection details = bill.getDynamicObjectCollection(entryKey.split("&")[2]);
            for (DynamicObject detail : details) {
                String type = detail.getString(keys[3]);

                AdminAttachSetVo setVo = typeMap.get(entryKey + "&" + type);

                AdminAttachInfoVo infoVo = new AdminAttachInfoVo();
                infoVo.setTarEntity(keys[1]);
                infoVo.setMatchField(setVo.getMatchField());
                infoVo.setTarEntry(setVo.getEntryName());
                infoVo.setTarSortField(setVo.getTypeName());
                infoVo.setTarSort(setVo.getType());
                infoVo.setTarAptField(setVo.getAptitudeType());
                infoVo.setTarAptSort(setVo.getAptitudeEnum());
                infoVo.setTarAptNameField(setVo.getAptitudeName());
                infoVo.setTarAptName(setVo.getName());
                infoVo.setTarAttachField(setVo.getAttachName());
                infoVo.setAttachCollection(detail.getDynamicObjectCollection(keys[4]));
                infoVo.setIsSueDateField(setVo.getIsSueName());
                infoVo.setIsSueDate(detail.getDate(setVo.getEffectName()));
                infoVo.setDateToField(setVo.getDateToName());
                infoVo.setDateTo(detail.getDate(setVo.getUnEffectName()));
                infoVo.setReNameField(setVo.getReName());
                infoVo.setPathName(setVo.getPathName());
                infoVo.setParentDataField(setVo.getParentDataField());
                infoVo.setOriEntry(keys[2]);
                infoVo.setIsReNameField(keys[4] + "_re");
                infoVo.setIsAlreadField(keys[4] + "_up");
                infoVo.setOriSeq(detail.getInt("seq"));

                if (0L == detail.getLong("id")) {
                    Long newId = DB.genLongId("");
                    detail.set("id", newId);
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getOriEntryId()))) {
                    infoVo.setEntryId(detail.getString(setVo.getOriEntryId()));
                } else {
                    infoVo.setEntryId(detail.getString("id"));
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getOriBillId()))) {
                    infoVo.setBillId(detail.getString(setVo.getOriBillId()));
                } else {
                    infoVo.setBillId(billId);
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getBillType()))) {
                    infoVo.setBillType(detail.getString(setVo.getBillType()));
                } else {
                    infoVo.setBillType(baseType);
                }

                entryDetails.put(entryKey + "&" + detail.getString("seq"), infoVo);
            }
        }

        return entryDetails;
    }

    /**
     * 获取资质类型配置信息
     * @return
     * @author: hst
     * @createDate: 2024/07/10
     */
    public static Map<String, AdminAttachSetVo> getTypeMap () {
        DataSet aptitudeTypes = AdminAttachHelper.getTypeMapInfo(null);
        if (aptitudeTypes.hasNext()) {
            Map<String, AdminAttachSetVo> typeMap = new HashMap<>();
            for (Row row : aptitudeTypes) {
                // 匹配字段
                String matchName = row.getString(AdminAttachMapConstant.MATCH_FIELD);
                // 附件库元数据标识
                String entityName = row.getString(AdminAttachMapConstant.ENTITYNAME_FIELD);
                // 资料补充函附件分录标识'
                String billEntryName = row.getString(AdminAttachMapConstant.BILLENTRY_FIELD);
                // 资料补充附件类型字段名
                String fieldName = row.getString(AdminAttachMapConstant.FIELDNAME_FIELD);
                // 资料补充函附件字段名
                String attachField = row.getString(AdminAttachMapConstant.ATTACHFIELD_FIELD);
                // 资料补充函附件枚举值
                String typeEnum = row.getString(AdminAttachConstant.TYPEENUM_FIELD);

                String key = matchName + "&" + entityName + "&" + billEntryName + "&" + fieldName + "&" + attachField + "&" + typeEnum;

                if (!typeMap.containsKey(key)) {
                    AdminAttachSetVo adminAttachVo = new AdminAttachSetVo();
                    // 匹配字段
                    adminAttachVo.setMatchField(row.getString(AdminAttachMapConstant.MATCH_FIELD));
                    // 资质类型
                    adminAttachVo.setType(row.getString(AdminAttachMapConstant.TYPE_FIELD));
                    // 资质名称
                    adminAttachVo.setName(row.getString(AdminAttachConstant.NAME_FIELD));
                    // 发证日期字段名
                    adminAttachVo.setEffectName(row.getString(AdminAttachMapConstant.EFFECTNAME_FIELD));
                    // 有效日期字段名
                    adminAttachVo.setUnEffectName(row.getString(AdminAttachMapConstant.UNEFFECTNAME_FIELD));
                    // 附件库附件分录标识
                    adminAttachVo.setEntryName(row.getString(AdminAttachMapConstant.ENTRYNAME_FILED));
                    // 附件库分类字段名
                    adminAttachVo.setTypeName(row.getString(AdminAttachMapConstant.TYPENAME_FIELD));
                    // 附件库资质类型字段名
                    adminAttachVo.setAptitudeType(row.getString(AdminAttachMapConstant.APTITUDETYPE_FIELD));
                    // 附件库资质名称字段名
                    adminAttachVo.setAptitudeName(row.getString(AdminAttachMapConstant.APTITUDENAME_FIELD));
                    // 附件库元数据标识
                    adminAttachVo.setEntityName(row.getString(AdminAttachMapConstant.ENTITYNAME_FIELD));
                    // 附件库资质类型枚举值
                    adminAttachVo.setAptitudeEnum(row.getString(AdminAttachConstant.APTITUDEENUM_FIELD));
                    // 附件库附件字段名
                    adminAttachVo.setAttachName(row.getString(AdminAttachMapConstant.ATTACHNAME_FIELD));
                    // 附件库签发日期字段名
                    adminAttachVo.setIsSueName(row.getString(AdminAttachMapConstant.ISSUENAME_FIELD));
                    // 附件库有效日期至字段名
                    adminAttachVo.setDateToName(row.getString(AdminAttachMapConstant.DATETONAME_FIELD));
                    // 重命名取值基础资料字段
                    adminAttachVo.setReName(row.getString(AdminAttachMapConstant.RENAME_FIELD));
                    // 归属基础资料字段名
                    adminAttachVo.setParentDataField(row.getString(AdminAttachMapConstant.PARENTDATA_FIELD));
                    // 来源单据ID字段名
                    adminAttachVo.setOriBillId(row.getString(AdminAttachMapConstant.ORIBILL_FIELD));
                    // 来源分录ID字段名
                    adminAttachVo.setOriEntryId(row.getString(AdminAttachMapConstant.ORIENTRY_FIELD));
                    // 路径名称
                    adminAttachVo.setPathName(row.getString(AdminAttachMapConstant.PATHNAME_FIELD));
                    // 路径名称
                    adminAttachVo.setPathName(row.getString(AdminAttachMapConstant.PATHNAME_FIELD));
                    // 来源单据类型
                    adminAttachVo.setBillType(row.getString(AdminAttachMapConstant.BILLTYPE_FIELD));

                    typeMap.put(key, adminAttachVo);
                }
            }
            return typeMap;
        } else {
            throw new KDBizException("未查询到资质类型配置信息，请联系管理员");
        }
    }

    /**
     * 附件重命名
     * @param entryDetails
     * @param sortNames
     * @param sortNumbers
     * @author: hst
     * @createDate: 2024/07/22
     */
    public static void reSetAttachmentName (DynamicObject bill, Map<String, AdminAttachInfoVo> entryDetails,
                                            Map<String, String> sortNames, Map<String, String> sortNumbers) {
        // update by hst -2024/11/16 是否删除原附件
        boolean isDelete = false;
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"SRM_IS_DELETE")});
        if (Objects.nonNull(param) && StringUtils.isNotBlank(param.getString("name"))) {
            isDelete = Boolean.valueOf(param.getString("name"));
        }

        Map<String, Integer> attachNums = new HashMap<>();

        for (Map.Entry<String, AdminAttachInfoVo> entryDetail : entryDetails.entrySet()) {
            AdminAttachInfoVo attInfo = entryDetail.getValue();

            DynamicObjectCollection attachments = attInfo.getAttachCollection();
            Collections.sort(attachments, Comparator.comparing(obj -> obj.getDynamicObject("fbasedataid").getDate("createtime")));

            for (DynamicObject attachment : attachments) {
                String baseName = sortNames.get(attInfo.getReNameField());
                String baseNumber = sortNumbers.get(attInfo.getReNameField());
                if (StringUtils.isNotBlank(baseName) && StringUtils.isNotBlank(baseNumber)) {
                    String aptName = attInfo.getTarAptName();
                    String aptNum = attInfo.getTarAptSort();
                    String pathName = attInfo.getPathName();

                    String startDate = DateUtil.date2str(attInfo.getIsSueDate(), "yyyyMMdd");
                    if (StringUtils.isBlank(startDate)) {
                        startDate = DateUtil.date2str(attachment.getDynamicObject("fbasedataid").getDate("createtime"), "yyyyMMdd");
                    }
                    String endDate = DateUtil.date2str(attInfo.getDateTo(), "yyyyMMdd");

                    String fileName = baseName + "-" + aptName + (Objects.nonNull(startDate) ? "-" + startDate : "")
                            + (Objects.nonNull(endDate) ? "-" + endDate : "");

                    // 是否已有重复的命名
                    if (attachNums.containsKey(fileName)) {
                        int index = attachNums.get(fileName);
                        attachNums.put(fileName, index + 1);

                        fileName = fileName + "_" + index;
                    } else {
                        attachNums.put(fileName, 1);
                    }

                    // 格式化附件名称
                    String path = formatAdminAttachName(attachment, fileName, pathName, baseNumber, aptNum, isDelete);

                    if (StringUtils.isBlank(path)) {
                        throw new KDBizException("附件：" + baseName + "-" + aptName + "重命名失败");
                    }
                }
                bill.getDynamicObjectCollection(attInfo.getOriEntry()).get(attInfo.getOriSeq() - 1).set(attInfo.getIsReNameField(), true);
            }
        }
    }

    /**
     * 格式化附件名称
     * @param attachment
     * @param fileName
     * @param sort
     * @param type
     * @author: hst
     * @createDate: 2024/11/23
     */
    private static String formatAdminAttachName (DynamicObject attachment, String fileName, String type, String sort,
                                                 String aptNum, boolean isDelete) {
        FileService fs = FileServiceFactory.getAttachmentFileService();
        String tarPath = "";

        DynamicObject baseData = attachment.getDynamicObject("fbasedataid");
        String baseDataId = baseData.getString("id");

        // 获取文件类型
        String extName = baseData.getString("type");

        // 文件路径
        String url = baseData.getString("url");
        String path = AdminAttachHelper.getPathFromDownloadUrl(url);

        // 校验文件是否存在
        String realPath = fs.getFileServiceExt().getRealPath(path);
        boolean isExist = fs.exists(fs.getFileServiceExt().getRealPath(path));
        if (!isExist) {
            throw new KDBizException("重命名" + fileName + "资质文件失败，文件不存在，请联系管理员");
        }

        if (StringUtils.isNotBlank(realPath) && !realPath.contains("srm")) {
            InputStream inputStream = fs.getInputStream(path);
            // 生成文件路径-上传附件时远程服务器需要存储文件的位置
            String pathParam = String.format("/%s/%s/%s/%s/%s/%s/%s/%s", RequestContext.get().getTenantId(),
                    RequestContext.get().getAccountId(), "srm", AdminAttachHelper.formatPath(type),
                    AdminAttachHelper.formatPath(sort), aptNum, baseDataId, AdminAttachHelper.formatPath(fileName + "." + extName));

            FileItem fi = new FileItem(fileName, pathParam, inputStream);
            fi.setCreateNewFileWhenExists(false);
            tarPath = fs.upload(fi);

            // 校验文件是否存在
            isExist = fs.exists(fs.getFileServiceExt().getRealPath(tarPath));
            if (StringUtils.isNotBlank(tarPath) && isExist) {
                // 修改附件名
                AttachmentFieldServiceHelper.rename(baseData.getString("uid"), fileName + "." + extName);

                // 修改附件路径映射关系
                AdminAttachHelper.reUrl(path, tarPath);

                if (isDelete && !fs.getFileServiceExt().getRealPath(path)
                        .equals(fs.getFileServiceExt().getRealPath(tarPath))) {
                    // 删除附件
                    fs.delete(path);
                }
            } else {
                throw new KDBizException("重命名" + fileName + "资质文件失败，新文件保存失败，请联系管理员");
            }
        } else {
            tarPath = path;
        }

        return tarPath;
    }

    /**
     * 更新附件库中准入附件
     * @param bill
     * @author: hst
     * @createDate: 2024/07/10
     */
    public static void addAdminAttach (DynamicObject bill, String billId, String formId) {
        Map<String, AdminAttachSetVo> typeMap = getTypeMap();
        // 提取分录资质信息
        Map<String, AdminAttachInfoVo> entryDetails = getAdminAttachInfoVo(bill, billId, formId, typeMap);

        // 已反写的记录
        Set<String> records = new HashSet<>();
        // 需要反写的附件库
        Map<String, DynamicObject> baseDatas = new HashMap<>();
        for (Map.Entry<String, AdminAttachInfoVo> infoVo : entryDetails.entrySet()) {
            String[] keys = infoVo.getKey().split("&");
            // 基础资料字段
            String baseField = keys[0];
            // 附件库标识
            String tarEntityField = keys[1];

            DynamicObject baseData = null;
            if (!baseDatas.containsKey(baseField)) {
                DynamicObject object = bill.getDynamicObject(baseField);
                if (Objects.nonNull(object)) {
                    baseData = BusinessDataServiceHelper.loadSingle(object.getString("id"), tarEntityField);
                    if (Objects.nonNull(baseData)) {
                        baseDatas.put(baseField, baseData);
                    } else {
                        throw new KDBizException(baseField + "获取不到对应基础资料信息");
                    }
                }
            } else {
                baseData = baseDatas.get(baseField);
            }

            if (Objects.nonNull(baseData)) {
                DynamicObjectCollection entries = baseData.getDynamicObjectCollection(infoVo.getValue().getTarEntry());
                for (DynamicObject entry : entries) {
                    records.add(entry.getString("yd_billkey") + "&" + entry.getString("yd_billentrykey"));
                }
            }
        }

        // 资质归档，保存到附件库
        qualificationFiling(bill, entryDetails, baseDatas);
    }

    /**
     * 资质归档，保存到附件库
     * @param bill
     * @param entryDetails
     * @param baseDatas
     * @author: hongsitao
     * @createDate: 2024/11/23
     */
    public static void qualificationFiling (DynamicObject bill, Map<String, AdminAttachInfoVo> entryDetails,
                                            Map<String, DynamicObject> baseDatas) {
        /* 按单据ID分组获取分录ID */
        Map<String, List<String>> groups = entryDetails.values().stream().collect(Collectors.groupingBy(AdminAttachInfoVo :: getBillId,
                Collectors.mapping(AdminAttachInfoVo :: getEntryId, Collectors.toList())));
        /* 归档 */
        for (Map.Entry<String, AdminAttachInfoVo> entryDetail : entryDetails.entrySet()) {
            AdminAttachInfoVo infoVo = entryDetail.getValue();

            String billId = infoVo.getBillId();
            String entryId = infoVo.getEntryId();

            /* 基础资料字段 */
            String baseField = entryDetail.getValue().getMatchField();

            DynamicObject baseData = baseDatas.get(baseField);

            /* 如果已归档，则更新，否则新增 */
            if (Objects.nonNull(baseData)) {

                DynamicObjectCollection records = baseData.getDynamicObjectCollection(infoVo.getTarEntry());

                DynamicObject attRecord = null;
                for (DynamicObject record : records) {
                    if (billId.equals(record.getString("yd_billkey"))
                            && entryId.equals(record.getString("yd_billentrykey"))) {
                        attRecord = record;
                    }
                }

                if (Objects.isNull(attRecord)) {
                    attRecord = baseData.getDynamicObjectCollection(infoVo.getTarEntry()).addNew();
                    attRecord.set("seq", baseData.getDynamicObjectCollection(infoVo.getTarEntry()).size());
                }

                /* 附件库分类枚举值 */
                setValue(attRecord, infoVo.getTarSortField(), infoVo.getTarSort());
                /* 附件库资质类型枚举值 */
                setValue(attRecord, infoVo.getTarAptField(), infoVo.getTarAptSort());
                /* 附件库资质名称 */
                setValue(attRecord, infoVo.getTarAptNameField(), infoVo.getTarAptName());
                /* 签发日期 */
                setValue(attRecord, infoVo.getIsSueDateField(), infoVo.getIsSueDate());
                /* 有效日期 */
                setValue(attRecord, infoVo.getDateToField(), infoVo.getDateTo());
                /* 复制附件 */
                copyAttach(infoVo.getAttachCollection(),
                        attRecord.getDynamicObjectCollection(infoVo.getTarAttachField()), false);

                /* 业务单据ID */
                setValue(attRecord, "yd_bill", billId);
                /* 业务单据类型 */
                setValue(attRecord, "yd_billtype", infoVo.getBillType());
                /* 业务单据号 */
                setValue(attRecord, "yd_billkey", billId);
                /* 业务单据分录ID */
                setValue(attRecord, "yd_billentrykey", entryId);

                String oriDataName = infoVo.getParentDataField();
                if (kd.bos.util.StringUtils.isNotEmpty(oriDataName)) {
                    String oriDataId = bill.getString(infoVo.getParentDataField() + ".id");
                    setValue(attRecord, "yd_oridata", oriDataId);
                }
            }
        }

        /* 处理删除的数据 */
        for (DynamicObject baseData : baseDatas.values()) {
            String entryName = "srm_supplier".equals(baseData.getDataEntityType().getName()) ?
                    "entry_aptitude" : "yd_aptitudeentity";

            /* 待删除的数据 */
            List<DynamicObject> deletes = new ArrayList<>();
            DynamicObjectCollection entries = baseData.getDynamicObjectCollection(entryName);
            for (DynamicObject entry : entries) {
                String billId = entry.getString("yd_billkey");
                String entryId = entry.getString("yd_billentrykey");

                if (groups.containsKey(billId) && !groups.get(billId).contains(entryId)) {
                    deletes.add(entry);
                }
            }

            for (DynamicObject delEntry : deletes) {
                entries.remove(delEntry);
            }
        }

        // 保存数据
        for (Map.Entry<String,DynamicObject> baseData : baseDatas.entrySet()) {
            OperationResult result = saveBaseData(baseData.getValue());

            if (!result.isSuccess()) {
                throw new KDBizException("保存" + baseData.getKey() + "类型附件失败，原因：" + result.getMessage());
            }
        }
    }


    /**
     * 字段赋值，判断字段名是否为空
     * @author: hst
     * @createDate: 2024/11/23
     */
    private static void setValue (DynamicObject dynamicObject, String field, Object value) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(field)) {
            dynamicObject.set(field, value);
        }
    }

    /**
     * 复制附件
     * @param oriCollection
     * @param tarCollection
     * @param isCopy
     * @author: hst
     * @createDate: 2024/07/11
     */
    public static void copyAttach (DynamicObjectCollection oriCollection, DynamicObjectCollection tarCollection, boolean isCopy) {
        tarCollection.clear();
        for (DynamicObject oriDynamicObject : oriCollection) {
            DynamicObject temp = tarCollection.addNew();
            if (isCopy) {
                temp.set("fpkid", oriDynamicObject.get("fpkid"));
            }
            temp.set("fbasedataid", oriDynamicObject.get("fbasedataid"));
        }
    }

    /**
     * 保存基础资料
     * @return
     * @author: hst
     * @createDate: 2024/11/23
     */
    private static OperationResult saveBaseData (DynamicObject baseData) {
        OperationResult result = SaveServiceHelper.saveOperate(baseData.getDynamicObjectType().getName(),
                new DynamicObject[]{baseData});
        return result;
    }
}
