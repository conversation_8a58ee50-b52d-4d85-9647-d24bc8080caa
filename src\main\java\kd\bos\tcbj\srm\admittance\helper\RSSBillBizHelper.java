package kd.bos.tcbj.srm.admittance.helper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;

/**
 * @auditor yanzuwei
 * @date 2022年8月4日
 * 
 */
public class RSSBillBizHelper 
{

	
	/**
	 @fun 资料补充行通过物料获取台账供应商信息
	 @desc 从资料补充函根据物料F7获取原辅料台账中的（状态为研发新增评估中、研发合格供应商、新增评估中、合格供应商）的台账记录中的供应商
   **/
	public static List<DynamicObject>  getRSSBillList(Object supInfoBillPk)
	{
		List<DynamicObject> rssBillList=new ArrayList();
		DynamicObject info= BusinessDataServiceHelper.loadSingle(supInfoBillPk,"yd_supplyinformationbill","yd_material,yd_supplier");
		DynamicObject matInfo=info.getDynamicObject("yd_material");
		if(matInfo==null)
		{
			return rssBillList;
		}
		
		DynamicObject oriSupInfo=info.getDynamicObject("yd_supplier");
		Set nodeNameSet=new HashSet();
//		nodeNameSet.addAll(Arrays.asList(new String[]{"研发新增评估中","研发合格供应商","新增评估中","现用供应商"})); 
		nodeNameSet.addAll(Arrays.asList(new String[]{"01","03","04","05"})); 
	    QFilter filter=new QFilter("yd_material.id","=",matInfo.getPkValue());
		filter.and(new QFilter("yd_supplieraccesspot.group.number ","in",nodeNameSet));
	    DynamicObject[] rows=BusinessDataServiceHelper.load("yd_rawsupsumbill", "yd_agent,id", filter.toArray());
		for(DynamicObject row:rows)
		{
			DynamicObject supInfo= row.getDynamicObject("yd_agent");	
			if(supInfo!=null 
				&& oriSupInfo!=null
				&& !supInfo.getPkValue().equals(oriSupInfo.getPkValue()))
			{
				rssBillList.add(BusinessDataServiceHelper.loadSingle(row.getPkValue(),"yd_rawsupsumbill"));
			} 
		}
		return rssBillList; 
	}
	
	/**
	 @fun 资料补充行通过物料获取台账供应商信息
	 @desc 从资料补充函根据物料F7获取原辅料台账中的（状态为研发新增评估中、研发合格供应商、新增评估中、合格供应商）的台账记录中的供应商
  **/
	public static List<DynamicObject>  getRSSBillListByMat(Object matPk)
	{
		List<DynamicObject> rssBillList=new ArrayList();
		
		Set nodeNameSet=new HashSet();
//		nodeNameSet.addAll(Arrays.asList(new String[]{"研发新增评估中","研发合格供应商","新增评估中","现用供应商"})); 
		nodeNameSet.addAll(Arrays.asList(new String[]{"01","03","04","05"})); 
	    QFilter filter=new QFilter("yd_material.id","=",matPk);
		filter.and(new QFilter("yd_supplieraccesspot.group.number ","in",nodeNameSet));
	    DynamicObject[] rows=BusinessDataServiceHelper.load("yd_rawsupsumbill", "yd_agent,id", filter.toArray());
		for(DynamicObject row:rows)
		{
			DynamicObject supInfo= row.getDynamicObject("yd_agent");	
			if(supInfo!=null)
			{
				rssBillList.add(BusinessDataServiceHelper.loadSingle(row.getPkValue(),"yd_rawsupsumbill"));
			} 
		}
		return rssBillList; 
	}

	/**
	 * 获取供应商状态
	 * @param number
	 * @return
	 */
	public static String getState (String number) {
		Map<String, String> map = new HashMap<String, String>() {{
			put("00", "2");
			put("05", "2");
			put("10", "2");
			put("15", "2");
			put("20", "2");
			put("25", "2");
			put("30", "2");
			put("35", "2");
			put("40", "2");
			put("50", "3");
			put("55", "4");
			put("60", "4");
			put("65", "4");
			put("70", "4");
			put("75", "4");
			put("80", "4");
			put("85", "4");
			put("90", "1");
			put("95", "1");
			put("100", "5");
			put("105", "6");
			put("110", "6");
			put("115", "7");
			put("120", "8");
			put("45", "9");
		}};

		if (map.containsKey(number)) {
			return map.get(number);
		} else {
			return "";
		}
	}
}
