package kd.bos.tcbj.srm.admittance.helper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import kd.bos.db.DB;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import org.apache.commons.lang3.StringUtils;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.enums.SupDicEnum;

/**
 * @auditor yanzuwei
 * @date 2022年7月19日
 * @desc 更新原辅料供应商台账
 */
public class RSSBillUpdateHelper 
{
	
	public static void updateBill(DynamicObject info,String opKey)
	{//更新原辅料供应商台账
		
		 String billName=info.getDataEntityType().getName();
		 if("yd_supplyinformationbill".equals(billName))
		 {//资料补充函
	        if("audited_rssbill".equals(opKey))
	        {
	        	update4SupInfoAudited(info);
	        } 
			 
		 }else if("yd_rawmatsupaccessbill".equals(billName))
		 {//原辅料供应商准入单
			    if("audited_rssbill".equals(opKey))
		        {
			    	update4RowmatAudited(info);
		        }else if("submit_rssbill".equals(opKey))
		        {
		        	update4RowmatSubmit(info);
		        }else if("auditing_rssbill".equals(opKey))
		        {
		        	update4RowmatAuditing(info);
		        }else if("updatenewmat".equals(opKey))
		        {//如果有替换物料，需要将原辅料供应商台账的物料进行更新，还要再更新准入单表头的物料,yzw
		        	updateMat4Rowmat(info);
		        } 
		 }else if("yd_newmatreqbill".equals(billName))
		 {//新物料需求及供应商评价单
			    if("audited_rssbill".equals(opKey))
		        {
			    	update4NewmatAudited(info);
		        } else if("auditing_rssbill".equals(opKey))
		        {
		        	update4NewmatAuditing(info);
		        } 
			 
		 }else if("yd_suptrialbizbill".equals(billName))
		 {//供应商试产业务单
			    if("auditing_rssbill".equals(opKey))
		        {
		        	update4SupBizAuditing(info);
		        } 
			 
		 }else if("yd_suptrialjobbill".equals(billName))
		 {//供应商试产作业单
			    if("auditing_rssbill".equals(opKey))
		        {
		        	update4SupJobAuditing(info);
		        } 
			 
		 }
	} 
	
	/**
	 * 获取台账数据
	 * @param supId  供应商ID
	 * @param prodId  生产商ID
	 * @param matName  物料名称
	 * @return
	 */
	public static DynamicObject findRSSBill(Long supId, Long prodId,String matName)
	{
	    QFilter f=QFilter.of("yd_agent=? and yd_producers=? and yd_newmatname=?", new Object[] {supId,prodId,matName}); 
		DynamicObject[]  infos=BusinessDataServiceHelper.load("yd_rawsupsumbill", "id", f.toArray());
		if(infos!=null&&infos.length>0)
		{
			return BusinessDataServiceHelper.loadSingle(infos[0].getPkValue(),"yd_rawsupsumbill");
		}
	    return null;
	}
	
	/**
	 * 获取台账数据
	 * @param supId  供应商ID
	 * @param prodId  生产商ID
	 * @param matId  物料ID
	 * @return
	 */
	  public static DynamicObject findRSSBill(Long supId, Long prodId,Long matId)
		{
		    QFilter f=QFilter.of("yd_agent=? and yd_producers=? and yd_material=?", new Object[] {supId,prodId,matId}); 
			DynamicObject[]  infos=BusinessDataServiceHelper.load("yd_rawsupsumbill", "id", f.toArray());
			if(infos!=null&&infos.length>0)
			{
				return BusinessDataServiceHelper.loadSingle(infos[0].getPkValue(),"yd_rawsupsumbill");
			}
		    return null;
		}
	
	
	//资料补充函-审核后
	static void update4SupInfoAudited(DynamicObject srcInfo)
	{
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_supplyinformationbill");
		DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
		DynamicObject matInfo=(DynamicObject)info.get("yd_material");
		DynamicObject prodInfo=(DynamicObject)info.get("yd_producers");
		String materialLevel = info.getString("yd_materiallevel");
		String matName=info.getString("yd_materialname");
		if(supInfo==null || prodInfo == null)
		{
			return ;
		}
		DynamicObject rssInfo=null;
		if(matInfo!=null)
		{
		   rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id")); 
		}else 
		{
		   rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matName);
		}
		
		if(rssInfo==null)
		{
		    rssInfo = BusinessDataServiceHelper.newDynamicObject("yd_rawsupsumbill");
			rssInfo.set("yd_agent", supInfo);
			rssInfo.set("yd_producers", prodInfo);  // 生产商
			rssInfo.set("yd_material", matInfo); 
			rssInfo.set("yd_newmatname",matName); 
			rssInfo.set("yd_version", 0);
			rssInfo.set("org", info.get("org"));
			rssInfo.set("billstatus","A");
		}
		
		//更新字段，分录字段，分录.附件类型字段，附件类型值，日期字段
		String[][]dateFields=new String[][]
				{
						// update by hst 2024/04/07 附件日期取有效日期
						//营业执照
						{"yd_bizdate", "yd_entrydomestic", "yd_domesattachmenttype", "A", "yd_domesuneffectdate"},
						{"yd_agentdate", "yd_entryagent", "yd_agentattachmenttype", "A", "yd_agentuneffectdate"},
						// update by hst 2024/09/18 经营许可
						{"yd_buslicense", "yd_entryagent", "yd_agentattachmenttype", "B", "yd_agentuneffectdate"},
						// update by hst 2024/09/18 代理证明
						{"yd_proof", "yd_entryagent", "yd_agentattachmenttype", "C", "yd_agentuneffectdate"},
						//生产许可
						{"yd_licensedate", "yd_entrydomestic", "yd_domesattachmenttype", "C", "yd_domesuneffectdate"},
						//书面调查表-国内
						{"yd_wqdate", "yd_entrydomestic", "yd_domesattachmenttype", "E", "yd_domesuneffectdate"},
						//书面调查表-国外
						{"yd_wqdate", "yd_entryforeign", "yd_foreigattachmenttype", "C", "yd_foreiguneffectdate"},
						//第三方报告
						{"yd_tprdate", "yd_entryprocessfitinto", "yd_pfitienattachmenttype", "B", "yd_pfitienuneffectdate"},
						//03物料描述表
						{"yd_mdtdate", "yd_entryprocessstar", "yd_pstarattachmenttype", "B", "yd_pstaruneffectdate"},
						//05非转基因/菌种证明 yd_nngdate
						{"yd_nngdate", "yd_entryprocessfitinto", "yd_pfitienattachmenttype", "D", "yd_pfitienuneffectdate"},
						//06菌种证明
						{"yd_spdate", "yd_entryprocessfitinto", "yd_pfitienattachmenttype", "E", "yd_pfitienuneffectdate"},
						//含过敏原 yd_cadate
//						{"yd_cadate", "yd_entryprocessfitinto", "yd_pfitienattachmenttype", "F", "yd_pfitienuneffectdate"},
						//yd_otherdate 09其他
//						{"yd_otherdate", "yd_entryprocessfitinto", "yd_pfitienattachmenttype", "G", "yd_pfitienuneffectdate"},
				};
		
		Calendar cal = Calendar.getInstance();
		
		for(int i=0;i<dateFields.length;i++)
		{
			String[] keys=dateFields[i];
			String key=keys[0];
			Date attDate=getAttDate(info,keys);
			if(attDate!=null)
			{
				rssInfo.set(key, attDate);
			}
		}
		
		int version=(Integer)rssInfo.get("yd_version");
		setValueNotNull(rssInfo, "yd_matleveltype", materialLevel);
		rssInfo.set("yd_version", version+1);
		rssInfo.set("modifytime", new Date());
		rssInfo.set("modifier", RequestContext.get().getCurrUserId()); 
		SaveServiceHelper.saveOperate("yd_rawsupsumbill",new DynamicObject[] {rssInfo},OperateOption.create());

		/* 校验当前资质日期是否都大于当前日期 */
		boolean isStop = rssInfo.getBoolean("yd_isstop");
		/* 是否恢复资格 */
		boolean isAllow = true;
		if (isStop) {
			for(int i=0;i<dateFields.length;i++)
			{
				String[] keys = dateFields[i];
				String key = keys[0];
				Date attDate = rssInfo.getDate(key);
				if(attDate!=null && attDate.compareTo(new Date()) <= 0) {
					isAllow = false;
				}
			}

			if (isAllow) {
				String oriBillId = rssInfo.getString("yd_stopbillid");
				if (StringUtils.isNotBlank(oriBillId)) {
					DynamicObject changeBill = BusinessDataServiceHelper.loadSingle(oriBillId, "yd_rawsupchgbill");

					if (Objects.nonNull(changeBill)) {
						createRawSupChange(changeBill);
					}
				}
			}
		}

		rssInfo.set("yd_isstop", false);
		rssInfo.set("yd_stopbillid", "");
		SaveServiceHelper.saveOperate("yd_rawsupsumbill",new DynamicObject[] {rssInfo},OperateOption.create());
	}
	
	static Date getAttDate(DynamicObject info,String[] keys)
	{
		String entryKey=keys[1];
		String typeKey=keys[2];
		String typeValue=keys[3]; 
		String dateKey=keys[4]; 
		Set typeSet=new HashSet();
		typeSet.addAll(Arrays.asList(StringUtils.split(typeValue, ","))); 
		DynamicObjectCollection entrys=info.getDynamicObjectCollection(entryKey);
		 if(entrys==null)
		 {
		   return null;
		 }
		 for(int i=0;i<entrys.size();i++)
		 {
			 DynamicObject entry= entrys.get(i);
			 String attType=(String)entry.get(typeKey);
			 if(typeSet.contains(attType))
			 {
				return  (Date)entry.get(dateKey);
			 }
		 } 
		return null;
	}
	
	//原辅料供应商准入单-提交
	static void update4RowmatSubmit(DynamicObject srcInfo)
	{
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_rawmatsupaccessbill");
		DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
		DynamicObject matInfo=(DynamicObject)info.get("yd_material");
		DynamicObject prodInfo=(DynamicObject)info.get("yd_tempproducers");
		if(supInfo==null||matInfo==null||prodInfo==null)
		{
			return ;
		}
		DynamicObject rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
		if(rssInfo==null) {
			rssInfo = BusinessDataServiceHelper.newDynamicObject("yd_rawsupsumbill");
			rssInfo.set("yd_agent", supInfo);
			rssInfo.set("yd_producers", prodInfo);  // 生产商
			rssInfo.set("yd_material", matInfo);
			rssInfo.set("yd_version", 0);
			rssInfo.set("org", info.get("org"));
			rssInfo.set("billstatus", "A");
			rssInfo.set("yd_accbillno", info);

		}

		if (Objects.isNull(rssInfo.get("yd_accbillno"))) {
			rssInfo.set("yd_accbillno", info);
		}

		DynamicObjectCollection rssCol = new DynamicObjectCollection();
		rssCol.add(rssInfo);
		
		// 获取现有物料代码
		DynamicObjectCollection otherMatObjs = info.getDynamicObjectCollection("yd_othermat");
		if (otherMatObjs != null) {
			for (DynamicObject tmpMat : otherMatObjs) {
				DynamicObject tmpRssInfo = findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),tmpMat.getLong("fbasedataid_id"));
				if(tmpRssInfo==null)
				{
					tmpRssInfo = BusinessDataServiceHelper.newDynamicObject("yd_rawsupsumbill");
					tmpRssInfo.set("yd_agent", supInfo);
					tmpRssInfo.set("yd_producers", prodInfo);  // 生产商
					tmpRssInfo.set("yd_material", BusinessDataServiceHelper.loadSingle(tmpMat.getLong("fbasedataid_id"), "bd_material", "id,name,number"));   
					tmpRssInfo.set("yd_version", 0);
					tmpRssInfo.set("org", info.get("org"));
					tmpRssInfo.set("billstatus","A"); 
					tmpRssInfo.set("yd_accbillno",info);  
				}
				rssCol.add(tmpRssInfo);
			}
		}
		
		for (DynamicObject finalRssInfo : rssCol) {
			finalRssInfo.set("yd_matcat", info.get("yd_matcat"));  
			DynamicObjectCollection entrys=info.getDynamicObjectCollection("entryentity");
			if(entrys!=null&&entrys.size()>0)
			{
				DynamicObject entry=entrys.get(0);
				setValueNotNull(finalRssInfo, "yd_origin", entry.get("yd_origin"));
				setValueNotNull(finalRssInfo, "yd_address", entry.get("yd_manufacturerplace"));

				setValueNotNull(finalRssInfo, "yd_manufacturer", entry.get("yd_manufacturer"));
				setValueNotNull(finalRssInfo, "yd_supmatname", entry.get("yd_supmatname"));
				
			}


			setValueNotNull(finalRssInfo, "yd_matleveltype",info.get("yd_matlevtype"));
			setValueNotNull(finalRssInfo, "yd_manmonth",info.get("yd_shelflife"));

			setValueNotNull(finalRssInfo, "yd_matsource",info.getString("yd_matsource"));
			String isHasAllergen = info.getString("yd_hasallergen");
			if (StringUtils.isNotBlank(isHasAllergen)) {
				finalRssInfo.set("yd_hasallergen", "1".equals(isHasAllergen) ? "1" : "0");
			}
			// update by hst 2024/09/18 过敏源携带
			setValueNotNull(finalRssInfo, "yd_allergysource",info.getString("yd_allergysource"));

			// update by hst 2024/04/09 型号
			setValueNotNull(finalRssInfo, "yd_specs",info.get("yd_packmodel"));
			// update by hst 2024/04/09 储存条件
			setValueNotNull(finalRssInfo, "yd_storagecon",info.get("yd_storagecon"));
			// update by hst 2024/08/06 采购净周期
			setValueNotNull(finalRssInfo, "yd_netcycle", info.get("yd_netcycle"));
			// update by hst 2024/08/23 规格
			setValueNotNull(finalRssInfo, "yd_spec",info.get("yd_spec"));
			/* update by hst 2024/11/04 每件重量 */
			setValueNotNull(finalRssInfo, "yd_weight",info.get("yd_weight"));
			/* update by hst 2024/11/04 重量单位 */
			setValueNotNull(finalRssInfo, "yd_unit",info.get("yd_unit"));
			/* update by hst 2024/11/04 最小单元包装形式 */
			setValueNotNull(finalRssInfo, "yd_minunit",info.get("yd_minunit"));
			/* update by hst 2024/11/04 是否涉及易燃易爆 */
//			setValueNotNull(finalRssInfo, "yd_isexplosive",info.get("yd_isexplosive"));
			/* update by hst 2024/11/04 动植物来源说明 */
			setValueNotNull(finalRssInfo, "yd_matsourceexp",info.get("yd_matsourceexp"));

			int version=(Integer)finalRssInfo.get("yd_version");
			finalRssInfo.set("yd_version", version+1);
			finalRssInfo.set("modifytime", new Date());
			finalRssInfo.set("modifier", RequestContext.get().getCurrUserId()); 
			SaveServiceHelper.saveOperate("yd_rawsupsumbill",new DynamicObject[] {finalRssInfo},OperateOption.create());
			
		}
		
		
	}
	//原辅料供应商准入单-审核中
	static void update4RowmatAuditing(DynamicObject srcInfo)
	{
		
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_rawmatsupaccessbill");
		DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
		DynamicObject matInfo=(DynamicObject)info.get("yd_material");
		DynamicObject prodInfo=(DynamicObject)info.get("yd_tempproducers");
		if(supInfo==null||matInfo==null||prodInfo==null)
		{
			return ;
		}
		DynamicObject rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
		if(rssInfo==null) {
			return;
		}
		  
		updateSupDic(info.getString("yd_mataddrtype"), info.getString("yd_supdirectory"),rssInfo); 
		
		SaveServiceHelper.save(new DynamicObject[] {rssInfo}); 
	}
	
	//原辅料供应商准入单-审核中-更新物料节点,yzw
	static void updateMat4Rowmat(DynamicObject srcInfo)
	{
		
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_rawmatsupaccessbill");
		DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
		DynamicObject prodInfo=(DynamicObject)info.get("yd_tempproducers");
		DynamicObject matInfo=(DynamicObject)info.get("yd_material");
		DynamicObject newMatInfo=(DynamicObject)info.get("yd_relmat");
		if(supInfo==null||matInfo==null||newMatInfo == null||prodInfo==null)
		{
			return ;
		}
		DynamicObject rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
		if(rssInfo==null)
		{
          return; 
		}
		
		rssInfo.set("yd_material", newMatInfo);
		SaveServiceHelper.save(new DynamicObject[] {rssInfo}); 
		
		info.set("yd_material", newMatInfo);
		SaveServiceHelper.save(new DynamicObject[] {info}); 
	}
	
	//更新供应商状态，准入节点
	static void updateSupDic(String matAddrType,String dicValue,DynamicObject rssInfo)
	{
		if(StringUtils.isNoneBlank(dicValue) && StringUtils.isNoneBlank(matAddrType))
		{
			// 工厂类型：OEM-1,珠海厂-2,供应商目录：批准使用-1,紧急时使用-2，要启用再审批-3，暂不启用-4
			// 准入节点：现用供应商批准使用90，OEM专用批准使用100，备用供应商130，暂不启用110
			String key = matAddrType+"&"+dicValue;
			String value = "00";
			if ("2&1".equals(key)) {
				value = "90";
			} else if ("1&1".equals(key)) {
				value = "100";
			} else if ("3".equals(dicValue)) {
				value = "130";
			} else if ("4".equals(dicValue)) {
				value = "110";
			}
			QFilter f=QFilter.of("number=?", new Object[] {value}); 
			DynamicObject[] datas=BusinessDataServiceHelper.load("yd_supplieraccesspot", "id,group",f.toArray());
			if(datas!=null&&datas.length>0)
			{
				DynamicObject info=datas[0];
				rssInfo.set("yd_supplieraccesspot", info);
				//rssInfo.set("yd_supstatus", info.get("group"));
			}
			
		}  
	}
	
	//更新供应商状态，准入节点
	public  static void updateSupDicByNodeName(String nodeName,DynamicObject rssInfo)
		{
		 
			if(StringUtils.isNoneBlank(nodeName))
			{
				   QFilter f=QFilter.of("name=?", new Object[] {nodeName}); 
					DynamicObject[] datas=BusinessDataServiceHelper.load("yd_supplieraccesspot", "id,group",f.toArray());
					if(datas!=null&&datas.length>0)
					{
						DynamicObject info=datas[0];
						rssInfo.set("yd_supplieraccesspot", info);
					}
				 
			}  
		}
	
	//更新供应商状态，准入节点
		public  static void updateSupDicByNodeNumber(String nodeNum,DynamicObject rssInfo)
		throws KDBizException
			{
			
			  DynamicObject nodeInfo= rssInfo.getDynamicObject("yd_supplieraccesspot");
			    if(nodeInfo!=null)
			    {
			    	String oldNodeNum=nodeInfo.getString("number");
			    	if(Integer.parseInt(nodeNum)<Integer.parseInt(oldNodeNum))
			    	{
//			    		throw new KDBizException("设置的准入节点小于当前节点编码");
			    		return ;
			    	} 
			    }
			    
				if(StringUtils.isNoneBlank(nodeNum))
				{
					   QFilter f=QFilter.of("number=?", new Object[] {nodeNum}); 
						DynamicObject[] datas=BusinessDataServiceHelper.load("yd_supplieraccesspot", "id,group",f.toArray());
						if(datas!=null&&datas.length>0)
						{
							DynamicObject info=datas[0];
							rssInfo.set("yd_supplieraccesspot", info);
						}
					 
				}  
			}
	//原辅料供应商准入单-审核后
	static void update4RowmatAudited(DynamicObject srcInfo)
	{
		
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_rawmatsupaccessbill");
		DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
		DynamicObject matInfo=(DynamicObject)info.get("yd_material");
		DynamicObject prodInfo=(DynamicObject)info.get("yd_tempproducers");
		if(supInfo==null||matInfo==null||prodInfo==null)
		{
			return ;
		}
		DynamicObject rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
		if(rssInfo==null)
		{
			return;
		}
		
		// 需要对现有物料代码对应的台帐进行更新
		DynamicObjectCollection rssCol = new DynamicObjectCollection();
		// 新增日期取当前日期,yzl,20231007
		rssInfo.set("yd_newdate", new Date());
		rssCol.add(rssInfo);
		
		// 获取现有物料代码
		DynamicObjectCollection otherMatObjs = info.getDynamicObjectCollection("yd_othermat");
		if (otherMatObjs != null) {
			for (DynamicObject tmpMat : otherMatObjs) {
				DynamicObject tmpRssInfo = findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),tmpMat.getLong("fbasedataid_id"));
				if(tmpRssInfo!=null)
				{
					rssCol.add(tmpRssInfo);
				}
			}
		}
		
		for (DynamicObject finalRssInfo : rssCol) {
			/* 本次准入是否现场审核 */
			String isAudit = info.getString("yd_hasspotaccess");
			if (StringUtils.isNotBlank(isAudit)) {
				finalRssInfo.set("yd_osaudit", "1".equals(info.getString("yd_hasspotaccess")) ? "1" : "2");
			}

			/* 现场审核日期 */
			setValueNotNull(finalRssInfo, "yd_osauditbegindate", info.get("yd_spotaccessdate"));
			setValueNotNull(finalRssInfo, "yd_osauditdate", info.get("yd_spotenddate"));

			/* 如果现场审核等级不为空时用来更新台账，其他的用书面审核等级来更新台账 */
			if (StringUtils.isNotEmpty(info.getString("yd_evalevel"))) {
				setValueNotNull(finalRssInfo, "yd_evalevel", info.get("yd_evalevel"));
			} else if (StringUtils.isNotEmpty(info.getString("yd_supevtype"))) {
				setValueNotNull(finalRssInfo, "yd_evalevel", info.get("yd_supevtype"));
			}

			/* 内部有效期 */
			setValueNotNull(finalRssInfo, "yd_internalmonth", info.get("yd_interperiod"));

			/* update by hst 2024/12/28 是否涉及易燃易爆 */
			setValueNotNull(finalRssInfo, "yd_isexplosive",info.get("yd_isexplosive"));

			/* update by hst 2024/08/23 是否加严 */
			String tighten = info.getString("yd_tighten");
			if (StringUtils.isNotBlank(tighten)) {
				finalRssInfo.set("yd_isstricter", "1".equals(tighten) ? "1" : "2");
			}

			/* update by hst 2024/08/23 加严原因 */
			setValueNotNull(finalRssInfo, "yd_strireason", info.getString("yd_tightenreason"));

			/* update by hst 2024/08/23 加严起始时间 */
			Date striDate = info.getDate("yd_stridate");
			Date oldDate = finalRssInfo.getDate("yd_stridate");
			if (Objects.isNull(oldDate)) {
				finalRssInfo.set("yd_stridate", striDate);
			}

			/* update by hst 2024/08/23 是否总混 */
			String isMix = info.getString("yd_ismix");
			if (StringUtils.isNotBlank(isMix)) {
				finalRssInfo.set("yd_ismix", "1".equals(isMix) ? "1" : "0");
			}

			/* update by hst 2024/08/23 总混原因 */
			setValueNotNull(finalRssInfo, "yd_mixreason", info.getString("yd_mixreason"));

			/* update by hst 2024/08/23 是否灭菌 */
			String isSterilizate = info.getString("yd_issterilizate");
			if (StringUtils.isNotBlank(isSterilizate)) {
				finalRssInfo.set("yd_issterilize", "1".equals(isSterilizate) ? "1" : "0");
			}

			/* update by hst 2024/08/23 灭菌原因 */
			setValueNotNull(finalRssInfo, "yd_sterreason", info.getString("yd_sterreason"));

			/* update by hst 2024/11/04 准入节点 */
			setValueNotNull(finalRssInfo, "yd_supplieraccesspot", info.get("yd_supplieraccesspot"));

			/* update by hst 2024/11/04 物料类别 */
			setValueNotNull(finalRssInfo, "yd_matcat", info.get("yd_matcat"));

			/* update by hst 2024/11/04 是否为S级物料 */
			setValueNotNull(finalRssInfo, "yd_isslevel", info.get("yd_isslevel"));

			/* update by hst 2024/11/04 是否涉及鱼油 */
			setValueNotNull(finalRssInfo, "yd_beinvolvekeymat", info.get("yd_beinvolvekeymat"));

			/* update by hst 2024/12/11 存储库位 */
			setValueNotNull(finalRssInfo, "yd_location", info.get("yd_location"));

//			updateSupDic(info.getString("yd_mataddrtype"), info.getString("yd_supdirectory"), finalRssInfo);
			SaveServiceHelper.save(new DynamicObject[]{finalRssInfo});
		}
	}
	
	//新物料需求及供应商评价单-审批中
	static void update4NewmatAuditing(DynamicObject srcInfo)
	{
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_newmatreqbill"); 
		String matName=info.getString("yd_materialname");
		if(StringUtils.isEmpty(matName))
		{
			return ;
		}
		DynamicObjectCollection entrys=info.getDynamicObjectCollection("yd_file1entry");
		if(entrys!=null&&entrys.size()>0)
		{
			
			for(int i=0;i<entrys.size();i++)
			{
				DynamicObject entry = entrys.get(i);
				DynamicObject supInfo = entry.getDynamicObject("yd_csup");
				DynamicObject prodInfo = entry.getDynamicObject("yd_cprod");
				String proName = entry.getString("yd_cnewsupname");
				if(supInfo!=null&&prodInfo!=null)
				{
					DynamicObject rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matName);
					if(rssInfo==null)
					{
						// update by hst 2024/04/29 通过物料名称，再次查找对应的物料基础资料
						rssInfo = findRSSBillByName(supInfo.getLong("id"),prodInfo.getLong("id"),matName);
						if (rssInfo==null) {
							rssInfo = BusinessDataServiceHelper.newDynamicObject("yd_rawsupsumbill");
							rssInfo.set("yd_agent", supInfo);
							rssInfo.set("yd_producers", prodInfo);  // 生产商
							rssInfo.set("yd_newmatname", matName);
							rssInfo.set("yd_version", 0);
							rssInfo.set("org", info.get("org"));
							rssInfo.set("billstatus", "A");
							rssInfo.set("yd_newreqbillno", info);
							// Update by hst 2024/08/23 新增供应商实物名称
							rssInfo.set("yd_supmatname", entry.getString("yd_supmatname"));
						}
					}


					//本次准入是否现场审核
					String isAccess = (String) getEntryValue(info,"yd_auditentry","yd_fsup",supInfo.getPkValue(),"yd_hasspotaccess");
					if (StringUtils.isNotBlank(isAccess)) {
						rssInfo.set("yd_osaudit", "1".equals(isAccess) ? "1" : "0");
					}
					/* 供应商质量等级 */
					if ("1".equals(isAccess)) {
						setValueNotNull(rssInfo, "yd_evalevel", getEntryValue(info,"yd_hsupentry","yd_hsup",supInfo.getPkValue(),"yd_supevalevel"));
					} else {
						setValueNotNull(rssInfo, "yd_evalevel", getEntryValue(info,"yd_auditentry","yd_fsup",supInfo.getPkValue(),"yd_evalevel"));
					}

					// update by hst 2024/08/23 是否总混
					String isMix = (String)getEntryValue(info,"yd_scoreentry",
							"yd_esup",supInfo.getPkValue(),"yd_ismix");
					if (StringUtils.isNotBlank(isMix)) {
						rssInfo.set("yd_ismix", "1".equals(isMix) ? "1" : "0");
					}

					// update by hst 2024/08/23 总混原因
					setValueNotNull(rssInfo, "yd_mixreason", getEntryValue(info,"yd_scoreentry",
							"yd_esup",supInfo.getPkValue(),"yd_mixreason"));

					// update by hst 2024/08/23 调整是否灭菌来源于书面审核节点
					String isSterilizate = (String)getEntryValue(info,"yd_scoreentry",
							"yd_esup",supInfo.getPkValue(),"yd_issterilizate");
					if (StringUtils.isNotBlank(isSterilizate)) {
						rssInfo.set("yd_issterilize", "1".equals(isSterilizate) ? "1" : "0");
					}

					// update by hst 2024/08/23 灭菌原因
					setValueNotNull(rssInfo, "yd_sterreason", getEntryValue(info,"yd_scoreentry",
							"yd_esup",supInfo.getPkValue(),"yd_sterreason"));

					/* 产地 */
					setValueNotNull(rssInfo, "yd_origin",getMaterialInfo(info,
							proName,"yd_origin"));

					/* 生产商地址 */
					setValueNotNull(rssInfo, "yd_address",getMaterialInfo(info,
							proName,"yd_proaddress"));

					/* 每件重量 */
					setValueNotNull(rssInfo, "yd_weight",getMaterialInfo(info,
							proName,"yd_weight"));

					/* 重量单位 */
					setValueNotNull(rssInfo, "yd_unit",getMaterialInfo(info,
							proName,"yd_unit"));

					/* 最小单元包装形式 */
					setValueNotNull(rssInfo, "yd_minunit",getMaterialInfo(info,
							proName,"yd_minunit"));

					/* 是否含过敏源 */
					setValueNotNull(rssInfo, "yd_hasallergen",getMaterialInfo(info,
							proName,"yd_hasallergen"));

					/* 过敏类型 */
					setValueNotNull(rssInfo, "yd_allergysource",getMaterialInfo(info,
							proName,"yd_allergysource"));

					/* 动植物来源 */
					setValueNotNull(rssInfo, "yd_matsource",getMaterialInfo(info,
							proName,"yd_matsource"));

					/* 动植物来源说明 */
					setValueNotNull(rssInfo, "yd_matsourceexp",getMaterialInfo(info,
							proName,"yd_matsourceexp"));

					/* 保质期 */
					setValueNotNull(rssInfo, "yd_manmonth",getMaterialInfo(info,proName,"yd_shelflife"));
					/* 内部有效期 */
//					rssInfo.set("yd_internalmonth",shelflife);

					String version = info.getString("yd_version");
					if ("1".equals(version)) {
						/* 物料级别 */
						setValueNotNull(rssInfo, "yd_matleveltype", getEntryValue(info,"yd_admitentity","yd_admitsup",
								supInfo.getPkValue(),"yd_enteradmitlevel"));
						/* 物料类别 */
						setValueNotNull(rssInfo, "yd_matcat", getEntryValue(info,"yd_admitentity","yd_admitsup",
								supInfo.getPkValue(),"yd_admitmatcat"));
					} else {
						/* 物料级别 */
						setValueNotNull(rssInfo, "yd_matleveltype", info.getString("yd_enterlevel"));
						/* 物料类别 */
						setValueNotNull(rssInfo, "yd_matcat", info.get("yd_matcat"));
					}

					// update by hst 2024/04/09 规格型号
					setValueNotNull(rssInfo, "yd_specs", getMaterialInfo(info,entry.getString("yd_cnewsupname"),"yd_model"));

					// update by hst 2024/04/09 储存条件
					setValueNotNull(rssInfo, "yd_storagecon",getMaterialInfo(info,entry.getString("yd_cnewsupname"),"yd_storagecon"));

					// update by hst 2024/08/23 规格
					setValueNotNull(rssInfo, "yd_spec",getMaterialInfo(info,entry.getString("yd_cnewsupname"),"yd_spec"));

					// update by hst 2024/08/23 是否涉及易燃易爆
//					setValueNotNull(rssInfo, "yd_isexplosive",getMaterialInfo(info,entry.getString("yd_cnewsupname"),"yd_isexplosive"));

					// Update by hst 2024/08/23 新增供应商实物名称
					setValueNotNull(rssInfo, "yd_supmatname", (String)getEntryValue(info,"yd_file1entry",
							"yd_csup",supInfo.getPkValue(),"yd_supmatname"));

					/* 新物料研发准入 */
					setValueNotNull(rssInfo, "yd_newreqbillno",info.getLong("id"));

					// update by hst 2024/08/23 调整是否加严来源于书面审核节点
					String tighten = (String)getEntryValue(info,"yd_hsupentry",
							"yd_hsup",supInfo.getPkValue(),"yd_tigsampletype");
					if (StringUtils.isNotBlank(tighten)) {
						rssInfo.set("yd_isstricter", "1".equals(tighten) ? "1" : "2");
					}

					// update by hst 2024/08/23 加严原因
					String tigReason = (String)getEntryValue(info,"yd_hsupentry",
							"yd_hsup",supInfo.getPkValue(),"yd_tightenreason");
					if (StringUtils.isNotBlank(tigReason)) {
						rssInfo.set("yd_strireason", tigReason);
					}

					// update by hst 2024/08/23 加严起始时间
					Date striDate = (Date)getEntryValue(info,"yd_hsupentry",
							"yd_hsup",supInfo.getPkValue(),"yd_stridate");
					Date oldDate = rssInfo.getDate("yd_stridate");
					if (Objects.isNull(oldDate)) {
						rssInfo.set("yd_stridate", striDate);
					}

					// update by hst 2024/12/11 存储库位
					String location = (String)getEntryValue(info,"yd_auditentry",
							"yd_fsup",supInfo.getPkValue(),"yd_location");
					if (StringUtils.isNotBlank(location)) {
						rssInfo.set("yd_location", location);
					}

					// update by hst 2024/12/28 是否涉及易燃易爆
					String isBurn = (String)getEntryValue(info,"yd_auditentry",
							"yd_fsup",supInfo.getPkValue(),"yd_isburn");
					if (StringUtils.isNotBlank(isBurn)) {
						rssInfo.set("yd_isexplosive", isBurn);
					}

					int oldVersion=(Integer)rssInfo.get("yd_version");
					rssInfo.set("yd_version", oldVersion+1);
					rssInfo.set("modifytime", new Date());
					rssInfo.set("modifier", RequestContext.get().getCurrUserId()); 
					SaveServiceHelper.saveOperate("yd_rawsupsumbill",new DynamicObject[] {rssInfo},OperateOption.create());
					
				} 
			}  
		}


	}
 
	//获取分录供应商对应的列数据
	static Object getEntryValue(DynamicObject info,String entryName,String supField,Object supId,String targetField)
	{
		DynamicObjectCollection entrys=info.getDynamicObjectCollection(entryName);
		for(int i=0;i<entrys.size();i++)
		{
			DynamicObject entry=entrys.get(i);
			DynamicObject supInfo=entry.getDynamicObject(supField);
			if(supInfo!=null&&supInfo.getPkValue().equals(supId))
			{
				return entry.get(targetField);
			} 
		} 
		return null; 
	}
	
	
	//新物料需求及供应商评价单-审批后
	static void update4NewmatAudited(DynamicObject srcInfo)
	{
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_newmatreqbill"); 
		String matName=info.getString("yd_materialname");
		if(StringUtils.isEmpty(matName))
		{
			return ;
		}
		DynamicObjectCollection entrys=info.getDynamicObjectCollection("yd_file1entry");
		if(entrys!=null&&entrys.size()>0)
		{
			
			for(int i=0;i<entrys.size();i++)
			{
				DynamicObject entry = entrys.get(i);
				DynamicObject supInfo = entry.getDynamicObject("yd_csup");
				DynamicObject prodInfo = entry.getDynamicObject("yd_cprod");
				String proName = entry.getString("yd_cnewsupname");
				if(supInfo!=null&&prodInfo!=null)
				{
					DynamicObject rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matName);
					if(rssInfo==null) {
						continue;
					}
                  
					//本次准入是否现场审核
					String isAccess = (String) getEntryValue(info,"yd_auditentry","yd_fsup",supInfo.getPkValue(),"yd_hasspotaccess");
					if (StringUtils.isNotBlank(isAccess)) {
						rssInfo.set("yd_osaudit", "1".equals(isAccess) ? "1" : "0");
					}

					/* 现场审核日期 */
					setValueNotNull(rssInfo, "yd_osauditbegindate",
							getEntryValue(info,"yd_hsupentry","yd_hsup",supInfo.getPkValue(),"yd_osauditbegindate"));
					setValueNotNull(rssInfo, "yd_osauditdate",
							getEntryValue(info,"yd_hsupentry","yd_hsup",supInfo.getPkValue(),"yd_osauditdate"));

					/* 供应商质量等级 */
					if ("1".equals(isAccess)) {
						setValueNotNull(rssInfo, "yd_evalevel", getEntryValue(info,"yd_hsupentry","yd_hsup",supInfo.getPkValue(),"yd_supevalevel"));
					} else {
						setValueNotNull(rssInfo, "yd_evalevel", getEntryValue(info,"yd_auditentry","yd_fsup",supInfo.getPkValue(),"yd_evalevel"));
					}

					// update by hst 2024/08/23 调整是否加严来源于书面审核节点
					String tighten = (String)getEntryValue(info,"yd_hsupentry",
							"yd_hsup",supInfo.getPkValue(),"yd_tigsampletype");
					if (StringUtils.isNotBlank(tighten)) {
						rssInfo.set("yd_isstricter", "1".equals(tighten) ? "1" : "2");
					}

					// update by hst 2024/08/23 加严原因
					String tigReason = (String)getEntryValue(info,"yd_hsupentry",
							"yd_hsup",supInfo.getPkValue(),"yd_tightenreason");
					setValueNotNull(rssInfo, "yd_strireason", tigReason);

					// update by hst 2024/08/23 加严起始时间
					Date striDate = (Date)getEntryValue(info,"yd_hsupentry",
							"yd_hsup",supInfo.getPkValue(),"yd_stridate");
					Date oldDate = rssInfo.getDate("yd_stridate");
					if (Objects.isNull(oldDate)) {
						rssInfo.set("yd_stridate", striDate);
					}

					// update by hst 2024/08/23 调整是否总混来源于书面审核节点
					String isMix = (String)getEntryValue(info,"yd_scoreentry",
							"yd_esup",supInfo.getPkValue(),"yd_ismix");
					if (StringUtils.isNotBlank(isMix)) {
						rssInfo.set("yd_ismix", "1".equals(isMix) ? "1" : "0");
					}

					// update by hst 2024/08/23 总混原因
					String mixReason = (String)getEntryValue(info,"yd_scoreentry",
							"yd_esup",supInfo.getPkValue(),"yd_mixreason");
					setValueNotNull(rssInfo, "yd_mixreason", mixReason);

					// update by hst 2024/08/23 调整是否灭菌来源于书面审核节点
					String isSterilizate = (String)getEntryValue(info,"yd_scoreentry",
							"yd_esup",supInfo.getPkValue(),"yd_issterilizate");
					if (StringUtils.isNotBlank(isSterilizate)) {
						rssInfo.set("yd_issterilize", "1".equals(isSterilizate) ? "1" : "0");
					}

					// update by hst 2024/08/23 灭菌原因
					String sterReason = (String)getEntryValue(info,"yd_scoreentry",
							"yd_esup",supInfo.getPkValue(),"yd_sterreason");
					setValueNotNull(rssInfo, "yd_sterreason", sterReason);
					
					//产地
					setValueNotNull(rssInfo, "yd_origin",getMaterialInfo(info,proName,"yd_origin"));

					/* 生产商地址 */
					setValueNotNull(rssInfo, "yd_address",getMaterialInfo(info,
							proName,"yd_proaddress"));

					/* 每件重量 */
					setValueNotNull(rssInfo, "yd_weight",getMaterialInfo(info,
							proName,"yd_weight"));

					/* 重量单位 */
					setValueNotNull(rssInfo, "yd_unit",getMaterialInfo(info,
							proName,"yd_unit"));

					/* 最小单元包装形式 */
					setValueNotNull(rssInfo, "yd_minunit",getMaterialInfo(info,
							proName,"yd_minunit"));

					/* 是否含过敏源 */
					setValueNotNull(rssInfo, "yd_hasallergen",getMaterialInfo(info,
							proName,"yd_hasallergen"));

					/* 过敏类型 */
					setValueNotNull(rssInfo, "yd_allergysource",getMaterialInfo(info,
							proName,"yd_allergysource"));

					/* 动植物来源 */
					setValueNotNull(rssInfo, "yd_matsource",getMaterialInfo(info,
							proName,"yd_matsource"));

					/* 动植物来源说明 */
					setValueNotNull(rssInfo, "yd_matsourceexp",getMaterialInfo(info,
							proName,"yd_matsourceexp"));

					//保质期
					setValueNotNull(rssInfo, "yd_manmonth",getMaterialInfo(info,
							proName,"yd_shelflife"));
//					rssInfo.set("yd_internalmonth",shelflife);

					// update by hst 2024/08/23 是否涉及易燃易爆
					setValueNotNull(rssInfo, "yd_isexplosive",getMaterialInfo(info,
							proName,"yd_isexplosive"));

					// update by hst 2024/12/11 存储库位
					setValueNotNull(rssInfo, "yd_location",
							getEntryValue(info,"yd_auditentry","yd_fsup",supInfo.getPkValue(),"yd_location"));


					/* 新物料研发准入 */
					setValueNotNull(rssInfo, "yd_newreqbillno",info.getLong("id"));

					String version = info.getString("yd_version");
					if ("1".equals(version)) {
						/* 物料级别 */
						setValueNotNull(rssInfo, "yd_matleveltype", getEntryValue(info,"yd_admitentity","yd_admitsup",
								supInfo.getPkValue(),"yd_enteradmitlevel"));
						/* 物料类别 */
						setValueNotNull(rssInfo, "yd_matcat", getEntryValue(info,"yd_admitentity","yd_admitsup",
								supInfo.getPkValue(),"yd_admitmatcat"));
						/* 物料F7 */
						setValueNotNull(rssInfo, "yd_material", getEntryValue(info,"yd_admitentity","yd_admitsup",
								supInfo.getPkValue(),"yd_admitmaterial"));

						/* 是否重点物料 */
						setValueNotNull(rssInfo, "yd_beinvolvekeymat", getEntryValue(info,"yd_admitentity","yd_admitsup",
								supInfo.getPkValue(),"yd_beinvolvekeymat"));

						/* 是否S级 */
						setValueNotNull(rssInfo, "yd_isslevel", getEntryValue(info,"yd_admitentity","yd_admitsup",
								supInfo.getPkValue(),"yd_isadmitslevel"));
					} else {
						/* 物料级别 */
						setValueNotNull(rssInfo, "yd_matleveltype", info.getString("yd_enterlevel"));
						/* 物料类别 */
						setValueNotNull(rssInfo, "yd_matcat", info.get("yd_matcat"));
						/* 物料F7 */
						setValueNotNull(rssInfo, "yd_material", info.get("yd_material"));
						/* 是否重点物料 */
						setValueNotNull(rssInfo, "yd_beinvolvekeymat", info.get("yd_isinvolve"));
						/* 是否S级 */
						setValueNotNull(rssInfo, "yd_isslevel", info.get("yd_isslevel"));
					}

					// update by hst 2024/04/09 规格型号
					setValueNotNull(rssInfo, "yd_specs",getMaterialInfo(info,entry.getString("yd_cnewsupname"),"yd_model"));
					// update by hst 2024/04/09 储存条件
					setValueNotNull(rssInfo, "yd_storagecon",getMaterialInfo(info,entry.getString("yd_cnewsupname"),"yd_storagecon"));
					// update by hst 2024/04/09 新增日期
					rssInfo.set("yd_newdate",new Date());
					// update by hst 2024/08/23 规格
					setValueNotNull(rssInfo, "yd_spec",getMaterialInfo(info,entry.getString("yd_cnewsupname"),"yd_spec"));

					SaveServiceHelper.save(new DynamicObject[] {rssInfo});
					
				} 
			}  
		}
	}
	
	static void update4SupBizAuditing(DynamicObject srcInfo)
	{//供应商试产业务单-审批中
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_suptrialbizbill");
		DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
		DynamicObject matInfo=(DynamicObject)info.get("yd_materiel");
		DynamicObject prodInfo=(DynamicObject)info.get("yd_producers");
		if(supInfo==null||matInfo==null||prodInfo==null)
		{
			return ;
		}
		DynamicObject rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
		if(rssInfo==null)
		{
			return;
		}
		
		updateSupDicByNodeName("紧急时使用",rssInfo);
	    SaveServiceHelper.save(new DynamicObject[] {rssInfo});
	}
	
	static void update4SupJobAuditing(DynamicObject srcInfo)
	{//供应商试产作业单-审批中
		DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"yd_suptrialjobbill");
		DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
		DynamicObject matInfo=(DynamicObject)info.get("yd_materiel");
		DynamicObject prodInfo=(DynamicObject)info.get("yd_producers");
		if(supInfo==null||matInfo==null||prodInfo==null)
		{
			return ;
		}
		DynamicObject rssInfo=findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
		if(rssInfo==null)
		{
			return;
		}
		updateSupDicByNodeName("批准使用",rssInfo);
		SaveServiceHelper.save(new DynamicObject[] {rssInfo});
	}

	/**
	 * 获取物料详细信息
	 * @param info
	 * @param proName
	 * @param field
	 * @return
	 */
	private static Object getMaterialInfo (DynamicObject info, String proName, String field) {
		DynamicObjectCollection entries = info.getDynamicObjectCollection("yd_materialentry");
		for (DynamicObject entry : entries) {
			String producer = entry.getString("yd_anewsupname");

			if (StringUtils.isNotBlank(producer) && StringUtils.isNotBlank(proName)
					&& proName.equals(producer)) {
				return entry.get(field);
			}
		}
		return null;
	}

	/**
	 * 获取台账数据（通过物料名称查找物料基础资料）
	 * @param supId  供应商ID
	 * @param prodId  生产商ID
	 * @param matName  物料名称
	 * @return
	 */
	public static DynamicObject findRSSBillByName(Long supId, Long prodId,String matName)
	{
		QFilter f=QFilter.of("yd_agent=? and yd_producers=? and yd_material.name =?", new Object[] {supId,prodId,matName});
		DynamicObject[]  infos=BusinessDataServiceHelper.load("yd_rawsupsumbill", "id", f.toArray());
		if(infos!=null&&infos.length>0)
		{
			return BusinessDataServiceHelper.loadSingle(infos[0].getPkValue(),"yd_rawsupsumbill");
		}
		return null;
	}

	/**
	 * 设置字段值
	 * @param tarBill
	 * @param field
	 * @param value
	 * @author: hst
	 * @createDate: 2024/11/04
	 */
	private static void setValueNotNull (DynamicObject tarBill, String field, Object value) {
		if (value instanceof DynamicObject) {
			if (Objects.nonNull(value)) {
				tarBill.set(field, value);
			}
		} else if (value instanceof Date) {
			if (Objects.nonNull(value)) {
				tarBill.set(field, value);
			}
		} else if (value instanceof String) {
			if (StringUtils.isNotBlank(value.toString())) {
				tarBill.set(field, value);
			}
		} else if (value instanceof BigDecimal) {
			if (((BigDecimal) value).compareTo(BigDecimal.ZERO) != 0) {
				tarBill.set(field, value);
			}
		} else if (value instanceof Integer) {
			if (((Integer) value) != 0) {
				tarBill.set(field, value);
			}
		} else if (value instanceof Long) {
			if (!Long.valueOf("0").equals((Long) value)) {
				tarBill.set(field, value);
			}
		}
	}

	/**
	 * 创建准入节点变更为原状态的合格目录变更单
	 * @author: hongsitao
	 * @createDate: 2024/12/16
	 */
	private static void createRawSupChange (DynamicObject oriBill) {
		DynamicObject changeBill = BusinessDataServiceHelper.newDynamicObject("yd_rawsupchgbill");

		changeBill.set("id", DB.genLongId("yd_rawsupchgbill"));
		changeBill.set("org", RequestContext.get().getOrgId());
		changeBill.set("yd_srcbillno", oriBill.get("yd_srcbillno"));
		changeBill.set("yd_srcbillid", oriBill.get("yd_srcbillid"));
		changeBill.set("yd_supplier", oriBill.get("yd_supplier"));
		changeBill.set("yd_materiel", oriBill.get("yd_materiel"));
		changeBill.set("yd_datastatus", "A");
		changeBill.set("billstatus", "A");
		changeBill.set("creator", UserServiceHelper.getCurrentUserId());

		DynamicObject entry = changeBill.getDynamicObjectCollection("entryentity").addNew();
		DynamicObject oriEntry = oriBill.getDynamicObjectCollection("entryentity").stream()
				.filter(o -> "准入节点".equals(o.getString("yd_chgfield"))).findFirst().orElse(null);
		if (Objects.nonNull(oriEntry)) {
			entry.set("seq", 1);
			entry.set("yd_infotype", oriEntry.get("yd_infotype"));
			entry.set("yd_chgfield", oriEntry.get("yd_chgfield"));
			entry.set("yd_oldvalue", oriEntry.get("yd_newvalue"));
			entry.set("yd_newvalue", oriEntry.get("yd_oldvalue"));
			entry.set("yd_fieldname", "yd_supplieraccesspot");
			entry.set("yd_oldvalueex", oriEntry.get("yd_newvalueex"));
			entry.set("yd_newvalueex", oriEntry.get("yd_oldvalueex"));

			String note = oriEntry.getString("yd_note");
			entry.set("yd_note", note.contains("】") ? note.substring(0, note.indexOf("】") + 1) + "资质更新，恢复供应。" : "");
		}

		OperationResult result = OperationServiceHelper.executeOperate("save", "yd_rawsupchgbill",
				new DynamicObject[]{changeBill});
		if (result.isSuccess()) {
			OperationServiceHelper.executeOperate("submit", "yd_rawsupchgbill",
					result.getSuccessPkIds().toArray(new Object[result.getSuccessPkIds().size()]), OperateOption.create());
		}
	}

	/**
	 * 设置台账字段值以及变更记录
	 * @param bill
	 * @param tarField
	 * @param value
	 * @author: hongsitao
	 * @createDate:
	 */
	private void setValueWithRecord (DynamicObject bill, String tarField, Object value) {

	}
}
