package kd.bos.tcbj.srm.admittance.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.db.DB;
import kd.bos.db.tx.TX;
import kd.bos.db.tx.TXHandle;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.metadata.dao.MetaCategory;
import kd.bos.metadata.dao.MetadataDao;
import kd.bos.metadata.form.FormMetadata;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.bos.workflow.engine.msg.info.MessageInfo;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.admittance.helper.RawSupSumBillHelper
 * @className RawSupSumBillHelper
 * @author: hst
 * @createDate: 2022/09/19
 * @version: v1.0
 */
public class RawSupSumBillHelper {

    private final Log logger = LogFactory.getLog(this.getClass().getName());
    /* ------ 参数配置表字段标识 ------ */
    public final static String ENTITY_CONFIG = "yd_paramconfigure";
    public final static String FIELD_BILL = "yd_bill";
    public final static String ENTITY_ACCOUNTCONFIG = "yd_accountconfig";
    public final static String COLUMN_ATTACHMENT = "yd_attachmentname";
    public final static String COLUMN_FIELDMARK = "yd_fieldmark";
    public final static String COLUMN_VALIDYEAR = "yd_validyear";
    public final static String COLUMN_VALIDDAY = "yd_validday";
    public final static String COLUMN_LEVEL = "yd_materiallevel";
    public final static String COLUMN_TYPE = "yd_type";
    /* ------ (原辅料)合格供应商目录字段标识 ------ */
    public final static String ENTITY_MAIN = "yd_rawsupsumbill";
    public final static String FIELD_MATLEVEL = "yd_matleveltype";
    public final static String FIELD_MATERIAL = "yd_material";
    public final static String FIELD_PRODUCERS = "yd_producers";
    public final static String FIELD_AGENT = "yd_agent";
    public final static String FILED_BIZDATE = "yd_bizdate";
    public final static String FILED_LICENSE = "yd_licensedate";
    public final static String FILED_WQDATE = "yd_wqdate";
    public final static String FILED_TPZDATE = "yd_tprdate";
    public final static String FIELD_MDTDATE = "yd_mdtdate";
    public final static String FIELD_ORG = "org";
    public final static String FILED_CREATETIME = "createtime";
    public final static String FILED_NEWDATE = "yd_newdate";
    public final static String FIELD_SUPSTATUS = "yd_supplierstatus";
    public final static String FIELD_ACCESSPOT = "yd_supplieraccesspot";
    /* ------ 资料补充函 ------ */
    public final static String ENTITY_SUPPLYINFORMATION = "yd_supplyinformationbill";
    public final static String FIELD_INF_SUPPLIER = "yd_supplier";
    public final static String FIELD_INF_PRODUCERS = "yd_producers";
    public final static String FIELD_INF_MATERIAL = "yd_material";
    public final static String FIELD_INF_MATTYPE = "yd_mattype";
    public final static String FIELD_INF_LEVEL = "yd_materiallevel";
    public final static String FIELD_INF_ORG = "org";
    public final static String FIELD_INF_STATUS = "billstatus";
    public final static String FIELD_INF_CREATOR = "creator";
    public final static String FIELD_INF_CREATEDATE = "createtime";
    public final static String FIELD_INF_BIZDATE = "yd_bizdate";
    public final static String FIELD_INF_USER = "yd_supplieruser";
    public final static String FIELD_INF_USERS = "yd_mulsupuser";
    public final static String FIELD_INF_COMBOFIELD = "yd_combofield";
    public final static String FIELD_INF_COMBOFIELD2 = "yd_combofield2";
    public final static String FIELD_MATNAME = "yd_newmatname";
    private final static Map<String,String>  fieldMap = new HashMap<>();
    static{
        fieldMap.put("yd_agentdate","代理商-营业执照");
        fieldMap.put("yd_buslicense","代理商-经营许可证");
        fieldMap.put("yd_proof","代理商-代理证明");
        fieldMap.put("yd_bizdate","生产商-营业执照");
        fieldMap.put("yd_licensedate","生产商-生产许可");
        fieldMap.put("yd_wqdate","书面调查表");
        fieldMap.put("yd_tprdate","第三方报告");
        fieldMap.put("yd_mdtdate","物料描述表");
        fieldMap.put("yd_nngdate","非转基因/菌种证明");
        fieldMap.put("yd_spdate","菌种证明");
    }

    private final static Map<String,String>  typeMap = new HashMap<>();
    static{

        typeMap.put("yd_entryagent&yd_agentattachmenttype&A", "yd_agentdate");
        typeMap.put("yd_entryagent&yd_agentattachmenttype&B", "yd_buslicense");
        typeMap.put("yd_entryagent&yd_agentattachmenttype&C", "yd_proof");
        typeMap.put("yd_entrydomestic&yd_domesattachmenttype&A", "yd_bizdate");
        typeMap.put("yd_entrydomestic&yd_domesattachmenttype&C", "yd_licensedate");
        typeMap.put("yd_entrydomestic&yd_domesattachmenttype&E", "yd_wqdate");
        typeMap.put("yd_entryforeign&yd_foreigattachmenttype&C", "yd_wqdate");
        typeMap.put("yd_entryprocessfitinto&yd_pfitienattachmenttype&B", "yd_tprdate");
        typeMap.put("yd_entryprocessstar&yd_pstarattachmenttype&B", "yd_mdtdate");
        typeMap.put("yd_entryprocessfitinto&yd_pfitienattachmenttype&D", "yd_nngdate");
        typeMap.put("yd_entryprocessfitinto&yd_pfitienattachmenttype&E", "yd_spdate");
    }

    /**
     * 资质到期预警
     * @author: hst
     * @createDate: 2022/09/19
     */
    public void expirationAlert(List<String> ids, boolean isSendSuppier, boolean isSendPurchase) {
        Map<String,String> alertBill = new HashMap<>();
        Map<String,List<String>> alertType = new HashMap<>();
        Map<String,String> messages = new HashMap<>();
        //获取参数配置
        Map<String,Object> param = getConfig(false);
        Map<String,Object> material = null;
        Map<String,Map<String,Object>> levels = (Map<String,Map<String,Object>>) param.get("levels");
        String qfilters = param.get("qfilters").toString();

        //获取资质到期预警台账
        List<String> status = new ArrayList<>();
//        status.add("04");
        status.add("05");
        QFilter qFilter = new QFilter(FIELD_ACCESSPOT + ".group.number", QFilter.in, status);
        if (Objects.nonNull(ids) && ids.size() > 0) {
            qFilter = qFilter.and(new QFilter("id",QFilter.in,ids));
        }
        DataSet bills = QueryServiceHelper.queryDataSet(this.getClass().getName(),ENTITY_MAIN,"id,yd_agent.name," +
                StringUtils.join(DynamicObjectUtil.getAllField(BusinessDataServiceHelper.newDynamicObject(ENTITY_MAIN).getDynamicObjectType()),","),
                new QFilter[]{qFilter},null);
        bills = bills.where(qfilters.substring(0,qfilters.length()-4));

        for (Row row: bills) {
            //标记是否是物料附件过期
            StringBuffer errMsg = new StringBuffer();
            //校验是否是物料附件过期
            if (StringUtils.isNotBlank(row.get(FIELD_MATLEVEL).toString())) {
                //获取对应级次的提醒日期
                material = levels.get(row.get(FIELD_MATLEVEL).toString());
                if (Objects.nonNull(material)) {
                    for (Map.Entry<String, Object> entry : material.entrySet()) {
                        if (Long.valueOf(row.get(FIELD_MATERIAL).toString()) != 0L) {
                            if (row.get(entry.getKey()) != null) {
                                if (DateUtil.date2str((Date) row.get(entry.getKey()), "yyyy-MM-dd HH:mm:ss")
                                        .compareTo(entry.getValue().toString()) < 0) {
                                    errMsg.append("【" + (fieldMap.containsKey(entry.getKey()) ? fieldMap.get(entry.getKey()) : entry.getKey())
                                            + "】、");
                                    alertBill.put(row.getString("id"), "1");
                                    if (alertType.containsKey(row.getString("id"))) {
                                        List<String> types = alertType.get(row.getString("id"));
                                        List<String> temp = this.getFieldType(entry.getKey());
                                        types.addAll(temp);
                                    } else {
                                        List<String> types = getFieldType(entry.getKey());
                                        alertType.put(row.getString("id"), types);
                                    }
                                }
                            }

                        }
                    }
                }
            }
            //生产商、供应商附件过期校验
            Map<String,String> supFileFields = (Map<String,String>) param.get("suppliers");
            for (Map.Entry<String,String> supFileField : supFileFields.entrySet()) {
                String field = supFileField.getKey();
                if (row.get(field) != null) {
                    if (DateUtil.date2str((Date) row.get(field), "yyyy-MM-dd HH:mm:ss")
                            .compareTo(supFileFields.get(field).toString()) < 0) {
                        errMsg.append("【" + (fieldMap.containsKey(field) ? fieldMap.get(field) : field)
                                + "】、");
                        alertBill.put(row.getString("id"), "2");
                        if (alertType.containsKey(row.getString("id"))) {
                            List<String> types = alertType.get(row.getString("id"));
                            List<String> temp = this.getFieldType(field);
                            types.addAll(temp);
                        } else {
                            List<String> types = getFieldType(field);
                            alertType.put(row.getString("id"), types);
                        }
                    }
                }
            }

            if (errMsg.length() > 0) {
                messages.put(row.getString("id"),errMsg.substring(0,errMsg.length() - 1));
            }
        }
        //创建台账
        Map<String,String> contexts = createDataSupplementLetter(alertBill, alertType);
        // 发送短信和邮件
        this.sendShortMessage(contexts, isSendSuppier, isSendPurchase);
    }

    /**
     * 资质到期暂停台账
     * @author: hst
     * @createDate: 2024/04/28
     */
    public void expirationStop(List<String> ids, boolean isSendSuppier, boolean isSendPurchase) {
        //获取参数配置
        Map<String, Object> param = getConfig(true);

        List<String> stopBillIds = new ArrayList<>();
        Map<String, String> contexts = new HashMap<>();
        Map<String, Object> material = null;

        Map<String, Map<String, Object>> levels = (Map<String, Map<String, Object>>) param.get("levels");
        String qfilters = param.get("qfilters").toString();

        //获取资质到期预警台账
        QFilter qFilter = new QFilter(FIELD_ACCESSPOT + ".group.number", QFilter.equals, "05");
        if (Objects.nonNull(ids) && ids.size() > 0) {
            qFilter = qFilter.and(new QFilter("id",QFilter.in,ids));
        }
        DataSet bills = QueryServiceHelper.queryDataSet(this.getClass().getName(), ENTITY_MAIN, "id,yd_agent.name,yd_agent.supplier," +
                        StringUtils.join(DynamicObjectUtil.getAllField(BusinessDataServiceHelper.newDynamicObject(ENTITY_MAIN).getDynamicObjectType()), ","),
                new QFilter[]{qFilter}, null);
        bills = bills.where(qfilters.substring(0, qfilters.length() - 4));

        for (Row row : bills) {
            //标记是否是物料附件过期
            StringBuffer errMsg = new StringBuffer();
            //校验是否是物料附件过期
            if (StringUtils.isNotBlank(row.get(FIELD_MATLEVEL).toString())) {
                //获取对应级次的提醒日期
                material = levels.get(row.get(FIELD_MATLEVEL).toString());
                if (Objects.nonNull(material)) {
                    for (Map.Entry<String, Object> entry : material.entrySet()) {
                        if (Long.valueOf(row.get(FIELD_MATERIAL).toString()) != 0L) {
                            if (row.get(entry.getKey()) != null) {
                                if (DateUtil.date2str((Date) row.get(entry.getKey()), "yyyy-MM-dd HH:mm:ss")
                                        .compareTo(entry.getValue().toString()) < 0) {
                                    errMsg.append("【" + (fieldMap.containsKey(entry.getKey()) ? fieldMap.get(entry.getKey()) : entry.getKey())
                                            + "】、");
                                }
                            }
                        }
                    }
                }
            }

            //生产商、供应商附件过期校验
            Map<String,String> supFileFields = (Map<String,String>) param.get("suppliers");
            for (Map.Entry<String,String> supFileField : supFileFields.entrySet()) {
                String field = supFileField.getKey();
                if (row.get(field) != null) {
                    if (DateUtil.date2str((Date) row.get(field), "yyyy-MM-dd HH:mm:ss")
                            .compareTo(supFileFields.get(field).toString()) < 0) {
                        errMsg.append("【" + (fieldMap.containsKey(field) ? fieldMap.get(field) : field)
                                + "】、");
                    }
                }
            }

            if (errMsg.length() > 0) {
                // 暂停采购
                stopBillIds.add(row.getString("id"));

                // 发送短信内容
//                String context = "请注意：【" + row.getString("yd_agent.name") + "】-因" + errMsg.substring(0, errMsg.length() - 1)
//                        + "过期未更新，现已暂停供应";
                contexts.put(row.getString("id"), errMsg.substring(0, errMsg.length() - 1));
            }
        }

        // 原辅料合格供应商目录暂停采购
        createRawSupChange(stopBillIds, contexts);

//        // 原辅料合格供应商目录资质文件到期暂停采购短信通知
//        stopRawSupSumBillSendMessage(isSendSuppier,isSendPurchase,stopBills);
    }

    /**
     * 原辅料合格供应商目录暂停采购
     * @param billIds
     * @author: hst
     * @createDate: 2024/04/28
     */
    private void stopRawSupSumBill (List<String> billIds) {
        List<DynamicObject> supplyLists = new ArrayList<>();

        // 供应商状态（暂停采购）信息
        QFilter stateFilter = new QFilter("number", QCP.equals, "08");
        DynamicObject supState = BusinessDataServiceHelper.loadSingle("yd_supplierstatus",
                "id,name,number", stateFilter.toArray());
        // 供应商准入节点（暂停采购）信息
        QFilter accessFilter = new QFilter("number", QCP.equals, "115");
        DynamicObject accessPot = BusinessDataServiceHelper.loadSingle("yd_supplieraccesspot",
                "id,name,number", accessFilter.toArray());

        if (Objects.nonNull(supState) && Objects.nonNull(accessPot)) {
            DynamicObject[] bills = BusinessDataServiceHelper.load(ENTITY_MAIN, "id," + FIELD_ACCESSPOT
                            + "," + FIELD_AGENT + "," + FIELD_MATERIAL,
                    new QFilter[]{new QFilter("id", QFilter.in, billIds)});

            for (DynamicObject bill : bills) {
                DynamicObject srmSupply = bill.getDynamicObject(FIELD_AGENT);
                DynamicObject bdSupply = Objects.nonNull(srmSupply) ? srmSupply.getDynamicObject("supplier") : null;
                String supplyId = Objects.nonNull(bdSupply) ? bdSupply.getPkValue().toString() : "";
                String materialId = bill.getString(FIELD_MATERIAL);
                if (StringUtils.isNotBlank(supplyId) && StringUtils.isNotBlank(materialId)) {
                    // 获取货源清单，货源清单如果是核准状态则重置为暂存状态
                    DynamicObject supplyList = new SupplyListBizHelper().getSupplyList(supplyId, materialId);
                    if (supplyList != null) {
                        if ("1".equals(supplyList.getString("yd_isuseable"))) {
                            supplyList.set("yd_issyn", false);
                            supplyList.set("yd_isuseable", "0");
                            supplyList.set("yd_supplierstatus", supState);
                            supplyLists.add(supplyList);
                        }
                    }
                }
                // 设置准入节点为暂停
                bill.set(FIELD_ACCESSPOT, accessPot);
            }

            SaveServiceHelper.save(bills);
            SaveServiceHelper.save(supplyLists.toArray(new DynamicObject[supplyLists.size()]));
        }
    }

    /**
     * 原辅料合格供应商目录资质文件到期暂停采购短信通知
     * @param isSendSuppier
     * @param isSendPurchase
     * @param stopBills
     * @author: hst
     * @createDate: 2024/04/28
     */
    private void stopRawSupSumBillSendMessage (boolean isSendSuppier,boolean isSendPurchase, Map<String, String> stopBills) {
        if (isSendSuppier) {
            for (Map.Entry<String,String> entrySet : stopBills.entrySet()) {
                String supplierId = entrySet.getKey().split("&")[1];
                String supplierName = entrySet.getKey().split("&")[2];
                String context = entrySet.getValue();
                // 发送短信
                this.sendShortMessageToSupplier(supplierId,"【" + supplierName + "】资质文件到期暂停采购通知",context);
            }
        }
        if (isSendPurchase) {
            // 质量管理部短信配置
            DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
                    "name", new QFilter("number", QCP.equals, "SRM_OVER_STOP_PHONE").toArray());

            if (Objects.nonNull(param) && Objects.nonNull(param.get("name"))) {
                String[] phones = param.get("name").toString().split(",");

                for (Map.Entry<String,String> entrySet : stopBills.entrySet()) {
                    String context = entrySet.getValue();
                    MessageUtil.sendShortMessage(Arrays.stream(phones).collect(Collectors.toList()), context);
                }
            }

            // 质量管理部邮件配置
            param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
                    "name", new QFilter("number", QCP.equals, "SRM_OVER_STOP_EMAIL").toArray());

            if (Objects.nonNull(param) && Objects.nonNull(param.get("name"))) {
                String[] emails = param.get("name").toString().split(",");

                for (Map.Entry<String,String> entrySet : stopBills.entrySet()) {
                    String supplierName = entrySet.getKey().split("&")[2];
                    String context = entrySet.getValue();
                    MessageUtil.sendEmail(Arrays.stream(emails).collect(Collectors.toList()),"【" + supplierName + "】资质文件到期暂停采购通知", context);
                }
            }
        }
    }

    /**
     * 向供应商发送短信
     * @param supplierId
     * @param context
     * @author: hst
     * @createDate: 2024/04/28
     */
    private void sendShortMessageToSupplier (String supplierId, String title, String context) {
        // 获取供应商用户
        DynamicObjectCollection supplierUsers = new BizPartnerBizHelper().getMulBdUserBySupplierId(supplierId);
        if (Objects.nonNull(supplierUsers) && supplierUsers.size() > 0) {
            List<String> phones = supplierUsers.stream().filter(supplierUser -> StringUtils.isNotBlank(supplierUser.getString("phone")))
                    .map(supplierUser -> supplierUser.getString("phone")).collect(Collectors.toList());
            MessageUtil.sendShortMessage(phones,context);
            List<String> emails = supplierUsers.stream().filter(supplierUser -> StringUtils.isNotBlank(supplierUser.getString("email")))
                    .map(supplierUser -> supplierUser.getString("email")).collect(Collectors.toList());
            MessageUtil.sendEmail(emails,title,context);
        }
    }

    /**
     * 获取参数配置表中的属性
     * 记录日期，用于后续取出数据后进行类型校验
     * @author: hst
     * @createDate: 2022/09/19
     * @return
     */
    public Map<String,Object> getConfig(boolean isStop) {
        Map<String,Object> param = new HashMap<>();
        Map<String,Object> material = null;
        Map<String,Map<String,Object>> levelMap = new HashMap<>();
        Map<String,String> supFileMap = new HashMap<>();
        //获取参数配置
        QFilter qFilter = new QFilter(FIELD_BILL,QFilter.equals,"1");

        if (isStop) {
            qFilter = qFilter.and(new QFilter("yd_isstop", QFilter.equals, true));
        }
        DataSet configs = QueryServiceHelper.queryDataSet(this.getClass().getName(),ENTITY_CONFIG,
                ENTITY_ACCOUNTCONFIG + "." + COLUMN_FIELDMARK + "," + ENTITY_ACCOUNTCONFIG + "." + COLUMN_VALIDYEAR
                        + "," + ENTITY_ACCOUNTCONFIG + "." + COLUMN_VALIDDAY + "," + ENTITY_ACCOUNTCONFIG + "." + COLUMN_LEVEL
                        + "," + ENTITY_ACCOUNTCONFIG + "." + COLUMN_TYPE,
                new QFilter[]{qFilter},null);
        StringBuffer qfilters = new StringBuffer();
        //过滤条件拼接
        for (Row row : configs) {
            //日期计算
            String date;
            if (isStop) {
                date = DateTimeUtils.format(new Date(), "yyyy-MM-dd");
            } else {
                date = DateTimeUtils.format(DateTimeUtils.addTime(new Date(),0,
                        0, (int) row.get(ENTITY_ACCOUNTCONFIG + "." + COLUMN_VALIDDAY)), "yyyy-MM-dd");
            }
            String levelStr = row.getString(ENTITY_ACCOUNTCONFIG + "." + COLUMN_LEVEL);
            // 没有物料级别的附件类型
            if (StringUtils.isBlank(levelStr)) {
                supFileMap.put(row.getString(ENTITY_ACCOUNTCONFIG + "." + COLUMN_FIELDMARK ),date);
            } else {
                //存储每个级次对应的有效期
                String[] levels = StringUtils.equals(levelStr, "") ? new String[]{""} : levelStr.substring(1).split(",");
                for (String level : levels) {
                    if (StringUtils.equals(row.getString(ENTITY_ACCOUNTCONFIG + "." + COLUMN_TYPE), "3")) {
                        if (levelMap.containsKey(level)) {
                            material = levelMap.get(level);
                            material.put(row.getString(ENTITY_ACCOUNTCONFIG + "." + COLUMN_FIELDMARK), date);
                        } else {
                            material = new HashMap<>();
                            material.put(row.getString(ENTITY_ACCOUNTCONFIG + "." + COLUMN_FIELDMARK), date);
                            levelMap.put(level, material);
                        }
                    }
                }
            }
            String tempFilter = "to_date(to_char(" + row.getString(ENTITY_ACCOUNTCONFIG + "." + COLUMN_FIELDMARK ) + ",'yyyy-MM-dd'),'yyyy-MM-dd')"
                    + " < to_date('" + date + "','yyyy-MM-dd')";
            //是否需要根据物料级次进行不同条件过滤
            if (StringUtils.isNotBlank(levelStr)) {
                qfilters.append("(" + new QFilter(FIELD_MATLEVEL,QFilter.in,levelStr.substring(1).split(",")).toString() + " and " + tempFilter + ") or ");
            } else {
                qfilters.append("(" + tempFilter + ") or ");
            }
        }
        param.put("levels",levelMap);
        param.put("qfilters",qfilters.toString());
        param.put("suppliers",supFileMap);
        return param;
    }

    /**
     * 创建资料补充函并提交工作流
     * @author: hst
     * @createDate: 2022/09/20
     * @param alertBill（masterId：1、生产商+供应商+物料维度；2、生产商+供应商维度）
     * @return
     */
    public Map<String,String> createDataSupplementLetter (Map<String,String> alertBill,
                                                          Map<String,List<String>> alertType) {
        Map<String,String> billMap = new HashMap<>();
        Map<String,String> contexts = new HashMap<>();
        List<DynamicObject> bills = new ArrayList<>();
        Map<String,String> messages = new HashMap<>();

        long userId = UserServiceHelper.getCurrentUserId();
        DynamicObject[] sumbills = BusinessDataServiceHelper.load(alertBill.keySet().toArray(),
                BusinessDataServiceHelper.newDynamicObject(ENTITY_MAIN).getDynamicObjectType());
        for (DynamicObject sumBill : sumbills) {
            DynamicObject supplyInformation;
            String masterId = sumBill.getPkValue().toString();
            String type = alertBill.get(masterId);
            //校验是否已存在该维度的资料补充函
            DynamicObject bill = existSupplementLetter(type,sumBill);

            if ("A".equals(bill.getString("billstatus"))) {
                supplyInformation = bill;
            } else {
                supplyInformation = BusinessDataServiceHelper.newDynamicObject(ENTITY_SUPPLYINFORMATION);
            }

            /* 自动新增资质类型 */
            StringBuffer errMsg = new StringBuffer();
            List<String> types = alertType.get(masterId);
            for (String key : types) {
                String[] keys = key.split("&");
                DynamicObjectCollection entries = supplyInformation.getDynamicObjectCollection(keys[0]);
                Set<String> existType = entries.stream().map(entry -> entry.getString(keys[1])).collect(Collectors.toSet());
                Set<String> submitType = new HashSet<>();
                if ("B".equals(bill.getString("billstatus"))
                        || "E".equals(bill.getString("billstatus"))) {
                    DynamicObjectCollection submitEntries = bill.getDynamicObjectCollection(keys[0]);
                    submitType = submitEntries.stream().map(entry -> entry.getString(keys[1])).collect(Collectors.toSet());
                }

                if (!existType.contains(keys[2]) && !existType.contains(keys[2])) {
                    DynamicObject entry = entries.addNew();
                    entry.set(keys[1], keys[2]);
                }

                if (!submitType.contains(keys[2])) {
                    errMsg.append("【" + (typeMap.containsKey(key) ? fieldMap.containsKey(typeMap.get(key)) ?
                            fieldMap.get(typeMap.get(key)) : "" : "") + "】、");
                }
            }

            if (errMsg.length() > 0) {
                messages.put(masterId, errMsg.substring(0, errMsg.length() - 1));

                //根据srm供应商获取对应主数据用户对象
                DynamicObjectCollection users = new BizPartnerBizHelper().
                        getMulBdUserBySrmSupplierId(((DynamicObject) sumBill.get(FIELD_AGENT)).getPkValue().toString());
                switch (type) {
                    case "1": {
                        supplyInformation.set(FIELD_INF_MATERIAL, sumBill.get(FIELD_MATERIAL));
                        supplyInformation.set(FIELD_INF_MATTYPE, "1");
                        supplyInformation.set(FIELD_INF_LEVEL, sumBill.get(FIELD_MATLEVEL));
                    }
                    case "2": {
                        supplyInformation.set(FIELD_INF_MATERIAL, sumBill.get(FIELD_MATERIAL));
                        supplyInformation.set(FIELD_INF_MATTYPE, "1");
                        supplyInformation.set(FIELD_INF_LEVEL, sumBill.get(FIELD_MATLEVEL));
                        supplyInformation.set(FIELD_INF_SUPPLIER, sumBill.get(FIELD_AGENT));
                        supplyInformation.set(FIELD_INF_PRODUCERS, sumBill.get(FIELD_PRODUCERS));
                        supplyInformation.set(FIELD_INF_ORG, sumBill.get(FIELD_ORG));
                        supplyInformation.set(FIELD_INF_STATUS, "A");
                        supplyInformation.set(FIELD_INF_CREATOR, userId);
                        supplyInformation.set(FIELD_INF_CREATEDATE, new Date());
                        supplyInformation.set(FIELD_INF_BIZDATE, new Date());
                        supplyInformation.set(FIELD_INF_COMBOFIELD, "否");
                        supplyInformation.set(FIELD_INF_COMBOFIELD2, "是");
                        for (DynamicObject user : users) {
                            DynamicObject userInfo = supplyInformation.getDynamicObjectCollection(FIELD_INF_USERS).addNew();
                            userInfo.set("fbasedataid", user);
                        }
                    }
                    default: {
                        if (0L == supplyInformation.getLong("id")) {
                            supplyInformation.set("id", DB.genLongId(ENTITY_SUPPLYINFORMATION));
                        }
                        bills.add(supplyInformation);
                        billMap.put(supplyInformation.getString("id"), sumBill.getString("id"));
                    }
                }
            }
        }
        if (bills.size() > 0) {
            Set<String> billIds = this.saveAndSumbitBill(ENTITY_SUPPLYINFORMATION,bills);
            for (String billId : billIds) {
                contexts.put(billId,messages.get(billMap.get(billId)));
            }
        }
        return contexts;
    }

    /**
     * 以不同维度校验是否存在资料补充函
     * @author: hst
     * @createDate: 2022/09/20
     * @param type（1、生产商+供应商+物料维度；2、生产商+供应商维度）
     * @return
     */
    public DynamicObject existSupplementLetter(String type, DynamicObject sumBill) {
        List<QFilter> qFilters = new ArrayList<>();
        switch (type) {
            case "1" : {
                qFilters.add(new QFilter(FIELD_INF_MATERIAL + ".number", QFilter.equals,
                        ((DynamicObject) sumBill.get(FIELD_MATERIAL)).getString("number")));
            }
            case "2" : {
                qFilters.add(new QFilter(FIELD_INF_SUPPLIER + ".number", QFilter.equals,
                        ((DynamicObject) sumBill.get(FIELD_AGENT)).getString("number")));
                qFilters.add(new QFilter(FIELD_INF_PRODUCERS + ".number", QFilter.equals,
                        ((DynamicObject) sumBill.get(FIELD_PRODUCERS)).getString("number")));
                qFilters.add(QFilter.or(new QFilter(FIELD_INF_STATUS, QFilter.equals, "A"),
                        new QFilter(FIELD_INF_STATUS, QFilter.equals, "B")));
            }
        }

        DynamicObject bill = BusinessDataServiceHelper.loadSingle(ENTITY_SUPPLYINFORMATION,
                qFilters.toArray(new QFilter[qFilters.size()]));

        if (Objects.isNull(bill)) {
            bill = BusinessDataServiceHelper.newDynamicObject(ENTITY_SUPPLYINFORMATION);
        }

        return bill;
    }

    /**
     * 发送短信
     * @author: hst
     * @createDate: 2022/09/20
     * @param contexts
     */
    public void sendShortMessage(Map<String,String> contexts, boolean isSendSuppier, boolean isSendPurchase) {
        // 质量管理部短信配置
        DynamicObject userParam = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
                "name", new QFilter("number", QCP.equals, "SRM_OVER_REMIND_USER").toArray());
        // 质量管理部邮件配置
        DynamicObject emailParam = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
                "name", new QFilter("number", QCP.equals, "SRM_OVER_REMIND_EMAIL").toArray());

        DynamicObject[] supplyInformations = BusinessDataServiceHelper.load(contexts.keySet().toArray(),
                BusinessDataServiceHelper.newDynamicObject(ENTITY_SUPPLYINFORMATION).getDynamicObjectType());
        for (DynamicObject supplyInformation : supplyInformations) {
            DynamicObject supplier = supplyInformation.getDynamicObject(FIELD_INF_SUPPLIER);
            String title = "【" + supplier.getString("name") + "】资质到期预警";
            String context = "【" + supplier.getString("name") + "】的"
                    + contexts.get(supplyInformation.getString("id")) + "即将过期，请处理资料补充函更新资质信息！";

            // 是否向供应商发送短信
            if (isSendSuppier) {
                DynamicObjectCollection users = supplyInformation.getDynamicObjectCollection(FIELD_INF_USERS);
                List<String> phones = users.stream().filter(user -> StringUtils.isNotBlank(user.getDynamicObject("fbasedataid").getString("phone")))
                        .map(user -> user.getDynamicObject("fbasedataid").getString("phone")).collect(Collectors.toList());
                if (phones.size() > 0) {
                    MessageUtil.sendShortMessage(phones, context);
                }

                List<String> emails = users.stream().filter(user -> StringUtils.isNotBlank(user.getDynamicObject("fbasedataid").getString("email")))
                        .map(user -> user.getDynamicObject("fbasedataid").getString("email")).collect(Collectors.toList());
                if (emails.size() > 0) {
                    MessageUtil.sendEmail(emails, title, context);
                }
            }

            // 是否向采购方发送短信
            if (isSendPurchase) {
                context = "【" + supplier.getString("name") + "】的"
                        + contexts.get(supplyInformation.getString("id")) + "即将过期，已提醒供应商补充资料补充函更新资质信息。";
                // 质量管理部短信
                if (Objects.nonNull(userParam) && Objects.nonNull(userParam.get("name"))) {
                    Long userId = userParam.getLong("name");
                    // 元数据信息
                    FormMetadata formMetadata = (FormMetadata) MetadataDao.readRuntimeMeta(MetadataDao.getIdByNumber("yd_supplyinformationbill",
                                    MetaCategory.Form), MetaCategory.Form);
                    // 元数据名称
                    LocaleString metaName = formMetadata.getRootAp().getName();
                    // 当前用户信息
//                    Map<String, Object> userInfo = UserServiceHelper.getUserInfoByID(userParam.getLong("name"));
                    // 标题
                    LocaleString esbTitle = new LocaleString("合格目录资质到期预警：供应商【" +supplier.getString("name") + "】的资质即将到期。");
                    // 内容
                    LocaleString esbContext = new LocaleString(context);

                    MessageInfo messageInfo = MessageUtil.sendMessageCenter("yd_supplyinformationbill",
                            supplyInformation.getString("id"), new HashSet<>(Arrays.asList(new Long[]{userId})), esbTitle, esbContext);

                    // 发送ESB待办
                    try {
                        MessageUtil.sendESBMessage(messageInfo);
                    } catch (Exception e) {
                        logger.error("kd.bos.tcbj.srm.admittance.helper.RawSupSumBillHelper.sendShortMessage —— " + e.getMessage());
                    }
                }

                // 质量管理部邮件
                if (Objects.nonNull(emailParam) && Objects.nonNull(emailParam.get("name"))) {
                    String[] emails = emailParam.get("name").toString().split(",");
                    MessageUtil.sendEmail(Arrays.stream(emails).collect(Collectors.toList()), title, context);
                }
            }
        }
    }

    /**
     * 对单据进行保存并审核
     * @author: hst
     * @createDate: 2022/9/20
     * @param billType 需要执行的单据类型
     * @param bills 单据数组
     * @return
     */
    public Set<String> saveAndSumbitBill(String billType, List<DynamicObject> bills) {
        Set<String> billIds = new HashSet<>();
        for (DynamicObject bill : bills) {
            try {
                OperationResult result = OperationServiceHelper.executeOperate("save", billType,
                        new DynamicObject[]{bill}, OperateOption.create());
                if (!result.isSuccess()) {
                    logger.error(result.getMessage());
                } else {
                    try {
                        result = OperationServiceHelper.executeOperate("alertsubmit", billType,
                               new DynamicObject[]{bill}, OperateOption.create());
                        if (!result.isSuccess()) {
                            logger.error(result.getMessage());
                            //提交不成功，删除单据
                            DeleteServiceHelper.delete(billType, new QFilter[]{new QFilter("id", QFilter.equals, bill.getPkValue().toString())});
                        } else {
                            billIds.add(bill.getString("id"));
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage());
                        //提交异常，删除该单据
                        DeleteServiceHelper.delete(billType, new QFilter[]{new QFilter("id", QFilter.equals, bill.getPkValue().toString())});
                    }
                }
            } catch (Exception e) {
                logger.error(e);
            }
        }
        return billIds;
    }

    /**
     * 长时间未采购暂停（原辅料）合格供应商目录
     * 台账创建时间大于3个月且3个月内没有采购订单
     * @author: hst
     * @createDate: 2022/12/08
     */
    public void checkLongTimeNoPurchase(List<String> ids, int month, boolean isStop,boolean isSendSuppier,boolean isSendPurchase) {
        // 供应商状态（暂停采购）信息
        QFilter stateFilter = new QFilter("number", QCP.equals,"08");
        DynamicObject supState = BusinessDataServiceHelper.loadSingle("yd_supplierstatus",
                "id,name,number", stateFilter.toArray());
        // 供应商准入节点（暂停采购）信息
        QFilter accessFilter = new QFilter("number", QCP.equals,"115");
        DynamicObject accessPot = BusinessDataServiceHelper.loadSingle("yd_supplieraccesspot",
                "id,name,number", accessFilter.toArray());

        if (Objects.nonNull(supState) && Objects.nonNull(accessPot)) {
            List<DynamicObject> supBills = new ArrayList<>();
            List<DynamicObject> supplyLists = new ArrayList<>();
            Date today = new Date();
            String date = DateUtil.date2str(DateUtil.addDays(today, -(month * 30)), "yyyy-MM-dd");

            // 查询新增时间大于n个月且供应商状态为现用供应商的（原辅料）合格供应商目录
            QFilter dateFilter = null;
            dateFilter = new QFilter(FILED_NEWDATE, QFilter.less_equals, date);
            QFilter supFilter = new QFilter(FIELD_ACCESSPOT + ".group.number", QFilter.equals, "05");
            if (Objects.nonNull(ids) && ids.size() > 0) {
                supFilter = supFilter.and(new QFilter("id",QFilter.in,ids));
            }
            DynamicObject[] bills = BusinessDataServiceHelper.load(ENTITY_MAIN, FIELD_AGENT + ".supplier.id,"
                            + FIELD_MATERIAL + "," + FIELD_ACCESSPOT + "," + FIELD_PRODUCERS + "," + FIELD_MATNAME + "," + FILED_NEWDATE,
                    new QFilter[]{dateFilter, supFilter});

            // 采购订单创建日期过滤
            QFilter orderFilter = new QFilter("billdate", QFilter.large_equals, date);
            Arrays.stream(bills).forEach(bill -> {
                DynamicObject srmSupply = bill.getDynamicObject(FIELD_AGENT);
                DynamicObject bdSupply = Objects.nonNull(srmSupply) ? srmSupply.getDynamicObject("supplier") : null;
                String supplyId = Objects.nonNull(bdSupply) ? bdSupply.getPkValue().toString() : "";
                String materialId = bill.getString(FIELD_MATERIAL + ".id");
                if (StringUtils.isNotBlank(supplyId) && StringUtils.isNotBlank(materialId)) {
                    if (isStop) {
                        // 查询是否有采购订单
                        boolean isExist = QueryServiceHelper.exists("scp_order",
                                new QFilter[]{QFilter.of("supplier.id = ? and materialentry.material.id = ?", supplyId, materialId), orderFilter});
                        if (!isExist) {
                            // 获取货源清单，货源清单如果是核准状态则重置为暂存状态
                            DynamicObject supplyList = new SupplyListBizHelper().getSupplyList(supplyId, materialId);
                            if (supplyList != null) {
                                if ("1".equals(supplyList.getString("yd_isuseable"))) {
                                    supplyList.set("yd_issyn", false);
                                    supplyList.set("yd_isuseable", "0");
                                    supplyList.set("yd_supplierstatus", supState);
                                    supplyLists.add(supplyList);
                                }
                            }
                            // 设置准入节点为暂停
                            bill.set(FIELD_ACCESSPOT, accessPot);
                            supBills.add(bill);
                        }
                    } else {
                        // 获取最新的订单日期
                        DataSet purOrders = QueryServiceHelper.queryDataSet(this.getClass().getName(), "scp_order", "billdate",
                                new QFilter[]{QFilter.of("supplier.id = ? and materialentry.material.id = ?", supplyId, materialId)},
                                "billdate desc");
                        if (purOrders.hasNext()) {
                            Row row = purOrders.next();
                            Date bizDate = row.getDate("billdate");
                            String bizDateStr = DateUtil.date2str(bizDate, "yyyy-MM-dd");
                            if (date.equals(bizDateStr)) {
                                supBills.add(bill);
                            }
                        } else {
                            Date newDate = bill.getDate(FILED_NEWDATE);
                            int differDay = DateUtil.getDifferDays(newDate,today);
                            if ((differDay % month * 30) == 0) {
                                supBills.add(bill);
                            }
                        }
                    }
                }
            });
            // 保存操作，若其中一步错误则回滚
            try (TXHandle h = TX.required("yd_rawsupsumbill")){
                try {
                    SaveServiceHelper.save(supplyLists.toArray(new DynamicObject[supplyLists.size()]));
                    SaveServiceHelper.save(supBills.toArray(new DynamicObject[supBills.size()]));
                } catch (Throwable e) {
                    h.markRollback();
                    throw e;
                }
            }

            // 发送短信
            longTimeNoPurchaseSendMessage(month,supBills,isStop,isSendSuppier,isSendPurchase);
        } else {
            throw new KDBizException("系统异常，请联系管理员！");
        }
    }

    /**
     * 长时间未采购合格供应商目录短信提醒
     * @param month
     * @param supBills 原辅料合格供应商目录
     * @param isStop 是否暂停
     * @param isSendSuppier 是否发送供应商
     * @param isSendPurchase 是否发送
     * @author: hst
     * @createDate: 2024/04/28
     */
    private void longTimeNoPurchaseSendMessage(int month, List<DynamicObject> supBills, boolean isStop,
                                               boolean isSendSuppier, boolean isSendPurchase) {
        // 质量管理部短信配置
        DynamicObject phoneParam = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
                "name", new QFilter("number", QCP.equals, isStop ? "SRM_UNPUR_STOP_PHONE" : "SRM_UNPUR_REMIND_PHONE").toArray());
        // 质量管理部邮件配置
        DynamicObject emailParam = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
                "name", new QFilter("number", QCP.equals, isStop ? "SRM_UNPUR_STOP_EMAIL" : "SRM_UNPUR_REMIND_EMAIL").toArray());

        for (DynamicObject supBill : supBills) {
            String proName = supBill.getString(FIELD_PRODUCERS + ".name");
            DynamicObject supplier = supBill.getDynamicObject(FIELD_AGENT);
            DynamicObject material = supBill.getDynamicObject(FIELD_MATERIAL);
            String matName = supBill.getString(FIELD_MATNAME);

            if (Objects.isNull(material)) {
                material = BusinessDataServiceHelper.loadSingle("bd_material",
                        new QFilter[]{new QFilter("name",QCP.equals,matName)});
            }

            int diffMonth = isStop ? month : DateUtil.getMonthDiff(supBill.getDate(FILED_NEWDATE),new Date());
            String context = "请注意：【" + proName + "】-【" + supplier.getString("name") + "】" +
                    (Objects.nonNull(material) ? "-【" + material.getString("number") + "】-【"
                    + material.getString("name") + "】超过" + Math.abs(diffMonth) + "个月未采购" : "") + (isStop ? "，现已暂停供应" : "");

            // 是否发送供应商
            if (isSendSuppier) {
                // 获取供应商用户
                DynamicObjectCollection supplierUsers = new BizPartnerBizHelper().getMulBdUserBySupplierId(supplier.getString("supplier.id"));
                if (Objects.nonNull(supplierUsers) && supplierUsers.size() > 0) {
                    List<String> phones = supplierUsers.stream().filter(supplierUser -> StringUtils.isNotBlank(supplierUser.getString("phone")))
                            .map(supplierUser -> supplierUser.getString("phone")).collect(Collectors.toList());
                    MessageUtil.sendShortMessage(phones, context);
                    List<String> emails = supplierUsers.stream().filter(supplierUser -> StringUtils.isNotBlank(supplierUser.getString("email")))
                            .map(supplierUser -> supplierUser.getString("email")).collect(Collectors.toList());
                    MessageUtil.sendEmail(emails,"【" + supplier.getString("name") + "】" + (isStop ? "长时间未采购暂停采购" : "长时间未采购提醒"), context);
                }
            }
            // 是否发送采购方
            if (isSendPurchase) {
                if (Objects.nonNull(phoneParam) && Objects.nonNull(phoneParam.get("name"))) {
                    String[] phones = phoneParam.get("name").toString().split(",");
                    MessageUtil.sendShortMessage(Arrays.stream(phones).collect(Collectors.toList()), context);
                }
                if (Objects.nonNull(emailParam) && Objects.nonNull(emailParam.get("name"))) {
                    String[] emails = emailParam.get("name").toString().split(",");
                    MessageUtil.sendEmail(Arrays.stream(emails).collect(Collectors.toList()),
                            "【" + supplier.getString("name") + "】" + (isStop ? "长时间未采购暂停采购" : "长时间未采购提醒"), context);
                }
            }
        }
    }

    /**
     * 获取台账变更记录
     * @param id
     * @param entityName
     * @return
     * @author: hst
     * @createDate: 2024/05/16
     */
    public DynamicObjectCollection getChangeRecords (String id, String entityName) {
        QFilter qFilter = new QFilter("yd_srcbillid", QFilter.equals, id);
        qFilter = qFilter.and(new QFilter("yd_datastatus",QFilter.equals, "B"));
        DynamicObjectCollection records = QueryServiceHelper.query(entityName,
                "entryentity.yd_infotype,entryentity.yd_chgfield,entryentity.yd_oldvalue,entryentity.yd_newvalue," +
                        "entryentity.yd_note,yd_datastatus,auditdate",new QFilter[]{qFilter},"auditdate desc");
        return records;
    }

    /**
     * 获取台账取消资格记录
     * @param id
     * @param entityName
     * @return
     * @author: hst
     * @createDate: 2024/05/29
     */
    public DynamicObjectCollection getCancelRecords (String id, String entityName) {
        QFilter qFilter = QFilter.of("yd_srcbillid = ? and billstatus = ? and (yd_cancelitem != '' or yd_cancelmatcode != '' " +
                "or yd_assessrisk != '')", id, "C");
        DynamicObjectCollection records = QueryServiceHelper.query(entityName,
                "yd_cancelitem,yd_cancelmatcode,yd_assessrisk,yd_cancelreason,auditdate,billstatus",
                new QFilter[]{qFilter},"auditdate desc");
        return records;
    }

    /**
     * 获取资质类型字段信息
     * @param key
     * @return
     * @author: hongsitao
     * @createDate: 2024/12/14
     */
    private List<String> getFieldType (String key) {
        List<String> infos = new ArrayList<>();
        for (Map.Entry<String, String> type : typeMap.entrySet()) {
            if (type.getValue().equals(key)) {
                infos.add(type.getKey());
            }
        }

        return infos;
    }

    /**
     * 创建准入节点变更为暂停采购的合格目录变更单
     * @author: hongsitao
     * @createDate: 2024/12/16
     */
    private void createRawSupChange (List<String> stopBillIds, Map<String, String> contexts) {
        DynamicObject[] bills = BusinessDataServiceHelper.load("yd_rawsupsumbill",
                "id, billno, yd_supplieraccesspot, yd_agent, yd_producers, yd_material",
                new QFilter("id", QFilter.in, stopBillIds).toArray());

        DynamicObject stopPot = BusinessDataServiceHelper.loadSingle("yd_supplieraccesspot"
                , QFilter.of ("number = '115'").toArray());

        List<DynamicObject> saveBills = new ArrayList<>();
        for (DynamicObject bill : bills) {
            String billId = bill.getString("id");

            boolean isExist = QueryServiceHelper.exists("yd_rawsupchgbill", QFilter.of("yd_srcbillid = ?" +
                    " and entryentity.yd_fieldname = 'yd_supplieraccesspot' and entryentity.yd_newvalue = '暂停采购'" +
                    " and billstatus != 'C'", billId).toArray());

            if (!isExist) {
                DynamicObject changeBill = BusinessDataServiceHelper.newDynamicObject("yd_rawsupchgbill");
                DynamicObject accessPot = bill.getDynamicObject("yd_supplieraccesspot");

                changeBill.set("id", DB.genLongId("yd_rawsupchgbill"));
                changeBill.set("org", RequestContext.get().getOrgId());
                changeBill.set("yd_srcbillno", bill.getString("billno"));
                changeBill.set("yd_srcbillid", billId);
                changeBill.set("yd_supplier", bill.getDynamicObject("yd_agent"));
                changeBill.set("yd_materiel", bill.getDynamicObject("yd_material"));
                changeBill.set("yd_datastatus", "A");
                changeBill.set("billstatus", "A");
                changeBill.set("creator", UserServiceHelper.getCurrentUserId());
                changeBill.set("yd_cancelitem", "2");
                changeBill.set("yd_cancelmatcode", "2");
                changeBill.set("yd_cancelreason", contexts.get(billId) + "到期暂停采购。");
                changeBill.set("yd_isstop", true);

                DynamicObject entry = changeBill.getDynamicObjectCollection("entryentity").addNew();
                entry.set("seq", 1);
                entry.set("yd_infotype", "Z");
                entry.set("yd_chgfield", "准入节点");
                entry.set("yd_oldvalue", accessPot.getString("name"));
                entry.set("yd_newvalue", stopPot.getString("name"));
                entry.set("yd_fieldname", "yd_supplieraccesspot");
                entry.set("yd_note", contexts.get(billId) + "到期暂停采购。");
                entry.set("yd_oldvalueex", "{\"enttype\":\"yd_supplieraccesspot\",\"value\":\"" + accessPot.getPkValue() + "\"}");
                entry.set("yd_newvalueex", "{\"enttype\":\"yd_supplieraccesspot\",\"value\":\"" + stopPot.getPkValue() + "\"}");

                saveBills.add(changeBill);
            }
        }

        OperationResult result =OperationServiceHelper.executeOperate("save", "yd_rawsupchgbill",
                saveBills.toArray(new DynamicObject[saveBills.size()]));
        if (result.isSuccess()) {
            OperationServiceHelper.executeOperate("submit", "yd_rawsupchgbill",
                    result.getSuccessPkIds().toArray(new Object[result.getSuccessPkIds().size()]), OperateOption.create());
        }

        SaveServiceHelper.save(bills);
    }
}
