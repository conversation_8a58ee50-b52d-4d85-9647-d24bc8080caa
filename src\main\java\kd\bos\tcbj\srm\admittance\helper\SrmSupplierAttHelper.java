package kd.bos.tcbj.srm.admittance.helper;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.entity.BillEntityType;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * @auditor yanzuwei
 * @date 2022年8月17日
 * @desc 供应商库附件反写
 */
public class SrmSupplierAttHelper
{
     
	
	public static Map getAttDataMap()
	{
		//附件类型枚举值 map 值组成： 枚举名称_供应商资质类型值
		Map<String,Map<String,String>> attachFieldMap = new HashMap<String,Map<String,String>>();
		
		Map<String,String> entrydomesticType = new HashMap<String,String>();
		entrydomesticType.put("A", "营业执照_2");
		entrydomesticType.put("B", "药品级生产许可_9");
		entrydomesticType.put("C", "食品级生产许可_9");
		entrydomesticType.put("D", "保健食品的提取物生产许可_9"); 
		
		
		Map<String,String> entryforeignType = new HashMap<String,String>();
		entryforeignType.put("A", "食品级_9");
		entryforeignType.put("B", "药品级_9");
		 
		
		
		Map<String,String> entryagentType = new HashMap<String,String>();
		entryagentType.put("A", "营业执照_2");
		entryagentType.put("B", "经营许可证_E");
		entryagentType.put("C", "代理证明_F");
		 
		
		
		Map<String,String> entryprocessstarType = new HashMap<String,String>();
		entryprocessstarType.put("A", "供应商书面调查表_G");
		entryprocessstarType.put("B", "物料描述表_H");
		entryprocessstarType.put("C", "3个批次样品 COA_I");
		entryprocessstarType.put("D", "样品我司检测报告_J");
		entryprocessstarType.put("E", "生产工艺流程图_K");
		entryprocessstarType.put("F", "厂家企业标准_L");
		 
		
		Map<String,String> entryprocessfitintoType = new HashMap<String,String>();
		entryprocessfitintoType.put("A", "产品稳定性报告_M");
		entryprocessfitintoType.put("B", "第三方型式检验报告_N");
		entryprocessfitintoType.put("C", "包装标签模板_O");
		entryprocessfitintoType.put("D", "转基因原料类_P");
		entryprocessfitintoType.put("E", "菌种/藻类_Q");
		entryprocessfitintoType.put("F", "微生物高风险物料_R");
		entryprocessfitintoType.put("G", "植物原料_S");
		entryprocessfitintoType.put("H", "动物原料_T");
		// update by hst 2024/07/09 动物原料拆分
		entryprocessfitintoType.put("L", "兽药残留检验报告_T");
		entryprocessfitintoType.put("M", "EDQM证书_T");
		entryprocessfitintoType.put("N", "初加工的动物原料品种鉴定证明_T");
		entryprocessfitintoType.put("O", "检验检疫证明_T");
		entryprocessfitintoType.put("I", "食品原料_U");
		entryprocessfitintoType.put("J", "体系认证文件_8");
		// update by hst 增加附件类型
		entryprocessfitintoType.put("K", "非辐照证明_V");
		// update by hst 增加附件类型
		entryprocessfitintoType.put("P", "MSDS_W");
		entryprocessfitintoType.put("Q", "文件清单_X");
		entryprocessfitintoType.put("R", "设备清单_Y");
		entryprocessfitintoType.put("S", "排污许可证_Z");

		attachFieldMap.put("yd_entrydomestic", entrydomesticType);
		attachFieldMap.put("yd_entryforeign", entryforeignType);
		attachFieldMap.put("yd_entryagent", entryagentType);
		attachFieldMap.put("yd_entryprocessstar", entryprocessstarType);
		attachFieldMap.put("yd_entryprocessfitinto", entryprocessfitintoType);
		
		return attachFieldMap;
		
	}
	/***
	 * @fun 新物料需求单据审核后， 将关联的资料补充函附件拷贝到供应商
	 * **/
	public static  void copyAtt4NewMatBill(Object newMatBillId)
	{
	  // 遍历书面审核分录的供应商+生产商去找对应的资料补充函
	  DynamicObject billInfo=BusinessDataServiceHelper.loadSingle(newMatBillId, "yd_newmatreqbill","org,yd_file1entry.yd_csup,yd_file1entry.yd_cprod");
	  DynamicObjectCollection entrys= billInfo.getDynamicObjectCollection("yd_file1entry");
	  for(DynamicObject entryInfo:entrys)
	  {
		  if (entryInfo.getDynamicObject("yd_csup") != null && entryInfo.getDynamicObject("yd_cprod") != null) {
			  QFilter infoBillFilter = QFilter.of("yd_newmatreq.id=?", new Object[] {newMatBillId});
			  infoBillFilter.and(QFilter.of("yd_supplier.id=?", new Object[] {entryInfo.getDynamicObject("yd_csup").getPkValue()}));
			  infoBillFilter.and(QFilter.of("yd_producers.id=?", new Object[] {entryInfo.getDynamicObject("yd_cprod").getPkValue()}));
			  
			  DynamicObject[]  datas=  BusinessDataServiceHelper.load("yd_supplyinformationbill", "id", 
					  infoBillFilter.toArray(),"createtime desc");
			  
			  if(datas!=null && datas.length>0)
			  {
				  copyAtt(datas[0].getPkValue());
			  } 
		  }
	  }
	}
	
	/***
	 * @fun 资料补充函附件 拷贝到供应商
	 * **/
	public static  void copyAtt4SupInfoBill(Object supInfoBillId)
	{
		DynamicObject srcInfo=BusinessDataServiceHelper.loadSingle(supInfoBillId, "yd_supplyinformationbill","yd_newmatreq");
		if(srcInfo.getDynamicObject("yd_newmatreq")!=null)
		{
			return ;
		}
		copyAtt(supInfoBillId);
	}
	
	/***
	 * @fun 
	 * **/
	private static  void copyAtt(Object supInfoBillId)
	{
		DynamicObject srcInfo=BusinessDataServiceHelper.loadSingle(supInfoBillId, "yd_supplyinformationbill"); 
		DynamicObject supInfo=srcInfo.getDynamicObject("yd_supplier");
		DynamicObject tarInfo=BusinessDataServiceHelper.loadSingle(supInfo.getPkValue(), "srm_supplier");
		DynamicObjectCollection tarEntrys=tarInfo.getDynamicObjectCollection("entry_aptitude");
		DynamicObjectCollection otherTarEntrys=tarInfo.getDynamicObjectCollection("yd_entry_aptitude");
		
		//分录标示，附件字段，附件类型字段,开始日期，结束日期
		String[][] fieldMap=new String[][]
	    {
			    {"yd_entrydomestic","yd_domesattachment","yd_domesattachmenttype","yd_domeseffectdate","yd_domesuneffectdate"},
			    {"yd_entryforeign","yd_foreigattachment","yd_foreigattachmenttype","yd_foreigeffectdate","yd_foreiguneffectdate"},
			    {"yd_entryagent","yd_agentattachment","yd_agentattachmenttype","yd_agenteffectdate","yd_agentuneffectdate"}, 
			    {"yd_entryprocessstar","yd_pstarattachment","yd_pstarattachmenttype","yd_pstareffectdate","yd_pstaruneffectdate"},
			    {"yd_entryprocessfitinto","yd_pfitienattachment","yd_pfitienattachmenttype","yd_pfitieneffectdate","yd_pfitienuneffectdate"} 
		};

		// 2022/10/13 hst 简易流程“其他”类型附件携带到单独的页签
		//判断元数据中是否含有该字段，没有则置为false，避免影响原逻辑
		boolean isSimple = isIncludeField("yd_supplyinformationbill","yd_combofield") ?
				"是".equals(srcInfo.getString("yd_combofield")) ? true : false : false;

		// update by hst 2023/11/04 记录文件名
		String supplierName = "";
		String productName = "";
		String matNum = "";
		String matName = srcInfo.getString("yd_materialname");
		DynamicObject supplierInfo = srcInfo.getDynamicObject("yd_supplier");
		if(supplierInfo != null) {
			supplierName = GeneralFormatUtils.getString(supplierInfo.get("name"));
		}
		DynamicObject producerInfo = srcInfo.getDynamicObject("yd_producers");
		if(producerInfo != null) {
			productName = GeneralFormatUtils.getString(producerInfo.get("name"));
		}
		DynamicObject material = srcInfo.getDynamicObject("yd_material");
		if(material != null) {
			matNum = GeneralFormatUtils.getString(material.get("number"));
		}

		Map<String,Map<String,String>> attachFieldMap = getAttDataMap();
		 for(int i=0;i<fieldMap.length;i++)
		 {   
			   String[] row= fieldMap[i];
			   String entryKey=row[0];
			   String attKey=row[1];
			   String attTypeKey=row[2]; 
			   String startDateKey=row[3];
			   String endDateKey=row[4];;
			   DynamicObjectCollection  entrys= srcInfo.getDynamicObjectCollection(entryKey); 
			   for(int j=0;j<entrys.size();j++)
			   {
				     DynamicObject entryInfo= entrys.get(j);
				     String entryName = entryInfo.getDynamicObjectType().getName();
				     Map<String,String> attachTypeMap = attachFieldMap.get(entryKey);//附件类型分录
				     String attTypeValue =entryInfo.getString(attTypeKey);
				     String typeName=null;
				     String tarTypeValue=null;
				     if(StringUtils.isNotBlank(attTypeValue))
				     {
				    	  String typeNames= attachTypeMap.get(attTypeValue);
				    	  if(StringUtils.isNotBlank(typeNames))
				    	    {
				    	    	 String []vs = StringUtils.split(typeNames,"_");
							     typeName=vs[0];//附件类型名称
							     tarTypeValue=vs[1];//供应商库.资质类型值
				    	    } 
				     }
				   // 2022/10/13 hst 简易流程“其他”类型附件携带到单独的页签
					 if (isSimple && StringUtils.equals("体系认证文件",typeName)) {
						 DynamicObject tarEntryInfo = otherTarEntrys.addNew();
						 tarEntryInfo.set("yd_aptitudetype", tarTypeValue);
						 tarEntryInfo.set("yd_aptitudename", typeName);
						 tarEntryInfo.set("yd_issuedate", entryInfo.getDate(startDateKey));
						 tarEntryInfo.set("yd_dateto", entryInfo.getDate(endDateKey));
						 String supType = "yd_entryforeign".equals(entryKey) ? "2" : "1";
						 tarEntryInfo.set("yd_othertype", supType);
						 AttachUtils.copyAttachField(entryInfo, attKey, tarEntryInfo, "yd_attachmentfield", true);
					 } else {
						 // update by hst 2023/11/04 记录文件名
						 String supType = "";
						 String fileName = "";
						 //文件类型-供应商名称
						 if ("yd_entryagent".equals(entryName)) {
							 fileName = supplierName;
							 supType = "1";
						 } else if ("yd_entrydomestic".equals(entryName) || "yd_entryforeign".equals(entryName)) {
							 fileName = productName;
							 supType = "yd_entryforeign".equals(entryKey) ? "2" : "3";
						 } else {
							 fileName = matNum + matName;
							 supType = "4";
						 }
						 DynamicObject tarEntryInfo = tarEntrys.addNew();
						 tarEntryInfo.set("yd_filename",fileName);
						 tarEntryInfo.set("aptitudetype", tarTypeValue);
						 tarEntryInfo.set("aptitudename", typeName);
						 tarEntryInfo.set("issuedate", entryInfo.getDate(startDateKey));
						 tarEntryInfo.set("dateto", entryInfo.getDate(endDateKey));
						 tarEntryInfo.set("yd_suptype", supType);
						 AttachUtils.copyAttachField(entryInfo, attKey, tarEntryInfo, "attachmentfield", true);
					 }
			   }
		 }
		
		 SaveServiceHelper.save(new DynamicObject[] {tarInfo});
	}

	/**
	 * 查询元数据中是否含有该字段
	 * @author: hst
	 * @createDate: 2022/10/13
	 * @param entityName
	 * @param fieldName
	 * @return
	 */
	protected static boolean isIncludeField (String entityName, String fieldName) {
		DynamicObject dynamicObject = BusinessDataServiceHelper.newDynamicObject(entityName);
		BillEntityType dynamicObjectType = (BillEntityType) dynamicObject.getDynamicObjectType();
		Map<String, IDataEntityProperty> fields = dynamicObjectType.getFields();
		for (Map.Entry<String, IDataEntityProperty> field : fields.entrySet()) {
			if (StringUtils.equals(fieldName,field.getValue().getName())) {
				return true;
			}
		}
		return false;
	}
}
