package kd.bos.tcbj.srm.admittance.helper;

import json.JSONArray;
import json.JSONObject;
import kd.bos.coderule.api.CodeRuleInfo;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.openapi.common.result.CustomApiResult;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.coderule.CodeRuleServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.constants.SumBillTempConstant;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @package（包）: kd.bos.tcbj.srm.admittance.helper.SupSumBillHelper
 * @className（类名称）: SupSumBillHelper
 * @description（类描述）: 包材合格供应商业务类
 * @author（创建人）: hst
 * @createDate（创建时间）: 2024/05/25
 * @version（版本）: v1.0
 */
public class SupSumBillHelper {

    /**
     * 临时采购申请
     * @param jsonStr
     * @return
     * @author: hst
     * @createDate: 2024/05/25
     */
    public static CustomApiResult tempPurApply (String jsonStr) {
        Map<String,String> result = new HashMap<>();

        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(jsonStr);
        } catch (Exception e) {
            return CustomApiResult.fail("srm.yd.100001","解析JSON数据异常");
        }

        JSONArray jsonArray = jsonObject.getJSONArray("datas");
        String userNo = jsonObject.getString("userNo");
        if (Objects.nonNull(jsonArray) && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                try {
                    JSONObject Object = jsonArray.getJSONObject(i);
                    String supplier = Object.getString("supplier");
                    String material = Object.getString("material");
                    String producer = Object.getString("producer");

                    if (StringUtils.isBlank(supplier)
                            || StringUtils.isBlank(material) || StringUtils.isBlank(producer)) {
                        throw new KDBizException("供应商|生产商|物料为空");
                    } else {
                        // 创建临时采购申请
                        createTempApply(userNo,supplier,producer,material);
                    }
                } catch (Exception e) {
                    result.put("第" + (i + 1) + "行",e.getMessage());
                }
            }
        }

        return CustomApiResult.success(result);
    }

    /**
     * 创建临时采购申请
     * @param supNo 供应商编码
     * @param producer 生产商
     * @param matNo 物料编码
     * @author: hst
     * @createDate: 2024/05/25
     */
    private static void createTempApply (String userNo, String supNo, String producer, String matNo) {
        // 查询是否存在未审核的临时采购申请
        QFilter qFilter = new QFilter("yd_material.number",QFilter.equals,matNo);
        qFilter = qFilter.and(new QFilter("yd_supplier.number",QFilter.equals,supNo));
        qFilter = qFilter.and(new QFilter("yd_supname",QFilter.equals,producer));
        boolean isExist = QueryServiceHelper.exists(SumBillTempConstant.MAIN_ENTITY,
                new QFilter[]{qFilter,new QFilter("yd_isclose",QFilter.equals,"0")});

        if (!isExist) {
            DynamicObject bill = BusinessDataServiceHelper.newDynamicObject(SumBillTempConstant.MAIN_ENTITY);
            bill.set(SumBillTempConstant.BILLSTATUS_FIELD,"A");
            bill.set(SumBillTempConstant.CREATETIME_FIELD,new Date());

            // 通过EAS用户工号查询苍穹用户
            QFilter userFilter = new QFilter("number",QFilter.equals,userNo);
            DynamicObject user = BusinessDataServiceHelper.loadSingle("bos_user",
                    new QFilter[]{userFilter});
            if (Objects.nonNull(user)) {
                bill.set(SumBillTempConstant.CREATOR_FIELD,user.getPkValue());
            } else {
                bill.set(SumBillTempConstant.CREATOR_FIELD, UserServiceHelper.getCurrentUserId());
            }

            // 单据编码
            CodeRuleInfo codeRule = CodeRuleServiceHelper.getCodeRule(SumBillTempConstant.MAIN_ENTITY,
                    bill, null);
            String billno = CodeRuleServiceHelper.getNumber(codeRule, bill);
            bill.set(SumBillTempConstant.BILLNO_FIELD,billno);

            // 物料
            DynamicObject material = BusinessDataServiceHelper.loadSingle("bd_material",
                    new QFilter[]{new QFilter("number",QFilter.equals,matNo)});
            if (Objects.isNull(material)) {
                throw new KDBizException("物料不存在");
            } else {
                bill.set(SumBillTempConstant.MATERIAL_FIELD,material);
            }

            // 供应商
            DynamicObject supplier = BusinessDataServiceHelper.loadSingle("bd_supplier",
                    new QFilter[]{new QFilter("number",QFilter.equals,supNo)});
            if (Objects.isNull(supplier)) {
                throw new KDBizException("供应商不存在");
            } else {
                bill.set(SumBillTempConstant.SUPPLIER_FILED,supplier);
            }

            // 合格供应商目录
            DynamicObject packBill = BusinessDataServiceHelper.loadSingle("yd_packsupsumbill",
                    new QFilter[]{qFilter});
            if (Objects.nonNull(packBill)) {
                bill.set(SumBillTempConstant.BIZNO_FIELD,packBill.getString("billno"));
                bill.set(SumBillTempConstant.BIZID_FIELD,packBill.getString("id"));
                bill.set(SumBillTempConstant.BILLTYPE_FIELD,"yd_packsupsumbill");
                bill.set(SumBillTempConstant.OLDSTATUS_FIELD,packBill.getString("yd_supstatus"));
                bill.set(SumBillTempConstant.NEWSTATUS_FIELD,"临时允许采购");
                bill.set(SumBillTempConstant.BILLTYPE_FIELD,"yd_packsupsumbill");
            } else {
                bill.set(SumBillTempConstant.BILLTYPE_FIELD,"yd_eas_purorder");
            }

            bill.set(SumBillTempConstant.EXPLAIN_FIELD,"旧版本物料采购下单");
            bill.set(SumBillTempConstant.ISCLOSE_FIELD,"0");
            bill.set(SumBillTempConstant.PRONAME_FILED,producer);

            // 保存并提交
            saveAndSubmitBill(bill);
        } else {
            throw new KDBizException("当前该供应商、生产商及物料存在未关闭的临时采购申请，无需再次发起");
        }
    }

    /**
     * 保存并提交单据
     * @param bill
     * @author: hst
     * @createDte: 2024/05/25
     */
    private static void saveAndSubmitBill (DynamicObject bill) {
        // 保存并提交
        OperationResult result = OperationServiceHelper.executeOperate("save",
                SumBillTempConstant.MAIN_ENTITY,new DynamicObject[]{bill}, OperateOption.create());
        if (result.isSuccess()) {
            result = OperationServiceHelper.executeOperate("submit",
                    SumBillTempConstant.MAIN_ENTITY,new DynamicObject[]{bill}, OperateOption.create());
            if (!result.isSuccess()) {
                throw new KDBizException(result.getMessage());
            }
        } else {
            throw new KDBizException(result.getMessage());
        }
    }
}
