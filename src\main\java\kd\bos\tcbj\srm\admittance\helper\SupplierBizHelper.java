package kd.bos.tcbj.srm.admittance.helper;
/**
 * @auditor liefeng<PERSON>u
 * @date 2022年6月13日
 * 
 */

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

public class SupplierBizHelper {

	/**
	 * 创建主数据供应商
	 * @param name 供应商名字
	 * @param number  供应商编码
	 * @param societycreditcode  统一社会信用代码
	 * @param groupNumber  供应商类别
	 * @return
	 */
	public DynamicObject createBdSupplier(String name,String number,String societycreditcode,String groupNumber,
			String creatorOrg,String type) {
		DynamicObject bdSupplierInfo = null;
		if(StringUtils.isEmpty(groupNumber))
			return bdSupplierInfo;
		bdSupplierInfo = BusinessDataServiceHelper.newDynamicObject("bd_supplier");
		//获取供应商组别数据
		DynamicObject supplierGroupInfo = new BizBillHelper().getObjByBillNumber("bd_suppliergroup", groupNumber);
		if(supplierGroupInfo == null)
			throw new KDBizException("分类编码数据在系统不存在！");
		
		if(!StringUtils.isEmpty(number))
			bdSupplierInfo.set("number",number);
		bdSupplierInfo.set("name", name);
		bdSupplierInfo.set("societycreditcode", societycreditcode);
		bdSupplierInfo.set("tx_register_no", societycreditcode);
		bdSupplierInfo.set("type", StringUtils.isEmpty(type) ? "1" : type);//伙伴类型  为空默认赋值法人企业
		bdSupplierInfo.set("group", supplierGroupInfo);//分组赋值
		DynamicObject orgInfo = new BizBillHelper().getObjByBillNumber("bos_adminorg", creatorOrg);
		if(orgInfo != null) {
			bdSupplierInfo.set("useorg",orgInfo);
			bdSupplierInfo.set("createorg", orgInfo);
		}
		List<String> bizFunctionList = new ArrayList<String>();
		bizFunctionList.add("A");
		bizFunctionList.add("B");
		bizFunctionList.add("C");
	//	bdSupplierInfo.set("bizfunction",bizFunctionList);//业务智能赋值
		DynamicObjectCollection entryColl = bdSupplierInfo.getDynamicObjectCollection("entry_groupstandard");
		if(entryColl != null) {
			DynamicObject entryInfo = new DynamicObject(entryColl.getDynamicObjectType());
			entryInfo.set("standardid", new BizBillHelper().getObjByBillNumber("bd_suppliergroupstandard", "JBFLBZ"));//分类标准
			entryInfo.set("groupid", supplierGroupInfo);
			entryColl.add(entryInfo);
		}
		bdSupplierInfo.set("status", "A");
		bdSupplierInfo.set("enable", "1");
		bdSupplierInfo.set("ctrlstrategy", "2");
		bdSupplierInfo.set("yd_needsyneas", true);  // 临时供应商需要标记为同步到EAS,yzw
		
	
		
	//		SaveServiceHelper.save(new DynamicObject[] {bdSupplierInfo});
		OperateOption option =  OperateOption.create();
		OperationResult  result= SaveServiceHelper.saveOperate("bd_supplier", new DynamicObject[] {bdSupplierInfo}, option);
		
		ABillServiceHelper.executeOperate("submit", "bd_supplier", new Object[] {bdSupplierInfo.getPkValue()},option);
		ABillServiceHelper.executeOperate("audit", "bd_supplier", new Object[] {bdSupplierInfo.getPkValue()}, option);
		
		
		return bdSupplierInfo;
	}
	
	/**
	 * 根据统一社会信用代码获取临时供应商数据
	 * @param societycreditcode 统一社会信用代码
	 * @return
	 */
	public DynamicObject getTempBdSupplier(String societycreditcode) {
		DynamicObject bdSupplier = null;
		if(StringUtils.isEmpty(societycreditcode)) {
			return bdSupplier;
		}
		DynamicObject[] bdSupplierColl = BusinessDataServiceHelper.load("bd_supplier", 
				"id,number,name,societycreditcode,enable", 
				new QFilter[] {new QFilter("societycreditcode", QCP.equals, societycreditcode).and("name", QCP.like, "%新增评估中%")}	);
		if(bdSupplierColl != null && bdSupplierColl.length > 0 && bdSupplierColl[0] != null) {
			bdSupplier = bdSupplierColl[0];
		}
		
		return bdSupplier;
	}
	
	/**
	 * 根据统一社会信用代码获取临时供应商数据
	 * @param societycreditcode 统一社会信用代码
	 * @return
	 */
	public DynamicObject getBdSupplier(String societycreditcode,String type) {
		DynamicObject bdSupplier = null;
		if(StringUtils.isEmpty(societycreditcode)) {
			return bdSupplier;
		}
		DynamicObject[] bdSupplierColl = BusinessDataServiceHelper.load("bd_supplier", 
				"id,number,name,societycreditcode,enable", 
				new QFilter[] {new QFilter("societycreditcode", QCP.equals, societycreditcode).and("name", QCP.like, "%"+type+"%")}	);
		if(bdSupplierColl != null && bdSupplierColl.length > 0 && bdSupplierColl[0] != null) {
			bdSupplier = bdSupplierColl[0];
		}
		
		return bdSupplier;
	}
	
	
	/**
	 * 根据srm供应商获取主数据供应商
	 * @param srmSupplierId srm供应商id
	 * @return
	 */
	public DynamicObject getBdSupplierBySrmSupplierId(String srmSupplierId) {
		DynamicObject bdSupplierInfo = null;
		if(StringUtils.isEmpty(srmSupplierId))
			return bdSupplierInfo;
		
		
		return bdSupplierInfo;
	}
}
