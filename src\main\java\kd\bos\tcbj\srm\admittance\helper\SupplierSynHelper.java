package kd.bos.tcbj.srm.admittance.helper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import kd.isc.iscb.service.IscFlowService;
import kd.scm.common.util.ApiConfigUtil;
import kd.scm.common.util.ApiUtil;
import kd.scm.srm.common.SrmApproveUtil;

/**
 * @auditor yanzuwei
 * @date 2022年6月13日
 * 
 */
public class SupplierSynHelper
{
	//EAS供应商禁用
	public static void disableEASSupplier(String easId)
	{
		IscFlowService srv=(IscFlowService)kd.isc.iscb.servicehelper.ServiceFactory.getService("IscFlowService");
		List parList=new ArrayList();
		parList.add(easId);
		srv.start("pur_supplier_disable_op", parList);
	}
	
	//苍穹供应商生成EAS供应商
	public static void cosmic2EASSupplier(Long cosmicId)
	{
		IscFlowService srv=(IscFlowService)kd.isc.iscb.servicehelper.ServiceFactory.getService("IscFlowService");
		List parList=new ArrayList();
		parList.add(cosmicId);
		srv.start("pur_supplier_op", parList); 
	}

}
