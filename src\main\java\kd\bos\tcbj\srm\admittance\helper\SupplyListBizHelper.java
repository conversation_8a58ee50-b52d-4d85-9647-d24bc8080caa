package kd.bos.tcbj.srm.admittance.helper;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * 货源清单业务工具类
 * @auditor liefengWu
 * @date 2022年6月14日
 * 
 */
public class SupplyListBizHelper {

	/**
	 * 根据主数据供应商id与物料id定位  货源清单数据
	 * @param bdSupplierId
	 * @param materialId
	 * @return
	 */
	public DynamicObject getSupplyList(String bdSupplierId,String materialId) {
		DynamicObject result = null;
		if(StringUtils.isEmpty(bdSupplierId)
				|| StringUtils.isEmpty(materialId)) {
			return result;
		}
		DynamicObject[] supplyListColl = BusinessDataServiceHelper.load("yd_supplylist", 
				"id,number,name,enable,yd_supplier,yd_material,yd_issyn,yd_easid,yd_supplierstatus,yd_isuseable", 
				new QFilter[] {new QFilter("yd_supplier", QCP.equals, bdSupplierId)
								.and("yd_material", QCP.equals, materialId)}	);
		if(supplyListColl != null && supplyListColl.length > 0 && supplyListColl[0] != null) {
			result = supplyListColl[0];
		}
		
		return result;
	}
	
	/**
	 * 根据物料id，主数据供应商id与组织编码创建二开资源入库单
	 * @param materialId
	 * @param bdSupplierId
	 * @param orgNumber
	 * @return
	 */
	public DynamicObject createSupplyListInfo(String materialId,String bdSupplierId,String orgNumber) {
		DynamicObject result = null;
		DynamicObject bdSupplierInfo = !StringUtils.isEmpty(bdSupplierId) ? 
				BusinessDataServiceHelper.loadSingle(bdSupplierId, "bd_supplier", "id,number,name") : null;
		DynamicObject orgInfo = !StringUtils.isEmpty(orgNumber) ? 
				new BizBillHelper().getObjByBillNumber("bos_adminorg", orgNumber) : null;
		DynamicObject materialInfo = !StringUtils.isEmpty(materialId) ?
					BusinessDataServiceHelper.loadSingle(materialId, "bd_material", "id,number,name") : null;
	
		if(bdSupplierInfo == null || orgInfo == null || materialInfo == null)
			return result;
		result = BusinessDataServiceHelper.newDynamicObject("yd_supplylist");
		
		result.set("yd_supplier", bdSupplierInfo);
		result.set("name", bdSupplierInfo.get("name"));
		result.set("number", UUID.randomUUID().toString().substring(0, 20));
		result.set("createorg", orgInfo);
		result.set("yd_material", materialInfo);
		result.set("status", "C");//审核通过
		result.set("enable", "1");
		result.set("ctrlstrategy", "5");
		result.set("yd_isuseable", "1");  // 核准状态
		result.set("yd_issyn", false);  // 未同步EAS
		QFilter stateF = new QFilter("number",QCP.equals,"05");
		DynamicObject[] supStates = BusinessDataServiceHelper.load("yd_supplierstatus", "id,name,number", stateF.toArray());
		if (supStates.length > 0) {
			result.set("yd_supplierstatus", supStates[0]);//供应商状态-合格供应商
		}
		SaveServiceHelper.save(new DynamicObject[] {result});
		
		return result;
	}
}
