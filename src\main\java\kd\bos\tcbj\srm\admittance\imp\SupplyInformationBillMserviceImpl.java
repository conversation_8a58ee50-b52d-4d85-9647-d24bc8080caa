package kd.bos.tcbj.srm.admittance.imp;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import json.JSONArray;
import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.dataentity.utils.OrmUtils;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.message.service.handler.EmailHandler;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.outbill.helper.InternalSettleServiceHelper;
import kd.bos.tcbj.im.transbill.service.ApiHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.admittance.helper.EmailBizHelper;
import kd.bos.tcbj.srm.admittance.helper.MessageCenterBizHelper;
import kd.bos.tcbj.srm.admittance.helper.PhoneBizHelper;
import kd.bos.tcbj.srm.admittance.mservice.SupplyInformationBillMservice;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.bos.workflow.engine.msg.handler.EmailServiceHandler;

/**
* @auditor yanzuwei
* @date 2022年3月15日
* 
*/
public class SupplyInformationBillMserviceImpl implements SupplyInformationBillMservice {
	
	/**
	 * 内部实例化
	 */
	private final static SupplyInformationBillMserviceImpl mServiceImpl = new SupplyInformationBillMserviceImpl();
	
	private final static SimpleDateFormat DATE_SDF =new SimpleDateFormat("yyyy-MM-dd");
	
	/**
	 * (构造函数)
	 * 
	 * @createDate  : 2022-06-07
	 * @createAuthor: liefengWu
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	public SupplyInformationBillMserviceImpl() {
		
	}
	
	/**
	 * 描述：获取实现类实例
	 */
	public static SupplyInformationBillMservice getInstance() {
		return mServiceImpl;
	}

	@Override
	public ApiResult sendSystemMsgToSupplier(String billId,String content) {
		//根据供应商字段，去定位供应商用户
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			if(StringUtils.isEmpty(billId))
				throw new KDBizException("单据为空，请核实！");
			DynamicObject info = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill",
								"id,billstatus,yd_supplier,number");
			if(info == null)
				throw new KDBizException("单据为空，请核实！");
			// 获取对应供应商数据
			DynamicObject supplierInfo = info.getDynamicObject("yd_supplier");
			String title = GeneralFormatUtils.getString(info.get("number"))+"单号操作";//主题
			content = GeneralFormatUtils.f(content, GeneralFormatUtils.getString(info.get("number")));
			if(supplierInfo != null && supplierInfo.getPkValue() != null) {
				//获取对应供应商用户数据 
				DynamicObject supplierUserInfo = new BizPartnerBizHelper()
														.getSrmSupplierUserBySrmSupplierId(supplierInfo.getPkValue().toString()); 
				if(supplierUserInfo != null) {
				/*	new EmailBizHelper().sendEmail(GeneralFormatUtils.getString(supplierUserInfo.get("email")),
							title, content);//发送邮件信息
									
*/					
				}
				
			}
			
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}
	
	
	

	@Override
	public ApiResult sendSystemMsgToPurchaser(String billId,String content) {
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			if(StringUtils.isEmpty(billId))
				throw new KDBizException("单据为空，请核实！");
			DynamicObject info = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill",
								"id,billstatus,cretor,number");
			if(info == null)
				throw new KDBizException("单据为空，请核实！");
			// 源制单人作为采购人
			DynamicObject creator = info.getDynamicObject("creator");
			String title = GeneralFormatUtils.getString(info.get("number"))+"单号操作";//内容
			content = GeneralFormatUtils.f(content, GeneralFormatUtils.getString(info.get("number")));
			if(creator != null && creator.getPkValue() != null) {
				creator = BusinessDataServiceHelper.loadSingle(creator.getPkValue().toString(), "bos_user",
						"name,phone,number,email");
				new EmailBizHelper().sendEmail(GeneralFormatUtils.getString(creator.get("email")),
																title, content);
			}
			
		} catch (Exception e) {
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	@Override
	public ApiResult sendFillInSupplyInformateToSupplier(String billId) {
		//根据供应商字段，去定位供应商用户
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			if(StringUtils.isEmpty(billId))
				throw new KDBizException("单据为空，请核实！");
			DynamicObject info = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill",
								"id,billstatus,yd_supplier,yd_supplier.id,billno");
			if(info == null)
				throw new KDBizException("单据为空，请核实！");
			// 获取对应供应商数据
			DynamicObject supplierInfo = info.getDynamicObject("yd_supplier");
			String title = "请完善"+GeneralFormatUtils.getString(info.get("billno"))+"资料补充函信息";//主题
			String content = "";
			if(supplierInfo != null && supplierInfo.getPkValue() != null) {
				//获取对应供应商用户数据 
				DynamicObject supplierUserInfo = new BizPartnerBizHelper()
														.getSrmSupplierUserBySrmSupplierId(supplierInfo.getPkValue().toString()); 
				if(supplierUserInfo != null) {
					content =GeneralFormatUtils.f("{0}:\n请填写对应的【资料补充函】{1}！",
							GeneralFormatUtils.getString(supplierUserInfo.get("user.username")),
							GeneralFormatUtils.getString(info.get("billno")));
					
					List<Long> userIds = new ArrayList<Long>();//用户id集合
				//	userIds.add( Long.valueOf(RequestContext.get().getUserId()));//暂时发送给当前用户测试
					
					userIds.add( Long.valueOf(GeneralFormatUtils.getString(supplierUserInfo.get("user.id"))));//暂时发送给供应商用户测试
					new MessageCenterBizHelper().sendMessageCenter(userIds, title, content, "填写补充函", Long.valueOf(billId), 
							 "yd_supplyinformationbill", "/ierp/ierp/index.html?formId=yd_admsupplyinformation&pkId="+billId);
					
					new EmailBizHelper().sendEmail(GeneralFormatUtils.getString(supplierUserInfo.get("user.email")),
								title, content);//发送邮件信息
					
					List<String> phoneList = new ArrayList<String>();//手机id集合
					phoneList.add(GeneralFormatUtils.getString(supplierUserInfo.get("user.phone")));
					new PhoneBizHelper().sendPhone(phoneList, content);//发送手机信息
				}
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	@Override
	public ApiResult sendRebackSupplyInformateToSupplier(String billId) {
		//根据供应商字段，去定位供应商用户
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			if(StringUtils.isEmpty(billId))
				throw new KDBizException("单据为空，请核实！");
			DynamicObject info = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill",
								"id,billstatus,yd_supplier,yd_supplier.id,billno");
			if(info == null)
				throw new KDBizException("单据为空，请核实！");
			// 获取对应供应商数据
			DynamicObject supplierInfo = info.getDynamicObject("yd_supplier");
			String title = "请完善"+GeneralFormatUtils.getString(info.get("billno"))+"资料补充函信息";//主题
			String content = "";
			if(supplierInfo != null && supplierInfo.getPkValue() != null) {
				//获取对应供应商用户数据 
				DynamicObject supplierUserInfo = new BizPartnerBizHelper()
														.getSrmSupplierUserBySrmSupplierId(supplierInfo.getPkValue().toString()); 
				if(supplierUserInfo != null) {
					content =GeneralFormatUtils.f("{0}:\n对应的【资料补充函】{1}已打回，请重新补充资料提交！！",
							GeneralFormatUtils.getString(supplierUserInfo.get("user.username")),
							GeneralFormatUtils.getString(info.get("billno")));
					
					List<Long> userIds = new ArrayList<Long>();//用户id集合
				//	userIds.add( Long.valueOf(RequestContext.get().getUserId()));//暂时发送给当前用户测试
					
					userIds.add( Long.valueOf(GeneralFormatUtils.getString(supplierUserInfo.get("user.id"))));//暂时发送给供应商用户测试
					new MessageCenterBizHelper().sendMessageCenter(userIds, title, content, "打回填写补充函", Long.valueOf(billId), 
							 "yd_supplyinformationbill", "/ierp/ierp/index.html?formId=yd_admsupplyinformation&pkId="+billId);
					
					new EmailBizHelper().sendEmail(GeneralFormatUtils.getString(supplierUserInfo.get("user.email")),
								title, content);//发送邮件信息

					List<String> phoneList = new ArrayList<String>();//手机id集合
					phoneList.add(GeneralFormatUtils.getString(supplierUserInfo.get("user.phone")));
					new PhoneBizHelper().sendPhone(phoneList, content);//发送手机信息
				
				}
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

	@Override
	public ApiResult sendAuditingSupplyInformateToPurchaser(String billId)  {
		//根据供应商字段，去定位供应商用户
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		try {
			if(StringUtils.isEmpty(billId))
				throw new KDBizException("单据为空，请核实！");
			DynamicObject info = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill",
								"id,billstatus,creator,creator.id,billno");
			if(info == null)
				throw new KDBizException("单据为空，请核实！");
			// 获取对应制单人id
			String creatorId = GeneralFormatUtils.getString(info.get("creator.id"));
			String title = "请审核"+GeneralFormatUtils.getString(info.get("billno"))+"资料补充函信息";//主题
			String content = "";
			if(!StringUtils.isEmpty(creatorId)) {
				//获取对应制单人用户数据 
				DynamicObject userInfo = BusinessDataServiceHelper.loadSingle(creatorId, "bos_user","id,phone,email,username");
				
				if(userInfo != null) {
					content =GeneralFormatUtils.f("{0}:\n对应的【资料补充函】{1}供应商已补充资料，请审核补充资料！！",
							GeneralFormatUtils.getString(userInfo.get("username")),
							GeneralFormatUtils.getString(info.get("billno")));
					
					List<Long> userIds = new ArrayList<Long>();//用户id集合
				//	userIds.add( Long.valueOf(RequestContext.get().getUserId()));//暂时发送给当前用户测试
					
					userIds.add( Long.valueOf(GeneralFormatUtils.getString(userInfo.get("id"))));
					new MessageCenterBizHelper().sendMessageCenter(userIds, title, content, "审核补充函", Long.valueOf(billId), 
							 "yd_supplyinformationbill", "/ierp/ierp/index.html?formId=yd_supplyinformationbill&pkId="+billId);
					
					new EmailBizHelper().sendEmail(GeneralFormatUtils.getString(userInfo.get("email")),
								title, content);//发送邮件信息

					List<String> phoneList = new ArrayList<String>();//手机id集合
					phoneList.add(GeneralFormatUtils.getString(userInfo.get("phone")));
					new PhoneBizHelper().sendPhone(phoneList, content);//发送手机信息					
				}
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage());
		}
		return apiResult;
	}

}
