package kd.bos.tcbj.srm.admittance.mservice;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

import kd.bos.entity.api.ApiResult;

/**
 * 描述：
 * 
* @auditor liefengWu
* @date 2022年3月15日
* 
*/
public interface SupplyInformationBillMservice {
	
	/*public ApiResult synPriceFromYXY(Set<String> billIdSet);*/
	
	/**
	 * 通过资料补充函单据信息   发送对应信息到对应供应商配置的邮箱，短信以及OA
	 * @param billId
	 * @param content
	 * @return
	 */
	public ApiResult sendSystemMsgToSupplier(String billId,String content);
	
	/**
	 * 通过补充函单据信息  发送对应信息到对应采购人配置的邮箱，手机以及OA
	 * @param billId
	 * @param content
	 * @return
	 */
	public ApiResult sendSystemMsgToPurchaser(String billId,String content);
	
	/**
	 * 根据补充函单据id  发送对应信息到对应供应商配置的邮箱，短信以及消息中心通知 填写表单
	 * @param billId
	 * @return
	 */
	public ApiResult sendFillInSupplyInformateToSupplier(String billId);
	
	/**
	 * 据补充函单据id  发送对应信息到对应供应商配置的邮箱，短信以及消息中心通知 填写表单
	 * @param billId
	 * @return
	 */
	public ApiResult sendRebackSupplyInformateToSupplier(String billId);

	/**
	 * 根据资料补充函id 发送对应信息到对应采购方（制单人）配置的邮箱，短信，消息中心以及OA待办  审批对应表单
	 * @param billId
	 * @return
	 */
	public ApiResult sendAuditingSupplyInformateToPurchaser(String billId);
}
