package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.List;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.exception.KDBizException;

/**
 * 供应商提交注册资料插件类
 * @auditor yanzuliang
 * @date 2022年11月4日
 * 
 */
public class AdmSupplierRegSubmitCusOp extends AbstractOperationServicePlugIn {
	
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		List fieldKeys = e.getFieldKeys();
		fieldKeys.add("number");
		fieldKeys.add("name");
		fieldKeys.add("entry_goods");
		fieldKeys.add("entry_goods.goodsname");
		fieldKeys.add("entry_goods.goodsmodel");
	}
	
	@Override
	public void onAddValidators(AddValidatorsEventArgs e) {
		super.onAddValidators(e);
		e.addValidator(new AdmSupplierRegSubmitValidators());
	}
	
}

/**
 * 供应商注册资料提交校验器
 * <AUTHOR>
 *
 */
class AdmSupplierRegSubmitValidators extends AbstractValidator {

	@Override
	public void validate() {
		ExtendedDataEntity[] datas = this.getDataEntities();
		for(ExtendedDataEntity dataEntity : datas) {
			DynamicObject ov=dataEntity.getDataEntity();
			String opKey = this.getOperateKey();
			if("submit".equals(opKey)) {
				DynamicObjectCollection goodsCol = ov.getDynamicObjectCollection("entry_goods");
				if (goodsCol.size() == 0) {
					throw new KDBizException("《产品与服务页签》中的产品与服务明细至少填写一行，请检查！");
				}
			}
		}
	}
	
}
