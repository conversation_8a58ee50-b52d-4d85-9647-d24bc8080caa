package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.oplugin.AdminAttachOpPlugin
 * @className AdminAttachOp
 * @author: hst
 * @createDate: 2024/07/10
 * @description: 资质类型操作插件
 * @version: v1.0
 */
public class AdminAttachOpPlugin extends AbstractOperationServicePlugIn {

    private final static String ATTLIB_NEW_OP = "attlib_new";

    private final static String ATTLIB_EX_OP = "attlib_exist";

    private final static String UNAUDIT_OP = "unaudit";

    private final static String RENAME_OP = "rename";

    private final static String DELETEATTACH_OP = "deleteattach";

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/07/10
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);

        e.getFieldKeys().add("yd_supplier");
        e.getFieldKeys().add("yd_producers");
        e.getFieldKeys().add("yd_material");
        e.getFieldKeys().add("yd_newmatreq");
        e.getFieldKeys().add("auditdate");

        e.getFieldKeys().add("yd_entrydomestic.seq");
        e.getFieldKeys().add("yd_domesattachmenttype");
        e.getFieldKeys().add("yd_domesattachment");
        e.getFieldKeys().add("yd_domeseffectdate");
        e.getFieldKeys().add("yd_domesuneffectdate");
        e.getFieldKeys().add("yd_domesattachment_re");
        e.getFieldKeys().add("yd_domesattachment_up");

        e.getFieldKeys().add("yd_entryforeign.seq");
        e.getFieldKeys().add("yd_foreigattachmenttype");
        e.getFieldKeys().add("yd_foreigattachment");
        e.getFieldKeys().add("yd_foreigeffectdate");
        e.getFieldKeys().add("yd_foreiguneffectdate");
        e.getFieldKeys().add("yd_foreigattachment_re");
        e.getFieldKeys().add("yd_foreigattachment_up");

        e.getFieldKeys().add("yd_entryagent.seq");
        e.getFieldKeys().add("yd_agentattachmenttype");
        e.getFieldKeys().add("yd_agentattachment");
        e.getFieldKeys().add("yd_agenteffectdate");
        e.getFieldKeys().add("yd_agentuneffectdate");
        e.getFieldKeys().add("yd_agentattachment_re");
        e.getFieldKeys().add("yd_agentattachment_up");

        e.getFieldKeys().add("yd_entryprocessstar.seq");
        e.getFieldKeys().add("yd_pstarattachmenttype");
        e.getFieldKeys().add("yd_pstarattachment");
        e.getFieldKeys().add("yd_pstareffectdate");
        e.getFieldKeys().add("yd_pstaruneffectdate");
        e.getFieldKeys().add("yd_pstarattachment_re");
        e.getFieldKeys().add("yd_pstarattachment_up");

        e.getFieldKeys().add("yd_entryprocessfitinto.seq");
        e.getFieldKeys().add("yd_pfitienattachmenttype");
        e.getFieldKeys().add("yd_pfitienattachment");
        e.getFieldKeys().add("yd_pfitieneffectdate");
        e.getFieldKeys().add("yd_pfitienuneffectdate");
        e.getFieldKeys().add("yd_pfitienattachment_re");
        e.getFieldKeys().add("yd_pfitienattachment_up");
    }

    /**
     * 事务开启时执行
     * @param e
     * @author: hst
     * @createDate: 2024/07/10
     */
    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        super.afterExecuteOperationTransaction(e);
        String key = e.getOperationKey();
        DynamicObject[] bills = e.getDataEntities();
        switch (key) {
            case ATTLIB_NEW_OP : {
                // 更新附件库中准入附件（新物料）
                this.addAdminAttach(bills, true);
                break;
            }
            case ATTLIB_EX_OP : {
                // 更新附件库中准入附件（已存在物料）
                this.addAdminAttach(bills, false);
                break;
            }
            case RENAME_OP : {
                // 资质重命名
                AdminAttachHelper.reNameAdminAttach(bills);
                break;
            }
            case UNAUDIT_OP : {
                // 删除资质库附件
                AdminAttachHelper.deleteAdminAttach(bills);
                break;
            }
            case DELETEATTACH_OP : {
                // 删除资质库附件
                AdminAttachHelper.deleteAdminAttach(bills);
                break;
            }
            default : {}
        }
    }

    /**
     * 更新附件库
     * @param bills
     * @param isNew 是否新物料研发更新
     * @author: hst
     * @createDate: 2024/07/22
     */
    private void addAdminAttach (DynamicObject[] bills, boolean isNew) {
        List<DynamicObject> billList = new ArrayList<>();
        for (DynamicObject bill : bills) {
            DynamicObject newMatReq = bill.getDynamicObject("yd_newmatreq");
            if (isNew && Objects.nonNull(newMatReq)) {
                billList.add(bill);
            }
            if (!isNew && Objects.isNull(newMatReq)) {
                billList.add(bill);
            }
        }

        // 更新附件库中准入附件
        AdminAttachHelper.addAdminAttach(billList.toArray(new DynamicObject[billList.size()]));
    }
}
