package kd.bos.tcbj.srm.admittance.oplugin;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.OsFeedBackHelper;

/**
 * 资质审查单操作插件类
 * 1.审核后创建非生产供应商台账记录
 * @auditor yanzuwei
 * @date 2022年8月11日
 * 
 */
public class AptitudeExamOp extends AbstractOperationServicePlugIn {
	
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		
		String opKey=e.getOperationKey();
		if(StringUtil.equalsIgnoreCase("gensupbook", opKey)) {  // 创建供应商台账记录
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0 && dynamicObjectArray[0] != null) {
				DynamicObject srcInfo = dynamicObjectArray[0];
				DynamicObject info=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(),"srm_aptitudeexam");
				DynamicObject supInfo=(DynamicObject)info.get("supplier");
				if(supInfo==null) {
					return ;
				}
				
				SimpleDateFormat dateTimeSdf =new SimpleDateFormat("yyyy-MM-dd");
				
				// 获取默认银行账户信息
				String bankName = "";String account = "";
				DynamicObjectCollection bankCol = info.getDynamicObjectCollection("entry_bank");
				for (DynamicObject bank : bankCol) {
					if (bank.getBoolean("isdefault")) {
						bankName = bank.getDynamicObject("bank").getString("name");
						account = bank.getString("account");
					}
				}
				
				// 供应商地址
				String supAddress = supInfo.getString("yd_manufacturerplace");
				// 税率
				String rate = info.getString("taxrate");
				// 结算方式
				String settle = info.getDynamicObject("settletype")==null?"":info.getDynamicObject("settletype").getString("name");
				// 最后一张现场审核记录
				DynamicObject osFeedBackBill = OsFeedBackHelper.findLastOsFeedbackBill(supInfo.getLong("id"));
				
				DynamicObjectCollection matCol = info.getDynamicObjectCollection("entryentity");
				for (DynamicObject matEn : matCol) {
					String categorytype = matEn.getString("categorytype");
					if (!"A".equalsIgnoreCase(categorytype)) {  // 非物料类型的不处理
						continue;
					}
					DynamicObject matInfo=(DynamicObject)matEn.get("material");
					if (matInfo == null) {
						continue;
					}
					DynamicObject rssInfo = findRSSBill(supInfo.getLong("id"),matInfo.getLong("id")); 
					
					if(rssInfo==null)
					{
						rssInfo = BusinessDataServiceHelper.newDynamicObject("yd_unprosupsumbill");
						rssInfo.set("yd_supplier", supInfo);
						rssInfo.set("yd_supname", supInfo.getString("name"));
						rssInfo.set("yd_material", matInfo); 
						rssInfo.set("yd_matno", matInfo.getString("number")); 
						rssInfo.set("yd_version", 0);  
//						rssInfo.set("org", info.get("org"));
						rssInfo.set("billstatus","A");
						//rssInfo.set("billno",info.get("billno")); 
						//rssInfo.set("yd_address", null);
					}
					
					rssInfo.set("yd_matprop", "2");  // 物料属性
					rssInfo.set("yd_supaddress", supAddress);  // 供应商地址
					rssInfo.set("yd_taxrate", rate);  // 税率
					if (StringUtils.isNotEmpty(bankName)) {
						rssInfo.set("yd_bankname", bankName);  // 开户银行信息
					}
					if (StringUtils.isNotEmpty(account)) {
						rssInfo.set("yd_recaccount", account);  // 收款银行帐号
					}
					rssInfo.set("yd_paytype", settle);  // 付款方式
					rssInfo.set("yd_supstatus", "1");  // 供应商状态
					if (osFeedBackBill !=null) {
						rssInfo.set("yd_potaccessdate", osFeedBackBill.getDate("yd_auditdate"));  // 现场审核时间
						rssInfo.set("yd_accessgoal", osFeedBackBill.get("yd_auditscore"));  // 审核分数
					}
					
					int version=(Integer)rssInfo.get("yd_version");
					rssInfo.set("yd_version", version+1);
					rssInfo.set("modifytime", new Date());
					rssInfo.set("modifier", RequestContext.get().getCurrUserId()); 
					SaveServiceHelper.saveOperate("yd_unprosupsumbill",new DynamicObject[] {rssInfo},OperateOption.create());
					
				}
			}
			
		}
	}
	
	// 获取台账数据
	public static DynamicObject findRSSBill(Long supId, Long matId) {
		QFilter f = QFilter.of("yd_supplier=? and yd_material=?", new Object[] { supId, matId });
		DynamicObject[] infos = BusinessDataServiceHelper.load("yd_unprosupsumbill", "id", f.toArray());
		if (infos != null && infos.length > 0) {
			return BusinessDataServiceHelper.loadSingle(infos[0].getPkValue(), "yd_unprosupsumbill");
		}
		return null;
	}
}
