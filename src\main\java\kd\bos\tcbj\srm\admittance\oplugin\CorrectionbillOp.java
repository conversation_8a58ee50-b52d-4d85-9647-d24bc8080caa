package kd.bos.tcbj.srm.admittance.oplugin;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.AttachUtils; 

/**
 * @auditor yanzuwei
 * @date 2022年7月4日
 * 
 */
public class CorrectionbillOp  extends AbstractOperationServicePlugIn
{
	public void onPreparePropertys(PreparePropertysEventArgs e) 
	{
	    e.getFieldKeys().add("yd_osfeedback"); 
	    e.getFieldKeys().add("yd_att2");  
	}
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
	    super.afterExecuteOperationTransaction(e);
	    DynamicObject[] infos=e.getDataEntities();
	    String key=e.getOperationKey();
	    if("reversesave_att".equals(key))
	    {//附件反写
	    	for(DynamicObject tarInfo:infos)
	    	{
	    		DynamicObject srcInfo=tarInfo.getDynamicObject("yd_osfeedback");
	    		if(srcInfo==null)
	    		{
	    			continue;
	    		}
	    		srcInfo=BusinessDataServiceHelper.loadSingle(srcInfo.getPkValue(), "yd_osfeedbackbill"); 
	    		AttachUtils.copyAttachField(tarInfo, "yd_att2", srcInfo, "yd_correctionrespatt", true);
	    		SaveServiceHelper.save(new DynamicObject[] {srcInfo});
	    	}
	    }
	}
	 
}
