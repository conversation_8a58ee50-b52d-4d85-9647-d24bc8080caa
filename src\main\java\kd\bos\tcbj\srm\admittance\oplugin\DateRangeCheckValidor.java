package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.exception.KDException;

/**
 * @auditor cary
 * @date 2022年6月7日
 * @desc 生效日期校验
 * 
 */
public class DateRangeCheckValidor extends AbstractOperationServicePlugIn 
{
    @Override
	public void onPreparePropertys(PreparePropertysEventArgs e)
	{
	   e.getFieldKeys().add("yd_entrydomestic.yd_domeseffectdate"); 
	   e.getFieldKeys().add("yd_entrydomestic.yd_domesuneffectdate"); 
	   e.getFieldKeys().add("yd_entrydomestic.yd_domesattachmenttype"); 
	   
	   e.getFieldKeys().add("yd_entryforeign.yd_foreigeffectdate"); 
	   e.getFieldKeys().add("yd_entryforeign.yd_foreiguneffectdate"); 
	   e.getFieldKeys().add("yd_entryforeign.yd_foreigattachmenttype"); 
	   
	   e.getFieldKeys().add("yd_entryagent.yd_agenteffectdate"); 
	   e.getFieldKeys().add("yd_entryagent.yd_agentuneffectdate"); 
	   e.getFieldKeys().add("yd_entryagent.yd_agentattachmenttype"); 
	   
	   e.getFieldKeys().add("yd_entryprocessstar.yd_pstareffectdate"); 
	   e.getFieldKeys().add("yd_entryprocessstar.yd_pstaruneffectdate"); 
	   e.getFieldKeys().add("yd_entryprocessstar.yd_pstarattachmenttype"); 
	   
	   
	   e.getFieldKeys().add("yd_entryprocessfitinto.yd_pfitieneffectdate"); 
	   e.getFieldKeys().add("yd_entryprocessfitinto.yd_pfitienuneffectdate"); 
	   e.getFieldKeys().add("yd_entryprocessfitinto.yd_pfitienattachmenttype"); 
	   
	   
	}
 
	@Override
	public void onAddValidators(AddValidatorsEventArgs e) 
	{
	   e.addValidator(new DateRaneCheckValidate());
	}

}

class DateRaneCheckValidate extends AbstractValidator
{
	
    void checkRange(DynamicObject ov,String entryKey,String entryName,String[] dateFields)
    throws KDException
	{
		
    	DynamicObjectCollection entrys=ov.getDynamicObjectCollection(entryKey);
		for(int i=0;i<entrys.size();i++)
		{
			DynamicObject  entryInfo=entrys.get(i);
			Date startDate=entryInfo.getDate(dateFields[0]);
			Date endDate=entryInfo.getDate(dateFields[1]);
			String type = entryInfo.getString(dateFields[2]) == null ? "" : entryInfo.getString(dateFields[2]);
			switch (entryKey) {
				case "yd_entrydomestic" : {
					//厂家资质-国内生产商,校验所有附件日期必填 -hst 2024/12/11
					if(startDate == null || endDate == null) {
						throw new KDException(entryName+",第"+(i+1)+"行:附件日期必须填写");
					}
					break;
				}
				case "yd_entryforeign" : {
					//厂家资质-国外生产商,校验供应商书面调查表日期必填 -hst 2024/12/11
					if (StringUtils.equals(type,"C")) {
						if (startDate == null || endDate == null) {
							throw new KDException(entryName + ",第" + (i + 1) + "行:附件日期必须填写");
						}
					}
					break;
				}
				case "yd_entryagent" : {
					//厂家资质-代理商,校验所有附件日期必填 -hst 2022/10/11
					if(startDate == null || endDate == null) {
						throw new KDException(entryName+",第"+(i+1)+"行:附件日期必须填写");
					}
					break;
				}
				case "yd_entryprocessstar" : {
					//物料信息-流程发起阶段必须资料,校验供应商书面调查表、物料描述表类型分录附件日期必填 -hst 2022/10/11
					if (StringUtils.equals(type,"B")) {
						if(startDate == null || endDate == null) {
							throw new KDException(entryName+",第"+(i+1)+"行:" + (StringUtils.equals(type,"A") ? "供应商书面调查表" : "物料描述表")
									+ "类附件日期必须填写");
						}
					}
					break;
				}
				case "yd_entryprocessfitinto" : {
					//物料信息-流程批准纳入前必须资料，校验第三方型式检验报告类型附件日期必填 -hst 2022/10/11
					if (StringUtils.equals(type,"B")) {
						if(startDate == null || endDate == null) {
							throw new KDException(entryName+",第"+(i+1)+"行:第三方型式检验报告类附件日期必须填写");
						}
					}
					break;
				}
			}
			if(startDate!=null&&endDate!=null)
			{
				if(startDate.after(endDate))
				{
					 throw new KDException(entryName+",第"+(i+1)+"行:失效日期必须晚于生效日期"); 
				}
				
			}
		}
		
		
	}
	
	@Override
	public void validate() 
	{
		
		ExtendedDataEntity[] datas = this.getDataEntities();
		for(ExtendedDataEntity dataEntity : datas)
		{
			
			DynamicObject ov=dataEntity.getDataEntity();
			 
			try 
			{
			 checkRange(ov,"yd_entrydomestic","国内生产商分录",new String[] {"yd_domeseffectdate","yd_domesuneffectdate","yd_domesattachmenttype"});
			 checkRange(ov,"yd_entryforeign","国外生产商分录",new String[] {"yd_foreigeffectdate","yd_foreiguneffectdate","yd_foreigattachmenttype"});
			 checkRange(ov,"yd_entryagent","代理商分录",new String[] {"yd_agenteffectdate","yd_agentuneffectdate","yd_agentattachmenttype"});
			 checkRange(ov,"yd_entryprocessstar","流程发起资料分录",new String[] {"yd_pstareffectdate","yd_pstaruneffectdate","yd_pstarattachmenttype"});
			 checkRange(ov,"yd_entryprocessfitinto","纳入资料分录",new String[] {"yd_pfitieneffectdate","yd_pfitienuneffectdate","yd_pfitienattachmenttype"});
			}catch(Exception e)
			{ 
			  this.addErrorMessage(dataEntity, e.getMessage()); 
			}
		 
			 
		}
	}
	 
}