package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject; 
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.botp.plugin.AbstractConvertPlugIn;
import kd.bos.entity.botp.plugin.args.AfterConvertEventArgs;
import kd.bos.entity.botp.plugin.args.BeforeBuildRowConditionEventArgs;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.servicehelper.BusinessDataServiceHelper; 
import kd.bos.tcbj.srm.admittance.helper.AttachUtils; 
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 现场审核反馈单-供应商不符合项整改单_自定义转换插件 
 * 
 */
public class Fb2CbConvertOp extends AbstractConvertPlugIn 
{
	    private String srcObjectId = ""; 
		@Override
		public void beforeBuildRowCondition(BeforeBuildRowConditionEventArgs e) 
		{
			super.beforeBuildRowCondition(e);
			Long[] srcIds = e.getSelectedRows().stream().map(ListSelectedRow::getPrimaryKeyValue).toArray(Long[]::new);
			String targetEntityNumber = this.getTgtMainType().getName();
			if("yd_correctionbill".equals(targetEntityNumber)) 
			{
				for (Long srcId : srcIds) 
				{
					srcObjectId = GeneralFormatUtils.getString(srcId);
				}
			}
		}
		
	@Override
	public void afterConvert(AfterConvertEventArgs e)
	{
		super.afterConvert(e);
		 ExtendedDataEntity[] billDataEntitys = e.getTargetExtDataEntitySet().FindByEntityKey("yd_correctionbill"); 
		 for(ExtendedDataEntity billDataEntity : billDataEntitys)
		 {
			  DynamicObject tarInfo = billDataEntity.getDataEntity();
			  DynamicObject srcInfo=  BusinessDataServiceHelper.loadSingle(srcObjectId, "yd_osfeedbackbill");
			  AttachUtils.copyAttachField(srcInfo, "yd_correctionatt", tarInfo, "yd_att1",false);  
		 }
	}
}
