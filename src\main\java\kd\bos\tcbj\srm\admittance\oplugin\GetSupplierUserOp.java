package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.property.BasedataProp;
import kd.bos.entity.property.MulBasedataProp;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 获取供应商用户的统一操作方法，为了不用重写那么多遍的根据供应商带出供应商用户，做一个单独的带出用户，配置在工作流中
 * 涉及单据：资料补充函、现场审核通知单、不符合项整改单、质量反馈单
 * 需要确保供应商字段yd_supplier、供应商用户字段yd_supplieruser（废弃），yd_mulsupuser才能用
 * @auditor yanzuwei
 * @date 2022年8月10日
 * 
 */
public class GetSupplierUserOp extends AbstractOperationServicePlugIn {
	
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		String opKey = e.getOperationKey();
		DynamicObject[] bills = e.getDataEntities();
		for (DynamicObject bill : bills) {
			String billName = bill.getDataEntityType().getName();
			DynamicObject info=BusinessDataServiceHelper.loadSingle(bill.getPkValue(), billName);
			DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
//			DynamicObject bdUser = new BizPartnerBizHelper().getBdUserBySrmSupplierId(GeneralFormatUtils.getString(supInfo.getPkValue()));
//			info.set("yd_supplieruser", bdUser);
			
			DynamicObjectCollection bdUsers = new BizPartnerBizHelper().getMulBdUserBySrmSupplierId(GeneralFormatUtils.getString(supInfo.getPkValue()));
			if (bdUsers == null || bdUsers.size() == 0) return;
//			info.set("yd_mulsupuser", bdUsers);
			MainEntityType mainType = (MainEntityType) info.getDataEntityType();
			// 多选基础资料：集合属性，与物理表格对应
			MulBasedataProp mulBasedataProp = (MulBasedataProp) mainType.findProperty("yd_mulsupuser");

			// 下级子实体：集合元素对应的实体对象，其下包含了普通基础资料属性对象
			DynamicObjectType subEntityType = (DynamicObjectType) mulBasedataProp.getDynamicCollectionItemPropertyType();

			// 基础资料：存储每条基础资料的数据包，属性名是个常量fbasedataid
			BasedataProp basedataProp = (BasedataProp) subEntityType.getProperties().get("fbasedataid");

			// 如下代码演示如何取值：
			// 取多选基础资料字段值：DynamicObjectCollection 类型
			DynamicObjectCollection rows = (DynamicObjectCollection) mulBasedataProp.getValue(info);
			rows.clear();  // 每次需要重置

//			// 逐条取基础资料及其主键
//			for (DynamicObject row : rows) {
//				DynamicObject basedataObj = (DynamicObject) basedataProp.getValue(row);
//				Long basedataId = (Long) basedataObj.getPkValue();
//			}

			for (DynamicObject bdUser : bdUsers) {
				// 如下代码，演示给多选基础资料字段，追加一条基础资料
				// 根据主键，自行到数据库读取数据包
				DynamicObject newBasedataObj = BusinessDataServiceHelper.loadSingleFromCache(bdUser.getLong("id"),basedataProp.getDynamicComplexPropertyType());
				
				// 给多选基础资料数据集合，增加新行，在新行上填写基础资料
				DynamicObject newRow = new DynamicObject(subEntityType);
				rows.add(newRow);
				basedataProp.setValue(newRow, newBasedataObj); // 使用这个方法，系统会同步填写主键属性值
			}
			
			SaveServiceHelper.save(new DynamicObject[] {info});
		}
	}
	
}
