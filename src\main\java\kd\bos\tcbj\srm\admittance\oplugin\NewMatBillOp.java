package kd.bos.tcbj.srm.admittance.oplugin;

import com.kingdee.bos.ctrl.common.util.StringUtil;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.NewMatReverseHelper;
import kd.bos.tcbj.srm.admittance.helper.SrmSupplierAttHelper;

import java.util.Date;
import java.util.Objects;

/**
 * @auditor yanzuwei
 * @date 2022年8月3日
 * 
 */
public class NewMatBillOp extends AbstractOperationServicePlugIn {
	/**
	 *
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 * 
	 * @createDate  : 2022年8月3日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 加载单据数据包事件
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_onsiteauditsum");
		e.getFieldKeys().add("yd_auditentry");
		e.getFieldKeys().add("yd_auditentry.yd_onsiteaudit");
		e.getFieldKeys().add("yd_material");
		e.getFieldKeys().add("yd_csup");
		e.getFieldKeys().add("yd_cprod");
		e.getFieldKeys().add("yd_tighten");
		e.getFieldKeys().add("yd_stridate");
		e.getFieldKeys().add("yd_version");
		e.getFieldKeys().add("yd_admitmaterial");
		e.getFieldKeys().add("yd_admitsup");
		e.getFieldKeys().add("yd_admitpro");
	}
	
	/**
	 * 
	 * 描述：开启事务，未提交数据库事件
	 * 1.审批过程中汇总分录字段yd_onsiteaudit的值到表头的yd_onsiteauditsum字段
	 * 
	 * @createDate  : 2022年8月3日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 开启事务，未提交数据库事件
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		String opKey=e.getOperationKey();
		if(StringUtil.equals("onsiteauditsum", opKey)) {  // 汇总现场审核判断值
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject billInfo : objs) {
				String sumaudit = "";
				DynamicObjectCollection auditCol = billInfo.getDynamicObjectCollection("yd_auditentry");
				for (DynamicObject auditEn : auditCol) {
					String onSiteAudit = auditEn.getString("yd_onsiteaudit");
					sumaudit = sumaudit + ";" + onSiteAudit+";";
				}
				billInfo.set("yd_onsiteauditsum", sumaudit);
			}
			SaveServiceHelper.save(objs);
		} else if ("getstridate".equals(opKey)) {
			// update by hst 2024/08/26 记录加严起始时间
			Date date = new Date();
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject billInfo : objs) {
				DynamicObjectCollection scoreCol = billInfo.getDynamicObjectCollection("yd_scoreentry");
				for (DynamicObject scoreEn : scoreCol) {
					Date striDate = scoreEn.getDate("yd_stridate");
					boolean isTighten = scoreEn.getBoolean("yd_tighten");
					if (isTighten && Objects.isNull(striDate)) {
						scoreEn.set("yd_stridate", date);
					}
				}
			}
			SaveServiceHelper.save(objs);
		}
	}
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
		super.afterExecuteOperationTransaction(e);	
		String opKey=e.getOperationKey();
		if("srmsup_attsave".equals(opKey))
		{//供应商库附件反写
			DynamicObject[] bills= e.getDataEntities();
			for (DynamicObject billInfo : bills) 
			{
			   SrmSupplierAttHelper.copyAtt4NewMatBill(billInfo.getPkValue());
			} 
		} else if("createsupplier".equals(opKey))
		{//创建供应商主数据
			DynamicObject[] bills= e.getDataEntities();
			for (DynamicObject billInfo : bills) 
			{
			   NewMatReverseHelper.createSupplier(billInfo.getPkValue());
			}
		}
	}
	
}
