package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper; 

/**
 * 研发准入单工作流支持
 * 1、研发判断满足要求的供应商才能留下，其余删除
 * 2、小试反馈后，只保留小试通过的供应商，其余删除
 * @auditor yanzuwei
 * @date 2022年7月4日
 * 
 */
public class NewMatBillUpdateEntryOp  extends AbstractOperationServicePlugIn
{ 
	
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
		 super.afterExecuteOperationTransaction(e); 
		 String op = e.getOperationKey(); 
		 DynamicObject[] bills = e.getDataEntities();
			for (DynamicObject bill : bills) 
			{
			     bill= BusinessDataServiceHelper.loadSingle(bill.getPkValue(), "yd_newmatreqbill");
			     if("filtersup_req".equals(op))
				 {//物料需求过滤供应商
			    	 filterSupEntryByMatReq(bill); 
				 }else if("filtersup_test".equals(op))
				 {//中试物料筛选过滤供应商
					 filterSupEntryByTest(bill);
				 } 
				 SaveServiceHelper.save(new DynamicObject[] {bill});
			}
	}
	
	
	void filterSupEntryByMatReq(DynamicObject bill)
	{//物料需求过滤供应商
		DynamicObjectCollection supEntrys=bill.getDynamicObjectCollection("entryentity");
		Set supIdSet=new HashSet();
		for(int i=0;i<supEntrys.size();i++)
		{
			DynamicObject entry=supEntrys.get(i);
			if(entry.getBoolean("yd_ismatchmat"))
			{
				String supName = entry.getString("yd_recomsup");
				String supId = entry.getDynamicObject("yd_sup")==null?"":entry.getDynamicObject("yd_sup").getString("id");
				supIdSet.add(supName + supId); 
			}
		}
		String[][] cols=new String[][] {
			{"yd_materialentry","yd_asup","yd_anewsupname"},
			{"yd_matanalyentry","yd_bsup","yd_bnewsupname"},
			{"yd_stryentry","yd_strysup","yd_strynewsupname"},
			{"yd_file1entry","yd_csup","yd_cnewsupname"},
			{"yd_file2entry","yd_dsup","yd_dnewsupname"},
			{"yd_scoreentry","yd_esup","yd_enewsupname"},
			{"yd_auditentry","yd_fsup","yd_fnewsupname"}, 
			{"yd_hsupentry","yd_hsup","yd_hnewsupname"},  
			{"yd_gsupentry","yd_gsup","yd_gnewsupname"},
			// update by hst 2023/11/04 新增补充性文件分录
			{"yd_entryentity","yd_ssup","yd_snewsupname"},
			// update by hst 2024/11/01 新增准入结论分录
			{"yd_admitentity","yd_admitsup","yd_admitproname"},
			// update by hst 2024/11/01 新增中试反馈分录
			{"yd_pilotentry","yd_pilotsup","yd_pilotname"}
		};
		
		 for(int i=0;i<cols.length;i++)
		 {
			 String entryKey=cols[i][0];
			 String supKey=cols[i][1];
			 String supNameKey=cols[i][2];
			 DynamicObjectCollection entrys= bill.getDynamicObjectCollection(entryKey);
			 Iterator<DynamicObject> it = entrys.iterator();
			 List<DynamicObject> deletes = new ArrayList<>();
			 while(it.hasNext())
			 {
				 DynamicObject entry= it.next();
				 String supName = entry.getString(supNameKey);
				 String supId = entry.getDynamicObject(supKey)==null?"":entry.getDynamicObject(supKey).getPkValue().toString();
				 if(!supIdSet.contains(supName+supId))
				 {
					 deletes.add(entry);
				 } 
			 }

			 for (DynamicObject entry : deletes) {
				 entrys.remove(entry);
			 }
		 } 
		
	}
	
	void filterSupEntryByTest(DynamicObject bill)
	{//中试物料筛选过滤供应商
		DynamicObjectCollection supEntrys=bill.getDynamicObjectCollection("yd_stryentry");
		Set supIdSet=new HashSet();
		for(int i=0;i<supEntrys.size();i++)
		{
			DynamicObject entry=supEntrys.get(i);
			String result=entry.getString("yd_stresult");
//			if(!"3".equals(result) && entry.getDynamicObject("yd_strysup")!=null)
			if(!"3".equals(result))
			{
				String supName = entry.getString("yd_strynewsupname");
				String supId = entry.getDynamicObject("yd_strysup")==null?"":entry.getDynamicObject("yd_strysup").getString("id");
				supIdSet.add(supName + supId); 
			}
		}
		String[][] cols=new String[][] 
		{ 
		    {"yd_file1entry","yd_csup","yd_cnewsupname"},
			{"yd_file2entry","yd_dsup","yd_dnewsupname"},
			{"yd_scoreentry","yd_esup","yd_enewsupname"},
			{"yd_auditentry","yd_fsup","yd_fnewsupname"}, 
			{"yd_hsupentry","yd_hsup","yd_hnewsupname"},  
			{"yd_gsupentry","yd_gsup","yd_gnewsupname"},
			// update by hst 2023/11/04 新增补充性文件分录
			{"yd_entryentity","yd_ssup","yd_snewsupname"},
			// update by hst 2024/11/01 新增准入结论分录
			{"yd_admitentity","yd_admitsup","yd_admitproname"},
			// update by hst 2024/11/01 新增中试反馈分录
			{"yd_pilotentry","yd_pilotsup","yd_pilotname"}
		};
		
		 for(int i=0;i<cols.length;i++)
		 {
			 String entryKey=cols[i][0];
			 String supKey=cols[i][1];
			 String supNameKey=cols[i][2];
			 DynamicObjectCollection entrys= bill.getDynamicObjectCollection(entryKey);
			 Iterator<DynamicObject> it=entrys.iterator();
			 List<DynamicObject> deletes = new ArrayList<>();
			 while(it.hasNext())
			 {
				 DynamicObject entry= it.next();
				 if(entry.getDynamicObject(supKey)!=null)
				 {
				 } 
				 String supName = entry.getString(supNameKey);
//				 Object supId= entry.getDynamicObject(supKey).getPkValue();
				 String supId = entry.getDynamicObject(supKey)==null?"":entry.getDynamicObject(supKey).getPkValue().toString();
				 if(!supIdSet.contains(supName+supId))
				 {
					 deletes.add(entry);
				 }
			 }

			 for (DynamicObject entry : deletes) {
				 entrys.remove(entry);
			 }
		 } 
	}
	
}
