package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper; 

/**
 * @auditor yanzuwei
 * @date 2022年7月4日
 * 
 */
public class NewMatBillUpdateOsEntryOp  extends AbstractOperationServicePlugIn
{ 
	
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
		 super.afterExecuteOperationTransaction(e);
		 DynamicObject[] bills = e.getDataEntities();
			for (DynamicObject bill : bills) 
			{
			    bill= BusinessDataServiceHelper.loadSingle(bill.getPkValue(),"yd_newmatreqbill","yd_hsupentry.yd_hsup");
				DynamicObjectCollection  entrys= bill.getDynamicObjectCollection("yd_hsupentry");
				if(entrys==null||entrys.size()==0)
				{
					continue;
				}
				
				for(int i=0;i<entrys.size();i++)
				{
					DynamicObject  entryInfo=entrys.get(0);
					setOsEntryInfo(entryInfo); 
				} 
				SaveServiceHelper.save(new DynamicObject[] {bill});
			}
	}
	
	

	
	void setOsEntryInfo(DynamicObject entryInfo)
	{
		//yd_hsup
		DynamicObject supInfo=entryInfo.getDynamicObject("yd_hsup");
		if(supInfo==null)
		{
			return ;
		}
		 QFilter f=new QFilter("supplier.id","=",supInfo.getPkValue());
		 f.and(new QFilter("billstatus","=","C"));
		 
		 DynamicObjectCollection  rs=QueryServiceHelper.query("srm_sceneexam", "scenescore,sceneresult,auditdate", f.toArray(),"auditdate desc");
         if(rs!=null&&rs.size()>0)
         {
        	 DynamicObject row= rs.get(0);
        	 entryInfo.set("osauditscore",row.getBigDecimal("scenescore"));
        	 String sceneresult=row.getString("sceneresult");
        	 String osresult="2";
        	 if("1".equals(sceneresult))
        	 {
        		 osresult="1";
        	 }
        	 entryInfo.set("osauditcon",osresult);
        	 entryInfo.set("osauditdate",row.getDate("auditdate")); 
         }
		
		
	}
}
