package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.RSSBillUpdateHelper;
import kd.bos.workflow.api.AgentExecution;
import kd.bos.workflow.api.WorkflowElement;
import kd.bos.workflow.engine.extitf.IWorkflowPlugin;

/**
 * @auditor yanzuwei
 * @desc 更新 台账节点状态(工作流)_新物料需求及供应商评价(供应商节点) 
 * "【研发准入单】审批通过后，根据供应商+物料名称查询台帐并根据“准入结论
”分录的“是否加入研发供应商目录”去更新“准入节点”，如果字段值为“是”，则更新为“45-研发合格供应商”，如果字段值为“否”，则更新为“50-研发新增不通过”"

 * @date 2022年7月27日
 * 
 */
public class NewMatBillWfPlugin implements IWorkflowPlugin {

	String getSupDir(DynamicObject info, DynamicObject supInfo, DynamicObject proInfo) {
		String version = info.getString("yd_version");
		if ("1".equals(version)) {
			DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_admitentity");
			if (entrys != null && entrys.size() > 0) {
				for (int i = 0; i < entrys.size(); i++) {
					DynamicObject entry = entrys.get(i);
					DynamicObject sInfo = entry.getDynamicObject("yd_admitsup");
					DynamicObject pInfo = entry.getDynamicObject("yd_admitpro");
					if (sInfo != null && pInfo != null
							&& sInfo.getLong("id") == supInfo.getLong("id")
							&& pInfo.getLong("id") == proInfo.getLong("id")) {
						return entry.getString("yd_isqualify");
					}
				}
			}
		} else {
			DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_gsupentry");
			if (entrys != null && entrys.size() > 0) {
				for (int i = 0; i < entrys.size(); i++) {
					DynamicObject entry = entrys.get(i);
					DynamicObject sInfo = entry.getDynamicObject("yd_gsup");
					DynamicObject pInfo = entry.getDynamicObject("yd_gprod");
					if (sInfo != null && pInfo != null
							&& sInfo.getLong("id") == supInfo.getLong("id")
							&& pInfo.getLong("id") == proInfo.getLong("id")) {
						return entry.getString("yd_supdir");
					}
				}
			}
		}
		return null;
	}

	String getSupTrial(DynamicObject info, DynamicObject supInfo, DynamicObject proInfo) {
		String version = info.getString("yd_version");
		if ("1".equals(version)) {
			DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_pilotentry");
			if (entrys != null && entrys.size() > 0) {
				for (int i = 0; i < entrys.size(); i++) {
					DynamicObject entry = entrys.get(i);
					DynamicObject sInfo = entry.getDynamicObject("yd_pilotsup");
					DynamicObject pInfo = entry.getDynamicObject("yd_pilotpro");
					if (sInfo != null && pInfo != null
							&& sInfo.getLong("id") == supInfo.getLong("id")
							&& pInfo.getLong("id") == proInfo.getLong("id")) {
						return entry.getString("yd_isadmitmt");
					}
				}
			}
		} else {
			DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_gsupentry");
			if (entrys != null && entrys.size() > 0) {
				for (int i = 0; i < entrys.size(); i++) {
					DynamicObject entry = entrys.get(i);
					DynamicObject sInfo = entry.getDynamicObject("yd_gsup");
					DynamicObject pInfo = entry.getDynamicObject("yd_gprod");
					if (sInfo != null && pInfo != null
							&& sInfo.getLong("id") == supInfo.getLong("id")
							&& pInfo.getLong("id") == proInfo.getLong("id")) {
						return entry.getString("yd_ismt");
					}
				}
			}
		}
		return null;
	}

	Date getSupDate(DynamicObject info, DynamicObject supInfo, DynamicObject proInfo) {
		DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_hsupentry");
		if (entrys != null && entrys.size() > 0) {
			for (int i = 0; i < entrys.size(); i++) {
				DynamicObject entry = entrys.get(i);
				DynamicObject sInfo = entry.getDynamicObject("yd_hsup");
				DynamicObject pInfo = entry.getDynamicObject("yd_hprod");
				if (sInfo != null && pInfo != null
						&& sInfo.getLong("id") == supInfo.getLong("id")
						&& pInfo.getLong("id") == proInfo.getLong("id")) {
					return entry.getDate("yd_osauditdate");
				}
			}
		}

		return null;
	}


	/**
	 * @fun 单据获取台账记录
	 **/
	public List getRSSInfos(String billName, Long billId, String nodeName) {
		List rssInfoList = new ArrayList();
		if ("yd_newmatreqbill".equals(billName)) {//新物料需求及供应商评价单
			String[] numbers = StringUtils.split(nodeName, "-");
			if (numbers != null && numbers.length > 0) {
				String nodeNum = numbers[0];

				DynamicObject info = BusinessDataServiceHelper.loadSingle(billId, billName);
				String matName = info.getString("yd_materialname");

				if ("20".equals(nodeNum)) {
					DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_file1entry");

					for (int i = 0; i < entrys.size(); i++) {
						DynamicObject entry = entrys.get(i);
						DynamicObject supInfo = entry.getDynamicObject("yd_csup");
						DynamicObject prodInfo = entry.getDynamicObject("yd_cprod");
						if (supInfo != null && prodInfo != null) {
							DynamicObject rss = RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),
									prodInfo.getLong("id"), matName);

							if (Objects.nonNull(rss)) {
								rssInfoList.add(new Object[]{rss, "20"});
							}
						}
					}
				} else if ("25".equals(nodeNum)) {
					DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_auditentry");

					for (int i = 0; i < entrys.size(); i++) {
						DynamicObject entry = entrys.get(i);
						DynamicObject supInfo = entry.getDynamicObject("yd_fsup");
						DynamicObject prodInfo = entry.getDynamicObject("yd_fprod");
						if (supInfo != null && prodInfo != null) {
							DynamicObject rss = RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),
									prodInfo.getLong("id"), matName);

							if (Objects.nonNull(rss)) {
								rssInfoList.add(new Object[]{rss, "25"});
							}
						}
					}
				} else if ("35".equals(nodeNum)) {
					DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_auditentry");

					for (int i = 0; i < entrys.size(); i++) {
						DynamicObject entry = entrys.get(i);
						DynamicObject supInfo = entry.getDynamicObject("yd_fsup");
						DynamicObject prodInfo = entry.getDynamicObject("yd_fprod");
						String isAudit = entry.getString("yd_onsiteaudit");
						if (supInfo != null && prodInfo != null) {
							if ("1".equals(isAudit)) {
								Date supAudit = getSupDate(info, supInfo, prodInfo);
								if (Objects.nonNull(supAudit)) {
									String supTrial = getSupTrial(info, supInfo, prodInfo);
									if ("1".equals(supTrial)) {
										DynamicObject rss = RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),
												prodInfo.getLong("id"), matName);
										rssInfoList.add(new Object[]{rss, "35"});
									}
								}
							} else if ("2".equals(isAudit)) {
								String supTrial = getSupTrial(info, supInfo, prodInfo);
								if ("1".equals(supTrial)) {
									DynamicObject rss = RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),
											prodInfo.getLong("id"), matName);
									rssInfoList.add(new Object[]{rss, "35"});
								}
							}
						}
					}
				} else if ("45".equals(nodeNum)) {
					if (StringUtils.isEmpty(matName)) {
						return null;
					}
					String version = info.getString("yd_version");
					if ("1".equals(version)) {
						DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_admitentity");
						if (entrys != null && entrys.size() > 0) {

							for (int i = 0; i < entrys.size(); i++) {
								DynamicObject entry = entrys.get(i);
								DynamicObject supInfo = entry.getDynamicObject("yd_admitsup");
								DynamicObject prodInfo = entry.getDynamicObject("yd_admitpro");
								String supDir = getSupDir(info, supInfo, prodInfo);
								if (supInfo != null && prodInfo != null) {
									DynamicObject rss = RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),
											prodInfo.getLong("id"), matName);
									if (rss != null) {
										if ("1".equals(supDir)) {
											rssInfoList.add(new Object[]{rss, "50"});
										} else if ("0".equals(supDir)) {
											rssInfoList.add(new Object[]{rss, "45"});
										}
									}
								}
							}
						}
					} else {
						DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_gsupentry");
						if (entrys != null && entrys.size() > 0) {

							for (int i = 0; i < entrys.size(); i++) {
								DynamicObject entry = entrys.get(i);
								DynamicObject supInfo = entry.getDynamicObject("yd_gsup");
								DynamicObject prodInfo = entry.getDynamicObject("yd_gprod");
								String supDir = getSupDir(info, supInfo, prodInfo);
								if (supInfo != null && prodInfo != null) {
									DynamicObject rss = RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),
											prodInfo.getLong("id"), matName);
									if (rss != null) {
										if ("1".equals(supDir)) {
											rssInfoList.add(new Object[]{rss, "50"});
										} else if ("2".equals(supDir)) {
											rssInfoList.add(new Object[]{rss, "45"});
										}
									}
								}
							}
						}
					}
				}
			}
		}
		return rssInfoList;
	}

	/**
	 * @fun 更新台账节点信息
	 **/
	public void updateNode(String billType, String billId, String nodeName) {
		List rssList = getRSSInfos(billType, Long.valueOf(billId), nodeName);
		for (int i = 0; i < rssList.size(); i++) {
			Object[] rssData = (Object[]) rssList.get(i);
			DynamicObject rssInfo = (DynamicObject) rssData[0];
			String nodeNum = (String) rssData[1];
			RSSBillUpdateHelper.updateSupDicByNodeNumber(nodeNum, rssInfo);
			SaveServiceHelper.save(new DynamicObject[]{rssInfo});
		}
	}

	public void notify(AgentExecution agent) {
		String billId = agent.getBusinessKey();
		String billType = agent.getEntityNumber();
		WorkflowElement<WorkflowElement> flowElement = agent.getCurrentFlowElement();
		String nodeName = flowElement.getName();//当前节点名称
		updateNode(billType, billId, nodeName);
	}
}
