package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.oplugin.NewMatReqAttachOp
 * @className NewMatReqAttachOp
 * @author: hst
 * @createDate: 2023/07/26
 * @description: 研发准入附件校验操作
 * @version: v1.0
 */
public class NewMatReqAttachOp extends AbstractOperationServicePlugIn {
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        e.getFieldKeys().add("yd_spc1");
        e.getFieldKeys().add("yd_spc2");
        e.getFieldKeys().add("yd_spc3");
        e.getFieldKeys().add("yd_spc4");
        e.getFieldKeys().add("yd_spc5");
        e.getFieldKeys().add("yd_spc6");
        e.getFieldKeys().add("yd_cnewsupname");
        e.getFieldKeys().add("yd_supple");
        e.getFieldKeys().add("yd_version");
        super.onPreparePropertys(e);
    }

    @Override
    public void onAddValidators(AddValidatorsEventArgs e) {
        super.onAddValidators(e);
        e.getValidators().add(new NewMatReqAttachValidate());
    }
}

class NewMatReqAttachValidate extends AbstractValidator {

    @Override
    public void validate() {
        ExtendedDataEntity[] extendedDataEntities = this.getDataEntities();
        for (ExtendedDataEntity extendedDataEntity : extendedDataEntities) {
            DynamicObject bill = extendedDataEntity.getDataEntity();
            String version = bill.getString("yd_version");
            if ("1".equals(version)) {
                // 校验资质文件是否已上传
                checkQualifications(extendedDataEntity);
            } else {
                // 书面审核上传资料
                checkWrittenReviewAttachments(extendedDataEntity);
                // 补充性文件适用时是否上传文件
                checkSupplementaryAttachments(extendedDataEntity);
                // 涉及某些因素时需上传附件
                checkInvolvementAttachments(extendedDataEntity);
            }
        }
    }

    /**
     * 校验资质文件是否已上传
     * @param extendedDataEntity
     * @author: hst
     * @createDate: 2024/10/23
     */
    private void checkQualifications(ExtendedDataEntity extendedDataEntity) {
        DynamicObject bill = extendedDataEntity.getDataEntity();
        Map<String,Map<String,String>> mapping = new HashMap<>();

        Map<String, String> fileMap = new HashMap<>();
        fileMap.put("A","供应商书面调查表");
        fileMap.put("B","物料描述表");
        fileMap.put("C","样品 COA");
        fileMap.put("D","样品我司检测报告");
        fileMap.put("E","生产工艺流程图");
        fileMap.put("F","厂家企业标准");
        mapping.put("yd_entryprocessstar&yd_pstarattachmenttype", fileMap);

        fileMap = new HashMap<>();
        fileMap.put("B","第三方型式检验报告");
        fileMap.put("C","包装标签模板");
        fileMap.put("K","非辐照证明");
        mapping.put("yd_entryprocessfitinto&yd_pfitienattachmenttype", fileMap);

        DynamicObjectCollection mainEntries = bill.getDynamicObjectCollection("yd_file1entry");

        int index = 1;
        for (DynamicObject mainEntry : mainEntries) {
            String supName = mainEntry.getString("yd_csup.name");
            String proName = mainEntry.getString("yd_cprod.name");

            for (Map.Entry<String,Map<String,String>> entryInfo : mapping.entrySet()) {
                String subEntryName = entryInfo.getKey().split("&")[0];
                String subFieldName = entryInfo.getKey().split("&")[1];

                Set<String> typeSet = mainEntry.getDynamicObjectCollection(subEntryName).stream()
                        .map(subEntry -> subEntry.getString(subFieldName)).collect(Collectors.toSet());

                StringBuffer errMsg = new StringBuffer();
                Map<String, String> fileInfo = entryInfo.getValue();
                for (Map.Entry<String, String> file : fileInfo.entrySet()) {
                    if (!typeSet.contains(file.getKey())) {
                        errMsg.append(file.getValue()).append("、");
                    }
                }

                if (errMsg.length() > 0) {
                    this.addErrorMessage(extendedDataEntity,
                            "书面审核需提供资料第" + index + "行，请上传供应商：" +
                                    (StringUtils.isNotBlank(supName) ? supName : "")
                                    + "，生产商：" + (StringUtils.isNotBlank(proName) ? proName : "")
                                    + " 的 " + errMsg.substring(0, errMsg.length() - 1) + "。");
                }
            }
        }
    }

    /**
     * 书面审核上传资料附件校验
     * @param extendedDataEntity
     * @author: hst
     * @createDate: 2023/07/26
     */
    private void checkWrittenReviewAttachments (ExtendedDataEntity extendedDataEntity) {
        DynamicObjectCollection fileEntries = extendedDataEntity.getDataEntity().getDynamicObjectCollection("yd_file1entry");
        Map<String,String> mapping = new HashMap<>();
        mapping.put("yd_spc1","供应商书面调查表");
        mapping.put("yd_spc2","物料描述表");
        mapping.put("yd_spc3","3个批次样品 COA");
//        mapping.put("yd_spc4","样品我司检测报告");
        mapping.put("yd_spc5","生产工艺流程图");
        mapping.put("yd_spc6","厂家企业标准");
        for (DynamicObject fileEntry : fileEntries) {
            String proName = fileEntry.getString("yd_cnewsupname");
            for (Map.Entry<String,String> entrySet : mapping.entrySet()) {
                DynamicObjectCollection attachments = fileEntry.getDynamicObjectCollection(entrySet.getKey());
                if (attachments.size() == 0) {
                    this.addErrorMessage(extendedDataEntity,
                            "请上传生产商 " + proName + " 的 " + entrySet.getValue());
                }
            }
        }
    }

    /**
     * 补充性文件附件校验
     * @param extendedDataEntity
     * @author: hst
     * @createDate: 2023/07/26
     */
    private void checkSupplementaryAttachments (ExtendedDataEntity extendedDataEntity) {
        DynamicObject bill = extendedDataEntity.getDataEntity();
        String supple = bill.getString("yd_supple");
        if ("1".equals(supple)) {
            DynamicObjectCollection fileEntries = bill.getDynamicObjectCollection("yd_entryentity");
            Map<String, String> mapping = new HashMap<>();
            mapping.put("yd_sps1", "第三方型式检验报告");
            mapping.put("yd_sps2", "包装标签模板");
            mapping.put("yd_sps3", "非辐照证明");
            for (DynamicObject fileEntry : fileEntries) {
                String proName = fileEntry.getString("yd_snewsupname");
                for (Map.Entry<String, String> entrySet : mapping.entrySet()) {
                    DynamicObjectCollection attachments = fileEntry.getDynamicObjectCollection(entrySet.getKey());
                    if (attachments.size() == 0) {
                        this.addErrorMessage(extendedDataEntity,
                                "请上传生产商 " + proName + " 的 " + entrySet.getValue());
                    }
                }
            }
        }
    }

    /**
     * 涉及性文件附件校验
     * @param extendedDataEntity
     * @author: hst
     * @createDate: 2023/07/26
     */
    private void checkInvolvementAttachments (ExtendedDataEntity extendedDataEntity) {
        DynamicObject bill = extendedDataEntity.getDataEntity();
        DynamicObjectCollection fileEntries = bill.getDynamicObjectCollection("yd_file2entry");
        Map<String, String> mapping = new HashMap<>();
        mapping.put("yd_spd4&yd_istrans", "非转基因证明");
        mapping.put("yd_spd5&yd_isstrain", "菌种/藻类鉴定证明");
        mapping.put("yd_spd6&yd_isprobiotics", "菌株鉴定报告");
        mapping.put("yd_spd11&yd_isprobiotics", "物料一致性管理流程和方法");
        mapping.put("yd_spd12&yd_isprobiotics", "最近3次物料一致性报告");
        mapping.put("yd_spd13&yd_isprobiotics", "菌株安全性报告");
        mapping.put("yd_spd8&yd_isanimal", "兽药残留检验报告");
        mapping.put("yd_spd15&yd_isanimal", "TSE/BSE声明或EDQM证书");
        mapping.put("yd_spd14&yd_isprocess", "品种鉴定证明（或检验检疫证明）");
        mapping.put("yd_spd7&yd_isplant", "农残检验报告");
        mapping.put("yd_spd9&yd_isnewfood", "生产工艺与法规要求一致性证明公告");
        mapping.put("yd_spd16&yd_isnewfood", "工艺详细说明（如收率等）");
        mapping.put("yd_spd10&yd_isexpose", "近一年产品菌落总数，霉菌和大肠菌群检验数据或趋势分析");
        mapping.put("yd_spd18&yd_ismsds", "MSDS");
        for (DynamicObject fileEntry : fileEntries) {
            String proName = fileEntry.getString("yd_dnewsupname");
            for (Map.Entry<String, String> entrySet : mapping.entrySet()) {
                String[] fields = entrySet.getKey().split("&");
                String conditionField = fields[1];
                String attachField = fields[0];
                DynamicObjectCollection attachments = fileEntry.getDynamicObjectCollection(attachField);
                if (attachments.size() == 0 && "1".equals(bill.getString(conditionField))) {
                    this.addErrorMessage(extendedDataEntity,
                            "请上传生产商 " + proName + " 的 " + entrySet.getValue());
                }
            }
        }
    }
}
