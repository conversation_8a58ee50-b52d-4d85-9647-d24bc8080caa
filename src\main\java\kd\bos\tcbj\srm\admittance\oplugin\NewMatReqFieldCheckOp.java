package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.oplugin.NewMatReqFieldCheckOp
 * @className NewMatReqFieldCheckOp
 * @author: hst
 * @createDate: 2023/08/02
 * @description: 研发准入单流程中字段必录性校验
 * @version: v1.0
 */
public class NewMatReqFieldCheckOp extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("yd_recomsup");
        e.getFieldKeys().add("yd_scmcmd");
        e.getFieldKeys().add("yd_origin");
        e.getFieldKeys().add("yd_capacity");
        e.getFieldKeys().add("yd_model");
        e.getFieldKeys().add("yd_execstd");
        e.getFieldKeys().add("yd_prodlicense");
        e.getFieldKeys().add("yd_storagecon");
        e.getFieldKeys().add("yd_shelflife");
        e.getFieldKeys().add("yd_packing");
        e.getFieldKeys().add("yd_deliverycyc");
        e.getFieldKeys().add("yd_textareafield");
        e.getFieldKeys().add("yd_initscore");
        e.getFieldKeys().add("yd_qadoc_att1");
        e.getFieldKeys().add("yd_supple");
        e.getFieldKeys().add("yd_istrans");
        e.getFieldKeys().add("yd_isstrain");
        e.getFieldKeys().add("yd_isprobiotics");
        e.getFieldKeys().add("yd_isanimal");
        e.getFieldKeys().add("yd_isprocess");
        e.getFieldKeys().add("yd_isplant");
        e.getFieldKeys().add("yd_isnewfood");
        e.getFieldKeys().add("yd_isexpose");
        e.getFieldKeys().add("yd_csup");
        e.getFieldKeys().add("yd_cprod");
        e.getFieldKeys().add("yd_anewsupname");
    }

    @Override
    public void onAddValidators(AddValidatorsEventArgs e) {
        super.onAddValidators(e);
        e.getValidators().add(new NewMatReqFieldCheck());
    }
}

class NewMatReqFieldCheck extends AbstractValidator {
    @Override
    public void validate() {
        ExtendedDataEntity[] extendedDataEntities = this.getDataEntities();
        for (ExtendedDataEntity extendedDataEntity : extendedDataEntities) {
            // 获取供应商是否被推荐
            Map<String,Boolean> map = getProductIsRecommended(extendedDataEntity.getDataEntity());
            // 校验物料详情信息是否已录入
            checkMaterialDetails(map,extendedDataEntity);
            // 校验物书面审核资料是否已填写
            checkWrittenReviewMaterials(extendedDataEntity);
        }
    }

    /**
     * 获取供应商是否被推荐
     * @param bill
     * @return
     * @author: hst
     * @createDate: 2023/08/02
     */
    private Map<String,Boolean> getProductIsRecommended (DynamicObject bill) {
        Map<String,Boolean> temp = new HashMap<>();
        for (DynamicObject entry : bill.getDynamicObjectCollection("entryentity")) {
            String product = entry.getString("yd_recomsup");
            if (StringUtils.isNotBlank(product)) {
                temp.put(product,"1".equals(entry.getString("yd_scmcmd")) ? true : false);
            }
        }
        return temp;
    }

    /**
     * 校验物料详情信息是否已录入
     * @param map
     * @param extendedDataEntity
     * @author: hst
     * @createDate: 2023/08/02
     */
    private void checkMaterialDetails(Map<String,Boolean> map, ExtendedDataEntity extendedDataEntity) {
        DynamicObject bill = extendedDataEntity.getDataEntity();
        Map<String,String> fields = new HashMap<>();
        fields.put("yd_origin","产地");
        fields.put("yd_capacity","产能");
        fields.put("yd_model","规格型号");
        fields.put("yd_execstd","厂家执行标准");
        fields.put("yd_prodlicense","生产许可");
        fields.put("yd_storagecon","储存条件");
        fields.put("yd_shelflife","保质期");
        fields.put("yd_packing","详细包装方式");
        fields.put("yd_deliverycyc","供货周期");
        fields.put("yd_textareafield","供货风险");
//        fields.put("yd_initscore","初筛评分");
//        fields.put("yd_qadoc_att1","供应商资质文件");
        DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_materialentry");
        for (int i = 0; i < entries.size(); i++) {
            DynamicObject entry = entries.get(i);
            String product = entry.getString("yd_anewsupname");
            if (map.containsKey(product) && map.get(product)) {
                for (Map.Entry<String, String> field : fields.entrySet()) {
                    Object value = entry.get(field.getKey());
                    if (value instanceof String) {
                        if (StringUtils.isBlank((String) value)) {
                            this.addErrorMessage(extendedDataEntity, "请填写物料详细信息分录第" + (i + 1) + "行的" + field.getValue());
                        }
                    } else if (value instanceof BigDecimal) {
                        if ((new BigDecimal("0")).compareTo((BigDecimal) value) == 0) {
                            this.addErrorMessage(extendedDataEntity, "请填写物料详细信息分录第" + (i + 1) + "行的" + field.getValue());
                        }
                    } else if (value instanceof DynamicObjectCollection) {
                        if (Objects.isNull(value) || ((DynamicObjectCollection) value).size() == 0) {
                            this.addErrorMessage(extendedDataEntity, "请填写物料详细信息分录第" + (i + 1) + "行的" + field.getValue());
                        }
                    }
                }
            }
        }
    }

    /**
     * 校验物书面审核资料是否已填写
     * @param extendedDataEntity
     * @author: hst
     * @createDate: 2023/10/25
     */
    private void checkWrittenReviewMaterials(ExtendedDataEntity extendedDataEntity) {
        Map<String, String> fields = new HashMap<>();
        fields.put("yd_supple", "补充性文件是否适用");
        fields.put("yd_istrans", "可能存在转基因物料");
        fields.put("yd_isstrain", "菌种/藻类");
        fields.put("yd_isprobiotics", "益生菌原料");
        fields.put("yd_isanimal", "动物原料");
        fields.put("yd_isprocess", "初加工的动物原料");
        fields.put("yd_isplant", "植物原料");
        fields.put("yd_isnewfood", "新食品原料");
        fields.put("yd_isexpose", "产品暴露区无洁净区或有微生物显著风险");
        DynamicObject bill = extendedDataEntity.getDataEntity();
        for (Map.Entry<String, String> field : fields.entrySet()) {
            Object value = bill.get(field.getKey());
            if (Objects.isNull(value)) {
                this.addErrorMessage(extendedDataEntity, "请填写是否涉及" + field.getValue());

            }
        }
        // 校验经销商和生产商是否填写
        DynamicObjectCollection entities = bill.getDynamicObjectCollection("yd_file1entry");
        for (int i = 0; i < entities.size(); i++) {
            DynamicObject entry = entities.get(i);
            String proName = entry.getString("yd_cnewsupname");
            if (StringUtils.isBlank(proName)) {
                if (Objects.isNull(entry.get("yd_csup")) || Objects.isNull("yd_cprod")) {
                    this.addErrorMessage(extendedDataEntity, "请填写书面审核资料分录第" + (i + 1) + "行的已注册的经销商/已登记的生产商");
                }
            }

        }
    }
}