package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.botp.plugin.AbstractConvertPlugIn;
import kd.bos.entity.botp.plugin.args.AfterConvertEventArgs;
import kd.bos.entity.botp.plugin.args.BeforeBuildRowConditionEventArgs;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.NewMatReverseHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

import java.util.Objects;

/**
 * 新物料需求及供应商评价生成原辅料及供应商_自定义转换插件 
 * 
 */
public class NewMatToRawMatConvertOp extends AbstractConvertPlugIn 
{
	
   private String srcObjectId = ""; 
	@Override
	public void beforeBuildRowCondition(BeforeBuildRowConditionEventArgs e) {
		super.beforeBuildRowCondition(e);
		Long[] srcIds = e.getSelectedRows().stream().map(ListSelectedRow::getPrimaryKeyValue).toArray(Long[]::new);
		String targetEntityNumber = this.getTgtMainType().getName();
		
		if("yd_rawmatsupaccessbill".equals(targetEntityNumber)) 
		{
			for (Long srcId : srcIds) 
			{
				srcObjectId = GeneralFormatUtils.getString(srcId);
			}
		}
	}
	@Override
	public void afterConvert(AfterConvertEventArgs e) {
		super.afterConvert(e);
		ExtendedDataEntity[] billDataEntitys = e.getTargetExtDataEntitySet().FindByEntityKey("yd_rawmatsupaccessbill");
		for (ExtendedDataEntity billDataEntity : billDataEntitys) {
			DynamicObject tarInfo = billDataEntity.getDataEntity();
			DynamicObject product = tarInfo.getDynamicObject("yd_tempproducers");
			DynamicObject supplier = tarInfo.getDynamicObject("yd_supplier");
			if (Objects.nonNull(supplier) && Objects.nonNull(product)) {
				// 资料补充函
				DynamicObject supInfo = BusinessDataServiceHelper.loadSingle("yd_supinfobase"
						, new QFilter[]{new QFilter("yd_newmatreq.id", QFilter.equals, srcObjectId)
								, new QFilter("yd_supplier.id", QFilter.equals, supplier.getPkValue())
								, new QFilter("yd_producers.id", QFilter.equals, product.getPkValue())});
				tarInfo.set("yd_supinfo",supInfo);
			}
			// update by hst 2024/11/05 携带研发准入单的基础信息到原辅料准入单上
			NewMatReverseHelper.bringBaseInfo(Long.valueOf(srcObjectId), tarInfo);
			NewMatReverseHelper.copyAttDataRawMatInfo(Long.valueOf(srcObjectId), tarInfo);
			// update by hst 2023/10/30 携带研发准入单的现场审核反馈信息到原辅料准入单上
			NewMatReverseHelper.bringOsFeedBackInfo(Long.valueOf(srcObjectId), tarInfo);
			// update by hst 2023/10/30 携带研发准入单的书面评分信息到原辅料准入单上
			NewMatReverseHelper.bringScoreInfo(Long.valueOf(srcObjectId), tarInfo);
			// update by hst 2023/10/30 携带研发准入单的物料详细信息到原辅料准入单上
			NewMatReverseHelper.bringMaterialDetailInfo(Long.valueOf(srcObjectId), tarInfo);
			// update by hst 2023/10/30 携带研发准入单的等级评估信息到原辅料准入单上
			NewMatReverseHelper.bringGradeAssessInfo(Long.valueOf(srcObjectId), tarInfo);
			// update by hst 2024/05/28 携带最新版本号
			tarInfo.set("yd_version",getNewestVersion());
		}
	}

	/**
	 * 获取最新版本号
	 * @return
	 * @author: hst
	 * @createDate: 2024/05/28
	 */
	private int getNewestVersion () {
		int version = 0;
		QFilter typeFilter = new QFilter("yd_bill", QFilter.equals, "4");
		QFilter stuFilter = new QFilter("billstatus",QFilter.equals,"C");
		DynamicObjectCollection configs = QueryServiceHelper.query("yd_paramconfigure",
				"yd_versionentity.yd_version", new QFilter[]{typeFilter,stuFilter}, null);
		for (DynamicObject config : configs) {
			if (config.getInt("yd_versionentity.yd_version") > version) {
				version = config.getInt("yd_versionentity.yd_version");
			}
		}
		return version;
	}
}
