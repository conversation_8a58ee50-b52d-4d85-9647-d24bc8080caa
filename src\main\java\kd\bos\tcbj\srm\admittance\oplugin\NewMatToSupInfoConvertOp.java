package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.botp.plugin.AbstractConvertPlugIn;
import kd.bos.entity.botp.plugin.args.AfterConvertEventArgs;
import kd.bos.entity.botp.plugin.args.BeforeBuildRowConditionEventArgs;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.NewMatReverseHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 新物料需求及供应商评价-资料补充函_自定义转换插件 
 * 
 */
public class NewMatToSupInfoConvertOp extends AbstractConvertPlugIn 
{
	
	@Override
	public void afterConvert(AfterConvertEventArgs e)
	{
		super.afterConvert(e);
		 ExtendedDataEntity[] billDataEntitys = e.getTargetExtDataEntitySet().FindByEntityKey("yd_supplyinformationbill");
		 for(ExtendedDataEntity billDataEntity : billDataEntitys)
		 {
			  DynamicObject tarInfo = billDataEntity.getDataEntity();
			 // update by hst 2024/05/28 携带最新版本号
			  tarInfo.set("yd_version",getNewestVersion());
			  NewMatReverseHelper.copyAttDataSupInfo(tarInfo);
		 }
	}

	/**
	 * 获取最新版本号
	 * @return
	 * @author: hst
	 * @createDate: 2024/05/28
	 */
	private int getNewestVersion () {
		int version = 0;
		QFilter typeFilter = new QFilter("yd_bill", QFilter.equals, "4");
		QFilter stuFilter = new QFilter("billstatus",QFilter.equals,"C");
		DynamicObjectCollection configs = QueryServiceHelper.query("yd_paramconfigure",
				"yd_versionentity.yd_version", new QFilter[]{typeFilter,stuFilter}, null);
		for (DynamicObject config : configs) {
			if (config.getInt("yd_versionentity.yd_version") > version) {
				version = config.getInt("yd_versionentity.yd_version");
			}
		}
		return version;
	}
}
