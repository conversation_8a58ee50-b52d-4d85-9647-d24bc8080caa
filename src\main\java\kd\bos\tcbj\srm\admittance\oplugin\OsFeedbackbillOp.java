package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.tcbj.srm.admittance.helper.OsFeedBackHelper;

/**
 * @auditor yanzuwei
 * @date 2022年7月4日
 * 
 */
public class OsFeedbackbillOp  extends AbstractOperationServicePlugIn
{
	
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
	    super.afterExecuteOperationTransaction(e);
	    DynamicObject[] infos=e.getDataEntities();
	    String key=e.getOperationKey();
	    for(DynamicObject info:infos)
	     { 
	    	if("reverse4submit".equals(key))
	    	{//提交后来源单据反写
	    		OsFeedBackHelper.reverseSaveNewMat4Submit(info);
	    		OsFeedBackHelper.reverseSaveRowSup4Submit(info); 
	    		
	    	}else if("reverse4audit".equals(key))
	    	{//审核后来源单据反写
	    		OsFeedBackHelper.reverseSaveNewMat4Audit(info);
	    		OsFeedBackHelper.reverseSaveRowSup4Audit(info); 
	    		
	    	}else if("update_rssbill".equals(key))
	    	{
	    	   OsFeedBackHelper.updateRssInfo4Audit(info);
	    	}else if("gen_correctionbill".equals(key))
	    	{//生成供应商不符合项整改单
	    		OsFeedBackHelper.genCorrectionBill(info); 
	    	}
	     } 
	}
	 
}
