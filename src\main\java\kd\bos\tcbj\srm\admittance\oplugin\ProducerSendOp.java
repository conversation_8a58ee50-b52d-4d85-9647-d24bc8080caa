package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.ProducerHelper;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.ProducerEditPlugin
 * @className ProducerEditPlugin
 * @author: hst
 * @createDate: 2024/03/15
 * @description: 生产商基同步WMS插件
 * @version: v1.0
 */
public class ProducerSendOp extends AbstractOperationServicePlugIn {

    /** 同步WMS操作标识 **/
    private final static String SENDWMS_OP = "yd_sendwms";

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/03/15
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("number");
        e.getFieldKeys().add("name");
        e.getFieldKeys().add("yd_issyn");
    }

    /**
     * 操作执行完，事务未提交
     * @param e
     * @author: hst
     * @createDate: 2024/03/15
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        super.endOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case SENDWMS_OP : {
                this.sendProducerToWMS(bills);
                break;
            }
        }
    }

    /**
     * 同步至WMS
     * @param bills
     * @author: hst
     * @createDate: 2024/03/15
     */
    private void sendProducerToWMS(DynamicObject[] bills) {
        String errStr = "";
        for (DynamicObject bill : bills) {
            String number = bill.getString("number");
            String name = bill.getString("name");
            if (StringUtils.isBlank(number) || StringUtils.isBlank(name)) {
                errStr = errStr + bill.getString("id") + ":生产商编码或名称不能为空" + ";";
            }

            Map<String,String> param = new HashMap<>();
            param.put("Code",number);
            param.put("Name",name);
            ApiResult apiResult = new ProducerHelper().sendProducerToWMS(param);
            if (!apiResult.getSuccess()) {
                errStr = errStr + bill.getString("id") + ":" + apiResult.getMessage() + ";";
                bill.set("yd_issyn",false);
            } else {
                bill.set("yd_issyn",true);
            }
        }

        if (errStr.length() > 0) {
            throw new KDBizException(errStr);
        }

        SaveServiceHelper.save(bills);
    }
}
