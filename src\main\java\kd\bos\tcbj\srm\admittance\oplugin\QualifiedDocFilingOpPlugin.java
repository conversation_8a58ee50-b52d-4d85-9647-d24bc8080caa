package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.db.DB;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachConstant;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachMapConstant;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;
import kd.bos.tcbj.srm.admittance.helper.QualifiedDocFilingHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.admittance.vo.AdminAttachInfoVo;
import kd.bos.tcbj.srm.admittance.vo.AdminAttachSetVo;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.QualifiedDocFilingEditPlugin
 * @className QualifiedDocFilingEditPlugin
 * @author: hst
 * @createDate: 2023/10/19
 * @description: 资质文件归档操作插件
 * @version: v1.0
 */
public class QualifiedDocFilingOpPlugin extends AbstractOperationServicePlugIn {

    /**
     * 加载字段
     *
     * @param e
     * @author: hst
     * @createDate: 2023/10/19
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        String formId = this.billEntityType.getName();
        super.onPreparePropertys(e);

        if ("yd_rawmatsupaccessbill".equals(formId)) {
            e.getFieldKeys().add("yd_supplier");
            e.getFieldKeys().add("yd_tempproducers");
            e.getFieldKeys().add("yd_material");
        } else if ("yd_newmatreqbill".equals(formId)) {
            e.getFieldKeys().add("yd_gsup");
            e.getFieldKeys().add("yd_gprod");
            e.getFieldKeys().add("yd_material");
            e.getFieldKeys().add("yd_admitsup");
            e.getFieldKeys().add("yd_admitpro");
            e.getFieldKeys().add("yd_version");
            e.getFieldKeys().add("yd_admitmaterial");
            e.getFieldKeys().add("yd_csup");
            e.getFieldKeys().add("yd_cprod");
        }
        e.getFieldKeys().add("yd_domesattachmenttype");
        e.getFieldKeys().add("yd_domesattachment");
        e.getFieldKeys().add("yd_domeseffectdate");
        e.getFieldKeys().add("yd_domesuneffectdate");
        e.getFieldKeys().add("yd_domesattachment_re");
        e.getFieldKeys().add("yd_domesattachment_up");
        e.getFieldKeys().add("yd_oribillid_dome");
        e.getFieldKeys().add("yd_orientryid_dome");
        e.getFieldKeys().add("yd_domesattachment_ty");
        e.getFieldKeys().add("yd_foreigattachmenttype");
        e.getFieldKeys().add("yd_foreigattachment");
        e.getFieldKeys().add("yd_foreigeffectdate");
        e.getFieldKeys().add("yd_foreiguneffectdate");
        e.getFieldKeys().add("yd_foreigattachment_re");
        e.getFieldKeys().add("yd_foreigattachment_up");
        e.getFieldKeys().add("yd_oribillid_forei");
        e.getFieldKeys().add("yd_orientryid_forei");
        e.getFieldKeys().add("yd_foreigattachment_ty");
        e.getFieldKeys().add("yd_agentattachmenttype");
        e.getFieldKeys().add("yd_agentattachment");
        e.getFieldKeys().add("yd_agenteffectdate");
        e.getFieldKeys().add("yd_agentuneffectdate");
        e.getFieldKeys().add("yd_agentattachment_re");
        e.getFieldKeys().add("yd_agentattachment_up");
        e.getFieldKeys().add("yd_oribillid_agent");
        e.getFieldKeys().add("yd_orientryid_agent");
        e.getFieldKeys().add("yd_agentattachment_ty");
        e.getFieldKeys().add("yd_pstarattachmenttype");
        e.getFieldKeys().add("yd_pstarattachment");
        e.getFieldKeys().add("yd_pstareffectdate");
        e.getFieldKeys().add("yd_pstaruneffectdate");
        e.getFieldKeys().add("yd_pstarattachment_re");
        e.getFieldKeys().add("yd_pstarattachment_up");
        e.getFieldKeys().add("yd_oribillid_pstar");
        e.getFieldKeys().add("yd_orientryid_pstar");
        e.getFieldKeys().add("yd_pstarattachment_ty");
        e.getFieldKeys().add("yd_pfitienattachmenttype");
        e.getFieldKeys().add("yd_pfitienattachment");
        e.getFieldKeys().add("yd_pfitieneffectdate");
        e.getFieldKeys().add("yd_pfitienuneffectdate");
        e.getFieldKeys().add("yd_pfitienattachment_re");
        e.getFieldKeys().add("yd_pfitienattachment_up");
        e.getFieldKeys().add("yd_oribillid_pfiti");
        e.getFieldKeys().add("yd_orientryid_pfiti");
        e.getFieldKeys().add("yd_pfitienattachment_ty");
        e.getFieldKeys().add("yd_entrydomestic.seq");
        e.getFieldKeys().add("yd_entryforeign.seq");
        e.getFieldKeys().add("yd_entryagent.seq");
        e.getFieldKeys().add("yd_entryprocessstar.seq");
        e.getFieldKeys().add("yd_entryprocessfitinto.seq");
    }

    /**
     * 事务提交后事件
     *
     * @param e
     * @author: hst
     * @createDate: 2023/10/19
     */
    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        super.afterExecuteOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String formId = this.billEntityType.getName();

        for (DynamicObject bill : bills) {
            switch (formId) {
                case "yd_rawmatsupaccessbill": {
                    /* 资质文件归档 */
                    this.addAdminAttach(bill, bill.getString("id"), formId);
                    break;
                }
                case "yd_newmatreqbill": {
                    /* 资质文件归档 */
                    this.documentFill(bill, bill.getString("id"), formId);
                    /* 更新资料补充行物料信息 */
                    this.upAttachLibAfterNewReqAudit(bill);
                    break;
                }
                default: {
                }
            }
        }

        SaveServiceHelper.save(bills);
    }

    /**
     * 更新附件库中准入附件
     *
     * @param bill
     * @author: hst
     * @createDate: 2024/07/10
     */
    public void addAdminAttach(DynamicObject bill, String billId, String formId) {
        Map<String, AdminAttachSetVo> typeMap = getTypeMap();
        // 提取分录资质信息
        Map<String, AdminAttachInfoVo> entryDetails = QualifiedDocFilingHelper.getAdminAttachInfoVo(bill, billId, formId, typeMap);

        // 已反写的记录
        Set<String> records = new HashSet<>();
        // 需要反写的附件库
        Map<String, DynamicObject> baseDatas = new HashMap<>();
        for (Map.Entry<String, AdminAttachInfoVo> infoVo : entryDetails.entrySet()) {
            String[] keys = infoVo.getKey().split("&");
            // 基础资料字段
            String baseField = keys[0];
            // 附件库标识
            String tarEntityField = keys[1];

            DynamicObject baseData = null;
            if (!baseDatas.containsKey(baseField)) {
                DynamicObject object = getTarBaseDataValue(bill, baseField);
                if (Objects.nonNull(object)) {
                    baseData = BusinessDataServiceHelper.loadSingle(object.getString("id"), tarEntityField);
                    if (Objects.nonNull(baseData)) {
                        baseDatas.put(baseField, baseData);
                    } else {
                        throw new KDBizException(baseField + "获取不到对应基础资料信息");
                    }
                }
            } else {
                baseData = baseDatas.get(baseField);
            }
        }

        // 资质归档，保存到附件库
        QualifiedDocFilingHelper.qualificationFiling(bill, entryDetails, baseDatas);
    }

    /**
     * 获取资质类型配置信息
     *
     * @return
     * @author: hst
     * @createDate: 2024/07/10
     */
    public Map<String, AdminAttachSetVo> getTypeMap() {
        DataSet aptitudeTypes = AdminAttachHelper.getTypeMapInfo(null);
        if (aptitudeTypes.hasNext()) {
            Map<String, AdminAttachSetVo> typeMap = new HashMap<>();
            for (Row row : aptitudeTypes) {
                // 匹配字段
                String matchName = row.getString(AdminAttachMapConstant.MATCH_FIELD);
                // 附件库元数据标识
                String entityName = row.getString(AdminAttachMapConstant.ENTITYNAME_FIELD);
                // 资料补充函附件分录标识'
                String billEntryName = row.getString(AdminAttachMapConstant.BILLENTRY_FIELD);
                // 资料补充附件类型字段名
                String fieldName = row.getString(AdminAttachMapConstant.FIELDNAME_FIELD);
                // 资料补充函附件字段名
                String attachField = row.getString(AdminAttachMapConstant.ATTACHFIELD_FIELD);
                // 资料补充函附件枚举值
                String typeEnum = row.getString(AdminAttachConstant.TYPEENUM_FIELD);

                String key = matchName + "&" + entityName + "&" + billEntryName + "&" + fieldName + "&" + attachField + "&" + typeEnum;

                if (!typeMap.containsKey(key)) {
                    AdminAttachSetVo adminAttachVo = new AdminAttachSetVo();
                    // 匹配字段
                    adminAttachVo.setMatchField("yd_producers".equals(matchName) ? "yd_tempproducers" : matchName);
                    // 资质类型
                    adminAttachVo.setType(row.getString(AdminAttachMapConstant.TYPE_FIELD));
                    // 资质名称
                    adminAttachVo.setName(row.getString(AdminAttachConstant.NAME_FIELD));
                    // 发证日期字段名
                    adminAttachVo.setEffectName(row.getString(AdminAttachMapConstant.EFFECTNAME_FIELD));
                    // 有效日期字段名
                    adminAttachVo.setUnEffectName(row.getString(AdminAttachMapConstant.UNEFFECTNAME_FIELD));
                    // 附件库附件分录标识
                    adminAttachVo.setEntryName(row.getString(AdminAttachMapConstant.ENTRYNAME_FILED));
                    // 附件库分类字段名
                    adminAttachVo.setTypeName(row.getString(AdminAttachMapConstant.TYPENAME_FIELD));
                    // 附件库资质类型字段名
                    adminAttachVo.setAptitudeType(row.getString(AdminAttachMapConstant.APTITUDETYPE_FIELD));
                    // 附件库资质名称字段名
                    adminAttachVo.setAptitudeName(row.getString(AdminAttachMapConstant.APTITUDENAME_FIELD));
                    // 附件库元数据标识
                    adminAttachVo.setEntityName(row.getString(AdminAttachMapConstant.ENTITYNAME_FIELD));
                    // 附件库资质类型枚举值
                    adminAttachVo.setAptitudeEnum(row.getString(AdminAttachConstant.APTITUDEENUM_FIELD));
                    // 附件库附件字段名
                    adminAttachVo.setAttachName(row.getString(AdminAttachMapConstant.ATTACHNAME_FIELD));
                    // 附件库签发日期字段名
                    adminAttachVo.setIsSueName(row.getString(AdminAttachMapConstant.ISSUENAME_FIELD));
                    // 附件库有效日期至字段名
                    adminAttachVo.setDateToName(row.getString(AdminAttachMapConstant.DATETONAME_FIELD));
                    // 重命名取值基础资料字段
                    String reName = row.getString(AdminAttachMapConstant.RENAME_FIELD);
                    adminAttachVo.setReName("yd_producers".equals(reName) ? "yd_tempproducers" : reName);
                    // 归属基础资料字段名
                    String parentName = row.getString(AdminAttachMapConstant.PARENTDATA_FIELD);
                    adminAttachVo.setParentDataField("yd_producers".equals(parentName) ? "yd_tempproducers" : parentName);
                    // 来源单据ID字段名
                    adminAttachVo.setOriBillId(row.getString(AdminAttachMapConstant.ORIBILL_FIELD));
                    // 来源分录ID字段名
                    adminAttachVo.setOriEntryId(row.getString(AdminAttachMapConstant.ORIENTRY_FIELD));
                    // 路径名称
                    adminAttachVo.setPathName(row.getString(AdminAttachMapConstant.PATHNAME_FIELD));
                    // 路径名称
                    adminAttachVo.setPathName(row.getString(AdminAttachMapConstant.PATHNAME_FIELD));
                    // 来源单据类型
                    adminAttachVo.setBillType(row.getString(AdminAttachMapConstant.BILLTYPE_FIELD));

                    typeMap.put(key, adminAttachVo);
                }
            }
            return typeMap;
        } else {
            throw new KDBizException("未查询到资质类型配置信息，请联系管理员");
        }
    }

    /**
     * 资质文件归档
     *
     * @param bill
     * @param billId
     * @param formId
     * @author: hst
     * @createDate: 2024/07/22
     */
    public void documentFill(DynamicObject bill, String billId, String formId) {
        Map<String, AdminAttachSetVo> typeMap = QualifiedDocFilingHelper.getTypeMap();
        DynamicObjectCollection fileEntries = bill.getDynamicObjectCollection("yd_file1entry");
        for (DynamicObject fileEntry : fileEntries) {
            // 提取分录资质信息
            Map<String, AdminAttachInfoVo> entryDetails = getAdminAttachInfoVo(fileEntry, billId, formId, typeMap);

            // 需要反写的附件库
            Map<String, DynamicObject> baseDatas = new HashMap<>();
            Map<String, String> sortNames = new HashMap<>();
            Map<String, String> sortNumbers = new HashMap<>();

            for (Map.Entry<String, AdminAttachInfoVo> detail : entryDetails.entrySet()) {
                AdminAttachInfoVo infoVo = detail.getValue();
                if (Objects.nonNull(infoVo.getAttachCollection())) {
                    // 基础资料字段
                    String baseField = infoVo.getMatchField();
                    // 附件库标识
                    String tarEntityField = infoVo.getTarEntity();
                    // 重命名字段名
                    String reNameField = infoVo.getReNameField();

                    // 需反写的物料库
                    DynamicObject baseData = null;
                    if (!baseDatas.containsKey(baseField)) {

                        DynamicObject object = getTarBaseDataValue(bill, fileEntry, baseField);
                        if (Objects.nonNull(object)) {
                            baseData = BusinessDataServiceHelper.loadSingle(object.getString("id"), tarEntityField);
                            if (Objects.nonNull(baseData)) {
                                String name = baseData.getString("name");
                                String number = baseData.getString("number");
                                sortNames.put(baseField, name);
                                sortNumbers.put(baseField, number);
                                baseDatas.put(baseField, baseData);
                            } else {
                                throw new KDBizException(baseField + "获取不到对应基础资料信息");
                            }
                        }
                    }

                    // 重命名基础资料
                    DynamicObject reNameData = null;
                    if (!baseDatas.containsKey(reNameField)) {
                        DynamicObject object = getTarBaseDataValue(bill, fileEntry, reNameField);
                        if (Objects.nonNull(object)) {
                            reNameData = BusinessDataServiceHelper.loadSingle(object.getString("id"), object.getDynamicObjectType());
                            if (Objects.nonNull(reNameData)) {
                                String name = reNameData.getString("name");
                                String number = reNameData.getString("number");
                                sortNames.put(reNameField, name);
                                sortNumbers.put(reNameField, number);
                            } else {
                                throw new KDBizException(reNameField + "获取不到对应基础资料信息");
                            }
                        } else {
                            throw new KDBizException(reNameField + "不能为空");
                        }
                    }
                }

                // 资质归档，保存到附件库
                QualifiedDocFilingHelper.qualificationFiling(fileEntry, entryDetails, baseDatas);
            }
        }
    }

    /**
     * 提取分录资质信息
     *
     * @param bill
     * @param billId
     * @param formId
     * @param typeMap
     * @author: hst
     * @createDate: 2024/07/21
     */
    public Map<String, AdminAttachInfoVo> getAdminAttachInfoVo(DynamicObject bill, String billId, String formId,
                                                                      Map<String, AdminAttachSetVo> typeMap) {
        /* 基础资料标识 */
        String baseType = "yd_newmatreqbill".equals(formId)
                ? "yd_newreqbase" : "yd_rawmatsupaccessbill".equals(formId) ? "yd_rawmatsupaccessbase" : "";
        Map<String, AdminAttachInfoVo> entryDetails = new HashMap<>();

        // 先获取所有相关的分录
        Set<String> entryKeys = typeMap.keySet().stream().map(type -> {
            String[] types = type.split("&");
            return types[0] + "&" + types[1] + "&" + types[2] + "&" + types[3] + "&" + types[4];
        }).collect(Collectors.toSet());

        for (String entryKey : entryKeys) {
            String[] keys = entryKey.split("&");
            DynamicObjectCollection details = bill.getDynamicObjectCollection(entryKey.split("&")[2]);
            for (DynamicObject detail : details) {
                String type = detail.getString(keys[3]);

                AdminAttachSetVo setVo = typeMap.get(entryKey + "&" + type);

                AdminAttachInfoVo infoVo = new AdminAttachInfoVo();
                infoVo.setTarEntity(keys[1]);
                infoVo.setMatchField(setVo.getMatchField());
                infoVo.setTarEntry(setVo.getEntryName());
                infoVo.setTarSortField(setVo.getTypeName());
                infoVo.setTarSort(setVo.getType());
                infoVo.setTarAptField(setVo.getAptitudeType());
                infoVo.setTarAptSort(setVo.getAptitudeEnum());
                infoVo.setTarAptNameField(setVo.getAptitudeName());
                infoVo.setTarAptName(setVo.getName());
                infoVo.setTarAttachField(setVo.getAttachName());
                infoVo.setAttachCollection(detail.getDynamicObjectCollection(keys[4]));
                infoVo.setIsSueDateField(setVo.getIsSueName());
                infoVo.setIsSueDate(detail.getDate(setVo.getEffectName()));
                infoVo.setDateToField(setVo.getDateToName());
                infoVo.setDateTo(detail.getDate(setVo.getUnEffectName()));
                infoVo.setReNameField(setVo.getReName());
                infoVo.setPathName(setVo.getPathName());
                infoVo.setOriEntry(keys[2]);
                infoVo.setIsReNameField(keys[4] + "_re");
                infoVo.setIsAlreadField(keys[4] + "_up");
                infoVo.setOriSeq(detail.getInt("seq"));

                if ("yd_supplier".equals(setVo.getParentDataField())) {
                    infoVo.setParentDataField("yd_csup");
                } else if ("yd_producers".equals(setVo.getParentDataField())) {
                    infoVo.setParentDataField("yd_cprod");
                }

                if (0L == detail.getLong("id")) {
                    Long newId = DB.genLongId("");
                    detail.set("id", newId);
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getOriEntryId()))) {
                    infoVo.setEntryId(detail.getString(setVo.getOriEntryId()));
                } else {
                    infoVo.setEntryId(detail.getString("id"));
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getOriBillId()))) {
                    infoVo.setBillId(detail.getString(setVo.getOriBillId()));
                } else {
                    infoVo.setBillId(billId);
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getBillType()))) {
                    infoVo.setBillType(detail.getString(setVo.getBillType()));
                } else {
                    infoVo.setBillType(baseType);
                }

                entryDetails.put(entryKey + "&" + detail.getString("seq"), infoVo);
            }
        }

        return entryDetails;
    }

    /**
     * 获取实际的基础资料信息
     * @param bill
     * @param oriField
     * @author: hst
     */
    private DynamicObject getTarBaseDataValue(DynamicObject bill, String oriField) {
        if ("yd_supplier".equals(oriField)) {
            return bill.getDynamicObject("yd_supplier");
        } else if ("yd_producers".equals(oriField)) {
            return bill.getDynamicObject("yd_tempproducers");
        } else if ("yd_material".equals(oriField)) {
            return bill.getDynamicObject("yd_material");
        }
        return null;
    }

    /**
     * 获取实际的基础资料信息
     * @param bill
     * @param oriEntry
     * @param oriField
     * @author: hst
     */
    private DynamicObject getTarBaseDataValue(DynamicObject bill, DynamicObject oriEntry, String oriField) {
        long supId = oriEntry.getLong("yd_csup.id");
        long proId = oriEntry.getLong("yd_cprod.id");
        String version = bill.getString("yd_version");
        if ("1".equals(version)) {
            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_admitentity");
            for (DynamicObject entry : entries) {
                if (supId == entry.getLong("yd_admitsup.id")
                        && proId == entry.getLong("yd_admitpro.id")) {
                    if ("yd_supplier".equals(oriField)) {
                        return entry.getDynamicObject("yd_admitsup");
                    } else if ("yd_producers".equals(oriField)) {
                        return entry.getDynamicObject("yd_admitpro");
                    } else if ("yd_material".equals(oriField)) {
                        return entry.getDynamicObject("yd_admitmaterial");
                    }
                }
            }
        } else {
            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_gsupentry");
            for (DynamicObject entry : entries) {
                if (supId == entry.getLong("yd_gsup.id")
                        && proId == entry.getLong("yd_gprod.id")) {
                    if ("yd_supplier".equals(oriField)) {
                        return entry.getDynamicObject("yd_gsup");
                    } else if ("yd_producers".equals(oriField)) {
                        return entry.getDynamicObject("yd_gprod");
                    } else if ("yd_material".equals(oriField)) {
                        return bill.getDynamicObject("yd_material");
                    }
                }
            }
        }

        return null;
    }

    /**
     * 新物料需求单据审核后， 将关联的资料补充函附件拷贝到供应商
     * @param bill
     * @author: hst
     * @createDate: 2024/11/24
     */
    private void upAttachLibAfterNewReqAudit(DynamicObject bill) {
        String version = bill.getString("yd_version");

        DynamicObjectCollection entrys = null;
        if ("1".equals(bill.getString("yd_version"))) {
            entrys = bill.getDynamicObjectCollection("yd_admitentity");
        } else {
            entrys = bill.getDynamicObjectCollection("yd_gsupentry");
        }

        for (DynamicObject entryInfo : entrys) {
            DynamicObject supplier = "1".equals(version) ? entryInfo.getDynamicObject("yd_admitsup")
                    : entryInfo.getDynamicObject("yd_gsup");
            DynamicObject producer = "1".equals(version) ? entryInfo.getDynamicObject("yd_admitpro")
                    : entryInfo.getDynamicObject("yd_gprod");
            DynamicObject material = "1".equals(version) ? entryInfo.getDynamicObject("yd_admitmaterial")
                    : bill.getDynamicObject("yd_material");

            if (Objects.nonNull(supplier) && Objects.nonNull(producer)) {
                QFilter infoBillFilter = QFilter.of("yd_newmatreq.id=?", new Object[]{bill.getLong("id")});
                infoBillFilter.and(QFilter.of("yd_supplier.id=?", new Object[]{supplier.getPkValue()}));
                infoBillFilter.and(QFilter.of("yd_producers.id=?", new Object[]{producer.getPkValue()}));

                DynamicObjectCollection datas = QueryServiceHelper.query("srm_update_attach", "yd_supplyinformationbill",
                        "id", infoBillFilter.toArray(), "createtime desc");

                if (datas.size() > 0) {
                    Set<String> ids = datas.stream().map(data -> data.getString("id")).collect(Collectors.toSet());

                    DynamicObject[] supplyInfos = BusinessDataServiceHelper.load(ids.toArray(new Object[ids.size()]),
                            BusinessDataServiceHelper.newDynamicObject("yd_supplyinformationbill")
                                    .getDynamicObjectType());

                    for (DynamicObject supplyInfo : supplyInfos) {
                        supplyInfo.set("yd_material", material);
                    }

                    OperationResult result = OperationServiceHelper.executeOperate("save", "yd_supplyinformationbill",
                            supplyInfos, OperateOption.create());

                    if (!result.isSuccess()) {
                        throw new KDBizException("下游资料补充函更新附件库失败，请联系管理员。原因：" + result.getMessage());
                    }
                }
            }
        }
    }
}
