package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.workflow.WorkflowServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.ABillServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.AptitudeExamBizHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.admittance.helper.OsFeedBackHelper;
import kd.bos.tcbj.srm.admittance.helper.SupplierBizHelper;
import kd.bos.tcbj.srm.admittance.helper.SupplyListBizHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 原辅料供应商准入单  服务端自定义插件
 * @auditor liefengWu
 * @date 2022年6月11日
 * 
 */
public class RawmatsupaccessbillOp extends AbstractOperationServicePlugIn {

	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_supplier");
		e.getFieldKeys().add("yd_material");
		e.getFieldKeys().add("yd_suppliergroup");
		e.getFieldKeys().add("billno");
		e.getFieldKeys().add("org");
		e.getFieldKeys().add("billstatus");
		e.getFieldKeys().add("yd_mataccatt1");
		e.getFieldKeys().add("yd_changetype");
		
		e.getFieldKeys().add("entryentity");
		e.getFieldKeys().add("entryentity.yd_suptype");//供应商分录_供应商类型
		e.getFieldKeys().add("entryentity.yd_dealer");//供应商分录_srm供应商
		
		e.getFieldKeys().add("yd_tempproducers");
		e.getFieldKeys().add("yd_osfeedback");
		e.getFieldKeys().add("yd_hasspotaccess");
		e.getFieldKeys().add("yd_spotaccessdate");
		e.getFieldKeys().add("yd_wftitle");
		e.getFieldKeys().add("yd_spotaccessscore");
		e.getFieldKeys().add("yd_evalevel");
		// update by hst 2023/07/07 物料用途
		e.getFieldKeys().add("yd_matusestype");
		// update by hst 2023/07/07 物料用途附件
		e.getFieldKeys().add("yd_mataccatt4");
		// update by hst 2024/08/23 加严起始时间
		e.getFieldKeys().add("yd_stridate");
		// update by hst 2024/08/23 是否加严
		e.getFieldKeys().add("yd_tighten");

	}
	
	@Override
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		super.beforeExecuteOperationTransaction(e);
		String opKey = e.getOperationKey();//操作value
		if("suppliereffected".equals(opKey)) {//创建供应商
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0) {
				for(int index = 0 ; index < dynamicObjectArray.length ; index ++) {
					DynamicObject info = dynamicObjectArray[index];
					if(info == null || info.getPkValue() == null)
						continue;
					
					DynamicObjectCollection entryColl = info.getDynamicObjectCollection("entryentity");
					DynamicObject srmSupplierInfo = null;
					DynamicObject materialInfo = null;
					DynamicObject materialObj = (DynamicObject)info.get("yd_material");
					materialInfo = materialObj;
					//定位供应商类型为新的供应商数据
					for(int jIndex = 0 ; jIndex < entryColl.size() ; jIndex ++) {
						DynamicObject entryInfo = entryColl.get(jIndex);
						if(entryInfo == null 
								|| !"new".equals(GeneralFormatUtils.getString(entryInfo.get("yd_suptype")))
								)
							continue;
						srmSupplierInfo = entryInfo.getDynamicObject("yd_dealer");
						if(srmSupplierInfo != null)
							break;
					}
					if(srmSupplierInfo == null)
						throw new KDBizException("准入单未存在供应商类型为新的供应商数据，请核实！");
					Long srmSupplierId = GeneralFormatUtils.getLong(srmSupplierInfo.getPkValue());
					
					DynamicObject aptitudeExamInfo =  new AptitudeExamBizHelper().getAptitudeExamBySrmSupplierAndMaterial(GeneralFormatUtils.getString(srmSupplierId), 
							 GeneralFormatUtils.getString(materialObj.getPkValue()) , null);
					if(aptitudeExamInfo == null || aptitudeExamInfo.getPkValue() == null) {
						aptitudeExamInfo = new AptitudeExamBizHelper().getAptitudeExamBySrmSupplier(GeneralFormatUtils.getString(srmSupplierId), new QFilter("auditstatus", QCP.equals, "A"));
					}
					
					
					String auditStatus = aptitudeExamInfo != null && !StringUtils.isEmpty(aptitudeExamInfo.getString("auditstatus")) 
														? aptitudeExamInfo.getString("auditstatus") : "A";
					if("A".equals(auditStatus)) {
						DynamicObject orgInfo = (DynamicObject)info.get("org");
						String orgNumber = orgInfo != null && !StringUtils.isEmpty(orgInfo.getString("number"))? orgInfo.getString("number") :"00003"; 
						String billId = aptitudeExamInfo != null ? GeneralFormatUtils.getString(aptitudeExamInfo.getPkValue()) : null;
						String supplierGroupNumber = "waitgroup";
						if(info != null && info.get("yd_suppliergroup") != null) {
							DynamicObject suppliergroup = (DynamicObject) info.get("yd_suppliergroup");
							if(suppliergroup != null && !StringUtils.isEmpty(suppliergroup.getString("number"))) {
								supplierGroupNumber = GeneralFormatUtils.getString(suppliergroup.getString("number"));
							}
						}
						aptitudeExamInfo = new AptitudeExamBizHelper().addnewOrUpdateAptitudeExam(billId, aptitudeExamInfo, supplierGroupNumber, 
								GeneralFormatUtils.getString(srmSupplierId), 
								GeneralFormatUtils.getString(materialObj.getPkValue())
								, orgNumber, null);
						//提交审核资质审查单
						OperationResult result01 = ABillServiceHelper.executeOperate("submit", "srm_aptitudeexam", new Object[] {aptitudeExamInfo.getPkValue()}, OperateOption.create());
						if (!result01.isSuccess()) {
							// 错误摘要
							throw new KDException("单据"+info.getString("billno") + "资质审查提交失败，查看原因：" + result01.getMessage());
						}
						
						OperationResult result02 = ABillServiceHelper.executeOperate("audit", "srm_aptitudeexam", new Object[] {aptitudeExamInfo.getPkValue()}, OperateOption.create());
					
						//根据botp去查找供应商生效单
						Map<String, HashSet<Long>>  botpRelationMap = BFTrackerServiceHelper.findTargetBills("srm_aptitudeexam", new Long[]{
								GeneralFormatUtils.getLong(aptitudeExamInfo.getPkValue())
									});
						if(botpRelationMap != null && botpRelationMap.get("srm_supapprove") != null 
								&& botpRelationMap.get("srm_supapprove").size() > 0) {
							HashSet<Long> suparroveIds = botpRelationMap.get("srm_supapprove");
							Long suparroveId = null;
							for(Long value : suparroveIds) {
								suparroveId = value;
								break;
							}
							if(suparroveId != null) {//提交审核供应商生效单
								OperationResult result03 = ABillServiceHelper.executeOperate("submit", "srm_supapprove", new Object[] {suparroveId}, OperateOption.create());
								OperationResult auditSupplierEffResult = ABillServiceHelper.executeOperate("audit", "srm_supapprove", new Object[] {suparroveId}, OperateOption.create());
								if (!auditSupplierEffResult.isSuccess()) {
									// 错误摘要
									throw new KDException("单据"+info.getString("billno") + "供应商生效审核失败，查看供应商生效失败原因：" + auditSupplierEffResult.getMessage());
								}
								
								// 对EAS供应商进行核准并分配到股份组织,yzw
								OperationResult updateEasSupStatus = ABillServiceHelper.executeOperate("updateeasstatus", "srm_supapprove", new Object[] {suparroveId}, OperateOption.create());
								
							}
						}
						
					}
														
														
					/*QFilter qFilter = new QFilter("supplier", QCP.equals, srmSupplierId)
										.and("auditstatus", QCP.equals, "A");
					DynamicObject aptitudeexamInfo = BusinessDataServiceHelper.loadSingle("srm_aptitudeexam",
							"id,entryentity,entryentity.material,entryentity.category,entryentity.categorytype,group,examresult,entertype,"
							+ "isscene,issample,ismaterial,isapprove,ischgflow", new QFilter[] {qFilter});
				
					//资质审查单为暂存逻辑处理  且对单据进行提交和审核操作，系统会自动生成供应商生效单
					if(aptitudeexamInfo != null) {
						aptitudeexamInfo.set("examresult", "0");
						//引入品类分录赋值
						DynamicObjectCollection aptitudeEntryColl = aptitudeexamInfo.getDynamicObjectCollection("entryentity");
						if(aptitudeEntryColl != null && aptitudeEntryColl.size() >0) {
							DynamicObject aptitudeEntryInfo = aptitudeEntryColl.get(0);
							if(aptitudeEntryInfo != null) {
								aptitudeEntryInfo.set("material", info.get("yd_material"));
								aptitudeEntryInfo.set("categorytype", "A");
								
								if(materialObj != null &&materialObj.getPkValue() != null) {
									materialObj = BusinessDataServiceHelper.loadSingle(materialObj.getPkValue(), "bd_material");
									aptitudeEntryInfo.set("category", materialObj.get("group"));
								}
								
							}
						}
						//供应商分类赋值_先默认赋值waitgroup  待分类
						String supplierGroupNumber = "waitgroup";
						if(info != null && info.get("yd_suppliergroup") != null) {
							DynamicObject suppliergroup = (DynamicObject) info.get("yd_suppliergroup");
							if(suppliergroup != null && !StringUtils.isEmpty(suppliergroup.getString("number"))) {
								supplierGroupNumber = GeneralFormatUtils.getString(suppliergroup.getString("number"));
							}
						}
						
						DynamicObject[] supplierGroupColl = BusinessDataServiceHelper.load("bd_suppliergroup", "id,number,name", 
																			new QFilter[] {new QFilter("number", QCP.equals, supplierGroupNumber)}	);
						
						aptitudeexamInfo.set("group", supplierGroupColl.length > 0 ? supplierGroupColl[0] : null);
						
						//准入类型赋值与准入类型相关数据赋值处理
						DynamicObject[] srmBizTypeColl = BusinessDataServiceHelper.load("srm_biztype", 
								"id,number,name,isaptitude,isscene,issample,ismaterial,isapprove,ischgflow", 
								new QFilter[] {new QFilter("number", QCP.equals, "002")}	);

						if(srmBizTypeColl.length > 0) {
							DynamicObject srmBizTypeInfo = srmBizTypeColl[0];
							aptitudeexamInfo.set("entertype", srmBizTypeInfo);
							aptitudeexamInfo.set("isscene", srmBizTypeInfo.get("isscene"));
							aptitudeexamInfo.set("issample", srmBizTypeInfo.get("issample"));
							aptitudeexamInfo.set("ismaterial", srmBizTypeInfo.get("ismaterial"));
							aptitudeexamInfo.set("isapprove", srmBizTypeInfo.get("isapprove"));
							aptitudeexamInfo.set("ischgflow", srmBizTypeInfo.get("ischgflow"));
						}
						
						//保存对应资质审查单
						SaveServiceHelper.save(new DynamicObject[] {aptitudeexamInfo});
						
						//提交审核资质审查单
						OperationResult result01 = ABillServiceHelper.executeOperate("submit", "srm_aptitudeexam", new Object[] {aptitudeexamInfo.getPkValue()}, OperateOption.create());
						OperationResult result02 = ABillServiceHelper.executeOperate("audit", "srm_aptitudeexam", new Object[] {aptitudeexamInfo.getPkValue()}, OperateOption.create());
					
					
						//根据botp去查找供应商生效单
						Map<String, HashSet<Long>>  botpRelationMap = BFTrackerServiceHelper.findTargetBills("srm_aptitudeexam", new Long[]{
								GeneralFormatUtils.getLong(aptitudeexamInfo.getPkValue())
									});
						if(botpRelationMap != null && botpRelationMap.get("srm_supapprove") != null 
								&& botpRelationMap.get("srm_supapprove").size() > 0) {
							HashSet<Long> suparroveIds = botpRelationMap.get("srm_supapprove");
							Long suparroveId = null;
							for(Long value : suparroveIds) {
								suparroveId = value;
								break;
							}
							
							if(suparroveId != null) {//提交审核供应商生效单
								OperationResult result03 = ABillServiceHelper.executeOperate("submit", "srm_supapprove", new Object[] {suparroveId}, OperateOption.create());
								OperationResult auditSupplierEffResult = ABillServiceHelper.executeOperate("audit", "srm_supapprove", new Object[] {suparroveId}, OperateOption.create());
								if (!auditSupplierEffResult.isSuccess()) {
									// 错误摘要
									throw new KDException("单据"+info.getString("billno") + "供应商生效审核失败，查看供应商生效失败原因：" + auditSupplierEffResult.getMessage());
								}
							}
						}
						
					}*/
					
					//获取主数据供应商
					Long bizPartnerId = new BizPartnerBizHelper().getBizPartnerIdBySrmSupplierId(GeneralFormatUtils.getString(srmSupplierId));
					if(bizPartnerId != null) {
						DynamicObject[] bdSupplierColl = BusinessDataServiceHelper.load("bd_supplier", "id,number,name", 
								new QFilter[] {new QFilter("bizpartner", QCP.equals, bizPartnerId)}	);
						DynamicObject bdSupplier = bdSupplierColl.length > 0 ? bdSupplierColl[0] : null;
						if(bdSupplier != null && bdSupplier.getPkValue() != null
								&& materialInfo != null && materialInfo.getPkValue() != null) {
							DynamicObject supplylistObj =new SupplyListBizHelper().getSupplyList(GeneralFormatUtils.getString(bdSupplier.getPkValue()), 
														GeneralFormatUtils.getString(materialInfo.getPkValue()));
							//生成二开货源清单
							if(supplylistObj == null) {
								DynamicObject orgInfo = (DynamicObject)info.get("org");
								String orgNumber = orgInfo != null && !StringUtils.isEmpty(orgInfo.getString("number"))? orgInfo.getString("number") :"00003"; 
								new SupplyListBizHelper().createSupplyListInfo(GeneralFormatUtils.getString(materialInfo.getPkValue()),
									GeneralFormatUtils.getString(bdSupplier.getPkValue()),
									orgNumber);
							} else {
								// 如果是保存状态则赋值为核准状态并且设置为未同步EAS
								QFilter stateF = new QFilter("number",QCP.equals,"05");
								DynamicObject[] supStates = BusinessDataServiceHelper.load("yd_supplierstatus", "id,name,number", stateF.toArray());
								if (supplylistObj.getString("yd_isuseable").equalsIgnoreCase("0") || supplylistObj.get("yd_isuseable")==null) {
									supplylistObj.set("yd_issyn", false);
									supplylistObj.set("yd_isuseable", "1");
									if (supStates.length > 0) {
										supplylistObj.set("yd_supplierstatus", supStates[0]);
									}
								}
							}
						}

					}
				}
			}
			
		}else if("unauditbill".equals(opKey)) {//审核不通过
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0) {
				for(int index = 0 ; index < dynamicObjectArray.length ; index ++) {
					DynamicObject info = dynamicObjectArray[index];
					if(info == null || info.getPkValue() == null)
						continue;
					info.set("billstatus", "D");
					SaveServiceHelper.save(new DynamicObject[] {info});
					
					DynamicObjectCollection entryColl = info.getDynamicObjectCollection("entryentity");
					DynamicObject srmSupplierInfo = null;
					DynamicObject materialInfo = (DynamicObject)info.get("yd_material");
					
					//定位供应商类型为新的供应商数据
					for(int jIndex = 0 ; jIndex < entryColl.size() ; jIndex ++) {
						DynamicObject entryInfo = entryColl.get(jIndex);
						if(entryInfo == null 
								|| !"new".equals(GeneralFormatUtils.getString(entryInfo.get("yd_suptype")))
								)
							continue;
						srmSupplierInfo = entryInfo.getDynamicObject("yd_dealer");
						if(srmSupplierInfo != null)
							break;
					}
					
					//对主供应商的货源清单进行反核准操作,yzw
					Long bizPartnerId = new BizPartnerBizHelper().getBizPartnerIdBySrmSupplierId(GeneralFormatUtils.getString(srmSupplierInfo.getPkValue()));
					if(bizPartnerId != null) {
						DynamicObject[] bdSupplierColl = BusinessDataServiceHelper.load("bd_supplier", "id,number,name", 
								new QFilter[] {new QFilter("bizpartner", QCP.equals, bizPartnerId)}	);
						DynamicObject bdSupplier = bdSupplierColl.length > 0 ? bdSupplierColl[0] : null;
						if(bdSupplier != null && bdSupplier.getPkValue() != null
								&& materialInfo != null && materialInfo.getPkValue() != null) {
							// 获取货源清单
							DynamicObject supplylistObj =new SupplyListBizHelper().getSupplyList(GeneralFormatUtils.getString(bdSupplier.getPkValue()), 
									GeneralFormatUtils.getString(materialInfo.getPkValue()));
							if(supplylistObj != null) {
								// 如果是核准状态则重置为暂存状态
								QFilter stateF = new QFilter("number",QCP.equals,"07");
								DynamicObject[] supStates = BusinessDataServiceHelper.load("yd_supplierstatus", "id,name,number", stateF.toArray());
								if (supplylistObj.getString("yd_isuseable").equalsIgnoreCase("1")) {
									supplylistObj.set("yd_issyn", false);
									supplylistObj.set("yd_isuseable", "0");
									if (supStates.length > 0) {
										supplylistObj.set("yd_supplierstatus", supStates[0]);
									}
								}
							}
						}

					}
					
					//如果存在临时供应商 且对应的启用状态为启用  则进行禁用处理
					if(srmSupplierInfo != null) {
						Long srmSupplierId = GeneralFormatUtils.getLong(srmSupplierInfo.getPkValue());
						srmSupplierInfo = BusinessDataServiceHelper.loadSingle(srmSupplierId, "srm_supplier");
						String societycreditcode = GeneralFormatUtils.getString(srmSupplierInfo.get("societycreditcode"));//统一社会信用代码
						DynamicObject bdSupplierInfo = new SupplierBizHelper().getTempBdSupplier(societycreditcode);
						//如果存在临时供应商 且对应的启用状态为启用  则进行禁用处理
						if(bdSupplierInfo != null && GeneralFormatUtils.getString(bdSupplierInfo.getString("enable")).equals("1")) {
							//禁用供应商操作
							ABillServiceHelper.executeOperate("disable", "bd_supplier", new Object[] {bdSupplierInfo.getPkValue()}, OperateOption.create());
							materialInfo = (DynamicObject)info.get("yd_material");
							if(materialInfo.getPkValue() != null) {//对对应临时供应商的货源清单进行禁用处理
								DynamicObject[] supplyListColl = BusinessDataServiceHelper.load("yd_supplylist", 
										"id,number,name,enable,yd_supplier,yd_material,yd_issyn,yd_easid,yd_supplierstatus,yd_isuseable", 
										new QFilter[] {new QFilter("yd_supplier", QCP.equals, bdSupplierInfo.getPkValue()).
																and("yd_material", QCP.equals, materialInfo.getPkValue())}	);
								QFilter stateF = new QFilter("number",QCP.equals,"07");
								DynamicObject[] supStates = BusinessDataServiceHelper.load("yd_supplierstatus", "id,name,number", stateF.toArray());
								for(int jIndex = 0 ; jIndex < supplyListColl.length ; jIndex ++) {
									DynamicObject supplyListInfo = supplyListColl[index];
									// 如果是核准状态则重置为暂存状态
									if (supplyListInfo.getString("yd_isuseable").equalsIgnoreCase("1")) {
										supplyListInfo.set("yd_issyn", false);
										supplyListInfo.set("yd_isuseable", "0");
										if (supStates.length > 0) {
											supplyListInfo.set("yd_supplierstatus", supStates[0]);
										}
									}
								}
								if (supplyListColl.length > 0) {
									SaveServiceHelper.save(supplyListColl);
								}
							}
						}
					}
						
					
				}		
			}
		}else if("save".equals(opKey) || "submit".equals(opKey)
				|| "submitandnew".equals(opKey)
				) {//对新增供应商字段赋值
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0) {
				for(int index = 0 ; index < dynamicObjectArray.length ; index ++) {
					DynamicObject info = dynamicObjectArray[index];
					if(info == null || info.getPkValue() == null)
						continue;
					
					DynamicObjectCollection entryColl = info.getDynamicObjectCollection("entryentity");
					DynamicObject srmSupplierInfo = null;
					DynamicObject materialInfo = null;
					DynamicObject srmProdInfo = null;  // 生产商
					//定位供应商类型为新的供应商数据
					for(int jIndex = 0 ; jIndex < entryColl.size() ; jIndex ++) {
						DynamicObject entryInfo = entryColl.get(jIndex);
						if(entryInfo == null 
								|| !"new".equals(GeneralFormatUtils.getString(entryInfo.get("yd_suptype")))
								)
							continue;
						srmSupplierInfo = entryInfo.getDynamicObject("yd_dealer");
						srmProdInfo = entryInfo.getDynamicObject("yd_producers");
						if(srmSupplierInfo != null)
							break;
					}
					info.set("yd_supplier", srmSupplierInfo);
					info.set("yd_tempproducers", srmProdInfo);  // 生产商
				}
			}
		} else if ("getNewestSiteAudit".equalsIgnoreCase(opKey)) {  // 带出现场审核记录,yzl,20230209
			// update by hst 2023/01/17 新增准入单时，如果无需再次现场审核时，需要抓取旧现场审核反馈单信息
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0) {
				for(int index = 0 ; index < dynamicObjectArray.length ; index ++) {
					DynamicObject info = dynamicObjectArray[index];
					if(info == null || info.getPkValue() == null)
						continue;
					// 如果是需要现场审核的才需要带出
					if (StringUtils.isNoneBlank(info.getString("yd_hasspotaccess")) && "0".equalsIgnoreCase(info.getString("yd_hasspotaccess"))) {
						OsFeedBackHelper.getNewestSiteAudit(info);
					}
				}
			}
		} else if ("getstridate".equals(opKey)) {
			DynamicObject[] bills = e.getDataEntities();
			Date date = new Date();
			for (DynamicObject bill : bills) {
				Date striDate = bill.getDate("yd_stridate");
				boolean isTighten = bill.getBoolean("yd_tighten");
				if (isTighten && Objects.isNull(striDate)) {
					bill.set("yd_stridate", date);
				}
			}
		}
	}
	
	/**
	 * 操作校验
	 */
	@Override
	public void onAddValidators(AddValidatorsEventArgs e) {
		super.onAddValidators(e);
		e.addValidator(new RawmatsupaccessBillDelCheckValidate());//添加准入单删除校验器
	}
	
	/**
	@Override
	public void endOperationTransaction(EndOperationTransactionArgs e) {
		super.endOperationTransaction(e);
		String opKey=e.getOperationKey();
		//如果为采购方提交  则发送短信通知供应商
		boolean isBizParnter = new BizPartnerBizHelper().isBizPartnerCurrentUser();
		if(StringUtil.equalsIgnoreCase("submit", opKey) || 
				StringUtil.equalsIgnoreCase("submitandnew", opKey) ) {
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0) {
				for(int index = 0 ; index < dynamicObjectArray.length ; index ++ ) {
					DynamicObject info = dynamicObjectArray[index];
					if(info != null && info.getPkValue() != null) {
						String billId = GeneralFormatUtils.getString(info.getPkValue());
						if(!StringUtils.isEmpty(billId)) {
							String billNumber = info.getString("billno");//单据号赋值
							DynamicObject baseInfo = BusinessDataServiceHelper.loadSingle(billId, "yd_rawmatsupaccessbase");
							baseInfo.set("number", billNumber);
							baseInfo.set("status", "C");
							baseInfo.set("enable", "1");
							SaveServiceHelper.save(new DynamicObject[] {baseInfo});
						}
					}
				}
			}
		}		
	}
	*/
}


/**
 * 原辅料准入单逻辑校验器
 * <AUTHOR>
 * 20220611
 */
class RawmatsupaccessBillDelCheckValidate extends AbstractValidator
{
	@Override
	public void validate() 
	{
		
		ExtendedDataEntity[] datas = this.getDataEntities();
		String opKey = this.getOperateKey();
		
		for(ExtendedDataEntity dataEntity : datas)
		{
			DynamicObject ov=dataEntity.getDataEntity();
			String billno = (String)ov.getString("billno");
			String billstatus = (String)ov.getString("billstatus");
		
			if("delete".equals(opKey)){
				if(StringUtils.equals("C", billstatus))
				{
				  this.addErrorMessage(dataEntity, "单号:"+billno+",审核状态单据不允许删除!");
				}
				Long id = (Long)dataEntity.getBillPkId();
				if(WorkflowServiceHelper.inProcess(GeneralFormatUtils.getString(id)))
				{
				   this.addErrorMessage(dataEntity, "单号:"+billno+",在流程中不允许删除!");
				}
			}else if("unauditbill".equals(opKey)) {
				if(!StringUtils.equals("B", billstatus))
				{
				  this.addErrorMessage(dataEntity, "单号:"+billno+",提交中状态单据才允许审批不通过!");
				}
			}else if("submit".equals(opKey)) {
				//校验供应商分录是否仅存在一条数据供应商类型为新的数据
				DynamicObjectCollection entryColl = ov.getDynamicObjectCollection("entryentity");
				DynamicObject srmSupplierInfo = null;
				boolean isHaveOneNewSupplier = false; //是否仅存在一个供应商类型为新的供应商数据
				//定位供应商类型为新的供应商数据
				for(int jIndex = 0 ; jIndex < entryColl.size() ; jIndex ++) {
					DynamicObject entryInfo = entryColl.get(jIndex);
					String supType = GeneralFormatUtils.getString(entryInfo.get("yd_suptype"));
					if("new".equals(supType) && isHaveOneNewSupplier) {//当对应标识为是  且存在下个供应商类型为新  判断存在多条供应商类型为新的数据
						 this.addErrorMessage(dataEntity, "单号:"+billno+",存在多条供应商类型为新的数据请调整!");
					}
					if(entryInfo == null 
							|| !"new".equals(supType)
							)
						continue;
					srmSupplierInfo = entryInfo.getDynamicObject("yd_dealer");
					isHaveOneNewSupplier = true;
					
				}
				if(srmSupplierInfo == null)
					this.addErrorMessage(dataEntity,"准入单未存在供应商类型为新的供应商数据，请核实！");
				//判断对应
//				String yd_changetype = GeneralFormatUtils.getString(ov.getString("yd_changetype"));
//				DynamicObjectCollection materialFileColl = ov.getDynamicObjectCollection("yd_mataccatt1");
//				if("1".equals(yd_changetype) && (materialFileColl == null || materialFileColl.size() == 0)) {
//					this.addErrorMessage(dataEntity, "单号:"+billno+",新增/变更类别为【新物料】，物料标准说明需上传对应附件!");
//				}
				
			}

			 
		}
	}
	 
}