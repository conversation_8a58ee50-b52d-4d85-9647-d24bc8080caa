package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.RSSBillUpdateHelper;
import kd.bos.workflow.api.AgentExecution;
import kd.bos.workflow.api.WorkflowElement;
import kd.bos.workflow.engine.extitf.IWorkflowPlugin;

/**
 * @auditor yanzuwei
 * @desc 更新 台账节点状态(工作流)
 * @date 2022年7月27日
 * 
 */
public class RssBillWfPlugin implements IWorkflowPlugin
{
	 /**
	  * @fun 单据获取台账记录
	  * **/
	DynamicObject[] getRSSInfos(String billName,Long billId)
	 {
		 List<DynamicObject> rssInfoList=new ArrayList();
		
		 if("yd_supplyinformationbill".equals(billName))
		 {//资料补充函
			    DynamicObject info=BusinessDataServiceHelper.loadSingle(billId,billName);
				DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
				DynamicObject matInfo=(DynamicObject)info.get("yd_material");
				DynamicObject prodInfo=(DynamicObject)info.get("yd_producers");
				String matName=info.getString("yd_materialname");
				if(supInfo==null || prodInfo==null)
				{
					return null;
				} 
				DynamicObject rssInfo=null;
				if(matInfo!=null)
				{
				   rssInfo=RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id")); 
				}else 
				{
				   rssInfo=RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matName);
				}
				 
				if(rssInfo!=null)
				 {
					 rssInfoList.add(rssInfo);
				 }
			 
		 }else if("yd_rawmatsupaccessbill".equals(billName))
		 {//原辅料供应商准入单
			    
			    DynamicObject info=BusinessDataServiceHelper.loadSingle(billId,billName);
				DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
				DynamicObject matInfo=(DynamicObject)info.get("yd_material");
				DynamicObject prodInfo=(DynamicObject)info.get("yd_tempproducers");
				if(supInfo==null||matInfo==null||prodInfo==null)
				{
					return  null;
				}
				DynamicObject rssInfo=null;
			    rssInfo=RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
			    if(rssInfo!=null)
				 {
					 rssInfoList.add(rssInfo);
				 }
			    
			    // 根据现有物料代码获取现有物料台账记录
				DynamicObjectCollection otherMatObjs = info.getDynamicObjectCollection("yd_othermat");
				if (otherMatObjs != null) {
					for (DynamicObject tmpMat : otherMatObjs) {
						DynamicObject tmpRssInfo = RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),tmpMat.getLong("fbasedataid_id"));
						if(tmpRssInfo!=null)
						{
							rssInfoList.add(tmpRssInfo);
						}
					}
				}
			 
		 }else if("yd_newmatreqbill".equals(billName))
		 {//新物料需求及供应商评价单
			    
			    DynamicObject info=BusinessDataServiceHelper.loadSingle(billId,billName); 
				String matName=info.getString("yd_materialname");
				if(StringUtils.isEmpty(matName))
				{
					return null ;
				}
				DynamicObjectCollection entrys=info.getDynamicObjectCollection("yd_file1entry");
				if(entrys!=null&&entrys.size()>0)
				{
					
					for(int i=0;i<entrys.size();i++)
					{
						DynamicObject entry=entrys.get(i);
						DynamicObject supInfo=entry.getDynamicObject("yd_csup");
						DynamicObject prodInfo=entry.getDynamicObject("yd_cprod");
						if(supInfo!=null&&prodInfo!=null)
						{
							DynamicObject rss=RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matName);
							 if(rss!=null)
							 {
								 rssInfoList.add(rss);
							 }
						} 
					}  
				}
			 
			 
		 }else if("yd_suptrialbizbill".equals(billName))
		 {//供应商试产业务单
			 DynamicObject info=BusinessDataServiceHelper.loadSingle(billId,billName);
				DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
				DynamicObject matInfo=(DynamicObject)info.get("yd_materiel");
				DynamicObject prodInfo=(DynamicObject)info.get("yd_producers");
				if(supInfo==null||matInfo==null||prodInfo==null)
				{
					return  null;
				}
				DynamicObject rssInfo=null;
			    rssInfo=RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
			    if(rssInfo!=null)
				 {
					 rssInfoList.add(rssInfo);
				 }
			 
		 }else if("yd_suptrialjobbill".equals(billName))
		 {//供应商试产作业单
			    DynamicObject info=BusinessDataServiceHelper.loadSingle(billId,billName);
				DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
				DynamicObject matInfo=(DynamicObject)info.get("yd_materiel");
				DynamicObject prodInfo=(DynamicObject)info.get("yd_producers");
				if(supInfo==null||matInfo==null||prodInfo==null)
				{
					return  null;
				}
				DynamicObject rssInfo=null;
			    rssInfo=RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
			    if(rssInfo!=null)
				{
					 rssInfoList.add(rssInfo);
				}
		 } 
		 else if("yd_trialpurreqbill".equals(billName))
		 {//试产采购申请单
			    DynamicObject info=BusinessDataServiceHelper.loadSingle(billId,billName);
				DynamicObject supInfo=(DynamicObject)info.get("yd_supplier");
				DynamicObject matInfo=(DynamicObject)info.get("yd_material");
				DynamicObject prodInfo=(DynamicObject)info.get("yd_producers");
				if(supInfo==null||matInfo==null||prodInfo==null)
				{
					return  null;
				}
				DynamicObject rssInfo=null;
			    rssInfo=RSSBillUpdateHelper.findRSSBill(supInfo.getLong("id"),prodInfo.getLong("id"),matInfo.getLong("id"));
			    if(rssInfo!=null)
				{
					 rssInfoList.add(rssInfo);
				}
		 } 
		 return rssInfoList.toArray(new DynamicObject[rssInfoList.size()]);
	 }
	
	/**
	 * @fun 更新台账节点信息
	 * @param nodeName 节点名称编码规则:编码-名称 例如:"55-资质评估"
	 * **/
	public void updateNode(String billType,String billId,String nodeName) {
		DynamicObject[] rssInfos = getRSSInfos(billType, Long.valueOf(billId));
		if (rssInfos == null || rssInfos.length == 0) {
			return;
		}

		String[] numbers = StringUtils.split(nodeName, "-");
		if (numbers != null && numbers.length > 0) {
			String nodeNum = numbers[0];
			for (DynamicObject rssInfo : rssInfos) {
				RSSBillUpdateHelper.updateSupDicByNodeNumber(nodeNum, rssInfo);
				SaveServiceHelper.save(new DynamicObject[]{rssInfo});
			}
		}
	}
	
	 public void notify(AgentExecution agent)
	 {
		  String billId = agent.getBusinessKey();
		  String billType=agent.getEntityNumber(); 
		  WorkflowElement<WorkflowElement> flowElement = agent.getCurrentFlowElement();
		  String nodeName = flowElement.getName();//当前节点名称
		  updateNode(billType,billId,nodeName); 
	 }
}
