package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.admittance.oplugin.SupSumBillTempAuditOp
 * @className SupSumBillTempAuditOp
 * @author: hst
 * @createDate: 2024/03/11
 * @description: 旧台账-临时允许采购审核插件
 * @version: v1.0
 */
public class SupSumBillTempAuditOp extends AbstractOperationServicePlugIn {

    /** 审核操作 **/
    private final static String AUDIT_OP = "audit";
    /** 反审核操作 **/
    private final static String UNAUDIT_OP = "unaudit";


    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/03/11
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        // 源合格供应商目录ID
        e.getFieldKeys().add("yd_sourceid");
        // 单据类型
        e.getFieldKeys().add("yd_billtype");
        // 新供应商状态
        e.getFieldKeys().add("yd_newstatus");
        // 旧供应商状态
        e.getFieldKeys().add("yd_supstatus");
        // 是否已关闭
        e.getFieldKeys().add("yd_isclose");
    }

    /**
     * 操作执行完毕，事务未提交时
     * @param e
     * @author: hst
     * @createDate: 2024/03/11
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        super.endOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case AUDIT_OP : {
                // 更新台账状态
                this.updateOldStandStatus(bills,"1");
                break;
            }
            case UNAUDIT_OP : {
                // 还原台账状态
                this.updateOldStandStatus(bills,"2");
            }
        }
    }

    /**
     * 更新台账状态
     * @param bills
     * @param type 类型（1：更新；2：还原）
     * @author: hst
     * @createDate: 2024/03/11
     */
    private void updateOldStandStatus (DynamicObject[] bills, String type) {
        Map<String,String> statusMap = new HashMap<>();
        Map<String,List<String>> typeMap = new HashMap<>();
        for (DynamicObject bill : bills) {
            String isClose = bill.getString("yd_isclose");
            String sourceId = bill.getString("yd_sourceid");
            String billType = bill.getString("yd_billtype");
            String status = "1".equals(type) ? bill.getString("yd_newstatus")
                    :bill.getString("yd_supstatus");
            if (StringUtils.isNotBlank(sourceId) && StringUtils.isNotBlank(billType)
                    && StringUtils.isNotBlank(status) && "yd_packsupsumbill".equals(billType)
                    && "0".equals(isClose)) {
                statusMap.put(sourceId,status);
                if (typeMap.containsKey(billType)) {
                    typeMap.get(billType).add(sourceId);
                } else {
                    typeMap.put(billType, Arrays.asList(new String[]{sourceId}));
                }
            }
        }

        // 获取需要进行处理的单据
        for (Map.Entry<String,List<String>> entry : typeMap.entrySet()) {
            String key = entry.getKey();
            List<String> sourceIds = entry.getValue();
            DynamicObject[] stands = BusinessDataServiceHelper.load(key,"yd_supstatus",
                    new QFilter("id",QFilter.in,sourceIds).toArray());

            for (DynamicObject stand : stands) {
                String billId = stand.getString("id");
                if (statusMap.containsKey(billId)) {
                    stand.set("yd_supstatus",statusMap.get(billId));
                }
            }

            SaveServiceHelper.save(stands);
        }
    }
}
