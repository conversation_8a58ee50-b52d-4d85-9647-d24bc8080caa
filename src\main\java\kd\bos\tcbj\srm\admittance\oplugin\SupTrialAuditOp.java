package kd.bos.tcbj.srm.admittance.oplugin;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.entity.validate.BillStatus;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.workflow.WorkflowServiceHelper;
import kd.isc.iscb.platform.core.proxy.BusinessDataServiceHelperProxy;

/**
 * @auditor cary
 * @date 2022年6月7日
 * 
 */
public class SupTrialAuditOp extends AbstractOperationServicePlugIn 
{
    @Override
	public void onPreparePropertys(PreparePropertysEventArgs e)
	{
	    e.getFieldKeys().add("yd_supaccess"); 
	    e.getFieldKeys().add("billno"); 
	    e.getFieldKeys().add("yd_testcomfiretype"); 
	    
	}
 
	@Override
	public void onAddValidators(AddValidatorsEventArgs e) 
	{
	   //e.addValidator(new DelCheckValidate());
	}
	
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) 
	{
		super.beforeExecuteOperationTransaction(e);
		    
	}
	
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
	    super.afterExecuteOperationTransaction(e);
	    String operationKey = e.getOperationKey();
	    if (StringUtils.equals(operationKey, "audit")) 
	    {
	        DynamicObject[] bills = e.getDataEntities();
	        for (DynamicObject bill : bills) 
	        {
	        	DynamicObject supaccessInfo= bill.getDynamicObject("yd_supaccess");
	            if(supaccessInfo!=null)
	            {
	            	genSupAccBillEntryData(""+supaccessInfo.getPkValue(),bill);
	            } 
	        } 
	        
	    } 
	}
	
	boolean isExist(DynamicObjectCollection entrys,String billno)
	{
		for(int i=0;i<entrys.size();i++)
		{
			String trialbillno=entrys.get(i).getString("yd_trialbillno");
			if(StringUtils.equals(trialbillno, billno))
			{
				return true;
			}
			
		}
		
		return false;
		
	}
	
	
	void genSupAccBillEntryData(String pk,DynamicObject supTrialInfo)
	{
		  
		 Object accBillPK=pk;
		 
		 DynamicObject accBillInfo= BusinessDataServiceHelper.loadSingle(accBillPK, "yd_rawmatsupaccessbill");
		 
		 DynamicObjectCollection trailentrys= accBillInfo.getDynamicObjectCollection("yd_trailentryentity");
		 
		 DynamicObject entryInfo = null;
		 for (int i = 0; i < trailentrys.size(); i++) {
			String trialbillno = trailentrys.get(i).getString("yd_trialbillno");
			if (StringUtils.equals(trialbillno, supTrialInfo.getString("billno"))) {
				entryInfo = trailentrys.get(i);
				break;
			}
		 }
		 if(entryInfo != null) {
			 entryInfo.set("yd_trialbillid",""+supTrialInfo.getPkValue());
			 entryInfo.set("yd_trialbillno",supTrialInfo.getString("billno"));
			 entryInfo.set("yd_trialstatus","审核");
			 
			 String reuslt=supTrialInfo.getString("yd_testcomfiretype");
			 String reusltText="";
			 
			  if("1".equals(reuslt))
			  {
				  reusltText="试产成功";
			  } else if("2".equals(reuslt))
			  {
				  reusltText="重新试产";
			  } else if("3".equals(reuslt))
			  {
				  reusltText="试产暂停";
			  } 
			 
			 entryInfo.set("yd_trialresult",reusltText); 
			 trailentrys.add(entryInfo);
			 SaveServiceHelper.save(new DynamicObject[] {accBillInfo});
		 }
		 
		 /*if(isExist(trailentrys,supTrialInfo.getString("billno")))
		 {
			 return ;
		 }
		 
		 
		 
		 DynamicObjectType type = trailentrys.getDynamicObjectType(); 
		 DynamicObject entryInfo = new DynamicObject(type);  
		 entryInfo.set("yd_trialbillid",""+supTrialInfo.getPkValue());
		 entryInfo.set("yd_trialbillno",supTrialInfo.getString("billno"));
		 entryInfo.set("yd_trialstatus","审核");
		 
		 
		 String reuslt=supTrialInfo.getString("yd_testcomfiretype");
		 String reusltText="";
		 
		  if("1".equals(reuslt))
		  {
			  reusltText="试产成功";
		  } else if("2".equals(reuslt))
		  {
			  reusltText="重新试产";
		  } else if("3".equals(reuslt))
		  {
			  reusltText="试产暂停";
		  } 
		 
		 entryInfo.set("yd_trialresult",reusltText); 
		 trailentrys.add(entryInfo);
		 SaveServiceHelper.save(new DynamicObject[] {accBillInfo});
		 */
	}

}

class SupTrialAuditValidate extends AbstractValidator
{
	@Override
	public void validate() 
	{
		
		ExtendedDataEntity[] datas = this.getDataEntities();
		for(ExtendedDataEntity dataEntity : datas)
		{
			DynamicObject ov=dataEntity.getDataEntity();
			  
		}
	}
	 
}