package kd.bos.tcbj.srm.admittance.oplugin;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.entity.validate.BillStatus;
import kd.bos.servicehelper.workflow.WorkflowServiceHelper;

/**
 * @auditor cary
 * @date 2022年6月7日
 * 
 */
public class SupTrialDeleteOp extends AbstractOperationServicePlugIn 
{
    @Override
	public void onPreparePropertys(PreparePropertysEventArgs e)
	{
	   e.getFieldKeys().add("billstatus"); 
	   e.getFieldKeys().add("billno"); 
	}
 
	@Override
	public void onAddValidators(AddValidatorsEventArgs e) 
	{
	   e.addValidator(new DelCheckValidate());
	}

}

class DelCheckValidate extends AbstractValidator
{
	@Override
	public void validate() 
	{
		
		ExtendedDataEntity[] datas = this.getDataEntities();
		for(ExtendedDataEntity dataEntity : datas)
		{
			DynamicObject ov=dataEntity.getDataEntity();
			String billno = (String)ov.getString("billno");
			String billstatus = (String)ov.getString("billstatus");
			
			if(StringUtils.equals("C", billstatus))
			{
			  this.addErrorMessage(dataEntity, "单号:"+billno+",审核状态单据不允许删除.");
			}
			String id =""+dataEntity.getBillPkId();
			if(WorkflowServiceHelper.inProcess(id))
			{
			   this.addErrorMessage(dataEntity, "单号:"+billno+",在流程中不允许删除.");
			}
			 
		}
	}
	 
}