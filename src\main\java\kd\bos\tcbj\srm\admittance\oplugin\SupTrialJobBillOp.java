package kd.bos.tcbj.srm.admittance.oplugin;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.db.DB;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 试产作业单_自定义服务插件
 * @auditor liefengWu
 * @date 2022年6月30日
 * 
 */
public class SupTrialJobBillOp extends AbstractOperationServicePlugIn {

	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_suptrialbizbill");//试产业务单
		e.getFieldKeys().add("yd_supaccess");//原辅料准入单
		
		e.getFieldKeys().add("id");
		e.getFieldKeys().add("billno");
		e.getFieldKeys().add("creator");
		
		e.getFieldKeys().add("yd_craftsman");//工艺员
		e.getFieldKeys().add("yd_trialworkshop");//试产车间（作废）
		e.getFieldKeys().add("yd_workshop");//试产车间（做成枚举）
		e.getFieldKeys().add("yd_trialprd");//试产产品
		e.getFieldKeys().add("yd_practice");//做法
		
		e.getFieldKeys().add("yd_testcomfiretype");//试产情况确认
		e.getFieldKeys().add("yd_trialprosummary");//试产总结
		e.getFieldKeys().add("yd_stabinvtype");//稳定性考察情况
		e.getFieldKeys().add("yd_stablyaccessqua");//稳定性考察是否合格
		e.getFieldKeys().add("yd_stablyaccessatt");//稳定性考察文件
		// update by hst 2023/04/26 新增试产结论以质量管理部判断为准
		e.getFieldKeys().add("yd_trialsituation");//质量管理部确认试产情况


		e.getFieldKeys().add("yd_prcadjtype");//是否涉及工艺调整
		e.getFieldKeys().add("yd_mataccessattach1");//涉及工艺调整_附件字段
		
	}
	
	@Override
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		super.beforeExecuteOperationTransaction(e);
		String opKey = e.getOperationKey();
		if (StringUtils.equals(opKey, "submit") || StringUtils.equals(opKey, "audit")) {
			DynamicObject[] bills = e.getDataEntities();
			for (DynamicObject bill : bills) {
				DynamicObject supaccessInfo = bill.getDynamicObject("yd_supaccess");  // 原辅料准入单
				DynamicObject suptrialbizbillInfo = bill.getDynamicObject("yd_suptrialbizbill");  // 业务单
				if(supaccessInfo != null && supaccessInfo.getPkValue() != null) {
					suptrialbizbillInfo = BusinessDataServiceHelper.loadSingle(suptrialbizbillInfo.getPkValue(), "yd_suptrialbizbill", "id,billno");
				}
				
				// 反写准入单试产业务分录
				if (supaccessInfo != null) {
					genOrUpdateSupAccBillEntryData("" + supaccessInfo.getPkValue(), opKey, bill, suptrialbizbillInfo);
					
				}
				
				// 反写业务单试产业务分录
				if(suptrialbizbillInfo != null) {
					genOrUpdateTrialBizEntryData(opKey, bill, suptrialbizbillInfo);
				}
			}

		}
		
		// 删除后需要同步删除业务单和准入单上的作业信息,yzl
		if (StringUtils.equals(opKey, "delete")) {
			DynamicObject[] bills = e.getDataEntities();
			for (DynamicObject bill : bills) {
				DynamicObject supaccessInfo = bill.getDynamicObject("yd_supaccess");  // 原辅料准入单
				DynamicObject suptrialbizbillInfo = bill.getDynamicObject("yd_suptrialbizbill");  // 业务单
				if(supaccessInfo != null && supaccessInfo.getPkValue() != null) {
					suptrialbizbillInfo = BusinessDataServiceHelper.loadSingle(suptrialbizbillInfo.getPkValue(), "yd_suptrialbizbill", "id,billno");
				}
				
				// 删除准入单试产业务分录
				if (supaccessInfo != null) {
					DynamicObject rawMatBill = BusinessDataServiceHelper.loadSingle(supaccessInfo.get("id"), "yd_rawmatsupaccessbill");
					DynamicObjectCollection trailEnCol = rawMatBill.getDynamicObjectCollection("yd_trailentryentity");
					for (int i=0;i<trailEnCol.size();i++) {
						if (bill.getString("id").equalsIgnoreCase(trailEnCol.get(i).getString("yd_supjobbill.id"))) {
							trailEnCol.remove(i);
							i=0;
						}
					}
					SaveServiceHelper.save(new DynamicObject[] {rawMatBill});
				}
				
				// 删除业务单试产业务分录
				if(suptrialbizbillInfo != null) {
					DynamicObject trialBizBill = BusinessDataServiceHelper.loadSingle(suptrialbizbillInfo.get("id"), "yd_suptrialbizbill");//试产业务单
					DynamicObjectCollection trailentrys = trialBizBill.getDynamicObjectCollection("entryentity");
					for (int i=0;i<trailentrys.size();i++) {
						if (bill.getString("id").equalsIgnoreCase(trailentrys.get(i).getString("yd_jobbillid"))) {
							trailentrys.remove(i);
							i=0;
						}
					}
					SaveServiceHelper.save(new DynamicObject[] {trialBizBill});
				}
			}
		}
		
	}
	
	/**
	 * 反写试产业务单的分录
	 * 提交作业单时创建一条记录，只记录试产业务单、试产作业单、工艺员
	 * 审核作业单时根据作业单单号反写原记录，反写车间、产品、试产情况确认、试产总结、是否需要稳定性考察、稳定性考察是否合格、稳定性考察文件
	 * @param pk
	 * @param opKey  操作类型
	 * @param supJobInfo  作业单INFO
	 * @param suptrialbizbillInfo  业务单INFO
	 */
	void genOrUpdateTrialBizEntryData(String opKey,DynamicObject supJobInfo,DynamicObject suptrialbizbillInfo) {

		if(suptrialbizbillInfo == null || suptrialbizbillInfo.getPkValue() == null)
			return;
		
		Object accBillPK = suptrialbizbillInfo.getPkValue();

		suptrialbizbillInfo = BusinessDataServiceHelper.loadSingle(accBillPK, "yd_suptrialbizbill");//试产业务单

		DynamicObjectCollection  trailentrys = suptrialbizbillInfo.getDynamicObjectCollection("entryentity");
		
		DynamicObject entryInfo = isExist(trailentrys, "yd_jobbillno", GeneralFormatUtils.getString(supJobInfo.getString("billno")));
		boolean isAddNew = entryInfo == null;
		
		DynamicObjectType type = trailentrys.getDynamicObjectType();
		entryInfo = entryInfo != null ? entryInfo : new DynamicObject(type);
		
		if(StringUtils.equals(opKey, "submit")) {
			entryInfo.set("yd_jobbillno", GeneralFormatUtils.getString(supJobInfo.getString("billno")));  // 试产作业单单号
			entryInfo.set("yd_jobbillid",  GeneralFormatUtils.getString(supJobInfo.getPkValue()));  // 试产作业单ID
			entryInfo.set("yd_entcraftsman", supJobInfo.get("yd_craftsman"));  // 工艺员
		} else if(StringUtils.equals(opKey, "audit"))  {
			entryInfo.set("yd_jobbillno", GeneralFormatUtils.getString(supJobInfo.getString("billno")));  // 试产作业单单号
			entryInfo.set("yd_jobbillid",  GeneralFormatUtils.getString(supJobInfo.getPkValue()));  // 试产作业单ID
			entryInfo.set("yd_entcraftsman", supJobInfo.get("yd_craftsman"));  // 工艺员
//			entryInfo.set("yd_workshop", supJobInfo.get("yd_trialworkshop"));  // 车间（作废）
			entryInfo.set("yd_workshopsel", supJobInfo.get("yd_workshop"));  // 车间
			entryInfo.set("yd_entproduct", supJobInfo.get("yd_trialprd"));  // 产品
			entryInfo.set("yd_suptrialconfirmsel", supJobInfo.get("yd_testcomfiretype"));  // 试产情况确认
			entryInfo.set("yd_trysumdesc", supJobInfo.get("yd_trialprosummary"));  // 试产总结
			entryInfo.set("yd_stabinvtype", supJobInfo.get("yd_stabinvtype"));  // 是否需要稳定性考察
			entryInfo.set("yd_stablyaccessqua", supJobInfo.get("yd_stablyaccessqua"));  // 稳定性考察是否合格
			
			// 稳定性考察文件
			DynamicObjectCollection accessattachCol = supJobInfo.getDynamicObjectCollection("yd_stablyaccessatt");
			
			if(accessattachCol != null && accessattachCol.size() > 0) {
				DynamicObjectCollection attCol = new DynamicObjectCollection();  // 最终的附件
				for(int jIndex = 0 ; jIndex < accessattachCol.size() ; jIndex ++) {
					DynamicObject att_bd = accessattachCol.get(jIndex);//原附件
					DynamicObject bd = copyAttachBd(att_bd.getDynamicObject("fbasedataid"));//复制附件
					bd.set("status", "B");
					SaveServiceHelper.save(new DynamicObject[] {bd});//将复制的附件存入系统
					//创建附件字段信息  (对应的是附件字段的表结构)
					DynamicObject attField = new DynamicObject(accessattachCol.getDynamicObjectType());
					System.out.println("----"+att_bd.getDynamicObjectType());
					attField.set("fbasedataid", bd);
					attCol.add(attField);
				}
				entryInfo.set("yd_entfileatt", attCol);  // 稳定性考察文件
			}
		}
		
		if(isAddNew)
			trailentrys.add(entryInfo);
		SaveServiceHelper.save(new DynamicObject[] { suptrialbizbillInfo });
	}
	
	/**
	 * 反写准入单的试产业务分录
	 * 提交作业单时创建一条记录，只记录试产业务单、试产作业单、工艺员
	 * 审核作业单时根据作业单单号反写原记录，反写车间、产品、试产情况确认、试产总结、是否需要稳定性考察、稳定性考察是否合格、稳定性考察文件
	 * @param pk  原辅料准入单ID
	 * @param opKey  操作类型submit/audit
	 * @param supJobInfo  作业单INFO
	 * @param suptrialbizbillInfo  业务单INFO
	 */
	void genOrUpdateSupAccBillEntryData(String pk,String opKey,DynamicObject supJobInfo,DynamicObject suptrialbizbillInfo) {

		if(suptrialbizbillInfo == null)
			return;
		
		Object accBillPK = pk;

		// 准入单
		DynamicObject accBillInfo = BusinessDataServiceHelper.loadSingle(accBillPK, "yd_rawmatsupaccessbill");

		// 准入单的试产作业分录
		DynamicObjectCollection trailentrys = accBillInfo.getDynamicObjectCollection("yd_trailentryentity");
		
		DynamicObject entryInfo = isExist(trailentrys, GeneralFormatUtils.getString(supJobInfo.getString("billno")));
		boolean isAddNew = entryInfo == null;
		DynamicObjectType type = trailentrys.getDynamicObjectType();
		entryInfo = entryInfo != null ? entryInfo : new DynamicObject(type);
		
		if(StringUtils.equals(opKey, "submit")) {
			entryInfo.set("yd_supjobbill", supJobInfo);  // 试产作业单F7
			entryInfo.set("yd_supbizbill", suptrialbizbillInfo);  // 试产业务单F7
			entryInfo.set("yd_trialbillid", "" + suptrialbizbillInfo.getPkValue());  // 试产业务单ID
			entryInfo.set("yd_trialbillno", suptrialbizbillInfo.getString("billno"));  // 试产业务单单号
			entryInfo.set("yd_suptrialjobbillno", GeneralFormatUtils.getString(supJobInfo.getString("billno")));  // 试产作业单单号
			entryInfo.set("yd_suptrialjobbillid",  GeneralFormatUtils.getString(supJobInfo.getPkValue()));  // 试产作业单ID
			entryInfo.set("yd_technician", supJobInfo.get("yd_craftsman"));  // 工艺员
		} else if(StringUtils.equals(opKey, "audit"))  {
			entryInfo.set("yd_supjobbill", supJobInfo);  // 试产作业单F7
			entryInfo.set("yd_supbizbill", suptrialbizbillInfo);  // 试产业务单F7
			entryInfo.set("yd_trialbillid", "" + suptrialbizbillInfo.getPkValue());  // 试产业务单ID
			entryInfo.set("yd_trialbillno", suptrialbizbillInfo.getString("billno"));  // 试产业务单单号
			entryInfo.set("yd_suptrialjobbillno", GeneralFormatUtils.getString(supJobInfo.getString("billno")));  // 试产作业单单号
			entryInfo.set("yd_suptrialjobbillid",  GeneralFormatUtils.getString(supJobInfo.getPkValue()));  // 试产作业单ID
			entryInfo.set("yd_technician", supJobInfo.get("yd_craftsman"));  // 工艺员
//			entryInfo.set("yd_workshop", supJobInfo.get("yd_trialworkshop"));  // 车间（作废）
			entryInfo.set("yd_workshopsel", supJobInfo.get("yd_workshop"));  // 车间
			entryInfo.set("yd_product", supJobInfo.get("yd_trialprd"));  // 产品
//			entryInfo.set("yd_suptrialconfirmsel", supJobInfo.get("yd_testcomfiretype"));  // 试产情况确认
			// update by hst 2023/04/26 新增试产结论以质量管理部判断为准
			entryInfo.set("yd_suptrialconfirmsel", supJobInfo.get("yd_trialsituation"));  // 试产情况确认
			entryInfo.set("yd_trialprosummary", supJobInfo.get("yd_trialprosummary"));  // 试产总结
			entryInfo.set("yd_stabinvtype", supJobInfo.get("yd_stabinvtype"));  // 是否需要稳定性考察
			entryInfo.set("yd_stablyaccessqua", supJobInfo.get("yd_stablyaccessqua"));  // 稳定性考察是否合格

			// 稳定性考察文件
			DynamicObjectCollection accessattachCol = supJobInfo.getDynamicObjectCollection("yd_stablyaccessatt");
			
			if(accessattachCol != null && accessattachCol.size() > 0) {
				DynamicObjectCollection attCol = new DynamicObjectCollection();  // 最终的附件
				for(int jIndex = 0 ; jIndex < accessattachCol.size() ; jIndex ++) {
					DynamicObject att_bd = accessattachCol.get(jIndex);//原附件
					DynamicObject bd = copyAttachBd(att_bd.getDynamicObject("fbasedataid"));//复制附件
					bd.set("status", "B");
					SaveServiceHelper.save(new DynamicObject[] {bd});//将复制的附件存入系统
					//创建附件字段信息  (对应的是附件字段的表结构)
					DynamicObject attField = new DynamicObject(accessattachCol.getDynamicObjectType());
					System.out.println("----"+att_bd.getDynamicObjectType());
					attField.set("fbasedataid", bd);
					attCol.add(attField);
				}
				entryInfo.set("yd_stablyaccessatt", attCol);  // 稳定性考察文件
			}
		}
		
		if(isAddNew)
			trailentrys.add(entryInfo);
		SaveServiceHelper.save(new DynamicObject[] { accBillInfo });
	}
	
	private DynamicObject copyAttachBd(DynamicObject att_bd) {
		
		System.out.println(att_bd.getDataEntityType().getName());
		DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject(att_bd.getDataEntityType().getName());
		DataEntityPropertyCollection properties = att_bd.getDataEntityType().getProperties();
		for(IDataEntityProperty p1:properties) {
			//id 和 multilanguagetext不用设置 设置了会导致字段的重复
			
			System.out.println(p1.getName()+"----"+att_bd.get(p1));
			if(!p1.getName().equals("id") &&  !p1.getName().equals("multilanguagetext")) {
				//uid加了索引，因此不能重复    粗略可以使用下面的策略       原本的生成策略没有找到
				if(p1.getName().equals("uid")) {
					newObj.set("uid",DB.genLongId(""));
				}else {
					Object value = att_bd.get(p1);
					newObj.set(p1, value);
				} 
			}
		}
		return newObj;
	}

	/**
	 * 校验分录是否存在对应试产作业单号
	 * @param entrys
	 * @param billno
	 * @return
	 */
	DynamicObject isExist(DynamicObjectCollection entrys, String suptrialbizbillNo) {
		DynamicObject result = null;
		for (int i = 0; i < entrys.size(); i++) {
			String trialbillno = entrys.get(i).getString("yd_suptrialjobbillno");
			if (StringUtils.equals(trialbillno, suptrialbizbillNo)) {
				result = entrys.get(i);
				return result;
			}
		}

		return result;

	}

	/**
	 * 校验分录字段名对应是否存在对应字段值
	 * @param entrys
	 * @param billno
	 * @return
	 */
	DynamicObject isExist(DynamicObjectCollection entrys, String fieldName,String fieldValue) {
		DynamicObject result = null;
		for (int i = 0; i < entrys.size(); i++) {
			String trialbillno = entrys.get(i).getString(fieldName);
			if (StringUtils.equals(trialbillno, fieldValue)) {
				result = entrys.get(i);
				return result;
			}
		}
		return result;

	}

	@Override
	public void onAddValidators(AddValidatorsEventArgs e) {
		super.onAddValidators(e);
		e.addValidator(new SupTrialJobValidator());
	}
}




/**
 * 试产作业单校验器
 * <AUTHOR>
 *
 */
class SupTrialJobValidator extends AbstractValidator
{
	@Override
	public void validate() 
	{
		
		ExtendedDataEntity[] datas = this.getDataEntities();
		for(ExtendedDataEntity dataEntity : datas)
		{
			DynamicObject ov=dataEntity.getDataEntity();
			String opKey = this.getOperateKey();
			if("save003".equals(opKey)) {//方案或指令提交校验 增加校验
				String yd_prcadjtype = GeneralFormatUtils.getString(ov.get("yd_prcadjtype"));//是否涉及工艺调整 为是  则需要校验需上传附件
				if("1".equals(yd_prcadjtype)) {
					DynamicObjectCollection attachColl = ov.getDynamicObjectCollection("yd_mataccessattach1");
					if(attachColl == null || attachColl.size() == 0) {
						 this.addErrorMessage(dataEntity, "单据编号:"+ov.getString("billno")+"【是否涉及工艺调整】字段值为‘涉及工艺调整’时，【工艺调整需上传试产方案】对应附件，请核实！");
					}
				}
			}
			if("save001".equals(opKey)) {//口感评价提交校验 增加校验,yzw
				DynamicObjectCollection attachColl = ov.getDynamicObjectCollection("yd_trial_att18");
				if(attachColl == null || attachColl.size() == 0) {
					this.addErrorMessage(dataEntity, "单据编号:"+ov.getString("billno")+"的【口感评估附件】必须上传，请核实！");
				}
			}
		
		}
	}
	 
}