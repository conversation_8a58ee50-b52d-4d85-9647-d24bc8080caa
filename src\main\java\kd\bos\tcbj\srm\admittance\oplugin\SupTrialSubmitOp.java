package kd.bos.tcbj.srm.admittance.oplugin;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * 试产业务单_提交自定义操作插件
 * 
 * @auditor liefengWu
 * @date 2022年6月30日
 * 
 */
public class SupTrialSubmitOp extends AbstractOperationServicePlugIn {

	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		e.getFieldKeys().add("yd_supaccess");
		e.getFieldKeys().add("billno");
		e.getFieldKeys().add("yd_testcomfiretype");

	}

	@Override
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		super.beforeExecuteOperationTransaction(e);
		String operationKey = e.getOperationKey();
		if (StringUtils.equals(operationKey, "submit")) {
			DynamicObject[] bills = e.getDataEntities();
			for (DynamicObject bill : bills) {
				DynamicObject supaccessInfo = bill.getDynamicObject("yd_supaccess");
				if (supaccessInfo != null) {
					genSupAccBillEntryData("" + supaccessInfo.getPkValue(), bill);
				}
			}

		}
	}

	void genSupAccBillEntryData(String pk, DynamicObject supTrialInfo) {

		Object accBillPK = pk;

		DynamicObject accBillInfo = BusinessDataServiceHelper.loadSingle(accBillPK, "yd_rawmatsupaccessbill");

		DynamicObjectCollection trailentrys = accBillInfo.getDynamicObjectCollection("yd_trailentryentity");

		if (isExist(trailentrys, supTrialInfo.getString("billno"))) {
			return;
		}

		DynamicObjectType type = trailentrys.getDynamicObjectType();
		DynamicObject entryInfo = new DynamicObject(type);
		entryInfo.set("yd_trialbillid", "" + supTrialInfo.getPkValue());
		entryInfo.set("yd_trialbillno", supTrialInfo.getString("billno"));
	//	entryInfo.set("yd_trialstatus", "审核");

		/*String reuslt = supTrialInfo.getString("yd_testcomfiretype");
		String reusltText = "";

		if ("1".equals(reuslt)) {
			reusltText = "试产成功";
		} else if ("2".equals(reuslt)) {
			reusltText = "重新试产";
		} else if ("3".equals(reuslt)) {
			reusltText = "试产暂停";
		}

		entryInfo.set("yd_trialresult", reusltText);*/
		trailentrys.add(entryInfo);
		SaveServiceHelper.save(new DynamicObject[] { accBillInfo });
	}

	boolean isExist(DynamicObjectCollection entrys, String billno) {
		for (int i = 0; i < entrys.size(); i++) {
			String trialbillno = entrys.get(i).getString("yd_trialbillno");
			if (StringUtils.equals(trialbillno, billno)) {
				return true;
			}
		}

		return false;

	}
}
