package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;

/**
 * @auditor cary
 * @date 2022年6月7日
 * 
 */
public class SupTrialUnAuditOp extends AbstractOperationServicePlugIn 
{
    @Override
	public void onPreparePropertys(PreparePropertysEventArgs e)
	{
	    e.getFieldKeys().add("yd_supaccess"); 
	}
 
	@Override
	public void onAddValidators(AddValidatorsEventArgs e) 
	{
	  
	}
	
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) 
	{
		super.beforeExecuteOperationTransaction(e);
		    
	}
	
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
	    super.afterExecuteOperationTransaction(e);
	    
	        DynamicObject[] bills = e.getDataEntities();
	        for (DynamicObject bill : bills) 
	        {
	        	DynamicObject supaccessInfo= bill.getDynamicObject("yd_supaccess");
	            if(supaccessInfo!=null)
	            {
	            	Object supAccId=supaccessInfo.getPkValue();
	            	Object billId=bill.getPkValue();
	            	String sql="delete from tk_yd_trailentryentity where fid=? and fk_yd_trialbillid=?";
	            	DB.execute(DBRoute.of("scm"), sql, new Object[] {supAccId,billId});
	            } 
	        } 
	}
	
	
	 
}