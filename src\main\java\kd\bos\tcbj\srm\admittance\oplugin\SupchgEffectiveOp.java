package kd.bos.tcbj.srm.admittance.oplugin;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.admittance.helper.SupplyListBizHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.bos.util.JSONUtils;

/**
 * @auditor yanzuwei
 * @date 2022年8月8日
 * @desc 台账变更单据生效逻辑
 */
public class SupchgEffectiveOp extends AbstractOperationServicePlugIn {
	/**
	 * 原辅料合格供应商目录变更生效
	 **/
	private final static String EFFECT_RAW_OP = "effective";
	/**
	 * 包材合格供应商目录变更生效
	 **/
	private final static String EFFECT_PACK_OP = "effective_pack";

	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		e.getFieldKeys().add("billstatus");
		e.getFieldKeys().add("billno");
		e.getFieldKeys().add("yd_datastatus");
		e.getFieldKeys().add("yd_srcbillid");
		e.getFieldKeys().add("entryentity.yd_infotype");
		e.getFieldKeys().add("entryentity.yd_chgfield");
		e.getFieldKeys().add("entryentity.yd_oldvalue");
		e.getFieldKeys().add("entryentity.yd_newvalue");
		e.getFieldKeys().add("entryentity.yd_note");
		e.getFieldKeys().add("entryentity.yd_fieldname");
		e.getFieldKeys().add("entryentity.yd_oldvalueex");
		e.getFieldKeys().add("entryentity.yd_newvalueex");
		e.getFieldKeys().add("yd_cancelitem");
		e.getFieldKeys().add("yd_cancelmatcode");
		e.getFieldKeys().add("yd_assessrisk");
		e.getFieldKeys().add("yd_cancelreason");
		e.getFieldKeys().add("yd_isstop");
		e.getFieldKeys().add("yd_cancelentry.yd_cancelitem");
		e.getFieldKeys().add("yd_cancelentry.yd_cancelmatcode");
		e.getFieldKeys().add("yd_cancelentry.yd_assessrisk");
		e.getFieldKeys().add("yd_cancelentry.yd_cancelreason");
		e.getFieldKeys().add("yd_cancelentry.yd_createtime");
		e.getFieldKeys().add("yd_cancelentry.yd_billstatus");
	}

	@Override
	public void onAddValidators(AddValidatorsEventArgs e) {
		e.addValidator(new RssChangeAuditValidate());
	}

	public void endOperationTransaction(EndOperationTransactionArgs e) {
		super.endOperationTransaction(e);
		// update by hst 2024/04/11 适配包材合格供应商目录
		String opKey = e.getOperationKey();
		String entityName = EFFECT_RAW_OP.equals(opKey) ? "yd_rawsupsumbill" : "yd_packsupsumbill";
		DynamicObject[] datas = e.getDataEntities();
		for (DynamicObject ov : datas) {
			String srcBillId = ov.getString("yd_srcbillid");
			DynamicObject srcInfo = BusinessDataServiceHelper.loadSingle(srcBillId, entityName);

			if (ov.getBoolean("yd_isstop")) {
				srcInfo.set("yd_isstop", true);
				srcInfo.set("yd_stopbillid", ov.getString("id"));
			}
			DynamicObjectCollection entrys = ov.getDynamicObjectCollection("entryentity");
			for (DynamicObject entryInfo : entrys) {
				String field = entryInfo.getString("yd_fieldname");
				String type = entryInfo.getString("yd_infotype");
				String value = entryInfo.getString("yd_newvalue");
				String valueEx = entryInfo.getString("yd_newvalueex");
				setValue(srcInfo, field, type, value, valueEx);
				/* 生成变更记录 */
				saveChangeRecord(srcInfo, entryInfo);
			}
			/* 取消记录 */
			saveCancelRecord(srcInfo, ov);
			SaveServiceHelper.save(new DynamicObject[]{srcInfo});
			ov.set("yd_datastatus", "B");
			SaveServiceHelper.save(new DynamicObject[]{ov});

			// update by hst 2024/04/11 原辅料合格供应商目录变更才需更新货源清单
			if (EFFECT_RAW_OP.equals(opKey)) {
				// 如果变更的是准入节点并且变更为暂停采购，则需要将对应的货源清单也进行禁用
				String potNum = srcInfo.getString("yd_supplieraccesspot.number");

				if (Objects.nonNull(srcInfo.get("yd_supplieraccesspot"))) {
					DynamicObject supStatus = BusinessDataServiceHelper.loadSingle(srcInfo.getString("yd_supplieraccesspot.group.id"), "yd_supplierstatus");
					DynamicObject materialInfo = srcInfo.getDynamicObject("yd_material");
					//对主供应商的货源清单进行反核准操作,yzw
					Long bizPartnerId = new BizPartnerBizHelper().getBizPartnerIdBySrmSupplierId(GeneralFormatUtils.getString(srcInfo.getString("yd_agent.id")));
					if (bizPartnerId != null) {
						DynamicObject[] bdSupplierColl = BusinessDataServiceHelper.load("bd_supplier", "id,number,name",
								new QFilter[]{new QFilter("bizpartner", QCP.equals, bizPartnerId)});
						DynamicObject bdSupplier = bdSupplierColl.length > 0 ? bdSupplierColl[0] : null;
						if (bdSupplier != null && bdSupplier.getPkValue() != null
								&& materialInfo != null && materialInfo.getPkValue() != null) {
							// 获取货源清单
							DynamicObject supplylistObj = new SupplyListBizHelper().getSupplyList(GeneralFormatUtils.getString(bdSupplier.getPkValue()),
									GeneralFormatUtils.getString(materialInfo.getPkValue()));
							if ("115".equals(potNum) || "120".equals(potNum) || "105".equals(potNum) || "110".equals(potNum) || potNum.contains("120-")) {
								if (supplylistObj != null) {
									// 如果是核准状态则重置为暂存状态
									if (supplylistObj.getString("yd_isuseable").equalsIgnoreCase("1")) {
										supplylistObj.set("yd_issyn", false);
										supplylistObj.set("yd_isuseable", "0");
										supplylistObj.set("yd_supplierstatus", supStatus);
										supplylistObj.set("enable", "0");
									}
								}
							} else if ("90".equals(potNum) || "95".equals(potNum) || "100".equals(potNum) || potNum.contains("90-")) {
								//对主供应商的货源清单进行核准操作，如果不存在需要创建货源清单,yzw
								if (supplylistObj == null) {
									DynamicObject orgInfo = (DynamicObject) srcInfo.get("org");
									String orgNumber = orgInfo != null && !StringUtils.isEmpty(orgInfo.getString("number")) ? orgInfo.getString("number") : "00003";
									new SupplyListBizHelper().createSupplyListInfo(GeneralFormatUtils.getString(materialInfo.getPkValue()),
											GeneralFormatUtils.getString(bdSupplier.getPkValue()),
											orgNumber);
								} else {
									// 如果是保存状态则赋值为核准状态并且设置为未同步EAS
									if (supplylistObj.getString("yd_isuseable").equalsIgnoreCase("0") || supplylistObj.get("yd_isuseable") == null) {
										supplylistObj.set("yd_issyn", false);
										supplylistObj.set("yd_isuseable", "1");
										supplylistObj.set("yd_supplierstatus", supStatus);
										supplylistObj.set("enable", "1");
									}
								}
							}

							// update by hst 2024/03/04 为空至无需保存
							if (Objects.nonNull(supplylistObj)) {
								SaveServiceHelper.save(new DynamicObject[]{supplylistObj});
							}
						}
					}
				}
			}
		}
	}

	void setValue(DynamicObject srcInfo, String field, String type, String value, String valueEx) {
		if (StringUtils.isEmpty(value)) {
			srcInfo.set(field, null);
			return;
		}

		if ("Z".equals(type)) {//基础:Z
			if (StringUtils.isNotEmpty(valueEx)) {
				try {
					Map exMap = JSONUtils.cast(valueEx, Map.class);
					String bdId = (String) exMap.get("value");
					String entType = (String) exMap.get("enttype");
					if (StringUtils.isNoneBlank(entType)) {
						srcInfo.set(field, BusinessDataServiceHelper.loadSingle(bdId, entType));

					} else {
						srcInfo.set(field, bdId);
					}
				} catch (IOException e) {
					e.printStackTrace();
				}

			}
		} else if ("D".equals(type)) {//日期:D
			srcInfo.set(field, DateUtil.string2date(value, "yyyy-MM-dd"));
		} else if ("B".equals(type)) {//布尔:B
			srcInfo.set(field, "是".equals(value) || "1".equals(value));
		} else if ("L".equals(type)) {//正数
			srcInfo.set(field, Long.parseLong(value));
		} else if ("N".equals(type)) {//数字
			srcInfo.set(field, new BigDecimal(value));
		} else {
			srcInfo.set(field, value);
		}
	}

	/**
	 * 生成变更记录
	 * @param srcInfo
	 * @param changeInfo
	 * @author: hongsitao
	 * @createDate: 2024/12/13
	 */
	private void saveChangeRecord (DynamicObject srcInfo, DynamicObject changeInfo) {
		DynamicObjectCollection entries = srcInfo.getDynamicObjectCollection("yd_changeentity");
		/* 已有记录 */
		Set<String> oriEnrtyIds = entries.stream().map(entry -> entry.getString("yd_orientryid"))
				.collect(Collectors.toSet());
		if (!oriEnrtyIds.contains(changeInfo.getString("id"))) {
			DynamicObject entry = entries.addNew();
			entry.set("yd_infotype", changeInfo.get("yd_infotype"));
			entry.set("yd_chgfield", changeInfo.get("yd_chgfield"));
			entry.set("yd_oldvalue", changeInfo.get("yd_oldvalue"));
			entry.set("yd_newvalue", changeInfo.get("yd_newvalue"));
			entry.set("yd_note", changeInfo.get("yd_note"));
			entry.set("yd_datastatus", "B");
			entry.set("yd_changetime", new Date());
			entry.set("yd_orientryid", changeInfo.getString("id"));
		}
	}
	/**
	 * 生成取消记录
	 * @param srcInfo
	 * @param changeInfo
	 * @author: hongsitao
	 * @createDate: 2024/12/13
	 */
	private void saveCancelRecord (DynamicObject srcInfo, DynamicObject changeInfo) {
		DynamicObjectCollection entries = srcInfo.getDynamicObjectCollection("yd_cancelentry");
		/* 已有记录 */
		Set<String> oriBillIds = entries.stream().map(bill -> bill.getString("yd_oribillid"))
				.collect(Collectors.toSet());
		String cancelItem = changeInfo.getString("yd_cancelitem");
		String cancelMatCode = changeInfo.getString("yd_cancelmatcode");
		String assessRisk = changeInfo.getString("yd_assessrisk");
		String reason = changeInfo.getString("yd_cancelreason");
		if (!oriBillIds.contains(changeInfo.getString("id")) && (StringUtils.isNotBlank(cancelItem)
				|| StringUtils.isNotBlank(cancelMatCode) || StringUtils.isNotBlank(assessRisk)
				|| StringUtils.isNotBlank(reason))) {
			DynamicObject entry = entries.addNew();
			entry.set("yd_cancelitem", cancelItem);
			entry.set("yd_cancelmatcode", cancelMatCode);
			entry.set("yd_assessrisk", assessRisk);
			entry.set("yd_cancelreason", reason);
			entry.set("yd_billstatus", "C");
			entry.set("yd_createtime", new Date());
			entry.set("yd_oribillid", changeInfo.getString("id"));
		}
	}
}

class RssChangeAuditValidate extends AbstractValidator
{
	@Override
	public void validate() 
	{
		
		ExtendedDataEntity[] datas = this.getDataEntities();
		for(ExtendedDataEntity dataEntity : datas)
		{
			DynamicObject ov=dataEntity.getDataEntity();
			 if("B".equals(ov.getString("yd_datastatus")))
			 {
			    this.addErrorMessage(dataEntity, "变更已生效，不允许操作");
			 }
		}
	}
	 
}
