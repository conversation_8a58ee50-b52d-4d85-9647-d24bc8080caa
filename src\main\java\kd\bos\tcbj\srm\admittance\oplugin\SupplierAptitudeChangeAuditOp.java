package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 供应商资质变更确认单审批插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-29
 */
public class SupplierAptitudeChangeAuditOp extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        e.getFieldKeys().add("billno");
        e.getFieldKeys().add("yd_bizdate");
        e.getFieldKeys().add("yd_supplier");
        e.getFieldKeys().add("yd_user");
        e.getFieldKeys().add("entryentity.yd_aptitudetype");
        e.getFieldKeys().add("entryentity.yd_oldaptitudeattr");
        e.getFieldKeys().add("entryentity.yd_ischange");
        e.getFieldKeys().add("entryentity.yd_newaptitudeattr");
    }

    @Override
    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {

        String opKey = e.getOperationKey();
        if (StringUtils.equals(opKey, "audit")) {
            DynamicObject[] infos = e.getDataEntities();
            // 遍历分录中的“两年内是否有变更”，如果有，则将供应商新上传的资质附件保存到供应商库的资质文件中，并按资质分类存放
            Map<String, DynamicObjectCollection> attachMap = new LinkedHashMap<>();
            for (DynamicObject info : infos) {
                DynamicObjectCollection entryCol = info.getDynamicObjectCollection("entryentity");
                for (DynamicObject entryInfo : entryCol) {
                    // 资质类型
                    String aptitudeType = entryInfo.getString("yd_aptitudetype");
                    // 是否两年内调整
                    boolean isChange = entryInfo.getBoolean("yd_ischange");
                    // 新的资质文件
                    DynamicObjectCollection attachCol = entryInfo.getDynamicObjectCollection("yd_newaptitudeattr");
                    if (isChange) {
                        // 将资质附件保存到供应商库的资质文件中
                        attachMap.put(aptitudeType, attachCol);
                    }
                }
            }
            // TODO 存放到供应商资质文件中保存 …… 20220929
        }
    }
}
