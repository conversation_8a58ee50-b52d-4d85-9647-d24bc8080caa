package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.exception.KDBizException;
import kd.bos.tcbj.im.helper.AttachHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 供应商研发新品单审核功能
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-5
 */
public class SupplierDevelopNPAuditOp extends AbstractOperationServicePlugIn {

    /**
     * 操作执行，加载单据数据包之前，触发此事件；
     *
     * @remark
     * 在单据列表上执行单据操作，传入的是单据内码；
     * 系统需要先根据传入的单据内码，加载单据数据包，其中只包含操作要用到的字段，然后再执行操作；
     * 在加载单据数据包之前，操作引擎触发此事件；
     *
     * 插件需要在此事件，添加需要用到的字段；
     * 否则，系统加载的单据数据包，可能没有插件要用到的字段值，从而引发中断
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> fieldKeys = e.getFieldKeys();
        // 添加 供应商、新元素名称、品牌、原文关键内容、单据ID
        fieldKeys.add("yd_supplier");
        fieldKeys.add("yd_elename");
        fieldKeys.add("yd_brand");
        fieldKeys.add("yd_sourcecontent_bg");
        fieldKeys.add("id");
//        // 添加附件字段
//        fieldKeys.add("yd_attachment"); // 页签【相关资料】——附件
//        fieldKeys.add("yd_referenceattr"); // 页签【元素介绍】——相关文献附件
//        fieldKeys.add("yd_experimentattr"); //页签【元素介绍】——实验附件
//        fieldKeys.add("yd_productattr"); // 页签【使用同原料/同功效已上市产品】——附件
//        fieldKeys.add("yd_remarkattr"); // 页签【其他信息】——补充附件
    }

    @Override
    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
        // 获取处理的单据
        DynamicObject[] infos = e.getDataEntities();

        // 循环遍历
        for (DynamicObject info : infos) {
            // 审批通过后
            // 根据供应商研发新品单的供应商更新到对应供应商库的“产品与服务”页签下的“产品与服务分录”
            // 新增一行分录，字段取数逻辑：
            //      产品名称-取新元素名称
            //      品牌/规格-取品牌
            //      详细描述-取原文关键内容
            //      附件-取附件
            //      研发新品单F7（需要新加字段）-取单据ID
            long supplierId = info.getLong("yd_supplier.id");
            DynamicObject supplierInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_SRM_SUPPLIER, supplierId);
            // 新建分录赋值
            DynamicObjectCollection supplierEntryCol = supplierInfo.getDynamicObjectCollection("entry_goods");
            DynamicObject supplierEntryInfo = supplierEntryCol.addNew();
            supplierEntryInfo.set("goodsnumber", info.getString("yd_elename")); // 产品编码-取新元素名称
            supplierEntryInfo.set("goodsname", info.getString("yd_elename")); // 产品名称-取新元素名称
            supplierEntryInfo.set("goodsmodel", info.getString("yd_brand")); // 品牌/规格-取品牌
//            supplierEntryInfo.set("goodsdesc", info.getString("yd_sourcecontent_bg")); // 详细描述-取原文关键内容（待确认）
            supplierEntryInfo.set("yd_supplierdevnpno", info.getString("billno")); // 供应商研发 -- 单据编码

            // 从供应商研发新品单中获取所有附件信息，放入到供应商库中
            List<DynamicObject> attachList = AttachHelper.getAttachFieldCollection(BillTypeHelper.BILLTYPE_SUPPLIERDEVELOPNP, info.getPkValue()
                    , new String[]{"yd_attachment", "yd_referenceattr", "yd_experimentattr", "yd_productattr", "yd_remarkattr"});

            DynamicObjectCollection attachEntryCol = supplierEntryInfo.getDynamicObjectCollection("goodsattachmentfield");

            if (attachList != null && attachList.size() > 0) {
                for (DynamicObject attachInfo : attachList) {
                    // 从ID重新获取数据
//                    DynamicObject tempInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_BD_ATTACHMENT, attachInfo.getDynamicObject("fbasedataId").get("id"));
                    attachEntryCol.addNew().set("fbasedataid", attachInfo.getDynamicObject("fbasedataId"));
                }
            }
            // 保存数据
            OperationResult result = BizHelper.saveDynamicObject(supplierInfo);
            String errorMessage = BizHelper.getOperationErrorMessage(result);
            if (StringUtils.isNotBlank(errorMessage)) {
                throw new KDBizException("供应商反写出错"+errorMessage);
            }
        }
    }
}
