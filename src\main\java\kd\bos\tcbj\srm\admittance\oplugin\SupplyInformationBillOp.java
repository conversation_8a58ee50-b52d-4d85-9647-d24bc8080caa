package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.exception.KDBizException;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.admittance.helper.NewMatReverseHelper;
import kd.bos.tcbj.srm.admittance.helper.SrmSupplierAttHelper;
import kd.bos.tcbj.srm.admittance.imp.SupplyInformationBillMserviceImpl;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 资料补充函操作插件
 * @auditor liefengWu
 * @date 2022年6月7日
 * 
 */
public class SupplyInformationBillOp  extends AbstractOperationServicePlugIn {
	
	/**
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_supplier");
		
		e.getFieldKeys().add("billstatus");
		e.getFieldKeys().add("yd_entryfilemodel");
		e.getFieldKeys().add("yd_entryfilemodel.yd_attachmenttype");
	}
	
	
	/**
	 * 描述：操作前执行校验
	 */
	@Override
	public void onAddValidators(AddValidatorsEventArgs e) {
		super.onAddValidators(e);
		e.addValidator(new SupplyInformationValidator());
	}
	
	/**
	 * 操作校验通过，开启了事务之后，还没有把数据提交到数据库之前触发此事件；
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		
		String opKey=e.getOperationKey();
		if(StringUtil.equalsIgnoreCase("submit", opKey)
				 || 
					StringUtil.equalsIgnoreCase("submitandnew", opKey)
				) {//提交功能触发
			//采购方提交单据后单据反写为待补充状态，并发送短信、邮件和系统通知（系统通知需要点进去处理单据的那种）给供应商
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0
					&& dynamicObjectArray[0] != null) {
				DynamicObject dynamicObject = dynamicObjectArray[0];
				boolean isBizParnter = new BizPartnerBizHelper().isBizPartnerCurrentUser();
				dynamicObject.set("billstatus", isBizParnter ? "E" : "B");//如果为  商务伙伴提交赋值为 未审核  反之赋值为待补充资料
				if(isBizParnter) {
					dynamicObject.set("yd_repulsereason", null);
				}
			}
		}else if(StringUtil.equalsIgnoreCase("supintoauditpoint", opKey)) {//进入供应商审核节点  将单据状态置为待补充资料
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0
					&& dynamicObjectArray[0] != null) {
				DynamicObject dynamicObject = dynamicObjectArray[0];
				DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(dynamicObject.getPkValue(), "yd_supplyinformationbill");
				billInfo.set("billstatus", "B");
				SaveServiceHelper.save(new DynamicObject[] {billInfo});
			}
		}else if(StringUtil.equalsIgnoreCase("purcherintoauditpoint", opKey)) {//进入采购方审核节点  将单据状态置为待补充资料
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0
					&& dynamicObjectArray[0] != null) {
				DynamicObject dynamicObject = dynamicObjectArray[0];
				DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(dynamicObject.getPkValue(), "yd_supplyinformationbill");
				billInfo.set("billstatus", "E");
				SaveServiceHelper.save(new DynamicObject[] {billInfo});
			}
		}else if(StringUtil.equalsIgnoreCase("supplierwithdraw", opKey)) {//供应商打回
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0
					&& dynamicObjectArray[0] != null) {
				DynamicObject dynamicObject = dynamicObjectArray[0];
				String billstatus = GeneralFormatUtils.getString(dynamicObject.get("billstatus"));
				if(!"E".equals(billstatus)) {
					throw new KDBizException("仅能撤回待审核数据，请核实！");
				}
				DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(dynamicObject.getPkValue(), "yd_supplyinformationbill");
				billInfo.set("billstatus", "B");
				SaveServiceHelper.save(new DynamicObject[] {billInfo});
			}
		}else if(StringUtil.equalsIgnoreCase("purchaserwithdraw", opKey)) {//采购方撤回
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0
					&& dynamicObjectArray[0] != null) {
				DynamicObject dynamicObject = dynamicObjectArray[0];
				String billstatus = GeneralFormatUtils.getString(dynamicObject.get("billstatus"));
				if(!"B".equals(billstatus)) {
					throw new KDBizException("仅能撤回待补充资料数据，请核实！");
				}
				DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(dynamicObject.getPkValue(), "yd_supplyinformationbill");
				billInfo.set("billstatus", "A");
				SaveServiceHelper.save(new DynamicObject[] {billInfo});
			}
		}else if(StringUtil.equalsIgnoreCase("confirmBill", opKey)) {//审批通过
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0
					&& dynamicObjectArray[0] != null) {
				DynamicObject dynamicObject = dynamicObjectArray[0];
				DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(dynamicObject.getPkValue(), "yd_supplyinformationbill");
				String billstatus = GeneralFormatUtils.getString(billInfo.get("billstatus"));
				if(!"E".equals(billstatus)) {
					throw new KDBizException("仅能确认已补充状态资料数据，请核实！");
				}
				billInfo.set("billstatus", "C");
				SaveServiceHelper.save(new DynamicObject[] {billInfo}); 
			}
		}
	}
	
	/**
	 * 操作执行完毕，事务提交之后，触发此事件；
	 */
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);	
		String opKey=e.getOperationKey();
		//如果为采购方提交  则发送短信通知供应商
		boolean isBizParnter = new BizPartnerBizHelper().isBizPartnerCurrentUser();
		if(StringUtil.equalsIgnoreCase("submit", opKey) || 
				StringUtil.equalsIgnoreCase("submitandnew", opKey)
				) {//提交功能后触发
			if(!isBizParnter) {//采购方提交
				DynamicObject[] dynamicObjectArray = e.getDataEntities();
				if(dynamicObjectArray != null && dynamicObjectArray.length > 0
						&& dynamicObjectArray[0] != null) {
					DynamicObject dynamicObject = dynamicObjectArray[0];
				//	new SupplyInformationBillMserviceImpl().getInstance().sendFillInSupplyInformateToSupplier(dynamicObject.getPkValue().toString());
				}
			}else {//供应商触发提交  发送信息给采购方审核
				DynamicObject[] dynamicObjectArray = e.getDataEntities();
				if(dynamicObjectArray != null && dynamicObjectArray.length > 0
						&& dynamicObjectArray[0] != null) {
					DynamicObject dynamicObject = dynamicObjectArray[0];
				//	new SupplyInformationBillMserviceImpl().getInstance().sendAuditingSupplyInformateToPurchaser(dynamicObject.getPkValue().toString());
				
				}
			}
		}
//		else if(StringUtil.equalsIgnoreCase("confirmBill", opKey))
//		{//审核通过，反写
//			DynamicObject[] dynamicObjectArray = e.getDataEntities();
//			DynamicObject dynamicObject = dynamicObjectArray[0];
//			NewMatReverseHelper.reverseNewBatAttInfo(dynamicObject.getLong("id"));
//			SrmSupplierAttHelper.copyAtt4SupInfoBill(dynamicObject.getPkValue());
//		}
		else if(StringUtil.equalsIgnoreCase("copyAtt4SupInfoBill", opKey))
		{//简易流程更新附件到供应商库
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			DynamicObject dynamicObject = dynamicObjectArray[0]; 
			SrmSupplierAttHelper.copyAtt4SupInfoBill(dynamicObject.getPkValue());
		}
	}
}



/**
 * 资料补充函校验器
 * <AUTHOR>
 *
 */
class SupplyInformationValidator extends AbstractValidator
{
	@Override
	public void validate() 
	{
		
		ExtendedDataEntity[] datas = this.getDataEntities();
		for(ExtendedDataEntity dataEntity : datas)
		{
			DynamicObject ov=dataEntity.getDataEntity();
		//	DynamicObjectCollection filemodelEntryColl = ov.getDynamicObjectCollection("yd_entryfilemodel");
		
			String opKey = this.getOperateKey();
			
			if("submit".equals(opKey)
					 || 
						StringUtil.equalsIgnoreCase("bar_submitandnew", opKey)
					) {//提交逻辑校验
			/*	Map<String,Boolean> filesMap = new HashMap<String,Boolean>();
				filesMap.put("A", false);
				filesMap.put("B", false);
				for(int index = 0 ; index < filemodelEntryColl.size() ; index ++) {
					DynamicObject entryInfo = filemodelEntryColl.get(index);
					if(entryInfo == null)
						continue;
					String yd_attachmenttype = GeneralFormatUtils.getString(entryInfo.get("yd_attachmenttype"));
					if(!StringUtils.isEmpty(yd_attachmenttype)) {
						filesMap.remove(yd_attachmenttype);
					}
				}
				if(filesMap.size() > 0) {
					 this.addErrorMessage(dataEntity, "对应文件模板分录明细缺少附件类型为供应商书面调查表或物料描述表，请核实！");
				}
				*/
			}
		
		}
	}
	 
}