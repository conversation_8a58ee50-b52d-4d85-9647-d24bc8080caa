package kd.bos.tcbj.srm.admittance.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.db.DB;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.entity.SupplyInfoEntryFieldEntiy;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.admittance.oplugin.SupplyInformationOp
 * @className SupplyInformationOp
 * @author: hst
 * @createDate: 2024/04/07
 * @version: v1.0
 */
public class SupplyInformationOp extends AbstractOperationServicePlugIn {

    /** 审核操作标识 **/
    private final static String NEWMATREQ_OP = "yd_newmatreq";

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/04/07
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("yd_newmatreq");
        e.getFieldKeys().add("yd_supplier");
        e.getFieldKeys().add("yd_producers");
        e.getFieldKeys().add("yd_domesattachmenttype");
        e.getFieldKeys().add("yd_domesattachment");
        e.getFieldKeys().add("yd_domeseffectdate");
        e.getFieldKeys().add("yd_domesuneffectdate");
        e.getFieldKeys().add("yd_foreigattachmenttype");
        e.getFieldKeys().add("yd_foreigattachment");
        e.getFieldKeys().add("yd_foreigeffectdate");
        e.getFieldKeys().add("yd_foreiguneffectdate");
        e.getFieldKeys().add("yd_agentattachmenttype");
        e.getFieldKeys().add("yd_agentattachment");
        e.getFieldKeys().add("yd_agenteffectdate");
        e.getFieldKeys().add("yd_agentuneffectdate");
        e.getFieldKeys().add("yd_pstarattachmenttype");
        e.getFieldKeys().add("yd_pstarattachment");
        e.getFieldKeys().add("yd_pfitienattachmenttype");
        e.getFieldKeys().add("yd_pfitienattachment");
        e.getFieldKeys().add("yd_pstareffectdate");
        e.getFieldKeys().add("yd_pstaruneffectdate");
        e.getFieldKeys().add("yd_pfitieneffectdate");
        e.getFieldKeys().add("yd_pfitienuneffectdate");
        e.getFieldKeys().add("yd_domesattachment_re");
        e.getFieldKeys().add("yd_domesattachment_up");
        e.getFieldKeys().add("yd_foreigattachment_re");
        e.getFieldKeys().add("yd_foreigattachment_up");
        e.getFieldKeys().add("yd_agentattachment_re");
        e.getFieldKeys().add("yd_agentattachment_up");
        e.getFieldKeys().add("yd_pstarattachment_re");
        e.getFieldKeys().add("yd_pstarattachment_up");
        e.getFieldKeys().add("yd_pfitienattachment_re");
        e.getFieldKeys().add("yd_pfitienattachment_up");
    }

    /**
     * 操作执行完成，事务提交之前
     * @param e
     * @author: hst
     * @createDate: 2024/04/07
     */
    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        super.afterExecuteOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case NEWMATREQ_OP : {
                // 更新资料补充函上游研发准入单的附件
                this.updateNewMatReqBillAttachment(bills);
                break;
            }
        }
    }

    /**
     * 更新资料补充函上游研发准入单的附件
     * @param bills
     * @author: hst
     * @createDate: 2024/04/17
     */
    private void updateNewMatReqBillAttachment (DynamicObject[] bills) {
        List<DynamicObject> reqBills = new ArrayList<>();

        for (DynamicObject bill : bills) {
            String supplier = bill.getString("yd_supplier.id");
            String producer = bill.getString("yd_producers.id");
            String reqBillId = bill.getString("yd_newmatreq.id");

            if (StringUtils.isNotBlank(reqBillId)
                    && StringUtils.isNotBlank(supplier) && StringUtils.isNotBlank(producer)) {
                // 获取研发准入单
                DynamicObject reqBill = BusinessDataServiceHelper.loadSingle(reqBillId,"yd_newmatreqbill");
                // 生产商名称
                String proName = Objects.nonNull(bill.getDynamicObject("yd_producers"))
                        ? bill.getString("yd_producers.name") : "";

                if (Objects.nonNull(reqBill)) {
                    // 书面审核单据体
                    DynamicObjectCollection writtenEntries = reqBill.getDynamicObjectCollection("yd_file1entry");
                    for (DynamicObject entry : writtenEntries) {
                        String tempSupplier = entry.getString("yd_csup.id");
                        String tempProducer = entry.getString("yd_cprod.id");

                        if (supplier.equals(tempSupplier) && producer.equals(tempProducer)) {
                            // 更新研发准入单的书面审核资料附件
                            this.updateNewMatReqBillWrittenAttach(proName, entry, bill);
                        }
                    }

                    // update by hst 2024/10/22 新版附件管理
                    String version = reqBill.getString("yd_version");
                    reqBill.set("yd_version", "1");

                    if (!"1".equals(version)) {
                        DynamicObjectCollection fileEntries = reqBill.getDynamicObjectCollection("yd_file1entry");
                        DynamicObjectCollection pilotEntries = reqBill.getDynamicObjectCollection("yd_pilotentry");
                        DynamicObjectCollection admitEntries = reqBill.getDynamicObjectCollection("yd_admitentity");

                        if (pilotEntries.size() == 0) {
                            for (DynamicObject fileEntry : fileEntries) {
                                DynamicObject pilotEntry = pilotEntries.addNew();
                                pilotEntry.set("seq", pilotEntries.size());
                                pilotEntry.set("yd_pilotname", fileEntry.get("yd_cnewsupname"));
                                pilotEntry.set("yd_pilotsup", fileEntry.get("yd_csup"));
                                pilotEntry.set("yd_pilotpro", fileEntry.get("yd_cprod"));
                            }
                        }

                        if (admitEntries.size() == 0) {
                            for (DynamicObject fileEntry : fileEntries) {
                                DynamicObject admitEntry = admitEntries.addNew();
                                admitEntry.set("seq", admitEntries.size());
                                admitEntry.set("yd_admitproname", fileEntry.get("yd_cnewsupname"));
                                admitEntry.set("yd_admitsup", fileEntry.get("yd_csup"));
                                admitEntry.set("yd_admitpro", fileEntry.get("yd_cprod"));
                            }
                        }
                    }
                }

                reqBills.add(reqBill);
            }
        }

        SaveServiceHelper.save(reqBills.toArray(new DynamicObject[reqBills.size()]));
    }

    /**
     * 更新研发准入单的书面审核资料附件
     * @param proName 生产商名称
     * @param entry 研发准入单书面审核分录行
     * @param inform 资料补充函
     * @author: hst
     * @createDate: 2024/04/07
     */
    private void updateNewMatReqBillWrittenAttach (String proName, DynamicObject entry, DynamicObject inform) {
        List<SupplyInfoEntryFieldEntiy> zzEntryAttachFieldList = AdminAttachHelper.getFieldMap();
        for(SupplyInfoEntryFieldEntiy filedEntity :zzEntryAttachFieldList) {//处理对应目标附件字段处理逻辑
            if (filedEntity == null) {
                continue;
            }

            String entryKey = filedEntity.getEntryEntityName();
            /* 目标单附件分录 */
            DynamicObjectCollection mainKeyColl = entry.getDynamicObjectCollection(entryKey);
            Set<String> existIds = mainKeyColl.stream().map(temp -> temp.getString(filedEntity.getOriEntryIdField())).collect(Collectors.toSet());
            /* 源单附件分录 */
            DynamicObjectCollection fileEntryColl = inform.getDynamicObjectCollection(entryKey);

            if (fileEntryColl != null && fileEntryColl.size() > 0) {
                for (int index = 0; index < fileEntryColl.size(); index++) {
                    DynamicObject fileEntryInfo = fileEntryColl.get(index);

                    if (existIds.contains(fileEntryInfo.getString("id"))) {
                        continue;
                    }

                    DynamicObject tarEntry = mainKeyColl.addNew();
                    /* 资质类型 */
                    tarEntry.set(filedEntity.getFileTypeField(), fileEntryInfo.get(filedEntity.getFileTypeField()));
                    /* 发证日期 */
                    tarEntry.set(filedEntity.getEffectField(), fileEntryInfo.get(filedEntity.getEffectField()));
                    /* 有效日期 */
                    tarEntry.set(filedEntity.getUnEffectField(), fileEntryInfo.get(filedEntity.getUnEffectField()));
                    /* 原单据ID */
                    tarEntry.set(filedEntity.getOriBillIdField(), inform.getString("id"));
                    /* 是否已重命名 */
                    tarEntry.set(filedEntity.getRenameField(), fileEntryInfo.get(filedEntity.getRenameField()));
                    /* 是否归档 */
                    tarEntry.set(filedEntity.getIsUploadField(), fileEntryInfo.get(filedEntity.getIsUploadField()));
                    /* 原分录ID */
                    tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.getString("id"));
                    /* 原分录ID */
                    tarEntry.set(filedEntity.getBillType(), "yd_supinfobase");
                    /* 序号 */
                    tarEntry.set("seq", index + 1);

                    /* 获取对应分录附件字段 多选基础资料数据 */
                    DynamicObjectCollection fileAttachColl = fileEntryInfo.getDynamicObjectCollection(filedEntity.getFileField());
                    if (fileAttachColl == null || fileAttachColl.size() == 0)
                        continue;

                    DynamicObjectCollection tarAttachColl = tarEntry.getDynamicObjectCollection(filedEntity.getFileField());
                    for (int jIndex = 0; jIndex < fileAttachColl.size(); jIndex++) {
                        DynamicObject att_bd = fileAttachColl.get(jIndex);//原附件
                        DynamicObject bd = att_bd.getDynamicObject("fbasedataid");//复制附件

                        //创建附件字段信息  (对应的是附件字段的表结构)
                        DynamicObject attField = tarAttachColl.addNew();
                        attField.set("fbasedataid", bd);
                    }
                }
            }
        }
    }
}
