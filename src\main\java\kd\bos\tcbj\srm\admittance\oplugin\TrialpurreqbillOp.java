package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.ABillServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.SupplierBizHelper;
import kd.bos.tcbj.srm.admittance.helper.SupplyListBizHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 试产采购申请单  服务端自定义插件
 * @auditor liefengWu
 * @date 2022年6月14日
 * 
 */
public class TrialpurreqbillOp extends AbstractOperationServicePlugIn {

	
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_supplier");
		e.getFieldKeys().add("org");
		e.getFieldKeys().add("yd_rawmatsupaccess");
		e.getFieldKeys().add("entryentity");
		e.getFieldKeys().add("entryentity.yd_entrymaterial");//物料
		
	}
	
	/**
	 * 操作校验
	 */
	@Override
	public void onAddValidators(AddValidatorsEventArgs e) {
		super.onAddValidators(e);
		e.addValidator(new TrialPurReqBillCheckValidate());
	}
	
	@Override
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		super.beforeExecuteOperationTransaction(e);
		String opKey = e.getOperationKey();//操作value
		if("createtempsup".equals(opKey)) {//创建供应商操作，原本是审核后执行，现在单独出来一个操作,yzw
			DynamicObject[] dynamicObjectArray = e.getDataEntities();
			if(dynamicObjectArray != null && dynamicObjectArray.length > 0) {
				for(int index = 0 ; index < dynamicObjectArray.length ; index ++) {
					//校验是否存在临时供应商  如果没，新增临时供应商数据
					DynamicObject info = dynamicObjectArray[index];
					if(info.get("yd_supplier") != null){
						//判断是否存在临时供应商数据，如果存在，则判断是否禁用，如为禁用进行启用处理
						DynamicObject srmSupplier = (DynamicObject) info.get("yd_supplier");
						DynamicObject bdSupplierInfo = null;
						DynamicObject org = (DynamicObject) info.get("org");
						
						if(org != null && org.getPkValue() != null) {
							org = BusinessDataServiceHelper.loadSingle(org.getPkValue(), "bos_adminorg", "id,number,name");
						}
						
						//获取对应准入单的供应商分类
						Map<String, HashSet<Long>> srcBills = BFTrackerServiceHelper
								.findSourceBills("yd_trialpurreqbill", new Long[]{
										GeneralFormatUtils.getLong(info.getPkValue())
								});
						String supplierGroupNumber = "waitgroup";//供应商组别
						if(srcBills.get("yd_rawmatsupaccessbill") != null && srcBills.get("yd_rawmatsupaccessbill").size() > 0) {
							String srcBillId = null;
							for(Long key : srcBills.get("yd_rawmatsupaccessbill")) {
								srcBillId = GeneralFormatUtils.getString(key);
								break;
							}
							DynamicObject srcBillInfo =  BusinessDataServiceHelper.loadSingle(srcBillId, "yd_rawmatsupaccessbill", "id,billno,yd_suppliergroup");
							if(srcBillInfo != null && srcBillInfo.get("yd_suppliergroup") != null) {
								DynamicObject suppliergroup = (DynamicObject) srcBillInfo.get("yd_suppliergroup");
								if(suppliergroup != null && !StringUtils.isEmpty(suppliergroup.getString("number"))) {
									supplierGroupNumber = GeneralFormatUtils.getString(suppliergroup.getString("number"));
								}
							}
						}
						
						
						
						if(srmSupplier != null && srmSupplier.getPkValue() != null) {
							srmSupplier = BusinessDataServiceHelper.loadSingle(srmSupplier.getPkValue(), "srm_supplier");
							bdSupplierInfo = new SupplierBizHelper().getTempBdSupplier(GeneralFormatUtils.getString(srmSupplier.get("societycreditcode")));
							
							if(bdSupplierInfo == null) {//如无存在临时供应商数据   则进行 创建临时供应商数据
								bdSupplierInfo = new SupplierBizHelper().createBdSupplier(
													srmSupplier.getString("name")+"_新增评估中", null, srmSupplier.getString("societycreditcode"), supplierGroupNumber, 
													org != null ? org.getString("number") : null //创建组织赋值
															, null) ;
							}
							
							
							if(bdSupplierInfo != null && !"1".equals(bdSupplierInfo.getString("enable"))) {//如果对应供应商禁用  对供应商进行启用
								ABillServiceHelper.executeOperate("enable", "bd_supplier",
															new Object[] {bdSupplierInfo.getPkValue()}, OperateOption.create());
							}
							
							
							Set<String> materialIds = new HashSet<String>();
							DynamicObjectCollection entryColl = info.getDynamicObjectCollection("entryentity");
							if(entryColl != null && entryColl.size() > 0) {
								//yd_entrymaterial
								for(int jIndex = 0 ; jIndex < entryColl.size() ; jIndex ++) {
									DynamicObject entryInfo = entryColl.get(jIndex);
									if(entryInfo == null && entryInfo.get("yd_entrymaterial") == null)
										continue;
									DynamicObject materialInfo = (DynamicObject) entryInfo.get("yd_entrymaterial") ;
									if(materialInfo != null && materialInfo.getPkValue() != null) {
										materialIds.add(GeneralFormatUtils.getString(materialInfo.getPkValue()));
									}
								}
							}
							//主数据供应商存在则执行后续货源清单逻辑处理
							if(bdSupplierInfo != null && materialIds.size() > 0) {
								String bdSupplierId = GeneralFormatUtils.getString(bdSupplierInfo.getPkValue());
								for(String materialId : materialIds) {
									DynamicObject supplyListInfo = new SupplyListBizHelper().getSupplyList(bdSupplierId, materialId);
									if(supplyListInfo != null && "0".equals(supplyListInfo.getString("yd_isuseable"))) {//二开货源清单存在且为暂存  进行核准处理
										QFilter stateF = new QFilter("number",QCP.equals,"05");
										DynamicObject[] supStates = BusinessDataServiceHelper.load("yd_supplierstatus", "id,name,number", stateF.toArray());
										supplyListInfo.set("yd_issyn", false);
										supplyListInfo.set("yd_isuseable", "1");
										if (supStates.length > 0) {
											supplyListInfo.set("yd_supplierstatus", supStates[0]);
										}
										SaveServiceHelper.save(new DynamicObject[] {supplyListInfo});
									}else if(supplyListInfo == null){//新增二开货源清单数据
										supplyListInfo = new SupplyListBizHelper().createSupplyListInfo(materialId, 
												GeneralFormatUtils.getString(bdSupplierInfo.getPkValue()), 
												org != null ? org.getString("number") : null );
									}
								}
							}
						}
					}
				}	
			}
		}
		
		if("audit".equals(opKey)) {//审核后反写原辅料准入单分录,yzw
			DynamicObject[] bills = e.getDataEntities();
			for (DynamicObject bill : bills) {
				DynamicObject supaccessInfo = bill.getDynamicObject("yd_rawmatsupaccess");  // 原辅料准入单
				DynamicObjectCollection enCol = bill.getDynamicObjectCollection("entryentity");
				DynamicObject matInfo = null;
				if (enCol.size() > 0) {
					matInfo = enCol.get(0).getDynamicObject("yd_entrymaterial");
				}
				if(supaccessInfo != null && supaccessInfo.getPkValue() != null) {
					// 准入单
					supaccessInfo = BusinessDataServiceHelper.loadSingle("" + supaccessInfo.getPkValue(), "yd_rawmatsupaccessbill");
					DynamicObjectCollection matpurchaseCol = supaccessInfo.getDynamicObjectCollection("yd_matpurchaseentry");
					DynamicObject existEnObj = null;
					for (int i = 0; i < matpurchaseCol.size(); i++) {
						String matpurchaseid = matpurchaseCol.get(i).getString("yd_purchasebill.id");
						if (StringUtils.equals(matpurchaseid, ""+bill.getPkValue())) {
							existEnObj = matpurchaseCol.get(i);
						}
					}
					boolean isAddNew = existEnObj == null;
					DynamicObjectType type = matpurchaseCol.getDynamicObjectType();
					existEnObj = existEnObj != null ? existEnObj : new DynamicObject(type);
					existEnObj.set("yd_purchasebill", ""+bill.getPkValue());
					existEnObj.set("yd_purchasemat", matInfo);
					if(isAddNew)
						matpurchaseCol.add(existEnObj);
					SaveServiceHelper.save(new DynamicObject[] { supaccessInfo });
				}
			}
		}
		
		if("delete".equals(opKey)) {//删除后反写原辅料准入单分录,yzw
			DynamicObject[] bills = e.getDataEntities();
			for (DynamicObject bill : bills) {
				if (bill.getDynamicObject("yd_rawmatsupaccess") == null) return;
				DynamicObject supaccessInfo = BusinessDataServiceHelper.loadSingle(bill.getDynamicObject("yd_rawmatsupaccess").getPkValue(), "yd_rawmatsupaccessbill");
				// 删除准入单试产业务分录
				if (supaccessInfo != null) {
					DynamicObject rawMatBill = BusinessDataServiceHelper.loadSingle(supaccessInfo.get("id"), "yd_rawmatsupaccessbill");
					DynamicObjectCollection purEnCol = rawMatBill.getDynamicObjectCollection("yd_matpurchaseentry");
					for (int i=0;i<purEnCol.size();i++) {
						if (bill.getString("id").equalsIgnoreCase(purEnCol.get(i).getString("yd_purchasebill.id"))) {
							purEnCol.remove(i);
							i=0;
						}
					}
					SaveServiceHelper.save(new DynamicObject[] {rawMatBill});
				}
			}
		}
	}
}

/**
 * 试产采购申请单服务端校验器
 * <AUTHOR>
 *
 */
class TrialPurReqBillCheckValidate extends AbstractValidator
{
	@Override
	public void validate() 
	{
		
		ExtendedDataEntity[] datas = this.getDataEntities();
		String opKey = this.getOperateKey();
		
		for(ExtendedDataEntity dataEntity : datas)
		{
			DynamicObject ov = dataEntity.getDataEntity();
			String billno = (String)ov.getString("billno");
		
			if("submit".equals(opKey)) {
				//校验供应商分录是否仅存在一条数据供应商类型为新的数据
				DynamicObject supaccessInfo =  ov.getDynamicObject("yd_rawmatsupaccess");
				if(supaccessInfo != null && supaccessInfo.getPkValue() != null ) {
					
					String billId = GeneralFormatUtils.getString(supaccessInfo.getPkValue());
					supaccessInfo = BusinessDataServiceHelper.loadSingle(billId, "yd_rawmatsupaccessbill");
		        	if(ov != null ) {
		        		  
		        		DynamicObject materialInfo = supaccessInfo.getDynamicObject("yd_material");
		        		if(materialInfo != null && materialInfo.getPkValue() != null) {
		        			String materialId = GeneralFormatUtils.getString(materialInfo.getPkValue());
		        			DynamicObjectCollection entryColl = ov.getDynamicObjectCollection("entryentity");
		        			boolean isHaveMaterialsupaccess = false;
		        			if(entryColl != null) {
		        				  for(int index = 0 ;  index < entryColl.size() ; index ++) {
			        				  DynamicObject entryInfo = entryColl.get(index);
			        				  if(entryInfo == null 
			        						  || entryInfo.getDynamicObject("yd_entrymaterial") == null
			        						  || entryInfo.getDynamicObject("yd_entrymaterial").getPkValue() == null
			        						  || isHaveMaterialsupaccess) {
			        					  continue;
			        				  }
			        				  isHaveMaterialsupaccess = materialId.equals(GeneralFormatUtils.getString(entryInfo.getDynamicObject("yd_entrymaterial").getPkValue()));
			        			  }
		        			  }
		        			  if(!isHaveMaterialsupaccess) {
		        				  this.addErrorMessage(dataEntity,"单号:"+billno+",明细未存在原辅料供应商准入单的物料数据请调整!");
		        			  }
		        		  }
		        	  }
				}
			}
			 
		}
	}
	 
}
