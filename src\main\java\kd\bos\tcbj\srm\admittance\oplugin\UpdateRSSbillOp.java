package kd.bos.tcbj.srm.admittance.oplugin;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.tcbj.srm.admittance.helper.RSSBillUpdateHelper;

/**
 * @auditor yanzuwei
 * @date 2022年7月19日
 * @desc 更新台账
 */
public class UpdateRSSbillOp extends AbstractOperationServicePlugIn
{
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
	    super.afterExecuteOperationTransaction(e);
	    String operationKey = e.getOperationKey();
	    DynamicObject[] bills = e.getDataEntities(); 
        for (DynamicObject bill : bills) 
        {
          RSSBillUpdateHelper.updateBill(bill,operationKey);
        } 
	}
}
