package kd.bos.tcbj.srm.admittance.oplugin;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.entity.validate.BillStatus;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.workflow.WorkflowServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.isc.iscb.platform.core.proxy.BusinessDataServiceHelperProxy;

/**
 * @auditor cary
 * @date 2022年6月7日
 * @desc 更新EAS供应商状态:核准并分配汤臣倍健股份公司
 * 
 */
public class UpdateSupStatusOp extends AbstractOperationServicePlugIn 
{
    @Override
	public void onPreparePropertys(PreparePropertysEventArgs e)
	{
	    e.getFieldKeys().add("supplier");  
	}
 
	 
	
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) 
	{
		super.beforeExecuteOperationTransaction(e);
		    
	}
	
	public void afterExecuteOperationTransaction(AfterOperationArgs e) 
	{
	    super.afterExecuteOperationTransaction(e);
	   // String operationKey = e.getOperationKey();
	        DynamicObject[] bills = e.getDataEntities();
	        for (DynamicObject bill : bills) 
	        {
	        	 DynamicObject srmSupInfo= bill.getDynamicObject("supplier");
	        	 if(srmSupInfo==null)
	        	 {
	        		 continue;
	        	 }
	        	 srmSupInfo= BusinessDataServiceHelper.loadSingle(srmSupInfo.getPkValue(),"srm_supplierinfo","supplier");
	        	 DynamicObject supInfo= srmSupInfo.getDynamicObject("supplier");
	        	 if(supInfo==null)
	        	 {
	        		 continue;
	        	 }
	        	 supInfo=BusinessDataServiceHelper.loadSingle(supInfo.getPkValue(), "bd_supplier","number");
	        	 String supNum=supInfo.getString("number");
	        	 Map reqMap=new HashMap(); 
	 		     reqMap.put("number",supNum); 
	 		     Object data =ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SupStatusFacade", "updateStatus", reqMap);
	 		     System.out.println("返回信息:"+data); 
	        } 
	     
	}
	
	 
	
	
	
	 
}