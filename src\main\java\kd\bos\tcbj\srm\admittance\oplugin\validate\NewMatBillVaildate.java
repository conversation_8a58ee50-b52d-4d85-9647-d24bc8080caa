package kd.bos.tcbj.srm.admittance.oplugin.validate;

import com.twelvemonkeys.util.LinkedMap;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.entity.validate.ValidationErrorInfo;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.oplugin.NewMatBillWfPlugin;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.oplugin.validate.NewMatBillVaildate
 * @className: NewMatBillVaildate
 * @author: hst
 * @createDate: 2024/10/26
 * @description: 新物料研发准入校验操作
 * @version: v1.0
 */
public class NewMatBillVaildate extends AbstractValidator {

    /* 供应链负责人提供生产商信息 */
    private final static String PROVIDE_PRODUCE = "provide_product";
    /* 研发判断是否满足物料需求 */
    private final static String RD_JUDGMENT = "rd_judgment";
    /* 原料评估供应商信息 */
    private final static String QUALITY_ANALYSIS = "quality_analysis";
    /* 研发负责人确认-填写小试物料供应商 */
    private final static String ARRANGE_SAMLLTRIAL = "arrange_smalltrial";
    /* 工艺技术部反馈小试结果 */
    private final static String TEST_FEEDBACK = "test_feedback";
    /* 研发负责人确认中试信息 */
    private final static String COMFIRM_PILOT = "confirm_pilot";
    /* 供应链负责人对供应商书面审核 */
    private final static String SCM_REVIEW = "scm_review";
    /* 书面审核 */
    private final static String QM_REVIEW = "qm_review";
    /* 预计现场审核时间 */
    private final static String ESTIMATED_TIME = "estimated_time";
    /* 现场审核批准 */
    private final static String ONSITE_AUDIT = "onsite_audit";
    /* 中试反馈 */
    private final static String PILOT_FEEDBACK = "pilot_feedback";
    /* 合格供应商目录确认审批 */
    private final static String CATALOG_CONFIRM = "catalog_confirm";
    /* 研发负责人提交样品我司质检报告 */
    private final static String SCM_SUBMITRPT = "scm_submitrpt";

    @Override
    public void validate() {
        String key = this.getOperateKey();
        ExtendedDataEntity[] extendedDataEntities = this.getDataEntities();

        switch (key) {
            case PROVIDE_PRODUCE : {
                /* 供应链负责人提供生产商信息 */
                this.provideProductValidate(extendedDataEntities);
                break;
            }
            case RD_JUDGMENT : {
                /* 研发判断是否满足物料需求 */
                this.rdJudgmentValidate(extendedDataEntities);
                break;
            }
            case QUALITY_ANALYSIS : {
                /* 原料评估供应商信息 */
                this.qualityAnalysisValidate(extendedDataEntities);
                break;
            }
            case ARRANGE_SAMLLTRIAL : {
                /* 研发负责人确认-填写小试物料供应商 */
                this.arrangeSmallTrialValidate(extendedDataEntities);
                break;
            }
            case TEST_FEEDBACK : {
                /* 工艺技术部反馈小试结果 */
                this.testFeedbackValidate(extendedDataEntities);
                break;
            }
            case COMFIRM_PILOT : {
                /* 研发负责人确认中试信息 */
                this.confirmPilotValidate(extendedDataEntities);
                break;
            }
            case SCM_REVIEW : {
                /* 供应链负责人对供应商书面审核 */
                this.scmReviewValidate(extendedDataEntities);
                break;
            }
            case QM_REVIEW : {
                /* 书面审核 */
                this.qmReviewValidate(extendedDataEntities);
                break;
            }
            case ESTIMATED_TIME : {
                /* 预计现场审核时间 */
                this.estimatedTimeValidate(extendedDataEntities);
                break;
            }
            case ONSITE_AUDIT : {
                /* 现场审核批准 */
                this.onSiteAuditValidate(extendedDataEntities);
                break;
            }
            case PILOT_FEEDBACK : {
                /* 中试反馈 */
                this.pilotFeedbackValidate(extendedDataEntities);
                /* 研发负责人提交样品我司质检报告 */
                this.scmSumbitRptValidate(extendedDataEntities);
                break;
            }
            case CATALOG_CONFIRM : {
                /* 合格供应商目录确认审批 */
                this.catalogConfirmValidate(extendedDataEntities);
                break;
            }
            case SCM_SUBMITRPT : {
                /* 研发负责人提交样品我司质检报告 */
                this.scmSumbitRptValidate(extendedDataEntities);
                break;
            }
        }
    }

    /**
     * 供应链负责人提供生产商信息
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/10/26
     */
    private void provideProductValidate(ExtendedDataEntity[] dataEntities) {
        Set<String> proNames = new HashSet();

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("entryentity");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                String proName = entry.getString("yd_recomsup");
                String isRecommend = entry.getString("yd_scmcmd");

                if (StringUtils.isNotBlank(proName) && StringUtils.isBlank(isRecommend)) {
                    errStr.append("2.推荐生产商信息，第" + (i+1) + "行：请填写“供应链是否推荐”。\n");
                } else if (StringUtils.isNotBlank(proName) && "1".equals(isRecommend)) {
                    proNames.add(proName);
                }
            }

            LinkedMap<String,String> fields = new LinkedMap<>();
            fields.put("yd_proaddress","生产商地址");
            fields.put("yd_origin","产地");
            fields.put("yd_capacity","产能");
            fields.put("yd_spec","重量规格");
            fields.put("yd_weight","每件重量");
            fields.put("yd_unit","重量单位");
            fields.put("yd_minunit","最小单元包装形式");
            fields.put("yd_model","型号");
            fields.put("yd_execstd","厂家执行标准");
            fields.put("yd_prodlicense","生产许可");
            fields.put("yd_storagecon","储存条件");
            fields.put("yd_shelflife","保质期");
            fields.put("yd_packing","详细包装方式");
            fields.put("yd_deliverycyc","供货周期");
            fields.put("yd_textareafield","供货风险");
            fields.put("yd_hasallergen","是否含过敏源");
            fields.put("yd_matsource","动植物来源");
//            fields.put("yd_isexplosive","是否涉及易燃易爆");
            fields.put("yd_initscore","初筛评分");
            DynamicObjectCollection matEntries = bill.getDynamicObjectCollection("yd_materialentry");
            for (int i = 0; i < matEntries.size(); i++) {
                DynamicObject entry = matEntries.get(i);
                String proName = entry.getString("yd_anewsupname");
                StringBuffer subMsg = new StringBuffer();

                if (proNames.contains(proName)) {
                    for (Map.Entry<String, String> field : fields.entrySet()) {
                        Object value = entry.get(field.getKey());
                        if (this.checkIsNull(value)) {
                            subMsg.append("“" + field.getValue() + "”，");
                        }
                    }
                }

                if ("1".equals(entry.getString("yd_hasallergen"))
                        && StringUtils.isBlank(entry.getString("yd_allergysource"))) {
                    subMsg.append("“过敏类型”，");
                }

                if (("1".equals(entry.getString("yd_matsource")) || "2".equals(entry.getString("yd_matsource")))
                        && StringUtils.isBlank(entry.getString("yd_matsourceexp"))) {
                    subMsg.append("“动植物来源说明”，");
                }

                if (subMsg.length() > 0) {
                    errStr.append("3.物料详细信息，第" + (i+1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0,errStr.length() - 1));
            }
        }
    }

    /**
     * 研发判断是否满足物料需求
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void rdJudgmentValidate (ExtendedDataEntity[] dataEntities) {
        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("entryentity");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                String isMatch = entry.getString("yd_ismatchmat");

                if (StringUtils.isBlank(isMatch)) {
                    errStr.append("2.推荐生产商信息，第" + (i + 1) + "行：请填写“研发判断是否满足物料需求”。\n");
                }
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0,errStr.length() - 1));
            }
        }
    }

    /**
     * 原料评估供应商信息校验
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void qualityAnalysisValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String,String> fields = new LinkedMap<>();
        fields.put("yd_gb","参考国标/行标");
        fields.put("yd_proproc","生产工艺评估");
        fields.put("yd_consistency","一致性、真实性判断");
        fields.put("yd_safety","功能及安全性");
        fields.put("yd_recommendedsup","是否推荐生产商");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_matanalyentry");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                StringBuffer subMsg = new StringBuffer();

                for (Map.Entry<String, String> field : fields.entrySet()) {
                    Object value = entry.get(field.getKey());
                    if (this.checkIsNull(value)) {
                        subMsg.append("“" + field.getValue() + "”，");
                    }
                }

                if (subMsg.length() > 0) {
                    errStr.append("4.物料优劣分析，第" + (i+1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0,errStr.length() - 1));
            }
        }
    }

    /**
     * 研发负责人确认-填写小试物料供应商校验
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void arrangeSmallTrialValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String,String> fields = new LinkedMap<>();
        fields.put("yd_issmaltry","是否安排小试");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_stryentry");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                StringBuffer subMsg = new StringBuffer();

                for (Map.Entry<String, String> field : fields.entrySet()) {
                    Object value = entry.get(field.getKey());
                    if (this.checkIsNull(value)) {
                        subMsg.append("“" + field.getValue() + "”，");
                    }
                }

                if (subMsg.length() > 0) {
                    errStr.append("5.小试反馈及建议中试供应商，第" + (i+1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0,errStr.length() - 1));
            }
        }
    }

    /**
     * 工艺技术部反馈小试结果
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void testFeedbackValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String,String> fields = new LinkedMap<>();
        fields.put("yd_stfeedback","小试反馈");
        fields.put("yd_stcomfirm","确认小试情况");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_stryentry");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                StringBuffer subMsg = new StringBuffer();

                for (Map.Entry<String, String> field : fields.entrySet()) {
                    Object value = entry.get(field.getKey());
                    if (this.checkIsNull(value)) {
                        subMsg.append("“" + field.getValue() + "”，");
                    }
                }

                if (subMsg.length() > 0) {
                    errStr.append("5.小试反馈及建议中试供应商，第" + (i+1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0,errStr.length() - 1));
            }
        }
    }

    /**
     * 研发负责人确认中试信息
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void confirmPilotValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String,String> fields = new LinkedMap<>();
        fields.put("yd_prjnum","项目编号");
        fields.put("yd_rdnum","RD编号");

        LinkedMap<String,String> entryFields = new LinkedMap<>();
        entryFields.put("yd_stresult","建议中试供应商");
        entryFields.put("yd_supmatsort","供应商物料排序");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();
            boolean isExistFile = false;

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("1.基础信息，请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_stryentry");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                subMsg = new StringBuffer();

                for (Map.Entry<String, String> field : entryFields.entrySet()) {
                    Object value = entry.get(field.getKey());
                    if (this.checkIsNull(value)) {
                        subMsg.append("“" + field.getValue() + "”，");
                    }
                }

                if (entry.getDynamicObjectCollection("yd_qlyreqatt").size() > 0) {
                    isExistFile = true;
                }

                if (subMsg.length() > 0) {
                    errStr.append("5.小试反馈及建议中试供应商，第" + (i+1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }

            if (!isExistFile) {
                errStr.append("5.小试反馈及建议中试供应商，请上传“初步质量要求”。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0,errStr.length() - 1));
            }
        }
    }

    /**
     * 供应链负责人对供应商书面审核
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void scmReviewValidate (ExtendedDataEntity[] dataEntities) {
        for (ExtendedDataEntity dataEntity : dataEntities) {
            StringBuffer errMsg = new StringBuffer();
            DynamicObject bill = dataEntity.getDataEntity();
            String version = bill.getString("yd_version");
            if ("1".equals(version)) {
                // 校验资质文件是否已上传
                errMsg.append(checkQualifications(dataEntity));
            } else {
                // 书面审核上传资料
                errMsg.append(checkWrittenReviewAttachments(dataEntity));
            }

            if (errMsg.length() > 0) {
                this.addErrorMessage(dataEntity, errMsg.toString());
            }
        }
    }

    /**
     * 研发负责人提交样品我司质检报告
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void scmSumbitRptValidate (ExtendedDataEntity[] dataEntities) {
        for (ExtendedDataEntity dataEntity : dataEntities) {
            StringBuffer errMsg = new StringBuffer();
            DynamicObject bill = dataEntity.getDataEntity();
            String version = bill.getString("yd_version");
            if ("1".equals(version)) {
                // 校验样品我司质检报告是否已上传
                LinkedMap<String,Map<String,String>> mapping = new LinkedMap<>();

                LinkedMap<String, String> typeMap = new LinkedMap<>();
                typeMap.put("物料信息-流程发起阶段必须资料","yd_entryprocessstar&yd_pstarattachmenttype");

                LinkedMap<String, String> fileMap = new LinkedMap<>();
                fileMap.put("D","样品我司检测报告");
                mapping.put("yd_entryprocessstar&yd_pstarattachmenttype", fileMap);

                DynamicObjectCollection mainEntries = bill.getDynamicObjectCollection("yd_file1entry");

                int index = 1;
                for (DynamicObject mainEntry : mainEntries) {
                    String proName = mainEntry.getString("yd_cnewsupname");
                    StringBuffer subErrMsg = new StringBuffer();

                    String isTest = getIsPilotTest(bill,
                            mainEntry.getDynamicObject("yd_csup"), mainEntry.getDynamicObject("yd_cprod"));

                    if ("1".equals(isTest)) {
                        for (Map.Entry<String, Map<String, String>> entryInfo : mapping.entrySet()) {
                            String subEntryName = entryInfo.getKey().split("&")[0];
                            String subFieldName = entryInfo.getKey().split("&")[1];

                            Set<String> typeSet = mainEntry.getDynamicObjectCollection(subEntryName).stream()
                                    .map(subEntry -> subEntry.getString(subFieldName)).collect(Collectors.toSet());

                            Map<String, String> fileInfo = entryInfo.getValue();
                            for (Map.Entry<String, String> file : fileInfo.entrySet()) {
                                if (!typeSet.contains(file.getKey())) {
                                    subErrMsg.append("“").append(file.getValue()).append("”，");
                                }
                            }
                        }

                        if (subErrMsg.length() > 0) {
                            errMsg.append("请上传生产商：" + proName
                                    + " 的 " + subErrMsg.substring(0, subErrMsg.length() - 1) + "。\n");
                        }
                    }

                    index++;
                }

                if (errMsg.length() > 0) {
                    this.addErrorMessage(dataEntity, errMsg.substring(0, errMsg.length() - 1));
                }
            } else {
                // 书面审核上传资料
                DynamicObjectCollection fileEntries = bill.getDynamicObjectCollection("yd_gsupentry");

                LinkedMap<String,String> fileMap = new LinkedMap<>();
                fileMap.put("yd_testreport","样品我司检测报告");

                for (int i = 0; i < fileEntries.size(); i++) {
                    StringBuffer subErrMsg = new StringBuffer();
                    DynamicObject fileEntry = fileEntries.get(i);
                    String proName = fileEntry.getString("yd_gnewsupname");


                    for (Map.Entry<String,String> entrySet : fileMap.entrySet()) {
                        DynamicObjectCollection attachments = fileEntry.getDynamicObjectCollection(entrySet.getKey());
                        if (attachments.size() == 0) {
                            subErrMsg.append("“").append(entrySet.getValue()).append("”，");
                        }
                    }

                    if (subErrMsg.length() > 0) {
                        errMsg.append("8.准入结论，第" + (i+1) + "行：请上传生产商：" + proName
                                + " 的 " + subErrMsg.substring(0, subErrMsg.length() - 1) + "。\n");
                    }
                }

                if (errMsg.length() > 0) {
                    this.addErrorMessage(dataEntity, errMsg.substring(0, errMsg.length() - 1));
                }
            }
        }
    }

    /**
     * 书面审核上传资料附件校验
     * @param extendedDataEntity
     * @author: hst
     * @createDate: 2023/07/26
     */
    private String checkWrittenReviewAttachments (ExtendedDataEntity extendedDataEntity) {
        StringBuffer errStr = new StringBuffer();
        DynamicObject bill = extendedDataEntity.getDataEntity();
        String supple = bill.getString("yd_supple");
        DynamicObjectCollection fileEntries = bill.getDynamicObjectCollection("yd_file1entry");
        DynamicObjectCollection suppleEntries = bill.getDynamicObjectCollection("yd_entryentity");
        DynamicObjectCollection involveEntries = bill.getDynamicObjectCollection("yd_file2entry");

        LinkedMap<String, String> mainMap = new LinkedMap<>();
        mainMap.put("yd_csup","已注册的经销商");
        mainMap.put("yd_cprod","已登记的生产商");
        mainMap.put("yd_supmatname","供应商实物名称");

        LinkedMap<String,String> fileMap = new LinkedMap<>();
        fileMap.put("yd_spc1","供应商书面调查表");
        fileMap.put("yd_spc2","物料描述表");
        fileMap.put("yd_spc3","3个批次样品 COA");
//        fileMap.put("yd_spc4","样品我司检测报告");
        fileMap.put("yd_spc5","生产工艺流程图");
        fileMap.put("yd_spc6","厂家企业标准");

        LinkedMap<String, String> suppleMap = new LinkedMap<>();
        suppleMap.put("yd_sps1", "第三方型式检验报告");
        suppleMap.put("yd_sps2", "包装标签模板");
        suppleMap.put("yd_sps3", "非辐照证明");

        LinkedMap<String, String> involveMap = new LinkedMap<>();
        involveMap.put("yd_spd4&yd_istrans", "非转基因证明");
        involveMap.put("yd_spd5&yd_isstrain", "菌种/藻类鉴定证明");
        involveMap.put("yd_spd6&yd_isprobiotics", "菌株鉴定报告");
        involveMap.put("yd_spd11&yd_isprobiotics", "物料一致性管理流程和方法");
        involveMap.put("yd_spd12&yd_isprobiotics", "最近3次物料一致性报告");
        involveMap.put("yd_spd13&yd_isprobiotics", "菌株安全性报告");
        involveMap.put("yd_spd8&yd_isanimal", "兽药残留检验报告");
        involveMap.put("yd_spd15&yd_isanimal", "TSE/BSE声明或EDQM证书");
        involveMap.put("yd_spd14&yd_isprocess", "品种鉴定证明（或检验检疫证明）");
        involveMap.put("yd_spd7&yd_isplant", "农残检验报告");
        involveMap.put("yd_spd9&yd_isnewfood", "生产工艺与法规要求一致性证明公告");
        involveMap.put("yd_spd16&yd_isnewfood", "工艺详细说明（如收率等）");
        involveMap.put("yd_spd10&yd_isexpose", "近一年产品菌落总数，霉菌和大肠菌群检验数据或趋势分析");
        involveMap.put("yd_spd18&yd_ismsds", "MSDS");

        for (int i = 0; i < fileEntries.size(); i++) {
            StringBuffer subErrMsg = new StringBuffer();
            DynamicObject fileEntry = fileEntries.get(i);
            String proName = fileEntry.getString("yd_cnewsupname");

            StringBuffer subMsg = new StringBuffer();
            for (Map.Entry<String, String> field : mainMap.entrySet()) {
                Object value = fileEntry.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("6.供应商书面审核资料上传，第" + (i+1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            for (Map.Entry<String,String> entrySet : fileMap.entrySet()) {
                DynamicObjectCollection attachments = fileEntry.getDynamicObjectCollection(entrySet.getKey());
                if (attachments.size() == 0) {
                    subErrMsg.append("“").append(entrySet.getValue()).append("”，");
                }
            }

            if ("1".equals(supple) && i <= suppleEntries.size()) {
                DynamicObject suppleEntry = suppleEntries.get(i);
                for (Map.Entry<String, String> entrySet : suppleMap.entrySet()) {
                    DynamicObjectCollection attachments = suppleEntry.getDynamicObjectCollection(entrySet.getKey());
                    if (attachments.size() == 0) {
                        subErrMsg.append("“").append(entrySet.getValue()).append("”，");
                    }
                }
            }

            if (i <= involveEntries.size()) {
                DynamicObject involveEntry = involveEntries.get(i);
                for (Map.Entry<String, String> entrySet : involveMap.entrySet()) {
                    String[] fields = entrySet.getKey().split("&");
                    String conditionField = fields[1];
                    String attachField = fields[0];
                    DynamicObjectCollection attachments = involveEntry.getDynamicObjectCollection(attachField);
                    if (attachments.size() == 0 && "1".equals(bill.getString(conditionField))) {
                        subErrMsg.append("“").append(entrySet.getValue()).append("”，");
                    }
                }
            }

            if (subErrMsg.length() > 0) {
                errStr.append("6.供应商书面审核资料上传，第" + (i+1) + "行：请上传生产商：" + proName
                        + " 的 " + subErrMsg.substring(0, subErrMsg.length() - 1) + "。\n");
            }
        }

        return errStr.length() > 0 ? errStr.substring(0, errStr.length() - 1) : "";
    }

    /**
     * 校验资质文件是否已上传
     * @param extendedDataEntity
     * @author: hst
     * @createDate: 2024/10/23
     */
    private String checkQualifications(ExtendedDataEntity extendedDataEntity) {
        StringBuffer errMsg = new StringBuffer();
        DynamicObject bill = extendedDataEntity.getDataEntity();
        LinkedMap<String,Map<String,String>> mapping = new LinkedMap<>();

        LinkedMap<String, String> mainMap = new LinkedMap<>();
        mainMap.put("yd_csup","已注册的经销商");
        mainMap.put("yd_cprod","已登记的生产商");
        mainMap.put("yd_supmatname","供应商实物名称");


        LinkedMap<String, String> typeMap = new LinkedMap<>();
        typeMap.put("国内生厂商","yd_entrydomestic&yd_domesattachmenttype");
        typeMap.put("国外生厂商","yd_entryforeign&yd_foreigattachmenttype");
        typeMap.put("代理商","yd_entryagent&yd_agentattachmenttype");
        typeMap.put("物料信息-流程发起阶段必须资料","yd_entryprocessstar&yd_pstarattachmenttype");
        typeMap.put("物料信息-流程批准纳入前必须资料","yd_entryprocessfitinto&yd_pfitienattachmenttype");

        LinkedMap<String, String> fileMap = new LinkedMap<>();
//        fileMap.put("A","供应商书面调查表");
        fileMap.put("B","物料描述表");
        fileMap.put("C","样品 COA");
//        fileMap.put("D","样品我司检测报告");
        fileMap.put("E","生产工艺流程图");
        fileMap.put("F","厂家企业标准");
        mapping.put("yd_entryprocessstar&yd_pstarattachmenttype", fileMap);

        fileMap = new LinkedMap<>();
        fileMap.put("B","第三方型式检验报告");
        fileMap.put("C","包装标签模板");
        fileMap.put("K","非辐照证明");
        mapping.put("yd_entryprocessfitinto&yd_pfitienattachmenttype", fileMap);

        DynamicObjectCollection mainEntries = bill.getDynamicObjectCollection("yd_file1entry");

        int index = 1;
        for (DynamicObject mainEntry : mainEntries) {
            String proName = mainEntry.getString("yd_cnewsupname");

            StringBuffer subMsg = new StringBuffer();
            for (Map.Entry<String, String> field : mainMap.entrySet()) {
                Object value = mainEntry.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errMsg.append("6.供应商书面审核资料上传，第" + index + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            for (Map.Entry<String,String> entryInfo : typeMap.entrySet()) {
                String subEntryName = entryInfo.getValue().split("&")[0];
                String subFieldName = entryInfo.getValue().split("&")[1];

                int subIndex = 1;
                subMsg = new StringBuffer();
                DynamicObjectCollection subEntries = mainEntry.getDynamicObjectCollection(subEntryName);
                for (DynamicObject subEntry : subEntries) {
                    if (StringUtils.isBlank(subEntry.getString(subFieldName))) {
                        subMsg.append(subIndex + "、");
                    }

                    subIndex++;
                }

                if (subMsg.length() > 0) {
                    errMsg.append("6.供应商书面审核资料上传，第" + index + "行：请填写生产商" + proName
                            + " 的 《" + entryInfo.getKey() + "》 资质文件的第" + subMsg.substring(0, subMsg.length() - 1) + "行的”附件类型“。\n");
                }
            }

            index++;
        }

        if (errMsg.length() > 0) {
            return errMsg.substring(0, errMsg.length() - 1);
        }

        index = 1;
        for (DynamicObject mainEntry : mainEntries) {
            String proName = mainEntry.getString("yd_cnewsupname");
            StringBuffer subErrMsg = new StringBuffer();

            DynamicObject domeFile = mainEntry.getDynamicObjectCollection("yd_entrydomestic")
                    .stream().filter(entry -> "E".equals(entry.getString("yd_domesattachmenttype")))
                    .findFirst().orElse(null);
            DynamicObject foreFile = mainEntry.getDynamicObjectCollection("yd_entryforeign")
                    .stream().filter(entry -> "C".equals(entry.getString("yd_foreigattachmenttype")))
                    .findFirst().orElse(null);

            if (Objects.isNull(domeFile) && Objects.isNull(foreFile)) {
                subErrMsg.append("“供应商书面调查表”、");
            }

            for (Map.Entry<String,Map<String,String>> entryInfo : mapping.entrySet()) {
                String subEntryName = entryInfo.getKey().split("&")[0];
                String subFieldName = entryInfo.getKey().split("&")[1];

                Set<String> typeSet = mainEntry.getDynamicObjectCollection(subEntryName).stream()
                        .map(subEntry -> subEntry.getString(subFieldName)).collect(Collectors.toSet());

                Map<String, String> fileInfo = entryInfo.getValue();
                for (Map.Entry<String, String> file : fileInfo.entrySet()) {
                    if (!typeSet.contains(file.getKey())) {
                        subErrMsg.append("“").append(file.getValue()).append("”，");
                    }
                }
            }

            if (subErrMsg.length() > 0) {
                errMsg.append("6.供应商书面审核资料上传，第" + index + "行：请上传生产商：" + proName
                        + " 的 " + subErrMsg.substring(0, subErrMsg.length() - 1) + "。\n");
            }

            index++;
        }

        return errMsg.length() > 0 ? errMsg.substring(0, errMsg.length() - 1) : "";
    }

    /**
     * 书面审核
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void qmReviewValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> scoreFields = new LinkedMap<>();
        scoreFields.put("yd_ismix", "是否总混");
        scoreFields.put("yd_issterilizate", "是否灭菌");
        scoreFields.put("yd_credibility", "供应商信誉度");
        scoreFields.put("yd_charisk", "物料特性风险");
        scoreFields.put("yd_prdprcrisk", "生产过程风险");
        scoreFields.put("yd_mrgrisk", "体系管理风险");
        scoreFields.put("yd_qascore", "质量评估总分");

        LinkedMap<String, String> auditFields = new LinkedMap<>();
        auditFields.put("yd_allfilecomplete", "资料是否齐全");
        auditFields.put("yd_evalevel", "供应商评估等级");
        auditFields.put("yd_onsiteaudit", "现场审核判断");
        auditFields.put("yd_writtencon", "书面审核结论");
        auditFields.put("yd_suptype", "生产商类型");
        auditFields.put("yd_location", "存储库位");
        auditFields.put("yd_isburn", "是否涉及易燃易爆");
        auditFields.put("yd_pqtype", "物料是否符合生产资质（经过书面审核、现场审核）");
        auditFields.put("yd_stabletype", "物料稳定性考察是否合格");
        auditFields.put("yd_opiniontype", "产品处理意见");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_scoreentry");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                StringBuffer subMsg = new StringBuffer();

                for (Map.Entry<String, String> field : scoreFields.entrySet()) {
                    Object value = entry.get(field.getKey());
                    if (this.checkIsNull(value)) {
                        subMsg.append("“" + field.getValue() + "”，");
                    }
                }

                if ("1".equals(entry.getString("yd_ismix"))
                        && StringUtils.isBlank(entry.getString("yd_mixreason"))) {
                    subMsg.append("“总混批量”，");
                }

                if ("1".equals(entry.getString("yd_issterilizate"))
                        && StringUtils.isBlank(entry.getString("yd_sterreason"))) {
                    subMsg.append("“灭菌方式”，");
                }

                if (subMsg.length() > 0) {
                    errStr.append("7.供应商书面审核（书面审核评分），第" + (i + 1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }

            entries = bill.getDynamicObjectCollection("yd_auditentry");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                StringBuffer subMsg = new StringBuffer();

                for (Map.Entry<String, String> field : auditFields.entrySet()) {
                    Object value = entry.get(field.getKey());
                    if (this.checkIsNull(value)) {
                        subMsg.append("“" + field.getValue() + "”，");
                    }
                }

                if (("1".equals(entry.getString("yd_onsiteaudit")) || "2".equals(entry.getString("yd_onsiteaudit")))
                        && StringUtils.isBlank(entry.getString("yd_hasspotaccess"))) {
                    subMsg.append("“本次准入是否现场审核”，");
                }

                if ("0".equals(entry.getString("yd_hasspotaccess"))
                        && StringUtils.isBlank(entry.getString("yd_reason"))) {
                    subMsg.append("“原因”，");
                }

                if ("3".equals(entry.getString("yd_onsiteaudit"))
                        && StringUtils.isBlank(entry.getString("yd_noauditres"))) {
                    subMsg.append("“无需现场审核直接试产原因”，");
                }

                if ("4".equals(entry.getString("yd_onsiteaudit"))
                        && StringUtils.isBlank(entry.getString("yd_stopdevres"))) {
                    subMsg.append("“停止供应商开发原因”，");
                }

                if ("3".equals(entry.getString("yd_pqtype"))
                        && StringUtils.isBlank(entry.getString("yd_pqreason"))) {
                    subMsg.append("“物料是否符合生产资质（经过书面审核、现场审核）-其他说明”，");
                }

                if ("3".equals(entry.getString("yd_stabletype"))
                        && StringUtils.isBlank(entry.getString("yd_stablereason"))) {
                    subMsg.append("“物料稳定性考察是否合格-其他说明”，");
                }

                if ("5".equals(entry.getString("yd_opiniontype"))
                        && StringUtils.isBlank(entry.getString("yd_opinionreason"))) {
                    subMsg.append("“产品处理意见-其他说明”，");
                }

                if (subMsg.length() > 0) {
                    errStr.append("7.供应商书面审核（供应商等级评估），第" + (i + 1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }


            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 预计现场审核时间
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void estimatedTimeValidate (ExtendedDataEntity[] dataEntities) {
        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_auditentry");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                StringBuffer subMsg = new StringBuffer();

                if ("1".equals(entry.getString("yd_hasspotaccess"))
                        && (Objects.isNull(entry.getDate("yd_compbegindate")) ||  Objects.isNull(entry.getDate("yd_compenddate")))) {
                    subMsg.append("“预计现场审核时间”，");
                }

                if (subMsg.length() > 0) {
                    errStr.append("7.供应商书面审核（供应商等级评估），第" + (i + 1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }


            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 现场审核批准
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void onSiteAuditValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
//        fields.put("yd_osauditscore", "现场审核得分");
//        fields.put("yd_osfeedback", "现场审核反馈单");
        fields.put("yd_auditresulttype", "审核结论（附审核报告）");
        fields.put("yd_supevalevel", "供应商质量等级");
        fields.put("yd_tigsampletype", "是否加严取样");
        fields.put("yd_cmpresulttype", "现有合格供应商的优劣势对比结论（基于审核表维度）");
        fields.put("yd_wftitle", "现场审核结论");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();

            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_hsupentry");
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject entry = entries.get(i);
                StringBuffer subMsg = new StringBuffer();

                DynamicObject supplier = entry.getDynamicObject("yd_hsup");
                DynamicObject producer = entry.getDynamicObject("yd_hprod");

                if (Objects.nonNull(supplier) && Objects.nonNull(producer)) {
                    String isAudit = getSupAudit(bill, supplier, producer, "yd_hasspotaccess");

                    if ("1".equals(isAudit)) {
                        for (Map.Entry<String, String> field : fields.entrySet()) {
                            Object value = entry.get(field.getKey());
                            if (this.checkIsNull(value)) {
                                subMsg.append("“" + field.getValue() + "”，");
                            }
                        }

//                        if (Objects.isNull(entry.getDate("yd_osauditbegindate"))
//                                || Objects.isNull(entry.getDate("yd_osauditdate"))) {
//                            subMsg.append("“现场审核日期”，");
//                        }

                        if ("1".equals(entry.getString("yd_tigsampletype"))
                                && StringUtils.isBlank(entry.getString("yd_tightenreason"))) {
                            subMsg.append("“加严原因”，");
                        }

                        if ("1".equals(entry.getString("yd_tigsampletype"))
                                && Objects.isNull(entry.getDate("yd_stridate"))) {
                            subMsg.append("“加严起始时间”，");
                        }

//                        if (entry.getDynamicObjectCollection("yd_mataccatt").size() == 0) {
//                            subMsg.append("“现场审核报告”，");
//                        }
                    }
                }

                if (subMsg.length() > 0) {
                    errStr.append("7.供应商书面审核（供应商现场审核记录），第" + (i + 1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 中试反馈
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void pilotFeedbackValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> newFields = new LinkedMap<>();
        newFields.put("yd_admititemno","项目编号");
        newFields.put("yd_pilotmaterial", "物料代码");
        newFields.put("yd_isadmitmt", "是否已进行中试");
        newFields.put("yd_admitmrresult", "中试结论");
        newFields.put("yd_admitmrcon", "中试情况");

        LinkedMap<String, String> oldFields = new LinkedMap<>();
        oldFields.put("yd_itemno","项目编号");
        oldFields.put("yd_ismt", "是否已进行中试");
        oldFields.put("yd_mrcon", "中试情况");
        oldFields.put("yd_mrresult", "中试结论");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            String version = bill.getString("yd_version");
            StringBuffer errStr = new StringBuffer();

            if ("1".equals(version)) {
                DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_pilotentry");
                for (int i = 0; i < entries.size(); i++) {
                    DynamicObject entry = entries.get(i);
                    StringBuffer subMsg = new StringBuffer();

                    DynamicObject supplier = entry.getDynamicObject("yd_pilotsup");
                    DynamicObject producer = entry.getDynamicObject("yd_pilotpro");

                    if (Objects.nonNull(supplier) && Objects.nonNull(producer)) {
                        String isAudit = getSupAudit(bill, supplier, producer, "yd_onsiteaudit");
                        Date auditDate = getSupDate(bill, supplier, producer);

                        if (("1".equals(isAudit) && Objects.nonNull(auditDate))
                                || "2".equals(isAudit)) {
                            for (Map.Entry<String, String> field : newFields.entrySet()) {
                                Object value = entry.get(field.getKey());
                                if (this.checkIsNull(value)) {
                                    subMsg.append("“" + field.getValue() + "”，");
                                }
                            }
                        }
                    }

                    if (subMsg.length() > 0) {
                        errStr.append("8.准入结论，第" + (i + 1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                    }
                }
            } else {
                StringBuffer subMsg = new StringBuffer();
                DynamicObject material = bill.getDynamicObject("yd_material");
                if (Objects.isNull(material)) {
                    subMsg.append("“物料代码”，");
                }
                if (subMsg.length() > 0) {
                    errStr.append("8.中试反馈，请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }

                DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_gsupentry");
                for (int i = 0; i < entries.size(); i++) {
                    DynamicObject entry = entries.get(i);
                    subMsg = new StringBuffer();

                    for (Map.Entry<String, String> field : oldFields.entrySet()) {
                        Object value = entry.get(field.getKey());
                        if (this.checkIsNull(value)) {
                            subMsg.append("“" + field.getValue() + "”，");
                        }
                    }

                    if (subMsg.length() > 0) {
                        errStr.append("8.准入结论，第" + (i + 1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                    }
                }
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 合格供应商目录确认审批
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void catalogConfirmValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> newFields = new LinkedMap<>();
        newFields.put("yd_admitmaterial","物料代码");
        newFields.put("yd_isqualify", "是否纳入研发合格供应商目录");

        LinkedMap<String, String> headFields = new LinkedMap<>();
        headFields.put("yd_material","物料代码");
        headFields.put("yd_matcat","物料类别");
        headFields.put("yd_enterlevel","纳入级别");
        headFields.put("yd_isinvolve","是否涉及鱼油、螺旋藻、褪黑素、辅酶Q10、破壁灵芝孢子粉、乳清蛋白、大豆分离蛋白、人参、西洋参、灵芝");

        LinkedMap<String, String> oldFields = new LinkedMap<>();
        oldFields.put("yd_supdir","是否纳入研发合格供应商目录");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            String version = bill.getString("yd_version");
            StringBuffer errStr = new StringBuffer();

            if ("1".equals(version)) {
                DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_admitentity");
                for (int i = 0; i < entries.size(); i++) {
                    DynamicObject entry = entries.get(i);
                    StringBuffer subMsg = new StringBuffer();

                    for (Map.Entry<String, String> field : newFields.entrySet()) {
                        Object value = entry.get(field.getKey());
                        if (this.checkIsNull(value)) {
                            subMsg.append("“" + field.getValue() + "”，");
                        }
                    }

                    if ("1".equals(entry.getString("yd_isqualify"))
                            && Objects.isNull(entry.getDynamicObject("yd_admitmatcat"))) {
                        subMsg.append("“物料类别”，");
                    }

                    if ("1".equals(entry.getString("yd_isqualify"))
                            && StringUtils.isBlank(entry.getString("yd_enteradmitlevel"))) {
                        subMsg.append("“纳入级别”，");
                    }

                    if ("1".equals(entry.getString("yd_isqualify"))
                            && StringUtils.isBlank(entry.getString("yd_beinvolvekeymat"))) {
                        subMsg.append("“是否涉及鱼油、螺旋藻、褪黑素、辅酶Q10、破壁灵芝孢子粉、乳清蛋白、大豆分离蛋白、人参、西洋参、灵芝”，");
                    }

                    if ("1".equals(entry.getString("yd_isqualify"))
                            && StringUtils.isBlank(entry.getString("yd_isadmitslevel"))) {
                        subMsg.append("“是否S级”，");
                    }

                    if (subMsg.length() > 0) {
                        errStr.append("9.准入结论，第" + (i + 1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                    }
                }
            } else {
                StringBuffer subMsg = new StringBuffer();
                for (Map.Entry<String, String> field : headFields.entrySet()) {
                    Object value = bill.get(field.getKey());
                    if (this.checkIsNull(value)) {
                        subMsg.append("“" + field.getValue() + "”，");
                    }
                }
                if (subMsg.length() > 0) {
                    errStr.append("8.准入结论，请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                }

                DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_gsupentry");
                for (int i = 0; i < entries.size(); i++) {
                    DynamicObject entry = entries.get(i);
                    subMsg = new StringBuffer();

                    for (Map.Entry<String, String> field : oldFields.entrySet()) {
                        Object value = entry.get(field.getKey());
                        if (this.checkIsNull(value)) {
                            subMsg.append("“" + field.getValue() + "”，");
                        }
                    }

                    if (subMsg.length() > 0) {
                        errStr.append("8.准入结论，第" + (i + 1) + "行：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
                    }
                }
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 校验字段值是否为空
     * @param value
     * @return
     * @author: hst
     * @createDate: 2024/11/03
     */
    private boolean checkIsNull (Object value) {
        if (value instanceof String) {
            if (StringUtils.isBlank((String) value)) {
                return true;
            }
        } else if (value instanceof BigDecimal) {
            if ((new BigDecimal("0")).compareTo((BigDecimal) value) == 0) {
                return true;
            }
        } else if (value instanceof DynamicObjectCollection) {
            if (Objects.isNull(value) || ((DynamicObjectCollection) value).size() == 0) {
                return true;
            }
        } else if (value instanceof DynamicObject) {
            if (Objects.isNull(value)) {
                return true;
            }
        } else if (Objects.isNull(value)) {
            if (Objects.isNull(value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取现场审核判断
     * @param info
     * @param supInfo
     * @param proInfo
     * @return
     * @author: hst
     * @createDate: 2024/11/04
     */
    private String getSupAudit(DynamicObject info, DynamicObject supInfo, DynamicObject proInfo, String field) {
        DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_auditentry");
        if (entrys != null && entrys.size() > 0) {
            for (int i = 0; i < entrys.size(); i++) {
                DynamicObject entry = entrys.get(i);
                DynamicObject sInfo = entry.getDynamicObject("yd_fsup");
                DynamicObject pInfo = entry.getDynamicObject("yd_fprod");
                if (sInfo != null && pInfo != null
                        && sInfo.getLong("id") == supInfo.getLong("id")
                        && pInfo.getLong("id") == proInfo.getLong("id")) {
                    return entry.getString(field);
                }
            }
        }
        return "";
    }

    /**
     * 获取供应商现场审核时间
     * @param info
     * @param supInfo
     * @param proInfo
     * @return
     * @author: hst
     * @createDate: 2024/11/04
     */
    private Date getSupDate(DynamicObject info, DynamicObject supInfo, DynamicObject proInfo) {
        DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_hsupentry");
        if (entrys != null && entrys.size() > 0) {
            for (int i = 0; i < entrys.size(); i++) {
                DynamicObject entry = entrys.get(i);
                DynamicObject sInfo = entry.getDynamicObject("yd_hsup");
                DynamicObject pInfo = entry.getDynamicObject("yd_hprod");
                if (sInfo != null && pInfo != null
                        && sInfo.getLong("id") == supInfo.getLong("id")
                        && pInfo.getLong("id") == proInfo.getLong("id")) {
                    return entry.getDate("yd_osauditdate");
                }
            }
        }

        return null;
    }

    /**
     * 获取供应商是否已进行中试
     * @param info
     * @param supInfo
     * @param proInfo
     * @return
     * @author: hst
     * @createDate: 2024/11/04
     */
    private String getIsPilotTest(DynamicObject info, DynamicObject supInfo, DynamicObject proInfo) {
        DynamicObjectCollection entrys = info.getDynamicObjectCollection("yd_pilotentry");
        if (entrys != null && entrys.size() > 0) {
            for (int i = 0; i < entrys.size(); i++) {
                DynamicObject entry = entrys.get(i);
                DynamicObject sInfo = entry.getDynamicObject("yd_pilotsup");
                DynamicObject pInfo = entry.getDynamicObject("yd_pilotpro");
                if (sInfo != null && pInfo != null
                        && sInfo.getLong("id") == supInfo.getLong("id")
                        && pInfo.getLong("id") == proInfo.getLong("id")) {
                    return entry.getString("yd_isadmitmt");
                }
            }
        }

        return null;
    }
}
