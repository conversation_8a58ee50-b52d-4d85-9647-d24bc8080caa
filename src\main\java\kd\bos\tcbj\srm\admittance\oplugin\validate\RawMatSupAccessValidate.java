package kd.bos.tcbj.srm.admittance.oplugin.validate;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;

import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.oplugin.validate.RawMatSupAccessValidate
 * @className RawMatSupAccessValidate
 * @author: hst
 * @createDate: 2023/07/07
 * @description: 原辅料准入单提交校验器
 * @version: v1.0
 */
public class RawMatSupAccessValidate extends AbstractValidator {

    @Override
    public void validate() {
        ExtendedDataEntity[] extendedDataEntities =  this.getDataEntities();
        for (ExtendedDataEntity extendedDataEntity : extendedDataEntities) {
            DynamicObject bill = extendedDataEntity.getDataEntity();
            String usesType = bill.getString("yd_matusestype");
            // 当物料用途为非新品时，需校验产品清单附件是否已上传
            if ("1".equals(usesType)) {
                DynamicObjectCollection attachments = bill.getDynamicObjectCollection("yd_mataccatt4");
                if (Objects.nonNull(attachments) && attachments.size() == 0) {
                    this.addErrorMessage(extendedDataEntity,"单据：" + bill.getString("billno") + "物料用途为非新品，请上传产品清单附件");
                }
            }
        }
    }
}
