package kd.bos.tcbj.srm.admittance.oplugin.validate;

import com.twelvemonkeys.util.LinkedMap;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.tcbj.im.util.StringUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.oplugin.validate.RawmatSupAccessVaildate
 * @className: RawmatSupAccessVaildate
 * @author: hst
 * @createDate: 2024/11/03
 * @description: 原辅料及供应商新增/变更申请校验操作
 * @version: v1.0
 */
public class RawmatSupAccessVaildate extends AbstractValidator {

    /* QC检验部评估 */
    private final static String QC_REVIEW = "qc_review";
    /* QM核查判断 */
    private final static String QM_REVIEW = "qm_review";
    /* 原料技术评估 */
    private final static String RAW_EVALUATION = "raw_evaluation";
    /* 产品研发中心主任意见 */
    private final static String DEP_OPINIONS = "dep_opinions";
    /* 研发副总意见 */
    private final static String PRESIDENT_OPINIONS = "dep_president_opinions";
    /* 预计现场审核时间 */
    private final static String ESTIMATED_TIME = "estimated_time";
    /* 现场审核批准 */
    private final static String ONSITE_AUDIT = "onsite_audit";
    /* 试产评估 */
    private final static String TRIA_EVALUATION = "tria_evaluation";
    /* 试产方案上传 */
    private final static String PLAN_UPLOAD = "plan_upload";
    /* 试产总结 */
    private final static String TRIAL_SUMMARY = "trial_summary";
    /* 营养健康研究院意见 */
    private final static String INSTITUTE_OPINIONS = "institute_opinions";
    /* 质量管理部审批 */
    private final static String QMDEP_REVIEW = "qmdep_review";
    /* 供应链总监审批 */
    private final static String SCM_REVIEW = "scm_review";
    /* 品质总监审批 */
    private final static String VQ_REVIEW = "vq_review";
    /* 透明工厂总经理审批 */
    private final static String VP_REVIEW = "vp_review";
    /* 研发副总审批 */
    private final static String RDVP_REVIEW = "rdvp_review";
    /* 合格供应商目录确认审批 */
    private final static String CATALOG_COMFIRM = "catalog_confirm";

    public void validate() {
        String key = this.getOperateKey();
        ExtendedDataEntity[] extendedDataEntities = this.getDataEntities();

        switch (key) {
            case QC_REVIEW: {
                /* QC检验部评估 */
                this.qcReviewValidate(extendedDataEntities);
                break;
            }
            case QM_REVIEW: {
                /* QM核查判断 */
                this.qmReviewValidate(extendedDataEntities);
                break;
            }
            case RAW_EVALUATION : {
                /* 原料技术评估 */
                this.rawEvaluationValidate(extendedDataEntities);
                break;
            }
            case DEP_OPINIONS : {
                /* 产品研发中心主任意见 */
                this.depOpinionsValidate(extendedDataEntities);
                break;
            }
            case PRESIDENT_OPINIONS : {
                /* 研发副总意见 */
                this.presidentOpinionsValidate(extendedDataEntities);
                break;
            }
            case ESTIMATED_TIME : {
                /* 预计现场审核时间 */
                this.estimatedTimeValidate(extendedDataEntities);
                break;
            }
            case ONSITE_AUDIT : {
                /* 现场审核批准 */
                this.onsiteAuditValidate(extendedDataEntities);
                break;
            }
            case TRIA_EVALUATION : {
                /* 试产评估 */
                this.triaEvaluationValidate(extendedDataEntities);
                break;
            }
            case PLAN_UPLOAD : {
                /* 试产方案上传 */
                this.planUploadValidate(extendedDataEntities);
                break;
            }
            case TRIAL_SUMMARY : {
                /* 试产总结 */
                this.trialSummaryValidate(extendedDataEntities);
                break;
            }
            case INSTITUTE_OPINIONS : {
                /* 营养健康研究院意见 */
                this.instituteOpinionsValidate(extendedDataEntities);
                break;
            }
            case QMDEP_REVIEW : {
                /* 质量管理部审批 */
                this.qmDepReviewValidate(extendedDataEntities);
                break;
            }
            case SCM_REVIEW : {
                /* 供应链总监审批 */
                this.scmReviewValidate(extendedDataEntities);
                break;
            }
            case VQ_REVIEW : {
                /* 品质总监审批 */
                this.vqReviewValidate(extendedDataEntities);
                break;
            }
            case VP_REVIEW : {
                /* 透明工厂总经理审批 */
                this.vpReviewValidate(extendedDataEntities);
                break;
            }
            case RDVP_REVIEW : {
                /* 研发副总审批 */
                this.rdvpReviewValidate(extendedDataEntities);
                break;
            }
            case CATALOG_COMFIRM : {
                /* 合格供应商目录确认审批 */
                this.catalogConfirmValidate(extendedDataEntities);
                break;
            }
        }
    }

    /**
     * QC检验部评估
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void qcReviewValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_testrestype", "样品检测结果");
        fields.put("yd_testresult", "样品检测结果意见");
        fields.put("yd_quarestype", "质量标准评估结果");
        fields.put("yd_quaresult", "质量标准评估结果意见（普通食品的物料需要核算5大营养素）");
        fields.put("yd_methodtype", "检验方法评估结果");
        fields.put("yd_method", "检验方法评估结果意见");
        fields.put("yd_fivenutrients", "核算5大营养素说明");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表5 新物料评估：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * QM核查判断
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void qmReviewValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_material", "物料名称");
        fields.put("yd_changereason", "新增/变更原因");
        fields.put("yd_changetype", "新增/变更类别");
        fields.put("yd_matlevtype", "物料级别");
        fields.put("yd_isslevel", "是否为S级物料");
        fields.put("yd_amttype", "采购金额");
        fields.put("yd_prdlabeltype", "是否涉及产品标签宣称");
        fields.put("yd_prdpubtype", "是否涉及销售及网页宣称");
        fields.put("yd_matusename", "物料用途-产品名称");
        fields.put("yd_mataddrtype", "成品生产地");
        fields.put("yd_issendtp", "是否需我司送第三方");
        fields.put("yd_netcycle", "采购净周期");
        fields.put("yd_quasuptype", "是否研发合格供应商");
        fields.put("yd_radiotrytype", "是否已试产");
        fields.put("yd_beselectedlot", "是否需要进行挑批次供货");

        LinkedMap<String, String> nextFields = new LinkedMap<>();
        nextFields.put("yd_spec", "规格");
        nextFields.put("yd_packmodel", "型号");
        nextFields.put("yd_storagecon", "储存条件");
        nextFields.put("yd_packaging", "详细包装方式");
        nextFields.put("yd_netwt", "净重");
        nextFields.put("yd_shelflife", "厂家有效期（月）");
        nextFields.put("yd_matsource", "动植物来源");
        nextFields.put("yd_hasallergen", "是否含过敏源");
        nextFields.put("yd_matdifftype", "与现有物料是否存在差异");

        LinkedMap<String, String> scoreFields = new LinkedMap<>();
        scoreFields.put("yd_credscore", "供应商信誉度");
        scoreFields.put("yd_matriskscore", "物料特性风险");
        scoreFields.put("yd_prdprcscore", "生产过程风险");
        scoreFields.put("yd_sysmrgscore", "体系管理风险");
        scoreFields.put("yd_writtenaccessscore", "质量评估总分");

        LinkedMap<String, String> auditFields = new LinkedMap<>();
        auditFields.put("yd_allfilecomplete", "资料是否齐全");
        auditFields.put("yd_supevtype", "供应商评估等级");
        auditFields.put("yd_interperiod", "内部有效期（月）");
        auditFields.put("yd_isexplosive", "是否涉及易燃易爆");
        auditFields.put("yd_ismix", "是否总混");
        auditFields.put("yd_issterilizate", "是否灭菌");
        auditFields.put("yd_onsiteaudit", "试产物料采购是否需要现场审核");
        auditFields.put("yd_protype", "生产商类型");
        auditFields.put("yd_location", "存储库位");
        auditFields.put("yd_pqtype", "物料是否符合生产资质（经过书面审核、现场审核）");
        auditFields.put("yd_stabletype", "物料稳定性考察是否合格");
        auditFields.put("yd_opiniontype", "产品放行意见");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("基础信息：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            subMsg = new StringBuffer();
            for (Map.Entry<String, String> field : nextFields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if ("1".equals(bill.getString("yd_hasallergen"))
                    && StringUtils.isBlank(bill.getString("yd_allergysource"))) {
                subMsg.append("“过敏类型”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表1 型号及包装：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            subMsg = new StringBuffer();
            for (Map.Entry<String, String> field : scoreFields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if ("1".equals(bill.getString("yd_hasallergen"))
                    && StringUtils.isBlank(bill.getString("yd_allergysource"))) {
                subMsg.append("“过敏类型”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表6 书面评估实施：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            subMsg = new StringBuffer();
            for (Map.Entry<String, String> field : auditFields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if ("1".equals(bill.getString("yd_ismix"))
                    && StringUtils.isBlank(bill.getString("yd_mixreason"))) {
                subMsg.append("“总混批量”，");
            }

            if ("1".equals(bill.getString("yd_issterilizate"))
                    && StringUtils.isBlank(bill.getString("yd_sterreason"))) {
                subMsg.append("“灭菌方式”，");
            }

            if (("1".equals(bill.getString("yd_onsiteaudit")) || "2".equals(bill.getString("yd_onsiteaudit")))
                    && StringUtils.isBlank(bill.getString("yd_hasspotaccess"))) {
                subMsg.append("“本次准入是否现场审核”，");
            }

            if ("0".equals(bill.getString("yd_hasspotaccess"))
                    && StringUtils.isBlank(bill.getString("yd_reason"))) {
                subMsg.append("“原因”，");
            }

//            if ("1".equals(bill.getString("yd_hasspotaccess"))
//                    && (Objects.isNull((bill.getDate("yd_onsitbegindate")))
//                    || Objects.isNull((bill.getDate("yd_onsitdate"))))) {
//                subMsg.append("“现场审核预计完成时间”，");
//            }

            if ("3".equals(bill.getString("yd_onsiteaudit"))
                    && StringUtils.isBlank(bill.getString("yd_noauditres"))) {
                subMsg.append("“无需现场审核直接试产原因”，");
            }

            if ("4".equals(bill.getString("yd_onsiteaudit"))
                    && StringUtils.isBlank(bill.getString("yd_stopdevres"))) {
                subMsg.append("“停止供应商开发原因”，");
            }

            if ("3".equals(bill.getString("yd_pqtype"))
                    && StringUtils.isBlank(bill.getString("yd_pqtypereason"))) {
                subMsg.append("“物料是否符合生产资质（经过书面审核、现场审核）- 其他说明”，");
            }

            if ("3".equals(bill.getString("yd_stabletype"))
                    && StringUtils.isBlank(bill.getString("yd_stablereason"))) {
                subMsg.append("“物料稳定性考察是否合格-其他说明”，");
            }

            if ("5".equals(bill.getString("yd_opiniontype"))
                    && StringUtils.isBlank(bill.getString("yd_opinionreason"))) {
                subMsg.append("“产品放行意见-其他说明”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表8 书面评估结论：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 原料技术评估
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void rawEvaluationValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_isfileeq", "物料名称、物料级别、物料组成、物料工艺是否与我司申报资料、旧物料一致");
        fields.put("yd_evcontent", "原料技术评估意见");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if ("2".equals(bill.getString("yd_isfileeq"))
                    && StringUtils.isBlank(bill.getString("yd_diffdesc"))) {
                subMsg.append("“物料名称、物料级别、物料组成、物料工艺与我司申报资料、旧物料不一致说明”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表9 原料技术评估：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 产品研发中心主任意见
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void depOpinionsValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_kjoptiontype", "营养健康研究院意见");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表9 原料技术评估：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 研发副总意见
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void presidentOpinionsValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_yfoptiontype", "研发副总意见");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表9 原料技术评估：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 现场审核批准
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void onsiteAuditValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_auditresulttype", "审核结论（附审核报告）");
        fields.put("yd_evalevel", "供应商质量等级");
        fields.put("yd_tighten", "是否加严");
        fields.put("yd_cmpresulttype", "现有合格供应商的优劣势对比结论（基于审核表维度）");
        fields.put("yd_wftitle", "现场审核结论");
//        fields.put("yd_spotaccessdate", "现场审核开始日期");
//        fields.put("yd_spotenddate", "现场审核结束日期");
//        fields.put("yd_spotaccessscore", "现场审核得分");
//        fields.put("yd_osfeedback", "现场审核反馈单");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

//            if (bill.getDynamicObjectCollection("yd_mataccatt5").size() == 0) {
//                subMsg.append("“现场审核报告”，");
//            }

            if ("1".equals(bill.getString("yd_tighten"))
                    && Objects.isNull(bill.getDate("yd_stridate"))) {
                subMsg.append("“加严起始时间”，");
            }

            if ("1".equals(bill.getString("yd_tighten"))
                    && StringUtils.isBlank(bill.getString("yd_tightenreason"))) {
                subMsg.append("“加严原因”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表10 现场审核反馈：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 试产评估
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void triaEvaluationValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_evacon", "评估结论（概述工艺顺应性和稳定性考察情况）");
        fields.put("yd_haschangemat", "原发起物料编码是否变更");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            if (!"1".equals(bill.getString("yd_ispush"))) {
                if (StringUtils.isBlank(bill.getString("yd_trialplan"))) {
                    subMsg.append("“试产方案制定”，");
                }
                if (StringUtils.isBlank(bill.getString("yd_allprdtype"))) {
                    subMsg.append("“是否可用于所有在产品种”，");
                }
            }

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if ("0".equals(bill.getString("yd_allprdtype"))
                    && StringUtils.isBlank(bill.getString("yd_appprd"))) {
                subMsg.append("“适用品种”，");
            }

            if ("1".equals(bill.getString("yd_haschangemat"))
                    && Objects.isNull(bill.getDynamicObject("yd_relmat"))) {
                subMsg.append("“新物料编码”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表11 试产评估反馈：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 试产方案上传
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void planUploadValidate (ExtendedDataEntity[] dataEntities) {
        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            if (!"1".equals(bill.getString("yd_ispush"))
                    && StringUtils.isBlank("yd_trialplan")) {
                subMsg.append("“试产方案制定”，");
            }

            if (!"1".equals(bill.getString("yd_ispush"))
                    && StringUtils.isBlank("yd_testprodqty")) {
                subMsg.append("“预估试产采购需求量”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表11 试产评估反馈：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 试产总结
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void trialSummaryValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_evacon", "评估结论（概述工艺顺应性和稳定性考察情况）");
        fields.put("yd_haschangemat", "原发起物料编码是否变更");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            if (!"1".equals(bill.getString("yd_ispush"))
                    && StringUtils.isBlank("yd_trialplan")) {
                subMsg.append("“试产方案制定”，");
            }

            if (!"1".equals(bill.getString("yd_ispush"))
                    && StringUtils.isBlank("yd_testprodqty")) {
                subMsg.append("“预估试产采购需求量”，");
            }

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (!"1".equals(bill.getString("yd_ispush"))
                    && StringUtils.isBlank("yd_allprdtype")) {
                subMsg.append("“是否可用于所有在产品种”，");
            }

            if ("0".equals(bill.getString("yd_allprdtype"))
                    && StringUtils.isBlank(bill.getString("yd_appprd"))) {
                subMsg.append("“适用品种”，");
            }

            if ("1".equals(bill.getString("yd_haschangemat"))
                    && Objects.isNull(bill.getDynamicObject("yd_relmat"))) {
                subMsg.append("“新物料编码”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表11 试产评估反馈：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 营养健康研究院意见
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void instituteOpinionsValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_kjzxoptiontype", "营养健康研究院审批");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表12 新增评估审批：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 质量管理部审批
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void qmDepReviewValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_qaevacon", "质量管理部评估结论");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表12 新增评估审批：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 供应链总监审批
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void scmReviewValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_scmoptiontype", "供应链审批");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表12 新增评估审批：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 品质总监审批
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void vqReviewValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_qcoptiontype", "品质保证中心审批");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表12 新增评估审批：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 透明工厂总经理审批
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void vpReviewValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_fgfzoptiontype", "分管副总审批");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表12 新增评估审批：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 研发副总审批
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void rdvpReviewValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_yffzoptiontype", "研发副总审批");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表12 新增评估审批：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 合格供应商目录确认审批
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/03
     */
    private void catalogConfirmValidate (ExtendedDataEntity[] dataEntities) {
        LinkedMap<String, String> fields = new LinkedMap<>();
        fields.put("yd_spec", "规格");
        fields.put("yd_packmodel", "型号");
        fields.put("yd_storagecon", "储存条件");
        fields.put("yd_packaging", "详细包装方式");
        fields.put("yd_netwt", "净重");
        fields.put("yd_shelflife", "厂家有效期（月）");
        fields.put("yd_matsource", "动植物来源");
        fields.put("yd_hasallergen", "是否含过敏源");
        fields.put("yd_matdifftype", "与现有物料是否存在差异");

        LinkedMap<String, String> nextFields = new LinkedMap<>();
        nextFields.put("yd_supdirectory", "纳入供应商目录意见");
        nextFields.put("yd_beinvolvekeymat", "是否涉及鱼油、螺旋藻、褪黑素、辅酶Q10、破壁灵芝孢子粉、乳清蛋白、大豆分离蛋白、人参、西洋参、灵芝");
        nextFields.put("yd_matcat", "物料类别");
        nextFields.put("yd_supplieraccesspot", "准入节点");

        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            for (Map.Entry<String, String> field : fields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if ("1".equals(bill.getString("yd_hasallergen"))
                    && StringUtils.isBlank(bill.getString("yd_allergysource"))) {
                subMsg.append("“过敏类型”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表1 型号及包装：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            subMsg = new StringBuffer();
            for (Map.Entry<String, String> field : nextFields.entrySet()) {
                Object value = bill.get(field.getKey());
                if (this.checkIsNull(value)) {
                    subMsg.append("“" + field.getValue() + "”，");
                }
            }

            if (subMsg.length() > 0) {
                errStr.append("表12 新增评估审批：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }

    /**
     * 校验字段值是否为空
     * @param value
     * @return
     * @author: hst
     * @createDate: 2024/11/03
     */
    private boolean checkIsNull (Object value) {
        if (value instanceof String) {
            if (StringUtils.isBlank((String) value)) {
                return true;
            }
        } else if (value instanceof BigDecimal) {
            if ((new BigDecimal("0")).compareTo((BigDecimal) value) == 0) {
                return true;
            }
        } else if (value instanceof DynamicObjectCollection) {
            if (Objects.isNull(value) || ((DynamicObjectCollection) value).size() == 0) {
                return true;
            }
        } else if (value instanceof DynamicObject) {
            if (Objects.isNull(value)) {
                return true;
            }
        } else if (Objects.isNull(value)) {
            if (Objects.isNull(value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 预计现场审核时间
     * @param dataEntities
     * @author: hst
     * @createDate: 2024/11/02
     */
    private void estimatedTimeValidate (ExtendedDataEntity[] dataEntities) {
        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            StringBuffer errStr = new StringBuffer();
            StringBuffer subMsg = new StringBuffer();

            if ("1".equals(bill.getString("yd_hasspotaccess"))
                    && (Objects.isNull(bill.getDate("yd_onsitbegindate"))
                    || Objects.isNull(bill.getDate("yd_onsitdate")))) {
                subMsg.append("“现场审核预计完成时间”，");
            }

            if (subMsg.length() > 0) {
                errStr.append("表8 书面评估结论：请填写" + subMsg.substring(0, subMsg.length() - 1) + "。\n");
            }

            if (errStr.length() > 0) {
                this.addErrorMessage(dataEntity, errStr.substring(0, errStr.length() - 1));
            }
        }
    }
}
