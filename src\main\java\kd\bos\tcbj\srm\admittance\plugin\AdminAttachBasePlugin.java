package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.base.AbstractBasePlugIn;
import kd.bos.bill.BillOperationStatus;
import kd.bos.bill.BillShowParameter;
import kd.bos.bill.OperationStatus;
import kd.bos.form.ShowType;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.ItemClassEdit;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachConstant;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachMapConstant;
import kd.bos.tcbj.srm.admittance.enums.BaseDataShowEnum;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;
import kd.bos.tcbj.srm.utils.EnumFieldUtils;
import kd.bos.util.StringUtils;

import java.util.EventObject;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.AdminAttachEditPlugin
 * @className AdminAttachEditPlugin
 * @author: hst
 * @createDate: 2024/07/10
 * @description: (基础资料）资质类型加载表单插件
 * @version: v1.0
 */
public class AdminAttachBasePlugin extends AbstractBasePlugIn {

    /**
     * 注册监听
     * @param e
     * @author: hst
     * @creaetDate: 2024/07/22
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.addF7Listener();
    }

    /**
     * 表单视图模型初始化，创建插件后，触发此事件
     * @author: hst
     * @createDate: 2024/07/11
     */
    @Override
    public void initialize() {
        super.initialize();
        String isInit = this.getPageCache().get("initEnum");
        if (StringUtils.isNull(isInit)) {
            // 初始化下拉控件枚举
            initEnumControl();
            this.getPageCache().put("initEnum", "true");
        }
    }

    /**
     * 初始化下拉控件枚举
     * @author: hst
     * @createDate: 2024/07/11
     */
    private void initEnumControl () {
        String formId = this.getView().getFormShowParameter().getFormId();

        QFilter qFilter = new QFilter(AdminAttachConstant.TYPE_FIELD + "." + AdminAttachMapConstant.ENTITYNAME_FIELD,
                QFilter.equals, formId);

        DataSet typeMap = AdminAttachHelper.getTypeMapInfo(qFilter);

        if (typeMap.hasNext()) {
            // 资质类型
            Map<String, Set<String>> aptTypeInfos = new HashMap();
            // 分类
            Map<String, Set<String>> sortInfos = new HashMap();
            for (Row row : typeMap) {
                // 资质类型字段名
                String aptField = row.getString(AdminAttachMapConstant.APTITUDETYPE_FIELD);
                // 资质类型名称
                String aptName = row.getString(AdminAttachConstant.NAME_FIELD);
                // 资质类型枚举值
                String aptEnum = row.getString(AdminAttachConstant.APTITUDEENUM_FIELD);
                // 是否显示
                String isShow = row.getString(AdminAttachConstant.ISSHOW_FIELD);
                // 分类字段名
                String sortField = row.getString(AdminAttachMapConstant.TYPENAME_FIELD);
                // 分类名称
                String sortName = row.getString("sortname");
                // 分类枚举值
                String sortEnum = row.getString(AdminAttachMapConstant.TYPE_FIELD);

                if (aptTypeInfos.containsKey(aptField)) {
                    Set<String> temp = aptTypeInfos.get(aptField);
                    temp.add(aptName + "&" + aptEnum + "&" + isShow);
                    aptTypeInfos.put(aptField, temp);
                } else {
                    Set<String> temp = new HashSet<>();
                    temp.add(aptName + "&" + aptEnum + "&" + isShow);
                    aptTypeInfos.put(aptField, temp);
                }

                if (sortInfos.containsKey(sortField)) {
                    Set<String> temp = sortInfos.get(sortField);
                    temp.add(sortName + "&" + sortEnum + "&" + isShow);
                    sortInfos.put(sortField, temp);
                } else {
                    Set<String> temp = new HashSet<>();
                    temp.add(sortName + "&" + sortEnum + "&" + isShow);
                    sortInfos.put(sortField, temp);
                }
            }
            // 初始化资质类型下拉控件枚举
            EnumFieldUtils.initEnumControl(this.getView(), aptTypeInfos);
            // 初始化分类下拉控件枚举
            EnumFieldUtils.initEnumControl(this.getView(), sortInfos);
        }
    }

    /**
     * 基础资料监听
     * @author: hst
     * @createDate: 2024/07/22
     */
    private void addF7Listener () {
        // 业务单据
        BasedataEdit billEdit = this.getControl("yd_bill");
        billEdit.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
            beforeF7ViewDetailEvent.setCancel(true);

            // 获取分录选中行
            ItemClassEdit classEdit = (ItemClassEdit) beforeF7ViewDetailEvent.getSource();
            String entryKey = classEdit.getEntryKey();
            int index = beforeF7ViewDetailEvent.getRowKey();
            String type = this.getModel().getDataEntity(true).getDynamicObjectCollection(entryKey)
                    .get(index).getString("yd_billtype");
            if (StringUtils.isNotEmpty(type)) {
                String tarEntity = this.getBillEntity(type);

                BillShowParameter billShowParameter = new BillShowParameter();
                billShowParameter.setPkId(beforeF7ViewDetailEvent.getPkId());
                billShowParameter.setBillStatus(BillOperationStatus.VIEW);
                billShowParameter.setStatus(OperationStatus.VIEW);
                billShowParameter.setBillTypeId(tarEntity);
                billShowParameter.setFormId(tarEntity);
                billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
                this.getView().showForm(billShowParameter);
            }
        });
    }

    /**
     * 通过基础资料标识获取单据标识
     * @param baseEntity
     * @return
     * @author: hst
     * @createDate: 2024/07/22
     */
    private String getBillEntity (String baseEntity) {
        BaseDataShowEnum showEnum = BaseDataShowEnum.getInstance(baseEntity);
        if (Objects.nonNull(showEnum)) {
            return showEnum.getValue();
        }

        return "";
    }
}
