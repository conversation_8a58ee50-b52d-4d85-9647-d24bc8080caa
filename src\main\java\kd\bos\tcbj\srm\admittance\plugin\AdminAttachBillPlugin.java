package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachConstant;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachMapConstant;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;
import kd.bos.tcbj.srm.utils.EnumFieldUtils;
import kd.bos.util.StringUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.AdminAttachBillPlugin
 * @className AdminAttachBillPlugin
 * @author: hst
 * @createDate: 2024/07/10
 * @description: (单据）资质类型加载表单插件
 * @version: v1.0
 */
public class AdminAttachBillPlugin extends AbstractBillPlugIn {

    /**
     * 表单视图模型初始化，创建插件后，触发此事件
     * @author: hst
     * @createDate: 2024/07/11
     */
    @Override
    public void initialize() {
        super.initialize();
        String isInit = this.getPageCache().get("hasInitEnum");
        if (StringUtils.isNull(isInit)) {
            // 初始化下拉控件枚举
            initEnumControl();
            this.getPageCache().put("hasInitEnum", "true");
        }
    }

    /**
     * 初始化下拉控件枚举
     * @author: hst
     * @createDate: 2024/07/11
     */
    private void initEnumControl () {
        String formId = this.getView().getFormShowParameter().getFormId();

        DataSet typeMap = AdminAttachHelper.getTypeMapInfo(null);

        if (typeMap.hasNext()) {
            // 资质类型
            Map<String, Set<String>> aptTypeInfos = new HashMap();
            for (Row row : typeMap) {
                // 附件类型字段名
                String fieldName = row.getString(AdminAttachMapConstant.FIELDNAME_FIELD);
                // 资质类型枚举值
                String typeNum = row.getString(AdminAttachConstant.TYPEENUM_FIELD);
                // 资质类型名称
                String typeName = row.getString(AdminAttachConstant.NAME_FIELD);
                // 是否显示
                String isShow = row.getString(AdminAttachConstant.ISSHOW_FIELD);

                if (aptTypeInfos.containsKey(fieldName)) {
                    Set<String> temp = aptTypeInfos.get(fieldName);
                    temp.add(typeName + "&" + typeNum + "&" + isShow);
                    aptTypeInfos.put(fieldName, temp);
                } else {
                    Set<String> temp = new HashSet<>();
                    temp.add(typeName + "&" + typeNum + "&" + isShow);
                    aptTypeInfos.put(fieldName, temp);
                }
            }
            // 初始化资质类型下拉控件枚举
            EnumFieldUtils.initEnumControl(this.getView(), aptTypeInfos);
        }
    }
}
