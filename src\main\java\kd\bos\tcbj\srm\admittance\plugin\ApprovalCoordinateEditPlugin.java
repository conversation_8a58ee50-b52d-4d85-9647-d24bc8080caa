package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.ILocaleString;
import kd.bos.db.tx.TX;
import kd.bos.db.tx.TXHandle;
import kd.bos.form.control.Control;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.id.ID;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.workflow.design.util.DesignerPluginUtil;
import kd.bos.workflow.engine.WfConstanst;
import kd.bos.workflow.engine.impl.flowchart.BaseTaskHandleRecord;
import kd.bos.workflow.engine.impl.flowchart.TaskHandleRecord;
import kd.bos.workflow.engine.impl.persistence.entity.task.TaskHandleLogEntity;
import kd.bos.workflow.service.WorkflowService;
import kd.bos.workflow.service.impl.ServiceFactory;

import java.util.*;

public class ApprovalCoordinateEditPlugin extends AbstractFormPlugin {

    private static Log log = LogFactory.getLog("ApprovalCoordinateEditPlugin");

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.addClickListeners(new String[]{"btnsubmit_taskcoordinate"});
    }

    @Override
    public void click(EventObject evt) {
        super.click(evt);
        String key = ((Control)evt.getSource()).getKey();
        switch (key) {
            case "btnsubmit_taskcoordinate": {
                this.saveCoordinateRecord();
                break;
            }
        }
    }

    /**
     * 保存协办记录
     * @author: hst
     * @createDate: 2023/11/27
     */
    private void saveCoordinateRecord () {
        String isCoordinate = this.getPageCache().get("is_taskcoordinate");
        if ("true".equals(isCoordinate)) {
            try {
                String taskId = this.getPageCache().get("taskid");
                String userId = String.valueOf(UserServiceHelper.getCurrentUserId());
                QFilter taskFilter = new QFilter("taskid", QFilter.equals, taskId);
                QFilter userFilter = new QFilter("userid", QFilter.equals, userId);
                QFilter typeFilter = new QFilter("type", QFilter.equals, "coordinate");
                DynamicObject[] hiParticipants = BusinessDataServiceHelper.load("wf_hiparticipant"
                        , "id,userid,ownerid,taskid,transferopinion,createdate"
                        , new QFilter[]{taskFilter, userFilter, typeFilter});
                if (hiParticipants.length == 1) {
                    // 父节点id
                    String preId = "";
                    // 前发起人
                    String owner = "";
                    // 路径
                    String way = "";
                    List<DynamicObject> coordinateRecords = new ArrayList<>();

                    // 先保存发起人
                    DynamicObject hiParticipant = hiParticipants[0];

                    BaseTaskHandleRecord taskHandleRecord = this.getHandoverLink(hiParticipant.getLong("taskid")
                            ,hiParticipant.getLong("ownerid")
                            ,hiParticipant.getLong("userid"));

                    List<String> logIds = new ArrayList<>();
                    if (Objects.nonNull(taskHandleRecord)) {
                        while (Objects.nonNull(taskHandleRecord)) {
                            logIds.add(taskHandleRecord.getEntity().getDynamicObject().getString("id"));
                            taskHandleRecord = taskHandleRecord.getNext();
                        }

                        // 查询该记录是否已保存
                        boolean isExist = QueryServiceHelper.exists("yd_coordinaterecord",
                                new QFilter[]{new QFilter("yd_businessid",QFilter.in,
                                        logIds)});
                        if(isExist) {
                            return;
                        }

                        DynamicObjectCollection handLogs = QueryServiceHelper.query("wf_taskhandlelog",
                                "id,taskid,ownerid,assigneeid,opinion,createDate,note",
                                new QFilter[]{new QFilter("id",QFilter.in,logIds)},
                                "createDate asc");

                        for (DynamicObject handLog : handLogs) {
                            DynamicObject coordinateRecord = BusinessDataServiceHelper.newDynamicObject("yd_coordinaterecord");
                            if ("协办请求".equals(handLog.getString("note"))) {
                                coordinateRecord.set("yd_type", "1");
                            } else if ("协办转交".equals(handLog.getString("note"))) {
                                coordinateRecord.set("yd_type", "2");
                            }
                            coordinateRecord.set("yd_taskid", handLog.getString("taskid"));
                            coordinateRecord.set("yd_ownerid", handLog.getString("ownerid"));
                            coordinateRecord.set("yd_ownerformat"
                                    , this.getUserNameFormat(handLog.getLong("ownerid")));
                            coordinateRecord.set("yd_userid", handLog.getString("assigneeid"));
                            coordinateRecord.set("yd_userformat"
                                    , this.getUserNameFormat(handLog.getLong("assigneeid")));
                            coordinateRecord.set("yd_content", handLog.getString("opinion"));
                            coordinateRecord.set("yd_createtime", handLog.getDate("createDate"));
                            coordinateRecord.set("id", ID.genLongId());
                            coordinateRecord.set("yd_preid", preId);
                            coordinateRecord.set("yd_businessid", handLog.getString("id"));
                            // 获取发起协办时上传的附件
                            String attachmentIds = this.getCoordinateTaskAttachments(taskId,
                                    handLog.getString("ownerid"));
                            coordinateRecord.set("yd_attachmentid",attachmentIds);
                            coordinateRecords.add(coordinateRecord);

                            preId = coordinateRecord.getString("id");
                            way = way + "," + coordinateRecord.getString("id");
                        }
                    }

                    // 查询回复信息保存
                    DynamicObject hiComment = BusinessDataServiceHelper.loadSingle("wf_hicomment"
                            , "id,userid,ownerid,taskid,message,time"
                            , new QFilter[]{taskFilter, userFilter});
                    DynamicObject lastRecord = BusinessDataServiceHelper.newDynamicObject("yd_coordinaterecord");
                    lastRecord.set("yd_type", "3");
                    lastRecord.set("yd_taskid", hiComment.getString("taskid"));
                    lastRecord.set("yd_ownerid", hiParticipant.getString("ownerid"));
                    lastRecord.set("yd_ownerformat"
                            , this.getUserNameFormat(hiParticipant.getLong("ownerid")));
                    lastRecord.set("yd_userid", hiComment.getString("userid"));
                    lastRecord.set("yd_userformat"
                            , this.getUserNameFormat(hiComment.getLong("userid")));
                    ILocaleString localeString = hiComment.getLocaleString("message");
                    lastRecord.set("yd_content", localeString.getLocaleValue());
                    lastRecord.set("yd_createtime", hiComment.getDate("time"));
                    lastRecord.set("id", ID.genLongId());
                    lastRecord.set("yd_preid", preId);
                    lastRecord.set("yd_businessid", hiComment.getString("id"));
                    way = way + "," + lastRecord.getString("id");
                    lastRecord.set("yd_way", way);
                    // 获取协办回复时上传的附件
                    String attachmentIds = this.getCoordinateTaskAttachments(taskId,
                            hiComment.getString("userid"));
                    lastRecord.set("yd_attachmentid",attachmentIds);

                    coordinateRecords.add(lastRecord);

                    try (TXHandle h = TX.required("Coordinate.save")) {
                        try {
                            SaveServiceHelper.save(coordinateRecords.toArray(new DynamicObject[coordinateRecords.size()]));
                        } catch (Exception e) {
                            h.markRollback();
                            log.error(e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }

    /**
     * 获取用户格式化名称
     * @param userId
     * @author: hst
     * @createDate: 2023/11/27
     * @return
     */
    private String getUserNameFormat (long userId) {
        String name = "";
        DynamicObject user = UserServiceHelper.getUserInfoByID(userId
                ,"name,entryentity.ispartjob,entryentity.position");
        if (Objects.nonNull(user)) {
            name = user.getString("name");
            for (DynamicObject entry : user.getDynamicObjectCollection("entryentity")) {
                boolean isPart = entry.getBoolean("ispartjob");
                if (!isPart) {
                    name = name + "|" + entry.getString("position");
                }
            }
        }
        return name;
    }

    /**
     * 获取转交链路
     * @param taskId
     * @param from
     * @param to
     * @author: hst
     * @createDate: 2023/04/18
     */
    private BaseTaskHandleRecord getHandoverLink(Long taskId, Long from, Long to) {
        try {
            WorkflowService service = (WorkflowService) ServiceFactory.getService(WorkflowService.class);
            List<TaskHandleLogEntity> entities = service.getTaskService().getTaskHandleLogs(taskId, new String[]{"coordinate", "transfer"});

            if (entities != null && !entities.isEmpty()) {
                Iterator iterator = entities.iterator();

                while (iterator.hasNext()) {
                    TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();
                    TaskHandleRecord taskHandleRecord = new TaskHandleRecord((TaskHandleLogEntity) entity);
                    Set<Long> handledIds = new HashSet();
                    this.buildRelation(taskHandleRecord, entities, handledIds);
                    List<BaseTaskHandleRecord> coordinateRecords = taskHandleRecord.getCoordinateRecords();
                    for (BaseTaskHandleRecord coordinateRecord : coordinateRecords) {
                        // 按时间倒叙，所以from取最后一个，to取第一个
                        // 节点数
                        int nodeNum = 1;
                        Long taskFrom = coordinateRecord.getFrom();
                        Long taskTo = coordinateRecord.getTo();
                        BaseTaskHandleRecord next = coordinateRecord.getNext();
                        while (Objects.nonNull(next)) {
                            taskTo = next.getTo();
                            next = next.getNext();
                            nodeNum++;
                        }
                        if (Objects.nonNull(taskFrom) && Objects.nonNull(taskTo)) {
                            if (taskFrom.longValue() == from.longValue() && taskTo.longValue() == to.longValue()) {
                                return coordinateRecord;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 构建协办转交链路
     * @param current
     * @param entities
     * @param handledIds
     * @author: hst
     * @createDate: 2023/04/18
     */
    private void buildRelation(TaskHandleRecord current, List<TaskHandleLogEntity> entities, Set<Long> handledIds) {
        Long taskOwner = current.getFrom();
        Iterator iterator = entities.iterator();

        while(iterator.hasNext()) {
            TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();

            if (!handledIds.contains(entity.getId()) && (taskOwner.equals(entity.getOwnerId()) && entity.getOwnerId() != WfConstanst.ADMIN || taskOwner == WfConstanst.ADMIN && entity.getOwnerId().equals(current.getTo()))) {
                handledIds.add(entity.getId());
                String type = entity.getType();
                if ("transfer".equals(type)) {
                    TaskHandleRecord next = new TaskHandleRecord(entity);
                    current.setNext(next);
                    if (taskOwner == WfConstanst.ADMIN && entity.getOwnerId().equals(current.getTo())) {
                        current.addCoordinateRecord(new BaseTaskHandleRecord(entity));
                    }

                    this.buildRelation(next, entities, handledIds);
                    break;
                }

                if ("coordinate".equals(type)) {
                    current.addCoordinateRecord(new BaseTaskHandleRecord(entity));
                }
            }
        }

        List<BaseTaskHandleRecord> coordinateRecords = current.getCoordinateRecords();
        if (!coordinateRecords.isEmpty()) {
            Collections.reverse(coordinateRecords);
            Iterator coordinateRecord = current.getCoordinateRecords().iterator();

            while(coordinateRecord.hasNext()) {
                BaseTaskHandleRecord child = (BaseTaskHandleRecord) coordinateRecord.next();
                this.buildCoordinateRelation(child, entities, handledIds);
            }
        }

    }

    /**
     * 构建协办转交链路
     * @param current
     * @param entities
     * @param handledIds
     * @author: hst
     * @createDate: 2023/04/18
     */
    private void buildCoordinateRelation(BaseTaskHandleRecord current, List<TaskHandleLogEntity> entities, Set<Long> handledIds) {
        Long userId = current.getTo();
        Iterator iterator = entities.iterator();
        DynamicObject log = current.getEntity().getDynamicObject();
        Date date = log.getDate("createdate");

        while(iterator.hasNext()) {
            TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();
            if (!handledIds.contains(entity.getId()) && userId.equals(entity.getOwnerId()) && "transfer".equals(entity.getType()) && "coordinateTask".equals(entity.getScenes())) {
                DynamicObject tempLog = entity.getDynamicObject();
                if (tempLog.getDate("createdate").compareTo(date) > 0) {
                    handledIds.add(entity.getId());
                    BaseTaskHandleRecord next = new BaseTaskHandleRecord(entity);
                    current.setNext(next);
                    this.buildCoordinateRelation(next, entities, handledIds);
                }
            }
        }

    }

    /**
     * 获取协办回复时的附件
     * @param businessId
     * @return
     * @author: hst
     * @createDate: 2023/11/27
     */
    private String getCoordinateReciverAttchements (String businessId) {
        String attchementIds = "";
        DynamicObject[] hiAttachments = BusinessDataServiceHelper.load("wf_hiattachment",
                "urlid",new QFilter[]{new QFilter("contentid",QFilter.equals,businessId)});
        for (DynamicObject hiAttachment : hiAttachments) {
            attchementIds = attchementIds + "," + hiAttachment.getString("urlid");
        }
        return attchementIds;
    }

    /**
     * 获取发起协办时上传的附件
     * @param taskId
     * @param userId
     * @author: hst
     * @createDate: 2023/11/30
     */
    private String getCoordinateTaskAttachments (String taskId, String userId) {
        QFilter typeFilter = new QFilter("type",QFilter.equals,"coordinate");
        QFilter taskFilter = new QFilter("taskid",QFilter.equals,taskId);
        QFilter userFitler = new QFilter("userid",QFilter.equals,userId);
        DynamicObject[] hiAttachments = BusinessDataServiceHelper.load("wf_hiattachment","urlid",
                new QFilter[]{typeFilter,taskFilter,userFitler});
        String ids = "";
        for (DynamicObject hiAttachment : hiAttachments) {
            ids = ids + "," + hiAttachment.getString("urlid");
        }
        if (ids.length() > 0) {
            ids = ids.substring(1,ids.length());
        }
        return ids;
    }
}
