package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;

/**
 * 资质审查单插件
 * @auditor yanzuwei
 * @date 2022年9月9日
 * 
 */
public class AptitudeExamExEdit extends AbstractBillPlugIn {
	
	@Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);
		
		// 赋值默认的简易流程
		QFilter filter = new QFilter("number",QCP.equals,"002");
		DynamicObjectCollection col = QueryServiceHelper.query("srm_biztype", "id", filter.toArray());
		if (col.size() > 0) {
			DynamicObject info = BusinessDataServiceHelper.loadSingle(col.get(0).getString("id"), "srm_biztype");
			this.getModel().setValue("ischgflow", Boolean.valueOf(info.getBoolean("ischgflow")));
			this.getModel().setValue("isscene", Boolean.valueOf(info.getBoolean("isscene")));
			this.getModel().setValue("issample", Boolean.valueOf(info.getBoolean("issample")));
			this.getModel().setValue("ismaterial", Boolean.valueOf(info.getBoolean("ismaterial")));
			this.getModel().setValue("isapprove", Boolean.valueOf(info.getBoolean("isapprove")));
			this.getModel().setValue("entertype", info);
		}
	}
}
