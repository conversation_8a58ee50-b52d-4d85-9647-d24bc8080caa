package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.FormShowParameter;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.Arrays;
import java.util.EventObject;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.AuditAttachmentPlugIn
 * @className AuditAttachmentPlugIn
 * @author: hst
 * @createDate: 2023/01/12
 * @description: 单据附件数据在一张动态表单展示
 * @version: v1.0
 */

public class AuditAttachmentPlugIn extends AbstractFormPlugin {

    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        FormShowParameter showParameter = this.getView().getFormShowParameter();
        String pkId = showParameter.getCustomParam("pkId");
        if (StringUtils.isNotBlank(pkId)) {
            List<String> ids = Arrays.stream(pkId.split(",")).
                    filter(StringUtils :: isNotBlank).collect(Collectors.toList());
            DynamicObject[] apAttachment = BusinessDataServiceHelper.load(ids.toArray(),
                    BusinessDataServiceHelper.newDynamicObject("bd_attachment").getDynamicObjectType());
            DynamicObjectCollection attachments = (DynamicObjectCollection) this.getControl("yd_attachment").
                    getModel().getDataEntity().get("yd_attachment");
            for (DynamicObject attachment : apAttachment) {
                attachments.addNew().set("fbasedataid", attachment);
            }
            this.getView().updateView("yd_attachment");
        }

    }

}
