package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;
import java.util.List;
import java.util.Map;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.events.BizDataEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.service.DispatchService;
import kd.bos.service.lookup.ServiceLookup;
import kd.bos.servicehelper.workflow.WorkflowServiceHelper;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordGroup;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordItem;

/**
 * @auditor yanzuwei
 * @date 2022年8月3日
 * 
 */
public class AuditResultList extends AbstractFormPlugin {
	@Override
	public void afterBindData(EventObject e) {
		super.afterBindData(e);
	}
	
	@Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);
		
		String billId = this.getView().getFormShowParameter().getCustomParam("billId").toString();
		List<Map<String, Object>> processWindowRecords = WorkflowServiceHelper.getProcessWindowRecords(billId);
		
		List<IApprovalRecordGroup> allApprovalRecord = WorkflowServiceHelper.getAllApprovalRecord(billId); 
		if (allApprovalRecord.size() > 0) {
			for (IApprovalRecordGroup iApprovalRecordGroup : allApprovalRecord) {
				List<IApprovalRecordItem> recordItems = iApprovalRecordGroup.getChildren();
				for (IApprovalRecordItem item : recordItems) {
					int rowIndex = this.getModel().createNewEntryRow("yd_entryentity");
					this.getModel().setValue("yd_auditpot", item.getActivityName(), rowIndex);  // 审批节点
					this.getModel().setValue("yd_operator", item.getAssignee(), rowIndex);  // 审批人
					if ("提交".equals(item.getMessage())) {
						this.getModel().setValue("yd_operation", "submit", rowIndex);  // 审批操作
					} else {
						this.getModel().setValue("yd_operation", item.getDecisionType(), rowIndex);  // 审批操作
					}
					this.getModel().setValue("yd_opinion", item.getMessage(), rowIndex);  // 审批意见
					this.getModel().setValue("yd_audittime", item.getTime(), rowIndex);  // 审批时间
				}
			}
		}
	}
	
}
