package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.OrmLocaleValue;
import kd.bos.dataentity.metadata.ICollectionProperty;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.control.TreeEntryGrid;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.workflow.component.approvalrecord.ApprovalAttachmentInfo;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordGroup;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordItem;
import kd.bos.workflow.design.util.DesignerPluginUtil;
import kd.bos.workflow.engine.WfConstanst;
import kd.bos.workflow.engine.impl.flowchart.BaseTaskHandleRecord;
import kd.bos.workflow.engine.impl.flowchart.TaskHandleRecord;
import kd.bos.workflow.engine.impl.persistence.entity.task.TaskHandleLogEntity;
import kd.bos.workflow.engine.impl.persistence.entity.task.component.ApprovalRecordGroup;
import kd.bos.workflow.engine.impl.persistence.entity.task.component.ApprovalRecordItem;
import kd.bos.workflow.service.WorkflowService;
import kd.bos.workflow.service.impl.ServiceFactory;
import kd.bos.workflow.service.impl.WorkflowServiceImpl;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EventObject;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.AuditResultListPlugin
 * @className AuditResultListPlugin
 * @author: hst
 * @createDate: 2022/12/23
 * @description: 审批记录
 * @version: v1.0
 */
public class AuditResultListPlugin extends AbstractFormPlugin implements HyperLinkClickListener {

    private final static String TREE_ENTRY = "yd_approvalrecord";
    private Date preDate = null;
    private final static Log log = LogFactory.getLog(AuditResultListPlugin.class);

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        EntryGrid grid = this.getView().getControl(TREE_ENTRY);
        grid.addHyperClickListener(this);
    }

    /**
     * 监听用户点击查看附件时，跳转附件查看动态表单
     * @author: hst
     * @createDate: 2023/01/12
     * @param hyperLinkClickEvent
     */
    @Override
    public void hyperLinkClick(HyperLinkClickEvent hyperLinkClickEvent) {
        String key = hyperLinkClickEvent.getFieldName();
        if ("yd_apattachment".equals(key)) {
            int row = hyperLinkClickEvent.getRowIndex();
            DynamicObject entry = this.getModel().getEntryRowEntity(TREE_ENTRY,row);
            if (Objects.nonNull(entry)) {
                String pkId = entry.getString("yd_apattachmentinfo");
                FormShowParameter formShowParameter = new FormShowParameter();
                formShowParameter.setFormId("yd_attachmentpanel_form");
                StyleCss styleCss = new StyleCss();
                styleCss.setHeight("396");
                styleCss.setWidth("650");
                formShowParameter.setCustomParam("pkId",pkId);
                formShowParameter.getOpenStyle().setInlineStyleCss(styleCss);
                formShowParameter.setCaption("审批附件");
                formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
                this.getView().showForm(formShowParameter);
            }
        }
    }

    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        DynamicObject bill = this.getModel().getDataEntity();
        String billId = bill.getPkValue().toString();
        if (StringUtils.isNotBlank(billId) && !"0".equals(billId)) {
            // 展开全部行
            TreeEntryGrid grid = this.getView().getControl(TREE_ENTRY);
            grid.setCollapse(false);
            try {
                List<IApprovalRecordGroup> approvalRecordGroupList = new WorkflowServiceImpl().getAllApprovalRecordInclCoordinate(billId);
                for (IApprovalRecordGroup approvalRecord : approvalRecordGroupList) {
                    List<IApprovalRecordItem> childrens = approvalRecord.getChildren();
                    String groupType = ((ApprovalRecordGroup) approvalRecord).getGroupType();
                    String activityName = approvalRecord.getActivityName();
                    String throughRule = approvalRecord.getThroughRule();
                    String addSignMsg = approvalRecord.getAddSignMsg();
                    if (StringUtils.isNotBlank(groupType) || StringUtils.isNotBlank(activityName)
                            || StringUtils.isNotBlank(throughRule)) {
                        this.countersigningNodeProcessing(childrens, activityName,throughRule);
                    } else {
                        this.commonNodeProcessing(childrens, addSignMsg);
                    }
                }
            } catch (Exception exceptinon) {
                exceptinon.printStackTrace();
                if (log.isErrorEnabled()) {
                    log.error(exceptinon.getMessage());
                }
            }
            // 设置关闭时不校验值变化
            this.setBizChanged(this.getModel().getDataEntity(),false,
                    new String[]{"yd_nodename","yd_handled","yd_apresults","yd_apopinion","yd_apdate","yd_aphour","yd_throughrule","yd_apattachment","yd_apattachmentinfo"});

        }
    }

    /**
     * 会签节点处理
     * @author: hst
     * @createDate: 2022/12/23
     */
    public void countersigningNodeProcessing(List<IApprovalRecordItem> childrens,String activityName,String throughRule) throws ParseException {
        Date auditDate = null;
        boolean isCoordinate = false;
        int row = this.getView().getModel().getEntryRowCount(TREE_ENTRY);
        for (IApprovalRecordItem children : childrens) {
            if (isCoordinate && !children.isCoordinate() && Objects.nonNull(children.getOwnerId())) {
                // update by hst 2023/11/27 二开记录表是否有保存
                boolean isExite = QueryServiceHelper.exists("yd_coordinaterecord",
                        new QFilter[]{QFilter.of("yd_ownerid = ? and yd_userid = ? and yd_taskid = ? " +
                                        "and yd_createtime = ? and yd_way != ''", children.getOwnerId().toString(),
                                children.getUserId().toString(),children.getTaskId(),children.getTime())});
                if (Objects.isNull(children.getTime())
                        ||(Objects.nonNull(children.getTime()) && !isExite)) {
                    // 转交路径
                    Long taskId = getHandoverLink(Long.valueOf(children.getTaskId()), children.getOwnerId(),
                            children.getUserId());
                    // 是否已多次转交
                    if (Objects.nonNull(taskId)) {
                        this.multipleReferralsForProcessing(taskId, children, row);
                    } else {
                        this.singleReferralsForProcessing(children, row);
                    }
                }
            } else {
                row = this.getModel().createNewEntryRow(TREE_ENTRY);
                if (children.isCoordinate()) {
                    isCoordinate = true;
                    // update by hst 2023/11/27 加载已完成的协办
                    initAlreadyCompleteCoordinateRecord(children,row);
                }
                this.getModel().setValue("yd_nodename", activityName, row);
                int sonRow = this.loadingTaskHandoverRecords(children,row);
                this.getModel().setValue("yd_handled", children.getAssignee(), sonRow);
                this.getModel().setValue("yd_apresults", children.getResult(), sonRow);
                this.getModel().setValue("yd_apopinion", "\n"
                        + (Objects.nonNull(children.getMessage()) ? children.getMessage().toString() : "")
                        + "\n\n", sonRow);
                this.getModel().setValue("yd_apdate", children.getTime(), sonRow);
                this.getModel().setValue("yd_throughrule", throughRule, sonRow);
                // update by hst 2023/01/11 处理时长计算
                if (StringUtils.isNotBlank(children.getMessage())) {
                    if (Objects.isNull(preDate)) {
                        preDate = DateUtil.parseDate(children.getTime());
                    } else {
                        Date endDate = DateUtil.parseDate(children.getTime());
                        String hour = DateUtil.getDifferHour(preDate, endDate);
                        auditDate = Objects.nonNull(auditDate) ? endDate.compareTo(auditDate) > 0 ? endDate : auditDate : endDate;
                        this.getModel().setValue("yd_aphour", hour + "小时", sonRow);
                    }
                }
                // update by hst 2023/01/11 审批附件处理
                this.carryApprovalAttachment(children, sonRow);
            }
        }
        // update by hst 2023/01/11 记录最晚的会签审批时间
        preDate = Objects.nonNull(auditDate) ? auditDate : preDate;
    }

    /**
     * 普通节点处理
     * @author: hst
     * @createDate: 2022/12/23
     */
    public void commonNodeProcessing (List<IApprovalRecordItem> childrens, String addSignMsg) throws ParseException {
        boolean isCoordinate = false;
        int row = this.getModel().createNewEntryRow(TREE_ENTRY);
        for (IApprovalRecordItem children : childrens) {
            if (isCoordinate && !children.isCoordinate()) {
                // update by hst 2023/11/27 二开记录表是否有保存
                boolean isExite = QueryServiceHelper.exists("yd_coordinaterecord",
                        new QFilter[]{QFilter.of("yd_ownerid = ? and yd_userid = ? and yd_taskid = ? " +
                                        "and yd_createtime = ? and yd_way != ''", children.getOwnerId().toString(),
                                children.getUserId().toString(),children.getTaskId(),children.getTime())});
                if (Objects.isNull(children.getTime())
                        ||(Objects.nonNull(children.getTime()) && !isExite)) {
                    // 转交路径
                    Long taskId = getHandoverLink(Long.valueOf(children.getTaskId()), children.getOwnerId(),
                            children.getUserId());
                    // 是否已多次转交
                    if (Objects.nonNull(taskId)) {
                        this.multipleReferralsForProcessing(taskId, children, row);
                    } else {
                        this.singleReferralsForProcessing(children, row);
                    }
                }
            } else {
                if (children.isCoordinate()) {
                    isCoordinate = true;
                    // update by hst 2023/11/27 加载已完成的协办
                    initAlreadyCompleteCoordinateRecord(children,row);
                }
                this.getModel().setValue("yd_nodename", children.getActivityName(), row);
                // update by hst 2023/08/02 加载任务转交记录
                int sonRow = this.loadingTaskHandoverRecords(children,row);
                this.getModel().setValue("yd_handled", children.getAssignee(), sonRow);
                this.getModel().setValue("yd_apresults", children.getResult(), sonRow);
                this.getModel().setValue("yd_apopinion", "\n"
                        + (StringUtils.isNotBlank(addSignMsg) ? addSignMsg + "\n\n" : "")
                        + (Objects.nonNull(children.getMessage()) ? children.getMessage().toString() : "")
                        + "\n\n", sonRow);
                this.getModel().setValue("yd_apdate", children.getTime(), sonRow);
                // update by hst 2023/01/11 处理时长计算
                if (StringUtils.isNotBlank(children.getMessage())) {
                    if (Objects.isNull(preDate)) {
                        preDate = DateUtil.parseDate(children.getTime());
                    } else {
                        Date endDate = DateUtil.parseDate(children.getTime());
                        String hour = DateUtil.getDifferHour(preDate, endDate);
                        preDate = endDate;
                        this.getModel().setValue("yd_aphour", hour + "小时", sonRow);
                    }
                }
                // update by hst 2023/01/11 审批附件处理
                this.carryApprovalAttachment(children,sonRow);
            }
        }
    }

    /**
     * 描述：设置动态对象值改变状态
     * @author: hst
     * @createDate: 2022/12/26
     * @param dataEntity 	对象
     * @param isChanged	对象属性值是否改变
     * @param propKeys	对象属性
     */
    public void setBizChanged(DynamicObject dataEntity, boolean isChanged, String... propKeys) {
        DataEntityPropertyCollection properties = dataEntity.getDataEntityType().getProperties();
        List<ICollectionProperty> collectionProperties = properties.getCollectionProperties(false);
        for (ICollectionProperty collectionPropertie : collectionProperties) {
            // 树形分录
            if ("yd_approvalrecord".equals(collectionPropertie.getName())) {
                DynamicObjectCollection entryColl = dataEntity.getDynamicObjectCollection(collectionPropertie.getName());
                DataEntityPropertyCollection treeEntryProperties = entryColl.getDynamicObjectType().getProperties();
                // 分录存在属性
                for (String propKey : propKeys) {
                    IDataEntityProperty property = treeEntryProperties.get(propKey);
                    if (property != null) {
                        for (DynamicObject entry : entryColl) {
                            entry.getDataEntityState().setBizChanged(property.getOrdinal(), isChanged);
                        }
                    }
                }
            }
        }
    }

    /**
     * 携带审批附件
     * @author: hst
     * @createDate: 2023/01/11
     */
    public void carryApprovalAttachment (IApprovalRecordItem item, int row) {
        if (Objects.nonNull(item.getAttachments())) {
            List<String> ids = new ArrayList<>();
            for (ApprovalAttachmentInfo attachment: item.getAttachments()) {
                ids.add(attachment.getId());
            }
            // update by hst 2023/01/12 分录中不存储附件，只存储附件id，待用户点击再查询
            if (ids.size() > 0) {
                this.getModel().setValue("yd_apattachment", "查看附件", row);
                this.getModel().setValue("yd_apattachmentinfo",this.getModel().getValue("yd_apattachmentinfo",row).toString()
                        + "," + String.join(",",ids),row);
            }
        }
    }

    /**
     * 初始化多次转交记录
     * @param task
     * @param row
     * @return
     * @author: hst
     * @createDate: 2023/04/17
     */
    private int initMultipleTransferRecords (IApprovalRecordItem task, int row) {
        BaseTaskHandleRecord records = this.getHandoverLinkRecords(Long.valueOf(task.getTaskId()), task.getOwnerId(),
                task.getUserId());
        int sonRow = row;
        if (Objects.nonNull(records)) {
            // 获取到的是时间降序，所以从最后开始获取
            while (Objects.nonNull(records)) {
                sonRow = this.getModel().insertEntryRow(TREE_ENTRY, row);
                DynamicObject record = records.getEntity().getDynamicObject();
                // 原处理人
                OrmLocaleValue owner = (OrmLocaleValue) record.get("ownerformat");
                // 现处理人
                OrmLocaleValue assignee = (OrmLocaleValue) record.get("assigneeformat");
                // 处理意见
                OrmLocaleValue opinion = (OrmLocaleValue) record.get("opinion");
                this.getModel().setValue("yd_handled", owner.getLocaleValue(), sonRow);
                this.getModel().setValue("yd_apresults", "协办转交", sonRow);
                this.getModel().setValue("yd_apopinion", "\n" + owner.getLocaleValue() + " 转交 " + assignee.getLocaleValue()
                        + ": \n" + opinion.getLocaleValue() + "\n\n", sonRow);
                this.getModel().setValue("yd_apdate", record.getDate("createdate"), sonRow);
                // update by hst 2023/05/25 获取转交协办时上传的附件
                this.getCoordinateTaskAttachments(record.getString("taskid"),
                        record.getString("ownerid"),sonRow);
                records = records.getNext();
            }
        }
        return sonRow;
    }

    /**
     * 获取转交链路
     * @param taskId
     * @param from
     * @param to
     * @author: hst
     * @createDate: 2023/04/18
     */
    private Long getHandoverLink(Long taskId, Long from, Long to) {
        try {
            WorkflowService service = (WorkflowService) ServiceFactory.getService(WorkflowService.class);
            List<TaskHandleLogEntity> entities = service.getTaskService().getTaskHandleLogs(taskId, new String[]{"coordinate", "transfer"});

            if (entities != null && !entities.isEmpty()) {
                Iterator iterator = entities.iterator();

                while (iterator.hasNext()) {
                    TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();
                    TaskHandleRecord taskHandleRecord = new TaskHandleRecord((TaskHandleLogEntity) entity);
                    Set<Long> handledIds = new HashSet();
                    this.buildRelation(taskHandleRecord, entities, handledIds);
                    List<BaseTaskHandleRecord> coordinateRecords = taskHandleRecord.getCoordinateRecords();
                    for (BaseTaskHandleRecord coordinateRecord : coordinateRecords) {
                        // 按时间倒叙，所以from取最后一个，to取第一个
                        // 节点数
                        int nodeNum = 1;
                        Long taskFrom = coordinateRecord.getFrom();
                        Long taskTo = coordinateRecord.getTo();
                        BaseTaskHandleRecord next = coordinateRecord.getNext();
                        while (Objects.nonNull(next)) {
                            taskTo = next.getTo();
                            next = next.getNext();
                            nodeNum++;
                        }
                        if (Objects.nonNull(taskFrom) && Objects.nonNull(taskTo)) {
                            if (taskFrom.longValue() == from.longValue() && taskTo.longValue() == to.longValue()) {
                                if (nodeNum > 1) {
                                    return coordinateRecord.getId();
                                } else {
                                    return null;
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            if (log.isErrorEnabled()) {
                log.error(e.getMessage());
            }
        }
        return null;
    }
    /**
     * 构建协办转交链路
     * @param current
     * @param entities
     * @param handledIds
     * @author: hst
     * @createDate: 2023/04/18
     */
    private void buildRelation(TaskHandleRecord current, List<TaskHandleLogEntity> entities, Set<Long> handledIds) {
        Long taskOwner = current.getFrom();
        Iterator iterator = entities.iterator();

        while(iterator.hasNext()) {
            TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();

            if (!handledIds.contains(entity.getId()) && (taskOwner.equals(entity.getOwnerId()) && !entity.getOwnerId().equals(WfConstanst.ADMIN) || taskOwner.equals(WfConstanst.ADMIN) && entity.getOwnerId().equals(current.getTo()))) {
                handledIds.add(entity.getId());
                String type = entity.getType();
                if ("transfer".equals(type)) {
                    TaskHandleRecord next = new TaskHandleRecord(entity);
                    current.setNext(next);
                    if (taskOwner.equals(WfConstanst.ADMIN) && entity.getOwnerId().equals(current.getTo())) {
                        current.addCoordinateRecord(new BaseTaskHandleRecord(entity));
                    }

                    this.buildRelation(next, entities, handledIds);
                    break;
                }

                if ("coordinate".equals(type)) {
                    current.addCoordinateRecord(new BaseTaskHandleRecord(entity));
                }
            }
        }

        List<BaseTaskHandleRecord> coordinateRecords = current.getCoordinateRecords();
        if (!coordinateRecords.isEmpty()) {
            Collections.reverse(coordinateRecords);
            Iterator coordinateRecord = current.getCoordinateRecords().iterator();

            while(coordinateRecord.hasNext()) {
                BaseTaskHandleRecord child = (BaseTaskHandleRecord) coordinateRecord.next();
                this.buildCoordinateRelation(child, entities, handledIds);
            }
        }

    }

    /**
     * 构建协办转交链路
     * @param current
     * @param entities
     * @param handledIds
     * @author: hst
     * @createDate: 2023/04/18
     */
    private void buildCoordinateRelation(BaseTaskHandleRecord current, List<TaskHandleLogEntity> entities, Set<Long> handledIds) {
        Long userId = current.getTo();
        Iterator iterator = entities.iterator();
        DynamicObject log = current.getEntity().getDynamicObject();
        Date date = log.getDate("createdate");

        while(iterator.hasNext()) {
            TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();
            if (!handledIds.contains(entity.getId()) && userId.equals(entity.getOwnerId()) && "transfer".equals(entity.getType()) && "coordinateTask".equals(entity.getScenes())) {
                DynamicObject tempLog = entity.getDynamicObject();
                if (tempLog.getDate("createdate").compareTo(date) > 0) {
                    handledIds.add(entity.getId());
                    BaseTaskHandleRecord next = new BaseTaskHandleRecord(entity);
                    current.setNext(next);
                    this.buildCoordinateRelation(next, entities, handledIds);
                }
            }
        }

    }

    /**
     * 多次转交处理
     * @param taskId
     * @param children
     * @param row
     * @author: hst
     * @createDate: 2023/04/18
     */
    private void multipleReferralsForProcessing(Long taskId,IApprovalRecordItem children, int row) {
        int sonRow = this.getModel().insertEntryRow(TREE_ENTRY, row);
        // update by hst 2023/04/17 初始化多次转交记录
        int lastRow = this.initMultipleTransferRecords(children, sonRow);
        this.getModel().setValue("yd_handled", children.getAssignee(), sonRow);
        QFilter filter = new QFilter("id", QFilter.equals, taskId);
        DynamicObject handleLog = BusinessDataServiceHelper.loadSingle("wf_taskhandlelog",
                "ownerformat,assigneeformat,createdate,opinion,taskid,ownerid", new QFilter[]{filter});
        if (Objects.nonNull(handleLog)) {
            // 原处理人
            OrmLocaleValue owner = (OrmLocaleValue) handleLog.get("ownerformat");
            // 现处理人
            OrmLocaleValue assignee = (OrmLocaleValue) handleLog.get("assigneeformat");
            // 处理意见
            OrmLocaleValue opinion = (OrmLocaleValue) handleLog.get("opinion");
            this.getModel().setValue("yd_handled", owner.getLocaleValue(), sonRow);
            this.getModel().setValue("yd_apresults", "邀请协办", sonRow);
            this.getModel().setValue("yd_apopinion", "\n" + owner.getLocaleValue() + " 邀请 " + assignee.getLocaleValue()
                    + " 协办: \n" + opinion.getLocaleValue() + "\n\n", sonRow);
            this.getModel().setValue("yd_apdate", handleLog.getDate("createdate"), sonRow);
            // update by hst 2023/05/25 获取发起协办时上传的附件
            this.getCoordinateTaskAttachments(handleLog.getString("taskid"),
                    handleLog.getString("ownerid"), sonRow);
        }
        sonRow = this.getModel().insertEntryRow(TREE_ENTRY, sonRow);
        this.getModel().setValue("yd_handled", children.getAssignee().length() > 5
                ? children.getAssignee().substring(0,children.getAssignee().length() - 5)
                : children.getAssignee() , sonRow);
        if (Objects.nonNull(children.getTime())) {
            // 将任务完成的时间节点及状态置于最后一个子节点
            this.getModel().setValue("yd_apresults", "协办完成", sonRow);
            this.getModel().setValue("yd_apdate", children.getTime(), sonRow);
            this.getModel().setValue("yd_apopinion", "\n" +children.getAssignee()
                    + " 回复：\n" + (Objects.nonNull(children.getMessage()) ?
                    children.getMessage() : "") + "\n\n", sonRow);
            this.carryApprovalAttachment(children, sonRow);

        } else {
            this.getModel().setValue("yd_apresults", "正在协办", sonRow);
            // update by hst 2023/05/25 获取发起协办时上传的附件
            this.getCoordinateTaskAttachments(children.getTaskId(), children.getUserId().toString(),
                    sonRow);
        }
    }

    /**
     * 单次协办处理
     * @param children
     * @param row
     * @author: hst
     * @createDate: 2023/04/18
     */
    private void singleReferralsForProcessing(IApprovalRecordItem children, int row) {
        int sonRow = this.getModel().insertEntryRow(TREE_ENTRY, row);
        QFilter filter = QFilter.of("taskid = ? and ownerid = ? and assigneeid = ?"
                , children.getTaskId(), children.getOwnerId(),children.getUserId());
        DynamicObject[] handleLogs = BusinessDataServiceHelper.load("wf_taskhandlelog",
                "ownerformat,assigneeformat,createdate,opinion,taskid,ownerid", new QFilter[]{filter});
        List<DynamicObject> temps = Arrays.stream(handleLogs).sorted(Comparator.comparing(data ->
                data.getDate("createdate"))).collect(Collectors.toList());
        DynamicObject handleLog = temps.size() > 0 ? temps.get(temps.size() - 1) : null;

        this.getModel().setValue("yd_handled", children.getOwnerName(), sonRow);
        this.getModel().setValue("yd_apresults", "发起协办", sonRow);

        if (Objects.nonNull(handleLog)) {
            this.getModel().setValue("yd_apopinion", "\n" + children.getOwnerName() + " 邀请 "
                    + UserServiceHelper.getUserInfoByID(children.getUserId()).get("name") + " 协办:\n"
                    + handleLog.getString("opinion") + "\n\n", sonRow);
            this.getModel().setValue("yd_apdate", handleLog.getDate("createdate"), sonRow);
        }
        // 获取发起协办时上传的附件
        this.getCoordinateTaskAttachments(children.getTaskId(),children.getOwnerId().toString(),sonRow);

        // 协办回复
        sonRow = this.getModel().insertEntryRow(TREE_ENTRY, sonRow);
        this.getModel().setValue("yd_handled", children.getAssignee(), sonRow);
        if (Objects.nonNull(children.getTime())) {
            this.getModel().setValue("yd_apopinion", "\n" + children.getAssignee()
                    + " 回复：\n" + (Objects.nonNull(children.getMessage()) ?
                    children.getMessage() : "") + "\n\n", sonRow);
            this.getModel().setValue("yd_apdate", children.getTime(), sonRow);
            this.getModel().setValue("yd_apresults", "协办完成", sonRow);
            // update by hst 2023/01/11 审批附件处理
            this.carryApprovalAttachment(children,sonRow);
        } else {
            this.getModel().setValue("yd_apresults", "正在协办", sonRow);
        }
    }

    /**
     * 获取发起协办时上传的附件
     * @param task
     * @param row
     * @author: hst
     * @createDate: 2023/05/25
     */
    private void getCoordinateTaskAttachments (ApprovalRecordItem task, int row) {
        this.getCoordinateTaskAttachments(task.getTaskId(),task.getUserId().toString(),row);
    }

    /**
     * 获取发起协办时上传的附件
     * @param taskId
     * @param userId
     * @param row
     * @author: hst
     * @createDate: 2023/05/25
     */
    private void getCoordinateTaskAttachments (String taskId, String userId, int row) {
        QFilter typeFilter = new QFilter("type",QFilter.equals,"coordinate");
        QFilter taskFilter = new QFilter("taskid",QFilter.equals,taskId);
        QFilter userFitler = new QFilter("userid",QFilter.equals,userId);
        DynamicObject[] hiAttachments = BusinessDataServiceHelper.load("wf_hiattachment","urlid",
                new QFilter[]{typeFilter,taskFilter,userFitler});
        String ids = this.getModel().getValue("yd_apattachmentinfo",row).toString();
        for (DynamicObject hiAttachment : hiAttachments) {
            ids = ids + "," + hiAttachment.getString("urlid");
        }
        if (ids.length() > 0) {
            this.getModel().setValue("yd_apattachment", "查看附件", row);
            this.getModel().setValue("yd_apattachmentinfo",ids,row);
        }
    }

    /**
     * 加载任务转交记录
     * @param children
     * @param row
     * @author: hst
     * @createDate: 2023/08/02
     */
    private int loadingTaskHandoverRecords (IApprovalRecordItem children, int row) {
        int sonRow = row;
        List<TaskHandleLogEntity> taskHandleLogs = DesignerPluginUtil.getTransferRecordsData(children.getTaskId(), children.getUserId().toString(),
                false, ((WorkflowService)ServiceFactory.getService(WorkflowService.class)).getTaskService());
        for (int i = taskHandleLogs.size() - 1; i >= 0; i--) {
            TaskHandleLogEntity taskHandleLogEntity = taskHandleLogs.get(i);
            DynamicObject record = taskHandleLogEntity.getDynamicObject();
            this.getModel().setValue("yd_handled",record.getLocaleString("owner").getLocaleValue(),sonRow);
            this.getModel().setValue("yd_apresults","任务转交",sonRow);
            this.getModel().setValue("yd_apdate",DateUtil.date2str(record.getDate("createdate"),
                    "yyyy-MM-dd HH:mm:ss"),sonRow);
            this.getModel().setValue("yd_apopinion", "\n" + record.getLocaleString("owner").getLocaleValue()
                    + " 转交 " + record.getLocaleString("assignee").getLocaleValue() + " :\n"
                    + (Objects.nonNull(record.getLocaleString("opinion")) ?
                    record.getLocaleString("opinion").getLocaleValue() : "") + "\n\n",sonRow);
            sonRow = this.getModel().insertEntryRow(TREE_ENTRY, sonRow);
        }
        return sonRow;
    }

    /**
     * 加载已完成的协办记录（二开）
     * @param children
     * @author: hst
     * @createDate: 2023/11/27
     * @return
     */
    private void initAlreadyCompleteCoordinateRecord (IApprovalRecordItem children, int row) {
        String taksId = String.valueOf(children.getTaskId());
        QFilter qFilter = QFilter.of("yd_taskid = ?", taksId);

        DynamicObjectCollection records = QueryServiceHelper.query("yd_coordinaterecord"
                , "id,yd_type,yd_taskid,yd_ownerid,yd_userid,yd_content," +
                        "yd_createtime,yd_way,yd_ownerformat,yd_userformat,yd_businessid,yd_attachmentid"
                , new QFilter[]{qFilter}, "yd_createtime asc");

        // 查询所有路径
        List<String> wayList = new ArrayList<>();
        for (DynamicObject record : records) {
            String way = record.getString("yd_way");
            if (StringUtils.isNotBlank(way)) {
                wayList.add(way);
            }
        }

        // 按每条路径进行处理
        int sonRow = -1;
        for (String way : wayList) {
            String[] ways = way.split(",");
            if (ways.length > 0) {
                sonRow = this.getModel().insertEntryRow("yd_approvalrecord", row);
                for (String wayId : ways) {
                    for (DynamicObject record : records) {
                        String id = record.getString("id");
                        if (StringUtils.isNotBlank(id) && id.equals(wayId)) {
                            // 发起协办或协办转交，取owner
                            String type = record.getString("yd_type");
                            int index = "1".equals(type) ? sonRow : this.getModel().insertEntryRow("yd_approvalrecord", sonRow);

                            if ("1".equals(type) || "2".equals(type)) {
                                this.getModel().setValue("yd_handled", record.getString("yd_ownerformat"), index);
                                this.getModel().setValue("yd_apresults", "1".equals(type) ? "发起协办" : "协办转交", index);
                                this.getModel().setValue("yd_apopinion", "\n" + record.getString("yd_ownerformat")
                                        + ("1".equals(type) ? " 邀请 " : " 转交 ") + record.getString("yd_userformat") + "：\n" +
                                        record.getString("yd_content") + "\n\n", index);
                            } else {
                                this.getModel().setValue("yd_handled", record.getString("yd_userformat"), index);
                                this.getModel().setValue("yd_apresults", "协办回复", index);
                                this.getModel().setValue("yd_apopinion", "\n" + record.getString("yd_userformat") +
                                        " 回复：\n" + record.getString("yd_content") + "\n\n", index);
                            }
                            this.getModel().setValue("yd_apdate", record.getDate("yd_createtime"), index);
                            String attachmentIds = record.getString("yd_attachmentid");
                            if (attachmentIds.length() > 0) {
                                this.getModel().setValue("yd_apattachment","查看附件",index);
                                this.getModel().setValue("yd_apattachmentinfo",attachmentIds,index);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取协办回复时的附件
     * @param businessId
     * @return
     * @author: hst
     * @createDate: 2023/11/27
     */
    private String getCoordinateReciverAttchements (String businessId) {
        String attchementIds = "";
        DynamicObject[] hiAttachments = BusinessDataServiceHelper.load("wf_hiattachment",
                "urlid",new QFilter[]{new QFilter("contentid",QFilter.equals,businessId)});
        for (DynamicObject hiAttachment : hiAttachments) {
            attchementIds = attchementIds + "," + hiAttachment.getString("urlid");
        }
        return attchementIds;
    }

    /**
     * 获取转交链路
     * @param taskId
     * @param from
     * @param to
     * @author: hst
     * @createDate: 2023/04/18
     */
    private BaseTaskHandleRecord getHandoverLinkRecords(Long taskId, Long from, Long to) {
        try {
            WorkflowService service = (WorkflowService) ServiceFactory.getService(WorkflowService.class);
            List<TaskHandleLogEntity> entities = service.getTaskService().getTaskHandleLogs(taskId, new String[]{"coordinate", "transfer"});

            if (entities != null && !entities.isEmpty()) {
                Iterator iterator = entities.iterator();

                while (iterator.hasNext()) {
                    TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();
                    TaskHandleRecord taskHandleRecord = new TaskHandleRecord((TaskHandleLogEntity) entity);
                    Set<Long> handledIds = new HashSet();
                    this.buildRelation(taskHandleRecord, entities, handledIds);
                    List<BaseTaskHandleRecord> coordinateRecords = taskHandleRecord.getCoordinateRecords();
                    for (BaseTaskHandleRecord coordinateRecord : coordinateRecords) {
                        // 按时间倒叙，所以from取最后一个，to取第一个
                        // 节点数
                        int nodeNum = 1;
                        Long taskFrom = coordinateRecord.getFrom();
                        Long taskTo = coordinateRecord.getTo();
                        BaseTaskHandleRecord next = coordinateRecord.getNext();
                        while (Objects.nonNull(next)) {
                            taskTo = next.getTo();
                            next = next.getNext();
                            nodeNum++;
                        }
                        if (Objects.nonNull(taskFrom) && Objects.nonNull(taskTo)) {
                            if (taskFrom.longValue() == from.longValue() && taskTo.longValue() == to.longValue()) {
                                return coordinateRecord.getNext();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (log.isErrorEnabled()) {
                log.error(e.getMessage());
            }
        }
        return null;
    }
}
