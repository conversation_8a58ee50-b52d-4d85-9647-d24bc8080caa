package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.ILocaleString;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.form.control.Control;
import kd.bos.form.plugin.AbstractFormPlugin;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.BillCirculationFormPlugin
 * @className BillCirculationFormPlugin
 * @author: hst
 * @createDate: 2023/04/28
 * @description: 列表传阅功能传阅弹窗数据回传插件
 * @version: v1.0
 */
public class BillCirculationFormPlugin extends AbstractFormPlugin {

    /**
     * 注册监听
     * @author: hst
     * @createDate: 2023/04/28
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        this.addClickListeners(new String[]{"yd_btnok","yd_btncancel"});
    }

    /**
     * 点击事件
     * @author: hst
     * @createDate: 2023/04/28
     * @param evt
     */
    @Override
    public void click(EventObject evt) {
        super.click(evt);
        switch (((Control)evt.getSource()).getKey()) {
            case "yd_btnok" : {
                this.showSubmitBtn();
                break;
            }
            case "yd_btncancel" : {
                this.getView().close();
                break;
            }
        }
    }

    /**
     * 数据回传处理
     * @author: hst
     * @createDate: 2023/04/28
     */
    private void showSubmitBtn() {
        // 传阅人处理
        DynamicObjectCollection userCollection = (DynamicObjectCollection)this.getModel().getValue("yd_circulationperson");
        Set<Long> selectUserList = new HashSet(userCollection.size());
        Iterator user = userCollection.iterator();
        while(user.hasNext()) {
            DynamicObject dynamicObject = (DynamicObject) user.next();
            DynamicObject userObject = dynamicObject.getDynamicObject("fbasedataid");
            Long UserId = (Long)userObject.getPkValue();
            selectUserList.add(UserId);
        }
        // 多语言文本处理
        ILocaleString circulationMsg = (ILocaleString)this.getModel().getValue("yd_taskcirculation");
        LocaleString circulationOption = new LocaleString();
        Iterator circulation = circulationMsg.entrySet().iterator();
        while(circulation.hasNext()) {
            Map.Entry<String, String> entry = (Map.Entry) circulation.next();
            circulationOption.put((String)entry.getKey(), entry.getValue());
        }
        Map<String,Object> result = new HashMap<>();
        result.put("selectUserList",selectUserList);
        result.put("circulationOption",circulationOption);
        this.getView().returnDataToParent(result);
        this.getView().close();
    }
}
