package kd.bos.tcbj.srm.admittance.plugin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kingdee.bos.util.backport.Arrays;
import kd.bos.Interface.utils.HttpUtil;
import kd.bos.common.QueryUtil;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.exception.KDBizException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.metadata.dao.MetaCategory;
import kd.bos.metadata.dao.MetadataDao;
import kd.bos.metadata.form.FormMetadata;
import kd.bos.mvc.list.ListView;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.servicehelper.workflow.MessageCenterServiceHelper;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.bos.workflow.engine.msg.MessageServiceUtil;
import kd.bos.workflow.engine.msg.info.MessageInfo;
import kd.bos.workflow.engine.msg.util.yzj.YunzhijiaToDoUtil;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.EventObject;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.BillCirculationListPlugin
 * @className BillCirculationListPlugin
 * @author: hst
 * @createDate: 2023/04/28
 * @description: 列表传阅功能插件
 * @version: v1.0
 */
public class BillCirculationListPlugin extends AbstractListPlugin {

    private static Log logger = LogFactory.getLog(BillCirculationListPlugin.class);
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    /* 传阅按钮标识 */
    private final static String CIRCULATION_KEY = "yd_circulation";
    private final static String CIRCULATION_ACTION = "taskCirCulation";

    /**
     * 监听按钮点击
     * @author: hst
     * @createDate: 2023/04/28
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case CIRCULATION_KEY : {
                this.openTaskCirCulation();
                break;
            }
        }
    }

    /**
     * 子页面关闭回调
     * @author: hst
     * @createDate: 2023/04/28
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        String key = closedCallBackEvent.getActionId();
        switch (key) {
            case CIRCULATION_ACTION : {
                // 发送消息中心消息
                this.processCallbackInformation(closedCallBackEvent);
                break;
            }
        }
    }

    /**
     * 打开传阅页面
     * @author: hst
     * @createDate: 2023/04/28
     */
    private void openTaskCirCulation() {
        ListSelectedRowCollection selectedRowCollection = this.getSelectedRows();
        if (selectedRowCollection.size() == 0) {
            this.getView().showTipNotification("请至少选择一条数据");
            return;
        }
        FormShowParameter formShowParameter = new FormShowParameter();
        formShowParameter.setCaption("传阅");
        formShowParameter.setFormId("yd_circulation");
        StyleCss css = new StyleCss();
        css.setWidth("500");
        css.setHeight("290");
        formShowParameter.getOpenStyle().setInlineStyleCss(css);
        formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
        formShowParameter.setCloseCallBack(new CloseCallBack(this,"taskCirCulation"));
        this.getView().showForm(formShowParameter);
    }

    /**
     * 处理回调信息
     * @param closedCallBackEvent
     * @author: hst
     * @createDate: 2023/04/28
     */
    private void processCallbackInformation(ClosedCallBackEvent closedCallBackEvent) {
        Object returnData = closedCallBackEvent.getReturnData();
        if (Objects.nonNull(returnData)) {
            Object userList = ((Map<String, Object>) returnData).get("selectUserList");
            Object option = ((Map<String, Object>) returnData).get("circulationOption");
            try {
                if (Objects.nonNull(userList) && Objects.nonNull(option)) {
                    Set<Long> selectUserList = (Set<Long>) userList;
                    LocaleString circulationOption = (LocaleString) option;
                    // 消息中心
                    this.sendMessageCenter(selectUserList,circulationOption);
                    this.getView().showSuccessNotification("发送成功");
                }
            } catch (Exception e) {
                this.getView().showErrorNotification("系统异常，请联系管理员！");
            }
        }
    }

    /**
     * 发送消息中心消息
     * @param selectUserList
     * @param circulationOption
     * @author: hst
     * @createDate: 2023/04/28
     */
    private void sendMessageCenter(Set<Long> selectUserList,LocaleString circulationOption) {
        ListSelectedRowCollection selectedRows = this.getSelectedRows();
        for (ListSelectedRow selectedRow : selectedRows) {
            MessageInfo messageInfo = new MessageInfo();
            // 默认标题标题
            LocaleString title = new LocaleString("单据传阅");
            messageInfo.setMessageTitle(title);
            // 消息内容
            messageInfo.setMessageContent(circulationOption);
            // 消息接收人列表
            messageInfo.setUserIds(Arrays.asList(selectUserList.toArray()));
            // 消息类型
            messageInfo.setType(MessageInfo.TYPE_MESSAGE);
            // 发送人
            Long userId = UserServiceHelper.getCurrentUserId();
            messageInfo.setSenderId(userId);
            // 邮件多人单发模式
            messageInfo.setSignleEmail(true);
            // 业务对象实体编码
            String formId = ((ListView) this.getView()).getBillFormId();
            messageInfo.setEntityNumber(formId);
            // 日期
            messageInfo.setSendTime(new Date());
            // 业务操作
            messageInfo.setOperation("view");
            messageInfo.setBizDataId((Long) selectedRow.getPrimaryKeyValue());
            DynamicObject bill = BusinessDataServiceHelper.loadSingle(selectedRow.getPrimaryKeyValue(), formId);
            if (Objects.nonNull(bill)) {
                // 元数据信息
                FormMetadata formMetadata = (FormMetadata) MetadataDao.readRuntimeMeta(MetadataDao.getIdByNumber(formId, MetaCategory.Form), MetaCategory.Form);
                // 元数据名称
                LocaleString metaName = formMetadata.getRootAp().getName();
                // 当前用户信息
                Map<String, Object> userInfo = UserServiceHelper.getUserInfoByID(userId);
                Object userName = userInfo.get("name");
                LocaleString metaTitle = new LocaleString("请查阅 " + userName +  " 传阅的" + metaName.getLocaleValue() + "【" + bill.getString("billno") + "】");
                messageInfo.setMessageTitle(metaTitle);
            }
            messageInfo.setContentUrl("/ierp/index.html?formId=" + formId + "&pkId=" + selectedRow.getPrimaryKeyValue());
            MessageCenterServiceHelper.sendMessage(messageInfo);

            // ESB待办
            this.sendESBMessage(messageInfo);
        }
    }

    /**
     * 发送EAS待办消息
     * @author: hst
     * @createDate: 2024/07/01
     */
    private void sendESBMessage (MessageInfo message) {
        try {
            String accessToken = "";
            String path = RequestContext.get().getClientFullContextPath();
            if (path.contains("/ierp")) {
                path = path.substring(0,path.indexOf("/ierp"));
            }
            String esbUrl = System.getProperty("esb.url");
            String appId = System.getProperty("esb.appId");
            String appSecret = System.getProperty("esb.appSecret");

            List<MessageInfo> messages = YunzhijiaToDoUtil.rebuildMessage(message);
            logger.info("ESB新增待阅接口 发送ESB链接消息日志：" + message);
            Iterator<MessageInfo> var4 = messages.iterator();
            while (var4.hasNext()) {
                MessageInfo toDoInfo = var4.next();
                String content = toDoInfo.getContent();
                String title = toDoInfo.getTitle();
                Long bizDataId = toDoInfo.getBizDataId();
                if (bizDataId == null || bizDataId.longValue() == 0L)
                    bizDataId = toDoInfo.getId();
                message.getId();
                List userids = toDoInfo.getUserIds();
                String url = toDoInfo.getContentUrl();
                if (url == null)
                    break;
                url = path + url;
                String sendDate = this.sdf.format((message.getSendTime() != null) ? message.getSendTime() : new Date());
                String founder = "系统发送";
                if (message.getSenderId() != null && message.getSenderId().longValue() != 0L) {
                    String senderId = message.getSenderId().toString();
                    QFilter userFilter = new QFilter("id", "=", senderId);
                    DynamicObject bos_user = QueryUtil.loadSingle("bos_user", userFilter.toArray());
                    founder = bos_user.getString("number");
                }
                JSONObject userName = new JSONObject();
                JSONArray userNames = new JSONArray();
                for (int i = 0; i < userids.size(); i++) {
                    QFilter qFilter = new QFilter("id", "=", userids.get(i));
                    DynamicObject user = BusinessDataServiceHelper.loadSingle("bos_user", "id,username", qFilter.toArray());
                    JSONObject LoginName = new JSONObject();
                    if (user != null) {
                        String wids = user.getString("username");
                        userName.put("LoginName", wids + "@by-healthdc.com");
                        LoginName.put("LoginName", wids + "@by-healthdc.com");
                        userNames.add(LoginName);
                    }
                }

                if (StringUtils.isBlank(accessToken)) {
                    String result = HttpUtil.get(esbUrl + "/ESBServer/sysApi/appAuthenticate?appId=" + appId + "&appSecret=" + appSecret);
                    logger.info("ESBESBTodoIntegrationHandler: ===>Get access token: " + result);
                    if (StringUtils.isNotBlank(result)) {
                        JSONObject resultObject = JSONObject.parseObject(result);
                        String errorCode = resultObject.getString("errorCode");

                        if ("00".equals(errorCode)) {
                            accessToken = resultObject.getJSONObject("returnObject").getString("access_token");
                        }
                    }
                }

                if (StringUtils.isNotBlank(accessToken)) {
                    String tcUrl = "http://eip.by-health.com/third/sso/ssoCommon_redirect.jsp?redirectTo=";
                    url = URLEncoder.encode(url, "utf-8");
                    Map<String, String> postValues = new HashMap<>();
                    postValues.put("appName", "FSSC");
                    postValues.put("modelName", "FSSC");
                    postValues.put("modelId", "" + bizDataId);
                    postValues.put("subject", title + (StringUtils.isNotBlank(content) ? ("：" + content) : ""));
                    postValues.put("link", tcUrl + url);
                    postValues.put("mobileLink", tcUrl + url);
                    postValues.put("padLink", tcUrl + url);
                    postValues.put("type", "2");
                    if (userids.size() > 1) {
                        postValues.put("targets", userNames.toString());
                    } else {
                        postValues.put("targets", userName.toString());
                    }
                    postValues.put("createTime", sendDate);

                    JSONObject creators = new JSONObject();
                    creators.put("LoginName", founder);
                    postValues.put("docCreator", creators.toString());
                    logger.info("ESBESBTodoIntegrationHandler: ===>Get " + postValues);
                    String result = HttpUtil.post(esbUrl + "/ESBServer/SysNotifyApi/sendTodo?access_token=" + accessToken, postValues);
                    logger.info("ESBESBTodoIntegrationHandler:===>Start workflow return: " + result);
                    if (StringUtils.isNotBlank(result)) {
                        JSONObject resultObject = JSONObject.parseObject(result);
                        String errorCode = resultObject.getString("errorCode");

                        if ("2".equals(errorCode)) {
                            logger.info("已保存");
                        } else {
                            String errorMessage = resultObject.getString("errorMessage");
                            throw new KDBizException(errorMessage);
                        }
                    }
                } else {
                    throw new KDBizException("获取EAS登录TOKEN失败，请联系管理员！");
                }
            }
        } catch (Exception e) {
            logger.info("ESBESBTodoIntegrationHandler:  ===> workflow error : " + e.getMessage());
            this.getView().showErrorNotification("发送EAS待阅失败：" + e.getMessage());
        }
    }
}
