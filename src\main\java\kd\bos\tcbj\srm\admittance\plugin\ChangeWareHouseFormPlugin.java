package kd.bos.tcbj.srm.admittance.plugin;

import com.alibaba.fastjson.JSONArray;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

import java.util.Date;
import java.util.EventObject;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.ChangeWareHouseFormPlugin
 * @className ChangeWareHouseFormPlugin
 * @author: hst
 * @createDate: 2025/02/26
 * @description: 变更仓库动态表单插件
 * @version: v1.0
 */
public class ChangeWareHouseFormPlugin extends AbstractFormPlugin {

    /**
     * 操作执行后
     * @param e
     * @author: hst
     * @createDate: 2025/02/26
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs e) {
        super.afterDoOperation(e);
        if ("confirm".equals(e.getOperateKey()) && e.getOperationResult().isSuccess()) {
            this.saveResult();
        }
    }

    /**
     * 保存变更结果
     * @author: hongsitao
     * @createDate: 2025/02/26
     */
    private void saveResult () {
        DynamicObject newValue = this.getModel().getDataEntity(true)
                .getDynamicObject("yd_warehouse");
        Object data = this.getView().getFormShowParameter().getCustomParam("billIds");

        if (Objects.nonNull(data)) {
            JSONArray billIds = (JSONArray) data;

            DynamicObject[] bills = BusinessDataServiceHelper.load("yd_rawsupsumbill",
                    "id, yd_warehouse, yd_changeentity.yd_infotype, yd_changeentity.yd_chgfield, " +
                            "yd_changeentity.yd_oldvalue, yd_changeentity.yd_oldvalue, yd_changeentity.yd_newvalue, " +
                            "yd_changeentity.yd_note, yd_changeentity.yd_datastatus, yd_changeentity.yd_changetime",
                    new QFilter("id", QFilter.in, billIds).toArray());

            for (DynamicObject bill : bills) {
                DynamicObject oldValue = bill.getDynamicObject("yd_warehouse");

                if (Objects.isNull(oldValue) || (Objects.nonNull(oldValue)
                        && oldValue.getLong("id") != newValue.getLong("id"))) {
                    DynamicObjectCollection changeEntries = bill.getDynamicObjectCollection("yd_changeentity");
                    DynamicObject changeEntry = changeEntries.addNew();

                    changeEntry.set("yd_infotype", "Z");
                    changeEntry.set("yd_chgfield", "实体仓库");
                    changeEntry.set("yd_oldvalue", Objects.nonNull(oldValue) ? oldValue.getString("name") : "");
                    changeEntry.set("yd_newvalue", Objects.nonNull(newValue) ? newValue.getString("name") : "");
                    changeEntry.set("yd_note", "送货实体仓库变更");
                    changeEntry.set("yd_datastatus", "B");
                    changeEntry.set("yd_changetime", new Date());
                }
            }

            SaveServiceHelper.save(bills);
        }

        this.getView().getParentView().showSuccessNotification("保存成功");
        this.getView().close();
    }
}
