package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.form.field.BasedataEdit;
import kd.scm.common.util.BaseDataViewDetailUtil;

/**
 * 不符合项整改单
 * @auditor yanzuwei
 * @date 2022年8月16日
 * 
 */
public class CorrectionBillEdit extends AbstractBillPlugIn {
	
	@Override
	public void registerListener(EventObject e) {
		super.registerListener(e);
		
		BasedataEdit yd_osfeedback = (BasedataEdit) this.getControl("yd_osfeedback");//现场审核反馈单写打开监听
		yd_osfeedback.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_osfeedbackbill", "yd_osfeedbackbill"));
		});
		
	}
}
