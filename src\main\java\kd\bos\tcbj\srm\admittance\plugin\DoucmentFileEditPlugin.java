package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.events.ChangeData;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.control.AttachmentPanel;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.QuailfiedDocFilingHelper;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.DoucmentFileEditPlugin
 * @className DoucmentFileEditPlugin
 * @author: hst
 * @createDate: 2024/04/25
 * @description: 资质文件归档动态表单插件
 * @version: v1.0
 */
public class DoucmentFileEditPlugin extends AbstractFormPlugin {

    /** 确认操作标识 **/
    private final static String CONFIRM_OP = "confirm";

    /**
     * 操作执行完事件
     * @param afterDoOperationEventArgs
     * @author: hst
     * @createDate: 2023/08/16
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs afterDoOperationEventArgs) {
        super.afterDoOperation(afterDoOperationEventArgs);
        String key = afterDoOperationEventArgs.getOperateKey();
        switch (key) {
            case CONFIRM_OP : {
                // 构建返回的数据
                if (afterDoOperationEventArgs.getOperationResult().isSuccess()) {
                    this.buildReturnData();
                    this.getView().close();
                }
                break;
            }
        }
    }

    /**
     * 值变更事件
     * @param e
     * @author: hst
     * @createDate: 2024/04/25
     */
    @Override
    public void propertyChanged(PropertyChangedArgs e) {
        super.propertyChanged(e);
        String key = e.getProperty().getName();
        switch (key) {
            case QuailfiedDocFilingHelper.NEXTLEVEL_FIELD : {
                this.checkNextLevelChoose(e);
                break;
            }
        }
    }

    /**
     * 校验用户选择
     * @param e
     * @author: hst
     * @createDate: 2024/04/25
     */
    private void checkNextLevelChoose(PropertyChangedArgs e) {
        ChangeData[] changeDatas = e.getChangeSet();
        if (changeDatas.length > 0) {
            ChangeData changeData = changeDatas[0];
            Object newValue = changeData.getNewValue();
            if (Objects.nonNull(newValue) && StringUtils.isNotBlank(newValue.toString())) {
                int rowIndex = changeData.getRowIndex();
                Object superLevel = this.getModel().getValue(QuailfiedDocFilingHelper.SUPERLEVEL_FIELD,rowIndex);
                if (Objects.isNull(superLevel)) {
                    this.getView().showTipNotification("请先选择归档资料第" + (rowIndex + 1) + "行分录的一级类型");
                    this.getModel().setValue(QuailfiedDocFilingHelper.NEXTLEVEL_FIELD,"",rowIndex);
                } else {
                    // 校验选中的二级类型是否包含在一级类型下
                    if (QuailfiedDocFilingHelper.mapping.containsKey(superLevel.toString())) {
                        String contains = QuailfiedDocFilingHelper.mapping.get(superLevel.toString());
                        if (!contains.contains(newValue.toString())) {
                            this.getView().showTipNotification(QuailfiedDocFilingHelper.tipMapping.get(superLevel.toString()));
                            this.getModel().setValue(QuailfiedDocFilingHelper.NEXTLEVEL_FIELD, "", rowIndex);
                        }
                    }
                }
            }
        }
    }

    /**
     * 构建返回的数据
     * @author: hst
     * @createDate: 2023/08/16
     */
    private void buildReturnData () {
        Map<String,Object> returnData = new HashMap<>();
        Object billId = this.getView().getFormShowParameter().getCustomParams().get("billId");
        if (Objects.nonNull(billId) && !"0".equals(billId.toString())) {
            DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId.toString(),"yd_rawmatsupaccessbill");

            // 保存归档资料
            DynamicObjectCollection documents = this.getModel().getDataEntity(true)
                    .getDynamicObjectCollection("yd_doucmententry");
            DynamicObjectCollection alreadDocuments = bill.getDynamicObjectCollection("yd_doucmententry");
            for (DynamicObject document : documents) {
                DynamicObject alreadDocument = alreadDocuments.addNew();
                alreadDocument.set("yd_superlevel",document.get("yd_superlevel"));
                alreadDocument.set("yd_nextlevel",document.get("yd_nextlevel"));
                alreadDocument.set("yd_issudate",document.get("yd_issudate"));
                alreadDocument.set("yd_effectdate",document.get("yd_effectdate"));
                alreadDocument.set("yd_isfile",document.get("yd_isfile"));
                alreadDocument.set("yd_document",document.get("yd_document"));
            }

            // 执行保存
            OperationResult result = OperationServiceHelper.executeOperate("save",
                    "yd_rawmatsupaccessbill",new DynamicObject[]{bill}, OperateOption.create());

            if (result.isSuccess()) {
                returnData.put("isSuccess", true);
                returnData.put("message", "保存成功");
            } else {
                returnData.put("isSuccess", false);
                returnData.put("message", result.getMessage());
            }
        } else {
            returnData.put("isSuccess", false);
            returnData.put("message", "请先保存单据！");
        }
        this.getView().returnDataToParent(returnData);
    }
}
