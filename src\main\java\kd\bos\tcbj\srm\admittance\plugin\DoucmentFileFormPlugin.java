package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.db.DB;
import kd.bos.exception.KDBizException;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachConstant;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachMapConstant;
import kd.bos.tcbj.srm.admittance.entity.SupplyInfoEntryFieldEntiy;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;
import kd.bos.tcbj.srm.admittance.helper.QualifiedDocFilingHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.admittance.vo.AdminAttachInfoVo;
import kd.bos.tcbj.srm.admittance.vo.AdminAttachSetVo;
import kd.bos.tcbj.srm.utils.EnumFieldUtils;

import java.util.EventObject;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.DoucmentFileEditPlugin
 * @className DoucmentFileEditPlugin
 * @author: hst
 * @createDate: 2024/04/25
 * @description: 资质文件归档动态表单插件
 * @version: v1.0
 */
public class DoucmentFileFormPlugin extends AbstractFormPlugin {

    /**
     * 确认操作标识
     **/
    private final static String CONFIRM_OP = "confirm";

    /**
     * 表单视图模型初始化，创建插件后，触发此事件
     *
     * @author: hst
     * @createDate: 2024/07/11
     */
    @Override
    public void initialize() {
        super.initialize();
        String isInit = this.getPageCache().get("hasInitEnum");
        if (StringUtils.isBlank(isInit)) {
            // 初始化下拉控件枚举
            initEnumControl();
            this.getPageCache().put("hasInitEnum", "true");
        }
    }

    /**
     * 初始化下拉控件枚举
     *
     * @author: hst
     * @createDate: 2024/07/11
     */
    private void initEnumControl() {
        DataSet typeMap = AdminAttachHelper.getTypeMapInfo(null);

        if (typeMap.hasNext()) {
            // 资质类型
            Map<String, Set<String>> aptTypeInfos = new HashMap();
            for (Row row : typeMap) {
                // 附件类型字段名
                String fieldName = row.getString(AdminAttachMapConstant.FIELDNAME_FIELD);
                // 资质类型枚举值
                String typeNum = row.getString(AdminAttachConstant.TYPEENUM_FIELD);
                // 资质类型名称
                String typeName = row.getString(AdminAttachConstant.NAME_FIELD);
                // 是否显示
                String isShow = row.getString(AdminAttachConstant.ISSHOW_FIELD);

                if (aptTypeInfos.containsKey(fieldName)) {
                    Set<String> temp = aptTypeInfos.get(fieldName);
                    temp.add(typeName + "&" + typeNum + "&" + isShow);
                    aptTypeInfos.put(fieldName, temp);
                } else {
                    Set<String> temp = new HashSet<>();
                    temp.add(typeName + "&" + typeNum + "&" + isShow);
                    aptTypeInfos.put(fieldName, temp);
                }
            }
            // 初始化资质类型下拉控件枚举
            EnumFieldUtils.initEnumControl(this.getView(), aptTypeInfos);
        }
    }

    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        /* 初始化资质信息 */
        initInfo();
    }

    /**
     * 操作执行完事件
     *
     * @param afterDoOperationEventArgs
     * @author: hst
     * @createDate: 2023/08/16
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs afterDoOperationEventArgs) {
        super.afterDoOperation(afterDoOperationEventArgs);
        String key = afterDoOperationEventArgs.getOperateKey();
        switch (key) {
            case CONFIRM_OP: {
                // 构建返回的数据
                if (afterDoOperationEventArgs.getOperationResult().isSuccess()) {
                    /* 重命名 */
                    this.renameAttach();
                    /* 归档 */
                    QualifiedDocFilingHelper.addAdminAttach(this.getModel().getDataEntity(true),
                            this.getView().getFormShowParameter().getCustomParam("billId"),
                            this.getView().getFormShowParameter().getCustomParam("formId"));
                    this.saveData();
                    this.getView().close();
                }
                break;
            }
            default:
        }
    }

    /**
     * 初始化资质信息
     *
     * @author: hst
     * @createDate: 2024/11/09
     */
    private void initInfo() {
        /* 单据ID */
        Object billId = this.getView().getFormShowParameter().getCustomParam("billId");
        /* 源分录 */
        String entryId = this.getView().getFormShowParameter().getCustomParam("entryId");
        /* 单据标识 */
        String formId = this.getView().getFormShowParameter().getCustomParam("formId");

        if (Objects.nonNull(billId)) {
            DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, formId);

            DynamicObject fileEntry;
            if (StringUtils.isNotBlank(entryId)) {
                fileEntry = bill.getDynamicObjectCollection("yd_file1entry").stream()
                        .filter(entry -> entryId.equals(entry.getString("id"))).findFirst().orElse(null);
                if (Objects.nonNull(fileEntry)) {
                    this.getModel().setValue("yd_supplier", fileEntry.getDynamicObject("yd_csup"));
                    this.getModel().setValue("yd_producers", fileEntry.getDynamicObject("yd_cprod"));
                    this.getModel().setValue("yd_material", getMaterial(bill));
                }
            } else {
                fileEntry = bill;
                this.getModel().setValue("yd_supplier", bill.getDynamicObject("yd_supplier"));
                this.getModel().setValue("yd_producers", bill.getDynamicObject("yd_tempproducers"));
                this.getModel().setValue("yd_material", bill.getDynamicObject("yd_material"));
            }

            if (Objects.nonNull(fileEntry)) {
                List<SupplyInfoEntryFieldEntiy> fieldList = AdminAttachHelper.getFieldMap();

                for (SupplyInfoEntryFieldEntiy filedEntity : fieldList) {//处理对应目标附件字段处理逻辑
                    if (filedEntity == null) {
                        continue;
                    }

                    String entryKey = filedEntity.getEntryEntityName();
                    /* 目标单附件分录 */
                    DynamicObjectCollection mainKeyColl = this.getModel().getDataEntity().getDynamicObjectCollection(entryKey);
                    Set<String> existIds = mainKeyColl.stream().map(temp -> temp.getString(filedEntity.getOriBillIdField())).collect(Collectors.toSet());
                    /* 源单附件分录 */
                    DynamicObjectCollection fileEntryColl = fileEntry.getDynamicObjectCollection(entryKey);

                    if (fileEntryColl != null && fileEntryColl.size() > 0) {
                        for (int index = 0; index < fileEntryColl.size(); index++) {
                            DynamicObject fileEntryInfo = fileEntryColl.get(index);
                            DynamicObject tarEntry = mainKeyColl.addNew();

                            /* id */
                            tarEntry.set("id", fileEntryInfo.getLong("id"));
                            /* 资质类型 */
                            tarEntry.set(filedEntity.getFileTypeField(), fileEntryInfo.get(filedEntity.getFileTypeField()));
                            /* 附件 */
                            QualifiedDocFilingHelper.copyAttach(fileEntryInfo.getDynamicObjectCollection(filedEntity.getFileField()),
                                    tarEntry.getDynamicObjectCollection(filedEntity.getFileField()), true);
                            /* 发证日期 */
                            tarEntry.set(filedEntity.getEffectField(), fileEntryInfo.get(filedEntity.getEffectField()));
                            /* 有效日期 */
                            tarEntry.set(filedEntity.getUnEffectField(), fileEntryInfo.get(filedEntity.getUnEffectField()));
                            /* 是否已重命名 */
                            tarEntry.set(filedEntity.getRenameField(), fileEntryInfo.get(filedEntity.getRenameField()));
                            /* 是否归档 */
                            tarEntry.set(filedEntity.getIsUploadField(), fileEntryInfo.get(filedEntity.getIsUploadField()));
                            /* 原单据ID */
                            tarEntry.set(filedEntity.getOriBillIdField(), fileEntryInfo.get(filedEntity.getOriBillIdField()));
                            /* 原分录ID */
                            tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.get(filedEntity.getOriEntryIdField()));
                            /* 来源单据类型 */
                            tarEntry.set(filedEntity.getBillType(), fileEntryInfo.get(filedEntity.getBillType()));
                            /* 序号 */
                            tarEntry.set("seq", fileEntryInfo.get("seq"));
                        }
                    }
                }
            }
        }

        this.getView().updateView();
    }

    /**
     * 获取物料信息
     * @param bill
     * @return
     * @author: hongsitao
     * @createDate: 2024/11/23
     */
    private DynamicObject getMaterial (DynamicObject bill) {
        String version = bill.getString("yd_version");
        if ("1".equals(version)) {
            /* 源分录 */
            String entryId = this.getView().getFormShowParameter().getCustomParam("entryId");
            if (StringUtils.isNotBlank(entryId)) {
                DynamicObject oriEntry = bill.getDynamicObjectCollection("yd_file1entry").stream()
                        .filter(entry -> entryId.equals(entry.getString("id"))).findFirst().orElse(null);
                if (Objects.nonNull(oriEntry)) {
                    DynamicObject supplier = oriEntry.getDynamicObject("yd_csup");
                    DynamicObject producer = oriEntry.getDynamicObject("yd_cprod");

                    DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_admitentity");
                    for (DynamicObject entry : entries) {
                        if (supplier.getLong("id") == entry.getDynamicObject("yd_admitsup").getLong("id")
                                && producer.getLong("id") == entry.getDynamicObject("yd_admitpro").getLong("id")) {
                            return entry.getDynamicObject("yd_admitmaterial");
                        }
                    }
                }
            } else {
                return bill.getDynamicObject("yd_material");
            }
        }

        return null;
    }

    /**
     * 附件重命名
     * @author: hongsitao
     * @createDate: 2024/11/23
     */
    private void renameAttach () {
        Map<String, AdminAttachSetVo> typeMap = QualifiedDocFilingHelper.getTypeMap();
        DynamicObject bill = this.getModel().getDataEntity();
        Map<String, AdminAttachInfoVo> entryDetails = getAdminAttachInfoVo(typeMap);

        Map<String, String> sortNames = new HashMap<>();
        Map<String, String> sortNumbers = new HashMap<>();
        for (Map.Entry<String, AdminAttachInfoVo> infoVo : entryDetails.entrySet()) {
            AdminAttachInfoVo info = infoVo.getValue();

            String baseField = info.getReNameField();
            if (!sortNames.containsKey(baseField)) {
                DynamicObject object = bill.getDynamicObject(baseField);
                if (Objects.nonNull(object)) {
                    DynamicObject baseData = BusinessDataServiceHelper.loadSingle(object.getString("id"), object.getDynamicObjectType());
                    if (Objects.nonNull(baseData)) {
                        String name = baseData.getString("name");
                        String number = baseData.getString("number");
                        sortNames.put(baseField, name);
                        sortNumbers.put(baseField, number);
                    } else {
                        throw new KDBizException(baseField + "获取不到对应基础资料名称");
                    }
                }
            }
        }

        // 附件重命名
        QualifiedDocFilingHelper.reSetAttachmentName(bill, entryDetails, sortNames, sortNumbers);
    }

    /**
     * 提取分录资质信息
     * @param typeMap
     * @author: hst
     * @createDate: 2024/07/21
     */
    private Map<String, AdminAttachInfoVo> getAdminAttachInfoVo(Map<String, AdminAttachSetVo> typeMap) {
        /* 单据ID */
        Object billId = this.getView().getFormShowParameter().getCustomParam("billId");
        /* 单据标识 */
        String formId = this.getView().getFormShowParameter().getCustomParam("formId");
        DynamicObject oribill = BusinessDataServiceHelper.loadSingle(billId, formId);
        /* 基础资料标识 */
        String baseType = "yd_newmatreqbill".equals(formId) ? "yd_newreqbase" : "yd_rawmatsupaccessbill".equals(formId)
                ? "yd_rawmatsupaccessbase" : "";
        Map<String, AdminAttachInfoVo> entryDetails = new HashMap<>();

        // 先获取所有相关的分录
        Set<String> entryKeys = typeMap.keySet().stream().map(type -> {
            String[] types = type.split("&");
            return types[0] + "&" + types[1] + "&" + types[2] + "&" + types[3] + "&" + types[4];
        }).collect(Collectors.toSet());

        for (String entryKey : entryKeys) {
            String[] keys = entryKey.split("&");
            DynamicObjectCollection details = this.getModel().getDataEntity(true).getDynamicObjectCollection(entryKey.split("&")[2]);
            for (DynamicObject detail : details) {
                String type = detail.getString(keys[3]);

                AdminAttachSetVo setVo = typeMap.get(entryKey + "&" + type);

                AdminAttachInfoVo infoVo = new AdminAttachInfoVo();
                infoVo.setTarEntity(keys[1]);
                infoVo.setMatchField(setVo.getMatchField());
                infoVo.setTarEntry(setVo.getEntryName());
                infoVo.setTarSortField(setVo.getTypeName());
                infoVo.setTarSort(setVo.getType());
                infoVo.setTarAptField(setVo.getAptitudeType());
                infoVo.setTarAptSort(setVo.getAptitudeEnum());
                infoVo.setTarAptNameField(setVo.getAptitudeName());
                infoVo.setTarAptName(setVo.getName());
                infoVo.setTarAttachField(setVo.getAttachName());
                infoVo.setAttachCollection(detail.getDynamicObjectCollection(keys[4]));
                infoVo.setIsSueDateField(setVo.getIsSueName());
                infoVo.setIsSueDate(detail.getDate(setVo.getEffectName()));
                infoVo.setDateToField(setVo.getDateToName());
                infoVo.setDateTo(detail.getDate(setVo.getUnEffectName()));
                infoVo.setReNameField(setVo.getReName());
                infoVo.setPathName(setVo.getPathName());
                infoVo.setParentDataField(setVo.getParentDataField());
                infoVo.setOriEntry(keys[2]);
                infoVo.setIsReNameField(keys[4] + "_re");
                infoVo.setIsAlreadField(keys[4] + "_up");
                infoVo.setOriSeq(detail.getInt("seq"));

                if (0L == detail.getLong("id")) {
                    Long newId = DB.genLongId("");
                    detail.set("id", newId);
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getOriEntryId()))) {
                    infoVo.setEntryId(detail.getString(setVo.getOriEntryId()));
                } else {
                    infoVo.setEntryId(detail.getString("id"));
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getOriBillId()))) {
                    infoVo.setBillId(detail.getString(setVo.getOriBillId()));
                } else {
                    infoVo.setBillId(oribill.getString("id"));
                }

                if (StringUtils.isNotBlank(detail.getString(setVo.getBillType()))) {
                    infoVo.setBillType(detail.getString(setVo.getBillType()));
                } else {
                    infoVo.setBillType(baseType);
                }

                entryDetails.put(entryKey + "&" + detail.getString("seq"), infoVo);
            }
        }

        return entryDetails;
    }

    /**
     * 保存数据
     * @author: hongsitao
     * @createDate: 2024/11/23
     */
    private void saveData() {
        /* 单据ID */
        Object billId = this.getView().getFormShowParameter().getCustomParam("billId");
        /* 源分录 */
        String entryId = this.getView().getFormShowParameter().getCustomParam("entryId");
        /* 单据标识 */
        String formId = this.getView().getFormShowParameter().getCustomParam("formId");

        if (Objects.nonNull(billId)) {
            DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, formId);

            DynamicObject fileEntry;
            if (StringUtils.isNotBlank(entryId)) {
                fileEntry = bill.getDynamicObjectCollection("yd_file1entry").stream()
                        .filter(entry -> entryId.equals(entry.getString("id"))).findFirst().orElse(null);
            } else {
                fileEntry = bill;
            }

            if (Objects.nonNull(fileEntry)) {
                List<SupplyInfoEntryFieldEntiy> fieldList = AdminAttachHelper.getFieldMap();

                for (SupplyInfoEntryFieldEntiy filedEntity : fieldList) {//处理对应目标附件字段处理逻辑
                    if (filedEntity == null) {
                        continue;
                    }

                    String entryKey = filedEntity.getEntryEntityName();
                    /* 目标单附件分录 */
                    DynamicObjectCollection mainKeyColl = fileEntry.getDynamicObjectCollection(entryKey);
                    mainKeyColl.clear();

                    /* 源单附件分录 */
                    DynamicObjectCollection fileEntryColl = this.getModel().getDataEntity().getDynamicObjectCollection(entryKey);

                    if (fileEntryColl != null && fileEntryColl.size() > 0) {
                        for (int index = 0; index < fileEntryColl.size(); index++) {
                            DynamicObject fileEntryInfo = fileEntryColl.get(index);
                            DynamicObject tarEntry = mainKeyColl.addNew();

                            tarEntry.set("id", fileEntryInfo.getLong("id"));
                            /* 资质类型 */
                            tarEntry.set(filedEntity.getFileTypeField(), fileEntryInfo.get(filedEntity.getFileTypeField()));
                            /* 附件 */
                            QualifiedDocFilingHelper.copyAttach(fileEntryInfo.getDynamicObjectCollection(filedEntity.getFileField()),
                                    tarEntry.getDynamicObjectCollection(filedEntity.getFileField()), true);
                            /* 发证日期 */
                            tarEntry.set(filedEntity.getEffectField(), fileEntryInfo.get(filedEntity.getEffectField()));
                            /* 有效日期 */
                            tarEntry.set(filedEntity.getUnEffectField(), fileEntryInfo.get(filedEntity.getUnEffectField()));
                            /* 是否已重命名 */
                            tarEntry.set(filedEntity.getRenameField(), fileEntryInfo.get(filedEntity.getRenameField()));
                            /* 是否归档 */
                            tarEntry.set(filedEntity.getIsUploadField(), fileEntryInfo.get(filedEntity.getIsUploadField()));
                            /* 原单据ID */
                            tarEntry.set(filedEntity.getOriBillIdField(), fileEntryInfo.get(filedEntity.getOriBillIdField()));
                            /* 原分录ID */
                            tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.get(filedEntity.getOriEntryIdField()));
                            /* 来源单据类型 */
                            tarEntry.set(filedEntity.getBillType(), fileEntryInfo.get(filedEntity.getBillType()));
                            /* 序号 */
                            tarEntry.set("seq", fileEntryInfo.get("seq"));
                        }
                    }
                }
            }

            SaveServiceHelper.save(new DynamicObject[]{bill});
        }
    }
}
