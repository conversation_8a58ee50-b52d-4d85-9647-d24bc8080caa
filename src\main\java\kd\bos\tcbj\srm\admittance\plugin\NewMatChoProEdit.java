package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.form.control.Control;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.tcbj.im.util.StringUtils;

import java.util.Arrays;
import java.util.EventObject;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.admittance.plugin.NewMatChoProEdit
 * @className NewMatChoProEdit
 * @author: hst
 * @createDate: 2023/07/04
 * @description: 选择生产商表单插件
 * @version: v1.0
 */
public class NewMatChoProEdit extends AbstractFormPlugin {

    /**
     * 注册监听
     * @author: hst
     * @createDate: 2023/07/04
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        this.addClickListeners(new String[]{"btnok"});
    }

    /**
     * 加载数据
     * @author: hst
     * @createDate: 2023/07/04
     * @param e
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        String param = this.getView().getFormShowParameter().getCustomParam("products");
        if (StringUtils.isNotBlank(param)) {
            List<String> products = Arrays.stream(param.split(",")).
                    filter(StringUtils:: isNotBlank).collect(Collectors.toList());
            for (String product : products) {
                int index = this.getModel().createNewEntryRow("yd_entryentity");
                this.getModel().setValue("yd_product",product,index);
            }
            this.getView().updateView("yd_entryentity");
        }
    }

    /**
     * 监听事件
     * @author: hst
     * @createDate: 2023/07/04
     * @param evt
     */
    @Override
    public void click(EventObject evt) {
        super.click(evt);
        switch (((Control)evt.getSource()).getKey()) {
            case "btnok" : {
                this.buildReturnData();
                break;
            }
        }
    }

    /**
     * 注册监听
     * @author: hst
     * @createDate: 2023/07/04
     */
    private void buildReturnData() {
        EntryGrid entryGrid = this.getControl("yd_entryentity");
        int selectRows[] = entryGrid.getSelectRows();
        if (selectRows.length != 1) {
            this.getView().showTipNotification("请选择一个供应商");
        } else {
            String proName = this.getModel().getValue("yd_product",selectRows[0]).toString();
            this.getView().returnDataToParent(proName);
            this.getView().close();
        }
    }
}
