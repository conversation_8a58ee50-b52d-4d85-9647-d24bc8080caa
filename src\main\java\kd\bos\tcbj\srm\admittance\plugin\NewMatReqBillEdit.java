package kd.bos.tcbj.srm.admittance.plugin;

import java.io.*;
import java.util.*;

import kd.bos.cache.CacheFactory;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.AnchorItems;
import kd.bos.entity.datamodel.events.AfterAddRowEventArgs;
import kd.bos.entity.datamodel.events.ChangeData;
import kd.bos.ext.form.control.AnchorControl;
import kd.bos.form.control.Control;
import kd.bos.form.control.Toolbar;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.utils.AttachmentUtil;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;
import org.apache.commons.lang3.StringUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.events.BizDataEventArgs;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.entity.report.CellStyle;
import kd.bos.exception.KDBizException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.control.events.RowClickEventListener;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.operate.FormOperate;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.UIUtils;
import kd.bos.tcbj.srm.admittance.helper.BizBillHelper;
import kd.bos.tcbj.srm.admittance.helper.NewMatReverseHelper;

/**
 * @auditor yanzuwei
 * @date 2022年6月21日
 * 
 */
public class NewMatReqBillEdit extends AbstractBillPlugIn
implements RowClickEventListener
{
  	 private static String entryKey="yd_resedsupentry";
  	 int selIdx[]=null;
	/* 下载附件按钮标识 */
	private final static String[] downItems = new String[]{"yd_resedsup","yd_suggest","yd_materialinfo",
			"yd_file1","yd_supply","yd_dnewsup","yd_stryfile","yd_downdomesattach","yd_downforeigattach",
			"yd_downagentattach","yd_downpstarattach","yd_downpfitienattach"};

	@Override
	public void registerListener(EventObject e) {
		// TODO Auto-generated method stub
		super.registerListener(e);
		EntryGrid entryGrid = this.getView().getControl(entryKey);
		entryGrid.addRowClickListener(this);

		EntryGrid entryGrid2 = this.getView().getControl("entryentity");
		entryGrid2.addRowClickListener(this);

		this.addItemClickListeners(new String[]{"yd_advcontoolbarap1","yd_advcontoolbarap4",
				"yd_advcontoolbarap9","yd_advcontoolbarap10","yd_advcontoolbarap7","yd_advcontoolbarap3",
				"yd_advcontoolbarap17","yd_advcontoolbarap16","yd_advcontoolbarap15","yd_advcontoolbarap14",
				"yd_advcontoolbarap13"});
		Toolbar toolbar = this.getView().getControl("yd_advcontoolbarap11");
		toolbar.addItemClickListener(this);
	}

	@Override
	public void afterLoadData(EventObject e) {
		super.afterLoadData(e);

		/* update by hst 2024/11/14 增加锚点设置 */
		this.initAnchorPoint();
	}



	@Override
	public void beforeDoOperation(BeforeDoOperationEventArgs e) {
		super.beforeDoOperation(e);
		FormOperate operate = (FormOperate) e.getSource();
		String key = operate.getOperateKey();

		if ("deleteentry".equals(key)) {
			EntryGrid entryGrid = this.getView().getControl(entryKey);
			selIdx = entryGrid.getSelectRows();
			delRows(this.selIdx);
		} else if ("yd_download".equals(key)) {
			//update by hst 2023/07/17 资质文件下载
			this.getAttachments("");
		} else if (StringUtil.equalsIgnoreCase("yd_gen_supinfo", key))
		{
			//创建资料补充函
			genSupInfoBill("gen_supinfo",getSupIds4Info());
		}else if (StringUtil.equalsIgnoreCase("yd_gen_supacc", key))
		{
			//创建原辅料供应商准入单
			genSupInfoBill("gen_supacc",getSupIds4Acc());
		}else if (StringUtil.equalsIgnoreCase("yd_gen_notice", key))
		{
			//创建现场审核通知单
			genSupInfoBill("gen_notice",getSupIds("yd_hsupentry","yd_hsup","yd_hprod"));
		}else if (StringUtil.equalsIgnoreCase("yd_gen_feedback", key))
		{
			//创建现场审核反馈单
			genSupInfoBill("gen_feedback",getSupIds("yd_hsupentry","yd_hsup","yd_hprod"));
		}
	}

	@Override
	 public void afterDoOperation(AfterDoOperationEventArgs e) {
		super.afterDoOperation(e);

		String key = e.getOperateKey();
		if ("newentry".equals(key) || "scm_newentry".equals(key)) {
			addRows();

		}
//		else if ("deleteentry".equals(key)) {
//			delRows(this.selIdx);
//		}
	}


	@Override
	public void propertyChanged(PropertyChangedArgs e)
	{
		 String fieldKey = e.getProperty().getName();

		 if("yd_ressup".equals(fieldKey))
		 {
			 setSup(getModel().getEntryCurrentRowIndex(entryKey));
		 }else if("yd_sup".equals(fieldKey))
		 {
			 setSup2(getModel().getEntryCurrentRowIndex("entryentity"));
		 }else if("yd_resnewsupname".equals(fieldKey))
		 {
			 setSupName(e.getChangeSet()[0].getOldValue(), e.getChangeSet()[0].getNewValue());
		 }else if("yd_recomsup".equals(fieldKey))
		 {
			 setSupName2(e.getChangeSet()[0].getOldValue(), e.getChangeSet()[0].getNewValue());
			 // update by hst 2023/07/21 根据供应链选择修改分录锁定性
			 lockFieldBySCM(e);
		 }else if("yd_csup".equals(fieldKey))
		 {
			 setNewSup(getModel().getEntryCurrentRowIndex("yd_file1entry"));
		 }else if("yd_aprod".equals(fieldKey))
		 {
			 setProd(getModel().getEntryCurrentRowIndex("yd_materialentry"));
		 }else if("yd_asup".equals(fieldKey))
		 {
			 setProd(getModel().getEntryCurrentRowIndex("yd_materialentry"));
		 }else if("yd_cprod".equals(fieldKey))
		 {
			 setProdLast(getModel().getEntryCurrentRowIndex("yd_file1entry"));
		 }else if ("yd_scmcmd".equals(fieldKey)) {
			 // update by hst 2023/07/21 根据供应链选择修改分录锁定性
			 lockFieldBySCM(e);
		 }else if ("yd_pilotmaterial".equals(fieldKey)) {
			 // update by hst 2024/11/21 物料代码填充
			 setNewMat(getModel().getEntryCurrentRowIndex("yd_pilotentry"));
		 }
	}

	@Override
	public void afterAddRow(AfterAddRowEventArgs e) {
		super.afterAddRow(e);
		String key = e.getEntryProp().getName();
		if(entryKey.equals(key))
		{
			addRows();
		}
		else if("entryentity".equals(key))
		{
			addRows2();
		}
	}

	   @Override
		public void createNewData(BizDataEventArgs e)
	   {
		  super.createNewData(e);

		   /* update by hst 2024/11/14 增加锚点设置 */
		   this.initAnchorPoint();
		}

	   private void addRows() {//选择供应商分录条数同步
		   IDataModel model = getModel();
		   int count = model.getEntryRowCount(entryKey);

		   String[] entryKeys = null;
		   if ("1".equals(model.getValue("yd_version"))) {
			   entryKeys = new String[]{"entryentity", "yd_materialentry", "yd_matanalyentry", "yd_stryentry",
					   "yd_file1entry", "yd_file2entry", "yd_scoreentry", "yd_pilotentry",
					   "yd_hsupentry", "yd_admitentity", "yd_auditentry", "yd_entryentity"};
		   } else {
			   entryKeys = new String[]{"entryentity", "yd_materialentry", "yd_matanalyentry", "yd_stryentry",
					   "yd_file1entry", "yd_file2entry", "yd_scoreentry",
					   "yd_hsupentry", "yd_gsupentry", "yd_auditentry", "yd_entryentity"};
		   }
		   for (int i = 0; i < entryKeys.length; i++) {
			   String tarEntKey = entryKeys[i];
			   int subCount = model.getEntryRowCount(tarEntKey);
			   if (count > subCount) {
				   model.createNewEntryRow(tarEntKey);
			   }
		   }
	   }

	   private void addNewSupRows() {//选择供应商分录条数同步
		   IDataModel model = getModel();
		   int count = model.getEntryRowCount(entryKey);

		   String[] entryKeys = null;
		   if ("1".equals(model.getValue("yd_version"))) {
			   entryKeys = new String[]{"yd_file2entry", "yd_scoreentry", "yd_hsupentry", "yd_admitentity", "yd_auditentry"};
		   } else {
			   entryKeys = new String[]{"yd_file2entry", "yd_scoreentry", "yd_hsupentry", "yd_gsupentry", "yd_auditentry"};
		   }

		   for (int i = 0; i < entryKeys.length; i++) {
			   String tarEntKey = entryKeys[i];
			   int subCount = model.getEntryRowCount(tarEntKey);
			   if (count > subCount) {
				   model.batchCreateNewEntryRow(tarEntKey, count - subCount);
			   }
		   }
	   }


		private void delRows(int idxs[]) {//删除分录
			if (idxs == null) {
				return;
			}

			IDataModel model = getModel();
			String[] entryKeys = null;
			if ("1".equals(model.getValue("yd_version"))) {
				entryKeys = new String[]{"entryentity&yd_recomsup", "yd_materialentry&yd_anewsupname",
						"yd_matanalyentry&yd_bnewsupname", "yd_stryentry&yd_strynewsupname",
						"yd_file1entry&yd_cnewsupname", "yd_file2entry&yd_dnewsupname",
						"yd_scoreentry&yd_enewsupname", "yd_pilotentry&yd_pilotname",
						"yd_auditentry&yd_fnewsupname", "yd_hsupentry&yd_hnewsupname",
						"yd_admitentity&yd_admitproname", "yd_entryentity&yd_snewsupname"};
			} else {
				entryKeys = new String[]{"entryentity&yd_recomsup", "yd_materialentry&yd_anewsupname",
						"yd_matanalyentry&yd_bnewsupname", "yd_stryentry&yd_strynewsupname",
						"yd_file1entry&yd_cnewsupname", "yd_file2entry&yd_dnewsupname",
						"yd_scoreentry&yd_enewsupname", "yd_auditentry&yd_fnewsupname",
						"yd_hsupentry&yd_hnewsupname", "yd_gsupentry&yd_gnewsupname",
						"yd_entryentity&yd_snewsupname"};
			}

			model.beginInit();
			for (int i = 0; i < idxs.length; i++) {
				int index = idxs[i];
				String proName = this.getModel().getValue("yd_resnewsupname", index).toString();

				for (int j = 0; j < entryKeys.length; j++) {
					String[] keys = entryKeys[j].split("&");

					DynamicObject entry = this.getModel().getDataEntity(true).getDynamicObjectCollection(keys[0])
							.stream().filter(o -> proName.equals(o.getString(keys[1]))).findFirst().orElse(null);

					if (Objects.nonNull(entry)) {
						this.getModel().getDataEntity(true).getDynamicObjectCollection(keys[0]).remove(entry);
					}
				}


			}
			model.endInit();
			for (int i = 0; i < entryKeys.length; i++) {
				this.getView().updateView(entryKeys[i].split("&")[0]);
			}
		}

	private void setSup(int row)
	{
		IDataModel  model=getModel();
		DynamicObject  supInfo=(DynamicObject)model.getValue("yd_ressup",row);
		if (supInfo == null) {
			return;
		}

		String[] entryKeys = null;
		if ("1".equals(model.getValue("yd_version"))) {
			entryKeys = new String[]{"yd_sup", "yd_asup", "yd_bsup", "yd_strysup",
					"yd_csup", "yd_dsup", "yd_esup", "yd_admitsup", "yd_hsup", "yd_fsup", "yd_pilotsup"};
		} else {
			entryKeys = new String[]{"yd_sup", "yd_asup", "yd_bsup", "yd_strysup",
					"yd_csup", "yd_dsup", "yd_esup",
					"yd_gsup", "yd_hsup", "yd_fsup"};
		}
		for(int i=0;i<entryKeys.length;i++)
		{
		   model.setValue(entryKeys[i], supInfo, row);
		}
	}

	private void setSup2(int row)
	{
		IDataModel  model=getModel();
		DynamicObject  supInfo=(DynamicObject)model.getValue("yd_sup",row);
		if (supInfo == null) {
			return;
		}

		String[] entryKeys = null;
		if ("1".equals(model.getValue("yd_version"))) {
			entryKeys = new String[]{"yd_asup", "yd_bsup", "yd_strysup",
					"yd_csup", "yd_dsup", "yd_esup", "yd_admitsup", "yd_hsup", "yd_fsup", "yd_pilotsup"};
		} else {
			entryKeys = new String[]{"yd_asup", "yd_bsup", "yd_strysup",
					"yd_csup", "yd_dsup", "yd_esup",
					"yd_gsup", "yd_hsup", "yd_fsup"};
		}

		for(int i=0;i<entryKeys.length;i++)
		{
		   model.setValue(entryKeys[i], supInfo, row);
		}
	}

	private void setSupName(Object oldValue, Object newValue) {
		IDataModel model = getModel();
		String[] entryNameKeys = null;

		if ("1".equals(model.getValue("yd_version"))) {
			entryNameKeys = new String[]{"entryentity&yd_recomsup", "yd_materialentry&yd_anewsupname",
					"yd_matanalyentry&yd_bnewsupname", "yd_stryentry&yd_strynewsupname",
					"yd_file1entry&yd_cnewsupname", "yd_file2entry&yd_dnewsupname",
					"yd_scoreentry&yd_enewsupname", "yd_pilotentry&yd_pilotname",
					"yd_auditentry&yd_fnewsupname", "yd_hsupentry&yd_hnewsupname",
					"yd_admitentity&yd_admitproname", "yd_entryentity&yd_snewsupname"};
		} else {
			entryNameKeys = new String[]{"entryentity&yd_recomsup", "yd_materialentry&yd_anewsupname",
					"yd_matanalyentry&yd_bnewsupname", "yd_stryentry&yd_strynewsupname",
					"yd_file1entry&yd_cnewsupname", "yd_file2entry&yd_dnewsupname",
					"yd_scoreentry&yd_enewsupname", "yd_auditentry&yd_fnewsupname",
					"yd_hsupentry&yd_hnewsupname", "yd_gsupentry&yd_gnewsupname",
					"yd_entryentity&yd_snewsupname"};
		}

		for(int i=0;i<entryNameKeys.length;i++) {
			String[] keys = entryNameKeys[i].split("&");
			DynamicObject entry = null;

			if (Objects.nonNull(oldValue) && StringUtils.isNotBlank(oldValue.toString())) {
				entry = this.getModel().getDataEntity(true).getDynamicObjectCollection(keys[0])
						.stream().filter(o -> oldValue.toString().equals(o.getString(keys[1]))).findFirst().orElse(null);
			} else {
				entry = this.getModel().getDataEntity(true).getDynamicObjectCollection(keys[0])
						.stream().filter(o -> "".equals(o.getString(keys[1]))).findFirst().orElse(null);
			}

			if (Objects.nonNull(entry)) {
				entry.set(keys[1], newValue);
			}
		}

		this.getView().updateView();
	}

	//监听生产商并赋值到后面几个分录中的生产商
	private void setProd(int row)
	{
		IDataModel  model=getModel();
		DynamicObject supName = (DynamicObject) model.getValue("yd_aprod", row);
		String[] entryNameKeys = null;
		if ("1".equals(model.getValue("yd_version"))) {
			entryNameKeys = new String[]{"yd_bprod", "yd_stryprod", "yd_cprod", "yd_dprod", "yd_eprod",
					"yd_fprod", "yd_hprod", "yd_admitpro", "yd_pilotpro"};
		} else {
			entryNameKeys = new String[]{"yd_bprod", "yd_stryprod", "yd_cprod", "yd_dprod", "yd_eprod",
					"yd_fprod", "yd_hprod", "yd_gprod"};
		}

		for(int i=0;i<entryNameKeys.length;i++)
		{
			model.setValue(entryNameKeys[i], supName, row);
		}
	}

	//监听生产商并赋值到后面几个分录中的生产商
	private void setProdLast(int row)
	{
		IDataModel  model=getModel();
		DynamicObject supName = (DynamicObject) model.getValue("yd_cprod", row);
		String[] entryNameKeys = null;
		if ("1".equals(model.getValue("yd_version"))) {
			entryNameKeys = new String[]{"yd_dprod", "yd_eprod",
					"yd_fprod", "yd_hprod", "yd_admitpro", "yd_sprod", "yd_pilotpro"};
		} else {
			entryNameKeys = new String[]{"yd_dprod", "yd_eprod",
					"yd_fprod", "yd_hprod", "yd_gprod", "yd_sprod"};
		}
		for(int i=0;i<entryNameKeys.length;i++)
		{
			model.setValue(entryNameKeys[i], supName, row);
		}
	}

	private void setSupName2(Object oldValue, Object newValue)
	{
		IDataModel  model=getModel();
		String[] entryNameKeys = null;
		if ("1".equals(model.getValue("yd_version"))) {
			entryNameKeys = new String[]{"yd_materialentry&yd_anewsupname",
					"yd_matanalyentry&yd_bnewsupname", "yd_stryentry&yd_strynewsupname",
					"yd_file1entry&yd_cnewsupname", "yd_file2entry&yd_dnewsupname",
					"yd_scoreentry&yd_enewsupname", "yd_pilotentry&yd_pilotname",
					"yd_auditentry&yd_fnewsupname", "yd_hsupentry&yd_hnewsupname",
					"yd_admitentity&yd_admitproname", "yd_entryentity&yd_snewsupname"};
		} else {
			entryNameKeys = new String[]{"yd_materialentry&yd_anewsupname",
					"yd_matanalyentry&yd_bnewsupname", "yd_stryentry&yd_strynewsupname",
					"yd_file1entry&yd_cnewsupname", "yd_file2entry&yd_dnewsupname",
					"yd_scoreentry&yd_enewsupname", "yd_auditentry&yd_fnewsupname",
					"yd_hsupentry&yd_hnewsupname", "yd_gsupentry&yd_gnewsupname",
					"yd_entryentity&yd_snewsupname"};
		}

		for(int i=0;i<entryNameKeys.length;i++) {
			String[] keys = entryNameKeys[i].split("&");
			DynamicObject entry = null;

			if (Objects.nonNull(oldValue) && StringUtils.isNotBlank(oldValue.toString())) {
				entry = this.getModel().getDataEntity(true).getDynamicObjectCollection(keys[0])
						.stream().filter(o -> oldValue.toString().equals(o.getString(keys[1]))).findFirst().orElse(null);
			} else {
				entry = this.getModel().getDataEntity(true).getDynamicObjectCollection(keys[0])
						.stream().filter(o -> "".equals(o.getString(keys[1]))).findFirst().orElse(null);
			}

			if (Objects.nonNull(entry)) {
		  		 entry.set(keys[1], newValue);
			}
		}

		this.getView().updateView();
	}

	// 赋值生产商
	private void setNewProd(int row)
	{
		IDataModel  model=getModel();
		String supName = model.getValue("yd_producers", row).toString();

		String[] entryNameKeys = null;
		if ("1".equals(model.getValue("yd_version"))) {
			entryNameKeys = new String[]{"yd_aprod", "yd_bprod", "yd_stryprod",
					"yd_cprod", "yd_dprod", "yd_eprod", "yd_pilotpro",
					"yd_fprod", "yd_hprod", "yd_gprod", "yd_admitpro"};
		} else {
			entryNameKeys = new String[]{"yd_aprod", "yd_bprod", "yd_stryprod",
					"yd_cprod", "yd_dprod", "yd_eprod",
					"yd_fprod", "yd_hprod"};
		}
		for(int i=0;i<entryNameKeys.length;i++)
		{
			model.setValue(entryNameKeys[i], supName, row);
		}
	}

	private void setNewSup(int row)
	{
		IDataModel  model=getModel();
		DynamicObject  supInfo=(DynamicObject)model.getValue("yd_csup",row);
		if (supInfo == null) {
			return;
		}
		String[] entryKeys = null;

		if ("1".equals(model.getValue("yd_version"))) {
			entryKeys = new String[]{"yd_dsup", "yd_esup", "yd_pilotsup",
					"yd_admitsup", "yd_hsup", "yd_fsup", "yd_ssup", "yd_pilotsup"};
		} else {
			entryKeys = new String[]{"yd_dsup", "yd_esup",
					"yd_gsup", "yd_hsup", "yd_fsup", "yd_ssup"};
		}
		for(int i=0;i<entryKeys.length;i++)
		{
		   model.setValue(entryKeys[i], supInfo, row);
		}
	}

	/**
	 * 物料代码填充
	 * @param row
	 * @author: hongsitao
	 * @createDate: 2024/11/21
	 */
	private void setNewMat(int row)
	{
		IDataModel model = getModel();
		DynamicObject matInfo = (DynamicObject) model.getValue("yd_pilotmaterial",row);
		if (matInfo == null) {
			return;
		}
		String[] entryKeys = new String[]{"yd_admitmaterial"};

		for(int i=0;i<entryKeys.length;i++)
		{
			model.setValue(entryKeys[i], matInfo, row);
		}
	}

	private void addRows2() {//供应商分录条数同步
		IDataModel model = getModel();
		int count = model.getEntryRowCount("entryentity");

		String[] entryKeys = null;
		if ("1".equals(model.getValue("yd_version"))) {
			entryKeys = new String[]{"yd_materialentry", "yd_matanalyentry", "yd_stryentry",
					"yd_file1entry", "yd_file2entry", "yd_scoreentry", "yd_pilotentry",
					"yd_hsupentry", "yd_admitentity", "yd_auditentry", "yd_entryentity"};
		} else {
			entryKeys = new String[]{"yd_materialentry", "yd_matanalyentry", "yd_stryentry",
					"yd_file1entry", "yd_file2entry", "yd_scoreentry",
					"yd_hsupentry", "yd_gsupentry", "yd_auditentry", "yd_entryentity"};
		}
		for (int i = 0; i < entryKeys.length; i++) {
			String tarEntKey = entryKeys[i];
			int subCount = model.getEntryRowCount(tarEntKey);
			if (count > subCount) {
				model.createNewEntryRow(tarEntKey);
			}
		}
	}

   @Override
	public void itemClick(ItemClickEvent evt) 
     { 
		super.itemClick(evt);
		
	    String itemKey=evt.getItemKey(); 
		if (Arrays.asList(downItems).contains(itemKey)) {
			this.getAttachments(itemKey);
		}
	}

	/**
	 * 按钮点击前事件
	 * @param evt
	 * @author: hst
	 * @createDate: 2023/07/21
	 */
	@Override
	public void beforeItemClick(BeforeItemClickEvent evt) {
		super.beforeItemClick(evt);
		String key = evt.getItemKey();
		if ("yd_scm_delentry".equals(key) || "yd_file1entry_delline".equals(key)) {
			boolean isExist = isInvestigatedSupply(key);
			if (!isExist) {
				this.getView().showTipNotification("请勿删除研发已调研过的生产商");
				evt.setCancel(true);
			}
		}
	}


   Set getSupIds(String entryField,String supField,String prodField)
   {
	    Set  s=new HashSet();
	   int count= this.getModel().getEntryRowCount(entryField);
	   for(int i=0;i<count;i++)
	   {
		  DynamicObject supInfo=(DynamicObject)this.getModel().getValue(supField, i);
		  DynamicObject prodInfo=(DynamicObject)this.getModel().getValue(prodField, i);
		  if(supInfo!=null&&prodInfo!=null)
		  {
			  s.add(supInfo.getPkValue()+"&"+prodInfo.getPkValue());
		  }
	   } 
	   return s;
   }
	
       Set getSupIds4Info()
       {
    	  return getSupIds("yd_file1entry","yd_csup","yd_cprod"); 
       }
       
       Set getSupIds4Acc()
       {
    	   Set  s=new HashSet();
		   if ("1".equals(this.getModel().getValue("yd_version"))) {
			   int count = this.getModel().getEntryRowCount("yd_admitentity");
			   for (int i = 0; i < count; i++) {
				   DynamicObject supInfo = (DynamicObject) this.getModel().getValue("yd_admitsup", i);
				   DynamicObject prodInfo = (DynamicObject) this.getModel().getValue("yd_admitpro", i);
				   String supDir = (String) this.getModel().getValue("yd_isqualify", i);

				   if ("1".equals(supDir) && supInfo != null && prodInfo != null) {
					   s.add(supInfo.getPkValue() + "&" + prodInfo.getPkValue());
				   }
			   }
		   } else {
			   int count = this.getModel().getEntryRowCount("yd_gsupentry");
			   for (int i = 0; i < count; i++) {
				   DynamicObject supInfo = (DynamicObject) this.getModel().getValue("yd_gsup", i);
				   DynamicObject prodInfo = (DynamicObject) this.getModel().getValue("yd_gprod", i);
				   String supDir = (String) this.getModel().getValue("yd_supdir", i);

				   if ("1".equals(supDir) && supInfo != null && prodInfo != null) {
					   s.add(supInfo.getPkValue() + "&" + prodInfo.getPkValue());
				   }
			   }
		   }
    	   return s;
       }
	  
	  private void genSupInfoBill(String actionKey,Set supIdSet) {
		  // 弹出选择供应商生产商的动态表单页面
		  FormShowParameter showParameter = new FormShowParameter();
		  showParameter.setFormId("yd_newmatsupselectui");
		  showParameter.setCloseCallBack(new CloseCallBack(this, actionKey));
		  showParameter.getOpenStyle().setShowType(ShowType.Modal);
		  DynamicObjectCollection entry = this.getModel().getEntryEntity("yd_file1entry");
		  Set<String> selectData = new HashSet<String>();
		  for (DynamicObject tempEn : entry) {
			  if (tempEn.getDynamicObject("yd_csup") != null && tempEn.getDynamicObject("yd_cprod") != null) {
				  if (supIdSet.contains(tempEn.getString("yd_csup.id") + "&" + tempEn.getString("yd_cprod.id"))) {
					  String supName = tempEn.getString("yd_cnewsupname");
					  String supId = tempEn.getString("yd_csup.id");
					  String prodId = tempEn.getString("yd_cprod.id");
					  boolean hasBill = false;

					  // update by hst 2023/11/04 查询是否存在资料补充函
					  hasBill = verifiesHadPushSupplyInformationBill(tempEn);
					  selectData.add(supName + "&" + supId + "&" + prodId + "&" + hasBill);
				  }
			  }
		  }
		  showParameter.setCustomParam("file1entry", selectData);
		  this.getView().showForm(showParameter);
	  }

	  void creatSupInfoBill(Object supId, Object prodId) {//创建资料补充函，并打开

		  Long id = (Long) this.getModel().getValue("id");
		  try {
			  NewMatReverseHelper.checkHasSupInfo(id, (Long) supId, (Long) prodId);  // 按供应商+生产商获取资料补充函
		  } catch (KDBizException e) {
			  this.getView().showErrorNotification(e.getMessage());
			  return;
		  }


		  DynamicObject info = BusinessDataServiceHelper.loadSingle(id, "yd_newmatreqbill", "id,yd_selectsup,yd_selectprod");
		  info.set("yd_selectsup", BusinessDataServiceHelper.loadSingle(supId, "srm_supplier", "id"));
		  info.set("yd_selectprod", BusinessDataServiceHelper.loadSingle(prodId, "yd_srmproducers", "id"));
		  SaveServiceHelper.save(new DynamicObject[]{info});

		  this.getView().invokeOperation("push_infobill", OperateOption.create());

		  info.set("yd_selectsup", null);
		  info.set("yd_selectprod", null);
		  SaveServiceHelper.save(new DynamicObject[]{info});

		  this.getView().invokeOperation("save");

	  }
	  
	  void creatSupAccBill(Object supId, Object prodId) {//创建原辅料供应商准入单，并打开

		  Long id = (Long) this.getModel().getValue("id");
		  DynamicObject info = BusinessDataServiceHelper.loadSingle(id, "yd_newmatreqbill", "id,yd_selectsup,yd_selectprod");
		  info.set("yd_selectsup", BusinessDataServiceHelper.loadSingle(supId, "srm_supplier", "id"));
		  info.set("yd_selectprod", BusinessDataServiceHelper.loadSingle(prodId, "yd_srmproducers", "id"));
		  SaveServiceHelper.save(new DynamicObject[]{info});

		  this.getView().invokeOperation("push_newmat", OperateOption.create());

		  info.set("yd_selectsup", null);
		  info.set("yd_selectprod", null);
		  SaveServiceHelper.save(new DynamicObject[]{info});
	  }
	  
	  void creatNoticeBill(Object supId, Object prodId) {//创建现场审核通知单，并打开

		  Long id = (Long) this.getModel().getValue("id");
		  DynamicObject info = BusinessDataServiceHelper.loadSingle(id, "yd_newmatreqbill", "id,yd_selectsup,yd_selectprod");
		  info.set("yd_selectsup", BusinessDataServiceHelper.loadSingle(supId, "srm_supplier", "id"));
		  info.set("yd_selectprod", BusinessDataServiceHelper.loadSingle(prodId, "yd_srmproducers", "id"));
		  SaveServiceHelper.save(new DynamicObject[]{info});

		  this.getView().invokeOperation("push_noticebill", OperateOption.create());

		  info.set("yd_selectsup", null);
		  info.set("yd_selectprod", null);
		  SaveServiceHelper.save(new DynamicObject[]{info});
	  }
	  
	  void creatFeedBackBill(Object supId, Object prodId) {//创建现场审核反馈单，并打开

		  Long id = (Long) this.getModel().getValue("id");
		  DynamicObject info = BusinessDataServiceHelper.loadSingle(id, "yd_newmatreqbill", "id,yd_selectsup,yd_selectprod");
		  info.set("yd_selectsup", BusinessDataServiceHelper.loadSingle(supId, "srm_supplier", "id"));
		  info.set("yd_selectprod", BusinessDataServiceHelper.loadSingle(prodId, "yd_srmproducers", "id"));
		  SaveServiceHelper.save(new DynamicObject[]{info});

		  this.getView().invokeOperation("push_feedback", OperateOption.create());

		  info.set("yd_selectsup", null);
		  info.set("yd_selectprod", null);
		  SaveServiceHelper.save(new DynamicObject[]{info});
	  }
	  
	
	@Override
	public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
		super.closedCallBack(closedCallBackEvent);

		if (StringUtils.equals(closedCallBackEvent.getActionId(), "gen_supinfo")) {//打回回调接口
			Map<String, Object> searchData = (Map<String, Object>) closedCallBackEvent.getReturnData();
			if (searchData != null) {
				DynamicObject supInfo = (DynamicObject) searchData.get("supId");
				DynamicObject prodInfo = (DynamicObject) searchData.get("prodId");
				if (supInfo != null && prodInfo != null) {
					creatSupInfoBill(supInfo.getPkValue(), prodInfo.getPkValue());
				} else {
					this.getView().showMessage("供应商和生产商不能为空！");
				}
			}
		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "gen_supacc")) {//打回回调接口
			Map<String, Object> searchData = (Map<String, Object>) closedCallBackEvent.getReturnData();
			if (searchData != null) {
				DynamicObject supInfo = (DynamicObject) searchData.get("supId");
				DynamicObject prodInfo = (DynamicObject) searchData.get("prodId");
				if (supInfo != null && prodInfo != null) {
					creatSupAccBill(supInfo.getPkValue(), prodInfo.getPkValue());
				} else {
					this.getView().showMessage("供应商和生产商不能为空！");
				}
			}
		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "gen_notice")) {//打回回调接口
			Map<String, Object> searchData = (Map<String, Object>) closedCallBackEvent.getReturnData();
			if (searchData != null) {
				DynamicObject supInfo = (DynamicObject) searchData.get("supId");
				DynamicObject prodInfo = (DynamicObject) searchData.get("prodId");
				if (supInfo != null && prodInfo != null) {
					creatNoticeBill(supInfo.getPkValue(), prodInfo.getPkValue());
				} else {
					this.getView().showMessage("供应商和生产商不能为空！");
				}
			}
		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "gen_feedback")) {//打回回调接口
			Map<String, Object> searchData = (Map<String, Object>) closedCallBackEvent.getReturnData();
			if (searchData != null) {
				DynamicObject supInfo = (DynamicObject) searchData.get("supId");
				DynamicObject prodInfo = (DynamicObject) searchData.get("prodId");
				if (supInfo != null && prodInfo != null) {
					creatFeedBackBill(supInfo.getPkValue(), prodInfo.getPkValue());
				} else {
					this.getView().showMessage("供应商和生产商不能为空！");
				}
			}
		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "chooseProduct")) {
			//update by hst 2023/07/05 选择生产商弹窗回调
			Object proName = closedCallBackEvent.getReturnData();
			if (Objects.nonNull(proName)) {
				this.getAttachmentByProduct(proName.toString());
			}
		}
	}
	
	@Override
	public void afterBindData(EventObject e) {
		super.afterBindData(e);
		// 如果是需要现场审核则进行颜色标记，为了适配后期动态改需求，采用单据设置列颜色，这里设置为白色的方式实现,yzw
		EntryGrid entryGrid = this.getControl("yd_auditentry");
		List<CellStyle> rowStyle = new ArrayList<CellStyle>();
		for (int index = 0, size=this.getModel().getEntryRowCount("yd_auditentry"); index < size; index++) {
			Object r = this.getModel().getValue("yd_onsiteaudit", index);
			if (r!=null && !"1".equals(r.toString())) {
				CellStyle mapStyle = new CellStyle();
				mapStyle.setRow(index);
				mapStyle.setFieldKey("yd_onsiteaudit");
				mapStyle.setBackColor("#FFFFFF");
				rowStyle.add(mapStyle);
			}
		}
		entryGrid.setCellStyle(rowStyle);
		
		// 如果是建议中试供应商则标黄
		EntryGrid stryEntryGrid = this.getControl("yd_stryentry");
		List<CellStyle> stryRowStyle = new ArrayList<CellStyle>();
		for (int index = 0, size=this.getModel().getEntryRowCount("yd_stryentry"); index < size; index++) {
			Object r = this.getModel().getValue("yd_stresult", index);
			if (r!=null && !"1".equals(r.toString())) {
				CellStyle mapStyle = new CellStyle();
				mapStyle.setRow(index);
				mapStyle.setFieldKey("yd_stresult");
				mapStyle.setBackColor("#FFFFFF");
				stryRowStyle.add(mapStyle);
			}
		}
		stryEntryGrid.setCellStyle(stryRowStyle);
		
		// 冻结字段
		UIUtils.setFreeze(this.getView(), "yd_materialentry", new String[] {"yd_anewsupname"});
	}
	
	@Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);
		
		// 设置组织为股份
		this.getModel().getDataEntity().set("org", new BizBillHelper().getObjByBillNumber("bos_org", "000002"));
	}

	/**
	 * 通过指定生产商获取附件
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/03
	 */
	private void getAttachmentByProduct (String proName) {
		List<DynamicObject> results = new ArrayList<>();
		if (StringUtils.isNotBlank(proName)) {
			Map<String,List<String>> fieldMap = this.getAttachmentFields();
			for (Map.Entry<String,List<String>> entry : fieldMap.entrySet()) {
				DynamicObjectCollection billEntry = this.getModel().getDataEntity(true).getDynamicObjectCollection(entry.getKey());
				results.addAll(getAttachmentByProduct(proName,billEntry,entry.getValue()));
			}
		}
		// 文件打包
		if (results.size() > 0) {
			packageDownload(results,proName);
		}
	}

	/**
	 * 通过指定生产商获取附件
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/03
	 */
	private List<DynamicObject> getAttachmentByProduct (String proName, DynamicObjectCollection billEntry, List<String> fields) {
		List<DynamicObject> results = new ArrayList<>();
		for (DynamicObject entry : billEntry) {
			String product = entry.getString(fields.get(0));
			if (StringUtils.isNotBlank(product) && product.equals(proName)) {
				for (int i = 1; i < fields.size(); i++) {
					String field = fields.get(i);
					DynamicObjectCollection attachments = entry.getDynamicObjectCollection(field);
					if (attachments.size() > 0) {
						results.addAll(getAttachment(attachments));
					}
				};
			}
		}
		return results;
	}

	/**
	 * 批量获取分录附件，单个/全部
	 * @param key
	 * @author: hst
	 * @createDate: 2023/07/14
	 */
	private void getAttachments (String key) {
		List<DynamicObject> results = new ArrayList<>();
		String entryName = this.getKeyMapEntry(key);
		String fileName = this.getKeyMapFileName(key);
		Map<String,List<String>> attachmentFields = this.getAttachmentFields();
		results.addAll(getAllAttachments(entryName,attachmentFields));
		// 文件打包
		if (results.size() > 0) {
			packageDownload(results,fileName);
		}
	}

	/** 获取key对应的分录标识 **/
	private String getKeyMapEntry(String key) {
		Map<String, String> map = new HashMap<>();
		map.put("yd_resedsup","yd_resedsupentry");
		map.put("yd_suggest","entryentity");
		map.put("yd_materialinfo","yd_materialentry");
		map.put("yd_file1","yd_file1entry");
		map.put("yd_supply", "yd_entryentity");
		map.put("yd_dnewsup","yd_file2entry");
		map.put("yd_stryfile","yd_stryentry");
		map.put("yd_downdomesattach", "yd_file1entry&yd_entrydomestic");
		map.put("yd_downforeigattach", "yd_file1entry&yd_entryforeign");
		map.put("yd_downagentattach", "yd_file1entry&yd_entryagent");
		map.put("yd_downpstarattach", "yd_file1entry&yd_entryprocessstar");
		map.put("yd_downpfitienattach", "yd_file1entry&yd_entryprocessfitinto");

		if (map.containsKey(key)) {
			return map.get(key);
		} else {
			return "";
		}
	}

	/** 获取key对应的文件名称
	 * @param key
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/14
	 */
	private String getKeyMapFileName(String key) {
		Map<String, String> map = new HashMap();
		map.put("yd_resedsup","研发已调研过的供应商");
		map.put("yd_suggest","推荐生产商");
		map.put("yd_materialinfo","物料详细信息");
		map.put("yd_file1","供应商书面审核资料");
		map.put("yd_supply","补充性文件");
		map.put("yd_dnewsup","涉及因素文件");
		map.put("yd_stryfile","小试反馈");
		map.put("yd_downdomesattach", "国内生产商");
		map.put("yd_downforeigattach", "国外生产商");
		map.put("yd_downagentattach", "代理商");
		map.put("yd_downpstarattach", "流程发起阶段必须资料");
		map.put("yd_downpfitienattach", "流程批准纳入前必须资料");

		return map.get(key);
	}

	/**
	 * 获取包含资质文件的分录及对应字段
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/03
	 */
	private Map<String,List<String>> getAttachmentFields () {
		Map<String,List<String>> fieldMap = new HashMap<>();
		// 研发已调研过的供应商
		fieldMap.put("yd_resedsupentry",Arrays.asList(new String[]{"yd_resedatt"}));
		// 推荐供应商信息
		fieldMap.put("entryentity",Arrays.asList(new String[]{"yd_attachmentfield1","yd_prodatt"}));
		// 物料详细信息
		fieldMap.put("yd_materialentry",Arrays.asList(new String[]{"yd_qadoc_att1","yd_qadoc_att2"}));
		// 书面审核_1
		fieldMap.put("yd_file1entry",Arrays.asList(new String[]{"yd_spc1","yd_spc2","yd_spc3","yd_spc4",
		"yd_spc5","yd_spc6","yd_entrydomestic&yd_domesattachment","yd_entryforeign&yd_foreigattachment",
		"yd_entryagent&yd_agentattachment","yd_entryprocessstar&yd_pstarattachment","yd_entryprocessfitinto&yd_pfitienattachment"}));
		// 补充性文件
		fieldMap.put("yd_entryentity",Arrays.asList(new String[]{"yd_sps1","yd_sps2","yd_sps3","yd_sps4"}));
		// 涉及必须提供
		fieldMap.put("yd_file2entry",Arrays.asList(new String[]{"yd_spd4","yd_spd5","yd_spd6","yd_spd11",
				"yd_spd12","yd_spd13","yd_spd8","yd_spd15","yd_spd14","yd_spd7","yd_spd9","yd_spd16", "yd_spd10",
				"yd_spd17","yd_spd18"}));
		// 小试反馈分录
		fieldMap.put("yd_stryentry",Arrays.asList(new String[]{"yd_qlyreqatt"}));
		/* 国内生产商 */
		fieldMap.put("yd_file1entry&yd_entrydomestic",Arrays.asList(new String[]{"yd_domesattachment"}));
		/* 国外生厂商 */
		fieldMap.put("yd_file1entry&yd_entryforeign",Arrays.asList(new String[]{"yd_foreigattachment"}));
		/* 代理商 */
		fieldMap.put("yd_file1entry&yd_entryagent",Arrays.asList(new String[]{"yd_agentattachment"}));
		/* 流程发起阶段必须资料 */
		fieldMap.put("yd_file1entry&yd_entryprocessstar",Arrays.asList(new String[]{"yd_pstarattachment"}));
		/* 流程批准纳入前必须资料 */
		fieldMap.put("yd_file1entry&yd_entryprocessfitinto",Arrays.asList(new String[]{"yd_pfitienattachment"}));

		return fieldMap;
	}

	/**
	 * 附件字段中获取附件
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/03
	 */
	private List<DynamicObject> getAttachment (DynamicObjectCollection attachments) {
		List<DynamicObject> results = new ArrayList<>();
		for (DynamicObject attachment : attachments) {
			DynamicObject baseData = attachment.getDynamicObject("fbasedataid");
			results.add(baseData);
		}
		return results;
	}

	/**
	 * 上传临时文件并提供用户下载
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/03
	 */
	private void packageDownload (List<DynamicObject> attachments, String name) {
		String billNo = this.getModel().getDataEntity(true).getString("billno");
		byte[] zipFile = AttachmentUtil.zipFiles(attachments);
		if (Objects.nonNull(zipFile)) {
			String url = CacheFactory.getCommonCacheFactory().getTempFileCache().saveAsUrl("新物料需求及供应商评价_" + billNo
					+ (StringUtils.isNotBlank(name) ? "_" + name : "") + ".zip", zipFile, 5000);
			this.getView().openUrl(url);
		}
	}

	/**
	 * 获取文件字节流
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/03
	 */
	private byte[] getFileByte (InputStream inputStream) throws IOException{
		//定义数组
		byte[] buffer = new byte[1024];
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		int len;
		while ((len = inputStream.read(buffer)) != -1) {
			baos.write(buffer, 0, len);
		}
		byte[] bytes = baos.toByteArray();
		inputStream.close();
		baos.close();
		return bytes;
	}

	private List<DynamicObject> getAllAttachments (String entryName,Map<String,List<String>> attachmentFields) {
		List<DynamicObject> results = new ArrayList<>();
		Set<String> alfields = new HashSet<>();
		DynamicObject bill = this.getModel().getDataEntity(true);
		Map<String,List<String>> pendings = new HashMap<>();
		if (StringUtils.isBlank(entryName)) {
			pendings = attachmentFields;
			results.addAll(getAttachment(bill.getDynamicObjectCollection("yd_attachmentfield")));
		} else {
			pendings.put(entryName,attachmentFields.get(entryName));
		}
		for (Map.Entry<String,List<String>> entrySet : pendings.entrySet()) {
			String key = entrySet.getKey();

			if (key.contains("&")) {
				entryName = key.split("&")[0];
				String subEntryName = key.split("&")[1];
				List<String> fields = entrySet.getValue();
				DynamicObjectCollection entries = bill.getDynamicObjectCollection(entryName);

				for (String field : fields) {
					if (!alfields.contains(field)) {
						alfields.add(field);

						for (DynamicObject entry : entries) {
							DynamicObjectCollection subEntries = entry.getDynamicObjectCollection(subEntryName);

							for (DynamicObject subEntry : subEntries) {
								results.addAll(getAttachment(subEntry.getDynamicObjectCollection(field)));
							}
						}
					}
				}
			} else {
				DynamicObjectCollection entries = bill.getDynamicObjectCollection(key);
				List<String> fields = entrySet.getValue();
				for (String field : fields) {
					if (field.contains("&")) {
						String subEntryName = field.split("&")[0];
						String subField = field.split("&")[1];
						if (!alfields.contains(subField)) {
							alfields.add(subField);

							for (DynamicObject entry : entries) {
								DynamicObjectCollection subEntries = entry.getDynamicObjectCollection(subEntryName);

								for (DynamicObject subEntry : subEntries) {
									results.addAll(getAttachment(subEntry.getDynamicObjectCollection(subField)));
								}
							}
						}
					} else {
						if (!alfields.contains(field)) {
							alfields.add(field);

							for (DynamicObject entry : entries) {
								results.addAll(getAttachment(entry.getDynamicObjectCollection(field)));
							}
						}
					}
				}
			}
		}
		return results;
	}

	/**
	 * 若非供应链推荐供应商，锁定分录字段
	 * @param e
	 * @author: hst
	 * @createDate: 2023/07/21
	 */
	private void lockFieldBySCM (PropertyChangedArgs e) {
		String key = e.getProperty().getName();
		ChangeData changeData = e.getChangeSet()[0];
		DynamicObject dataEntity = changeData.getDataEntity();
		Map<String, String> mapping = new HashMap<>();
		mapping.put("yd_materialentry", "yd_anewsupname");
		String proName = "";
		String isSuggest = "";
		if ("yd_scmcmd".equals(key) && e.getChangeSet().length > 0) {
			proName = dataEntity.getString("yd_recomsup");
			isSuggest = changeData.getNewValue().toString();
		} else if ("yd_recomsup".equals(key)) {
			proName = changeData.getNewValue().toString();
			isSuggest = dataEntity.getString("yd_scmcmd");
		}
		Map<String,Boolean> lockMap = new HashMap<>();
		if ("2".equals(isSuggest) && StringUtils.isNotBlank(proName)) {
			lockMap.put(proName,false);
		} else {
			lockMap.put(proName,true);
		}
		for (DynamicObject entry : this.getModel().getEntryEntity("entryentity")) {
			String product = entry.getString("yd_recomsup");
			if (!proName.equals(product)) {
				lockMap.put(product,"2".equals(entry.getString("yd_scmcmd")) ? false : true);
			}
		}
		lockFieldByProName(mapping,lockMap);
	}

	/**
	 * 修改分录字段
	 * @param mapping
	 * @param lockMap
	 * @author: hst
	 * @createDate: 2023/07/21
	 */
	private void lockFieldByProName (Map<String,String> mapping, Map<String,Boolean> lockMap) {
		for (Map.Entry<String,String> entrySet : mapping.entrySet()) {
			String entryName = entrySet.getKey();
			String fieldName = entrySet.getValue();
			DynamicObjectCollection entries = this.getModel().getEntryEntity(entryName);
			if (entries.size() > 0) {
				List<String> fields = DynamicObjectUtil.getAllField(entries.get(0).getDynamicObjectType());
				for (int i = 0; i < entries.size(); i++) {
					String product = entries.get(i).getString(fieldName);
					if (lockMap.containsKey(product)) {
						boolean isLock = lockMap.get(product);
						this.getView().setEnable(isLock,entries.get(i).getInt("seq") -1,
								fields.toArray(new String[fields.size()]));
					}
					this.getView().setEnable(false,i,fieldName);
				}
				this.getView().updateView(entryName);
			}
		}
	}

	/**
	 * 判断当前选中的生产商是否已调研过
	 * @param key
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/21
	 */
	private boolean isInvestigatedSupply (String key) {
		String entryName = "";
		String fieldName = "";
		switch (key) {
			case "yd_scm_delentry": {
				entryName = "entryentity";
				fieldName = "yd_recomsup";
				break;
			}
			case "yd_file1entry_delline": {
				entryName = "yd_file1entry";
				fieldName = "yd_cnewsupname";
				break;
			}
		}
		if (StringUtils.isNotBlank(entryName)) {
			EntryGrid entryGrid = this.getView().getControl(entryName);
			int[] selectRows = entryGrid.getSelectRows();
			if (selectRows.length > 0) {
				int index = selectRows[0];
				DynamicObject selectEntry = this.getModel().getEntryRowEntity(entryName, index);
				if (Objects.nonNull(selectEntry)) {
					List<String> proNames = new ArrayList<>();
					for (DynamicObject entry : this.getModel().getEntryEntity("yd_resedsupentry")) {
						String proName = entry.getString("yd_resnewsupname");
						if (StringUtils.isNotBlank(proName)) {
							proNames.add(proName);
						}
					}
					String name = selectEntry.getString(fieldName);
					if (proNames.contains(name)) {
						return false;
					} else {
						String[] entryKeys;
						if ("1".equals(this.getModel().getValue("yd_version"))) {
							entryKeys = new String[]{"yd_materialentry&yd_anewsupname",
									"yd_matanalyentry&yd_bnewsupname", "yd_stryentry&yd_strynewsupname",
									"yd_file1entry&yd_cnewsupname", "yd_file2entry&yd_dnewsupname",
									"yd_scoreentry&yd_enewsupname", "yd_pilotentry&yd_pilotname",
									"yd_auditentry&yd_fnewsupname", "yd_hsupentry&yd_hnewsupname",
									"yd_admitentity&yd_admitproname", "yd_entryentity&yd_snewsupname"};
						} else {
							entryKeys = new String[]{"yd_materialentry&yd_anewsupname",
									"yd_matanalyentry&yd_bnewsupname", "yd_stryentry&yd_strynewsupname",
									"yd_file1entry&yd_cnewsupname", "yd_file2entry&yd_dnewsupname",
									"yd_scoreentry&yd_enewsupname", "yd_auditentry&yd_fnewsupname",
									"yd_hsupentry&yd_hnewsupname", "yd_gsupentry&yd_gnewsupname",
									"yd_entryentity&yd_snewsupname"};
						}

						String proName = this.getModel().getValue("yd_recomsup", index).toString();
						for (int j = 0; j < entryKeys.length; j++) {
							String[] keys = entryKeys[j].split("&");

							DynamicObject entry = this.getModel().getDataEntity(true).getDynamicObjectCollection(keys[0])
									.stream().filter(o -> proName.equals(o.getString(keys[1]))).findFirst().orElse(null);

							if (Objects.nonNull(entry)) {
								this.getModel().getDataEntity(true).getDynamicObjectCollection(keys[0]).remove(entry);
							}
						}

						for (int i = 0; i < entryKeys.length; i++) {
							this.getView().updateView(entryKeys[i].split("&")[0]);
						}
					}
				}
			}
		}
		return true;
	}

	/**
	 * 校验是否已创建资料补充函
	 * @param column
	 * @return
	 * @author: hst
	 * @createDate: 2023/11/04
	 */
	private boolean verifiesHadPushSupplyInformationBill (DynamicObject column) {
		boolean isExist = false;
		DynamicObject supply = column.getDynamicObject("yd_csup");
		DynamicObject product = column.getDynamicObject("yd_cprod");
		if (Objects.nonNull(supply) && Objects.nonNull(product)) {
			QFilter idFilter = new QFilter("yd_newmatreq.id", QFilter.equals, this.getModel().getValue("id"));
			QFilter supFilter = new QFilter("yd_supplier.id", QFilter.equals, supply.getPkValue());
			QFilter proFilter = new QFilter("yd_producers", QFilter.equals, product.getPkValue());
			isExist = QueryServiceHelper.exists("yd_supplyinformationbill",
					new QFilter[]{idFilter, supFilter, proFilter});
		}
		return isExist;
	}

	/**
	 * 初始化锚点
	 * @author: hongsitao
	 * @createDate: 2024/11/14
	 */
	private void initAnchorPoint () {
		String isInit = this.getPageCache().get("initAnchor");
		if (StringUtils.isBlank(isInit)) {
			Control control = this.getView().getControl("yd_anchorcontrolap");
			if (Objects.nonNull(control)) {
				AnchorControl anchorCtl = (AnchorControl) control;

				List<AnchorItems> items = new ArrayList();
				items.add(new AnchorItems("yd_flexpanelap97", ResManager.loadKDString("基础信息",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap98", ResManager.loadKDString("生产商",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap100", ResManager.loadKDString("物料信息",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap101", ResManager.loadKDString("优劣分析",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap102", ResManager.loadKDString("小试反馈",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap103", ResManager.loadKDString("资料上传",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap104", ResManager.loadKDString("书面审核",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap108", ResManager.loadKDString("中试反馈",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap94", ResManager.loadKDString("准入结论",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap17", ResManager.loadKDString("审批记录",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));

				anchorCtl.addItems(items);

				this.getPageCache().put("initEnum", "true");
			}
		}
	}
}
