package kd.bos.tcbj.srm.admittance.plugin;

import com.alibaba.fastjson.JSONArray;
import com.aliyun.odps.utils.StringUtils;
import com.kingdee.bos.ctrl.common.util.StringUtil;
import kd.bos.exception.KDBizException;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.servicehelper.BusinessDataServiceHelper;

import java.util.EventObject;
import java.util.Map;
import java.util.Objects;

/**
 * 研发准入单选择供应商及生产商
 * @auditor yanzuwei
 * @date 2022年8月29日
 * 
 */
public class NewMatReqSelectSupList extends AbstractFormPlugin {
	
	@Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);

		JSONArray entryCol = this.getView().getFormShowParameter().getCustomParam("file1entry");
		for (int i = 0, len = entryCol.size(); i < len; i++) {
			String key = entryCol.get(i).toString();
			String[] keys = key.split("&");
			int rowIndex = this.getModel().createNewEntryRow("yd_entryentity");
			this.getModel().setValue("yd_cnewsupname", keys[0], rowIndex);  // 供应商名称
			this.getModel().setValue("yd_supplier", BusinessDataServiceHelper.loadSingle(keys[1], "srm_supplier", "id,name,number"), rowIndex);  // 供应商
			this.getModel().setValue("yd_producers", BusinessDataServiceHelper.loadSingle(keys[2], "yd_srmproducers", "id,name,number"), rowIndex);  // 生产商
			this.getModel().setValue("yd_hassupplybill", keys[3], rowIndex);  // 是否有资料补充函
			if (keys.length >4) {
				this.getModel().setValue("yd_orientryid", keys[4], rowIndex);
			}
		}
	}
	
	@Override
	public void afterDoOperation(AfterDoOperationEventArgs e) {
		super.afterDoOperation(e);
		
		String opKey=e.getOperateKey();
		if(StringUtil.equals("ok",opKey)) {
			EntryGrid entry = this.getView().getControl("yd_entryentity");
			int[] selectedRows = entry.getEntryState().getSelectedRows();

			if (selectedRows.length == 0) {
				throw new KDBizException("请至少选择一行数据！");
			}

			if (StringUtils.isNotBlank(this.getView().getFormShowParameter().getCustomParam("type"))
					&& "uploadfile".equals(this.getView().getFormShowParameter().getCustomParam("type"))
					&&!Boolean.valueOf(this.getModel().getValue("yd_hassupplybill", selectedRows[0]).toString())) {
				throw new KDBizException("请先发起资料补充函！");
			}

			Map<String, Object> paramMap = getView().getFormShowParameter().getCustomParams();
			paramMap.put("supId",this.getModel().getValue("yd_supplier", selectedRows[0]));
			paramMap.put("prodId",this.getModel().getValue("yd_producers", selectedRows[0]));
			paramMap.put("entryId",this.getModel().getValue("yd_orientryid", selectedRows[0]));
			getView().returnDataToParent(paramMap);
		}
	}
}
