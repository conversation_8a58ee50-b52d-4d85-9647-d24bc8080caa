package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.form.field.BasedataEdit;
import kd.scm.common.util.BaseDataViewDetailUtil;

/**
 * @auditor yanzuwei
 * @date 2022年8月10日
 * 
 */
public class OsFeedackBillEdit extends AbstractBillPlugIn {

	@Override
	public void registerListener(EventObject e) {
		super.registerListener(e);
		
		BasedataEdit yd_rawmataccbill = (BasedataEdit) this.getControl("yd_rawmataccbill");//原辅料准入单重写打开监听
		yd_rawmataccbill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_rawmatsupaccessbill", "yd_rawmatsupaccessbill"));
		});
		
		BasedataEdit yd_newmatbill = (BasedataEdit) this.getControl("yd_newmatbill");//研发准入单重写打开监听
		yd_newmatbill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_newmatreqbill", "yd_newmatreqbill"));
		});
	}
}
