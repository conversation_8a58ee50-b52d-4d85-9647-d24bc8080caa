package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import org.apache.commons.lang3.StringUtils;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.form.control.Control;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.control.events.ItemClickListener;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.TextEdit;
import kd.bos.tcbj.srm.admittance.utils.UIUtils;
import kd.scm.common.util.BaseDataViewDetailUtil; 

/**
 * @auditor yanzuwei
 * @date 2022年8月2日
 * 
 */
public class OsnoticeBillEdit extends AbstractBillPlugIn
{
	@Override
	public void registerListener(EventObject e) 
	{
		 super.registerListener(e); 
		 BasedataEdit yd_supinfo = (BasedataEdit) this.getControl("yd_newmatbill"); 
			yd_supinfo.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
				beforeF7ViewDetailEvent.setCancel(true);
				this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
						"yd_newmatreqbill", "yd_newmatreqbill"));
			});
			
			BasedataEdit yd_purchasebill = (BasedataEdit) this.getControl("yd_rawmatbill");
			yd_purchasebill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
				beforeF7ViewDetailEvent.setCancel(true);
				this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
						"yd_rawmatsupaccessbill", "yd_rawmatsupaccessbill"));
			}); 
	}
	 

}
