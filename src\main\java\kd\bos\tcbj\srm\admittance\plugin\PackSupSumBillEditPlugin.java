package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillOperationStatus;
import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.RawSupSumBillHelper;
import kd.bos.tcbj.srm.admittance.utils.BillChangeUtils;
import kd.bos.tcbj.srm.admittance.utils.UIUtils;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;

import java.util.EventObject;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.PackSupSumBillEditPlugin
 * @className PackSupSumBillEditPlugin
 * @author: hst
 * @createDate: 2024/03/11
 * @description: 包材供应商台账表单界面插件
 * @version: v1.0
 */
public class PackSupSumBillEditPlugin extends AbstractBillPlugIn {

    /** 临时允许采购操作标识 **/
    private final static String TEMPPUR_BTN = "yd_temppur";
    /** 发起变更按钮标识 **/
    private final static String CHANGE_BTN = "yd_change";
    /** 变更确认按钮标识 **/
    private final static String CONFIRM_BTN = "yd_confirm";

    /**
     * 按钮点击事件
     * @param evt
     * @author: hst
     * @createDate: 2024/04/11
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case CHANGE_BTN : {
                //发起变更
                this.showChangePage();
                break;
            }
            case CONFIRM_BTN : {
                // 变更确认
                this.changeConfirm();
                break;
            }
        }
    }

    /**
     * 加载数据后事件
     * @param e
     * @author: hst
     * @createDate: 2024/05/16
     */
    @Override
    public void afterLoadData(EventObject e) {
        super.afterLoadData(e);
        // 加载变更记录
        this.initChangeRecords();
        // 加载资格变更记录
        this.initCancelRecords();
    }

    /**
     * 操作执行完触发事件
     *
     * @param afterDoOperationEventArgs
     * @author: hst
     * @createDate: 2024/03/11
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs afterDoOperationEventArgs) {
        super.afterDoOperation(afterDoOperationEventArgs);
        String key = afterDoOperationEventArgs.getOperateKey();
        boolean isSuccess = Objects.nonNull(afterDoOperationEventArgs.getOperationResult())
                && afterDoOperationEventArgs.getOperationResult().isSuccess() ? true : false;
        if (isSuccess && TEMPPUR_BTN.equals(key)) {
            this.showTempPurApplyPage();
        }
    }

    /**
     * 打开临时采购申请页面
     * @author: hst
     * @createDate: 2024/03/11
     */
    private void showTempPurApplyPage () {
        // 查询是否存在未关闭的临时采购申请
        DynamicObject bill = this.getModel().getDataEntity(true);
        QFilter qFilter = new QFilter("yd_material",QFilter.equals,bill.getString("yd_material.id"));
        qFilter = qFilter.and(new QFilter("yd_supplier",QFilter.equals,bill.getString("yd_supplier.id")));
        qFilter = qFilter.and(new QFilter("yd_supname",QFilter.equals,bill.getString("yd_supname")));
        qFilter = qFilter.and(new QFilter("yd_isclose",QFilter.equals,"0"));
        boolean isExist = QueryServiceHelper.exists("yd_supsumbill_temp",new QFilter[]{qFilter});

        if (!isExist) {
            BillShowParameter billShowParameter = new BillShowParameter();
            billShowParameter.setFormId("yd_supsumbill_temp");
            billShowParameter.setBillStatus(BillOperationStatus.ADDNEW);
            billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);

            // 传参
            Map<String, Object> param = new HashMap<>();
            param.put("billNo", bill.getString("billno"));
            param.put("oldStatus", bill.getString("yd_supstatus"));
            param.put("type", "yd_packsupsumbill");
            param.put("sourceId", bill.getString("id"));
            billShowParameter.setCustomParams(param);

            this.getView().showForm(billShowParameter);
        } else {
            this.getView().showTipNotification("当前该供应商、生产商及物料存在未关闭的临时采购申请，无需再次发起");
        }
    }

    /**
     * 打开台账变更界面
     * @author: hst
     * @createDate: 2024/04/11
     */
    private void showChangePage () {
        Object billId=this.getModel().getValue("id");
        if (!billId.equals(0l)) {
            UIUtils.showBillUI(this.getView(), "yd_packsupsumbilledit", billId, ShowType.MainNewTabPage);
        } else {
            this.getView().showTipNotification("请先保存单据");
        }
    }

    /**
     * 变更确认
     * @author: hst
     * @createDate: 2024/04/11
     */
    private void changeConfirm () {
        DynamicObject  bill = this.getModel().getDataEntity();
        Map retMap = BillChangeUtils.getChangeData(bill,"yd_packsupchgbill");
        String status = (String) retMap.get("status");
        if("ok".equals(status))
        {
            Object billId = retMap.get("billid");
            UIUtils.showBillUI(this.getView(), "yd_packsupchgbill", billId,ShowType.MainNewTabPage);
            // 设置关闭时不校验值变化
            DynamicObjectUtil.setBizChanged(this.getModel().getDataEntity(),false,
                    new String[]{});
            this.getView().invokeOperation("close");

        }else if("error".equals(status))
        {
            this.getView().showTipNotification((String) retMap.get("errMsg"));
        }
    }

    /**
     * 加载变更记录
     * @author: hst
     * @createDate: 2024/05/16
     */
    private void initChangeRecords () {
        String billId = this.getModel().getDataEntity().getString("id");
        if (!"0".equals(billId)) {
            // 获取变更记录
            DynamicObjectCollection records = new RawSupSumBillHelper().getChangeRecords(billId,"yd_packsupchgbill");
            for (DynamicObject record : records) {
                int index = this.getModel().createNewEntryRow("yd_changeentity");
                this.getModel().setValue("yd_infotype",record.get("entryentity.yd_infotype"),index);
                this.getModel().setValue("yd_chgfield",record.get("entryentity.yd_chgfield"),index);
                this.getModel().setValue("yd_oldvalue",record.get("entryentity.yd_oldvalue"),index);
                this.getModel().setValue("yd_newvalue",record.get("entryentity.yd_newvalue"),index);
                this.getModel().setValue("yd_note",record.get("entryentity.yd_note"),index);
                this.getModel().setValue("yd_datastatus",record.get("yd_datastatus"),index);
                this.getModel().setValue("yd_changetime",record.get("auditdate"),index);
            }
        }
        // 设置关闭时不校验值变化
        DynamicObjectUtil.setBizChanged(this.getModel().getDataEntity(),false,
                new String[]{"yd_infotype","yd_chgfield","yd_oldvalue","yd_newvalue","yd_note","yd_datastatus","yd_changetime"});
    }

    /**
     * 加载资格取消记录
     * @author: hst
     * @createDate: 2024/05/29
     */
    private void initCancelRecords () {
        String billId = this.getModel().getDataEntity().getString("id");
        if (!"0".equals(billId)) {
            // 获取变更记录
            DynamicObjectCollection records = new RawSupSumBillHelper().getCancelRecords(billId,"yd_packsupchgbill");
            for (DynamicObject record : records) {
                int index = this.getModel().createNewEntryRow("yd_cancelentry");
                this.getModel().setValue("yd_cancelitem",record.get("yd_cancelitem"),index);
                this.getModel().setValue("yd_cancelmatcode",record.get("yd_cancelmatcode"),index);
                this.getModel().setValue("yd_assessrisk",record.get("yd_assessrisk"),index);
                this.getModel().setValue("yd_cancelreason",record.get("yd_cancelreason"),index);
                this.getModel().setValue("yd_createtime",record.get("auditdate"),index);
                this.getModel().setValue("yd_billstatus",record.get("billstatus"),index);
            }
        }
        // 设置关闭时不校验值变化
        DynamicObjectUtil.setBizChanged(this.getModel().getDataEntity(),false,
                new String[]{"yd_cancelitem","yd_cancelmatcode","yd_assessrisk","yd_cancelreason","yd_createtime","yd_billstatus"});
    }
}
