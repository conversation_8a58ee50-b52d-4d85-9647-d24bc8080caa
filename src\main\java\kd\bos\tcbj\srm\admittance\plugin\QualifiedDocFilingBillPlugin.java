package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachConstant;
import kd.bos.tcbj.srm.admittance.constants.AdminAttachMapConstant;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;
import kd.bos.tcbj.srm.utils.EnumFieldUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.QualifiedDocFilingEditPlugin
 * @className QualifiedDocFilingEditPlugin
 * @author: hst
 * @createDate: 2023/10/19
 * @description: 资质文件归档表单插件
 * @version: v1.0
 */
public class QualifiedDocFilingBillPlugin extends AbstractBillPlugIn {

    /** 归档文件上传按钮标识 **/
    private final static String UPDATEFILE_BTN = "yd_updatefile";
    /** 归档文件上传页面标识 **/
    private final static String UPDATEFILE_PAGE = "yd_doucmentfile";
    /** 归档文件上传按钮标识 —— 新物料研发准入 **/
    private final static String UPLOADFILE_BTN = "yd_uploadfile_req";
    /* 归档资料上传选择供应商 */
    private final static String UPLOAD_CHOOSE = "upload_choose";

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs args) {
        super.afterDoOperation(args);
        String key = args.getOperateKey();

        switch (key) {
            case UPDATEFILE_BTN : {
                // 弹窗处理
                HashMap<String,Object> params = new HashMap<>();
                params.put("billId",this.getModel().getDataEntity().getPkValue().toString());
                params.put("formId","yd_rawmatsupaccessbill");

                this.showForm(UPDATEFILE_PAGE, params,
                        new CloseCallBack(this, "DOUCMENT_FILE"), ShowType.Modal);
                break;
            }
            case UPLOADFILE_BTN : {
                // 新物料研发准入
                this.openChooseSupplierPage();
            }
            default :
        }
    }

    /**
     * 子页面回调事件
     * @param closedCallBackEvent
     * @author: hst
     * @createDate: 2024/04/25
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        String key = closedCallBackEvent.getActionId();
        switch (key) {
            case "DOUCMENT_FILE" : {
                this.getView().showSuccessNotification("保存成功");
                this.getView().invokeOperation("refresh");
                break;
            }
            case UPLOAD_CHOOSE : {
                Object returnData = closedCallBackEvent.getReturnData();
                this.openUploadWithSupplier(returnData);
            }
            default :
        }
    }

    /**
     * showForm
     * @param formId        String
     * @param params
     * @param closeCallBack CloseCallBack
     * @param showType      ShowType
     * @author: hst
     * @createDate: 2024/04/25
     */
    public void showForm(String formId, Map<String,Object> params, CloseCallBack closeCallBack, ShowType showType) {
        FormShowParameter formShowParameter = new FormShowParameter();
        formShowParameter.getOpenStyle().setShowType(showType);
        formShowParameter.setFormId(formId);
        if (params != null) {
            formShowParameter.setCustomParams(params);
        }
        if (closeCallBack != null) {
            formShowParameter.setCloseCallBack(closeCallBack);
        }

        if (0L == this.getModel().getDataEntity().getLong("id")) {
            this.getView().showTipNotification("请先保存单据！");
        } else {
            SaveServiceHelper.save(new DynamicObject[]{this.getModel().getDataEntity(true)});
//            this.getView().invokeOperation("save");

            this.getView().showForm(formShowParameter);
        }
    }

    /**
     * 打开资料归档页面选择页面
     * @author: hst
     * @createDate: 2024/11/09
     */
    private void openChooseSupplierPage () {
        // 弹出选择供应商生产商的动态表单页面
        FormShowParameter showParameter = new FormShowParameter();
        showParameter.setFormId("yd_newmatsupselectui");
        showParameter.setCloseCallBack(new CloseCallBack(this, UPLOAD_CHOOSE));
        showParameter.getOpenStyle().setShowType(ShowType.Modal);

        Set<String> selectData = new HashSet<String>();
        DynamicObjectCollection entry = this.getModel().getEntryEntity("yd_file1entry");
        for (DynamicObject tempEn : entry) {
            if (tempEn.getDynamicObject("yd_csup") != null && tempEn.getDynamicObject("yd_cprod") != null) {
                String supName = tempEn.getString("yd_cnewsupname");
                String supId = tempEn.getString("yd_csup.id");
                String prodId = tempEn.getString("yd_cprod.id");
                String entryId = tempEn.getString("id");
                boolean hasBill = false;

                // update by hst 2023/11/04 查询是否存在资料补充函
                hasBill = verifiesHadPushSupplyInformationBill(tempEn);
                selectData.add(supName + "&" + supId + "&" + prodId + "&" + hasBill + "&" + entryId);
            }
        }


        showParameter.setCustomParam("file1entry", selectData);
        showParameter.setCustomParam("type", "uploadfile");
        this.getView().showForm(showParameter);
    }

    /**
     * 打开资料归档页面，携带供应商、生产商信息
     * @param returnData
     * @author: hst
     * @createDate: 2024/11/09
     */
    private void openUploadWithSupplier (Object returnData) {
        if (Objects.nonNull(returnData)) {
            // 弹窗处理
            HashMap<String, Object> params = new HashMap<>();
            Map<String, Object> paramMap = (Map<String, Object>) returnData;
            params.put("billId", this.getModel().getDataEntity().getPkValue().toString());
            params.put("entryId", paramMap.get("entryId"));
            params.put("formId", "yd_newmatreqbill");

            this.showForm(UPDATEFILE_PAGE, params,
                    new CloseCallBack(this, "DOUCMENT_FILE"), ShowType.Modal);
        }
    }

    /**
     * 校验是否已创建资料补充函
     * @param column
     * @return
     * @author: hst
     * @createDate: 2023/11/04
     */
    private boolean verifiesHadPushSupplyInformationBill (DynamicObject column) {
        boolean isExist = false;
        DynamicObject supply = column.getDynamicObject("yd_csup");
        DynamicObject product = column.getDynamicObject("yd_cprod");
        if (Objects.nonNull(supply) && Objects.nonNull(product)) {
            QFilter idFilter = new QFilter("yd_newmatreq.id", QFilter.equals, this.getModel().getValue("id"));
            QFilter supFilter = new QFilter("yd_supplier.id", QFilter.equals, supply.getPkValue());
            QFilter proFilter = new QFilter("yd_producers", QFilter.equals, product.getPkValue());
            isExist = QueryServiceHelper.exists("yd_supplyinformationbill",
                    new QFilter[]{idFilter, supFilter, proFilter});
        }
        return isExist;
    }
}
