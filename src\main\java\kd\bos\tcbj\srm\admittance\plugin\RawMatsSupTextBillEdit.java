package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.control.Control;
import kd.bos.form.control.Html;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.EventObject;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.RawMatsSupTextBillEdit
 * @className RawMatsSupTextBillEdit
 * @author: hst
 * @createDate: 2023/05/24
 * @description: 根据配置信息为HTML控件设置内容
 * @version: v1.0
 */
public class RawMatsSupTextBillEdit extends AbstractBillPlugIn {

    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        this.getModel().getDataEntity().set("yd_version",getNewestVersion());
    }

    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        this.bindControlText();
    }

    private void bindControlText() {
        int verison = this.getModel().getDataEntity().getInt("yd_version");
        QFilter typeFilter = new QFilter("yd_bill", QFilter.equals, "4");
        QFilter verFilter = new QFilter("yd_versionentity.yd_version", QFilter.equals, verison);
        QFilter stuFilter = new QFilter("billstatus",QFilter.equals,"C");
        DynamicObjectCollection configs = QueryServiceHelper.query("yd_paramconfigure",
                "yd_versionentity.yd_version,yd_versionentity.yd_htmlentity.yd_mark," +
                        "yd_versionentity.yd_htmlentity.yd_html_tag", new QFilter[]{typeFilter, verFilter,stuFilter}, null);
        if (Objects.nonNull(configs) && configs.size() > 0) {
            for (DynamicObject config : configs) {
                String mark = config.getString("yd_versionentity.yd_htmlentity.yd_mark");
                if (StringUtils.isNotBlank(mark)) {
                    Control control = this.getView().getControl(mark);
                    if (control instanceof Html) {
                        ((Html) control).setConent(config.getString("yd_versionentity.yd_htmlentity.yd_html_tag"));
                    }
                }
            }
        }
    }

    /**
     * 获取最新版本号
     * @return
     * @author: hst
     * @createDate: 2024/05/28
     */
    private int getNewestVersion () {
        int version = 0;
        QFilter typeFilter = new QFilter("yd_bill", QFilter.equals, "4");
        QFilter stuFilter = new QFilter("billstatus",QFilter.equals,"C");
        DynamicObjectCollection configs = QueryServiceHelper.query("yd_paramconfigure",
                "yd_versionentity.yd_version", new QFilter[]{typeFilter,stuFilter}, null);
        for (DynamicObject config : configs) {
            if (config.getInt("yd_versionentity.yd_version") > version) {
                version = config.getInt("yd_versionentity.yd_version");
            }
        }
        return version;
    }
}
