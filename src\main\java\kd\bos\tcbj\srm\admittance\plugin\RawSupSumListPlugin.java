package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.AfterQueryOfExportEvent;
import kd.bos.form.events.BeforeCreateListDataProviderArgs;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.plugin.provider.RawSupSumProvider;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.RawSupSumListPlugin
 * @className RawSupSumListPlugin
 * @author: hst
 * @createDate: 2024/12/16
 * @description: （原辅料）合格供应商目录列表插件
 * @version: v1.0
 */
public class RawSupSumListPlugin extends AbstractListPlugin {

    /* 变更仓库操作标识 */
    private final static String CHAWARE_OP = "changeware";

    @Override
    public void beforeCreateListDataProvider(BeforeCreateListDataProviderArgs args) {
        super.beforeCreateListDataProvider(args);
        //设置当前数据源
        args.setListDataProvider(new RawSupSumProvider());
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs args) {
        super.afterDoOperation(args);
        String key = args.getOperateKey();

        switch (key) {
            case CHAWARE_OP : {
                /* 打开变更仓库页面 */
                this.openChangeWareHousePage();
                break;
            }
        }
    }

    @Override
    public void afterQueryOfExport(AfterQueryOfExportEvent e) {
        super.afterQueryOfExport(e);
        DynamicObject[] datas = e.getQueryValues();

        /* 拼接变更记录、取消记录 */
        this.initChangeRecord(datas);
    }

    /**
     * 拼接变更记录、取消记录
     * @author: hongsitao
     * @createDate: 2024/12/16
     */
    private void initChangeRecord (DynamicObject[] datas) {
        DynamicObject[] infos = BusinessDataServiceHelper.load("yd_rawsupsumbill",
                "id, yd_changeentity.yd_changetime, yd_changeentity.yd_chgfield, yd_changeentity.yd_oldvalue, " +
                        "yd_changeentity.yd_newvalue, yd_changeentity.yd_note, yd_changeentity.yd_track, " +
                        "yd_cancelentry.yd_createtime, yd_cancelentry.yd_cancelitem, yd_cancelentry.yd_cancelmatcode, " +
                        "yd_cancelentry.yd_assessrisk, yd_cancelentry.yd_cancelreason, yd_cancelentry.yd_trackmark",
                null);
        Map<String, DynamicObject> map = new HashMap();
        for (DynamicObject info : infos) {
            map.put(info.getString("id"), info);
        }
        for (DynamicObject data : datas) {
            StringBuffer changeStr = new StringBuffer();
            String billId = data.getString("id");
            DynamicObject info = map.get(billId);

            if (Objects.nonNull(info)) {
                DynamicObjectCollection changeEntries = info.getDynamicObjectCollection("yd_changeentity");
                changeEntries.stream().forEach(entry -> {
                    changeStr.append(DateUtil.date2str(entry.getDate("yd_changetime"), "yyyy-MM-dd")).append("，");
                    changeStr.append("【").append(entry.getString("yd_chgfield")).append("】：");
                    changeStr.append(entry.getString("yd_oldvalue")).append(" 变更为 ");
                    changeStr.append(entry.getString("yd_newvalue")).append("。");
                    if (StringUtils.isNotBlank(entry.getString("yd_note"))) {
                        changeStr.append("备注：").append(entry.getString("yd_note"));
                    }
                    if (StringUtils.isNotBlank(entry.getString("yd_track"))) {
                        changeStr.append("跟踪备注：").append(entry.getString("yd_track"));
                    }
                    changeStr.append("\n");
                });
                data.set("yd_record", changeStr.toString());

                StringBuffer cancelStr = new StringBuffer();
                DynamicObjectCollection cancelEntries = info.getDynamicObjectCollection("yd_cancelentry");
                cancelEntries.stream().forEach(entry -> {
                    cancelStr.append(DateUtil.date2str(entry.getDate("yd_createtime"), "yyyy-MM-dd")).append("，");
                    if (!"否".equals(entry.getString("yd_cancelitem")) &&
                            !"".equals(entry.getString("yd_cancelitem"))) {
                        cancelStr.append(entry.getString("yd_cancelitem"));
                    }
                    if (!"否".equals(entry.getString("yd_cancelmatcode")) &&
                            !"".equals(entry.getString("yd_cancelmatcode"))) {
                        cancelStr.append("取消物料代码");
                    }
                    if (!"否".equals(entry.getString("yd_assessrisk")) &&
                            !"".equals(entry.getString("yd_assessrisk"))) {
                        cancelStr.append("，").append("涉及重大质量风险评估");
                    }
                    cancelStr.append("。取消原因：").append(entry.getString("yd_cancelreason"));
                    if (StringUtils.isNotBlank(entry.getString("yd_trackmark"))) {
                        cancelStr.append("跟踪备注：").append(entry.getString("yd_trackmark"));
                    }
                    cancelStr.append("\n");
                });
                data.set("yd_cancel", changeStr.toString());
            }
        }
    }

    /**
     * 打开变更仓库页面
     * @author: hongsitao
     * @createDate: 2025/02/26
     */
    private void openChangeWareHousePage () {
        DynamicObject bill = this.getModel().getDataEntity(true);
        if (Objects.nonNull(bill)) {
            ListSelectedRowCollection selectedRow = this.getSelectedRows();
            Set<Object> billIds = selectedRow.stream().map(row -> row.getPrimaryKeyValue()).collect(Collectors.toSet());

            FormShowParameter formShowParameter = new FormShowParameter();
            formShowParameter.setCaption("变更仓库");
            formShowParameter.setFormId("yd_srm_changeware");
            formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
            formShowParameter.setCustomParam("billIds", billIds);
            this.getView().showForm(formShowParameter);
        }
    }
}
