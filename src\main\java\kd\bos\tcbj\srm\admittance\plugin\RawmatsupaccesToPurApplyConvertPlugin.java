package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.botp.plugin.AbstractConvertPlugIn;
import kd.bos.entity.botp.plugin.args.AfterConvertEventArgs;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;

/**
 * 原辅料准入单生成试产物料采购申请单_自定义转换插件
 * @auditor liefengWu
 * @date 2022年6月28日
 * 
 */
public class RawmatsupaccesToPurApplyConvertPlugin extends AbstractConvertPlugIn {
	@Override
	public void afterConvert(AfterConvertEventArgs e) {
		super.afterConvert(e);
		ExtendedDataEntity[] billDataEntitys = e.getTargetExtDataEntitySet().FindByEntityKey("yd_trialpurreqbill");
		for(ExtendedDataEntity billDataEntity : billDataEntitys){
			
			DynamicObject targetDObj = billDataEntity.getDataEntity();
			long userId = UserServiceHelper.getCurrentUserId();
			DynamicObject currentUser = BusinessDataServiceHelper.loadSingle(userId, "bos_user");
			Long deptId = UserServiceHelper.getUserMainOrgId(userId);
			try {
				if(deptId != null) {
					targetDObj.set("yd_org", BusinessDataServiceHelper.loadSingle(deptId, "bos_adminorg"));
				}
			} catch (Exception e2) {
				e2.printStackTrace();
			}
			
			
			targetDObj.set("yd_applier", currentUser);
		}
	}
}
