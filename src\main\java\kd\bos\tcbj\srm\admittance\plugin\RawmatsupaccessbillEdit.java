package kd.bos.tcbj.srm.admittance.plugin;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.EventObject;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.cache.CacheFactory;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.AnchorItems;
import kd.bos.ext.form.control.AnchorControl;
import kd.bos.form.control.Control;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.tcbj.srm.admittance.entity.SupplyInfoEntryFieldEntiy;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;
import kd.bos.tcbj.srm.admittance.helper.NewMatReverseHelper;
import kd.bos.tcbj.srm.utils.AttachmentUtil;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillShowParameter;
import kd.bos.bill.OperationStatus;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.form.ShowType;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.form.field.BasedataEdit;
import kd.bos.mvc.bill.BillModel;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.srm.admittance.enums.AgentTypeEnum;
import kd.bos.tcbj.srm.admittance.helper.AttachUtils;
import kd.bos.tcbj.srm.admittance.helper.BizBillHelper;
import kd.bos.tcbj.srm.admittance.helper.RSSBillBizHelper;
import kd.bos.tcbj.srm.admittance.utils.CommonUtils;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.scm.common.enums.SrmSupplierStatusEnum;
import kd.scm.common.util.BaseDataViewDetailUtil;

/**
 * 原辅料准入单编辑自定义插件
 * @auditor liefengWu
 * @date 2022年6月23日
 * 
 */
public class RawmatsupaccessbillEdit extends AbstractBillPlugIn {

	private Log logger = LogFactory.getLog(this.getClass().getName());
	/* 下载附件按钮标识 */
	private final static String[] downItems = new String[]{"yd_downfactorfile","yd_downdomesattach","yd_downforeigattach",
			"yd_downagentattach","yd_downmaterial", "yd_downpstarattach", "yd_downpfitienattach"};

	@Override
	public void initialize() {
		super.initialize();

		/* update by hst 2024/11/14 增加锚点设置 */
		this.initAnchorPoint();
	}

	@Override
	public void registerListener(EventObject e) {
		super.registerListener(e);
		BasedataEdit yd_supinfo = (BasedataEdit) this.getControl("yd_supinfo");//资料补充函重写打开监听
		yd_supinfo.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_supplyinformationbill", "yd_supplyinformationbill"));
		});
		
		BasedataEdit yd_purchasebill = (BasedataEdit) this.getControl("yd_purchasebill");//分录试产物料采购申请单单据重写打开监听
		yd_purchasebill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_trialpurreqbill", "yd_trialpurreqbill"));
		});
		
		BasedataEdit yd_supjobbill = (BasedataEdit) this.getControl("yd_supjobbill");//分录试产作业单据重写打开监听
		yd_supjobbill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_suptrialjobbill", "yd_suptrialjobbill"));
		});
		
		BasedataEdit yd_supbizbill = (BasedataEdit) this.getControl("yd_supbizbill");//分录试产业务单据重写打开监听
		yd_supbizbill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_suptrialbizbill", "yd_suptrialbizbill"));
		});
		
		EntryGrid entry = this.getView().getControl("yd_trailentryentity");//试产分录超链接监听
		entry.addHyperClickListener(new HyperLinkClickListener() {
			
			@Override
			public void hyperLinkClick(HyperLinkClickEvent event) {
				String fileName = event.getFieldName();
				String billType = "";
				String billId = "";
				int rowIndex = event.getRowIndex();
				
				if("yd_trialbillno".equals(fileName)) {//试产业务单
					billType = "yd_suptrialbizbill";
					billId = GeneralFormatUtils.getString(getModel().getValue("yd_trialbillid", rowIndex));
					String billno = GeneralFormatUtils.getString(getModel().getValue("yd_trialbillno", rowIndex));
					QFilter billFilter = new QFilter("billno", QCP.equals, billno);
					DynamicObjectCollection bills = QueryServiceHelper.query(billType, "id,billno", billFilter.toArray());
					if (bills.size() > 0) {
						billId = bills.get(0).getString("id");
					}
				}else if("yd_suptrialjobbillno".equals(fileName)) {//试产作业单
					billType = "yd_suptrialjobbill";
					billId = GeneralFormatUtils.getString(getModel().getValue("yd_suptrialjobbillid", rowIndex));
					String billno = GeneralFormatUtils.getString(getModel().getValue("yd_suptrialjobbillno", rowIndex));
					QFilter billFilter = new QFilter("billno", QCP.equals, billno);
					DynamicObjectCollection bills = QueryServiceHelper.query(billType, "id,billno", billFilter.toArray());
					if (bills.size() > 0) {
						billId = bills.get(0).getString("id");
					}
				}
				
				if(StringUtils.isEmpty(billId) || StringUtils.isEmpty(billType)) {
					return;
				}
				// 打开单据界面
				BillShowParameter parameter = new BillShowParameter();
				parameter.setFormId(billType);
				parameter.setPkId(billId);
				parameter.setStatus(OperationStatus.VIEW);
				parameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
				parameter.setHasRight(true);
				getView().showForm(parameter);
			}
		});

		/* update by hst 2024/10/22 新增分录批量下载功能 */
		this.addItemClickListeners(new String[]{"yd_factortoolbarap","yd_advcontoolbarap20","yd_advcontoolbarap24",
				"yd_advcontoolbarap21","yd_advcontoolbarap22","yd_advcontoolbarap4","yd_advcontoolbarap23"});

		/* 准入节点过滤，只允许选择流程结束节点 */
		BasedataEdit accessPot = (BasedataEdit) this.getControl("yd_supplieraccesspot");
		accessPot.addBeforeF7SelectListener((event) -> {
			event.addCustomQFilter(new QFilter("yd_isflowend", QFilter.equals, true));
		});
	}

	@Override
	public void itemClick(ItemClickEvent evt) {
		super.itemClick(evt);
		String itemKey=evt.getItemKey();

		if (Arrays.asList(downItems).contains(itemKey)) {
			this.getAttachments(itemKey);
		}
	}

	@Override
	public void propertyChanged(PropertyChangedArgs e) {
		super.propertyChanged(e);
		String fieldKey = e.getProperty().getName();
		if (StringUtils.equals("yd_supinfo", fieldKey)) {  // 监听选择资料补充函
			DynamicObject value = (DynamicObject) e.getChangeSet()[0].getNewValue();
			if (value != null) {
				loadSupInfoData(value.getPkValue());
			}
		} else if (StringUtils.equals("yd_material", fieldKey)) {  // 监听选择物料带出台账信息
			try {
				DynamicObject value = (DynamicObject) e.getChangeSet()[0].getNewValue();

				if (value != null) {
					List<DynamicObject> rssList = RSSBillBizHelper.getRSSBillListByMat(value.getPkValue());

					IDataModel model = this.getModel();
					for (DynamicObject info : rssList) {
						DynamicObjectCollection enCol = model.getDataEntity(true).getDynamicObjectCollection("entryentity");
						Boolean beExisted = false;
						for (DynamicObject enObj : enCol) {
							// update by hst 2024/06/25 调整判重条件
							DynamicObject dealer = enObj.getDynamicObject("yd_dealer");
							DynamicObject producer = enObj.getDynamicObject("yd_dealer");
							if (Objects.nonNull(dealer)
									&& dealer.getString("id").equals(info.getString("yd_agent.id"))
									&& Objects.nonNull(producer)
									&& producer.getString("id").equals(info.getString("yd_tempproducers.id"))) {
								beExisted = true;
								break;
							}
						}
						if (beExisted) continue;
						int index = model.createNewEntryRow("entryentity");
						model.setValue("yd_dealer", CommonUtils.getPK(info, "yd_agent"), index);  // 供应商
						model.setValue("yd_producers", CommonUtils.getPK(info, "yd_producers"), index);  // 生产商
						model.setValue("yd_manufacturer", info.getString("yd_manufacturer"), index);  // 生产商（作废）
						model.setValue("yd_origin", info.getString("yd_origin"), index);   // 产地
						model.setValue("yd_matlevel", info.getString("yd_matleveltype"), index); // 物料级别
						model.setValue("yd_supmatname", info.getString("yd_supmatname"), index);   // 供应商物料名称
						if (info.getDate("yd_licensedate") != null) {
							model.setValue("yd_prdlicense", DateUtil.date2str(info.getDate("yd_licensedate"), "yyyy-MM-dd"), index);  // 生产许可
						}

						// update by hst 2024/06/25 若生产商地址为空，取供应商对应的地址
						if (StringUtils.isBlank(info.getString("yd_address"))) {
							model.setValue("yd_manufacturerplace", info.getString("yd_agent.address"), index);
						} else {
							model.setValue("yd_manufacturerplace", info.getString("yd_address"), index);
						}

						model.setValue("yd_supplierstate", NewMatReverseHelper.getState(info.getDynamicObject("yd_supplieraccesspot")), index);
						model.setValue("yd_manufacturerstate", NewMatReverseHelper.getState(info.getDynamicObject("yd_supplieraccesspot")), index);
						this.getModel().setValue("yd_suptype", "old", index);  // 准入类型
					}
				}

				/* update by hst 2024/11/11 根据供应商、生产商、物料获取最新的台账信息*/
				setBaseInfo();
			} catch (Exception exception) {
				logger.error(exception.getMessage());
			}
		} else if ("yd_supplier".equals(fieldKey)) {
			/* update by hst 2024/11/11 根据供应商、生产商、物料获取最新的台账信息*/
			setBaseInfo();
		} else if ("yd_tempproducers".equals(fieldKey)) {
			/* update by hst 2024/11/11 根据供应商、生产商、物料获取最新的台账信息*/
			setBaseInfo();
		}
	}
	
	
	
	 private void loadSupInfoData(Object id)
	 {//资料补充函带出数据
		
		  DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(id, "yd_supplyinformationbill");
		  if(billInfo!=null)
		  {
			  this.getModel().deleteEntryData("entryentity");
			  DynamicObject matInfo= billInfo.getDynamicObject("yd_material");   
			  DynamicObject supInfo= billInfo.getDynamicObject("yd_supplier");
			  DynamicObject proInfo= billInfo.getDynamicObject("yd_producers");

			  genSupEntry(id, supInfo, proInfo);

			  this.getModel().setValue("yd_material", matInfo);
			  supInfo = BusinessDataServiceHelper.loadSingle(supInfo.getPkValue(), "srm_supplier","id,name,number");
			  int idx = getSupEntryIndex();
			  this.getModel().setValue("yd_dealer", supInfo, idx);
			  this.getModel().setValue("yd_supplier", supInfo);
			  this.getModel().setValue("yd_producers", proInfo, idx);
			  this.getModel().setValue("yd_tempproducers", proInfo);

			  if(supInfo != null && supInfo.getPkValue() != null) {
				  supInfo = BusinessDataServiceHelper.loadSingle(supInfo.getPkValue(), "srm_supplier");
					
				  Long groupId = Long.valueOf(supInfo.getLong("group_id"));
				  String auditstatus = supInfo.getString("auditstatus");
				  if (groupId != null && groupId.longValue() != 0L
							&& (SrmSupplierStatusEnum.SUCCESS.getValue().equals(auditstatus)
									|| SrmSupplierStatusEnum.APTITUDE.getValue().equals(auditstatus)
									|| SrmSupplierStatusEnum.UNAPROVE.getValue().equals(auditstatus))) {
						
						getModel().setValue("yd_suppliergroup", supInfo.get("group"));
				  }	
				}
			  
			  copyAtt(billInfo);
			 
		  }
		  this.getView().updateView();
	 }
	 /**
	  * @fun 生成供应商分录
	  * **/
	 void genSupEntry(Object id, DynamicObject supInfo, DynamicObject proInfo)
	 {
		 List<DynamicObject> rssList=RSSBillBizHelper.getRSSBillList(id);
		 IDataModel model= this.getModel();

		 for(DynamicObject info:rssList)
		 {
			 if (Objects.nonNull(supInfo) && Objects.nonNull(proInfo)) {
				 if (CommonUtils.getPK(info,"yd_agent") == supInfo.getPkValue()
						 && CommonUtils.getPK(info,"yd_producers") == proInfo.getPkValue()) {
					 continue;
				 }
			 }
			 int index= model.createNewEntryRow("entryentity");
			 model.setValue("yd_dealer", CommonUtils.getPK(info,"yd_agent"),index);
			 model.setValue("yd_producers", CommonUtils.getPK(info,"yd_producers"),index);  // 生产商
			 model.setValue("yd_manufacturer", info.getString("yd_manufacturer"),index);
			 model.setValue("yd_origin", info.getString("yd_origin"),index); 
			 model.setValue("yd_matlevel", info.getString("yd_matleveltype"),index); 
			 model.setValue("yd_supmatname", info.getString("yd_supmatname"),index);
			 if(info.getDate("yd_licensedate")!=null)
			 {
				 model.setValue("yd_prdlicense",DateUtil.date2str(info.getDate("yd_licensedate"),"yyyy-MM-dd"),index);
			 }

			 // update by hst 2024/06/25 若生产商地址为空，取供应商对应的地址
			 if (StringUtils.isBlank(info.getString("yd_address"))) {
				 model.setValue("yd_manufacturerplace", info.getString("yd_agent.address"),index);
			 } else {
				 model.setValue("yd_manufacturerplace", info.getString("yd_address"),index);
			 }

			 model.setValue("yd_supplierstate", NewMatReverseHelper.getState(info.getDynamicObject("yd_supplieraccesspot")), index);
			 model.setValue("yd_manufacturerstate", NewMatReverseHelper.getState(info.getDynamicObject("yd_supplieraccesspot")), index);

			 this.getModel().setValue("yd_suptype", "old",index);
		 }
	 }
	 
	 int  getSupEntryIndex()
	 { //获取供应商类型为"新"的分录 
		int idx=-1;
		int count= this.getModel().getEntryRowCount("entryentity");
		for(int i=0;i<count;i++)
		{
			String supType=(String)this.getModel().getValue("yd_suptype", i);
			if("new".equals(supType))
			{//"新"返回
				idx=i;
				break;
			}else if(StringUtils.isEmpty(supType))
			{//为空，设置"新"
				this.getModel().setValue("yd_suptype", "new",i);
				idx=i;
				break;
			}
		}
		if(idx<0)
		{//没找到'新'，新增一条记录
			idx=this.getModel().createNewEntryRow("entryentity");
			this.getModel().setValue("yd_suptype", "new",idx);
		}
		
		 return idx;
	 }
	 
	 private void copyAtt(DynamicObject info) {
		 //附件处理自动带出
		 List<SupplyInfoEntryFieldEntiy> zzEntryAttachFieldList = AdminAttachHelper.getFieldMap();

		 Map<String, List<DynamicObject>> results = getAttachmentInfo(info);

		 for(SupplyInfoEntryFieldEntiy filedEntity :zzEntryAttachFieldList) {//处理对应目标附件字段处理逻辑
			 if (filedEntity == null) {
				 continue;
			 }

			 String entryKey = filedEntity.getEntryEntityName();
			 for (Map.Entry<String, List<DynamicObject>> result : results.entrySet()) {
				 for (DynamicObject oriBill : result.getValue()) {
					 /* 目标单附件分录 */
					 DynamicObjectCollection mainKeyColl = this.getModel().getDataEntity(true).getDynamicObjectCollection(entryKey);
					 Set<String> existIds = mainKeyColl.stream().map(temp -> temp.getString(filedEntity.getOriEntryIdField())).collect(Collectors.toSet());
					 /* 源单附件分录 */
					 DynamicObjectCollection fileEntryColl = oriBill.getDynamicObjectCollection(entryKey);

					 mainKeyColl.clear();
					 if (fileEntryColl != null && fileEntryColl.size() > 0) {
						 for (int index = 0; index < fileEntryColl.size(); index++) {
							 DynamicObject fileEntryInfo = fileEntryColl.get(index);

							 if (existIds.contains(fileEntryInfo.getString("id"))) {
								 continue;
							 }

							 DynamicObject tarEntry = mainKeyColl.addNew();
							 /* 资质类型 */
							 tarEntry.set(filedEntity.getFileTypeField(), fileEntryInfo.get(filedEntity.getFileTypeField()));
							 /* 发证日期 */
							 tarEntry.set(filedEntity.getEffectField(), fileEntryInfo.get(filedEntity.getEffectField()));
							 /* 有效日期 */
							 tarEntry.set(filedEntity.getUnEffectField(), fileEntryInfo.get(filedEntity.getUnEffectField()));
							 /* 是否已重命名 */
							 tarEntry.set(filedEntity.getRenameField(), fileEntryInfo.get(filedEntity.getRenameField()));
							 /* 是否归档 */
							 tarEntry.set(filedEntity.getIsUploadField(), fileEntryInfo.get(filedEntity.getIsUploadField()));

							 if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getOriEntryIdField()))) {
								 /* 原分录ID */
								 tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.getString(filedEntity.getOriEntryIdField()));
							 } else {
								 tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.getString("id"));
							 }

							 if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getOriBillIdField()))) {
								 /* 原单据ID */
								 tarEntry.set(filedEntity.getOriBillIdField(), fileEntryInfo.getString(filedEntity.getOriBillIdField()));
							 } else {
								 tarEntry.set(filedEntity.getOriBillIdField(), result.getKey().split("&")[0]);
							 }

							 if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getBillType()))) {
								 /* 来源单据类型 */
								 tarEntry.set(filedEntity.getBillType(), fileEntryInfo.getString(filedEntity.getBillType()));
							 } else {
								 tarEntry.set(filedEntity.getBillType(), result.getKey().split("&")[1]);
							 }
							 /* 序号 */
							 tarEntry.set("seq", mainKeyColl.size() + 1);

							 /* 获取对应分录附件字段 多选基础资料数据 */
							 DynamicObjectCollection fileAttachColl = fileEntryInfo.getDynamicObjectCollection(filedEntity.getFileField());
							 if (fileAttachColl == null || fileAttachColl.size() == 0)
								 continue;

							 DynamicObjectCollection tarAttachColl = tarEntry.getDynamicObjectCollection(filedEntity.getFileField());
							 for (int jIndex = 0; jIndex < fileAttachColl.size(); jIndex++) {
								 DynamicObject att_bd = fileAttachColl.get(jIndex);//原附件
								 DynamicObject bd = att_bd.getDynamicObject("fbasedataid");//复制附件

								 //创建附件字段信息  (对应的是附件字段的表结构)
								 DynamicObject attField = tarAttachColl.addNew();
								 attField.set("fbasedataid", bd);
							 }
						 }
					 }
				 }
			 }
		 }
	 }
	 
	@Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);
		
		// 设置组织为股份
		this.getModel().getDataEntity().set("org", new BizBillHelper().getObjByBillNumber("bos_org", "000002"));

		/* update by hst 2024/11/14 增加锚点设置 */
		this.initAnchorPoint();
	}

	@Override
	public void afterLoadData(EventObject e) {
		super.afterLoadData(e);

		/* update by hst 2024/11/14 增加锚点设置 */
		this.initAnchorPoint();
	}

	/**
	 * 批量获取分录附件，单个/全部
	 * @param key
	 * @author: hst
	 * @createDate: 2024/10/22
	 */
	private void getAttachments (String key) {
		List<DynamicObject> results = new ArrayList<>();
		List<String> entryNames = this.getKeyMapEntry(key);
		String fileName = this.getKeyMapFileName(key);
		Map<String,String> attachmentFields = this.getAttachmentFields();
		for (String entryName : entryNames) {
			results.addAll(getAllAttachments(entryName, attachmentFields));
		}
		// 文件打包
		if (results.size() > 0) {
			packageDownload(results,fileName);
		}
	}

	/**
	 * 获取下载按钮的key对应的需下载分录标识
	 * @param key
	 * @return
	 */
	private List<String> getKeyMapEntry(String key) {
		Map<String, String> map = new HashMap<>();
		map.put("yd_downfactorfile", "yd_entrydomestic,yd_entryforeign,yd_entryagent");
		map.put("yd_downdomesattach", "yd_entrydomestic");
		map.put("yd_downforeigattach", "yd_entryforeign");
		map.put("yd_downagentattach", "yd_entryagent");
		map.put("yd_downmaterial", "yd_entryprocessstar,yd_entryprocessfitinto");
		map.put("yd_downpstarattach", "yd_entryprocessstar");
		map.put("yd_downpfitienattach", "yd_entryprocessfitinto");

		if (map.containsKey(key)) {
			return Arrays.asList(map.get(key).split(","));
		}
		return new ArrayList();
	}

	/** 获取key对应的文件名称
	 * @param key
	 * @return
	 * @author: hst
	 * @createDate: 2024/10/22
	 */
	private String getKeyMapFileName(String key) {
		Map<String, String> map = new HashMap<>();
		map.put("yd_downfactorfile", "厂家资质");
		map.put("yd_downdomesattach", "国内生产商");
		map.put("yd_downforeigattach", "国外生产商");
		map.put("yd_downagentattach", "代理商");
		map.put("yd_downmaterial", "物料资质");
		map.put("yd_downpstarattach", "流程发起阶段必须资料");
		map.put("yd_downpfitienattach", "流程批准纳入前必须资料");

		return map.get(key);
	}

	/**
	 * 获取包含资质文件的分录及对应字段
	 * @return
	 * @author: hst
	 * @createDate: 2024/10/22
	 */
	private Map<String, String> getAttachmentFields () {
		Map<String,String> fieldMap = new HashMap<>();
		/* 国内生产商 */
		fieldMap.put("yd_entrydomestic", "yd_domesattachment");
		/* 国外生产商 */
		fieldMap.put("yd_entryforeign", "yd_foreigattachment");
		/* 代理商 */
		fieldMap.put("yd_entryagent", "yd_agentattachment");
		/* 流程发起阶段必须资料 */
		fieldMap.put("yd_entryprocessstar", "yd_pstarattachment");
		/* 流程批准纳入前必须资料 */
		fieldMap.put("yd_entryprocessfitinto", "yd_pfitienattachment");

		return fieldMap;
	}

	/**
	 * 获取所有需下载的附件信息
	 * @return
	 * @author: hst
	 * @createDate: 2024/10/22
	 */
	private List<DynamicObject> getAllAttachments (String entryName,Map<String, String> attachmentFields) {
		List<DynamicObject> results = new ArrayList<>();
		DynamicObject bill = this.getModel().getDataEntity(true);
		if (attachmentFields.containsKey(entryName)) {
			String field = attachmentFields.get(entryName);
			DynamicObjectCollection entries = bill.getDynamicObjectCollection(entryName);

			for (DynamicObject entry : entries) {
				results.addAll(getAttachment(entry.getDynamicObjectCollection(field)));
			}
		}

		return results;
	}

	/**
	 * 附件字段中获取附件
	 * @return
	 * @author: hst
	 * @createDate: 2024/10/22
	 */
	private List<DynamicObject> getAttachment (DynamicObjectCollection attachments) {
		List<DynamicObject> results = new ArrayList<>();
		for (DynamicObject attachment : attachments) {
			DynamicObject baseData = attachment.getDynamicObject("fbasedataid");
			results.add(baseData);
		}
		return results;
	}

	/**
	 * 上传临时文件并提供用户下载
	 * @return
	 * @author: hst
	 * @createDate: 2023/07/03
	 */
	private void packageDownload (List<DynamicObject> attachments, String name) {
		String billNo = this.getModel().getDataEntity(true).getString("billno");
		byte[] zipFile = AttachmentUtil.zipFiles(attachments);
		if (Objects.nonNull(zipFile)) {
			String url = CacheFactory.getCommonCacheFactory().getTempFileCache().saveAsUrl("新物料需求及供应商评价_" + billNo
					+ "_" + name + ".zip", zipFile, 5000);
			this.getView().openUrl(url);
		}
	}

	/**
	 * 根据供应商、生产商、物料获取最新的台账信息
	 * @author: hongsitao
	 * @createDate: 2024/11/11
	 */
	private void setBaseInfo () {
		DynamicObject material = this.getModel().getDataEntity(true).getDynamicObject("yd_material");
		DynamicObject supplier = this.getModel().getDataEntity(true).getDynamicObject("yd_supplier");
		DynamicObject producer = this.getModel().getDataEntity(true).getDynamicObject("yd_tempproducers");

		if (Objects.nonNull(material) && Objects.nonNull(supplier) && Objects.nonNull(producer)) {
			QFilter qFilter = QFilter.of("yd_material.id = ? and yd_agent = ? and yd_producers = ?",
					material.getPkValue(), supplier.getPkValue(), producer.getPkValue());

			DataSet dataSet = QueryServiceHelper.queryDataSet("setBaseInfo", "yd_rawsupsumbill",
					"yd_spec, yd_weight, yd_unit, yd_minunit, yd_specs, yd_storagecon, yd_manmonth, yd_address, " +
							"yd_matsource, yd_isexplosive, yd_matsourceexp, yd_hasallergen, yd_allergysource, yd_origin," +
							"yd_matleveltype, yd_matcat, yd_beinvolvekeymat, yd_isslevel, yd_supplieraccesspot.number," +
							"yd_supmatname", qFilter.toArray(), "yd_newdate desc").top(1);

			if (dataSet.hasNext()) {
				Row row = dataSet.next();

				this.getModel().setValue("yd_spec",row.get("yd_spec"));
				this.getModel().setValue("yd_weight",row.get("yd_weight"));
				this.getModel().setValue("yd_unit",row.get("yd_unit"));
				this.getModel().setValue("yd_minunit",row.get("yd_minunit"));
				this.getModel().setValue("yd_packmodel",row.get("yd_specs"));
				this.getModel().setValue("yd_storagecon",row.get("yd_storagecon"));
				this.getModel().setValue("yd_shelflife",row.get("yd_manmonth"));
				this.getModel().setValue("yd_matsource",row.get("yd_matsource"));
				this.getModel().setValue("yd_isexplosive",row.get("yd_isexplosive"));
				this.getModel().setValue("yd_matsourceexp",row.get("yd_matsourceexp"));
				this.getModel().setValue("yd_hasallergen",row.get("yd_hasallergen"));
				this.getModel().setValue("yd_allergysource",row.get("yd_allergysource"));
				this.getModel().setValue("yd_matlevtype",row.get("yd_matleveltype"));
				this.getModel().setValue("yd_matcat",row.get("yd_matcat"));
				this.getModel().setValue("yd_beinvolvekeymat",row.get("yd_beinvolvekeymat"));
				this.getModel().setValue("yd_isslevel",row.get("yd_isslevel"));

				DynamicObjectCollection entries = this.getModel().getDataEntity(true).getDynamicObjectCollection("entryentity");
				for (int i = 0; i < entries.size(); i++) {
					DynamicObject entry = entries.get(i);
					String supType = entry.getString("yd_suptype");

					if ("new".equals(supType)) {
						this.getModel().setValue("yd_matlevel", row.getString("yd_matleveltype"), i);
						this.getModel().setValue("yd_manufacturerplace", row.getString("yd_address"), i);
						this.getModel().setValue("yd_origin", row.getString("yd_origin"), i);
						this.getModel().setValue("yd_supmatname", row.getString("yd_supmatname"), i);
						this.getModel().setValue("yd_supplierstate",
								RSSBillBizHelper.getState(row.getString("yd_supplieraccesspot.number")), i);
						this.getModel().setValue("yd_manufacturerstate",
								RSSBillBizHelper.getState(row.getString("yd_supplieraccesspot.number")), i);
					}
				}
			}
		}
	}

	/**
	 * 获取附件信息
	 * @param srcInfo
	 * @return
	 */
	private Map<String, List<DynamicObject>> getAttachmentInfo (DynamicObject srcInfo) {
		Map<String, List<DynamicObject>> results = new HashMap<>();

		/* 资料补充函是否有上游新物料研发准入单 */
		Map<String, HashSet<Long>> sourceMap = BFTrackerServiceHelper.findSourceBills("yd_supplyinformationbill",
				new Long[]{Long.valueOf(srcInfo.getLong("id"))});
		Set<Long> oriBillIds = new HashSet<>();
		for (Map.Entry<String, HashSet<Long>> source : sourceMap.entrySet()) {
			if ("yd_newmatreqbill".equals(source.getKey())) {
				oriBillIds.addAll(source.getValue());
			}
		}

		if (oriBillIds.size() > 0) {
			long supId = srcInfo.getLong("yd_supplier.id");
			long proId = srcInfo.getLong("yd_producers.id");
			DynamicObject[] bills = BusinessDataServiceHelper.load(oriBillIds.toArray(),
					BusinessDataServiceHelper.newDynamicObject("yd_newmatreqbill").getDynamicObjectType());
			for (DynamicObject bill : bills) {
				List<DynamicObject> result = new ArrayList();
				DynamicObjectCollection fileEntries = bill.getDynamicObjectCollection("yd_file1entry");

				for (DynamicObject fileEntry : fileEntries) {
					if (supId == fileEntry.getLong("yd_csup.id")
							&& proId == fileEntry.getLong("yd_cprod.id")) {
						result.add(fileEntry);
					}
				}

				if (result.size() > 0) {
					results.put(bill.getString("id") + "&yd_newreqbase", result);
				}
			}
		} else {
			List<DynamicObject> result = new ArrayList<>();
			result.add(srcInfo);
			results.put(srcInfo.getString("id") + "&yd_supinfobase", result);
		}
		return results;
	}

	/**
	 * 初始化锚点
	 * @author: hongsitao
	 * @createDate: 2024/11/14
	 */
	private void initAnchorPoint () {
		String isInit = this.getPageCache().get("initAnchor");
		if (StringUtils.isBlank(isInit)) {
			Control control = this.getView().getControl("yd_anchorcontrolap");
			if (Objects.nonNull(control)) {
				AnchorControl anchorCtl = (AnchorControl) control;

				List<AnchorItems> items = new ArrayList();
				items.add(new AnchorItems("yd_flexpanelap323", ResManager.loadKDString("基础信息",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap328", ResManager.loadKDString("物料信息",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap329", ResManager.loadKDString("资质文件",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap330", ResManager.loadKDString("优劣分析",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap331", ResManager.loadKDString("物料评估",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap332", ResManager.loadKDString("书面评估",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap333", ResManager.loadKDString("技术评估",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap334", ResManager.loadKDString("现场审核",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap335", ResManager.loadKDString("试产评估",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap336", ResManager.loadKDString("新增评估",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));
				items.add(new AnchorItems("yd_flexpanelap23", ResManager.loadKDString("审批记录",
						"initAnchorPoint_0", "srm_admittance_plugin", new Object[0]), (List) null));

				anchorCtl.addItems(items);

				this.getPageCache().put("initEnum", "true");
			}
		}
	}
}
