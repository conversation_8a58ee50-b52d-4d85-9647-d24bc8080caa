package kd.bos.tcbj.srm.admittance.plugin;

import java.util.Arrays;
import java.util.EventObject;
import java.util.Map;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.filter.PermissionFilterUtil;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.BeforeClosedEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ITimeService;
import kd.bos.service.IUserService;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.RawSupSumBillHelper;
import kd.bos.tcbj.srm.admittance.utils.BillChangeUtils;
import kd.bos.tcbj.srm.admittance.utils.UIUtils;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;
import kd.scm.common.util.BaseDataViewDetailUtil;

/**
 * @auditor yanzuwei
 * @date 2022年8月8日
 * @desc 台账查看，编辑页面
 * 
 */
public class RawsupsumbillEdit extends AbstractBillPlugIn {

	@Override
	public void itemClick(ItemClickEvent evt) {
		super.itemClick(evt);
		String itemKey = evt.getItemKey();
		if ("yd_change".equals(itemKey)) {//发起变更
			Object billId = this.getModel().getValue("id");
			UIUtils.showBillUI(this.getView(), "yd_rawsupsumbilledit", billId, ShowType.MainNewTabPage);

		} else if ("yd_confirm".equals(itemKey)) {//变更确认，生成变更单
			DynamicObject ov = this.getModel().getDataEntity();
			Map retMap = BillChangeUtils.getChangeData(ov);
			String status = (String) retMap.get("status");
			if ("ok".equals(status)) {
				Object billId = retMap.get("billid");
				UIUtils.showBillUI(this.getView(), "yd_rawsupchgbill", billId, ShowType.MainNewTabPage);
				this.getView().invokeOperation("close");

			} else if ("error".equals(status)) {
				this.getView().showErrorNotification((String) retMap.get("errMsg"));
			}
		} else if ("yd_expirationalert".equals(itemKey)) {
			//资质到期预警按钮，用于测试调度任务 ——hst 2022.09.19
			String id = this.getModel().getDataEntity().getString("id");
			new RawSupSumBillHelper().expirationAlert(Arrays.asList(new String[]{id}), false, true);
		} else if ("yd_expirationastop".equals(itemKey)) {
			//资质到期预暂停采购
			String id = this.getModel().getDataEntity().getString("id");
			new RawSupSumBillHelper().expirationStop(Arrays.asList(new String[]{id}), false, true);
		} else if ("yd_purchaselert".equals(itemKey)) {
			//长时间未采购提醒
			String id = this.getModel().getDataEntity().getString("id");
			new RawSupSumBillHelper().checkLongTimeNoPurchase(Arrays.asList(new String[]{id}), 1, false, true, true);
		} else if ("yd_purchasestop".equals(itemKey)) {
			//长时间未采购暂停合格供应商目录
			String id = this.getModel().getDataEntity().getString("id");
			new RawSupSumBillHelper().checkLongTimeNoPurchase(Arrays.asList(new String[]{id}), 2, true, true, true);
		}
	}

	@Override
	public void beforeClosed(BeforeClosedEvent e) {
		super.beforeClosed(e);
		e.setCheckDataChange(false);
	}

	@Override
	public void registerListener(EventObject e) {
		super.registerListener(e);

		BasedataEdit yd_rawmataccbill = (BasedataEdit) this.getControl("yd_accbillno");//原辅料准入单重写打开监听
		yd_rawmataccbill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			// 校验是否有特殊权限,yzl,20221125
			DynamicObject accessBill = this.getModel().getDataEntity().getDynamicObject("yd_accbillno");
			ITimeService timeService = (ITimeService) this.getView().getService(ITimeService.class);
			IUserService userService = (IUserService) this.getView().getService(IUserService.class);
			long userId = Long.parseLong(RequestContext.get().getUserId());
			String entityNumber = "yd_rawmatsupaccessbill";
			QFilter filter = PermissionFilterUtil.getSpecialDataPermissionFilter("srm", entityNumber, "view", timeService, userService);
			DynamicObjectCollection list = QueryServiceHelper.query(entityNumber, "id,billno", filter.toArray());
			Boolean canView = false;
			for (DynamicObject obj : list) {
				if (accessBill.getString("name").equalsIgnoreCase(obj.getString("billno"))) {
					canView = true;
					break;
				}
			}
			if (!canView) {
				this.getView().showErrorNotification("无权限查看准入单" + accessBill.getString("name") + "！");
			} else {
				this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(), "yd_rawmatsupaccessbill", "yd_rawmatsupaccessbill"));
			}
		});

		BasedataEdit yd_newmatbill = (BasedataEdit) this.getControl("yd_newreqbillno");//研发准入单重写打开监听
		yd_newmatbill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_newmatreqbill", "yd_newmatreqbill"));
		});
	}
}
