package kd.bos.tcbj.srm.admittance.plugin;

import java.util.List;
import java.util.Set;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;

/**
 * 供应商的准入协同页面的列表界面插件，用于做供应商台账展示
 * @auditor yanzuwei
 * @date 2022年8月8日
 * 
 */
public class RawsupsumbillList extends AbstractListPlugin {
	
	/**
	 * 添加默认过滤方法
	 */
	@Override
	public void setFilter(SetFilterEvent e) {
		super.setFilter(e);
		List qFilterList = e.getQFilters();
		Set<String> srmSupplierIds = new BizPartnerBizHelper().getCurrentSrmSupplierId();
		if(srmSupplierIds != null && srmSupplierIds.size() > 0) {//如果对应获取当前登录供应商有返回值  证明为供应商类型的商务伙伴登录 ，过滤对应供应商的数据进行查看
			qFilterList.add(new QFilter("yd_agent", QCP.in , srmSupplierIds));
//			qFilterList.add(new QFilter("billstatus", QCP.not_equals, "A"));//过滤保存状态不查看
		}
		
	}
}
