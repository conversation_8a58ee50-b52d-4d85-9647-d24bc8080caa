package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;
import java.util.HashMap;

import org.apache.commons.lang3.StringUtils;

import kd.bos.form.control.Control;
import kd.bos.form.plugin.AbstractFormPlugin;

/**
 * 备注输入框备注说明
 * @auditor liefengWu 
 * @date 2022年6月9日
 * 
 */
public class RemarkInputForm extends AbstractFormPlugin {
	
	//页面确认按钮标识
    private final static String KEY_OK = "yd_confirmbtn";
    //页面取消按钮标识
    private final static String KEY_CANCEL = "yd_cancelbtn";
    
    //页面备注信息控件标识
    private final static String KEY_REMARK = "yd_remark";

    /**
     * 注册监听，监听页面确认按钮和取消按钮
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        //页面确认按钮和取消按钮添加监听
        this.addClickListeners(KEY_OK, KEY_CANCEL);
    }
	
    
    public void click(EventObject evt) {
        super.click(evt);
        //获取被点击的控件对象
        Control source = (Control) evt.getSource();
        if (StringUtils.equals(source.getKey(), KEY_OK)) {
            HashMap<String, String> hashMap 	= new HashMap<>();
            hashMap.put(KEY_REMARK, String.valueOf(this.getModel().getValue(KEY_REMARK)));
            
            this.getView().returnDataToParent(hashMap);
            this.getView().close();
        } else if (StringUtils.equals(source.getKey(), KEY_CANCEL)) {
            this.getView().returnDataToParent(null);
            this.getView().close();
        }
    }
}
