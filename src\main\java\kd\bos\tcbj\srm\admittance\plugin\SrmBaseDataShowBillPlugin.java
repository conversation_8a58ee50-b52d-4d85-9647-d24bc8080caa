package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.base.BaseShowParameter;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillOperationStatus;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.form.ShowType;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.events.BeforeF7ViewDetailEvent;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;

import java.util.EventObject;
import java.util.Objects;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.SrmBaseDataShowBillPlugin
 * @className SrmBaseDataShowEditPlugin
 * @author: hst
 * @createDate: 2024/07/22
 * @description: SRM基础资料弹窗干预界面插件
 * @version: v1.0
 */
public class SrmBaseDataShowBillPlugin extends AbstractBillPlugIn {

    /**
     * 注册监听
     * @param e
     * @author: hst
     * @creaetDate: 2024/07/22
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.materialF7Listener();
    }

    /**
     * 物料基础资料监听
     * @author: hst
     * @createDate: 2024/07/22
     */
    private void materialF7Listener () {
        IDataEntityType dataEntityType = this.getModel().getDataEntity().getDataEntityType();
        Set<String> fieldNames = DynamicObjectUtil.getF7FieldNameByType(dataEntityType, "bd_material");
        for (String fieldName : fieldNames) {
            BasedataEdit edit = this.getControl(fieldName);
            if (Objects.nonNull(edit)) {
                edit.addBeforeF7ViewDetailListener((BeforeF7ViewDetailEvent evt) -> {
                    evt.setCancel(true);
                    BaseShowParameter baseShowParameter = new BaseShowParameter();
                    baseShowParameter.setFormId("yd_srm_material");
                    baseShowParameter.setPkId(evt.getPkId());
                    baseShowParameter.setBillStatus(BillOperationStatus.VIEW);
                    baseShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
                    this.getView().showForm(baseShowParameter);
                });
            }
        }
    }
}
