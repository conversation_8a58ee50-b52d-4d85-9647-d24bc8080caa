package kd.bos.tcbj.srm.admittance.plugin;

import io.searchbox.strings.StringUtils;
import kd.bos.base.BaseShowParameter;
import kd.bos.bill.BillOperationStatus;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.form.ShowType;
import kd.bos.form.events.HyperLinkClickArgs;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.list.BillList;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;

import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.SrmBaseDataShowListPlugin
 * @className SrmBaseDataShowEditPlugin
 * @author: hst
 * @createDate: 2024/07/22
 * @description: SRM基础资料查询基础资料列表插件
 * @version: v1.0
 */
public class SrmBaseDataShowListPlugin extends AbstractListPlugin {

    /**
     * 超链接点击事件
     * @param args
     * @author: hst
     * @createDate: 2024/07/22
     */
    @Override
    public void billListHyperLinkClick(HyperLinkClickArgs args) {
        super.billListHyperLinkClick(args);
        // 弹出基础资料页面
        this.openBaseDataPage(args);
    }

    /**
     * 弹出基础资料页面
     * @author: hst
     * @createDate: 2024/07/22
     */
    private void openBaseDataPage (HyperLinkClickArgs args) {
        HyperLinkClickEvent evt = args.getHyperLinkClickEvent();
        BillList billList = (BillList) evt.getSource();

        String fieldName = evt.getFieldName();
        if (fieldName.contains("_name")) {
            fieldName = fieldName.substring(0, fieldName.lastIndexOf("_"));

            if (StringUtils.isNotBlank(fieldName)) {
                String formId = billList.getBillFormId();
                ListSelectedRow row = billList.getCurrentSelectedRowInfo();
                Object billId = row.getPrimaryKeyValue();

                DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, formId);
                List<String> fields = DynamicObjectUtil.getAllField(bill.getDynamicObjectType());

                if (fields.contains(fieldName)) {
                    if (bill.get(fieldName) instanceof DynamicObject) {
                        args.setCancel(true);

                        DynamicObject object = bill.getDynamicObject(fieldName);
                        String baseName = object.getDynamicObjectType().getName();

                        baseName = getBaseDataName(baseName);

                        BaseShowParameter baseShowParameter = new BaseShowParameter();
                        baseShowParameter.setFormId(baseName);
                        baseShowParameter.setPkId(object.getPkValue());
                        baseShowParameter.setBillStatus(BillOperationStatus.VIEW);
                        baseShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
                        this.getView().showForm(baseShowParameter);
                    }
                }
            }
        }
    }

    /**
     * 获取基础资料标识
     * @param name
     * @author: hst
     * @createDate: 2024/07/22
     */
    private String getBaseDataName (String name) {
        if (name.equals("bd_material")) {
            return "yd_srm_material";
        }

        return name;
    }
}
