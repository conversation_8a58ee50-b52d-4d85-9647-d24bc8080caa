package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.base.AbstractBasePlugIn;
import kd.bos.base.BaseShowParameter;
import kd.bos.bill.BillOperationStatus;
import kd.bos.form.ShowType;
import org.apache.commons.lang3.StringUtils;

import java.util.EventObject;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.SrmMaterialBasePlugin
 * @className SrmMaterialEditPlugin
 * @author: hst
 * @createDate: 2024/07/21
 * @description: 物料资质库界面插件
 * @version: v1.0
 */
public class SrmMaterialBasePlugin extends AbstractBasePlugIn {

    /**
     * 数据绑定后事件
     * @param e
     * @author: hst
     * @createDate: 2024/07/21
     */
    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        // 加载物料基础资料页面
        this.showMaterialInfo();
    }

    /**
     * 加载合格供应商目录页面
     * @author: hst
     * @createDate: 2024/03/11
     */
    private void showMaterialInfo () {
        String materialId = this.getModel().getDataEntity().getString("id");

        if (StringUtils.isNotBlank(materialId) && !"0".equals(materialId)) {
            BaseShowParameter baseShowParameter = new BaseShowParameter();
            baseShowParameter.setFormId("yd_bd_material_layout");
            baseShowParameter.setPkId(Long.valueOf(materialId).longValue());
            baseShowParameter.setBillStatus(BillOperationStatus.VIEW);
            baseShowParameter.getOpenStyle().setShowType(ShowType.InContainer);
            baseShowParameter.getOpenStyle().setTargetKey("yd_baseinfo");
            this.getView().showForm(baseShowParameter);
        }
    }
}
