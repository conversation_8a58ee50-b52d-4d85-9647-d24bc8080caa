package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.tcbj.srm.admittance.helper.BizBillHelper;

/**
 * @auditor yanzu<PERSON>
 * @date 2022年10月10日
 * 
 */
public class SrmProducersBillEdit extends AbstractBillPlugIn {
	
	@Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);
		// 设置组织为股份
		this.getModel().getDataEntity().set("createorg", new BizBillHelper().getObjByBillNumber("bos_org", "000002"));
	}
}
