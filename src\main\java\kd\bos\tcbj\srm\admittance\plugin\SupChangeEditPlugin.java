package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.field.BasedataEdit;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;

import java.util.ArrayList;
import java.util.EventObject;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.SupChangeEditPlugin
 * @className SupChangeEditPlugin
 * @author: hst
 * @createDate: 2024/05/22
 * @description: 资料变更管理表单插件
 * @version: v1.0
 */
public class SupChangeEditPlugin extends AbstractBillPlugIn {

    /**
     * 事件注册
     * @param e
     * @author: hst
     * @createDate: 2024/05/22
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.addBeforeF7SelectListener();
    }

    /**
     * 基础资料选择过滤条件
     * @author: hst
     * @createDate: 2024/05/22
     */
    private void addBeforeF7SelectListener () {
        // 审批人员
        BasedataEdit userEdit = this.getView().getControl("yd_audituser");
        // 弹窗前过滤
        userEdit.addBeforeF7SelectListener((evt) -> {
            String belongOrg = this.getModel().getDataEntity().getString("yd_belongorg");
            if (StringUtils.isNotBlank(belongOrg)) {
                String key = "Audit_User_" + belongOrg;
                List<String> userIds = new ArrayList<>();
                DataSet param = QueryServiceHelper.queryDataSet(this.getClass().getName(),
                        "yd_paramconfigure","yd_paramentity.yd_user.fbasedataid",
                        new QFilter[]{new QFilter("yd_paramentity.yd_paramname",QFilter.equals,key)
                                , new QFilter("yd_bill",QFilter.equals,"8")},null);
                for (Row row : param) {
                    userIds.add(row.getString("yd_paramentity.yd_user.fbasedataid"));
                }
                evt.getCustomQFilters().add(new QFilter("id", QFilter.in, userIds));
                evt.getFormShowParameter().setFormId("bos_list");
            } else {
                this.getView().showTipNotification("请先选择所属部门！");
                evt.setCancel(true);
            }
        });
    }
}
