package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.ShowType;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.metadata.dao.MetaCategory;
import kd.bos.metadata.dao.MetadataDao;
import kd.bos.metadata.form.FormMetadata;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.plugin.KdepBillPlugin;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.admittance.vo.SupplyCommentVo;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordGroup;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordItem;
import kd.bos.workflow.engine.msg.info.MessageInfo;
import kd.bos.workflow.service.impl.WorkflowServiceImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 补充意见分录处理
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-5
 */
public class SupComEntryEdit extends KdepBillPlugin {

    /**补充意见按钮标识*/
    public static final String BTN_SUPPLYCOMMENT = "yd_btn_supplycomment";
    /**补充意见操作标识*/
    public static final String OP_SUPPLYCOMMENT = "supplycomment";
    /**补充意见关闭弹窗Action标识*/
    public static final String ACTION_SUPPLYCOMMENT = "ACTION_SUPPLYCOMMENT";

    /**
     * 操作前处理
     * <AUTHOR>
     * @date 2022-9-5
     */
    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs e) {
        String opKey = this.getOpKey(e);
        if (StringUtils.equals(OP_SUPPLYCOMMENT, opKey)) {
            actionSupplyComment(e);
        }
    }

    private void actionSupplyComment(BeforeDoOperationEventArgs e) {
        if (this.getId() == null || (Long)this.getId() == 0L) throw new KDBizException("单据保存后再进行补充意见！");
        // 点击后弹窗显示处理
        Map<String, Object> param = new HashMap<>();
        param.put("pageId", this.getView().getPageId()); // 页面ID

        // 弹窗处理
        this.showForm(BillTypeHelper.BILLTYPE_SUPPLYCOMMENT, param, new CloseCallBack(this, ACTION_SUPPLYCOMMENT), ShowType.Modal);
    }

    /**
     * 弹窗操作结束后处理
     * <AUTHOR>
     * @date 2022-9-5
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        if (StringUtils.equals(closedCallBackEvent.getActionId(), ACTION_SUPPLYCOMMENT)) {
            Map<String, Object> returnData = (Map<String, Object>) closedCallBackEvent.getReturnData();
            // 如果返回值不为空，且为true，则刷新界面
            if (returnData != null) {
                // 解析
                String comment = (String) returnData.get(SupplyCommentVo.KEY_COMMENT);
                DynamicObjectCollection srcAttCol = (DynamicObjectCollection) returnData.get(SupplyCommentVo.KEY_COMMENTATTR);
                List<Long> attachIdList = new ArrayList<>();
                srcAttCol.forEach(attach -> attachIdList.add(attach.getDynamicObject("fbasedataId").getLong("id")));
                int newEntryRow = this.getModel().createNewEntryRow(SupplyCommentVo.KEY_ATTRENTRYENTITY);
                this.setValue(SupplyCommentVo.KEY_OPRATOR, BizHelper.getUserInfo(), newEntryRow);
                this.setValue(SupplyCommentVo.KEY_OPERATETIME, new Date(), newEntryRow);
                this.setValue(SupplyCommentVo.KEY_COMMENT, comment, newEntryRow);
                this.setValue(SupplyCommentVo.KEY_COMMENTATTR, attachIdList.toArray(new Object[0]), newEntryRow);
                // update by hst 2023/02/13 保存成功后需要发送传阅给工作流已处理人员
                OperationResult result =  this.getView().invokeOperation("save");
                if (result.isSuccess()) {
                    this.sendMessage(comment);
                }
            }
        }
    }

    /**
     * 值变更事件
     * <AUTHOR>
     * @date 2022/12/10
     */
    @Override
    public void propertyChanged(PropertyChangedArgs e) {
        super.propertyChanged(e);
        
    }

    /**
     * 向已审批的用户发送传阅信息
     * @author: hst
     * @createDate: 2023/02/14
     */
    public void sendMessage(String comment) {
        String billId = this.getModel().getDataEntity().getPkValue().toString();
        Long userId = UserServiceHelper.getCurrentUserId();
        String taskId = "";
        Set<Long> userIds = new HashSet<>();
        List<IApprovalRecordGroup> approvalRecordGroupList = new WorkflowServiceImpl().getAllApprovalRecordInclCoordinate(billId);
        for (IApprovalRecordGroup approvalRecord : approvalRecordGroupList) {
            for (IApprovalRecordItem children : approvalRecord.getChildren()) {
                // 已审批的用户，包括协办
                if (Objects.nonNull(children.getTime()) && StringUtils.isNotBlank(children.getMessage())) {
                    Long owner = children.getUserId();
                    if (!String.valueOf(userId).equals(String.valueOf(children.getUserId()))) {
                        userIds.add(owner);
                    }
                    taskId = children.getTaskId();
                }
            }
        }

        // update by hst 2024/04/08 判空
        if (userIds.size() > 0) {
            String formId = this.getModel().getDataEntity().getDataEntityType().getName();
            // 元数据信息
            FormMetadata formMetadata = (FormMetadata) MetadataDao.readRuntimeMeta(MetadataDao.getIdByNumber(formId, MetaCategory.Form), MetaCategory.Form);
            // 元数据名称
            LocaleString metaName = formMetadata.getRootAp().getName();
            // 当前用户信息
            Map<String, Object> userInfo = UserServiceHelper.getUserInfoByID(userId);
            Object userName = userInfo.get("name");
            // 标题
            LocaleString title = new LocaleString("请查阅：您审批的" + metaName.getLocaleValue() + "【"
                    + this.getModel().getDataEntity().getString("billno") + "】 已由 "
                    + userName + " 进行补充意见" + (StringUtils.isBlank(comment) ? "-" + DateUtil.date2str(new Date(), DateUtil.FORMAT_PARTEN) : ""));
            // 内容
            LocaleString context = new LocaleString(StringUtils.isNotBlank(comment)
                    ? (comment + "-" + DateUtil.date2str(new Date(), DateUtil.FORMAT_PARTEN)) : "");

            MessageInfo messageInfo = MessageUtil.sendMessageCenter(formId, billId, userIds, title, context);

            // 发送ESB待办
            try {
                MessageUtil.sendESBMessage(messageInfo);
            } catch (Exception e) {
                this.getView().showErrorNotification(e.getMessage());
            }
        }
    }
}
