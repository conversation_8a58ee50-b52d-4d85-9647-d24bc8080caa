package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillOperationStatus;
import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.EventObject;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.SupSumBillTempEditPlugin
 * @className SupSumBillTempEditPlugin
 * @author: hst
 * @createDate: 2024/03/11
 * @description: 旧台账-临时允许采购表单界面插件
 * @version: v1.0
 */
public class SupSumBillTempEditPlugin extends AbstractBillPlugIn {

    /**
     * 新增数据包加载完成后事件
     * @param e
     * @author: hst
     * @createDate: 2024/03/11
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        // 初始化时，根据父页面传参绑定数据
        this.initBindData();
    }

    /**
     * 数据绑定后事件
     * @param e
     * @author: hst
     * @createDate: 2024/03/11
     */
    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        // 加载合格供应商目录页面
        this.showSupSumBill();
    }

    /**
     * 初始化时，根据父页面传参绑定数据
     * @author: hst
     * @createDate: 2024/03/11
     */
    private void initBindData () {
        FormShowParameter showParameter = this.getView().getFormShowParameter();
        if (Objects.nonNull(showParameter)) {
            Map<String, Object> param = showParameter.getCustomParams();
            if (param.containsKey("sourceId") && param.containsKey("type")) {
                String sourceId = param.get("sourceId").toString();
                String billType = param.get("type").toString();
                DynamicObject bill = BusinessDataServiceHelper.loadSingle(sourceId,billType);
                if (Objects.nonNull(bill)) {
                    this.getModel().setValue("yd_material",bill.getDynamicObject("yd_material"));
                    this.getModel().setValue("yd_supplier",bill.getDynamicObject("yd_supplier"));
                    this.getModel().setValue("yd_supname",bill.getString("yd_supname"));
                    this.getModel().setValue("yd_supstatus",bill.getString("yd_supstatus"));
                    this.getModel().setValue("yd_billtype",billType);
                    this.getModel().setValue("yd_sourceid",sourceId);
                    this.getModel().setValue("yd_billno",bill.getString("billno"));
                }
            }
        }
    }

    /**
     * 加载合格供应商目录页面
     * @author: hst
     * @createDate: 2024/03/11
     */
    private void showSupSumBill () {
        String billId = this.getModel().getDataEntity().getString("yd_sourceid");
        String type = this.getModel().getDataEntity().getString("yd_billtype");

        if (StringUtils.isNotBlank(billId) && StringUtils.isNotBlank(type)) {
            BillShowParameter billShowParameter = new BillShowParameter();
            billShowParameter.setFormId(type + "_layout");
            billShowParameter.setPkId(Long.valueOf(billId).longValue());
            billShowParameter.setBillStatus(BillOperationStatus.VIEW);
            billShowParameter.getOpenStyle().setShowType(ShowType.InContainer);
            billShowParameter.getOpenStyle().setTargetKey("yd_flexpanelap");
            this.getView().showForm(billShowParameter);
        }
    }
}
