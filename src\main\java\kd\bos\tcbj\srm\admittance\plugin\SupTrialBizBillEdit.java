package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import org.apache.commons.lang3.StringUtils;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillShowParameter;
import kd.bos.bill.OperationStatus;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.form.ShowType;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.form.field.BasedataEdit;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.scm.common.util.BaseDataViewDetailUtil;

/**
 * 供应商试产业务单 编辑自定义插件
 * @auditor cary
 * @date 2022年6月7日
 * 
 */
public class SupTrialBizBillEdit extends AbstractBillPlugIn
{
	@Override
	public void propertyChanged(PropertyChangedArgs e) 
	{
	   super.propertyChanged(e); 
	   String fieldKey = e.getProperty().getName();
       if (StringUtils.equals("yd_supaccess",fieldKey))
       {
    	  DynamicObject value =(DynamicObject)e.getChangeSet()[0].getNewValue();// 字段新值
          if(value!=null)
          {
              loadSupAccData(""+value.getPkValue());
          } 
       }	 
	   
	}
	
	 private void loadSupAccData(String id)
	 {//加载供应商准入单信息
		
		  DynamicObject   billInfo= BusinessDataServiceHelper.loadSingle(id, "yd_rawmatsupaccessbill", "yd_material,entryentity.yd_dealer"); 
		  if(billInfo!=null)
		  {
			  DynamicObject matInfo= billInfo.getDynamicObject("yd_material");   
			  getModel().setValue("yd_materiel", matInfo);	 
			  DynamicObjectCollection entrys= billInfo.getDynamicObjectCollection("entryentity");  
			  if(entrys.size()>0)
			  {
				  getModel().setValue("yd_supplier",entrys.get(0).getDynamicObject("yd_dealer"));
			  }
			  
		  } 
	 }
	 
	 /**
	  * 增加原辅料准入单打开监听  add by liefengWu 20220623
	  */
	 @Override
	public void registerListener(EventObject e) {
		super.registerListener(e);
		BasedataEdit yd_supaccess = (BasedataEdit) this.getControl("yd_supaccess");//原辅料准入单重写打开监听
		yd_supaccess.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_rawmatsupaccessbill", "yd_rawmatsupaccessbill"));
		});
		
		EntryGrid entry = this.getView().getControl("entryentity");//试产分录超链接监听
		entry.addHyperClickListener(new HyperLinkClickListener() {
			
			@Override
			public void hyperLinkClick(HyperLinkClickEvent event) {
				String fileName = event.getFieldName();
				String billType = "";
				String billId = "";
				int rowIndex = event.getRowIndex();
				
				if("yd_jobbillno".equals(fileName)) {//试产作业单
					billType = "yd_suptrialjobbill";
					billId = GeneralFormatUtils.getString(getModel().getValue("yd_jobbillid", rowIndex));
				}
				
				if(StringUtils.isEmpty(billId) || StringUtils.isEmpty(billType)) {
					return;
				}
				// 打开单据界面
				BillShowParameter parameter = new BillShowParameter();
				parameter.setFormId(billType);
				parameter.setPkId(billId);
				parameter.setStatus(OperationStatus.VIEW);
				parameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
				parameter.setHasRight(true);
				getView().showForm(parameter);
			}
		});
	}
}
