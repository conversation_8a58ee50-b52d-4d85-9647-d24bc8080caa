package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.db.DB;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.botp.plugin.AbstractConvertPlugIn;
import kd.bos.entity.botp.plugin.args.AfterConvertEventArgs;
import kd.bos.entity.botp.plugin.args.BeforeBuildRowConditionEventArgs;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 试产业务单生成试产作业单_自定义转换插件
 * @auditor liefengWu
 * @date 2022年6月28日
 * 
 */
public class SupTrialBizToJobBillConvertPlugin extends AbstractConvertPlugIn {
	
	private String srcObjectId = "";
	
	@Override
	public void beforeBuildRowCondition(BeforeBuildRowConditionEventArgs e) {
		super.beforeBuildRowCondition(e);
		Long[] srcIds = e.getSelectedRows().stream().map(ListSelectedRow::getPrimaryKeyValue).toArray(Long[]::new);
		String targetEntityNumber = this.getTgtMainType().getName();
		
		if("yd_suptrialjobbill".equals(targetEntityNumber)) {
			for (Long srcId : srcIds) {
				srcObjectId = GeneralFormatUtils.getString(srcId);
			}
		}
	}
	
	@Override
	public void afterConvert(AfterConvertEventArgs e) {
		super.afterConvert(e);
		ExtendedDataEntity[] billDataEntitys = e.getTargetExtDataEntitySet().FindByEntityKey("yd_suptrialjobbill");
		for(ExtendedDataEntity billDataEntity : billDataEntitys){
			
			//工艺技术部评估附件处理自动带出,yzw,begin
			DynamicObject srcInfo = BusinessDataServiceHelper.loadSingle(srcObjectId, "yd_suptrialbizbill");
			DynamicObjectCollection accessattachCol = srcInfo.getDynamicObjectCollection("yd_gyaccessattach");
			DynamicObjectCollection attCol = new DynamicObjectCollection();  // 最终的附件
			
			if(accessattachCol != null && accessattachCol.size() > 0) {
				for(int jIndex = 0 ; jIndex < accessattachCol.size() ; jIndex ++) {
					DynamicObject att_bd = accessattachCol.get(jIndex);//原附件
					DynamicObject bd = copyAttachBd(att_bd.getDynamicObject("fbasedataid"));//复制附件
					bd.set("status", "B");
					SaveServiceHelper.save(new DynamicObject[] {bd});//将复制的附件存入系统
					//创建附件字段信息  (对应的是附件字段的表结构)
					DynamicObject attField = new DynamicObject(accessattachCol.getDynamicObjectType());
					System.out.println("----"+att_bd.getDynamicObjectType());
					attField.set("fbasedataid", bd);
					attCol.add(attField);
				}
			}
			// yzw,end
			
			DynamicObject targetDObj = billDataEntity.getDataEntity();
			
			DynamicObjectCollection entryColl = targetDObj.getDynamicObjectCollection("yd_entryentity");
			
			String[] entryAttachType = new String[] {"0","1","2"};
			for(int index = 0; index < entryAttachType.length ; index ++ ) {
				DynamicObject entryInfo = entryColl.addNew();
				entryInfo.set("yd_gyaccesstype",entryAttachType[index]);
			//	entryColl.add(entryInfo);
			}	
			targetDObj.set("yd_entryentity", entryColl);
			targetDObj.set("yd_gyaccessattach",attCol);  // 赋值附件,yzw
		}
	}
	
	private DynamicObject copyAttachBd(DynamicObject att_bd) {
		
		System.out.println(att_bd.getDataEntityType().getName());
		DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject(att_bd.getDataEntityType().getName());
		DataEntityPropertyCollection properties = att_bd.getDataEntityType().getProperties();
		for(IDataEntityProperty p1:properties) {
			//id 和 multilanguagetext不用设置 设置了会导致字段的重复
			
			System.out.println(p1.getName()+"----"+att_bd.get(p1));
			if(!p1.getName().equals("id") &&  !p1.getName().equals("multilanguagetext")) {
				//uid加了索引，因此不能重复    粗略可以使用下面的策略       原本的生成策略没有找到
				if(p1.getName().equals("uid")) {
					newObj.set("uid",DB.genLongId(""));
				}else {
					Object value = att_bd.get(p1);
					newObj.set(p1, value);
				} 
			}
		}
		return newObj;
	}
	
}
