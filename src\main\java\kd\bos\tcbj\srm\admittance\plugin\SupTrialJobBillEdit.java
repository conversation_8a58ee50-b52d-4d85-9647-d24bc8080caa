package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.field.BasedataEdit;
import kd.scm.common.util.BaseDataViewDetailUtil;

/**
 * 试产作业单 编辑自定义插件
 * @auditor liefengWu
 * @date 2022年6月23日
 * 
 */
public class SupTrialJobBillEdit extends AbstractBillPlugIn {
	 /**
	  * 增加原辅料准入单打开监听  add by liefengWu 20220623
	  */
	 @Override
	public void registerListener(EventObject e) {
		super.registerListener(e);
		BasedataEdit yd_supaccess = (BasedataEdit) this.getControl("yd_supaccess");//原辅料准入单重写打开监听
		yd_supaccess.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_rawmatsupaccessbill", "yd_rawmatsupaccessbill"));
		});
		
		BasedataEdit yd_suptrialbizbill = (BasedataEdit) this.getControl("yd_suptrialbizbill");
		yd_suptrialbizbill.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_suptrialbizbill", "yd_suptrialbizbill"));
		});
		
	}
	 
	 @Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);
		DynamicObjectCollection entryColl = this.getModel().getDataEntity().
				getDynamicObjectCollection("yd_entryentity");
		entryColl.clear();
	
		String[] entryAttachType = new String[] {"0","1","2"};
		for(int index = 0; index < entryAttachType.length ; index ++ ) {
			this.getModel().createNewEntryRow("yd_entryentity");
			this.getModel().setValue("yd_gyaccesstype",entryAttachType[index],index);
		}	
	}
}
