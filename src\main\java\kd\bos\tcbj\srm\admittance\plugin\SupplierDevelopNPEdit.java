package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.form.control.RichTextEditor;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.tcbj.im.plugin.KdepBillPlugin;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.EventObject;
import java.util.Set;

/**
 * 供应商研发新品单编辑界面
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-2
 */
public class SupplierDevelopNPEdit extends KdepBillPlugin {

    /**
     * 操作前处理
     * <AUTHOR>
     * @date 2022-9-2
     */
    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs event) {
        String opKey = getOpKey(event);
        // 保存提交前处理
        if (StringUtils.equalsIgnoreCase("save", opKey) || StringUtils.equalsIgnoreCase("submit", opKey)) {
            beforeSaveOrSubmit(event, opKey);
        }
    }

    /**
     * 保存或则提交前操作
     * @param event 事件
     * @param opKey 操作标识
     * <AUTHOR>
     * @date 2022-9-2
     */
    private void beforeSaveOrSubmit(BeforeDoOperationEventArgs event, String opKey) {
        // 保存/提交的时候将内容存储的控件中
        RichTextEditor edit = this.getControl("yd_sourcecontent"); //富文本控件
        this.getModel().setValue("yd_sourcecontent_bg", edit.getText()); // 富文本内存存储在控件中

        // 校验地址是否填写开头正确
        String link = (String) this.getModel().getValue("yd_link"); // 链接
        if (StringUtils.isNotBlank(link) && !(link.startsWith("http://") || link.startsWith("https://"))) {
            this.getView().showTipNotification("链接地址填写不正确，请以http://或者https://开头");
            event.setCancel(true);
        }
    }

    /**
     * 数据绑定加载后处理
     * <AUTHOR>
     * @date 2022-9-2
     */
    @Override
    public void afterBindData(EventObject e) {
        // 打开界面的时候，给富文本赋值上存储的内容
        RichTextEditor edit = this.getControl("yd_sourcecontent"); // 富文本控件
        edit.setText((String) this.getModel().getValue("yd_sourcecontent_bg"));
    }

    @Override
    public void afterCreateNewData(EventObject e) {
        // 根据当前人带出供应商库供应商信息
        Set<String> currentSrmSupplierId = new BizPartnerBizHelper().getCurrentSrmSupplierId();
        if (currentSrmSupplierId.size() > 0) {
            // 获取第一个值
            String srmSupplierId = currentSrmSupplierId.iterator().next();
            // 设置到供应商控件中
            this.getModel().setItemValueByID("yd_supplier", srmSupplierId);
        }
    }
}
