package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import org.apache.commons.lang3.StringUtils;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.form.control.Control;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.control.events.ItemClickListener;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.TextEdit;
import kd.bos.mvc.bill.BillModel;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.UIUtils;
import kd.scm.common.util.BaseDataViewDetailUtil; 

/**
 * @auditor yanzuwei
 * @date 2022年8月2日
 * 
 */
public class SupplierEditEx extends AbstractBillPlugIn
{
	@Override
	public void registerListener(EventObject e) 
	{
		 super.registerListener(e); 
		 this.addClickListeners("yd_savemat");
		 
	}
	@Override
	public void click(EventObject evt) 
	{
		 super.click(evt);
		 
		String key= ((Control)evt.getSource()).getKey();
		if("yd_savemat".equals(key))
		{
			EntryGrid entry=this.getControl("entry_goods");
			int rows[]=entry.getSelectRows();
			if(rows==null || rows.length==0)
			{
			   this.getView().showErrorNotification("请先选择产品分录行后，再选择物料");	
			   return;
			} 
		   UIUtils.selectF7(this, "bd_material", "selectMat",false,null); 
		}
	}
	 
	@Override
	public void closedCallBack(ClosedCallBackEvent closedCallBackEvent)
	{
		super.closedCallBack(closedCallBackEvent);
		if (StringUtils.equals(closedCallBackEvent.getActionId(), "selectMat") )
		 { 
			ListSelectedRowCollection selectData=(ListSelectedRowCollection)closedCallBackEvent.getReturnData();
            if(selectData!=null&&selectData.size()>0)
            {
            	ListSelectedRow row=selectData.get(0);
            	Object matId=row.getPrimaryKeyValue(); 
            	EntryGrid entry=this.getControl("entry_goods");
    			int rows[]=entry.getSelectRows();
    			if(rows!=null&&rows.length>0)
    			{
    				  int rowIndex=rows[0];
    				  BillModel model = (BillModel) getModel(); 
    				  DynamicObject  info= BusinessDataServiceHelper.loadSingle(model.getPKValue(), "srm_supplier","entry_goods.yd_easmat");
    				  DynamicObjectCollection entrys= info.getDynamicObjectCollection("entry_goods");
    				  DynamicObject rowInfo= entrys.get(rowIndex);
    				  rowInfo.set("yd_easmat",  BusinessDataServiceHelper.loadSingle(matId, "bd_material","id"));
    				  SaveServiceHelper.save(new DynamicObject[] {info});
    				  model.load(model.getPKValue());
    				  getView().updateView("entry_goods");
    			}
            }
			
			
		 }
		
	}
}
