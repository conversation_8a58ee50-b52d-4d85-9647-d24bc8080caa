package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDBizException;
import kd.bos.form.FormShowParameter;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.plugin.KdepFormPlugin;
import kd.bos.tcbj.srm.admittance.vo.SupplyCommentVo;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 补充意见表单插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-5
 */
public class SupplyCommentPlugin extends KdepFormPlugin {

    /**日志*/
    // 创建logger
    private final static Log logger = LogFactory.getLog("SupplyCommentPlugin");

    /**按钮_确认*/
    protected static final String BTN_OK = "btnok";
    /**按钮_取消*/
    protected static final String BTN_CANCEL = "btncancel";
    /**表头_意见*/
    protected static final String HEAD_COMMENT = "yd_comment";
    /**表头_附件*/
    protected static final String HEAD_COMMENTATTR = "yd_commentattr";

    /**
     * 控件事件注册
     * <AUTHOR>
     * @date 2022-9-5
     */
    @Override
    public void registerListener(EventObject e) {
        // 对确认和取消按钮做注册处理
        this.addClickListeners(BTN_OK, BTN_CANCEL);
    }

    @Override
    public void click(EventObject evt) {
        String opKey = getKey(evt);
        // 确认按钮处理
        if (StringUtils.equals(BTN_OK, opKey)) {
            actionOk(evt);
        }else if (StringUtils.equals(BTN_CANCEL, opKey)) {
            actionCancel(evt);
        }
    }

    private void actionOk(EventObject evt) {
        // 获取父表单传递的参数（实体名称、操作ID）
        FormShowParameter parameter = this.getView().getFormShowParameter();
        String pageId = parameter.getCustomParam("pageId");
        if (StringUtils.isBlank(pageId)) throw new KDBizException("父界面参数传递有误，pageId为空！");
        // 判断输入的值是否为空
        String comment = (String) this.getModel().getValue(HEAD_COMMENT);
        DynamicObjectCollection srcAttCol = (DynamicObjectCollection) this.getModel().getValue(HEAD_COMMENTATTR);
        if (StringUtils.isBlank(comment)) throw new KDBizException("意见不能为空！");
        // 更新父页面对象
        updateAttachments(pageId);

        // 关闭表单
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(SupplyCommentVo.KEY_COMMENT, comment); // 意见
        resultMap.put(SupplyCommentVo.KEY_COMMENTATTR, srcAttCol); // 附件
        this.getView().returnDataToParent(resultMap);
        this.getView().close();
    }

    /**
     * 更新附件页面ID
     * @param pageId 父页面ID
     */
    private void updateAttachments(String pageId) {
        QFilter filter1 = new QFilter("tempfile", QCP.equals, "0");
        QFilter filter2 = new QFilter("pageid", QCP.equals, this.getView().getPageId());
        QFilter filter3 = new QFilter("status", QCP.equals, "B");
        String selectProperties = "id, name, tempfile, pageid, size, status, modifytime";
        QFilter[] filters = new QFilter[] { filter1, filter2, filter3 };
        DynamicObject[] attas = BusinessDataServiceHelper.load("bd_attachment", selectProperties, filters);
        for (DynamicObject tempAtta : attas) {
            tempAtta.set("pageid", pageId);
            tempAtta.set("modifytime", new Date());
        }
        // 更新
        SaveServiceHelper.save(attas);
    }

    /**
     * 取消时关闭表单，返回给父节点为空
     * <AUTHOR>
     * @date 2022-9-5
     */
    private void actionCancel(EventObject evt) {
        this.getView().returnDataToParent(null);
        this.getView().close();
    }
}
