package kd.bos.tcbj.srm.admittance.plugin;

import java.util.*;
import java.util.stream.Collectors;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.CloneUtils;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.db.DB;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.botp.plugin.AbstractConvertPlugIn;
import kd.bos.entity.botp.plugin.args.AfterConvertEventArgs;
import kd.bos.entity.botp.plugin.args.BeforeBuildRowConditionEventArgs;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.entity.SupplyInfoEntryFieldEntiy;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;
import kd.bos.tcbj.srm.admittance.helper.RSSBillBizHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.scm.common.enums.SrmSupplierStatusEnum;

/**
 * 资料补充函生成准入单信息 插件功能实现
 * @auditor liefengWu
 * @date 2022年6月10日
 * 
 */
public class SupplyInfToRawmatsupaccesConvertPlugin extends AbstractConvertPlugIn {
	
	private String srcObjectId = "";


	@Override
	public void beforeBuildRowCondition(BeforeBuildRowConditionEventArgs e) {
		super.beforeBuildRowCondition(e);
		Long[] srcIds = e.getSelectedRows().stream().map(ListSelectedRow::getPrimaryKeyValue).toArray(Long[]::new);
		String targetEntityNumber = this.getTgtMainType().getName();
		
		if("yd_rawmatsupaccessbill".equals(targetEntityNumber)) {
			for (Long srcId : srcIds) {
				srcObjectId = GeneralFormatUtils.getString(srcId);
			}
		}
	}
	
	
	private void setCancel(BeforeBuildRowConditionEventArgs e, String errMsg) {
		e.setCustFilterDesc(errMsg);
		e.setIgnoreRuleFilterPolicy(true);
		/*QFilter qFilter;
		//--设置条件表达式，用于脚本执行 （必选）
		e.setCustFilterExpression(" billstatus = '0000' ");
		//--同时设置具有相同含义的QFilter条件，用于选单数据查询 （必选）
		qFilter = new QFilter("billstatus", QCP.equals, "0000"); 
		e.getCustQFilters().add(qFilter);*/
	}
	
	
	 void genSupEntry(Object id,DynamicObject  info)
	 {//生成供应商分录
		 List<DynamicObject> supList=RSSBillBizHelper.getRSSBillList(id);
		 for(DynamicObject rssInfo:supList)
		 {
			 DynamicObjectCollection entrys=(DynamicObjectCollection)info.getDynamicObjectCollection("entryentity");
			 DynamicObject  entryInfo=entrys.addNew();
			 entryInfo.set("yd_dealer", rssInfo.getDynamicObject("yd_agent"));
			 entryInfo.set("yd_producers", rssInfo.getDynamicObject("yd_producers"));  // 生产商
			 entryInfo.set("yd_manufacturer", rssInfo.getString("yd_manufacturer"));
			 entryInfo.set("yd_origin", rssInfo.getString("yd_origin")); 
			 entryInfo.set("yd_matlevel", rssInfo.getString("yd_matleveltype")); 
			 entryInfo.set("yd_supmatname", rssInfo.getString("yd_supmatname")); 
			 entryInfo.set("yd_manufacturerplace", rssInfo.getString("yd_address")); 
			 if(rssInfo.getDate("yd_licensedate")!=null)
			 {
				 entryInfo.set("yd_prdlicense",DateUtil.date2str(rssInfo.getDate("yd_licensedate"),"yyyy-MM-dd"));
			 }
			 
			 if(rssInfo.getDynamicObject("yd_supplieraccesspot")!=null)
			 {
				 DynamicObject spotInfo= BusinessDataServiceHelper.loadSingle(rssInfo.getDynamicObject("yd_supplieraccesspot").getPkValue(), "yd_supplieraccesspot","group");
				 if(spotInfo!=null)
				 {
					 DynamicObject groupInfo= spotInfo.getDynamicObject("group");
					 entryInfo.set("yd_supplierstate", ""+Integer.parseInt(groupInfo.getString("number")));
				 }
			 }
			 
			 entryInfo.set("yd_suptype", "old"); 
		 }
		 
	 }
	
	
	@Override
	public void afterConvert(AfterConvertEventArgs e) {
		// TODO Auto-generated method stub
		super.afterConvert(e);
		ExtendedDataEntity[] billDataEntitys = e.getTargetExtDataEntitySet().FindByEntityKey("yd_rawmatsupaccessbill");

		for(ExtendedDataEntity billDataEntity : billDataEntitys){
			DynamicObject srcInfo = BusinessDataServiceHelper.loadSingle(srcObjectId, "yd_supplyinformationbill");
			genSupEntry(srcInfo.getPkValue(),billDataEntity.getDataEntity());
			/* 根据供应商、生产商、物料获取最新的台账信息 */
			this.setBaseInfo(srcInfo, billDataEntity.getDataEntity());

			List<SupplyInfoEntryFieldEntiy> zzEntryAttachFieldList = AdminAttachHelper.getFieldMap();

	        //附件处理自动带出
			Map<String, List<DynamicObject>> results = getAttachmentInfo(srcInfo);

			for(SupplyInfoEntryFieldEntiy filedEntity :zzEntryAttachFieldList) {//处理对应目标附件字段处理逻辑
				if (filedEntity == null) {
					continue;
				}

				String entryKey = filedEntity.getEntryEntityName();
				for (Map.Entry<String, List<DynamicObject>> result : results.entrySet()) {
					for (DynamicObject oriBill : result.getValue()) {
						/* 目标单附件分录 */
						DynamicObjectCollection mainKeyColl = billDataEntity.getDataEntity().getDynamicObjectCollection(entryKey);
						Set<String> existIds = mainKeyColl.stream().map(temp -> temp.getString(filedEntity.getOriEntryIdField())).collect(Collectors.toSet());
						/* 源单附件分录 */
						DynamicObjectCollection fileEntryColl = oriBill.getDynamicObjectCollection(entryKey);

						if (fileEntryColl != null && fileEntryColl.size() > 0) {
							for (int index = 0; index < fileEntryColl.size(); index++) {
								DynamicObject fileEntryInfo = fileEntryColl.get(index);

								if (existIds.contains(fileEntryInfo.getString("id"))) {
									continue;
								}

								DynamicObject tarEntry = mainKeyColl.addNew();
								/* 资质类型 */
								tarEntry.set(filedEntity.getFileTypeField(), fileEntryInfo.get(filedEntity.getFileTypeField()));
								/* 发证日期 */
								tarEntry.set(filedEntity.getEffectField(), fileEntryInfo.get(filedEntity.getEffectField()));
								/* 有效日期 */
								tarEntry.set(filedEntity.getUnEffectField(), fileEntryInfo.get(filedEntity.getUnEffectField()));
								/* 是否已重命名 */
								tarEntry.set(filedEntity.getRenameField(), fileEntryInfo.get(filedEntity.getRenameField()));
								/* 是否归档 */
								tarEntry.set(filedEntity.getIsUploadField(), fileEntryInfo.get(filedEntity.getIsUploadField()));

								if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getOriEntryIdField()))) {
									/* 原分录ID */
									tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.getString(filedEntity.getOriEntryIdField()));
								} else {
									tarEntry.set(filedEntity.getOriEntryIdField(), fileEntryInfo.getString("id"));
								}

								if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getOriBillIdField()))) {
									/* 原单据ID */
									tarEntry.set(filedEntity.getOriBillIdField(), fileEntryInfo.getString(filedEntity.getOriBillIdField()));
								} else {
									tarEntry.set(filedEntity.getOriBillIdField(), result.getKey().split("&")[0]);
								}

								if (StringUtils.isNotBlank(fileEntryInfo.getString(filedEntity.getBillType()))) {
									/* 来源单据类型 */
									tarEntry.set(filedEntity.getBillType(), fileEntryInfo.getString(filedEntity.getBillType()));
								} else {
									tarEntry.set(filedEntity.getBillType(), result.getKey().split("&")[1]);
								}
								/* 序号 */
								tarEntry.set("seq", mainKeyColl.size() + 1);

								/* 获取对应分录附件字段 多选基础资料数据 */
								DynamicObjectCollection fileAttachColl = fileEntryInfo.getDynamicObjectCollection(filedEntity.getFileField());
								if (fileAttachColl == null || fileAttachColl.size() == 0)
									continue;

								DynamicObjectCollection tarAttachColl = tarEntry.getDynamicObjectCollection(filedEntity.getFileField());
								for (int jIndex = 0; jIndex < fileAttachColl.size(); jIndex++) {
									DynamicObject att_bd = fileAttachColl.get(jIndex);//原附件
									DynamicObject bd = att_bd.getDynamicObject("fbasedataid");//复制附件

									//创建附件字段信息  (对应的是附件字段的表结构)
									DynamicObject attField = tarAttachColl.addNew();
									attField.set("fbasedataid", bd);
								}
							}
						}
					}
				}
			}
			
			
			DynamicObject supplierObj = srcInfo.getDynamicObject("yd_supplier");
			if(supplierObj != null && supplierObj.getPkValue() != null) {
				supplierObj = BusinessDataServiceHelper.loadSingle(supplierObj.getPkValue(), "srm_supplier");
				
				Long groupId = Long.valueOf(supplierObj.getLong("group_id"));
				String auditstatus = supplierObj.getString("auditstatus");
				if (groupId != null && groupId.longValue() != 0L
						&& (SrmSupplierStatusEnum.SUCCESS.getValue().equals(auditstatus)
								|| SrmSupplierStatusEnum.APTITUDE.getValue().equals(auditstatus)
								|| SrmSupplierStatusEnum.UNAPROVE.getValue().equals(auditstatus))) {
					
					billDataEntity.getDataEntity().set("yd_suppliergroup", supplierObj.get("group"));
				}	
			}

			// update by hst 2024/05/28 携带最新版本号
			billDataEntity.setValue("yd_version",getNewestVersion());
	    }
		
	}

	/**
	 * 使用原型模式创建对象
	 * @param cloneObj
	 * @return
	 */
	private DynamicObject cloneObj(DynamicObject cloneObj) {
		DynamicObjectType cloneType = null;
		DynamicObject newObj = null;
		try {
			//获取对象类型
			DynamicObjectType entityType = (DynamicObjectType)cloneObj.getDataEntityType(); 
			//原型模式创建对象
			cloneType = (DynamicObjectType) entityType.clone();
			//根据对象类型创建对象  obj传原型类型创建的一个实例
			newObj = (DynamicObject) new CloneUtils(false, false).clone(cloneType, cloneObj);
		}catch (Exception e) {
			e.printStackTrace();
		}
		return newObj;
	}
	
	private DynamicObject copyAttachBd(DynamicObject att_bd) {
		
		System.out.println(att_bd.getDataEntityType().getName());
		DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject(att_bd.getDataEntityType().getName());
		DataEntityPropertyCollection properties = att_bd.getDataEntityType().getProperties();
		for(IDataEntityProperty p1:properties) {
			//id 和 multilanguagetext不用设置 设置了会导致字段的重复
			
			System.out.println(p1.getName()+"----"+att_bd.get(p1));
			if(!p1.getName().equals("id") &&  !p1.getName().equals("multilanguagetext")) {
				//uid加了索引，因此不能重复    粗略可以使用下面的策略       原本的生成策略没有找到
				if(p1.getName().equals("uid")) {
					newObj.set("uid",DB.genLongId(""));
				}else {
					Object value = att_bd.get(p1);
					newObj.set(p1, value);
				} 
			}
		}
		return newObj;
	}

	/**
	 * 判断附件分录中是否存在附件
	 * @author: hst
	 * @createDate: 2022/10/11
	 * @param entity 元数据
	 * @param entryName 分录名称
	 * @param field 附件字段名
	 * @return
	 */
	public boolean existAttach (DynamicObject entity, String entryName, String field) {
		DynamicObjectCollection entry = entity.getDynamicObjectCollection(entryName);
		if (entry.size() > 0) {
			for (DynamicObject data : entry) {
				if (data.getDynamicObjectCollection(field).size() > 0) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 获取最新版本号
	 * @return
	 * @author: hst
	 * @createDate: 2024/05/28
	 */
	private int getNewestVersion () {
		int version = 0;
		QFilter typeFilter = new QFilter("yd_bill", QFilter.equals, "4");
		QFilter stuFilter = new QFilter("billstatus",QFilter.equals,"C");
		DynamicObjectCollection configs = QueryServiceHelper.query("yd_paramconfigure",
				"yd_versionentity.yd_version", new QFilter[]{typeFilter,stuFilter}, null);
		for (DynamicObject config : configs) {
			if (config.getInt("yd_versionentity.yd_version") > version) {
				version = config.getInt("yd_versionentity.yd_version");
			}
		}
		return version;
	}

	/**
	 * 获取附件信息
	 * @param srcInfo
	 * @return
	 */
	private Map<String, List<DynamicObject>> getAttachmentInfo (DynamicObject srcInfo) {
		Map<String, List<DynamicObject>> results = new HashMap<>();

		/* 资料补充函是否有上游新物料研发准入单 */
		Map<String, HashSet<Long>> sourceMap = BFTrackerServiceHelper.findSourceBills("yd_supplyinformationbill",
				new Long[]{Long.valueOf(srcInfo.getLong("id"))});
		Set<Long> oriBillIds = new HashSet<>();
		for (Map.Entry<String, HashSet<Long>> source : sourceMap.entrySet()) {
			if ("yd_newmatreqbill".equals(source.getKey())) {
				oriBillIds.addAll(source.getValue());
			}
		}

		if (oriBillIds.size() > 0) {
			long supId = srcInfo.getLong("yd_supplier.id");
			long proId = srcInfo.getLong("yd_producers.id");
			DynamicObject[] bills = BusinessDataServiceHelper.load(oriBillIds.toArray(),
					BusinessDataServiceHelper.newDynamicObject("yd_newmatreqbill").getDynamicObjectType());
			for (DynamicObject bill : bills) {
				List<DynamicObject> result = new ArrayList();
				DynamicObjectCollection fileEntries = bill.getDynamicObjectCollection("yd_file1entry");

				for (DynamicObject fileEntry : fileEntries) {
					if (supId == fileEntry.getLong("yd_csup.id")
							&& proId == fileEntry.getLong("yd_cprod.id")) {
						result.add(fileEntry);
					}
				}

				if (result.size() > 0) {
					results.put(bill.getString("id") + "&yd_newreqbase", result);
				}
			}
		} else {
			List<DynamicObject> result = new ArrayList<>();
			result.add(srcInfo);
			results.put(srcInfo.getString("id") + "&yd_supinfobase", result);
		}
		return results;
	}

	/**
	 * 根据供应商、生产商、物料获取最新的台账信息
	 * @author: hongsitao
	 * @createDate: 2024/12/11
	 */
	private void setBaseInfo (DynamicObject srcInfo, DynamicObject tarInfo) {
		DynamicObject material = srcInfo.getDynamicObject("yd_material");
		DynamicObject supplier = srcInfo.getDynamicObject("yd_supplier");
		DynamicObject producer = srcInfo.getDynamicObject("yd_producers");

		if (Objects.nonNull(material) && Objects.nonNull(supplier) && Objects.nonNull(producer)) {
			QFilter qFilter = QFilter.of("yd_material.id = ? and yd_agent = ? and yd_producers = ?",
					material.getPkValue(), supplier.getPkValue(), producer.getPkValue());

			DataSet dataSet = QueryServiceHelper.queryDataSet("setBaseInfo", "yd_rawsupsumbill",
					"yd_spec, yd_weight, yd_unit, yd_minunit, yd_specs, yd_storagecon, yd_manmonth, yd_address, " +
							"yd_matsource, yd_isexplosive, yd_matsourceexp, yd_hasallergen, yd_allergysource, yd_origin," +
							"yd_matleveltype, yd_matcat, yd_beinvolvekeymat, yd_isslevel, yd_supplieraccesspot.number," +
							"yd_supmatname", qFilter.toArray(), "yd_newdate desc").top(1);

			if (dataSet.hasNext()) {
				Row row = dataSet.next();

				tarInfo.set("yd_spec",row.get("yd_spec"));
				tarInfo.set("yd_weight",row.get("yd_weight"));
				tarInfo.set("yd_unit",row.get("yd_unit"));
				tarInfo.set("yd_minunit",row.get("yd_minunit"));
				tarInfo.set("yd_packmodel",row.get("yd_specs"));
				tarInfo.set("yd_storagecon",row.get("yd_storagecon"));
				tarInfo.set("yd_shelflife",row.get("yd_manmonth"));
				tarInfo.set("yd_matsource",row.get("yd_matsource"));
				tarInfo.set("yd_isexplosive",row.get("yd_isexplosive"));
				tarInfo.set("yd_matsourceexp",row.get("yd_matsourceexp"));
				tarInfo.set("yd_hasallergen",row.get("yd_hasallergen"));
				tarInfo.set("yd_allergysource",row.get("yd_allergysource"));
				tarInfo.set("yd_matlevtype",row.get("yd_matleveltype"));
				tarInfo.set("yd_matcat",row.get("yd_matcat"));
				tarInfo.set("yd_beinvolvekeymat",row.get("yd_beinvolvekeymat"));
				tarInfo.set("yd_isslevel",row.get("yd_isslevel"));
			}
		}
	}
}
