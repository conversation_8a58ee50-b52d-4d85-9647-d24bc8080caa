package kd.bos.tcbj.srm.admittance.plugin;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.EventObject;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.OperationStatus;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.db.DB;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.exception.KDBizException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.AttachmentPanel;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeClosedEvent;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.field.AttachmentEdit;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.events.AfterF7SelectEvent;
import kd.bos.form.field.events.AfterF7SelectListener;
import kd.bos.form.operate.FormOperate;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.AttachmentServiceHelper;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.BizBillHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.admittance.imp.SupplyInformationBillMserviceImpl;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.scm.common.util.BaseDataViewDetailUtil;

/**
 * 资料补充函_编辑界面插件
 * @auditor liefengWu
 * @date 2022年6月6日
 * 
 */
public class SupplyInformationBillEdit extends AbstractBillPlugIn {
	
	private String KEY_REPLUE ="KEY_REPLUE";//打回操作指令
	
	@Override
	public void initialize() {
		// TODO Auto-generated method stub
		super.initialize();
		//	initBtn();
	}
	
	@Override
	public void propertyChanged(PropertyChangedArgs e) {
		super.propertyChanged(e);
		
		// 选择物料后带出物料分类，如果是物料编码开头是C原辅料供应商台账，物料编码开头是D、E包材供应商台账，物料编码开头是F、L非生产物料供应商台账
		String fieldKey = e.getProperty().getName();
		if (StringUtils.equalsIgnoreCase("yd_material", fieldKey)) {
			 DynamicObject mat = (DynamicObject) e.getChangeSet()[0].getNewValue();
			 if (mat != null) {
				 String matNum = mat.getString("number");
				 if (matNum.startsWith("C")) {
					 this.getModel().setValue("yd_mattype", "1");
				 } else if (matNum.startsWith("D") || matNum.startsWith("E")) {
					 this.getModel().setValue("yd_mattype", "2");
				 } else if ((matNum.startsWith("F") || matNum.startsWith("L"))) {
					 this.getModel().setValue("yd_mattype", "3");
				 } else {
					 this.getModel().setValue("yd_mattype", null);
				 }
			 }
		} else if (StringUtils.equalsIgnoreCase("yd_supplier", fieldKey)) {
			// 根据供应商带出生产商
			DynamicObject sup = (DynamicObject) e.getChangeSet()[0].getNewValue();
			if (sup != null) {
				String supId = sup.getString("id");
				QFilter supF = new QFilter("yd_srmsupplier.id", QCP.equals, supId);
				DynamicObject[] pros = BusinessDataServiceHelper.load("yd_srmproducers", "id,name,number", supF.toArray());
				if (pros.length == 1) {
					this.getModel().setValue("yd_producers", pros[0]);
				}
			}
		}
		
		// 带出失效日期
		String name = e.getProperty().getName();
        if (this.getModel().getValue("yd_materiallevel")!=null && ("yd_pfitieneffectdate".equals(name) || "yd_pstareffectdate".equals(name))) {
            String matLevel = this.getModel().getValue("yd_materiallevel").toString();
            QFilter qFilter = QFilter.of("yd_bill = ? and yd_accountconfig.yd_fieldmark = ?","2",name);
            DataSet configs = QueryServiceHelper.queryDataSet(this.getClass().getName(),"yd_paramconfigure",
                    "yd_accountconfig.yd_targetfield,yd_accountconfig.yd_validyear,yd_accountconfig.yd_materiallevel",
                    qFilter.toArray(),null);
            while (configs.hasNext()) {
                Row row = configs.next();
                String target = row.getString("yd_accountconfig.yd_targetfield");
                String valid = row.getString("yd_accountconfig.yd_validyear");
                String level = row.getString("yd_accountconfig.yd_materiallevel");
                if (StringUtils.isNotBlank(target) && StringUtils.isNotBlank(valid)
                        && ((StringUtils.isNotBlank(level) && StringUtils.isNotBlank(matLevel) && level.indexOf(matLevel) > 0)
                        || (StringUtils.isBlank(level) && StringUtils.isBlank(matLevel)))) {
                    String parent = e.getProperty().getParent().getName();
                    int index = this.getModel().getEntryCurrentRowIndex(parent);
                    Date date = this.getEntryObject(parent,index).getDate(name);
                    if (Objects.nonNull(date)) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(date);
                        calendar.add(Calendar.YEAR, Integer.valueOf(valid));
                        this.getEntryObject(parent, index).set(target, calendar.getTime());
                    } else {
                        this.getEntryObject(parent, index).set(target, null);
                    }
                    this.getView().updateView(parent);
                }
            }
        }
	}
	
	/**
	 * 	获取分录根据index
	 * @param entryId 分录标识
	 * @param index 索引
	 * @return DynamicObject
	 */
	public DynamicObject getEntryObject(String entryId,int index) {
		return getModel().getEntryRowEntity(entryId, index);
	}
	
	@Override
	public void afterDoOperation(AfterDoOperationEventArgs afterDoOperationEventArgs) {
		
		super.afterDoOperation(afterDoOperationEventArgs);
		String key = afterDoOperationEventArgs.getOperateKey();
		initBtn();
		if("supplierwithdraw".equals(key)
				|| "purchaserwithdraw".equals(key)
				|| "confirmBill".equals(key)) {
			getView().invokeOperation("refresh");
		}
	}
	
	
	@Override
	public void afterBindData(EventObject e) 
	{
		super.afterBindData(e);
		initBtn();
		 addModelFileAttach();
		  
	}
	
	
	/**
	 * 界面按钮点击触发
	 */
	@Override
	public void itemClick(ItemClickEvent evt) {
		super.itemClick(evt);
		String itemKey=evt.getItemKey();
		
		if (StringUtil.equalsIgnoreCase("yd_supplierwithdraw", itemKey)) {//供应商撤回功能
			supplierWithdraw();
			getView().invokeOperation("refresh");
			this.getView().showMessage("执行完成，请查看处理结果！");
		}else if(StringUtil.equalsIgnoreCase("yd_purchaserwithdraw", itemKey)) {//采购方撤回功能
			purchaserWithdraw();
			getView().invokeOperation("refresh");
			this.getView().showMessage("执行完成，请查看处理结果！");
//			supplierWithdrawTest();
			
		}else if(StringUtil.equalsIgnoreCase("yd_repulse", itemKey)) {//打回功能
			repulse();
		
		}else if(StringUtil.equalsIgnoreCase("yd_confirmbill", itemKey)) {//确认通过功能
			confirmBill();
			getView().invokeOperation("refresh");
			this.getView().showMessage("执行完成，请查看处理结果！");
		}else if(StringUtil.equalsIgnoreCase("yd_createadmittancebill", itemKey)) {//创建原辅料准入单功能
			/*createAdmittancebill();
			getView().invokeOperation("refresh");
			this.getView().showMessage("执行完成，请查看处理结果！");*/
		}

	}
	
	
	@Override
	public void beforeDoOperation(BeforeDoOperationEventArgs args) {
		super.beforeDoOperation(args);
		FormOperate op = (FormOperate)args.getSource();
		String opKey = op.getOperateKey();
		/*if("push".equals(opKey)) {
			String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
			String billstatus = GeneralFormatUtils.getString(this.getModel().getDataEntity().get("billstatus"));
			if(!"C".equals(billstatus)) {
				throw new KDBizException("仅能对已审核资料数据创建原辅料准入单，请核实！");
			} 
		}*/
	}
	
	
	/**
	 * 页面关闭回调事件
	 */
	@Override
	public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
		super.closedCallBack(closedCallBackEvent);
		
		if (StringUtils.equals(closedCallBackEvent.getActionId(), KEY_REPLUE) 
				&& null != closedCallBackEvent.getReturnData()) {//打回回调接口
            
            HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
            
            String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
            
            DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill");
    		billInfo.set("billstatus", "D");
    		billInfo.set("yd_repulsereason", returnData.get("yd_remark"));
    		SaveServiceHelper.save(new DynamicObject[] {billInfo});
    		new SupplyInformationBillMserviceImpl().getInstance().sendRebackSupplyInformateToSupplier(billId);
    		getView().invokeOperation("refresh");
			this.getView().showMessage("执行完成，请查看处理结果！");
        }
	}
	
	private void initBtn() {
//	   System.out.println(this.getModel().getDataEntity().getPkValue().toString()+"-------");//单据id
		
		String billstatus = GeneralFormatUtils.getString(this.getModel().getValue("billstatus")); 
		boolean isBizPartner = new BizPartnerBizHelper().isBizPartnerCurrentUser();//是否为商务伙伴
		
		this.getView().setVisible(isBizPartner, "yd_supplierwithdraw");
		this.getView().setVisible(!isBizPartner, "yd_purchaserwithdraw");
		this.getView().setVisible(!isBizPartner, "yd_createadmittancebill");
		this.getView().setVisible(!isBizPartner, "yd_confirmbill");
		this.getView().setVisible(!isBizPartner, "yd_repulse");
		
	
		//当单据为待补充或者打回状态  如果对应当前登录人为商务伙伴  可进行编辑提交操作
		if("A".equals(billstatus)) {//当状态为待补充资料   允许供应商提交或者采购方撤回操作
			this.getView().setVisible(!isBizPartner, "bar_submit");
			this.getView().setVisible(false , "yd_purchaserwithdraw");
			this.getView().setVisible(false, "yd_createadmittancebill");
			this.getView().setVisible(false, "yd_confirmbill");
			this.getView().setVisible(false, "yd_repulse");
		//	this.getView().setStatus(!isBizPartner ? OperationStatus.VIEW : OperationStatus.EDIT);
		}

		if("B".equals(billstatus)) {//当状态为待补充资料   允许供应商提交或者采购方撤回操作
			this.getView().setVisible(isBizPartner, "bar_submit");
			this.getView().setVisible(!isBizPartner , "yd_purchaserwithdraw");
			this.getView().setVisible(false, "yd_createadmittancebill");
			this.getView().setVisible(false, "yd_confirmbill");
			this.getView().setVisible(false, "yd_repulse");
			this.getView().setVisible(false, "bar_modify");
			
			if(isBizPartner) {
				this.getView().setStatus(OperationStatus.EDIT);
			}
			
		//	this.getView().setStatus(!isBizPartner ? OperationStatus.VIEW : OperationStatus.EDIT);
		}
		if("D".equals(billstatus)) {//当单据状态为打回状态  ，只允许供应商编辑提交或者采购方撤回；
			this.getView().setVisible(isBizPartner, "bar_submit");
			this.getView().setVisible(!isBizPartner, "yd_purchaserwithdraw");
			this.getView().setVisible(false, "yd_createadmittancebill");
			this.getView().setVisible(false, "yd_confirmbill");
			this.getView().setVisible(false, "bar_modify");
			
			if(isBizPartner) {
				this.getView().setStatus(OperationStatus.EDIT);
			}else {
				this.getView().setVisible(false, "bar_modify");
				this.getView().setVisible(false, "bar_del");
				this.getView().setVisible(false, "bar_save");
				this.getView().setVisible(false, "bar_submitandnew");
			}
		//	this.getView().setStatus(!isBizPartner ? OperationStatus.VIEW : OperationStatus.EDIT);
		}
		if("C".equals(billstatus)) {
			this.getView().setVisible(false, "yd_supplierwithdraw");
			this.getView().setVisible(false, "yd_purchaserwithdraw");
			this.getView().setVisible(false, "yd_confirmbill");

			this.getView().setVisible(false, "yd_repulse");
			
			this.getView().setVisible(false, "bar_modify");
			this.getView().setVisible(!isBizPartner, "yd_createadmittancebill");
			this.getView().setVisible(false, "bar_submit");
		//	this.getView().setStatus(OperationStatus.VIEW);
		}
		if("E".equals(billstatus)) {//当单据装填为待审核  不允许编辑，只允许采购方确认或者打回或者供应商撤回
			this.getView().setVisible(false, "bar_submit");
			this.getView().setVisible(isBizPartner, "yd_supplierwithdraw");
			this.getView().setVisible(false, "yd_purchaserwithdraw");
			this.getView().setVisible(!isBizPartner, "yd_confirmbill");
			this.getView().setVisible(!isBizPartner, "yd_repulse");

			this.getView().setVisible(false, "bar_modify");
			this.getView().setVisible(false, "bar_del");
			this.getView().setVisible(false, "bar_save");
			this.getView().setVisible(false, "bar_submitandnew");
		//	this.getView().setStatus(OperationStatus.VIEW);
		}
		
		
	}
	
	/**
	 * 新增单据封装初始化值  add by liefengWu 20220606
	 */
	@Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);
		//新增
		/*DynamicObjectCollection fieldModelColl = this.getModel().getDataEntity().getDynamicObjectCollection("yd_entryfilemodel");
		fieldModelColl.clear();
		//文件模板分录，系统自动生成类别为“供应商书面调查表”和“物料描述表”的两行分录行
		String[] entryAttachType = new String[] {"A","B"};//文档模板附件分录附件分类枚举值
		for(int index = 0; index < entryAttachType.length ; index ++ ) {
			this.getModel().createNewEntryRow("yd_entryfilemodel");
			this.getModel().setValue("yd_attachmenttype",entryAttachType[index],index);
		}	*/
		this.getModel().getDataEntity().set("yd_bizdate", new Date());
		
		
		initBtn();
		addModelFileAttach();
		
		// 根据进入的菜单判断是否为简易流程并赋值字段,yzw
		if (this.getView().getParentView().getFormShowParameter().getCustomParam("beSimple") != null) {
			String beSimple = this.getView().getParentView().getFormShowParameter().getCustomParam("beSimple").toString();
			if (StringUtils.isNotEmpty(beSimple) && "true".equalsIgnoreCase(beSimple)) {
				this.getModel().getDataEntity().set("yd_combofield", "是");
			}
		}
		
		// 设置组织为股份
		this.getModel().getDataEntity().set("org", new BizBillHelper().getObjByBillNumber("bos_org", "000002"));
	}
	
	private void supplierWithdrawTest() {
		String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
		List<Long> userIds = new ArrayList<Long>();
		/*userIds.add( Long.valueOf(RequestContext.get().getUserId()));
		new MessageCenterBizHelper().sendEmail(userIds, "测试", "测试", "操作", Long.valueOf(billId), 
				 "yd_supplyinformationbill", "/ierp/ierp/index.html?formId=yd_supplyinformationbill&pkId="+billId);*/
		
	}
	
	
	/**
	 * 供应商撤回操作 
	 */
	private void supplierWithdraw() {
		String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
		String billstatus = GeneralFormatUtils.getString(this.getModel().getDataEntity().get("billstatus"));
		if(!"E".equals(billstatus)) {
			throw new KDBizException("仅能撤回待审核数据，请核实！");
		}
		DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill");
		billInfo.set("billstatus", "B");
		SaveServiceHelper.save(new DynamicObject[] {billInfo});
		
	}
	
	/**
	 * 采购方撤回功能实现
	 */
	private void purchaserWithdraw() {
		String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
		String billstatus = GeneralFormatUtils.getString(this.getModel().getDataEntity().get("billstatus"));
		if(!"B".equals(billstatus)) {
			throw new KDBizException("仅能撤回待补充资料数据，请核实！");
		}
		DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill");
		billInfo.set("billstatus", "A");
		SaveServiceHelper.save(new DynamicObject[] {billInfo});
	}
	
	/**
	 * 打回单据功能实现
	 */
	private void repulse() {
		String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
		String billstatus = GeneralFormatUtils.getString(this.getModel().getDataEntity().get("billstatus"));
		if(!"E".equals(billstatus)) {
			throw new KDBizException("仅能打回已补充状态资料数据，请核实！");
		}
		 FormShowParameter ShowParameter = new FormShowParameter();
         //设置弹出页面的编码
         ShowParameter.setFormId("yd_remarkinput");
         //设置弹出页面标题
         ShowParameter.setCaption("请输入打回原因");
         //设置页面关闭回调方法
         //CloseCallBack参数：回调插件，回调标识
         ShowParameter.setCloseCallBack(new CloseCallBack(this, KEY_REPLUE));
         //设置弹出页面打开方式，支持模态，新标签等
         ShowParameter.getOpenStyle().setShowType(ShowType.Modal);
         //弹出页面对象赋值给父页面
         this.getView().showForm(ShowParameter);
		
	}
	
	/**
	 * 确认通过功能实现
	 */
	private void confirmBill() {
		String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
		String billstatus = GeneralFormatUtils.getString(this.getModel().getDataEntity().get("billstatus"));
		if(!"E".equals(billstatus)) {
			throw new KDBizException("仅能确认已补充状态资料数据，请核实！");
		}
		DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(billId, "yd_supplyinformationbill");
		billInfo.set("billstatus", "C");
		SaveServiceHelper.save(new DynamicObject[] {billInfo});
	}
	
	/**
	 * 创建原辅料准入单
	 */
	private void createAdmittancebill() {
		String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
		String billstatus = GeneralFormatUtils.getString(this.getModel().getDataEntity().get("billstatus"));
		if(!"C".equals(billstatus)) {
			throw new KDBizException("仅能对已审核资料数据创建原辅料准入单，请核实！");
		}
		//生成原辅料准入单
		
	}
	
	
	@Override
	public void registerListener(EventObject e) {
		super.registerListener(e);
		BasedataEdit supplierF7 = this.getView().getControl("yd_supplier");
		supplierF7.addAfterF7SelectListener(new AfterF7SelectListener() {//新增供应商用户值变更后的时
			public void afterF7Select(AfterF7SelectEvent event) {
				DynamicObject bdUser = null;
				DynamicObject srmSupplierInfo = getModel().getDataEntity().getDynamicObject("yd_supplier");
				if(srmSupplierInfo != null &&srmSupplierInfo.getPkValue() != null) {
					bdUser = new BizPartnerBizHelper().getBdUserBySrmSupplierId(GeneralFormatUtils.getString(srmSupplierInfo.getPkValue()));
				}
				getModel().setValue("yd_supplieruser", bdUser);
			}
		});
		
		BasedataEdit yd_newmatreq = (BasedataEdit) this.getControl("yd_newmatreq");//研发准入单重写打开监听
		yd_newmatreq.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_newmatreqbill", "yd_newmatreqbill"));
		});
	}
	
	/**
	 * 新增文件模板附件 默认赋值  add by liefengWu
	 */
	private void addModelFileAttach() {
		 
		 
		AttachmentEdit attEdit = this.getView().getControl("yd_modelfile"); 
		
		List<Map<String,Object>> attachmentData = new ArrayList<Map<String,Object>>();
 
		DynamicObjectCollection destFileColl =	(DynamicObjectCollection)this.getModel().getValue("yd_modelfile");
		
		if(destFileColl!=null&&destFileColl.size()>0)
		{
			return ;
		}
		
		
		DynamicObjectCollection attCol = new DynamicObjectCollection();
		
		if(attachmentData == null || attachmentData.size() ==0) {//当附件面板为空时，获取对应附件数据到文件模板分录
			attachmentData = new ArrayList<Map<String,Object>>();
			QFilter queryFilter = new QFilter("biztype",QCP.equals,"4")
										.and("billstatus", QCP.equals, "C");//获取文件类型为原辅料准入单并且审核通过的供应商帮助文档
			DynamicObject[] srmHelps = BusinessDataServiceHelper.load("srm_srmhelp", "id,billno,biztype", new QFilter[] {queryFilter});
			if(srmHelps != null && srmHelps.length > 0) {
				for(int index = 0 ; index < srmHelps.length ; index ++) {
					List<Map<String, Object>>  srmHelpAttach = AttachmentServiceHelper.
																		getAttachments("srm_srmhelp", srmHelps[index].getPkValue(), "attachmentpanel");
//					System.out.println(System.currentTimeMillis()+"-------------");
					for(int kIndex = 0 ; kIndex < srmHelpAttach.size() ; kIndex ++ ) {
						Map<String, Object> attachDetail = srmHelpAttach.get(kIndex);
						attachDetail.put("size",GeneralFormatUtils.getLong(attachDetail.get("size")));
						attachmentData.add(attachDetail);
//						System.out.println(attachDetail.get("attPkId"));
					/*	DynamicObject att_bd  = BusinessDataServiceHelper.loadSingle(attachDetail.get("attPkId"), "bos_attachment");
						DynamicObject bd = copyAttachBd(att_bd);//复制附件
						//bd.set("status", "B");
						SaveServiceHelper.save(new DynamicObject[] {bd});//将复制的附件存入系统
						DynamicObject attField = new DynamicObject(destFileColl.getDynamicObjectType());
						System.out.println("----"+att_bd.getDynamicObjectType());
						attField.set("fbasedataid", bd);
					//	attField.set("status", "success");
				//		attField.set("fbasedataid_id", bd.getPkValue());
						attCol.add(attField);*/
					}
					
				}
			}
			/*if(attCol.size() > 0) {
				this.getModel().setValue("yd_modelfile", attCol);
				this.getView().updateView("yd_modelfile");
			}*/
//			System.out.println("------"+System.currentTimeMillis());
//			System.out.println("记录日志时间戳原附件长度:"+attachmentData.size());
		
			
			if(attachmentData != null && attachmentData.size() > 0) {
				int index = 1;
				for(int kIndex = 0 ; kIndex < attachmentData.size() ; kIndex ++) {
					Map<String,Object> attach = attachmentData.get(kIndex);
					String uuidStr = getUid(GeneralFormatUtils.getString(index)).toString();
//					System.out.println("新附件id-----"+uuidStr);
					
					attach.put("uid", uuidStr);
					attach.put("entityNum", getView().getEntityId());
					attach.put("billPkId", String.valueOf(getModel().getValue("id")));
					
					index++;
				}
				
				List<DynamicObject> saveAttachments = attEdit.getAttachmentModel().saveAttachments(attEdit.getModel(), this.getView().getPageId(),
							this.getModel().getDataEntityType().getName(), attachmentData);
//				System.out.println("saveAttachments长度:"+saveAttachments.size());
				
				List<Long> idSet = new ArrayList<>();
				saveAttachments.forEach(save -> idSet.add(save.getLong("id")));
				//给目标附件字段赋值
//				System.out.println("idSet长度:"+idSet.size());
				getModel().setValue("yd_modelfile", idSet.toArray());
			}
			
		}
		

	}
	
	
	/**
	 * 获取附件uid
	 * @return
	 */
	private StringBuffer getUid() {
		StringBuffer uid = new StringBuffer("rc-upload-");
		uid.append((new Date()).getTime());
		uid.append("-");
		int index = (int)(1.0D + Math.random() * 10.0D);
		uid.append(index);
		return uid;
	}
	
	/**
	 * 获取附件uid
	 * @return
	 */
	private StringBuffer getUid(String index) {
		StringBuffer uid = new StringBuffer("rc-upload-");
		uid.append((new Date()).getTime());
		uid.append("-");
		uid.append(index);
		return uid;
	}
	
	
	
	private DynamicObject copyAttachBd(DynamicObject att_bd) {
		
//		System.out.println(att_bd.getDataEntityType().getName());
		DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject(att_bd.getDataEntityType().getName());
		DataEntityPropertyCollection properties = att_bd.getDataEntityType().getProperties();
		for(IDataEntityProperty p1:properties) {
			//id 和 multilanguagetext不用设置 设置了会导致字段的重复
			
//			System.out.println(p1.getName()+"----"+att_bd.get(p1));
			if(!p1.getName().equals("id") &&  !p1.getName().equals("multilanguagetext")) {
				//uid加了索引，因此不能重复    粗略可以使用下面的策略       原本的生成策略没有找到
				if(p1.getName().equals("uid")) {
					newObj.set("uid",DB.genLongId(""));
				}else {
					Object value = att_bd.get(p1);
					newObj.set(p1, value);
				} 
			}
		}
		return newObj;
	}
	
	
}
