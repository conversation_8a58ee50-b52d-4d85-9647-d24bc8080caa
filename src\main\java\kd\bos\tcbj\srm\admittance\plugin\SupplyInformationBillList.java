package kd.bos.tcbj.srm.admittance.plugin;

import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import kd.bos.bill.BillOperationStatus;
import kd.bos.bill.OperationStatus;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.events.BeforeShowBillFormEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

/**
 * 资料补充函列表界面插件
 * @auditor liefengWu
 * @date 2022年6月7日
 * 
 */
public class SupplyInformationBillList extends AbstractListPlugin {
	
	
	
	/**
	 * 添加默认过滤方法
	 */
	@Override
	public void setFilter(SetFilterEvent e) {
		super.setFilter(e);
		List qFilterList = e.getQFilters();
		Set<String> srmSupplierIds = new BizPartnerBizHelper().getCurrentSrmSupplierId();
		if(srmSupplierIds != null && srmSupplierIds.size() > 0) {//如果对应获取当前登录供应商有返回值  证明为供应商类型的商务伙伴登录 ，过滤对应供应商的数据进行查看
			qFilterList.add(new QFilter("yd_supplier", QCP.in , srmSupplierIds)
					);
			qFilterList.add(new QFilter("billstatus", QCP.not_equals, "A"));//过滤保存状态不查看
		}
		
		if (this.getView().getFormShowParameter().getCustomParam("beSimple") != null) {
			String beSimple = this.getView().getFormShowParameter().getCustomParam("beSimple").toString();
			if (StringUtils.isNotEmpty(beSimple) && "true".equalsIgnoreCase(beSimple)) {
				qFilterList.add(new QFilter("yd_combofield", QCP.equals, "是"));//过滤简易流程的单据
			} else {
				qFilterList.add(new QFilter("yd_combofield", QCP.equals, "否"));//过滤简易流程的单据
			}
		}
		
		
	//	System.out.println("-------"+RequestContext.get().getUserId());
	}
	
	
	@Override
	public void beforeShowBill(BeforeShowBillFormEvent e) {
		super.beforeShowBill(e);
		
		String pkId = GeneralFormatUtils.getString(e.getParameter().getPkId());
		boolean isBizPartner = new BizPartnerBizHelper().isBizPartnerCurrentUser();//是否为商务伙伴
		if(!StringUtils.isEmpty(pkId)) {
			DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(pkId, "yd_supplyinformationbill");
			if(billInfo != null) {
				String billstatus = GeneralFormatUtils.getString(billInfo.get("billstatus"));
				OperationStatus viewOpState = null;
				BillOperationStatus billOpState = null;
				if("A".equals(billstatus)) {
				
				}

				if("B".equals(billstatus)) {
					viewOpState = !isBizPartner ? OperationStatus.VIEW : OperationStatus.EDIT;
					billOpState = !isBizPartner ? BillOperationStatus.VIEW : BillOperationStatus.EDIT;
				}
				if("D".equals(billstatus)) {//当单据状态为打回状态  ，只允许供应商编辑提交或者采购方撤回；
					viewOpState = !isBizPartner ? OperationStatus.VIEW : OperationStatus.EDIT;
					billOpState = !isBizPartner ? BillOperationStatus.VIEW : BillOperationStatus.EDIT;
				}
				if("C".equals(billstatus)) {
					viewOpState =  OperationStatus.VIEW;
					billOpState = BillOperationStatus.VIEW;
				}
				if("E".equals(billstatus)) {//当单据装填为待审核  不允许编辑，只允许采购方确认或者打回或者供应商撤回
					viewOpState =  OperationStatus.VIEW;
					billOpState = BillOperationStatus.VIEW;
				}
				/*
				e.getParameter().setStatus(viewOpState);
				e.getParameter().setBillStatus(billOpState);*/
			}
		}
		
  	

	}
	
}
