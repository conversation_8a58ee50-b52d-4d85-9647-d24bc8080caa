package kd.bos.tcbj.srm.admittance.plugin;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.db.DB;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.botp.plugin.AbstractConvertPlugIn;
import kd.bos.entity.botp.plugin.args.AfterConvertEventArgs;
import kd.bos.entity.botp.plugin.args.BeforeBuildRowConditionEventArgs;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.srm.admittance.entity.SupplyInfoEntryFieldEntiy;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.scm.common.enums.SrmSupplierStatusEnum;

/**
 * 资料补充函审批后创建资质审查时携带附件，与创建原辅料准入单携带附件一样
 * @auditor yanzuwei
 * @date 2022年8月11日
 * 
 */
public class SupplyinfToAptitudeExamConvertPlugin extends AbstractConvertPlugIn {
	
	private String srcObjectId = "";
	
	@Override
	public void beforeBuildRowCondition(BeforeBuildRowConditionEventArgs e) {
		super.beforeBuildRowCondition(e);
		Long[] srcIds = e.getSelectedRows().stream().map(ListSelectedRow::getPrimaryKeyValue).toArray(Long[]::new);
		String targetEntityNumber = this.getTgtMainType().getName();
		
		if("srm_aptitudeexam".equals(targetEntityNumber)) {
			for (Long srcId : srcIds) {
				srcObjectId = GeneralFormatUtils.getString(srcId);
			}
		}
	}
	
	@Override
	public void afterConvert(AfterConvertEventArgs e) {
		super.afterConvert(e);
		
		ExtendedDataEntity[] billDataEntitys = e.getTargetExtDataEntitySet().FindByEntityKey("srm_aptitudeexam");
		for(ExtendedDataEntity billDataEntity : billDataEntitys){
	        //附件处理自动带出
			DynamicObject srcInfo = BusinessDataServiceHelper.loadSingle(srcObjectId, "yd_supplyinformationbill");

			Map<String,List<SupplyInfoEntryFieldEntiy>> destAttachFieldMap = new HashMap<String,List<SupplyInfoEntryFieldEntiy>>();//key-value 目标附件字段名-对应获取源单分录字段名详情
			
			List<SupplyInfoEntryFieldEntiy> zzEntryAttachFieldList = new ArrayList<SupplyInfoEntryFieldEntiy>();
			zzEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entrydomestic", "yd_domesattachment", "yd_domesattachmenttype","yd_domeseffectdate", "yd_domesuneffectdate"));
			zzEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entryforeign", "yd_foreigattachment", "yd_foreigattachmenttype","yd_foreigeffectdate", "yd_foreiguneffectdate"));
			zzEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entryagent", "yd_agentattachment", "yd_agentattachmenttype","yd_agenteffectdate", "yd_agentuneffectdate"));
			
			
			List<SupplyInfoEntryFieldEntiy> materialEntryAttachFieldList = new ArrayList<SupplyInfoEntryFieldEntiy>();
			materialEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entryprocessstar", "yd_pstarattachment", "yd_pstarattachmenttype","yd_pstareffectdate", "yd_pstaruneffectdate"));
			materialEntryAttachFieldList.add(new SupplyInfoEntryFieldEntiy("yd_entryprocessfitinto", "yd_pfitienattachment", "yd_pfitienattachmenttype","yd_pfitieneffectdate", "yd_pfitienuneffectdate"));
			
			destAttachFieldMap.put("yd_apt_att2", zzEntryAttachFieldList);
			destAttachFieldMap.put("yd_apt_att1", materialEntryAttachFieldList);

			for(String mainKey : destAttachFieldMap.keySet()) {//处理对应目标附件字段处理逻辑
				List<SupplyInfoEntryFieldEntiy> entryAndAttachFieldList = destAttachFieldMap.get(mainKey);
				
				if(entryAndAttachFieldList == null || entryAndAttachFieldList.size() == 0)
					continue;
				DynamicObjectCollection attCol = new DynamicObjectCollection();
				
				DynamicObjectCollection mainKeyColl =	(DynamicObjectCollection)billDataEntity.getValue(mainKey);
				if(mainKeyColl == null)
					continue;
				for(SupplyInfoEntryFieldEntiy filedEntity : entryAndAttachFieldList) {
					if(filedEntity == null)
						continue;
					String key = filedEntity.getEntryEntityName();
					
					DynamicObjectCollection fileEntryColl = srcInfo.getDynamicObjectCollection(key);
					if(fileEntryColl != null && fileEntryColl.size() > 0) {
						for(int index = 0 ; index < fileEntryColl.size(); index ++) {
							DynamicObject fileEntryInfo = fileEntryColl.get(index);
							DynamicObjectCollection fileAttachColl = fileEntryInfo.getDynamicObjectCollection(filedEntity.getFileField());//获取对应分录附件字段 多选基础资料数据
							if(fileAttachColl == null || fileAttachColl.size() == 0)
								continue;

							for(int jIndex = 0 ; jIndex < fileAttachColl.size() ; jIndex ++) {
								DynamicObject att_bd = fileAttachColl.get(jIndex);//原附件
								DynamicObject bd = att_bd.getDynamicObject("fbasedataid");//复制附件

								//创建附件字段信息  (对应的是附件字段的表结构)
								DynamicObject attField = new DynamicObject(mainKeyColl.getDynamicObjectType());
								attField.set("fbasedataid", bd);
								attCol.add(attField);
							}
						}
					}
				}
				
				billDataEntity.setValue(mainKey,attCol);
			}
			
			
			DynamicObject supplierObj = srcInfo.getDynamicObject("yd_supplier");
			if(supplierObj != null && supplierObj.getPkValue() != null) {
				supplierObj = BusinessDataServiceHelper.loadSingle(supplierObj.getPkValue(), "srm_supplier");
				
				Long groupId = Long.valueOf(supplierObj.getLong("group_id"));
				String auditstatus = supplierObj.getString("auditstatus");
				if (groupId != null && groupId.longValue() != 0L
						&& (SrmSupplierStatusEnum.SUCCESS.getValue().equals(auditstatus)
								|| SrmSupplierStatusEnum.APTITUDE.getValue().equals(auditstatus)
								|| SrmSupplierStatusEnum.UNAPROVE.getValue().equals(auditstatus))) {
					
					billDataEntity.getDataEntity().set("group", supplierObj.get("group"));
				}	
			}
	    }
	}
	
	private DynamicObject copyAttachBd(DynamicObject att_bd) {
		
		System.out.println(att_bd.getDataEntityType().getName());
		DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject(att_bd.getDataEntityType().getName());
		DataEntityPropertyCollection properties = att_bd.getDataEntityType().getProperties();
		for(IDataEntityProperty p1:properties) {
			//id 和 multilanguagetext不用设置 设置了会导致字段的重复
			
			System.out.println(p1.getName()+"----"+att_bd.get(p1));
			if(!p1.getName().equals("id") &&  !p1.getName().equals("multilanguagetext")) {
				//uid加了索引，因此不能重复    粗略可以使用下面的策略       原本的生成策略没有找到
				if(p1.getName().equals("uid")) {
					newObj.set("uid",DB.genLongId(""));
				}else {
					Object value = att_bd.get(p1);
					newObj.set(p1, value);
				} 
			}
		}
		return newObj;
	}
}
