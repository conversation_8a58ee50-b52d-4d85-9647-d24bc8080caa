package kd.bos.tcbj.srm.admittance.plugin;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.EventObject;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.IOUtils;

import com.google.gson.Gson;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.control.Control;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.field.TextEdit;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.PackmatApplyHelper;
import kd.bos.tcbj.srm.admittance.helper.SrmSupplierAttHelper;
import kd.bos.tcbj.srm.admittance.utils.CommonUtils;
import kd.bos.tcbj.srm.admittance.utils.ImportBillUtils;
import kd.bos.tcbj.srm.admittance.utils.MetasUtils;
import kd.bos.tcbj.srm.admittance.utils.MetasUtils.Prop; 

/**
 * @auditor yanzuwei
 * @date 2022年6月28日
 * 
 */
public class TestEasFacadePlugin extends AbstractFormPlugin
{

	
	@Override
	public void registerListener(EventObject e) 
	{
		
		super.registerListener(e);
		this.addClickListeners("yd_btntest");
	}
	
	@Override
	public void itemClick(ItemClickEvent evt) 
	{ 
		super.itemClick(evt);
	}
	
	@Override
	public void click(EventObject evt)
	{ 
		super.click(evt);
		
		//SrmSupplierAttHelper.copyAtt4SupInfoBill("1488388844767167488");
		//System.out.println(CommonUtils.getDBSettingsFromZK("ierp"));
		MetasUtils.listProps("yd_supplyinformationbill");
		
	}
	
}
