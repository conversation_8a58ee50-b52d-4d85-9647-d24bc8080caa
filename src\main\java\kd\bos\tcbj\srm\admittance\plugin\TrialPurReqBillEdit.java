package kd.bos.tcbj.srm.admittance.plugin;

import java.util.EventObject;

import org.apache.commons.lang3.StringUtils;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.form.field.BasedataEdit;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.scm.common.util.BaseDataViewDetailUtil;

/**
 * 试产采购申请单  编辑自定义插件
 * @auditor liefengWu
 * @date 2022年6月23日
 * 
 */
public class TrialPurReqBillEdit extends AbstractBillPlugIn {
	 /**
	  * 增加原辅料准入单打开监听  add by liefengWu 20220623
	  */
	 @Override
	public void registerListener(EventObject e) {
		super.registerListener(e);
		BasedataEdit yd_supaccess = (BasedataEdit) this.getControl("yd_rawmatsupaccess");//原辅料准入单重写打开监听
		yd_supaccess.addBeforeF7ViewDetailListener((beforeF7ViewDetailEvent) -> {
			beforeF7ViewDetailEvent.setCancel(true);
			this.getView().showForm(BaseDataViewDetailUtil.buildShowParam(beforeF7ViewDetailEvent.getPkId(),
					"yd_rawmatsupaccessbill", "yd_rawmatsupaccessbill"));
		});
	}
	 
	 
	 public void propertyChanged(PropertyChangedArgs e){
		  super.propertyChanged(e); 
		  String fieldKey = e.getProperty().getName();
	      if (StringUtils.equals("yd_rawmatsupaccess",fieldKey))//准入单字段值变更事件
	      {
	    	  DynamicObject value =(DynamicObject)e.getChangeSet()[0].getNewValue();
	          if(value!=null)
	          {
	        	  String billId = GeneralFormatUtils.getString(value.getPkValue());
	        	  DynamicObject info = BusinessDataServiceHelper.loadSingle(billId, "yd_rawmatsupaccessbill");
	        	  if(info != null ) {
	        		  //供应商数据赋值
	        		  this.getModel().setValue("yd_supplier", info.getDynamicObject("yd_supplier"));  
	        		  //获取物料数据
	        		  DynamicObject materialInfo = info.getDynamicObject("yd_material");
	        		  if(materialInfo != null && materialInfo.getPkValue() != null) {
	        			  String materialId = GeneralFormatUtils.getString(materialInfo.getPkValue());
	        			  DynamicObjectCollection entryColl = this.getView().getModel().getEntryEntity("entryentity");
	        			  boolean isHaveMaterialsupaccess = false;
	        			  if(entryColl != null) {
	        				  for(int index = 0 ;  index < entryColl.size() ; index ++) {
		        				  DynamicObject entryInfo = entryColl.get(index);
		        				  if(entryInfo == null 
		        						  || entryInfo.getDynamicObject("yd_entrymaterial") == null
		        						  || entryInfo.getDynamicObject("yd_entrymaterial").getPkValue() == null
		        						  || isHaveMaterialsupaccess) {
		        					  continue;
		        				  }
		        				  isHaveMaterialsupaccess = materialId.equals(GeneralFormatUtils.getString(entryInfo.getDynamicObject("yd_entrymaterial").getPkValue()));
		        			  }
	        			  }
	        			  if(!isHaveMaterialsupaccess) {
	        				  this.getModel().createNewEntryRow("entryentity");
	        				  this.getModel().setValue("yd_entrymaterial",materialInfo,entryColl.size());
	        			  }
	        		  }
	        	  }
	        	 
	          } 
	      }
	}
	 
	 
	 @Override
	public void afterCreateNewData(EventObject e) {
		super.afterCreateNewData(e);
		long userId = UserServiceHelper.getCurrentUserId();
		DynamicObject currentUser = BusinessDataServiceHelper.loadSingle(userId, "bos_user");
		Long deptId = UserServiceHelper.getUserMainOrgId(userId);
		
		this.getModel().setValue("yd_org", deptId);
		this.getModel().setValue("yd_applier", currentUser);
		
		
	}
	 
	 
}
