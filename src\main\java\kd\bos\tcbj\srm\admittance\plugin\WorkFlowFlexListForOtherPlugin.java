package kd.bos.tcbj.srm.admittance.plugin;

import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.events.PackageDataEvent;
import kd.bos.entity.list.column.ColumnDesc;
import kd.bos.filter.CommonFilterColumn;
import kd.bos.form.events.BeforeCreateListDataProviderArgs;
import kd.bos.form.events.HyperLinkClickArgs;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.BillList;
import kd.bos.list.ListShowParameter;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.mvc.list.ListDataProvider;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.workflow.WorkflowServiceHelper;
import kd.bos.workflow.api.BizProcessStatus;
import kd.fi.er.formplugin.daily.web.workflow.ErWorkFlowFlexListForOtherPlugin;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.WorkFlowFlexListForOtherPlugin
 * @className WorkFlowFlexListForOtherPlugin
 * @author: hst
 * @createDate: 2023/08/14
 * @description: 当前审批节点及处理人
 * @version: v1.0
 */
public class WorkFlowFlexListForOtherPlugin extends AbstractListPlugin {
    private static Log log = LogFactory.getLog(ErWorkFlowFlexListForOtherPlugin.class);
    private Map<String, String> auditorsMap = new HashMap();

    @Override
    public void billListHyperLinkClick(HyperLinkClickArgs args) {
        super.billListHyperLinkClick(args);
        HyperLinkClickEvent linkClick = args.getHyperLinkClickEvent();
        BillList billList = (BillList)linkClick.getSource();
        ListSelectedRow selectedRow = billList.getCurrentSelectedRowInfo();
        if (selectedRow != null) {
            if ("yd_nextauditor".equals(linkClick.getFieldName())) {
                args.setCancel(true);
                ListShowParameter listShow = (ListShowParameter)this.getView().getFormShowParameter();
                String pageId = listShow.getPageId();
                WorkflowServiceHelper.viewFlowchart(pageId, selectedRow.getPrimaryKeyValue());
            }

        }
    }

    @Override
    public void beforeCreateListDataProvider(BeforeCreateListDataProviderArgs args) {
        super.beforeCreateListDataProvider(args);
        args.setListDataProvider(new ListDataProvider() {
            public DynamicObjectCollection getData(int arg0, int arg1) {
                long t1 = System.currentTimeMillis();
                DynamicObjectCollection collection = super.getData(arg0, arg1);
                List<String> pkIds = new ArrayList();
                collection.forEach((v) -> {
                    pkIds.add(String.valueOf(v.getPkValue()));
                });
                if (!pkIds.isEmpty()) {
                    WorkFlowFlexListForOtherPlugin.this.getNextAuditors(pkIds);
                }

                long t2 = System.currentTimeMillis();
                WorkFlowFlexListForOtherPlugin.log.info("审核人数据加载B 用时： " + (t2 - t1) + "ms");
                return collection;
            }
        });
    }

    @Override
    public void packageData(PackageDataEvent e) {
        super.packageData(e);
        if (e.getSource() instanceof ColumnDesc) {
            ColumnDesc columnDesc = (ColumnDesc)e.getSource();
            if ("yd_nextauditor".equals(columnDesc.getKey()) && this.auditorsMap != null && this.auditorsMap.size() > 0) {
                String auditor = (String)this.auditorsMap.get(e.getRowData().getPkValue().toString());
                e.setFormatValue(auditor);
            }
        }

    }

    private void getNextAuditors(List<String> pkIds) {
        this.auditorsMap = new HashMap(pkIds.size());
        String[] ids = new String[pkIds.size()];
        pkIds.toArray(ids);
        Map<String, List<BizProcessStatus>> allPro = WorkflowServiceHelper.getBizProcessStatus(ids);

        Map.Entry entry;
        StringBuilder msg;
        for(Iterator var4 = allPro.entrySet().iterator(); var4.hasNext(); this.auditorsMap.put(entry.getKey().toString(), msg.toString())) {
            entry = (Map.Entry)var4.next();
            List<BizProcessStatus> node = (List)entry.getValue();
            int count = 0;
            String nodeName = "";
            StringBuilder participantName = new StringBuilder();
            msg = new StringBuilder();

            try {
                Iterator var11 = node.iterator();

                while(var11.hasNext()) {
                    BizProcessStatus e = (BizProcessStatus)var11.next();
                    nodeName = e.getCurrentNodeName();
                    String auditor = e.getParticipantName();
                    e.getProcessStatus();
                    if (auditor != null && !"".equals(auditor.trim())) {
                        participantName.append(",");
                        participantName.append(auditor);
                        ++count;
                        if (count == 3) {
                            participantName.append("...");
                            break;
                        }
                    }
                }

                if (!StringUtils.isBlank(nodeName)) {
                    msg.append(nodeName);
                    if (!StringUtils.isBlank(participantName)) {
                        msg.append('/');
                        msg.append(participantName.toString().replaceFirst(",", ""));
                    }
                }
            } catch (Exception var14) {
                log.info("WorkFlowFlexListForOtherPlugin>>>获取审批人出现异常: " + var14.getMessage());
            }
        }

    }
}
