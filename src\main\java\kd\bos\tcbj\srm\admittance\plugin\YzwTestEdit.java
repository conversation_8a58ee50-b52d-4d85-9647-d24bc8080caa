package kd.bos.tcbj.srm.admittance.plugin;

import java.util.List;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.BeforeClosedEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.servicehelper.workflow.WorkflowServiceHelper;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordGroup;

/**
 * @auditor yanzuwei
 * @date 2022年7月5日
 * 
 */
public class YzwTestEdit extends AbstractBillPlugIn {
	@Override
	public void beforeClosed(BeforeClosedEvent e) {
		super.beforeClosed(e);
		e.setCheckDataChange(false);
	}
	
	@Override
	public void itemClick(ItemClickEvent evt) {
		super.itemClick(evt);
		
		String itemKey=evt.getItemKey();
		
		if (StringUtil.equalsIgnoreCase("yd_testviewaudit", itemKey)) {//查看审批记录
			String billId = this.getModel().getDataEntity().getPkValue().toString();//单据id
			// 弹出选择查询参数的动态表单页面
			FormShowParameter showParameter = new FormShowParameter();
			showParameter.setFormId("yd_auditresultlist");
			showParameter.setCloseCallBack(new CloseCallBack(this, "yd_auditresultlist"));
			showParameter.getOpenStyle().setShowType(ShowType.Modal);
			showParameter.setCustomParam("billId", billId);
			this.getView().showForm(showParameter);
			
//			List<IApprovalRecordGroup> allApprovalRecord = WorkflowServiceHelper.getAllApprovalRecord(billId);
//			if (allApprovalRecord.size() > 0) {
//				for (IApprovalRecordGroup iApprovalRecordGroup : allApprovalRecord) {
//					iApprovalRecordGroup.getChildren();
//					System.out.println(222);
//				}
//				System.out.println(111);
//			}
		}
	}
	
	@Override
	public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
		super.closedCallBack(closedCallBackEvent);
	}
	
}
