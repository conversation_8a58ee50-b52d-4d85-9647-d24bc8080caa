package kd.bos.tcbj.srm.admittance.plugin.print;

import kd.bos.algo.Algo;
import kd.bos.algo.DataSet;
import kd.bos.algo.DataType;
import kd.bos.algo.Row;
import kd.bos.algo.RowMeta;
import kd.bos.algo.RowMetaFactory;
import kd.bos.algo.input.CollectionInput;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.OrmLocaleValue;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.print.core.data.DataRowSet;
import kd.bos.print.core.data.datasource.CustomDataSource;
import kd.bos.print.core.data.field.TextField;
import kd.bos.print.core.plugin.AbstractPrintPlugin;
import kd.bos.print.core.plugin.event.CustomDataLoadEvent;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.admittance.plugin.AuditResultListPlugin;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordGroup;
import kd.bos.workflow.component.approvalrecord.IApprovalRecordItem;
import kd.bos.workflow.design.util.DesignerPluginUtil;
import kd.bos.workflow.engine.WfConstanst;
import kd.bos.workflow.engine.impl.flowchart.BaseTaskHandleRecord;
import kd.bos.workflow.engine.impl.flowchart.TaskHandleRecord;
import kd.bos.workflow.engine.impl.persistence.entity.task.TaskHandleLogEntity;
import kd.bos.workflow.engine.impl.persistence.entity.task.component.ApprovalRecordGroup;
import kd.bos.workflow.service.WorkflowService;
import kd.bos.workflow.service.impl.ServiceFactory;
import kd.bos.workflow.service.impl.WorkflowServiceImpl;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class AuditResultPrintPlugin extends AbstractPrintPlugin {

    /* 审批记录数据源标识 */
    private final static String WF_RECORD = "yd_wfrecord";
    private final static Log log = LogFactory.getLog(AuditResultPrintPlugin.class);
    private Date preDate = null;
    private String[] fields = new String[]{"yd_nodename", "yd_handled", "yd_apresults",
            "yd_apopinion", "yd_apdate", "yd_aphour", "yd_throughrule"};
    private DataType[] dataTypes = new DataType[]{DataType.StringType, DataType.StringType, DataType.StringType,
            DataType.StringType, DataType.DateType, DataType.StringType, DataType.StringType};

    @Override
    public void loadCustomData(CustomDataLoadEvent evt) {
        super.loadCustomData(evt);
        Object billId = evt.getDataSource().getPkId();
        CustomDataSource dataSource = evt.getDataSource();
        List<DataRowSet> customDataRow = evt.getCustomDataRows();
        if (WF_RECORD.equals(dataSource.getDsName())) {
            LinkedList<DataRowSet> records = this.getAuditResultRecord(billId);
            for (DataRowSet record : records) {
                customDataRow.add(record);
            }
        }
    }

    /**
     * 获取审批记录
     *
     * @param billId
     * @author: hongsitao
     * @createDate: 2024/11/07
     */
    private LinkedList<DataRowSet> getAuditResultRecord(Object billId) {
        LinkedList<DataRowSet> records = new LinkedList<>();
        try {
            List<IApprovalRecordGroup> approvalRecordGroupList = new WorkflowServiceImpl()
                    .getAllApprovalRecordInclCoordinate(String.valueOf(billId));

            for (IApprovalRecordGroup approvalRecord : approvalRecordGroupList) {
                List<IApprovalRecordItem> childrens = approvalRecord.getChildren();
                String groupType = ((ApprovalRecordGroup) approvalRecord).getGroupType();
                String activityName = approvalRecord.getActivityName();
                String throughRule = approvalRecord.getThroughRule();
                String addSignMsg = approvalRecord.getAddSignMsg();
                if (StringUtils.isNotBlank(groupType) || StringUtils.isNotBlank(activityName)
                        || StringUtils.isNotBlank(throughRule)) {
                    this.countersigningNodeProcessing(records, childrens, activityName,throughRule);
                } else {
                    this.commonNodeProcessing(records, childrens, addSignMsg);
                }
            }
        } catch (Exception exceptinon) {
            exceptinon.printStackTrace();
            if (log.isErrorEnabled()) {
                log.error("kd.bos.tcbj.srm.admittance.plugin.print.AuditResultPrintPlugin.getAuditResultRecord —— "
                        + exceptinon.getMessage());
            }
        }

        return records;
    }

    /**
     * 会签节点处理
     * @author: hst
     * @createDate: 2024/11/07
     */
    public void countersigningNodeProcessing(LinkedList<DataRowSet> records, List<IApprovalRecordItem> childrens,
                                             String activityName, String throughRule) throws ParseException {
        Date auditDate = null;
        boolean isCoordinate = false;
        for (IApprovalRecordItem children : childrens) {
            if (isCoordinate && !children.isCoordinate() && Objects.nonNull(children.getOwnerId())) {
                // update by hst 2023/11/27 二开记录表是否有保存
                boolean isExite = QueryServiceHelper.exists("yd_coordinaterecord",
                        new QFilter[]{QFilter.of("yd_ownerid = ? and yd_userid = ? and yd_taskid = ? " +
                                        "and yd_createtime = ? and yd_way != ''", children.getOwnerId().toString(),
                                children.getUserId().toString(),children.getTaskId(),children.getTime())});
                if (Objects.isNull(children.getTime())
                        ||(Objects.nonNull(children.getTime()) && !isExite)) {
                    // 转交路径
                    Long taskId = getHandoverLink(Long.valueOf(children.getTaskId()), children.getOwnerId(),
                            children.getUserId());
                    // 是否已多次转交
                    if (Objects.nonNull(taskId)) {
                        this.multipleReferralsForProcessing(taskId, records, children);
                    } else {
                        this.singleReferralsForProcessing(records, children);
                    }
                }
            } else {
                LinkedList<DataRowSet> corrdinateRecords = new LinkedList<>();
                if (children.isCoordinate()) {
                    isCoordinate = true;
                    /* 加载已完成的协办 */
                    corrdinateRecords = initAlreadyCompleteCoordinateRecord(children);
                }

                /* 加载任务转交记录 */
                LinkedList<DataRowSet> handOverRecords = this.loadingTaskHandoverRecords(children);
                if (handOverRecords.size() > 0) {
                    corrdinateRecords.addAll(handOverRecords);
                }

                DataRowSet temp = new DataRowSet();
                temp.put("yd_handled", new TextField(children.getAssignee()));
                temp.put("yd_apresults", new TextField(children.getResult()));
                temp.put("yd_apopinion", new TextField("\n"
                        + (Objects.nonNull(children.getMessage()) ? children.getMessage().toString() : "")
                        + "\n"));
                temp.put("yd_apdate", new TextField(children.getTime()));
                temp.put("yd_throughrule", new TextField(throughRule));
                temp.put("yd_nodename", new TextField(children.getActivityName()));
                /* 处理时长计算 */
                if (StringUtils.isNotBlank(children.getMessage())) {
                    if (Objects.isNull(preDate)) {
                        preDate = DateUtil.parseDate(children.getTime());
                    } else {
                        Date endDate = DateUtil.parseDate(children.getTime());
                        String hour = DateUtil.getDifferHour(preDate, endDate);
                        preDate = endDate;
                        temp.put("yd_aphour", new TextField(hour + "小时"));
                    }
                }
                if (handOverRecords.size() == 0) {
                    records.add(temp);
                    records.addAll(corrdinateRecords.stream()
                            .sorted(Comparator.comparing((DataRowSet row) ->
                                    row.getField("yd_apdate").getValue().toString()))
                            .collect(Collectors.toList()));
                } else {
                    records.addAll(corrdinateRecords.stream()
                            .sorted(Comparator.comparing((DataRowSet row) ->
                                    row.getField("yd_apdate").getValue().toString()))
                            .collect(Collectors.toList()));
                    records.add(temp);
                }
            }
        }
        /* 记录最晚的会签审批时间 */
        preDate = Objects.nonNull(auditDate) ? auditDate : preDate;
    }

    /**
     * 普通节点处理
     * @param records
     * @param childrens
     * @param addSignMsg
     * @author: hst
     * @createDate: 2022/12/23
     */
    public void commonNodeProcessing (LinkedList<DataRowSet> records, List<IApprovalRecordItem> childrens,
                                      String addSignMsg) throws ParseException {
        boolean isCoordinate = false;
        for (IApprovalRecordItem children : childrens) {
            if (isCoordinate && !children.isCoordinate()) {
                /* 二开记录表是否有保存 */
                boolean isExite = QueryServiceHelper.exists("yd_coordinaterecord",
                        new QFilter[]{QFilter.of("yd_ownerid = ? and yd_userid = ? and yd_taskid = ? " +
                                        "and yd_createtime = ? and yd_way != ''", children.getOwnerId().toString(),
                                children.getUserId().toString(),children.getTaskId(),children.getTime())});
                if (Objects.isNull(children.getTime())
                        ||(Objects.nonNull(children.getTime()) && !isExite)) {
                    // 转交路径
                    Long taskId = getHandoverLink(Long.valueOf(children.getTaskId()), children.getOwnerId(),
                            children.getUserId());
                    // 是否已多次转交
                    if (Objects.nonNull(taskId)) {
                        this.multipleReferralsForProcessing(taskId, records, children);
                    } else {
                        this.singleReferralsForProcessing(records, children);
                    }
                }
            } else {
                LinkedList<DataRowSet> corrdinateRecords = new LinkedList<>();
                if (children.isCoordinate()) {
                    isCoordinate = true;
                    /* 加载已完成的协办 */
                    corrdinateRecords = initAlreadyCompleteCoordinateRecord(children);
                }

                /* 加载任务转交记录 */
                LinkedList<DataRowSet> handOverRecords = this.loadingTaskHandoverRecords(children);
                if (handOverRecords.size() > 0) {
                    corrdinateRecords.addAll(handOverRecords);
                }

                DataRowSet temp = new DataRowSet();
                temp.put("yd_handled", new TextField(children.getAssignee()));
                temp.put("yd_apresults", new TextField(children.getResult()));
                temp.put("yd_apopinion", new TextField("\n"
                        + (StringUtils.isNotBlank(addSignMsg) ? addSignMsg + "\n\n" : "")
                        + (Objects.nonNull(children.getMessage()) ? children.getMessage().toString() : "")
                        + "\n"));
                temp.put("yd_apdate", new TextField(children.getTime()));
                temp.put("yd_nodename", new TextField(children.getActivityName()));
                /* 处理时长计算 */
                if (StringUtils.isNotBlank(children.getMessage())) {
                    if (Objects.isNull(preDate)) {
                        preDate = DateUtil.parseDate(children.getTime());
                    } else {
                        Date endDate = DateUtil.parseDate(children.getTime());
                        String hour = DateUtil.getDifferHour(preDate, endDate);
                        preDate = endDate;
                        temp.put("yd_aphour", new TextField(hour + "小时"));
                    }
                }
                if (handOverRecords.size() == 0) {
                    records.add(temp);
                    records.addAll(corrdinateRecords.stream()
                            .sorted(Comparator.comparing((DataRowSet row) ->
                                    row.getField("yd_apdate").getValue().toString()))
                            .collect(Collectors.toList()));
                } else {
                    records.addAll(corrdinateRecords.stream()
                            .sorted(Comparator.comparing((DataRowSet row) ->
                                    row.getField("yd_apdate").getValue().toString()))
                            .collect(Collectors.toList()));
                    records.add(temp);
                }
            }
        }
    }

    /**
     * 加载任务转交记录
     * @param children
     * @author: hst
     * @createDate: 2024/11/07
     */
    private LinkedList<DataRowSet> loadingTaskHandoverRecords (IApprovalRecordItem children) {
        List<TaskHandleLogEntity> taskHandleLogs = DesignerPluginUtil.getTransferRecordsData(children.getTaskId(), children.getUserId().toString(),
                false, ((WorkflowService) ServiceFactory.getService(WorkflowService.class)).getTaskService());
        LinkedList<DataRowSet> handOverRecords = new LinkedList<>();

        for (int i = taskHandleLogs.size() - 1; i >= 0; i--) {
            DataRowSet temp = new DataRowSet();
            TaskHandleLogEntity taskHandleLogEntity = taskHandleLogs.get(i);
            DynamicObject record = taskHandleLogEntity.getDynamicObject();
            temp.put("yd_nodename", new TextField(children.getActivityName()));
            temp.put("yd_handled",new TextField(record.getLocaleString("owner").getLocaleValue()));
            temp.put("yd_apresults",new TextField("任务转交"));
            temp.put("yd_apdate",new TextField(DateUtil.date2str(record.getDate("createdate"),
                    "yyyy-MM-dd HH:mm:ss")));
            temp.put("yd_apopinion", new TextField("\n" + record.getLocaleString("owner").getLocaleValue()
                    + " 转交 " + record.getLocaleString("assignee").getLocaleValue() + " :\n"
                    + (Objects.nonNull(record.getLocaleString("opinion")) ?
                    record.getLocaleString("opinion").getLocaleValue() : "") + "\n"));

            handOverRecords.add(temp);
        }

        return handOverRecords;
    }

    /**
     * 多次转交处理
     * @param taskId
     * @param recordList
     * @param children
     * @author: hst
     * @createDate: 2024/11/07
     */
    private void multipleReferralsForProcessing(Long taskId, LinkedList<DataRowSet> recordList,
                                                IApprovalRecordItem children) {
        /* 初始化多次转交记录 */
        this.initMultipleTransferRecords(recordList, children);

        DataRowSet temp = new DataRowSet();
        temp.put("yd_handled", new TextField(children.getAssignee()));
        QFilter filter = new QFilter("id", QFilter.equals, taskId);
        DynamicObject handleLog = BusinessDataServiceHelper.loadSingle("wf_taskhandlelog",
                "ownerformat,assigneeformat,createdate,opinion,taskid,ownerid", new QFilter[]{filter});
        if (Objects.nonNull(handleLog)) {
            // 原处理人
            OrmLocaleValue owner = (OrmLocaleValue) handleLog.get("ownerformat");
            // 现处理人
            OrmLocaleValue assignee = (OrmLocaleValue) handleLog.get("assigneeformat");
            // 处理意见
            OrmLocaleValue opinion = (OrmLocaleValue) handleLog.get("opinion");
            temp.put("yd_handled", new TextField(owner.getLocaleValue()));
            temp.put("yd_apresults", new TextField("邀请协办"));
            temp.put("yd_apopinion", new TextField("\n" + owner.getLocaleValue() + " 邀请 " + assignee.getLocaleValue()
                    + " 协办: \n" + opinion.getLocaleValue() + "\n"));
            temp.put("yd_apdate", new TextField(DateUtil.date2str(handleLog.getDate("createdate"),
                    "yyyy-MM-dd HH:mm:ss")));
        }
        recordList.add(temp);

        temp = new DataRowSet();
        temp.put("yd_handled", new TextField(children.getAssignee().length() > 5
                ? children.getAssignee().substring(0,children.getAssignee().length() - 5)
                : children.getAssignee()));
        if (Objects.nonNull(children.getTime())) {
            /* 将任务完成的时间节点及状态置于最后一个子节点 */
            temp.put("yd_apresults", new TextField("协办完成"));
            temp.put("yd_apdate", new TextField(children.getTime()));
            temp.put("yd_apopinion", new TextField("\n" +children.getAssignee()
                    + " 回复：\n" + (Objects.nonNull(children.getMessage()) ?
                    children.getMessage() : "") + "\n"));

        } else {
            temp.put("yd_apresults", new TextField("正在协办"));
        }
        recordList.add(temp);
    }

    /**
     * 单次协办处理
     * @param children
     * @param recordList
     * @author: hst
     * @createDate: 2024/11/07
     */
    private void singleReferralsForProcessing(LinkedList<DataRowSet> recordList, IApprovalRecordItem children) {
        DataRowSet temp = new DataRowSet();
        QFilter filter = QFilter.of("taskid = ? and ownerid = ? and assigneeid = ?"
                , children.getTaskId(), children.getOwnerId(),children.getUserId());
        DynamicObject handleLog = BusinessDataServiceHelper.loadSingle("wf_taskhandlelog",
                "ownerformat,assigneeformat,createdate,opinion,taskid,ownerid", new QFilter[]{filter});
        temp.put("yd_handled", new TextField(children.getOwnerName()));
        temp.put("yd_apresults", new TextField("发起协办"));

        if (Objects.nonNull(handleLog)) {
            temp.put("yd_apopinion", new TextField("\n" + children.getOwnerName() + " 邀请 "
                    + UserServiceHelper.getUserInfoByID(children.getUserId()).get("name") + " 协办:\n"
                    + handleLog.getString("opinion") + "\n"));
            temp.put("yd_apdate", new TextField(DateUtil.date2str(handleLog.getDate("createdate"),
                    "yyyy-MM-dd HH:mm:ss")));
        }
        recordList.add(temp);

        /* 协办回复 */
        temp = new DataRowSet();
        temp.put("yd_handled", new TextField(children.getAssignee()));
        if (Objects.nonNull(children.getTime())) {
            temp.put("yd_apopinion", new TextField("\n" + children.getAssignee()
                    + " 回复：\n" + (Objects.nonNull(children.getMessage()) ?
                    children.getMessage() : "") + "\n"));
            temp.put("yd_apdate", new TextField(children.getTime()));
            temp.put("yd_apresults", new TextField("协办完成"));
        } else {
            temp.put("yd_apresults", new TextField("正在协办"));
        }
        recordList.add(temp);
    }

    /**
     * 加载已完成的协办记录（二开）
     * @param children
     * @author: hst
     * @createDate: 2023/11/07
     * @return
     */
    private LinkedList<DataRowSet> initAlreadyCompleteCoordinateRecord (IApprovalRecordItem children) {
        String taksId = String.valueOf(children.getTaskId());
        QFilter qFilter = QFilter.of("yd_taskid = ?", taksId);

        DynamicObjectCollection records = QueryServiceHelper.query("yd_coordinaterecord"
                , "id,yd_type,yd_taskid,yd_ownerid,yd_userid,yd_content," +
                        "yd_createtime,yd_way,yd_ownerformat,yd_userformat,yd_businessid,yd_attachmentid"
                , new QFilter[]{qFilter}, "yd_createtime asc");

        // 查询所有路径
        List<String> wayList = new ArrayList<>();
        for (DynamicObject record : records) {
            String way = record.getString("yd_way");
            if (StringUtils.isNotBlank(way)) {
                wayList.add(way);
            }
        }

        // 按每条路径进行处理
        LinkedList<DataRowSet> recordList = new LinkedList<>();
        int sonRow = -1;
        for (String way : wayList) {
            String[] ways = way.split(",");
            if (ways.length > 0) {
                for (String wayId : ways) {
                    for (DynamicObject record : records) {
                        String id = record.getString("id");
                        if (StringUtils.isNotBlank(id) && id.equals(wayId)) {
                            DataRowSet temp = new DataRowSet();
                            // 发起协办或协办转交，取owner
                            String type = record.getString("yd_type");

                            if ("1".equals(type) || "2".equals(type)) {
                                temp.put("yd_handled", new TextField(record.getString("yd_ownerformat")));
                                temp.put("yd_apresults", new TextField("1".equals(type) ? "发起协办" : "协办转交"));
                                temp.put("yd_apopinion", new TextField("\n" + record.getString("yd_ownerformat")
                                        + ("1".equals(type) ? " 邀请 " : " 转交 ") + record.getString("yd_userformat") + "：\n" +
                                        record.getString("yd_content") + "\n"));
                            } else {
                                temp.put("yd_handled", new TextField(record.getString("yd_userformat")));
                                temp.put("yd_apresults", new TextField("协办回复"));
                                temp.put("yd_apopinion", new TextField("\n" + record.getString("yd_userformat") +
                                        " 回复：\n" + record.getString("yd_content") + "\n"));
                            }
                            temp.put("yd_nodename", new TextField(children.getActivityName()));
                            temp.put("yd_apdate", new TextField(DateUtil.date2str(record.getDate("yd_createtime"),
                                    "yyyy-MM-dd HH:mm:ss")));

                            recordList.add(temp);
                        }
                    }
                }
            }
        }

        return recordList;
    }

    /**
     * 初始化多次转交记录
     * @param recordList
     * @param task
     * @return
     * @author: hst
     * @createDate: 2024/11/07
     */
    private void initMultipleTransferRecords (LinkedList<DataRowSet> recordList, IApprovalRecordItem task) {
        BaseTaskHandleRecord records = this.getHandoverLinkRecords(Long.valueOf(task.getTaskId()), task.getOwnerId(),
                task.getUserId());

        DataRowSet temp = new DataRowSet();
        if (Objects.nonNull(records)) {
            /* 获取到的是时间降序，所以从最后开始获取 */
            while (Objects.nonNull(records)) {
                DynamicObject record = records.getEntity().getDynamicObject();
                // 原处理人
                OrmLocaleValue owner = (OrmLocaleValue) record.get("ownerformat");
                // 现处理人
                OrmLocaleValue assignee = (OrmLocaleValue) record.get("assigneeformat");
                // 处理意见
                OrmLocaleValue opinion = (OrmLocaleValue) record.get("opinion");
                temp.put("yd_handled", new TextField(owner.getLocaleValue()));
                temp.put("yd_apresults", new TextField("协办转交"));
                temp.put("yd_apopinion", new TextField("\n" + owner.getLocaleValue() + " 转交 " + assignee.getLocaleValue()
                        + ": \n" + opinion.getLocaleValue() + "\n"));
                temp.put("yd_apdate", new TextField(DateUtil.date2str(record.getDate("createdate"),
                        "yyyy-MM-dd HH:mm:ss")));

                records = records.getNext();
                recordList.add(temp);
            }
        }
    }

    /**
     * 获取转交链路
     * @param taskId
     * @param from
     * @param to
     * @author: hst
     * @createDate: 2023/04/18
     */
    private Long getHandoverLink(Long taskId, Long from, Long to) {
        try {
            WorkflowService service = (WorkflowService) ServiceFactory.getService(WorkflowService.class);
            List<TaskHandleLogEntity> entities = service.getTaskService().getTaskHandleLogs(taskId, new String[]{"coordinate", "transfer"});

            if (entities != null && !entities.isEmpty()) {
                Iterator iterator = entities.iterator();

                while (iterator.hasNext()) {
                    TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();
                    TaskHandleRecord taskHandleRecord = new TaskHandleRecord((TaskHandleLogEntity) entity);
                    Set<Long> handledIds = new HashSet();
                    this.buildRelation(taskHandleRecord, entities, handledIds);
                    List<BaseTaskHandleRecord> coordinateRecords = taskHandleRecord.getCoordinateRecords();
                    for (BaseTaskHandleRecord coordinateRecord : coordinateRecords) {
                        // 按时间倒叙，所以from取最后一个，to取第一个
                        // 节点数
                        int nodeNum = 1;
                        Long taskFrom = coordinateRecord.getFrom();
                        Long taskTo = coordinateRecord.getTo();
                        BaseTaskHandleRecord next = coordinateRecord.getNext();
                        while (Objects.nonNull(next)) {
                            taskTo = next.getTo();
                            next = next.getNext();
                            nodeNum++;
                        }
                        if (Objects.nonNull(taskFrom) && Objects.nonNull(taskTo)) {
                            if (taskFrom.longValue() == from.longValue() && taskTo.longValue() == to.longValue()) {
                                if (nodeNum > 1) {
                                    return coordinateRecord.getId();
                                } else {
                                    return null;
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            if (log.isErrorEnabled()) {
                log.error(e.getMessage());
            }
        }
        return null;
    }

    /**
     * 获取转交链路
     * @param taskId
     * @param from
     * @param to
     * @author: hst
     * @createDate: 2024/11/07
     */
    private BaseTaskHandleRecord getHandoverLinkRecords(Long taskId, Long from, Long to) {
        try {
            WorkflowService service = (WorkflowService) ServiceFactory.getService(WorkflowService.class);
            List<TaskHandleLogEntity> entities = service.getTaskService().getTaskHandleLogs(taskId, new String[]{"coordinate", "transfer"});

            if (entities != null && !entities.isEmpty()) {
                Iterator iterator = entities.iterator();

                while (iterator.hasNext()) {
                    TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();
                    TaskHandleRecord taskHandleRecord = new TaskHandleRecord((TaskHandleLogEntity) entity);
                    Set<Long> handledIds = new HashSet();
                    this.buildRelation(taskHandleRecord, entities, handledIds);
                    List<BaseTaskHandleRecord> coordinateRecords = taskHandleRecord.getCoordinateRecords();
                    for (BaseTaskHandleRecord coordinateRecord : coordinateRecords) {
                        // 按时间倒叙，所以from取最后一个，to取第一个
                        // 节点数
                        int nodeNum = 1;
                        Long taskFrom = coordinateRecord.getFrom();
                        Long taskTo = coordinateRecord.getTo();
                        BaseTaskHandleRecord next = coordinateRecord.getNext();
                        while (Objects.nonNull(next)) {
                            taskTo = next.getTo();
                            next = next.getNext();
                            nodeNum++;
                        }
                        if (Objects.nonNull(taskFrom) && Objects.nonNull(taskTo)) {
                            if (taskFrom.longValue() == from.longValue() && taskTo.longValue() == to.longValue()) {
                                return coordinateRecord.getNext();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (log.isErrorEnabled()) {
                log.error(e.getMessage());
            }
        }
        return null;
    }

    /**
     * 构建协办转交链路
     * @param current
     * @param entities
     * @param handledIds
     * @author: hst
     * @createDate: 2024/11/07
     */
    private void buildRelation(TaskHandleRecord current, List<TaskHandleLogEntity> entities, Set<Long> handledIds) {
        Long taskOwner = current.getFrom();
        Iterator iterator = entities.iterator();

        while(iterator.hasNext()) {
            TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();

            if (!handledIds.contains(entity.getId()) && (taskOwner.equals(entity.getOwnerId()) && !entity.getOwnerId().equals(WfConstanst.ADMIN) || taskOwner.equals(WfConstanst.ADMIN) && entity.getOwnerId().equals(current.getTo()))) {
                handledIds.add(entity.getId());
                String type = entity.getType();
                if ("transfer".equals(type)) {
                    TaskHandleRecord next = new TaskHandleRecord(entity);
                    current.setNext(next);
                    if (taskOwner.equals(WfConstanst.ADMIN) && entity.getOwnerId().equals(current.getTo())) {
                        current.addCoordinateRecord(new BaseTaskHandleRecord(entity));
                    }

                    this.buildRelation(next, entities, handledIds);
                    break;
                }

                if ("coordinate".equals(type)) {
                    current.addCoordinateRecord(new BaseTaskHandleRecord(entity));
                }
            }
        }

        List<BaseTaskHandleRecord> coordinateRecords = current.getCoordinateRecords();
        if (!coordinateRecords.isEmpty()) {
            Collections.reverse(coordinateRecords);
            Iterator coordinateRecord = current.getCoordinateRecords().iterator();

            while(coordinateRecord.hasNext()) {
                BaseTaskHandleRecord child = (BaseTaskHandleRecord) coordinateRecord.next();
                this.buildCoordinateRelation(child, entities, handledIds);
            }
        }
    }

    /**
     * 构建协办转交链路
     * @param current
     * @param entities
     * @param handledIds
     * @author: hst
     * @createDate: 2024/11/07
     */
    private void buildCoordinateRelation(BaseTaskHandleRecord current, List<TaskHandleLogEntity> entities, Set<Long> handledIds) {
        Long userId = current.getTo();
        Iterator iterator = entities.iterator();
        DynamicObject log = current.getEntity().getDynamicObject();
        Date date = log.getDate("createdate");

        while(iterator.hasNext()) {
            TaskHandleLogEntity entity = (TaskHandleLogEntity) iterator.next();
            if (!handledIds.contains(entity.getId()) && userId.equals(entity.getOwnerId()) && "transfer".equals(entity.getType()) && "coordinateTask".equals(entity.getScenes())) {
                DynamicObject tempLog = entity.getDynamicObject();
                if (tempLog.getDate("createdate").compareTo(date) > 0) {
                    handledIds.add(entity.getId());
                    BaseTaskHandleRecord next = new BaseTaskHandleRecord(entity);
                    current.setNext(next);
                    this.buildCoordinateRelation(next, entities, handledIds);
                }
            }
        }

    }

    /**
     * 创建数据行
     */
    private DataSet createDataSetRow (LinkedList<Map<String, Object>> datas) {
        Collection<Object[]> coll = new ArrayList<>();
        //创建显示行字段
        RowMeta createRowMeta = RowMetaFactory.createRowMeta(fields, dataTypes);
        CollectionInput collectionInput = new CollectionInput(createRowMeta, coll);
        DataSet createDataSet = Algo.create(".AuditResultPrintPlugin").createDataSet(collectionInput);
        List<String> fieldList = Arrays.asList(fields);

        //初始创建一个空的报表数据行
        for (Map<String, Object> data : datas) {
            Object[] tempData = new Object[fields.length];
            coll.add(tempData);

            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                int index = Arrays.asList(fields).indexOf(key);
                if (index > -1) {
                    tempData[index] = value;
                }
            }
        }

        return createDataSet;
    }
}
