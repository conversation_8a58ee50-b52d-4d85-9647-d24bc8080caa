package kd.bos.tcbj.srm.admittance.plugin.provider;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicSimpleProperty;
import kd.bos.mvc.list.ListDataProvider;

import java.util.Date;

/**
 * @package: kd.bos.tcbj.srm.admittance.plugin.provider.RawSupSumProvider
 * @className RawSupSumProvider
 * @author: hst
 * @createDate: 2024/12/16
 * @description: （原辅料）合格供应商目录列表自定义取数插件
 * @version: v1.0
 */
public class RawSupSumProvider extends ListDataProvider {

    public DynamicObjectCollection getData(int arg0, int arg1) {
        DynamicObjectCollection collection = super.getData(arg0, arg1);
        DynamicSimpleProperty property = new DynamicSimpleProperty();
        /* 设置字段标识为“yd_nowdate” */
        property.setName("yd_nowdate");
        /* 添加字段 */
        collection.getDynamicObjectType().addProperty(property);
        /* 遍历设值 */
        Date date = new Date();
        for (int i = 0; i < collection.size(); i++) {
            DynamicObject object = collection.get(i);
            object.set("yd_nowdate", date);
        }
        return collection;
    }
}
