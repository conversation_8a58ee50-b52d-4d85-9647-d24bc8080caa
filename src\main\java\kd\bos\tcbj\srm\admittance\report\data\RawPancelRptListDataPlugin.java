package kd.bos.tcbj.srm.admittance.report.data;


import kd.bos.algo.DataSet;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicPropertyCollection;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.ORM;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.taxc.common.util.StringUtil;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.admittance.report.data.RawPancelRptListDataPlugin
 * @className: RawPancelRptListDataPlugin
 * @description: 用于控制原辅料供应商看板列表数据过滤逻辑
 * @author: hst
 * @createDate: 2022/08/22
 * @version: v1.0
 */
public class RawPancelRptListDataPlugin extends AbstractReportListDataPlugin {

    private final static String ENTITY_NAME = "yd_rawsupsumbill";

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        List<QFilter> qFilters = reportQueryParam.getFilter().getQFilters();
        String sortInfo = reportQueryParam.getSortInfo();
        List<QFilter> headFilter = reportQueryParam.getFilter().getHeadFilters();
        ORM orm = ORM.create();
        String fields = "billno,yd_material.number as yd_material_number,yd_material.name as yd_material_name,yd_newmatname,yd_material.modelnum," +
                "yd_manufacturer,yd_agent.name as yd_agent_name,yd_agent.id as yd_agent_masterid,yd_matcat.name,yd_supplieraccesspot.group.name," +
                "yd_supplieraccesspot.name,yd_osaudit,yd_matleveltype,yd_newdate,yd_isstricter,yd_ismix,yd_issterilize,yd_issinglesup,yd_bizdate," +
                "yd_licensedate,yd_wqdate,yd_oadate,yd_tprdate,yd_mdtdate,yd_nngdate,yd_spdate,yd_apsdate,yd_cadate,yd_otherdate,yd_origin,yd_manmonth," +
                "yd_internalmonth,yd_supmatname,yd_evalevel,yd_remark,yd_version";
        DataSet dataSet =  orm.queryDataSet(getClass().getName(), ENTITY_NAME, fields,
                qFilters.toArray(new QFilter[qFilters.size()]), "yd_newdate desc");
        for (QFilter qFilter : headFilter) {
            dataSet = dataSet.where(qFilter.toString());
        }
        if (StringUtil.isNotBlank(sortInfo)) {
            dataSet = dataSet.orderBy(new String[]{sortInfo});
        }
        return dataSet;
    }
}
