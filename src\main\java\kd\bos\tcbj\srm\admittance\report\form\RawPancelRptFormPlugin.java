package kd.bos.tcbj.srm.admittance.report.form;


import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.form.ClientProperties;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.container.Container;
import kd.bos.form.control.Button;
import kd.bos.form.control.Control;
import kd.bos.form.events.FilterContainerSearchClickArgs;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.form.plugin.FormViewPluginProxy;
import kd.bos.orm.query.QFilter;
import kd.bos.report.ReportList;
import kd.bos.report.events.CreateFilterInfoEvent;
import kd.bos.report.events.SortAndFilterEvent;
import kd.bos.report.plugin.AbstractReportFormPlugin;
import kd.bos.report.plugin.ReportViewPluginProxy;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.taxc.common.util.StringUtil;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.admittance.report.form.RawPancelRptListDataPlugin
 * @className: RawPancelRptFormPlugin
 * @description: 用于控制原辅料供应商看板页面显示逻辑
 * @author: hst
 * @createDate: 2022/08/22
 * @version: v1.0
 */
public class RawPancelRptFormPlugin extends AbstractReportFormPlugin implements HyperLinkClickListener{

    private final static String NUM_KEY = "numtext";
    private final static String STATUS_KEY = "statustext";
    private final static String COLUMN_STATUS = "yd_supplieraccesspot.group.name";
    private final static String COLUMN_BILLNO = "billno";
    private final static String COLUMN_AGENTNAME = "yd_agent_name";
    private final static String COLUMN_AGENTID = "yd_agent_masterid";
    private final static String ENTITY_NAME = "yd_rawsupsumbill";

    /**
     * 数据加载完毕后触发
     * <AUTHOR>
     * @param e
     */
    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        this.getView().refresh();
    }

    /**
     * 打开页面后，加载数据之前触发，用于为页面标签赋值
     * <AUTHOR>
     * @createDate: 2022/08/22
     * @param e
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        this.initDataNum();
    }

    /**
     * 设置字段是否进行排序或过滤
     * <AUTHOR>
     * @createDate: 2022/08/22
     * @param allColumns
     */
    @Override
    public void setSortAndFilter(List<SortAndFilterEvent> allColumns) {
        super.setSortAndFilter(allColumns);
        String fields = "yd_material_number,yd_material_name,yd_agent_name";
        for (SortAndFilterEvent event : allColumns) {
            if (fields.indexOf(event.getColumnName()) > -1){
                event.setSort(true);
                event.setFilter(true);
            }
        }
    }

    /**
     * 注册监听
     * <AUTHOR>
     * @createDate: 2022/08/22
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Container flexPanel = this.getView().getControl("yd_flexpanelap4");
        Button refresh = this.getView().getControl("yd_refresh");
        for (Control supregPanel : flexPanel.getItems()) {
            ((Container)supregPanel).addClickListener(this);
        }
        refresh.addClickListener(this);
        ReportList reportList = getControl("reportlistap");
        reportList.addHyperClickListener(this);
    }

    /**
     * 监听用户点击列表上的超链接，跳转对应基础资料页面
     * <AUTHOR>
     * @createDate: 2022/08/25
     * @param hyperLinkClickEvent
     */
    @Override
    public void hyperLinkClick(HyperLinkClickEvent hyperLinkClickEvent) {
        String key = hyperLinkClickEvent.getFieldName();
        switch (key) {
            case COLUMN_AGENTNAME : {
                this.forwardToAgent(hyperLinkClickEvent);
                break;
            }
        }

    }

    /**
     * <AUTHOR>
     * @createDate: 2022/08/22
     * @param evt
     */
    @Override
    public void click(EventObject evt) {
        super.click(evt);
        String key = null;
        Object object = evt.getSource();
        if (null != object) {
            if (object instanceof Container) {
                Container source = (Container) evt.getSource();
                key = source.getKey();
                this.numPanelClick(key);
            } else if (object instanceof Button) {
                this.refreshData(null);
            }
        }
    }

    /**
     * 统计不同状态的单据的数量
     * <AUTHOR>
     * @createDate: 2022/08/22
     */
    public void initDataNum() {
        Container flexPanel = (Container) this.getView().getControl("yd_flexpanelap4");
        Map<String,String> filterMap = new HashMap<>();
        Map<String,String> relationMap = new HashMap<>();
        List<String> filterValue = new ArrayList<>();
        for (Object supregPanel : flexPanel.getItems()) {
            String numKey = "";
            String statusKey = "";
            for (Control panelObject : ((Container) supregPanel).getItems()) {
                List<Control> panelList = ((Container) panelObject).getItems();
                for (Control control : panelList) {
                    if (control.getKey().indexOf(NUM_KEY) > -1) {
                        numKey = control.getKey();
                    }else if (control.getKey().indexOf(STATUS_KEY) > -1) {
                        statusKey = control.getKey();
                        filterMap.put(control.getModel().getValue(statusKey).toString(), statusKey);
                        filterValue.add(control.getModel().getValue(statusKey).toString());
                    }
                    if (StringUtil.isNotBlank(numKey) && StringUtil.isNotEmpty(statusKey)) {
                        relationMap.put(statusKey, numKey);
                    }
                }
            }
        }
        if (filterValue.size() > 0) {
            DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), ENTITY_NAME, COLUMN_STATUS,
                            new QFilter[]{new QFilter(COLUMN_STATUS, QFilter.in, filterValue)}, null);
            DataSet resultSet = dataSet.groupBy(new String[]{COLUMN_STATUS}).count().finish();
            HashMap<String,Object> fieldMap = new HashMap<>();
            fieldMap.put(ClientProperties.ForeColor,"themeColor");
            for (Row row : resultSet) {
                String status = row.getString(COLUMN_STATUS);
                String count = row.getString("count");
                String column = relationMap.get(filterMap.get(status));
                this.getModel().setValue(column,count);
                this.getView().updateControlMetadata(column,fieldMap);
            }
        }
    }

    /**
     * 根据点击的状态过滤数据
     * <AUTHOR>
     * @createDate: 2022/08/22
     * @param key
     */
    public void numPanelClick(String key) {
        Container supregPanel = this.getView().getControl(key);
        for (Object flexPane : supregPanel.getItems()) {
            for (Control control : ((Container)flexPane).getItems()) {
                if (control.getKey().indexOf(STATUS_KEY) > -1) {
                    String status = this.getModel().getValue(control.getKey()).toString();
                    this.refreshData(new QFilter(COLUMN_STATUS,QFilter.equals,status));
                }
            }
        }
    }

    /**
     * 刷新页面数据
     * <AUTHOR>
     * @createDate: 2022/08/22
     * @param qFilter 过滤条件
     */
    public void refreshData(QFilter qFilter) {
        ReportQueryParam queryParam = getQueryParam();
        List<QFilter> qFilters = queryParam.getFilter().getQFilters();
        qFilters.clear();
        if (qFilter != null) {
            qFilters.add(qFilter);
        }
        this.getView().refresh();
    }

    /**
     * 跳转到相应的代理商基础资料页面
     * <AUTHOR>
     * @createDate: 2022/08/25
     * @param hyperLinkClickEvent
     */
    public void forwardToAgent(HyperLinkClickEvent hyperLinkClickEvent) {
        //DynamicObject rowData = hyperLinkClickEvent.getRowData();
        int index = hyperLinkClickEvent.getRowIndex();
        DynamicObject rowData = ((ReportList)this.getControl("reportlistap")).getReportModel().getRowData(1);
        String agent = rowData.getString(COLUMN_AGENTNAME);
        if (StringUtils.isNotEmpty(agent)) {
            String billNo = rowData.getString(COLUMN_BILLNO);
            DynamicObject billEntity =  BusinessDataServiceHelper.loadSingle(ENTITY_NAME,"yd_agent.masterid",
                    new QFilter[]{new QFilter(COLUMN_BILLNO,QFilter.equals,billNo)});
            if (StringUtils.isNotEmpty(billEntity.getString("yd_agent.masterid"))) {
                BillShowParameter billShowParameter = new BillShowParameter();
                billShowParameter.setFormId("srm_supplier");
                billShowParameter.setPkId(billEntity.getString("yd_agent.masterid"));
                billShowParameter.setCaption(String.format("供应商-%s", agent));
                billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
                this.getView().showForm(billShowParameter);
            } else {
                this.getView().showTipNotification("系统异常，未查询到对应的代理商信息！");
            }
        } else {
            this.getView().showTipNotification("该单据中未找到代理商！");
        }
    }
}
