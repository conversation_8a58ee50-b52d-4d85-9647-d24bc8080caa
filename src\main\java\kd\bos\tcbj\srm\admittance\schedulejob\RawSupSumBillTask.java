package kd.bos.tcbj.srm.admittance.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.srm.admittance.helper.RawSupSumBillHelper;

import java.util.Map;

/**
 * @package kd.bos.tcbj.srm.admittance.schedulejob.RawSupSumBillTask
 * @className RawSupSumBillTask
 * @author: hst
 * @createDate: 2022/09/19
 * @version: v1.0
 */
public class RawSupSumBillTask extends AbstractTask {

    private final static String TIMEOUT_STOP = "timeout_stop";
    private final static String TIMEOUT_ALERT = "timeout_alert";
    private final static String STOPPUR = "stop_purchase";
    private final static String NOPUR_REMIND = "noPurchase_remind";

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        String key = map.get("key").toString();
        switch (key) {
            case TIMEOUT_ALERT : {
                //（原辅料）合格供应商目录附件到期暂停采购
                // 是否向供应商发送短信
                boolean isSendSuppier = Boolean.valueOf(map.get("isSendSupplier").toString());
                // 是否向采购方发送短信
                boolean isSendPurchase = Boolean.valueOf(map.get("isSendPurchase").toString());
                //（原辅料）合格供应商目录附件到期预警
                new RawSupSumBillHelper().expirationAlert(null,isSendSuppier,isSendPurchase);
                break;
            }
            case TIMEOUT_STOP : {
                //（原辅料）合格供应商目录附件到期暂停采购
                // 是否向供应商发送短信
                boolean isSendSuppier = Boolean.valueOf(map.get("isSendSupplier").toString());
                // 是否向采购方发送短信
                boolean isSendPurchase = Boolean.valueOf(map.get("isSendPurchase").toString());
                new RawSupSumBillHelper().expirationStop(null,isSendSuppier,isSendPurchase);
                break;
            }
            case STOPPUR : {
                // 长时间未采购暂停（原辅料）合格供应商目录
                // update by hst 2024/04/26 增加月份配置
                int month = Integer.parseInt(map.get("month").toString());
                // 是否向供应商发送短信
                boolean isSendSuppier = Boolean.valueOf(map.get("isSendSupplier").toString());
                // 是否向采购方发送短信
                boolean isSendPurchase = Boolean.valueOf(map.get("isSendPurchase").toString());
                new RawSupSumBillHelper().checkLongTimeNoPurchase(null,month,true,isSendSuppier,isSendPurchase);
                break;
            }
            case NOPUR_REMIND : {
                // 提醒月份
                int month = Integer.parseInt(map.get("month").toString());
                // 是否向供应商发送短信
                boolean isSendSuppier = Boolean.valueOf(map.get("isSendSupplier").toString());
                // 是否向采购方发送短信
                boolean isSendPurchase = Boolean.valueOf(map.get("isSendPurchase").toString());
                new RawSupSumBillHelper().checkLongTimeNoPurchase(null,month,false,isSendSuppier,isSendPurchase);
                break;
            }
        }
    }
}
