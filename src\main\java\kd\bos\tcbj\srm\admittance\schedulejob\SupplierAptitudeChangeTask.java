package kd.bos.tcbj.srm.admittance.schedulejob;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.im.helper.BillTypeHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.outbill.schedulejob.BaseTask;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.ORMUtils;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 供应商资质变更确认单任务
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-9-29
 */
public class SupplierAptitudeChangeTask extends BaseTask {


    @Override
    protected void beforeExecute(RequestContext requestContext, Map<String, Object> map, List<Object> list) {
        // 遍历创建时间为两年前的供应商库，检查是否存在供应商库变更单，如果不存在，添加对应的ID到集合中
        String twoYearDateStr = DateTimeUtils.format(DateTimeUtils.addYear(new Date(), -2), DateTimeUtils.SDF_DATE);

        String runLocate = BizHelper.getRunLocate();
        DataSet dataSet = null;
        try {
            // 找出符合条件，且没有创建供应商资质变更申请单的供应商
            dataSet = BizHelper.getQueryDataSet(runLocate, BillTypeHelper.BILLTYPE_SRM_SUPPLIER, "id as aSupId"
                    , QFilter.of("status='C' and enable = 1 and createtime > ?", twoYearDateStr).toArray());
            DataSet aptitudeDataSet = BizHelper.getQueryDataSet(runLocate, BillTypeHelper.BILLTYPE_SUPAPTITUDECHANGE, "yd_supplier.id as bSupId", null);
            dataSet = dataSet.leftJoin(aptitudeDataSet).on("aSupId", "bSupId").select(new String[]{"aSupId"}, new String[]{"bSupId"}).finish();
            DataSet wDataSet = dataSet.where("bSupId is null or bSupId = 0");

            for (Row row : wDataSet) {
                // 将需要处理的供应商ID加入到集合中
                list.add(row.getLong("aSupId"));
            }
        }finally {
            ORMUtils.close(dataSet);
        }
    }

    @Override
    protected void afterExecute(RequestContext requestContext, Map<String, Object> map, List<Object> list) {
        // 如果不存在需要处理的则跳过
        if (list.size() == 0) return;
        // 自动创建一张《供应商资质变更确认单》，并自动提交单据进入审批流程
        for (Object supId : list) {
            DynamicObject supplierInfo = BizHelper.getDynamicObjectById(BillTypeHelper.BILLTYPE_SRM_SUPPLIER, supId, "id, number, name");
            DynamicObject userInfo = new BizPartnerBizHelper().getSrmSupplierUserBySrmSupplierId(String.valueOf(supId)); // 供应商用户

            // 创建《供应商资质变更确认单》
            DynamicObject info = BizHelper.createDynamicObject(BillTypeHelper.BILLTYPE_SUPAPTITUDECHANGE);
            info.set("yd_supplier", supplierInfo); // 供应商
            info.set("yd_user", userInfo); // 供应商用户
            info.set("yd_bizdate", new Date()); // 业务日期
            // 设置系统默认状态
            BizHelper.defaultDynamicObject(info, true);
            // TODO 分录初始化 —— 待确认
        }
    }
}
