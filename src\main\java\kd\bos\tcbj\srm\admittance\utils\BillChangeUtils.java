package kd.bos.tcbj.srm.admittance.utils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;


import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.dataentity.metadata.clr.CollectionProperty;
import kd.bos.dataentity.metadata.clr.ComplexProperty;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.dataentity.metadata.dynamicobject.DynamicProperty;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.EntityType;
import kd.bos.entity.EntryType;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.ValueMapItem;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.property.AdminDivisionProp;
import kd.bos.entity.property.AssistantProp;
import kd.bos.entity.property.AttachmentProp;
import kd.bos.entity.property.BasedataProp;
import kd.bos.entity.property.BooleanProp;
import kd.bos.entity.property.ComboProp;
import kd.bos.entity.property.DateTimeProp;
import kd.bos.entity.property.DecimalProp;
import kd.bos.entity.property.EntryProp;
import kd.bos.entity.property.MulBasedataProp;
import kd.bos.entity.property.MulComboProp;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.util.JSONUtils;

/**
 * @auditor yanzuwei
 * @date 2022年8月4日
 * 
 */
public class BillChangeUtils 
{	private static Log log = LogFactory.getLog(BillChangeUtils.class);
    private static final String chgBillName="yd_rawsupchgbill";
    
	private static final ArrayList<String> exceptFields = new ArrayList(Arrays
			.asList(new String[]{"seq", "id", "masterid", "createtime", "modifytime", "auditdate","entryentity","creator","auditor","org",
					"yd_infotype", "yd_chgfield", "yd_oldvalue", "yd_newvalue", "yd_note", "yd_datastatus", "yd_changetime", "yd_track",
					"yd_orientryid", "yd_cancelitem", "yd_cancelmatcode", "yd_assessrisk", "yd_cancelreason", "yd_billstatus", "yd_createtime",
					"yd_trackmark", "yd_oribillid"}));
    
	public static Map getChangeData(DynamicObject obj)
	{
		HashMap resultMap = new HashMap();
		LinkedHashMap<String, List<Object>> newValueMap = new LinkedHashMap<String, List<Object>>();
		LinkedHashMap<String, List<Object>> oldValueMap = new LinkedHashMap<String, List<Object>>();
		MainEntityType mainEntityType = (MainEntityType) obj.getDataEntityType();
		DynamicObject newDataEntity = obj;
		DynamicObject tgtDataEntity = BusinessDataServiceHelper.newDynamicObject(chgBillName);
		EntryProp entryProp = (EntryProp) tgtDataEntity.getDataEntityType().getProperties().get("entryentity");
		EntryType entryType = (EntryType) entryProp.getItemType();
		long billId = ((Long) obj.getPkValue()).longValue();
		DynamicObject oldDataEntity=BusinessDataServiceHelper.loadSingle(obj.getPkValue(), obj.getDynamicObjectType());
		if (oldDataEntity == null)
		{  
			resultMap.put("errMsg","无变更内容"); 
		    resultMap.put("status","error"); 
			return resultMap;
		}else
		{
			DynamicObjectCollection tgtChangeEntry = tgtDataEntity.getDynamicObjectCollection("entryentity");
			tgtChangeEntry.clear();
			readPropValue(oldDataEntity, null, null, null, mainEntityType, 0, true, newValueMap, oldValueMap,
					null, null);
			HashMap<String, String> oldEntryIdSeqMap = new HashMap<String, String>();
			for (Map.Entry<String, List<Object>> oldEntry : oldValueMap.entrySet()) {
				String[] splitStrArr = oldEntry.getKey().split("\\.");
				if (splitStrArr.length != 4)
					continue;
				oldEntryIdSeqMap.put(splitStrArr[2], splitStrArr[3]);
			}
			readPropValue(newDataEntity, null, null, null, mainEntityType, 0, false, newValueMap, oldValueMap,
					null, oldEntryIdSeqMap);
			log.info("开始差异比较，并新增分录");
			compareAndCreateEntry(newValueMap, oldValueMap, entryType, entryProp, tgtChangeEntry);
			
			tgtDataEntity.set("yd_supplier", obj.getDynamicObject("yd_agent"));
			// update by hst 2024/04/11 调整为去物料基础资料名称
			tgtDataEntity.set("yd_materiel", obj.get("yd_material"));
			tgtDataEntity.set("yd_srcbillid", obj.getPkValue().toString()); 
			tgtDataEntity.set("yd_srcbillno", obj.getString("billno")); 
			
			tgtDataEntity.set("org",obj.getDynamicObject("org"));
			tgtDataEntity.set("billstatus","A");
			tgtDataEntity.set("yd_datastatus","A");
			// update by hst 2024/03/04 一下方法获取到的是Map，需要调整
//			tgtDataEntity.set("creator",UserServiceHelper.getUserInfoByID(UserServiceHelper.getCurrentUserId()));
			tgtDataEntity.set("creator",UserServiceHelper.getCurrentUser("id"));
			System.out.println(tgtDataEntity);
			
			if(tgtChangeEntry.size()==0)
			{
			   resultMap.put("errMsg","无变更内容"); 
			   resultMap.put("status","error"); 
			   
			   return resultMap;
			}
		 
			OperationResult result=SaveServiceHelper.saveOperate(chgBillName,new DynamicObject[] {tgtDataEntity});
			
			resultMap.put("status","ok"); 
	        resultMap.put("billid",tgtDataEntity.getPkValue()); 
			 
		} 
		return resultMap;
	}
	
	
	
	public static void readPropValue(DynamicObject dataEntity, String entryName, String refName, Object entryId,
			MainEntityType mainEntityType, Object baseDataId, boolean isOldChangeEntry,
			Map<String, List<Object>> newValueMap, Map<String, List<Object>> oldValueMap, String seq,
			Map<String, String> oldEntryIdSeqMap) {
		IDataEntityType dType = dataEntity.getDataEntityType();
		for (IDataEntityProperty property : dType.getProperties()) {
			ArrayList<Object> tempList;
			String propName = property.getName();
			if (exceptFields.contains(propName))
				continue;
			if (property instanceof CollectionProperty || property instanceof EntryProp) {
				DynamicObjectCollection rows = dataEntity.getDynamicObjectCollection(property);
				rows.sort((row1, row2) -> row1.getInt("seq") - row2.getInt("seq"));
				int i = 1;
				for (DynamicObject row : rows) {
					row.set("seq", (Object) i++);
				}
				if (rows == null || rows.size() <= 0)
					continue;
				for (DynamicObject row : rows) {
					String newSeq = row.getString("seq");
					if (!isOldChangeEntry && null != oldEntryIdSeqMap
							&& (newSeq = oldEntryIdSeqMap.get(String.valueOf(row.getPkValue()))) == null) {
						newSeq = row.getString("seq");
					}
					readPropValue(row, property.toString(), refName, row.getPkValue(), mainEntityType,
							baseDataId, isOldChangeEntry, newValueMap, oldValueMap, newSeq, oldEntryIdSeqMap);
				}
				continue;
			}
			String key = null;
			Object propValue = null;
			String propDisplayName = null;
			String infoType = null;
			StringBuilder attachmentUrl = new StringBuilder();
			DynamicObjectCollection attachmentCollection = null;
			baseDataId = 0;
			String entType=null;
			try {
				key = entryName != null && refName != null
						? entryName + "." + refName + "." + property.getName()
						: (entryName != null
								? entryName + "." + property.getName()
								: (refName != null ? refName + "." + property.getName() : property.getName()));
				if (seq != null) {
					key = key + "." + seq;
				}
				if (property instanceof ComplexProperty || property instanceof AssistantProp
						|| property instanceof BasedataProp) {
					DynamicObject dyn = dataEntity.getDynamicObject(property);
					if (null != dyn) {
						propValue = dyn.getString("name");
						baseDataId = dyn.getLong("id");
						infoType = "Z";
						
						entType=dyn.getDataEntityType().getName();
						
					}
				} else if (property instanceof MulBasedataProp) {
					dataEntity.get(property);
					DynamicObjectCollection rows = dataEntity.getDynamicObjectCollection(property);
					propValue = "";
					baseDataId = "";
					DynamicObject province = null;
					for (DynamicObject row : rows) {
						province = (DynamicObject) row.get(1);
						entType=province.getDataEntityType().getName();
						if (propValue.toString().length() < 1) {
							propValue = province.getLocaleString("name");
							baseDataId = province.getPkValue();
							continue;
						}
						propValue = propValue + "," + province.getString("name");
						baseDataId = baseDataId + "," + province.getPkValue();
					}
					infoType = "M";
				} else if (property instanceof MulComboProp) {
					baseDataId = dataEntity.get(property);
					propValue = "";
					String baseDataIdStr = (String) baseDataId;
					MulComboProp comb = (MulComboProp) property;
					for (ValueMapItem item : comb.getComboItems()) {
						if (baseDataIdStr.indexOf("," + item.getValue() + ",") <= -1)
							continue;
						propValue = propValue+item.getName().getLocaleValue()+",";
					}
					infoType = "Z";
				} else if (property instanceof ComboProp) {
					baseDataId = dataEntity.get(property);
					ComboProp comb = (ComboProp) property;
					for (ValueMapItem item : comb.getComboItems()) {
						if (!item.getValue().equals(baseDataId))
							continue;
						propValue = item.getName();
						break;
					}
					infoType = "Z";
				} else if (property instanceof DateTimeProp) {
					Object obj = dataEntity.get(property);
					if (null != obj) {
						propValue = DateUtil.date2strByLocalDateTime((Date) dataEntity.getDate(property),
								(String) "yyyy-MM-dd");
					}
					infoType = "D";
				} else if (property instanceof BooleanProp) {
					propValue = (Boolean) dataEntity.get(property) != false ? 1 : 0;
					infoType = "B";
				} else if (property instanceof AdminDivisionProp) {
					baseDataId = dataEntity.get(property);
					String funame = queryAdmindiVisionFuname(baseDataId);
					if (!StringUtils.isEmpty(funame)) {
						propValue = funame;
						infoType = "Z";
					}
				} else {
					propValue = dataEntity.get(property);
					if (property instanceof DecimalProp) {
						propValue = new BigDecimal(String.valueOf(propValue)).stripTrailingZeros().toPlainString();
					}
					infoType = "S";
				}
				if (property instanceof AttachmentProp) {
					DynamicObject content;
					DynamicObjectCollection orginAttachments = (DynamicObjectCollection) dataEntity.get(property);
					StringBuilder propBuilder = new StringBuilder();
					int size = orginAttachments.size();
					int length = 0;
					if (size > 1) {
						for (DynamicObject attachment : orginAttachments) {
							content = (DynamicObject) attachment.get(1);
							if (length < size - 1) {
								propBuilder.append(content.getString("name")).append(';');
							} else {
								propBuilder.append(content.getString("name"));
							}
							attachmentUrl.append(content.getString("url"));
							++length;
						}
					} else {
						for (DynamicObject attachment : orginAttachments) {
							content = (DynamicObject) attachment.get(1);
							propBuilder.append(content.getString("name"));
							attachmentUrl.append(content.getString("url"));
						}
					}
					propValue = propBuilder.toString();
					attachmentCollection = orginAttachments;
					infoType = "A";
				}
				if (entryName != null) {
					LocaleString s2;
					LocaleString s1 = mainEntityType.getProperty(entryName).getDisplayName();
					DynamicProperty entryProp = ((EntityType) mainEntityType.getAllEntities().get(entryName))
							.getProperty(propName);
					LocaleString localeString = s2 = entryProp == null ? null : entryProp.getDisplayName();
					if (s1 != null && s2 != null) {
						propDisplayName = MessageFormat.format(ResManager.loadKDString((String) "{0}-第{1}行-{2}",
								(String) "SrmSupChgUtil_0", (String) "scm-common", (Object[]) new Object[0]),
								new Object[]{s1, seq, s2});
					}
				} else if (refName != null) {
					propDisplayName = mainEntityType.getProperty(refName).getDisplayName().toString() + "_"
							+ (Object) mainEntityType.getProperty(propName).getDisplayName();
				} else {
					LocaleString proname = mainEntityType.getProperty(propName).getDisplayName();
					String string = propDisplayName = proname == null ? null : proname.toString();
				}
				if (key == null || propName == null || propValue == null || propDisplayName == null)
					continue;
				tempList = new ArrayList();
				tempList.add(propName);
				tempList.add(propValue);
				tempList.add(propDisplayName);
				tempList.add(baseDataId);
				if (entryName != null) {
					tempList.add(entryName);
				} else {
					tempList.add(refName);
				}
				tempList.add(seq);
				tempList.add(infoType);
				tempList.add(attachmentUrl.toString());
				tempList.add((Object) attachmentCollection);
				tempList.add(entType);
				
				if (isOldChangeEntry) {
					oldValueMap.put(key, tempList);
					continue;
				}
				newValueMap.put(key, tempList);
			} catch (Exception e) {
				try {
					log.error((Throwable) e);
					if (key == null || propName == null || propValue == null || propDisplayName == null)
						continue;
					tempList = new ArrayList<Object>();
					tempList.add(propName);
					tempList.add((String) propValue);
					tempList.add(propDisplayName);
					tempList.add(baseDataId);
					if (entryName != null) {
						tempList.add(entryName);
					} else {
						tempList.add(refName);
					}
					tempList.add(seq);
					tempList.add(infoType);
					tempList.add(attachmentUrl.toString());
					tempList.add((Object) attachmentCollection);
					if (isOldChangeEntry) {
						oldValueMap.put(key, tempList);
						continue;
					}
					newValueMap.put(key, tempList);
				} catch (Throwable throwable) {
					if (key != null && propName != null && propValue != null && propDisplayName != null) {
						ArrayList<Object> tempList2 = new ArrayList<Object>();
						tempList2.add(propName);
						tempList2.add(propValue);
						tempList2.add(propDisplayName);
						tempList2.add(baseDataId);
						if (entryName != null) {
							tempList2.add(entryName);
						} else {
							tempList2.add(refName);
						}
						tempList2.add(seq);
						tempList2.add(infoType);
						tempList2.add(attachmentUrl.toString());
						tempList2.add(attachmentCollection);
						if (isOldChangeEntry) {
							oldValueMap.put(key, tempList2);
						} else {
							newValueMap.put(key, tempList2);
						}
					}
					throw throwable;
				}
			}
		}
	}
	
	private static String queryAdmindiVisionFuname(Object adminId) {
		if (adminId.equals("")) {
			return null;
		} else {
			QFilter filter = new QFilter("id", "=", adminId);
			QFilter[] filters = new QFilter[]{filter};
			DynamicObject dynamicObject = QueryServiceHelper.queryOne("bd_admindivision",
					"id,name,fullname,country.id,country.name", filters);
			return Objects.nonNull(dynamicObject) && Objects.nonNull(dynamicObject.get("fullname"))
					? dynamicObject.getString("fullname")
					: null;
		}
	}
	
	
	public static void compareAndCreateEntry(Map<String, List<Object>> newValueMap,
			Map<String, List<Object>> oldValueMap, EntryType entryType, EntryProp entryProp,
			DynamicObjectCollection tgtChangeEntry) {
		List<Object> oldList;
		List<Object> newList;
		for (Map.Entry<String, List<Object>> entryMap : oldValueMap.entrySet()) {
			oldList = entryMap.getValue();
			newList = newValueMap.get(entryMap.getKey());
			if (oldList != null && newList != null) {
				if (oldList.size() < 2 || newList.size() < 2 || Objects.isNull(oldList.get(1).toString())
						|| Objects.isNull(oldList.get(2).toString()) || Objects.isNull(newList.get(1).toString())
						|| Objects.isNull(newList.get(2).toString())) {
					newValueMap.remove(entryMap.getKey());
					continue;
				}
				if (oldList.get(1).toString().concat(oldList.get(2).toString())
						.equals(newList.get(1).toString().concat(newList.get(2).toString()))) {
					if (!("Z".equals(oldList.get(6)) && !oldList.get(3).equals(newList.get(3)))) {
						newValueMap.remove(entryMap.getKey());
						continue;
					}
				}
			}
			insertRow(entryMap.getKey(), oldList, newList, entryType, entryProp, tgtChangeEntry);
			newValueMap.remove(entryMap.getKey());
		}
		for (Map.Entry<String, List<Object>> newValueEntryMap : newValueMap.entrySet()) {
			oldList = oldValueMap.get(newValueEntryMap.getKey());
			newList = newValueEntryMap.getValue();
			if (oldList != null && newList != null
					&& (oldList.size() < 2 || newList.size() < 2 || Objects.isNull(oldList.get(1).toString())
							|| Objects.isNull(oldList.get(2).toString()) || Objects.isNull(newList.get(1).toString())
							|| Objects.isNull(newList.get(2).toString())
							|| oldList.get(1).toString().concat(oldList.get(2).toString())
									.equals(newList.get(1).toString().concat(newList.get(2).toString()))))
				continue;
			 insertRow(newValueEntryMap.getKey(), oldList, newList, entryType, entryProp, tgtChangeEntry);
		}
	}
	
	
	public static void insertRow(String key, List<Object> oldList, List<Object> newList, EntryType entryType,
			EntryProp entryProp, DynamicObjectCollection tgtChangeEntry)
	{
		log.info("新增分录开始" + key);
		String v1 = oldList == null ? null : oldList.get(1).toString().trim();
		String v2 = newList == null ? null : newList.get(1).toString().trim();
		log.info("新增分录开始V1:" + v1 + " ,v2:" + v2);
		if ((v1 == null || v1.equals("")) && (v2 == null || v2.equals(""))) {
			return;
		}
		
		DynamicObject tempRow = new DynamicObject((DynamicObjectType) entryType);
		tempRow.set("yd_chgfield", newList == null ? oldList.get(2) : newList.get(2));
		tempRow.set("yd_oldvalue", (Object) (oldList == null ? null : oldList.get(1).toString()));
		tempRow.set("yd_newvalue", (Object) (newList == null ? null : newList.get(1).toString()));
		tempRow.set("yd_fieldname", newList == null ? oldList.get(0) : newList.get(0));
		tempRow.set("yd_infotype", newList == null ? oldList.get(6) : newList.get(6));
		if("Z".equals(tempRow.getString("yd_infotype")))
		{
			//3,9
			if(StringUtils.isNotBlank(tempRow.getString("yd_oldvalue")))
			{
				Map oldExtMap=new HashMap();
				oldExtMap.put("value", ""+oldList.get(3));
				oldExtMap.put("enttype", oldList.get(9));
				try {
					tempRow.set("yd_oldvalueex", JSONUtils.toString(oldExtMap));
				} catch (IOException e)
				{ 
					e.printStackTrace();
				}
				
			}
			
			if(StringUtils.isNotBlank(tempRow.getString("yd_newvalue")))
			{
				 
				Map extMap=new HashMap();
				extMap.put("value", ""+newList.get(3)); 
				extMap.put("enttype", newList.get(9));
				try {
					tempRow.set("yd_newvalueex", JSONUtils.toString(extMap));
				} catch (IOException e)
				{ 
					e.printStackTrace();
				}
				
			}
			
		}
		
		
		if (tempRow.get("yd_fieldname").equals("isdefault") || tempRow.get("yd_fieldname").equals("isdefault_link"))
		{
			if (tempRow.get("yd_oldvalue").equals("0")) {
				tempRow.set("yd_oldvalue", (Object) ResManager.loadKDString((String) "非默认", (String) "SrmSupChgUtil_1",
						(String) "scm-common", (Object[]) new Object[0]));
			} else {
				tempRow.set("yd_oldvalue", (Object) ResManager.loadKDString((String) "默认", (String) "SrmSupChgUtil_2",
						(String) "scm-common", (Object[]) new Object[0]));
			}
			if (tempRow.get("yd_newvalue").equals("0")) {
				tempRow.set("yd_newvalue", (Object) ResManager.loadKDString((String) "非默认", (String) "SrmSupChgUtil_1",
						(String) "scm-common", (Object[]) new Object[0]));
			} else {
				tempRow.set("yd_newvalue", (Object) ResManager.loadKDString((String) "默认", (String) "SrmSupChgUtil_2",
						(String) "scm-common", (Object[]) new Object[0]));
			}
		}
		tgtChangeEntry.add(tempRow);
		log.info("新增分录完成");
	}

	/**
	 * 包材合格供应商目录变更
	 * @param obj
	 * @param entityName
	 * @return
	 * @author: hst
	 * @createDate: 2024/04/11
	 */
	public static Map getChangeData(DynamicObject obj, String entityName)
	{
		HashMap resultMap = new HashMap();
		LinkedHashMap<String, List<Object>> newValueMap = new LinkedHashMap<String, List<Object>>();
		LinkedHashMap<String, List<Object>> oldValueMap = new LinkedHashMap<String, List<Object>>();
		MainEntityType mainEntityType = (MainEntityType) obj.getDataEntityType();
		DynamicObject newDataEntity = obj;
		DynamicObject tgtDataEntity = BusinessDataServiceHelper.newDynamicObject(entityName);
		EntryProp entryProp = (EntryProp) tgtDataEntity.getDataEntityType().getProperties().get("entryentity");
		EntryType entryType = (EntryType) entryProp.getItemType();
		long billId = ((Long) obj.getPkValue()).longValue();
		DynamicObject oldDataEntity = BusinessDataServiceHelper.loadSingle(obj.getPkValue(), obj.getDynamicObjectType());
		if (oldDataEntity == null)
		{
			resultMap.put("errMsg","无变更内容");
			resultMap.put("status","error");
			return resultMap;
		}else
		{
			DynamicObjectCollection tgtChangeEntry = tgtDataEntity.getDynamicObjectCollection("entryentity");
			tgtChangeEntry.clear();
			readPropValue(oldDataEntity, null, null, null, mainEntityType, 0, true, newValueMap, oldValueMap,
					null, null);
			HashMap<String, String> oldEntryIdSeqMap = new HashMap<String, String>();
			for (Map.Entry<String, List<Object>> oldEntry : oldValueMap.entrySet()) {
				String[] splitStrArr = oldEntry.getKey().split("\\.");
				if (splitStrArr.length != 4)
					continue;
				oldEntryIdSeqMap.put(splitStrArr[2], splitStrArr[3]);
			}
			readPropValue(newDataEntity, null, null, null, mainEntityType, 0, false, newValueMap, oldValueMap,
					null, oldEntryIdSeqMap);

			compareAndCreateEntry(newValueMap, oldValueMap, entryType, entryProp, tgtChangeEntry);

			tgtDataEntity.set("yd_supplier", obj.getDynamicObject("yd_supplier"));
			tgtDataEntity.set("yd_materiel", obj.get("yd_material"));
			tgtDataEntity.set("yd_srcbillid", obj.getPkValue().toString());
			tgtDataEntity.set("yd_srcbillno", obj.getString("billno"));
			tgtDataEntity.set("billstatus","A");
			tgtDataEntity.set("yd_datastatus","A");
			tgtDataEntity.set("creator",UserServiceHelper.getCurrentUser("id"));

			if(tgtChangeEntry.size()==0)
			{
				resultMap.put("errMsg","无变更内容");
				resultMap.put("status","error");

				return resultMap;
			}

			OperationResult result=SaveServiceHelper.saveOperate(entityName,new DynamicObject[] {tgtDataEntity});

			resultMap.put("status","ok");
			resultMap.put("billid",tgtDataEntity.getPkValue());

		}
		return resultMap;
	}
}
