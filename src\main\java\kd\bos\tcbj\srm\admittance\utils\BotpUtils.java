package kd.bos.tcbj.srm.admittance.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.botp.ConvertRuleElement;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.botp.runtime.TableDefine;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.ConvertMetaServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * @auditor yanzuwei
 * @date 2022年8月17日
 * 
 */
public class BotpUtils 
{
	/**
	 * 下推单据
	 * @param oriBillId
	 * @param oriBillEntity
	 * @param desBillEntity
	 * @param ruleId
	 * @return
	 */
	public static DynamicObject pushBill(String oriBillId, String oriBillEntity, String desBillEntity, String ruleId)
	{
		// 将源单ID，源单标识，目标单标识，目标单名称，下推规则作为参数
		// 返回目标单ID
		// 构建下推参数
		PushArgs pushArgs = new PushArgs();
		pushArgs.setSourceEntityNumber(oriBillEntity);  // 源单标志
		pushArgs.setTargetEntityNumber(desBillEntity);  // 目标单标志
		pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告 
		if(StringUtils.isEmpty(ruleId))
		{
			 List<ConvertRuleElement> ruleList= ConvertMetaServiceHelper.loadRules(oriBillEntity, desBillEntity);
		        if(!ruleList.isEmpty())
		        {
		        	ruleId= ruleList.get(0).getId(); 
		        } 
		}
		pushArgs.setRuleId(ruleId);  // 固定下推规则  
	 
		//需要下推的单据
        List<ListSelectedRow> selectedRows = new ArrayList<>();
        ListSelectedRow srcBill = new ListSelectedRow(oriBillId);
        selectedRows.add(srcBill);
        pushArgs.setSelectedRows(selectedRows);
        
       
        
        //调用下推引擎，下推目标单
        ConvertOperationResult pushResult = ConvertServiceHelper.push(pushArgs);

        //判断下推是否成功，如果失败，提炼失败消息
        if(!pushResult.isSuccess()){
            //错误摘要
            String errMessage = pushResult.getMessage();
            throw new KDException("下推失败:" + errMessage);
        }

        //获取生成的目标单数据包
        MainEntityType targetMainType = EntityMetadataCache.getDataEntityType(desBillEntity);
        List<DynamicObject> targetBillObjs = pushResult.loadTargetDataObjects(new IRefrencedataProvider() {
            @Override
            public void fillReferenceData(Object[] objs, IDataEntityType dType) {
                BusinessDataReader.loadRefence(objs, dType);
            }
        }, targetMainType);
        
        return targetBillObjs.get(0);
	}
	
	
	
	
	   /**
     * @param srcEntityNumber 源单实体标识
     * @param srcBillId 源单单据id
     * @param targetEntityNumber 目标单实体标识
     * @param targetBillId 目标单单据id
     * 
     * 创建下游单据与源单的关联关系
     */
    public static void createRelation(String srcEntityNumber, Long srcBillId, 
    		String targetEntityNumber, Long targetBillId) {

        // 读取需修复的目标单
        DynamicObject targetBillObj = BusinessDataServiceHelper.loadSingle(targetBillId, targetEntityNumber);

        // 获取源单单据体的表格定义：记录关联关系时，需要用到此对象中的tableId值，用一个长整数值唯一标识源单及单据体
        TableDefine srcTableDefine = EntityMetadataCache.loadTableDefine(srcEntityNumber, srcEntityNumber);

        // 根据源单编号，读取源单数据
        DynamicObject sourceBillObj = BusinessDataServiceHelper.loadSingle(srcBillId, srcEntityNumber);

        //判断是否已存在该关系
        String lkEntryKey = "billhead_lk"; 
        QFilter qFilter = new QFilter("id", QCP.equals, targetBillObj.getPkValue())
        		.and(lkEntryKey + "." + lkEntryKey + "_sid", QCP.equals, sourceBillObj.getPkValue())
        		.and(lkEntryKey + "." + lkEntryKey + "_sbillid", QCP.equals, sourceBillObj.getPkValue())
        		.and(lkEntryKey + "." + lkEntryKey + "_stableid", QCP.equals, srcTableDefine.getTableId());
		boolean exists = QueryServiceHelper.exists(targetBillObj.getDynamicObjectType().getName(), qFilter.toArray());
        if(!exists) {
        	createLinkEntity(lkEntryKey, targetBillObj, srcTableDefine, sourceBillObj);
        	// 调用目标单的保存操作，保存维护好的关联子实体行数据，并自动调用反写引擎，创建关联关系及反写：
        	OperationResult saveOperate = SaveServiceHelper.saveOperate(
        			targetEntityNumber,							// 目标单主实体编码
        			new DynamicObject[] {targetBillObj},		// 目标单数据包
        			OperateOption.create());					// 操作参数，可通过option传入各种自定义参数
        	if (!saveOperate.isSuccess()) {
        		throw new KDException("保存单据失败：" + saveOperate.getMessage());
        	}
        }

    }

    /**
     * @param lkEntryKey 固定值billhead_lk
     * @param targetBillObj 目标单obj
     * @param srcTableDefine table定义实体
     * @param sourceBillObj 源单obj
     * 只适用关联实体为单据头的关联关系
     */
    private static void createLinkEntity(String lkEntryKey, DynamicObject targetBillObj,
            TableDefine srcTableDefine, DynamicObject sourceBillObj) {
    	// 获取下级_lk子实体行
    	DynamicObjectCollection linkRows = targetBillObj.getDynamicObjectCollection(lkEntryKey);
    	linkRows.clear();
    	// 找到了匹配的行，创建一条_lk子实体上数据，记录源单内码
    	DynamicObject linkRow = new DynamicObject(linkRows.getDynamicObjectType());
    	linkRows.add(linkRow);
    	// 在lk行中，记录源单分录表格编码、源单内码、源单分录内码
    	linkRow.set(lkEntryKey + "_stableid", srcTableDefine.getTableId());        // 源单分录表格编码：以此标识源单类型及单据体
    	linkRow.set(lkEntryKey + "_sbillid", sourceBillObj.getPkValue());        // 源单内码
    	linkRow.set(lkEntryKey + "_sid", sourceBillObj.getPkValue());            // 源单分录行内码    
    }
}
