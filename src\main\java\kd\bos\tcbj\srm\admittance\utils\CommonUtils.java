package kd.bos.tcbj.srm.admittance.utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.encrypt.Encrypters;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDException;
import kd.bos.login.emun.MCDBType;
import kd.bos.login.mc.service.MCService;
import kd.bos.login.utils.ZKUtils;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.util.RevProxyUtil;

/**
 * @auditor yanzuwei
 * @date 2022年8月9日
 * 
 */
public class CommonUtils 
{
	public static Object getPK(DynamicObject info,String prop)
	{
		if(info!=null&&info.getDynamicObject(prop)!=null)
		{
			return info.getDynamicObject(prop).getPkValue();
		}
		return 0L;
	}
	
	public static boolean isEmpty(DynamicObject info)
	{
		if(info==null||info.getPkValue()==null)
		{
			return true;
		}
		if(info.getPkValue() instanceof Long)
		{
			if(((Long)info.getPkValue()).longValue()==0L)
			{
				return true;
			}
		}
		return false;
		
	}
	
	

	/**
	 * 操作单据的保存、提交、审核，都会触发源单的业务规则
	 */
	public static void doSaveAndSubmit(String desBillEntity, DynamicObject targetBillObj)
	{
		Map<String,String> result = new HashMap<String,String>();
		String desBillId = "";
	    String billName=targetBillObj.getDataEntityType().getAlias();
		
		// 保存目标单据
        OperationResult saveResult = SaveServiceHelper.saveOperate(desBillEntity, new DynamicObject[]{targetBillObj}, OperateOption.create());
        if (!saveResult.isSuccess())
        {
           throw new KDException("保存"+billName+"失败：" + saveResult.getMessage()); 
        } 
        // 提交目标单据
        List<Object> successPkIds = saveResult.getSuccessPkIds();
        for (Object successPkId : successPkIds) 
        {
        	String pk = saveResult.getSuccessPkIds().get(0).toString();
        	QFilter qFilter1 = new QFilter("id", QCP.equals, pk);
        	Object billNo = BusinessDataServiceHelper.loadSingle(desBillEntity, "id,billno", qFilter1.toArray()).get("billno");
        	OperationResult submitResult = OperationServiceHelper.executeOperate("submit", desBillEntity, new Object[]{pk}, OperateOption.create());
        	if (!submitResult.isSuccess()) 
        	{   // 错误摘要
        	    throw new KDException(billName + billNo + "生成成功，但提交失败，请到"+billName+"手动提交，查看提交失败原因：" + submitResult.getMessage()); 
        	}
		}
      
		
	}
	
	
	/**
	 * 单据操作
	 */
	public static void executeOperate(String desBillEntity,  String op,DynamicObject info)
	{
        String billName=info.getDataEntityType().getAlias(); 
        OperationResult submitResult = OperationServiceHelper.executeOperate(op, desBillEntity, new Object[]{info.getPkValue()}, OperateOption.create());
        if (!submitResult.isSuccess()) 
        {
           throw new KDException(billName +"["+ info.getString("billno")+"]"+op + "操作失败，失败原因：" + submitResult.getMessage());
        }  
	}


	
	
	
	public static JSONObject getDBSettingsFromZK(String tenantcode)
	{
		JSONObject res = new JSONObject(); 
		String dbKey = String.format("mc.tenant.%s.data", tenantcode);
		try {
			JSONObject prop;
			JSONArray dbdata;
			String zkValue = ZKUtils.getZkData((String) MCService.getZKUrl(),
					(String) MCService.getZKCommonPropPath((String) dbKey));
			if (StringUtils.isNotEmpty((String) zkValue) && !(dbdata = JSONArray.parseArray((String) zkValue)).isEmpty()
					&& Objects.nonNull((Object) (prop = dbdata.getJSONObject(0)))) {
				String datacenterid;
				for (Object it : prop.getJSONArray("instancelist")) {
					JSONObject ins = (JSONObject) it;
					if (!Objects.nonNull(ins.get((Object) "routekey")) || !ins.getString("routekey").equals("sys"))
						continue;
					String dbtype = ins.getString("dbtype");
					res.put("dbtype", (Object) MCDBType.getCode((String) dbtype));
					res.put("dbhost", ins.get((Object) "dbip"));
					res.put("dbport", ins.get((Object) "dbport"));
					res.put("dbuser", ins.get((Object) "dbuser"));
					res.put("dbinstance", ins.get((Object) "dbinsatnce")); 
					res.put("dbpassword", ins.get((Object) "dbpassword"));
					res.put("dbpassword_decode",Encrypters.decode((String)ins.get("dbpassword")));
					
					break;
				}
				if (StringUtils.isNotEmpty((String) (datacenterid = prop.getString("datacenterid")))) {
					res.put("accountId", (Object) datacenterid);
				}
			}
		} catch (Exception e) 
		{
			 e.printStackTrace();
		}
		return res;
	}

}
