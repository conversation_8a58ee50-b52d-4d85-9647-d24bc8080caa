package kd.bos.tcbj.srm.admittance.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.exception.ErrorCode;
import kd.bos.exception.KDException;
import kd.bos.servicehelper.TimeServiceHelper;
import org.apache.commons.lang.StringUtils;

public class DateUtil {
	public static final String FORMAT_PARTEN = "yyyy-MM-dd";
	public static final String FORMAT_PARTEN2 = "yyyy-MM-dd HH:mm:ss";
	public static final String FORMAT_PARTEN3 = "yyyyMM";
	public static final ZoneId UTC_PLUS_8 = ZoneId.systemDefault();

	
	  public static Date truncateDate(Date dt)
	    {
	      if (dt == null)
	      {
	       return null;
	      }
	      Calendar cal = Calendar.getInstance();
	      cal.setTime(dt);
	      cal.set(cal.get(1), cal.get(2), cal.get(5), 0, 0, 0); 
	      return new Date(cal.getTimeInMillis() / 1000L * 1000L);
	    }

	 public static Date parseDate(String s)
	 throws ParseException
	 {
		  String[] fmts= {"yyyy-MM-dd HH:mm:ss","yyyy-MM-dd","MM/dd/yyyy HH:mm:ss","MM/dd/yyyy","HH:mm:ss"};
		  for(int i=0;i<fmts.length;i++)
		  {
			  try 
			  {
				  Date date= string2date(s, fmts[i]);
				  return date;
				  
			  }catch(Exception e)
			  {
				  e.printStackTrace();
			  }
		  }
		  throw new ParseException("can not understand your format",-1);
		      
	 }
	 
	
	public static String getFormatEndDate(Date date) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		LocalDateTime localDateTime = localDate.atTime(23, 59, 59);
		return localDateTime2str(localDateTime, "yyyy-MM-dd HH:mm:ss");
	}

	

	public static Date getFormatDateEndMaxDate(Date date) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		localDate = localDate.withDayOfMonth(localDate.lengthOfMonth());
		LocalDateTime localDateTime = localDate.atTime(23, 59, 59);
		return localDateTime2date(localDateTime);
	}

	public static String getFormatStartDate(Date date) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		LocalDateTime localDateTime = localDate.atStartOfDay();
		return localDateTime2str(localDateTime, "yyyy-MM-dd HH:mm:ss");
	}

	public static Date formatEndDate(Date date) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		LocalDateTime localDateTime = localDate.atTime(23, 59, 59);
		return localDateTime2date(localDateTime);
	}

	public static Date formatEndDateForCurDate(Date date) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		LocalDateTime localDateTime = localDate.atTime(LocalTime.now(UTC_PLUS_8));
		return localDateTime2date(localDateTime);
	}

	public static Date formatStartDate(Date date) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		LocalDateTime localDateTime = localDate.atStartOfDay();
		return localDateTime2date(localDateTime);
	}

	public static final Date getPreviousSomeMonthStingDate(Date date, int count) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		localDate = localDate.minusMonths((long) count).withDayOfMonth(1);
		LocalDateTime localDateTime = localDate.atStartOfDay();
		return localDateTime2date(localDateTime);
	}

	public static Date getPreviousMonthDate(Date date) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		localDate = localDate.minusMonths(1L);
		LocalDateTime localDateTime = localDate.atStartOfDay();
		return localDateTime2date(localDateTime);
	}

	public static Date getPreviousNatureMonthDate(int count) {
		Date temp = TimeServiceHelper.now();
		LocalDate localDate = temp.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		int lengthOfMonth = localDate.lengthOfMonth();
		int dayOfMonth = count >= lengthOfMonth ? lengthOfMonth : count;
		localDate = localDate.withDayOfMonth(dayOfMonth);
		LocalDateTime localDateTime = localDate.atTime(23, 59, 59);
		return localDateTime2date(localDateTime);
	}

	public static String date2str(Date date, String pattern) {
		if (null == date) {
			return null;
		} else {
			SimpleDateFormat format = null;
			if (StringUtils.isNotEmpty(pattern)) {
				format = new SimpleDateFormat(pattern);
			} else {
				format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			}

			return format.format(date);
		}
	}

	public static Date getDefaultPreviousMonthDate(Date date) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		localDate = localDate.minusMonths(1L);
		LocalDateTime localDateTime = localDate.atStartOfDay();
		return localDateTime2date(localDateTime);
	}

	public static Date string2date(String str, String pattern) {
		SimpleDateFormat format = null;
		if (StringUtils.isNotEmpty(pattern)) {
			format = new SimpleDateFormat(pattern);
		} else {
			format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		}

		Date date = null;
		if (!StringUtils.isEmpty(str)) {
			try {
				date = format.parse(str);
			} catch (ParseException arg4) {
				throw new KDException(
						new ErrorCode("time convert error",
								ResManager.loadKDString("日期%s转换异常", "DateUtil_0", "scm-common", new Object[0])),
						new Object[]{str});
			}
		}

		return date;
	}

	public static final Date getPreviousSomeMonth(Date date, int count) {
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
		LocalDateTime localDateTime = localDate.minusMonths((long) count).withDayOfMonth(1).atStartOfDay();
		return localDateTime2date(localDateTime);
	}

	public static ArrayList<String> assembleContinuousMonth(Date date) {
		byte num = 5;
		String[] dateSet = new String[num + 1];
		LocalDate localDate = date.toInstant().atZone(UTC_PLUS_8).toLocalDate();

		for (int i = 0; i < dateSet.length; ++i) {
			LocalDateTime localDateTime = localDate.minusMonths((long) num - (long) i).withDayOfMonth(1).atStartOfDay();
			dateSet[i] = localDateTime.format(DateTimeFormatter.ofPattern("yyyyMM"));
		}

		return new ArrayList(Arrays.asList(dateSet));
	}

	public static final Date addMonth(Date date, int month) {
		LocalDateTime localDateTime = date2localDateTime(date);
		return localDateTime2date(localDateTime.plusMonths((long) month));
	}

	public static final Date addWeek(Date date, int week) {
		LocalDateTime localDateTime = date2localDateTime(date);
		return localDateTime2date(localDateTime.plusWeeks((long) week));
	}

	public static final Date getNextDay(Date date) {
		LocalDateTime localDateTime = date2localDateTime(date);
		return localDateTime2date(localDateTime.plusDays(1L));
	}

	public static final Date addDays(Date date, int days) {
		LocalDateTime localDateTime = date2localDateTime(date);
		return localDateTime2date(localDateTime.plusDays((long) days));
	}

	public static final Date getDateByOffsetDay(Date now, int offsetDay) {
		LocalDateTime localDateTime = date2localDateTime(now);
		return localDateTime2date(localDateTime.plusDays((long) offsetDay));
	}

	public static int daysBetween(Date firstDate, Date secDate) {
		LocalDate localDate1 = date2localDate(firstDate);
		LocalDate localDate2 = date2localDate(secDate);
		return localDate2.isAfter(localDate1)
				? (int) localDate1.until(localDate2, ChronoUnit.DAYS)
				: (int) localDate2.until(localDate1, ChronoUnit.DAYS);
	}

	public static Date getDayStart(Date date) {
		return formatStartDate(date);
	}

	public static String date2strByLocalDateTime(Date date, String pattern) {
		LocalDateTime localDateTime = date2localDateTime(date);
		return localDateTime2str(localDateTime, pattern);
	}

	public static Date str2dateByLocalDateTime(String dateStr, String pattern) {
		LocalDateTime localDateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
		return localDateTime2date(localDateTime);
	}

	public static String getMondayOfThisWeek() {
		return localDateTime2str(LocalDate.now().with(DayOfWeek.MONDAY).atStartOfDay(), "yyyy-MM-dd HH:mm:ss");
	}

	public static String getSundayOfThisWeek() {
		LocalDateTime localDateTime = LocalDate.now().with(DayOfWeek.SUNDAY).atTime(23, 59, 59);
		return localDateTime2str(localDateTime, "yyyy-MM-dd HH:mm:ss");
	}

	private static String localDateTime2str(LocalDateTime localDateTime, String pattern) {
		return localDateTime.format(DateTimeFormatter.ofPattern(pattern));
	}

	private static Date localDateTime2date(LocalDateTime localDateTime) {
		return Date.from(localDateTime.atZone(UTC_PLUS_8).toInstant());
	}

	private static LocalDateTime date2localDateTime(Date date) {
		return date.toInstant().atZone(UTC_PLUS_8).toLocalDateTime();
	}

	private static LocalDate date2localDate(Date date) {
		return date.toInstant().atZone(UTC_PLUS_8).toLocalDate();
	}

	/**
	 * 计算两个时间差（小时）
	 * 结果保留两位小数
	 * @author: hst
	 * @createDate: 2023/01/11
	 */
	public static String getDifferHour(Date startDate, Date endDate) {
		double hourM = (double) 1000 * 60 * 60;
		double differ = (double) endDate.getTime() - (double) startDate.getTime();
		double hour =differ/hourM;
		return String.format("%.2f",hour);
	}

	/**
	 * 计算两个时间差（天）
	 * 结果保留两位小数
	 * @author: hst
	 * @createDate: 2023/01/12
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static String getDifferDay(Date startDate, Date endDate) {
		double DayM = (double) 1000 * 60 * 60 * 24;
		double differ = (double) endDate.getTime() - (double) startDate.getTime();
		double day =differ/DayM;
		return String.format("%.2f",day);
	}

	/**
	 * 计算两个时间差（天）
	 * @author: hst
	 * @createDate: 2024/06/03
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static int getDifferDays(Date startDate, Date endDate) {
		double DayM = (double) 1000 * 60 * 60 * 24;
		double differ = (double) endDate.getTime() - (double) startDate.getTime();
		double day =differ/DayM;
		return (int) day;
	}

	/**
	 * 返回指定格式的日期
	 * @author: hst
	 * @createDate: 2023/01/12
	 * @param date
	 * @param pattern
	 * @return
	 */
	public static Date formatDate (Date date, String pattern) {
		return string2date(date2str(date,pattern),pattern);
	}

	/**
	 * 返回增加指定小时数的日期
	 * @author: hst
	 * @createDate: 2023/01/12
	 * @param date
	 * @param hour
	 * @return
	 */
	public static final Date addHour(Date date, int hour) {
		LocalDateTime localDateTime = date2localDateTime(date);
		return localDateTime2date(localDateTime.plusHours((long) hour));
	}

	/**
	 * 获取月份差
	 * @param startDate
	 * @param endDate
	 * @return
	 * @author: hst
	 * @createDate: 2024/06/03
	 */
	public static int getMonthDiff (Date startDate, Date endDate) {
		Calendar startCalendar = Calendar.getInstance();
		startCalendar.setTime(startDate);
		Calendar endCalendar = Calendar.getInstance();
		endCalendar.setTime(endDate);

		// 确保 startCalendar 的日期在 endCalendar 之前
		if (startCalendar.after(endCalendar)) {
			Calendar temp = startCalendar;
			startCalendar = endCalendar;
			endCalendar = temp;
		}

		int months = endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
		int years = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);

		// 如果年份差不为0，则从月份差中减去（年份差 * 12）
		return months - years * 12;
	}

	/**
	 * 获取日期
	 * @param date
	 * @return
	 * @author: hst
	 * @createDate: 2024/03/25
	 */
	public static int getDay (Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int day = calendar.get(Calendar.DAY_OF_MONTH);
		return day;
	}
}