package kd.bos.tcbj.srm.admittance.utils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;

import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang.StringUtils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.MetasUtils.Prop;

public class ImportBillUtils
{
	
	private static void transMap(Map map,Object[][] cfgMap,Map dataMap)
	{
		
		if(map==null || cfgMap==null)
		{
			return ;
		}
		 
		for(int i=0;i<cfgMap.length;i++)
		{
	    	Object[] cfg=cfgMap[i];
	    	MapCfgInfo cfgInfo=new MapCfgInfo();
	    	cfgInfo.setJsonProp((String)cfg[0]);
	    	cfgInfo.setEasProp((String)cfg[1]);
	    	if(cfg.length>=3)
	    	{
	    	 cfgInfo.setRequired((Boolean)cfg[2]);
	    	}
	    	if(cfg.length>=4)
	    	{
	    	 cfgInfo.setF7SeachProp((String)cfg[3]);
	    	}
	    	if(cfg.length>=5)
	    	{
	    	  cfgInfo.setF7Filter((String)cfg[4]);
	    	}
			// update by hst 2023/05/06 若对应值为空则不存储，用于同一字段配置多个映射
			if (isIncludeAttributes(dataMap,(String)cfg[0])) {
				map.put(cfgInfo.getEasProp(), cfgInfo);
			}
	   } 
	}
	
	/***
	 * @fun json转化对象
	 * @param cfgMap
	 * {
	 *   {"json属性","cosmic属性","是否必填","F7查询字段","过滤特殊"}
	 * }
	 * 
	 * ****/
	 public static void convertBill(DynamicObject info,Map dataMap,Object[][] cfgMap,ImportHandler handler) 
	  throws KDException
	  {
		 Map m=new HashMap();
		 // update by hst 2023/05/06 当同一字段配置多个映射时，若对应值为空则不存储
		 transMap(m,cfgMap,dataMap);
		 convertBill(info,dataMap,m,handler);
	  }
	
	 public static void convertBill(DynamicObject info,Map dataMap,Map<String,MapCfgInfo> cfgMap,ImportHandler handler) 
	  throws KDException
	  {
		 if(cfgMap==null)
		 {
			 cfgMap=new HashMap();
		 }
		map2Info(dataMap,info,cfgMap,handler,null); 
	  }
	    
    private static DynamicObject map2Info( 
    		Map dataMap,
    		String entityName,
    		Map<String,MapCfgInfo> cfgMap ,
    		String entryName,
    		ImportHandler handler)
     throws KDException
    {
    	  DynamicObject ov =BusinessDataServiceHelper.newDynamicObject(entityName);
	     return  map2Info(dataMap,ov,cfgMap,handler,null);
    }
	   
	  private static DynamicObject map2Info(
	    
	    Map dataMap,
	    DynamicObject info,
	    Map<String,MapCfgInfo> cfgMap ,
	    ImportHandler handler,List<Prop> chProps)
	    throws KDException
	   {
		  
		     DynamicObject ov = info;
		     List<Prop>  proList=null;
		     if(chProps!=null)
		     {
		    	proList=chProps; 
		     }else 
		     {
		        proList=MetasUtils.getProps(info.getDataEntityType().getName());
		     }
		     for(int i=0;i<proList.size();i++)
		     {
		    	 Prop proType=proList.get(i);
		    	 String propName=proType.getKey();
		    	 String jsonName=propName;
		    	 MapCfgInfo mapCfg=cfgMap.get(propName);

		    	 if(mapCfg==null)
		    	 {
		    		 continue;
		    	 }
		    	 
		    	 if(mapCfg!=null&&StringUtils.isNotEmpty(mapCfg.getJsonProp()))
		    	 {
		    		 jsonName=mapCfg.getJsonProp();
		    	 }
		    	 
		    	 Object data=dataMap.get(jsonName);
		    	 if(data==null)
		    	 {
		    		 if(mapCfg!=null&&mapCfg.isRequired())
			    	 {
			    		 throw  new KDException("【"+proType.getLabel()+"】不允许为空!");
			    	 }
		    	 }else
		    	 {
		    		if(Prop.KEY_Entry.equals(proType.getType()))
		    		{//处理分录
		    		 
		    		   DynamicObjectCollection cs= ov.getDynamicObjectCollection(propName);
		    		   
		    			if(data instanceof List)
		    			{
		    				List entrys=(List)data;
		    				for(int j=0;j<entrys.size();j++)
		    				{
		    					Map entryMap=(Map)entrys.get(j);
		    					DynamicObject entryInfo=cs.addNew();
		    					map2Info(entryMap,entryInfo,cfgMap,handler,proType.getEntryPropList()); 
		    				}
		    			}
		    			
		    		}else
		    		{//处理基本字段及F7
		    		   setPropValue(ov,data,proType,mapCfg);
		    		}
		    	  	 
		    		 
		    	 }
		    	 
		     }
		     if(handler!=null)
			   {
				 handler.convert( dataMap, info);  
			   } 
		   return ov;
		   
	   }
	   
	  private static void setPropValue(DynamicObject ov,Object value,Prop propType,MapCfgInfo  mapCfg )
	  throws   KDException
	   {
		   String fieldType=propType.getType();
		   String propName=propType.getKey();
		   
		     if(value==null)
			 {
				 ov.set(propName, null);
				 return ;
			 }
		   
		   String strValue=value.toString();
		   
		   if(Prop.KEY_String.equals(fieldType))
		   {
			   ov.set(propName,strValue);
			   
		   }else if(Prop.KEY_BigDecimal.equals(fieldType))
		   {
			   if(StringUtils.isNotBlank(strValue))
			   {
				  strValue=strValue.trim();
				 try
				 {
			     ov.set(propName, new BigDecimal(strValue));
			     }catch(Exception e)
			     {
				    throw new KDException("【"+propType.getLabel()+"】:"+strValue+",数字转化失败");
			     }
			   }
			   
		   }else if(Prop.KEY_Date.equals(fieldType))
		   {
			   Date date=null;
			   if(StringUtils.isNotBlank(strValue))
			   {
				   try
				   {
			        date=DateUtil.parseDate(strValue);
			        ov.set(propName, date); 
			       }catch(ParseException e)
				   {
			    	 throw new  KDException("【"+propType.getLabel()+"】:"+strValue+",日期格式无法识别");
				   }
			   }
		   }else if(Prop.KEY_Long.equals(fieldType))
		   {
			   try{
				   ov.set(propName,Long.parseLong(strValue));
					}catch (Exception e) 
			       {
						ov.set(propName,0);
				   }
		   }else if(Prop.KEY_Boolean.equals(fieldType))
			{
			     boolean bValue=false;
			     if("true".equalsIgnoreCase(strValue)
			    	 ||"是".equalsIgnoreCase(strValue)
			    	 ||"1".equalsIgnoreCase(strValue)
			    	 ||"y".equalsIgnoreCase(strValue)
			    	 )
			     {
			    	 bValue=true;
			     }
			     ov.set(propName,bValue); 
			     
		    }else if(Prop.KEY_Enum.equals(fieldType))
		    {
		        ov.set(propName, strValue);
					 
		    }else if(Prop.KEY_MutiEnum.equals(fieldType))
		   {
			   ov.set(propName, strValue);

		   }else if(Prop.KEY_BaseData.equals(fieldType))
		    {
		     
		    	String whl="";
		    	if(mapCfg!=null && StringUtils.isNotEmpty(mapCfg.getF7Filter()))
		    	{
		    		whl=" and ("+mapCfg.getF7Filter()+") "; 
		    	}
		    	
		    	String seachProp="name";
		    	if(mapCfg!=null && StringUtils.isNotEmpty(mapCfg.f7SeachProp))
		    	{
		    		seachProp=mapCfg.f7SeachProp;
		    	}
		    	
		    	DynamicObject[]  cs=BusinessDataServiceHelper.load(propType.getBaseDataKey(), "id", QFilter.of( ""+seachProp+"='"+strValue+"' "+whl, new Object[0]).toArray());
		    	
		    	if(cs.length>0)
		    	{
		    		ov.set(propName,cs[0]);
		    	}else
		    	{   
		    		if(mapCfg!=null&&mapCfg.isRequired)
		    		{
		    		  throw new  KDException("【"+propType.getLabel()+"】:"+strValue+",不存在");
		    		}
		    	}
		    }
		   
	   }

	/**
	 * 校验已解析完毕的OA数据中是否包含指定字段
	 * @param dataMap
	 * @param field
	 * @return
	 * @author: hst
	 * @createDate: 2023/05/06
	 */
	   private static boolean isIncludeAttributes(Map dataMap, String field) {
		 if (dataMap.containsKey(field)) {
			 return true;
		 } else {
			 for (Map.Entry< String,Object> entry : ((Map<String,Object>) dataMap).entrySet()) {
				 Object data = dataMap.get(entry.getKey());
				 if (data instanceof LinkedTreeMap) {
					if (((LinkedTreeMap) data).containsKey(field)) {
						return true;
					}
				 }
				 if (data instanceof ArrayList) {
					 for (int i = 0; i < ((ArrayList) data).size(); i++) {
						 Map map = (Map) ((ArrayList) data).get(i);
						 if (map.containsKey(field)) {
							 return true;
						 }
					 }
				 }
			 }
			 return false;
		 }
	   }
	    
	  public static class MapCfgInfo
	  {
	 	private String jsonProp=null;
	 	private String easProp=null;
	 	private boolean isRequired=false;
	 	private String f7Filter=null;
	 	private String f7SeachProp=null;
	 	 
	 	public String getJsonProp() {
	 		return jsonProp;
	 	}
	 	public void setJsonProp(String jsonProp) {
	 		this.jsonProp = jsonProp;
	 	}
	 	public String getEasProp() {
	 		return easProp;
	 	}
	 	public void setEasProp(String easProp) {
	 		this.easProp = easProp;
	 	}
	 	public boolean isRequired() {
	 		return isRequired;
	 	}
	 	public void setRequired(boolean isRequired) {
	 		this.isRequired = isRequired;
	 	}
	 	
	 	public String getF7Filter() {
	 		return f7Filter;
	 	}
	 	public void setF7Filter(String filter) {
	 		f7Filter = filter;
	 	}
	 	public String getF7SeachProp() {
	 		return f7SeachProp;
	 	}
	 	public void setF7SeachProp(String seachProp) {
	 		f7SeachProp = seachProp;
	 	}
	}
}
