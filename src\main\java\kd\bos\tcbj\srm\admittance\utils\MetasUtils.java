package kd.bos.tcbj.srm.admittance.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import kd.bos.dataentity.metadata.IDataEntityProperty; 
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.ValueMapItem;
import kd.bos.entity.property.AttachmentProp;
import kd.bos.entity.property.BasedataProp;
import kd.bos.entity.property.BooleanProp;
import kd.bos.entity.property.ComboProp;
import kd.bos.entity.property.DateProp;
import kd.bos.entity.property.DateTimeProp;
import kd.bos.entity.property.DecimalProp;
import kd.bos.entity.property.EntryProp;
import kd.bos.entity.property.LongProp;
import kd.bos.entity.property.MulBasedataProp;
import kd.bos.entity.property.MulComboProp;
import kd.bos.entity.property.TextProp;

/**
 * @auditor yanzuwei
 * @date 2022年8月15日
 * 
 */
public class MetasUtils 
{
 
    public static List<Prop> getProps(String entkey)
    {
    	
    	MainEntityType entityType = EntityMetadataCache.getDataEntityType(entkey);
    	List<Prop> propList=new ArrayList();  
    	for (IDataEntityProperty property : entityType.getProperties())
    	{
    		if(property.isDbIgnore())
    		{
    			continue;
    		}
    		 
    		if (property instanceof EntryProp)
    		{//分录,子分录 计算分录数据
    			Prop simpleProp=new  Prop(); 
    		    simpleProp.key=property.getName();
    		    simpleProp.label=""+property.getDisplayName();
    			simpleProp.type= Prop.KEY_Entry; 
    			 EntryProp p=(EntryProp)property; 
    			 DataEntityPropertyCollection entryProperties = p.getDynamicCollectionItemPropertyType().getProperties();
    		     for(IDataEntityProperty entryProp:entryProperties)
    		     { 
    		    	 Prop entProp=transProp(simpleProp.key,entryProp);
    		    	 simpleProp.entryPropList.add(entProp);
    		    	 
    		     }
    		     propList.add(simpleProp);
    		}else
    		{//基本数据类型处理
    			
    			Prop sProp=transProp(null,property);
    			propList.add(sProp);
    			
    		}
    	}
    	return propList;
    } 
    
    
    
    static Prop  transProp(String entryName,IDataEntityProperty property)
    {
    	Prop simpleProp=new  Prop(); 
    	simpleProp.key=property.getName();
		simpleProp.label=""+property.getDisplayName();
		simpleProp.type=""+property.getClass().getSimpleName(); 
		
    	if(property instanceof TextProp)
		{
			simpleProp.type=Prop.KEY_String;
					
		}else if(property instanceof DecimalProp ) 
		{
			simpleProp.type=Prop.KEY_BigDecimal;
			
		} else if(property instanceof BooleanProp)
		{
			simpleProp.type=Prop.KEY_Boolean;
			
		}  if(property instanceof BasedataProp)
		{
			BasedataProp p=( BasedataProp)property;
			simpleProp.baseDataKey=p.getBaseEntityId();
			simpleProp.type=Prop.KEY_BaseData;
			
		} else if(property instanceof MulBasedataProp)
		{
			MulBasedataProp p=( MulBasedataProp)property;
			simpleProp.baseDataKey=p.getBaseEntityId();
			simpleProp.type=Prop.KEY_MutiBaseData;
			
		}else if(property instanceof DateTimeProp)
		{
		    simpleProp.type=Prop.KEY_Date;
		    
		}else if(property instanceof LongProp)
		{
		    simpleProp.type=Prop.KEY_Long;
		    
		}else if(property instanceof TextProp)
		{
		    simpleProp.type=Prop.KEY_String;
		    
		}else if(property instanceof ComboProp)
		{
			if(property instanceof  MulComboProp)
			{
				 simpleProp.type=Prop.KEY_MutiEnum;
			}else
			{
				 simpleProp.type=Prop.KEY_Enum;
			}
		   
		    ComboProp p=( ComboProp)property;
		    List<ValueMapItem> vs= p.getComboItems();
		    for(ValueMapItem item:vs)
		    {
		      simpleProp.enumMap.put(item.getValue(), item.getName().toString()); 
		    } 
		}
    	
    	return simpleProp;
    }
    
    public static void listProps(String entKey)
    {
    	List<Prop> propList=getProps(entKey);
    	for(Prop p:propList)
    	{
    		System.out.println(p.getKey()+","+p.getLabel()+","+p.getType());
    		if(Prop.KEY_Entry.equals(p.getType()))
    		{
    			List<Prop> entryList=p.getEntryPropList();
    			for(Prop entProp:entryList)
    			{
    				System.out.println("\t\t"+entProp.getKey()+","+entProp.getLabel()+","+entProp.getType());
    			}
    			
    		} 
    	}
    	
    }
    
    
    
    
  public static class Prop
    {
    	public static final String KEY_String="String";
    	public static final String KEY_Date="Date";
    	public static final String KEY_BigDecimal="BigDecimal";
    	public static final String KEY_Long="Long";
    	public static final String KEY_Boolean="boolean";
    	public static final String KEY_int="int";
    	public static final String KEY_BaseData="basedata";
    	public static final String KEY_MutiBaseData="mutibasedata";
    	public static final String KEY_Enum="enum";
    	public static final String KEY_MutiEnum="mutienum";
    	public static final String KEY_Entry="entry";
    	
    	
    	String key;
    	String label;
    	String type;
    	
    	String baseDataKey;
    	Map enumMap=new HashMap();
    	List entryPropList=new ArrayList();
    	
    	
		public String getKey() {
			return key;
		}
		public String getLabel() {
			return label;
		}
		public String getType() {
			return type;
		}
		public String getBaseDataKey() {
			return baseDataKey;
		}
		public Map getEnumMap() {
			return enumMap;
		}
		public List getEntryPropList() {
			return entryPropList;
		}
		
		public String toString()
		{
			return ReflectionToStringBuilder.toString(this);
		}
    	
    }

	/**
	 * 获取实体所有属性
	 **/
	public static List<String> getAllSelector(String entityName) {
		MainEntityType entityType = EntityMetadataCache.getDataEntityType(entityName);
		List<String> propList = new ArrayList();
		for (IDataEntityProperty property : entityType.getProperties()) {
			if (property.isDbIgnore()) {
				continue;
			}
			if (property instanceof EntryProp) {//分录,子分录 计算分录数据

				propList.add(property.getName());
				EntryProp p = (EntryProp) property;
				DataEntityPropertyCollection entryProperties = p.getDynamicCollectionItemPropertyType().getProperties();
				for (IDataEntityProperty entryProp : entryProperties)
				{
					String entryPropName=entryProp.getName();
					if(entryPropName.equals("id") || entryPropName.equals("seq"))
					{
						propList.add(property.getName()+"."+entryPropName);
						continue;
					}
					if(entryPropName.endsWith("_id"))
					{
						entryPropName= StringUtils.substringBeforeLast(entryPropName,"_id");
					}
					propList.add(entryPropName);
				}
			} else {//基本数据类型处理

				String prop=property.getName();
				if(prop.endsWith("_id"))
				{
					prop=StringUtils.substringBeforeLast(prop,"_id");
				}
				propList.add(prop);
			}
		}
		return propList;
	}

}
