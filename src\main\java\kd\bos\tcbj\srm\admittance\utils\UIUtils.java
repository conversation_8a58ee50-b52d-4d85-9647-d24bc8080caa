package kd.bos.tcbj.srm.admittance.utils;

import kd.bos.bill.BillShowParameter;
import kd.bos.form.CloseCallBack;
import kd.bos.form.IFormView;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.form.plugin.IFormPlugin;
import kd.bos.list.ListFilterParameter;
import kd.bos.list.ListShowParameter;
import kd.bos.orm.query.QFilter;

/**
 * @auditor yanzuwei
 * @date 2022年8月2日
 * 
 */
public class UIUtils
{
	public static void showBillUI(IFormView fromView,String fromId,Object pkId )
	{
	 
		BillShowParameter p=getBillShowParameter(fromId,pkId);
		p.getOpenStyle().setShowType(ShowType.Modal);
		fromView.showForm(p);
	}
	
	public static void showBillUI(IFormView fromView,String fromId,Object pkId,ShowType showType )
	{
	    BillShowParameter billShowParameter = new BillShowParameter(); 
		billShowParameter.setFormId(fromId); 
		billShowParameter.setPkId(pkId); 
		billShowParameter.getOpenStyle().setShowType(showType);   
		fromView.showForm(billShowParameter); 
	}
	
	public static BillShowParameter getBillShowParameter(String fromId,Object pkId)
	{
	    BillShowParameter billShowParameter = new BillShowParameter(); 
		billShowParameter.setFormId(fromId); 
		billShowParameter.setPkId(pkId);  
		StyleCss inlineStyleCss = new StyleCss();
		inlineStyleCss.setHeight("600");
		inlineStyleCss.setWidth("800");
		billShowParameter.getOpenStyle().setInlineStyleCss(inlineStyleCss); 
		return billShowParameter; 
	}
	
	/**
	 * @fun F7选择
	 * @param formId 基础数据fromid
	 * @param actionKey 回调标示
	 * ***/
	public static void selectF7(AbstractFormPlugin plugin,String fromId,
			String actionKey,
			boolean isMultiSelect,
			QFilter filter)
	{
		//创建弹出列表界面对象，ListShowParameter 表示弹出页面为列表界面
		ListShowParameter listShowParameter = new ListShowParameter();
		//设置F7列表表单模板
		listShowParameter.setFormId("bos_listf7");
		//设置BillFormId为基础资料的标识
		listShowParameter.setBillFormId(fromId);
		//设置弹出页面的打开方式
		listShowParameter.getOpenStyle().setShowType(ShowType.Modal);
		//设置打开页面的大小
		StyleCss inlineStyleCss = new StyleCss();
		inlineStyleCss.setHeight("580");
		inlineStyleCss.setWidth("960");
		listShowParameter.getOpenStyle().setInlineStyleCss(inlineStyleCss);
		listShowParameter.setMultiSelect(isMultiSelect);
		
		if(filter!=null)
		{
			ListFilterParameter filterParam=new ListFilterParameter();
			filterParam.getQFilters().add(filter);
			listShowParameter.setListFilterParameter(filterParam); 
		}
		//LookUp必须为true，该界面才是可选界面
		listShowParameter.setLookUp(true);
		ListFilterParameter filterParam=new ListFilterParameter(); 
		listShowParameter.setListFilterParameter(filterParam);
		CloseCallBack closeCallBack =new CloseCallBack(plugin,actionKey);
		listShowParameter.setCloseCallBack(closeCallBack);
		//弹出F7选择界面
		plugin.getView().showForm(listShowParameter);
		
	}

}
