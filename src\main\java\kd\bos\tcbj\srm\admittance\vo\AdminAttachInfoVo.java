package kd.bos.tcbj.srm.admittance.vo;

import kd.bos.dataentity.entity.DynamicObjectCollection;

import java.util.Date;

/**
 * @package: kd.bos.tcbj.srm.admittance.vo.AdminAttachInfoVo
 * @className AdminAttachInfoVo
 * @author: hst
 * @createDate: 2024/07/10
 * @description: 准入附件信息实体类
 * @version: v1.0
 */
public class AdminAttachInfoVo {
    // 匹配字段
    private String matchField;
    // 单据ID
    private String billId;
    // 单据编号
    private String billNo;
    // 分录id
    private String entryId;
    // 目标附件库
    private String tarEntity;
    // 目标分录
    private String tarEntry;
    // 目标分类字段
    private String tarSortField;
    // 目标分类
    private String tarSort;
    // 目标资质类型字段
    private String tarAptField;
    // 目标资质类型
    private String tarAptSort;
    // 目标资质类型名称
    private String tarAptNameField;
    // 目标资质类型名称
    private String tarAptName;
    // 目标附件字段
    private String tarAttachField;
    // 附件信息
    private DynamicObjectCollection attachCollection;
    // 签发日期字段名
    private String isSueDateField;
    // 签发日期
    private Date isSueDate;
    // 有效日期至字段名
    private String dateToField;
    // 有效日期至
    private Date dateTo;
    // 重命名取值基础资料字段
    private String reNameField;
    // 归属基础资料字段名
    private String parentDataField;
    // 路径名称
    private String pathName;
    // 源分录标识
    private String oriEntry;
    // 源分录序号
    private int oriSeq;
    // 标记已重命名字段
    private String isReNameField;
    // 标记已更新附件库字段
    private String isAlreadField;
    // 扩展字段
    private String ext1;
    // 单据类型
    private String billType;

    public String getMatchField() {return matchField;}

    public void setMatchField(String matchField) {this.matchField = matchField;}

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {this.billId = billId;}
    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {this.billNo = billNo;}

    public String getEntryId() {
        return entryId;
    }

    public void setEntryId(String entryId) {this.entryId = entryId;}

    public String getTarEntity() {return tarEntity;}

    public void setTarEntity(String tarEntity) {this.tarEntity = tarEntity;}

    public String getTarEntry() {return tarEntry;}

    public void setTarEntry(String tarEntry) {this.tarEntry = tarEntry;}

    public String getTarSortField() {
        return tarSortField;
    }

    public void setTarSortField(String tarSortField) {this.tarSortField = tarSortField;}

    public String getTarSort() {
        return tarSort;
    }

    public void setTarSort(String tarSort) {
        this.tarSort = tarSort;
    }

    public String getTarAptField() {
        return tarAptField;
    }

    public void setTarAptField(String tarAptField) {
        this.tarAptField = tarAptField;
    }

    public String getTarAptSort() {return tarAptSort;}

    public void setTarAptSort(String tarAptSort) {
        this.tarAptSort = tarAptSort;
    }

    public String getTarAttachField() {
        return tarAttachField;
    }

    public void setTarAttachField(String tarAttachField) {
        this.tarAttachField = tarAttachField;
    }

    public DynamicObjectCollection getAttachCollection() {
        return attachCollection;
    }

    public void setAttachCollection(DynamicObjectCollection attachCollection) {this.attachCollection = attachCollection;}

    public Date getIsSueDate() {return isSueDate;}

    public void setIsSueDate(Date isSueDate) {
        this.isSueDate = isSueDate;
    }

    public Date getDateTo() {
        return dateTo;
    }

    public void setDateTo(Date dateTo) {
        this.dateTo = dateTo;
    }

    public String getTarAptName() {return tarAptName;}

    public void setTarAptName(String tarAptName) {this.tarAptName = tarAptName;}

    public String getTarAptNameField() {return tarAptNameField;}

    public void setTarAptNameField(String tarAptNameField) {this.tarAptNameField = tarAptNameField;}

    public String getIsSueDateField() {return isSueDateField;}

    public void setIsSueDateField(String isSueDateField) {this.isSueDateField = isSueDateField;}

    public String getDateToField() {return dateToField;}

    public void setDateToField(String dateToField) {this.dateToField = dateToField;}

    public String getReNameField() {return reNameField;}

    public void setReNameField(String reNameField) {this.reNameField = reNameField;}

    public String getPathName() {return pathName;}

    public void setPathName(String pathName) {this.pathName = pathName;}

    public String getParentDataField() {return parentDataField;}

    public void setParentDataField(String parentDataField) {this.parentDataField = parentDataField;}

    public int getOriSeq() {return oriSeq;}

    public void setOriSeq(int oriSeq) {this.oriSeq = oriSeq;}

    public String getOriEntry() {return oriEntry;}

    public void setOriEntry(String oriEntry) {this.oriEntry = oriEntry;}

    public String getIsReNameField() {return isReNameField;}

    public void setIsReNameField(String isReNameField) {this.isReNameField = isReNameField;}

    public String getIsAlreadField() {return isAlreadField;}

    public void setIsAlreadField(String isAlreadField) {this.isAlreadField = isAlreadField;}

    public String getExt1() {return ext1;}

    public void setExt1(String ext1) {this.ext1 = ext1;}

    public String getBillType() {return billType;}

    public void setBillType(String billType) {this.billType = billType;}
}
