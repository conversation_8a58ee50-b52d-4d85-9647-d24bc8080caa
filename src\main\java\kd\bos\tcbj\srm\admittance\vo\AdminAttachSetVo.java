package kd.bos.tcbj.srm.admittance.vo;

/**
 * @package: kd.bos.tcbj.srm.admittance.vo.AdminAttachSetVo
 * @className AdminAttachSetVo
 * @author: hst
 * @createDate: 2024/07/10
 * @description: 资质类型设置实体类
 * @version: v1.0
 */
public class AdminAttachSetVo {

    // 资质类型
    private String type;
    // 资质名称
    private String name;
    // 匹配字段
    private String matchField;
    // 资料补充函附件字段名
    private String attachField;
    // 发证日期字段名
    private String effectName;
    // 有效日期字段名
    private String unEffectName;
    // 附件库元数据标识
    private String entityName;
    // 附件库附件分录标识
    private String entryName;
    // 附件库分类字段名
    private String typeName;
    // 附件库资质类型字段名
    private String aptitudeType;
    // 附件库资质名称字段名
    private String aptitudeName;
    // 附件库资质类型枚举值
    private String aptitudeEnum;
    // 附件库附件字段名
    private String attachName;
    // 附件库签发日期字段名
    private String isSueName;
    // 附件库有效日期至字段名
    private String dateToName;
    // 重命名取值基础资料字段
    private String reName;
    // 归属基础资料字段名
    private String parentDataField;
    // 源单ID字段
    private String oriBillId;
    // 源分录ID字段
    private String oriEntryId;
    // 路径名称
    private String pathName;
    // 来源单据类型
    private String billType;

    public String getType() {return type;}

    public void setType(String type) {this.type = type;}

    public String getName() {return name;}

    public void setName(String name) {this.name = name;}

    public String getEffectName() {return effectName;}

    public void setEffectName(String effectName) {this.effectName = effectName;}

    public String getUnEffectName() {return unEffectName;}

    public void setUnEffectName(String unEffectName) {this.unEffectName = unEffectName;}

    public String getEntityName() {return entityName;}

    public void setEntityName(String entityName) {this.entityName = entityName;}

    public String getEntryName() {return entryName;}

    public void setEntryName(String entryName) {this.entryName = entryName;}

    public String getTypeName() {return typeName;}

    public void setTypeName(String typeName) {this.typeName = typeName;}

    public String getAptitudeType() {return aptitudeType;}

    public void setAptitudeType(String aptitudeType) {this.aptitudeType = aptitudeType;}

    public String getAptitudeName() {return aptitudeName;}

    public void setAptitudeName(String aptitudeName) {this.aptitudeName = aptitudeName;}

    public String getAptitudeEnum() {return aptitudeEnum;}

    public void setAptitudeEnum(String aptitudeEnum) {this.aptitudeEnum = aptitudeEnum;}

    public String getIsSueName() {return isSueName;}

    public void setIsSueName(String isSueName) {this.isSueName = isSueName;}

    public String getDateToName() {return dateToName;}

    public void setDateToName(String dateToName) {this.dateToName = dateToName;}

    public String getAttachField() {return attachField;}

    public void setAttachField(String attachField) {this.attachField = attachField;}

    public String getAttachName() {return attachName;}

    public void setAttachName(String attchment) {this.attachName = attchment;}

    public String getMatchField() {return matchField;}

    public void setMatchField(String matchField) {this.matchField = matchField;}

    public String getReName() {return reName;}

    public void setReName(String reName) {this.reName = reName;}

    public String getPathName() {return pathName;}

    public void setPathName(String pathName) {this.pathName = pathName;}

    public String getOriBillId() {return oriBillId;}

    public void setOriBillId(String oriBillId) {this.oriBillId = oriBillId;}

    public String getOriEntryId() {return oriEntryId;}

    public void setOriEntryId(String oriEntryId) {this.oriEntryId = oriEntryId;}

    public String getParentDataField() {return parentDataField;}

    public void setParentDataField(String parentDataField) {this.parentDataField = parentDataField;}

    public String getBillType() {return billType;}

    public void setBillType(String billType) {this.billType = billType;}
}
