package kd.bos.tcbj.srm.admittance.webapi; 
import kd.bos.bill.IBillWebApiPlugin;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.db.DB; 
import kd.bos.entity.api.ApiResult;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.util.JSONUtils;  
import java.io.IOException; 
import java.util.Map;

import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

public class OACallbackPlugin implements IBillWebApiPlugin
{

    @Override
    public ApiResult doCustomService(Map<String, Object> params) 
    {
    	
    	 System.out.println("请求参数="+params); 
    	 RequestContext ctx = RequestContext.get(); 
    	 String strCtx=null;
    	  try
    	  {
    		  strCtx=JSONUtils.toString(ctx);
		  } catch (IOException e) 
    	  { 
			e.printStackTrace();
		  }
    	  
    	  System.out.println("ctx="+strCtx);
    	 long userId= UserServiceHelper.getCurrentUserId();
    	 Map userMap=UserServiceHelper.getUserInfoByID(userId);
    	 System.out.println("当前用户:"+userMap); 
    	  String ids= testSaveBill();
    	  OAResult ret=new OAResult();
    	  ret.setStatus("200");
    	  ret.setError("no error");
    	  ret.setData("保存数据,"+ids);
         return ret;
    }
    
    
    String testSaveBill()
    {
    	DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject("yd_matcat");
    	newObj.set("id", DB.genLongId(""));
    	String num=RandomStringUtils.randomAlphabetic(10);
    	newObj.set("number", "编码"+num);
    	newObj.set("name", "名称"+num);
    	Object[] ids=SaveServiceHelper.save(new DynamicObject[] {newObj}); 
    	return StringUtils.join(ids,",");
    	
    }

}
 
