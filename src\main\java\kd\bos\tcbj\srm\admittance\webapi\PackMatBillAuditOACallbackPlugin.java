package kd.bos.tcbj.srm.admittance.webapi;

import java.util.Date;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import kd.bos.bill.IBillWebApiPlugin;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.db.DB;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.api.WebApiContext;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.PackmatApplyHelper;

/**
 * OA包装试机物料流程审核时回调接口
 * @auditor yanzuwei
 * @date 2022年8月15日
 * 
 */
public class PackMatBillAuditOACallbackPlugin implements IBillWebApiPlugin {
	
	@Override
	public String getVersion() {
		return "2.0";
	}
	
	@Override
	public ApiResult doCustomService(WebApiContext ctx) {
		Map<String, Object> dataMap = ctx.getQueryString();
		String jsonStr = dataMap.get("jsonStr").toString();
		
//		System.out.println("请求参数："+JsonUtils.toJsonString(ctx));
		String ids = saveBill(jsonStr);
		OAResult ret = new OAResult();
		ret.setStatus("200");
		ret.setError("no error");
		ret.setData("保存成功的单据ID："+ids);
		return ret;
	}
    
    String saveBill(String jsonStr)
    {
 
    	return ""+PackmatApplyHelper.saveBill(jsonStr);
    	  
    	
    }
}
