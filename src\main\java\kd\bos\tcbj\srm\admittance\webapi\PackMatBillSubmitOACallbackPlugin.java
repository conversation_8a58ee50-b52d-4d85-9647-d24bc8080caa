package kd.bos.tcbj.srm.admittance.webapi;

import java.util.Date;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import kd.bos.bill.IBillWebApiPlugin;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.db.DB;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.api.WebApiContext;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * OA包装试机物料流程提交时回调接口
 * @auditor yanzuwei
 * @date 2022年8月15日
 * 
 */
public class PackMatBillSubmitOACallbackPlugin implements IBillWebApiPlugin {
	
	@Override
	public String getVersion() {
		return "2.0";
	}
	
	@Override
	public ApiResult doCustomService(WebApiContext ctx) {
		Map<String, Object> dataMap = ctx.getQueryString();
		String jsonStr = dataMap.get("jsonStr").toString();
		
//		System.out.println("请求参数："+JsonUtils.toJsonString(ctx));
		String ids = saveBill(jsonStr);
		OAResult ret = new OAResult();
		ret.setStatus("200");
		ret.setError("no error");
		ret.setData("保存成功的单据ID："+ids);
		return ret;
	}
    
    String saveBill(String jsonStr)
    {
    	DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject("yd_packmatcommissbill");
    	newObj.set("id", DB.genLongId(""));
    	String num=DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS");
    	newObj.set("billno", "编码"+num);
    	newObj.set("yd_oadata", "OA表单数据");
    	newObj.set("yd_oadata_tag", jsonStr);
    	newObj.set("billstatus", "A");
    	newObj.set("yd_oacallbackstate", "已提交");  // OA单据状态-对应准入节点编码
    	Object[] ids=SaveServiceHelper.save(new DynamicObject[] {newObj}); 
    	return StringUtils.join(ids,",");
    	
    }
}
