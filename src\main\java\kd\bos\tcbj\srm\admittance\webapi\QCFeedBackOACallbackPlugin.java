package kd.bos.tcbj.srm.admittance.webapi;

import kd.bos.bill.IBillWebApiPlugin;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.db.DB;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.api.WebApiContext;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.JsonUtils;
import kd.fi.cas.helper.OperateServiceHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * OA供应商产品质量反馈单回调接口
 * @auditor hst
 * @date 2022/11/14
 *
 */
public class QCFeedBackOACallbackPlugin implements IBillWebApiPlugin {
    @Override
    public String getVersion() {
        return "2.0";
    }

    @Override
    public ApiResult doCustomService(WebApiContext ctx) {
        Map<String, Object> dataMap = ctx.getQueryString();
        String jsonStr = dataMap.get("jsonStr").toString();
//        String jsonStr = JsonUtils.toJsonString(ctx.getData());
        String ids = saveBill(jsonStr);
        OAResult ret = new OAResult();
        ret.setStatus("200");
        ret.setError("no error");
        ret.setData("保存成功的单据ID："+ids);
        return ret;
    }

    String saveBill(String jsonStr)
    {
        DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject("yd_scp_qcfeedback");
        newObj.set("id", DB.genLongId(""));
        String num= DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS");
        newObj.set("billno", "编码"+num);
        newObj.set("yd_oadata", "OA表单数据");
        newObj.set("yd_oadata_tag", jsonStr);
        newObj.set("billstatus", "A");
        newObj.set("createtime", new Date());
        Object[] ids=SaveServiceHelper.save(new DynamicObject[] {newObj});
        return StringUtils.join(ids,",");

    }
}
