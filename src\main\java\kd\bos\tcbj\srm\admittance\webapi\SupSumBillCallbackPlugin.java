package kd.bos.tcbj.srm.admittance.webapi;

import kd.bos.openapi.common.custom.annotation.ApiController;
import kd.bos.openapi.common.custom.annotation.ApiMapping;
import kd.bos.openapi.common.custom.annotation.ApiParam;
import kd.bos.openapi.common.custom.annotation.ApiPostMapping;
import kd.bos.openapi.common.result.CustomApiResult;
import kd.bos.tcbj.srm.admittance.helper.SupSumBillHelper;

import java.io.Serializable;

/**
 * @package（包）: kd.bos.tcbj.srm.admittance.webapi.SupSumBillCallbackPlugin
 * @className（类名称）: SupSumBillCallbackPlugin
 * @description（类描述）: 包材合格供应商接口
 * @author（创建人）: hst
 * @createDate（创建时间）: 2024/05/25
 * @version（版本）: v1.0
 */
@ApiController(value = "srm", desc = "供应商管理")
@ApiMapping(value = "supSumBill")
public class SupSumBillCallbackPlugin implements Serializable {

    /**
     * 临时采购申请
     * @author: hst
     * @createDate: 2024/05/25
     * @param jsonStr
     * @return
     */
    @ApiPostMapping(value = "tempPurApply", desc = "创建临时采购申请")
    public CustomApiResult<Object> tempPurApply (@ApiParam("jsonStr") String jsonStr) {
        return SupSumBillHelper.tempPurApply(jsonStr);
    }
}
