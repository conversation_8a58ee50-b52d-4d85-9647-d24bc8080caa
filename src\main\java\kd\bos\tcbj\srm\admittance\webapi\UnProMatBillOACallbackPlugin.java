package kd.bos.tcbj.srm.admittance.webapi; 

import kd.bos.bill.IBillWebApiPlugin;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.db.DB; 
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.api.WebApiContext;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * OA非生产物料试机流程回调接口
 * <AUTHOR>
 * @date 2022-07-28
 */
public class UnProMatBillOACallbackPlugin implements IBillWebApiPlugin
{
	
	@Override
	public String getVersion() {
		return "2.0";
	}
	
	@Override
	public ApiResult doCustomService(WebApiContext ctx) {
		Map<String, Object> dataMap = ctx.getQueryString();
		String jsonStr = dataMap.get("jsonStr").toString();
		
//		System.out.println("请求参数："+JsonUtils.toJsonString(ctx));
		String ids = saveBill(jsonStr);
		OAResult ret = new OAResult();
		ret.setStatus("200");
		ret.setError("no error");
		ret.setData("保存成功的单据ID："+ids);
		return ret;
	}
    
    String saveBill(String jsonStr)
    {
    	DynamicObject newObj = BusinessDataServiceHelper.newDynamicObject("yd_unpromatcommissbill");
    	newObj.set("id", DB.genLongId(""));
    	String num=DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS");
    	newObj.set("billno", "编码"+num);
    	newObj.set("yd_oadata", "OA表单数据");
    	newObj.set("yd_oadata_tag", jsonStr);
    	newObj.set("billstatus", "A");
    	Object[] ids=SaveServiceHelper.save(new DynamicObject[] {newObj}); 
    	return "编码"+num;
    	
    }

}
 
