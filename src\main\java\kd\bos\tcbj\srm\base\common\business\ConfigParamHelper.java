package kd.bos.tcbj.srm.base.common.business;

import com.alibaba.fastjson.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.base.plugin.AbstractParamShowPlugin
 * @className AbstractParamShowPlugin
 * @author: hst
 * @createDate: 2025/02/18
 * @description: 费用核算参数配置公共插件类
 * @version: v1.0
 */
public class ConfigParamHelper {

    /**
     * 获取参数配置信息
     * @param code
     * @return
     * @author: hst
     * @createDate: 2025/02/18
     */
    public static Object getConfigParam (String code, String name) {
        // 获取对应的节点信息
        DynamicObject node = BusinessDataServiceHelper.loadSingle("yd_srm_param_tree",
                new QFilter("yd_model",QFilter.equals,code).toArray());

        if (Objects.nonNull(node)) {
            // 获取参数配置信息
            DynamicObject config = BusinessDataServiceHelper.loadSingle("yd_srm_param_setting",
                    new QFilter("yd_nodeid",QFilter.equals,node.getString("yd_seq")).toArray());

            if (Objects.nonNull(config)) {
                return getConfigParam(config, name);
            } else {
                throw new KDBizException("获取不到参数配置，请联系管理员！");
            }
        } else {
            throw new KDBizException("获取不到参数配置节点信息，请联系管理员！");
        }
    }

    /**
     * 构造参数配置信息
     * @param config
     * @param name
     * @return
     * @author: hst
     * @createDate: 2024/02/23
     */
    private static Object getConfigParam (DynamicObject config, String name) {
        String paramStr = config.getString("yd_params_tag");

        JSONObject jsonParam = JSONObject.parseObject(paramStr);
        if (jsonParam.containsKey(name)) {
            return jsonParam.get(name);
        }

        return null;
    }
}
