package kd.bos.tcbj.srm.base.plugin;

import kd.bos.dataentity.serialization.SerializationUtils;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.EventObject;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.base.plugin.SaloutParamFormPlugin
 * @className SaloutParamFormPlugin
 * @author: hst
 * @createDate: 2025/02/18
 * @description: 销售发货参数配置动态表单插件
 * @version: v1.0
 */
public class SaloutParamFormPlugin extends AbstractParamShowPlugin {

    /**
     * 新建数据包完毕后，触发此事件
     * @param e
     * @author: hst
     * @createDate: 2024/02/22
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        this.setDefaultValue();
    }

    /**
     * 加载默认数据
     * @author: hst
     * @createDate: 2024/02/23
     */
    private void setDefaultValue() {
        String paramJson = super.getParamSettings();
        if (StringUtils.isNotEmpty(paramJson)) {
            Map paramMap = SerializationUtils.fromJsonString(paramJson, Map.class);
            if (paramMap.containsKey("yd_warehouse")) {
                this.getModel().setValue("yd_warehouse", ((Map) paramMap.get("yd_warehouse")).get("id"));
            }
            if (paramMap.containsKey("yd_weburl")) {
                this.getModel().setValue("yd_weburl", paramMap.get("yd_weburl"));
            }
        }
    }
}
