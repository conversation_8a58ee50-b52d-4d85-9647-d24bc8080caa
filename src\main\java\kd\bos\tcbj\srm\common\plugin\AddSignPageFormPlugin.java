package kd.bos.tcbj.srm.common.plugin;

import kd.bos.form.events.CustomEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.workflow.taskcenter.plugin.validate.BeforeAddSignConfirmCustomEvent;

import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.common.plugin.AddSignPageFormPlugin
 * @className: AddSignPageFormPlugin
 * @description: 发起加签页面插件，设置允许上传附件
 * @author: hst
 * @createDate: 2024/11/17
 * @version: v1.0
 */
public class AddSignPageFormPlugin extends AbstractFormPlugin {

    @Override
    public void customEvent(CustomEventArgs e) {
        super.customEvent(e);
        if (Objects.nonNull(e) && BeforeAddSignConfirmCustomEvent.SELECT_ADDSIGNCONFIRM_ISOLATION.equals(e.getKey())
                && e instanceof BeforeAddSignConfirmCustomEvent) {
            BeforeAddSignConfirmCustomEvent bac = (BeforeAddSignConfirmCustomEvent) e;
            Map<String, Object> extProps = bac.getExtProps();
            /* 允许上传流程附件 */
            extProps.put("allowuploadprocattach", true);
        }
    }
}
