package kd.bos.tcbj.srm.oabill.helper;

import java.util.*;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.oabill.imp.OABillManageMserviceImpl;
import kd.bos.tcbj.srm.oabill.mservice.OABillManageMservice;
import kd.bos.tcbj.srm.oabill.utils.OABillManageUtil;

/**
 * @auditor yanzuwei
 * @date 2022年7月28日
 * 
 */
public class OABillManageHelper {
	/**
	 * 描述：解析OA的非生产物料试机申请单单据数据并更新非生产供应商台账
	 * 
	 * @createDate  : 2022-07-28
	 * @author: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param idSet 接口参数
	 * @return 是否成功
	 */
	public static ApiResult updateUnProSupBill(Set<String> idSet) {
		OABillManageMservice oaBillManageMservice = OABillManageMserviceImpl.getInstance();
		return oaBillManageMservice.updateUnProSupBill(idSet);
	}

	/**
	 * 描述：解析OA的非生产物料供应商新增/变更申请数据
	 * @author: hst
	 * @createDate  : 2022/08/29
	 * @param ids
	 * @return 是否成功
	 */
	public static ApiResult analysiUnProScpBill(List<String> ids) {
		OABillManageMservice oaBillManageMservice = OABillManageMserviceImpl.getInstance();
		return oaBillManageMservice.analysiUnProScpBill(ids);
	}

	/**
	 * 描述：非生产物料供应商新增/变更申请数据更新非生产供应商台账
	 * @author: hst
	 * @createDate  : 2022/08/29
	 * @param ids
	 */
	public static void updateUnProSupSumNill(List<String> ids) {
		List<DynamicObject> applyList = new ArrayList<>();
		List<DynamicObject> billList = new ArrayList<>();
		String applyFields = "yd_suppliername,yd_companyaddress,yd_taxrate,yd_depositbank,yd_bankaccount," +
				"yd_payway,yd_odmchartervalid,yd_odmqsvaild,yd_beupdatedbook";
		String billFields = "yd_supname,yd_supaddress,yd_taxrate,yd_bankname,yd_recaccount,yd_paytype,yd_businesslicensedate," +
				"yd_productlicensedate";
		DynamicObject[] applys = BusinessDataServiceHelper.load("yd_unproscpbill",applyFields,
				new QFilter[]{new QFilter("id",QFilter.in,ids)});
		Arrays.asList(applys).stream().forEach(apply -> {
			String supplierName = apply.getString("yd_suppliername");
			// update by hst 2023/10/17 修改过滤字段
			DynamicObject[] unProSupSumBills = BusinessDataServiceHelper.load("yd_unprosupsumbill", billFields,
					new QFilter[]{new QFilter("yd_supplier.name",QFilter.equals,supplierName)});
			for (DynamicObject unProSupSumBill : unProSupSumBills) {
				Map<String,Object> mapping = new HashMap<>();
				mapping.put("yd_supaddress",apply.get("yd_companyaddress"));
				mapping.put("yd_taxrate",apply.get("yd_taxrate"));
				mapping.put("yd_bankname",apply.get("yd_depositbank"));
				mapping.put("yd_recaccount",apply.get("yd_bankaccount"));
				mapping.put("yd_paytype",apply.get("yd_payway"));
				mapping.put("yd_businesslicensedate",apply.get("yd_odmchartervalid"));
				mapping.put("yd_productlicensedate",apply.get("yd_odmqsvaild"));
				OABillManageUtil.updateField(unProSupSumBill,mapping);
				billList.add(unProSupSumBill);
			}
			if (unProSupSumBills.length > 0) {
				apply.set("yd_beupdatedbook", true);
				applyList.add(apply);
			}
		});
		if (billList.size() > 0) {
			SaveServiceHelper.save(billList.toArray(new DynamicObject[billList.size()]));
		}
		if (applyList.size() > 0) {
			SaveServiceHelper.save(applyList.toArray(new DynamicObject[applyList.size()]));
		}
	}

	/**
	 * 描述：解析质量反馈单数据
	 * @author: hst
	 * @createDate  : 2022/11/24
	 * @param ids
	 * @return 是否成功
	 */
	public static ApiResult analysiQCFeedBack(List<String> ids,String userName) {
		OABillManageMservice oaBillManageMservice = OABillManageMserviceImpl.getInstance();
		return oaBillManageMservice.analysiQCFeedBack(ids,userName);
	}
}
