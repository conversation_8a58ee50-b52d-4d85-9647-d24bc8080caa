package kd.bos.tcbj.srm.oabill.helper;


import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.coderule.api.CodeRuleInfo;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.coderule.CodeRuleServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.taxc.common.util.StringUtil;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.*;

/**
 * @package kd.bos.tcbj.srm.oabill.helper.PackMatCommissHelper
 * @className PackMatCommissHelper
 * @author: hst
 * @createDate: 2022/08/26
 * @version: v1.0
 */
public class PackMatCommissHelper {

    public final static String ENTITY_MAIN = "yd_packmatcommissbill";
    public final static String ENTITY_ACCOUNT = "yd_packsupsumbill";
    public final static String ENTITY_SUPENTRY = "yd_trysupentry";
    public final static String ENTITY_MATENTRY = "yd_tm_matentry";
    public final static String ENTITY_SUPPLIER = "bd_supplier";
    public final static String ENTITY_MATERIAL = "bd_material";
    public final static String FIELD_ID = "id";
    public final static String FIELD_BEUPDATE = "yd_beupdatedbook";
    public final static String FIELD_NOTEXISTMAT = "yd_notexistmat";
    public final static String FIELD_NOTEXTISPRO = "yd_notexistpro";
    public final static String FIELD_SUPPLIER = "yd_supplier";
    public final static String FIELD_BILLSTATUS = "billstatus";
    public final static String COLUMN_MANUFACTURER = "yd_trysupentry.yd_scm_manufacturer";
    public final static String COLUMN_SCMDEALER = "yd_trysupentry.yd_scm_dealer";
    public final static String COLUMN_SCMMATTYPE = "yd_trysupentry.yd_scm_mattype";
    public final static String COLUMN_MATNUM = "yd_tm_matentry.yd_tme_matnum";
    public final static String COLUMN_MATNAME = "yd_tm_matentry.yd_tme_matname";
    public final static String COLUMN_TMESTRUC = "yd_tm_matentry.yd_tme_struc";
    public final static String COLUMN_TMEFORMCOMP = "yd_tm_matentry.yd_tme_formucomp";
    public final static String COLUMN_PROBRIEF = "yd_tm_matentry.yd_tme_probrief";
    public final static String COLUMN_TMESIZE = "yd_tm_matentry.yd_tme_size";
    public final static String COLUMN_TMETN = "yd_tm_matentry.yd_tme_tn";
    public final static String COLUMN_TMEWT = "yd_tm_matentry.yd_tme_wt";
    public final static String COLUMN_TMETOUCH = "yd_tm_matentry.yd_tme_touch";
    public final static String MATFIELDS = "id, yd_tm_matentry.yd_tme_matnum, yd_tm_matentry.yd_tme_matname, " +
            "yd_tm_matentry.yd_tme_struc, yd_tm_matentry.yd_tme_probrief, yd_tm_matentry.yd_tme_size, yd_tm_matentry.yd_tme_tn, " +
            "yd_tm_matentry.yd_tme_wt, yd_tm_matentry.yd_internalcontrol, yd_tm_matentry.yd_suppack, yd_tm_matentry.yd_tme_formucomp" +
            ", yd_tm_matentry.yd_tme_pro, yd_tm_matentry.yd_tme_sup, yd_tm_matentry.yd_supstatus, yd_tm_matentry.yd_period, " +
            "yd_tm_matentry.yd_isenter";
    /**
     * 解析包装物料试机打样申请单，更新台账
     * @author: hst
     * @createDate: 2022/08/26
     * @param Ids
     */
    public static void updatePacksUpSumBill(List<String> Ids) {
        List<DynamicObject> commissList = new ArrayList<>();
        List<DynamicObject> accountList = new ArrayList<>();
        //包材供应商台账字段
        String accountFields = "billno,yd_matno,yd_matname,yd_supplier,yd_material,billstatus,yd_texture,yd_formula,yd_process," +
                "yd_size,yd_thickness,yd_weight,yd_betouch,yd_agentsup,yd_matlevel,creator,auditor,yd_supstatus,yd_supname," +
                "yd_matlevel,yd_supaddress,yd_agentprintlicense,yd_businesslicense,yd_productionlicense,yd_printlicense," +
                "yd_agentbusinesslicense,yd_agentproductlicense,yd_agentprintlicense,yd_paperreq,yd_supexecutereq_tag," +
                "yd_supexecutereq,yd_incontrolreq_tag,yd_incontrolreq";
        DataSet matDataSet = QueryServiceHelper.queryDataSet("PackMatCommissHelper",ENTITY_MAIN,MATFIELDS,
                new QFilter[]{new QFilter(FIELD_ID,QFilter.in,Ids.toArray(new String[Ids.size()]))
                        , new QFilter("yd_tm_matentry.yd_isenter", QFilter.equals, "是")},null);
        for (Row row : matDataSet) {
            DynamicObject matCommiss = BusinessDataServiceHelper.loadSingle(row.get(FIELD_ID),ENTITY_MAIN);
            String supname = row.get("yd_tm_matentry.yd_tme_sup") != null ? row.get("yd_tm_matentry.yd_tme_sup").toString() : "";
            String matno = row.get("yd_tm_matentry.yd_tme_matnum") != null ? row.get("yd_tm_matentry.yd_tme_matnum").toString() : "";
            //查看生产商F7是否存在
            DynamicObject supplier = BusinessDataServiceHelper.loadSingle(ENTITY_SUPPLIER,"id,number,name",
                    new QFilter[]{new QFilter("name",QFilter.equals, supname)});
            Map<String,Object> map = new HashMap<>();
            if (supplier == null) {
                matCommiss.set(FIELD_NOTEXTISPRO,true);
            } else {
                map.put("yd_supplier",supplier);
            }
            //查看物料F7是否存在
            DynamicObject material = BusinessDataServiceHelper.loadSingle(ENTITY_MATERIAL,"id,number,name",
                    new QFilter[]{new QFilter("number",QFilter.equals, matno)});
            // update by hst 2023/10/17 若通过物料编码查询不到，则通过助记码查询
            if (material == null) {
                material = BusinessDataServiceHelper.loadSingle(ENTITY_MATERIAL,"id,number,name",
                        new QFilter[]{new QFilter("helpcode",QFilter.equals, matno)});
            }
            if (material == null) {
                matCommiss.set(FIELD_NOTEXISTMAT,true);
            } else {
                map.put("yd_material",material);
            }
            // update by hst 2023/10/17 匹配生产商
            map.put("yd_producer",BusinessDataServiceHelper.loadSingle("yd_srmproducers","id,number,name",
                    new QFilter[]{new QFilter("name",QFilter.equals, row.get("yd_tm_matentry.yd_tme_pro"))}));
            // update by hst 2024/04/23 调整匹配条件
            if (Objects.nonNull(supplier) && Objects.nonNull(material)) {
                QFilter supplierFilter = new QFilter("yd_supplier.number",QFilter.equals,supplier.getString("number"));
                // update by hst 2023/12/05 使用物料基础资料编码进行匹配
                QFilter materialFilter = new QFilter("yd_material.number",QFilter.equals,material.getString("number"));
                DynamicObject account = BusinessDataServiceHelper.loadSingle(ENTITY_ACCOUNT, accountFields, new QFilter[]{supplierFilter, materialFilter});
                if (account == null) {
                    account = BusinessDataServiceHelper.newDynamicObject(ENTITY_ACCOUNT);
                    CodeRuleInfo codeRule = CodeRuleServiceHelper.getCodeRule(account.getDataEntityType().getName(), account, null);
                    map.put("billno", CodeRuleServiceHelper.getNumber(codeRule, account));
                    map.put("yd_adddate", new Date());
                    map.put("yd_material", material);
                    map.put("yd_supplier", supplier);
                }
                map.put("yd_matno", row.get(COLUMN_MATNUM));
                map.put("yd_matname", row.get(COLUMN_MATNAME));
                // update by hst 2023/12/05 新增物料基础资料
                map.put("yd_material", material.getPkValue());
                // 截取物料编码版本号前的编码
                if (StringUtils.isNotBlank(matno) && "-".indexOf(matno) > -1) {
                    map.put("yd_materialno", matno.substring(0,"-".indexOf(matno)));
                } else {
                    map.put("yd_materialno", matno);
                }
                map.put("billstatus", "C");
                map.put("creator", RequestContext.get().getUserId());
                map.put("auditor", RequestContext.get().getUserId());
                map.put("yd_texture", row.get("yd_tm_matentry.yd_tme_struc"));
                map.put("yd_formula", row.get("yd_tm_matentry.yd_tme_formucomp"));
                map.put("yd_process", row.get("yd_tm_matentry.yd_tme_probrief"));
                map.put("yd_size", row.get("yd_tm_matentry.yd_tme_size"));
                map.put("yd_thickness", row.get("yd_tm_matentry.yd_tme_tn"));
                map.put("yd_weight", row.get("yd_tm_matentry.yd_tme_wt"));
                map.put("yd_supstatus", row.get("yd_tm_matentry.yd_supstatus"));
                //根据生厂商获取最新一张《包材供应商新增/变更申请》
                DataSet packsUpApplys = QueryServiceHelper.queryDataSet("PackMatCommissHelper", PacksUpApplyBillHelper.MAINENTITY,
                        PacksUpApplyBillHelper.FIELDS, new QFilter[]{new QFilter(PacksUpApplyBillHelper.COLUMN_PRONAME, QFilter.equals,
                                supname)}, PacksUpApplyBillHelper.FIELD_CREATEDATE + " desc");
                DataSet applys = packsUpApplys.top(1);
                for (Row apply : applys) {
                    map.put("yd_supaddress", apply.get(PacksUpApplyBillHelper.COLUMN_PROADDRESS));
                    map.put("yd_businesslicense", apply.get(PacksUpApplyBillHelper.FIELD_ODMCHAVALID));
                    map.put("yd_productionlicense", apply.get(PacksUpApplyBillHelper.FIELD_ODMQSVAILD));
                    map.put("yd_printlicense", apply.get(PacksUpApplyBillHelper.FIELD_ODMPRIVAILD));
                    map.put("yd_agentbusinesslicense", apply.get(PacksUpApplyBillHelper.FIELD_AGNTCHAVAILD));
                    map.put("yd_agentproductlicense", apply.get(PacksUpApplyBillHelper.FIELD_AGNTLTOVAILD));
                    map.put("yd_agentprintlicense", apply.get(PacksUpApplyBillHelper.FIELD_AGNTPCVAILD));
                    map.put("yd_paperreq", apply.get(PacksUpApplyBillHelper.FIELD_MFGRSWQVAILD));
                    String applyId = apply.getString(PacksUpApplyBillHelper.FIELD_ID);
                    //根据生产商及物料代码维度获取《包材供应商新增/变更申请》
                    DataSet wareDetail = QueryServiceHelper.queryDataSet("PackMatCommissHelper", PacksUpApplyBillHelper.MAINENTITY, PacksUpApplyBillHelper.FIELDS,
                            new QFilter[]{new QFilter(PacksUpApplyBillHelper.FIELD_ID, QFilter.equals, applyId),
                                    new QFilter(PacksUpApplyBillHelper.COLUMN_WARECODE, QFilter.equals, matno)}, null);
                    if (wareDetail.hasNext()) {
                        Row ware = wareDetail.next();
                        map.put("yd_supexecutereq_tag", ware.getString(PacksUpApplyBillHelper.COLUMN_WARECRITERION));
                        map.put("yd_incontrolreq_tag", ware.getString(PacksUpApplyBillHelper.COLUMN_WARESTANDARD));
                    }
                }
                updateField(account, map);
                accountList.add(account);
                matCommiss.set(FIELD_BEUPDATE,true);
            }
            commissList.add(matCommiss);
        }
        if (accountList.size() > 0) {
            SaveServiceHelper.saveOperate(ENTITY_ACCOUNT,
                    accountList.toArray(new DynamicObject[accountList.size()]));
        }
        if (commissList.size() > 0) {
            SaveServiceHelper.save(commissList.toArray(new DynamicObject[commissList.size()]));
        }
    }

    /**
     * 更新元数据字段，为空不更新
     * <AUTHOR>
     * @createDate 2022/8/26
     * @param dynamicObject
     * @param map
     */
    public static void updateField (DynamicObject dynamicObject, Map<String,Object> map) {
        for (Map.Entry<String,Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                Object value = entry.getValue();
                if (!"/".equals(value) && !"NA".equals(value) && !"-".equals(value)) {
                    if (entry.getKey().endsWith("_tag")) {
                        dynamicObject.set(entry.getKey(), entry.getValue());
                        dynamicObject.set(entry.getKey().substring(0, entry.getKey().lastIndexOf("_tag")), entry.getValue().toString().length() > 20 ?
                                entry.getValue().toString() + "..." : entry.getValue().toString());
                    } else {
                        dynamicObject.set(entry.getKey(), entry.getValue());
                    }
                }
            }
        }
    }
}
