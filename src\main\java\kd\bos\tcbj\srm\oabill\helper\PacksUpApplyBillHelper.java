package kd.bos.tcbj.srm.oabill.helper;

import com.google.gson.Gson;

import kd.bos.algo.DataSet;
import kd.bos.algo.JoinDataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicProperty;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.operate.Save;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.ImportBillUtils;
import kd.bos.tcbj.srm.oabill.imp.OABillManageMserviceImpl;
import kd.bos.tcbj.srm.oabill.utils.OABillManageUtil;
import kd.taxc.common.util.StringUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.oabill.helper.PacksUpApplyBillHelper
 * @className PacksUpApplyBillHelper
 * @author: hst
 * @createDate: 2022/08/23
 * @version: v1.0
 */
public class PacksUpApplyBillHelper {

    private final static Log logger = LogFactory.getLog(PacksUpApplyBillHelper.class);
    public final static String MAINENTITY = "yd_packsupapplybill";
    public final static String ENTITY_PACKSUPSUM = "yd_packsupsumbill";
    public final static String ENTITY_PACKWARES = "yd_packswares_en";
    public final static String ENTITY_PRODETAIL = "yd_packsdetail_en";
    public final static String COLUMN_PRONAME = "yd_packsdetail_en.yd_packsno";
    public final static String COLUMN_PROADDRESS = "yd_packsdetail_en.yd_packsaddress";
    public final static String COLUMN_WARECODE = "yd_packswares_en.yd_warescode";
    public final static String COLUMN_WARENAME = "yd_packswares_en.yd_waresname";
    public final static String COLUMN_WARES = "yd_wares";
    public final static String COLUMN_WARECRITERION = "yd_packswares_en.yd_warescriterion";
    public final static String COLUMN_WARESTANDARD = "yd_packswares_en.yd_waresstandard";
    public final static String FIELD_ID = "id";
    public final static String FIELD_ODMCHAVALID = "yd_odmchartervalid";
    public final static String FIELD_ODMQSVAILD = "yd_odmqsvaild";
    public final static String FIELD_ODMPRIVAILD = "yd_odmprintvaild";
    public final static String FIELD_AGNTCHAVAILD = "yd_agntchartervaild";
    public final static String FIELD_AGNTLTOVAILD = "yd_agntltovaild";
    public final static String FIELD_AGNTPCVAILD = "yd_agntpcvaild";
    public final static String FIELD_BEUPDATE = "yd_beupdatedbook";
    public final static String FIELD_MFGRSWQVAILD = "yd_mfgrswqvaild";
    public final static String FIELD_CREATEDATE = "createtime";
    public final static String FIELD_BEUSED = "yd_beused";
    public final static String FIELD_OADATA = "yd_oadata_tag";
    public final static String FIELDS =  FIELD_ID + "," + COLUMN_PRONAME  + "," + COLUMN_PROADDRESS +
        "," + FIELD_ODMCHAVALID + "," + FIELD_ODMQSVAILD + "," + FIELD_ODMPRIVAILD + "," + FIELD_AGNTCHAVAILD + "," +
        FIELD_AGNTLTOVAILD + "," + FIELD_AGNTPCVAILD + "," + COLUMN_WARECODE + "," + FIELD_MFGRSWQVAILD + "," +
         COLUMN_WARECRITERION + "," +  COLUMN_WARESTANDARD + "," + FIELD_BEUPDATE;



    /**
     * 设置字段关联映射
     * <AUTHOR>
     * @createDate 2022/8/23
     */
    public static Object[][] getOAFieldMapping() {
        Object[][] mapping = new Object[][]{
                {"fd_3ae47aa8fc6f3e","yd_packsdetail_en"},            //
                {"fd_3ae6120733e634","yd_packsno"},                    //生产商名称
                {"fd_3ae6120b5fb802","yd_packsname"},                //经销商名称
                {"fd_3ae6120e33b5e0","yd_packsaddress"},             //生产商地址
                {"fd_3ae47b7ef002f8","yd_qualifysupplier"},         //是否为现有合格生产商
                {"fd_3ae47b8a1b6fae","yd_qualifydistributor"},      //是否为现有合格生产商
                {"fd_3ae47ba3b65bb6","yd_packswares_en"},              //
                {"fd_3ae47c382bed36","yd_waresclass"},                //物料品类
                {"fd_3ae47c3a8e1c98","yd_warescode"},                //物料代码
                {"fd_3ae47c3bb19a12","yd_waresname"},                 //物料名称
                {"fd_3ae47c3c9d688a","yd_warescriterion"},           //厂家执行标准
                {"fd_3ae47c3d8cf0e8","yd_waresmaterial"},            //材质与结构
                {"fd_3ae47c3e98213c","yd_waresformula"},             //配方组分
                {"fd_3ae47c419a8fc6","yd_waresperiod"},              //保质期
                {"fd_3ae47c43dc456c","yd_waresstandard"},               //我司供货质量标准（编号+名称）
                {"fd_3ae47ed6921da8","yd_modifycategory"},           //新增/变更类别
                {"fd_3ae47f35678fcc","yd_modifyreason_tag"},         //新增变更原因
                {"","yd_modifyreason"},                                 //新增变更原因
                {"fd_3ae47fc90151dc","yd_materiallevel"},            //物料级别
                {"fd_3ae47feb8c2452","yd_controltype"},              //管控类型
                {"fd_3ae4aa22d805fe","yd_siteaudit"},                //是否需要现场审核
                {"fd_3ae481f528f6c4","yd_directcontact"},            //是否与产品直接接触
                {"fd_3ae4820301f788","yd_makecopy"},                 //是否抄送股份精益管理部
                {"fd_3ae4821337ca38","yd_feedback"},                 //试机评估反馈
                {"fd_3ae4826cef907c","yd_testdate"},                 //试机完成日期
                {"fd_3ae48641166a60","yd_odmcharterissue"},         //生产商营业执照发证日期
                {"fd_3ae4866ee112b8","yd_odmchartervalid"},         //有效期1
                {"fd_3ae486423404e4","yd_odmqsissue"},              //生产商生产许可发证日期
                {"fd_3ae48670139bbc","yd_odmqsvaild"},              //生产商生产许可有效期
                {"fd_3ae48645a39140","yd_odmprintissue"},           //生产商印刷经营许可证发证日期
                {"fd_3ae4867727dda2","yd_odmprintvaild"},           //生产商印刷经营许可证有效期
                {"fd_3ae61c20bf53ca","yd_agntcharterissue"},        //代理商营业执照发证日期
                {"fd_3ae61c232e9956","yd_agntchartervaild"},        //代理商营业执照有效期
                {"fd_3ae61c21b2209e","yd_agntltoissue"},            //代理商经营许可证发证日期
                {"fd_3ae61c240241b8","yd_agntltovaild"},            //代理商经营许可证有效期
                {"fd_3ae61c2277b6b6","yd_agntpcissue"},             //代理商代理证明发证日期
                {"fd_3ae61c24ed2fc4","yd_agntpcvaild"},             //代理商代理证明有效期
                {"fd_3ae48649e1667c","yd_mfgrswqissue"},           //生产商供应商书面调查表发证日期
                {"fd_3ae4867a958192","yd_mfgrswqvaild"},           //生产商供应商书面调查表有效期
                {"fd_3ae61dbc7086c2","yd_mfgrtpiissue"},           //生产商第三方检验报告发证日期
                {"fd_3ae61dc079317a","yd_mfgrtpivaild"},            //生产商第三方检验报告有效期
                {"fd_3ae61dbd1ee180","yd_mfgrpdfissue"},           //生产商产品工艺流程图发证日期
                {"fd_3ae61dc14df60e","yd_mfgrpdfvaild"},           //生产商产品工艺流程图有效期
                {"fd_3ae61dbde23de8","yd_mfgrcoaissue"},           //生产商3个批次样品COA发证日期
                {"fd_3ae61dc22e9fb2","yd_mfgrcoavaild"},           //生产商3个批次样品COA有效期
                {"fd_3ae61dbeb9d7d0","yd_mfgresissue"},            //生产商厂家企业标准发证日期
                {"fd_3ae61dc32667d6","yd_mfgresvaild"},            //生产商厂家企业标准有效期
                {"fd_3ae61dbf9e894e","yd_mfgrtsrissue"},           //生产商我司检验报告（内包材）发证日期
                {"fd_3ae61dc45a06ba","yd_mfgrtsrvaild"},           //生产商我司检验报告（内包材）有效期
                {"fd_3ae60fe0db1afc","yd_othername_tag"},           //包材相关信息1
                {"","yd_othername"},                                  //包材相关信息1
                {"fd_3ae60fe5c3c15e","yd_othercondition_tag"},     //包材相关信息2
                {"","yd_othercondition"},                             //包材相关信息2
                {"fd_3ae60fe88da072","yd_otherlimit_tag"},          //包材相关信息3
                {"","yd_otherlimit"},                                 //包材相关信息3
                {"fd_3ae60feb19faa0","yd_advantage_tag"},            //优势分析
                {"","yd_advantage"},                                   //优势分析
                {"fd_3ae60fedfb62f6","yd_inferiority_tag"},         //劣势分析
                {"","yd_inferiority"},                                 //劣势分析
                {"fd_3ae4870e1bcfc0","yd_illegalnotice"},           //违法公告
                {"fd_3ae61c10c3c354","yd_writtenassess"},           //书面评估结论
                {"fd_3ae48af60d53dc","yd_sampleresult"},            //样品检测结果
                {"fd_3ae48b0070f69a","yd_sampleopinion"},           //样品检测结果意见
                {"fd_3ae48b0af7887c","yd_standardresult"},          //质量标准评估结果
                {"fd_3ae48b12f0fc2e","yd_standardopinion"},         //质量标准评估结果意见
                {"fd_3ae48b1b2a98ba","yd_methodresult"},            //检验方法评估结果
                {"fd_3ae48b21902d68","yd_methodopinion"},           //检验方法评估结果意见
                {"fd_3ae61dab1d1606","yd_assessverdict"},           //检验评估结论
                {"fd_3ae48d2226e6fe","yd_examineverdict"},          //审核结论（附审核报告）
                {"fd_3ae48d388b941c","yd_contrastverdict"},         //现有合格供应商的优劣势对比结论
                {"fd_3ae48d80ddec54","yd_scienceexamine"},          //科技中心审批
                {"fd_3ae48d87862fce","yd_sciencereason"},           //科技中心审批原因
                {"fd_3ae48d9deffa24","yd_qualityexamine"},           //质量管理部审批
                {"fd_3ae48dacdb295e","yd_qualityreason"},           //质量管理部审批意见
                {"fd_3ae48dc32f4262","yd_chainexamine"},            //供应链中心审批
                {"fd_3ae48dcb0cdf54","yd_chainopinion"},            //供应链中心审批原因
                {"fd_3ae48dd68b8220","yd_supplierdirectory"},      //供应商目录
                {"","yd_beused"}                                      //记录是否已解析字段
        };
        return mapping;
    }

    public static ApiResult analysisOABillData(List<DynamicObject> analyBills, String jsonField) {
        return analysisOABillData(analyBills,jsonField,"");
    }

    public static ApiResult analysisOABillData(List<String> ids) {
//        StringBuffer fields = new StringBuffer();
//        for (Object[] field : getOAFieldMapping()) {
//            fields.append(field[1] + ",");
//        }
//        fields.append(FIELD_OADATA + "," + COLUMN_WARES);
//        DynamicObject[] analyBills = BusinessDataServiceHelper.load(PacksUpApplyBillHelper.MAINENTITY,fields.toString(),
//                new QFilter[]{new QFilter(PacksUpApplyBillHelper.FIELD_BEUSED,QFilter.equals,false)},null);
//        return analysisOABillData(new ArrayList<DynamicObject>(Arrays.asList(analyBills)),FIELD_OADATA,FIELD_BEUSED);
        ApiResult apiResult = new ApiResult();
        apiResult.setSuccess(true);
        apiResult.setMessage("解析成功！");
        try {
            OABillManageUtil.analysisOABillData(ids,PacksUpApplyBillHelper.MAINENTITY,FIELD_OADATA,FIELD_BEUSED);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage());
            apiResult.setSuccess(false);
            apiResult.setMessage(e.getLocalizedMessage()+";");
        }
        return apiResult;
    }

    /**
     * 解析单据上保存的OA系统单据信息
     * <AUTHOR>
     * @createDate 2022/8/23
     * @param analyBills
     * @param jsonField json保存的字段名称
     */
    public static ApiResult analysisOABillData(List<DynamicObject> analyBills, String jsonField, String statusField) {
        ApiResult apiResult = new ApiResult();
        apiResult.setSuccess(true);
        apiResult.setMessage("解析成功！");
        try {
            Object[][] mapping = new Object[0][0];
            for (DynamicObject applybill : analyBills) {
                String jsonStr = applybill.getString(jsonField);
                if (StringUtil.isNotBlank(jsonStr)) {
                    Map oaData = OABillManageUtil.parseJson(jsonStr);
                    String formId = oaData.get("fdTemplateId").toString();
                    Map<String, Object> result = OABillManageUtil.getFieldMapping(formId);
                    if (Objects.isNull(result) || result.size() == 0) {
                        throw new KDBizException("formId:" + formId + "未配置字段映射");
                    } else {
                        Map cosmicData = OABillManageUtil.transMap(oaData);
                        mapping = (Object[][]) result.get("yd_mapping_tag");
                        ImportBillUtils.convertBill(applybill, cosmicData, mapping, null);
                        DynamicObjectCollection wares = (DynamicObjectCollection) applybill.get(ENTITY_PACKWARES);
                        for (DynamicObject ware : wares) {
                            DynamicObject material = BusinessDataServiceHelper.loadSingle("bd_material", "id,number,name",
                                    new QFilter[]{new QFilter("number", QFilter.equals,
                                            ware.getString("yd_warescode"))});
                            ware.set(COLUMN_WARES, material);
                        }
                        if (StringUtil.isNotEmpty(statusField)) {
                            applybill.set(statusField, true);
                        }
                    }
                }
            }
            OABillManageUtil.handleLargeText(mapping,analyBills);
            SaveServiceHelper.save(analyBills.toArray(new DynamicObject[analyBills.size()]));
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage());
            apiResult.setSuccess(false);
            apiResult.setMessage(e.getLocalizedMessage()+";");
        }
        return apiResult;
    }

    /**
     * 解析包材供应商新增/变更申请，更新台账
     * <AUTHOR>
     * @createDate 2022/8/25
     * @param Ids
     */
    public static void updatePacksUpSumBill(List<String> Ids) {
        List<DynamicObject> updateList = new ArrayList<>();
        List<DynamicObject> packsUpSumList = new ArrayList<>();
        //包材供应商台账字段
        String packsUpFields = "yd_supaddress,yd_businesslicense,yd_productionlicense,yd_printlicense,yd_agentbusinesslicense,yd_paperreq," +
                "yd_agentproductlicense,yd_agentprintlicense,yd_incontrolreq,yd_supexecutereq,yd_matno,yd_supexecutereq_tag,yd_incontrolreq_tag";
        DataSet packsUpApplys = QueryServiceHelper.queryDataSet("PacksUpApplyBillHelper",MAINENTITY,FIELDS,
                new QFilter[]{new QFilter(FIELD_ID,QFilter.in,Ids)},null);
        for (Row packsUpApply : packsUpApplys) {
            //根据生产商维度获取包材供应商台账
            // update by hst 2023/10/17 修改过滤字段
            DynamicObject[] packsUpSums = BusinessDataServiceHelper.load(ENTITY_PACKSUPSUM,packsUpFields,new QFilter[]{
                    new QFilter("yd_supplier.name",QFilter.equals,packsUpApply.getString(COLUMN_PRONAME))});
            for(DynamicObject packsUpSum : packsUpSums) {
                updatePacksUpSum(packsUpSum,packsUpApply);
                packsUpSumList.add(packsUpSum);
            }
            if (packsUpSums.length > 0) {
                DynamicObject apply = (BusinessDataServiceHelper.loadSingle(packsUpApply.getString("id"), MAINENTITY));
                apply.set(FIELD_BEUPDATE, true);
                updateList.add(apply);
            }
        }
        if (packsUpSumList.size() > 0) {
            SaveServiceHelper.save(packsUpSumList.toArray(new DynamicObject[packsUpSumList.size()]));
        }
        if (updateList.size() > 0) {
            SaveServiceHelper.save(updateList.toArray(new DynamicObject[updateList.size()]));
        }
    }

    /**
     * 根据包材供应商新增/变更申请单更新包材供应商台账字段
     * <AUTHOR>
     * @createDate 2022/8/26
     * @param packsUpSum
     * @param packsUpApply
     */
    public static void updatePacksUpSum(DynamicObject packsUpSum, Row packsUpApply) {
        Map<String,Object> map = new HashMap<>();
        map.put("yd_supaddress",packsUpApply.get(COLUMN_PROADDRESS));
        map.put("yd_businesslicense",packsUpApply.get(FIELD_ODMCHAVALID));
        map.put("yd_productionlicense",packsUpApply.get(FIELD_ODMQSVAILD));
        map.put("yd_printlicense",packsUpApply.get(FIELD_ODMPRIVAILD));
        map.put("yd_agentbusinesslicense",packsUpApply.get(FIELD_AGNTCHAVAILD));
        map.put("yd_agentproductlicense",packsUpApply.get(FIELD_AGNTLTOVAILD));
        map.put("yd_agentprintlicense",packsUpApply.get(FIELD_AGNTPCVAILD));
        map.put("yd_supexecutereq",packsUpApply.getString(COLUMN_WARECRITERION).length() > 20 ?
                packsUpApply.getString(COLUMN_WARECRITERION).substring(0,20) + "..."
                : packsUpApply.getString(COLUMN_WARECRITERION));
        map.put("yd_supexecutereq_tag",packsUpApply.getString(COLUMN_WARECRITERION));
        map.put("yd_incontrolreq",packsUpApply.getString(COLUMN_WARESTANDARD).length() > 20 ?
                packsUpApply.getString(COLUMN_WARESTANDARD).substring(0,20) + "..."
                : packsUpApply.getString(COLUMN_WARESTANDARD));
        map.put("yd_incontrolreq_tag",packsUpApply.getString(COLUMN_WARESTANDARD));
        map.put("yd_paperreq",packsUpApply.get(FIELD_MFGRSWQVAILD));
        //若台账中的物料代码与当前申请单的物料代码一致
        if (StringUtil.equals(packsUpSum.getString("yd_matno"),packsUpApply.getString(COLUMN_WARECODE))){
            map.put("yd_incontrolreq",packsUpApply.get(COLUMN_WARECRITERION));
            map.put("yd_supexecutereq",packsUpApply.get(COLUMN_WARESTANDARD));
        }
        OABillManageUtil.updateField(packsUpSum,map);
    }
}
