package kd.bos.tcbj.srm.oabill.helper;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.fi.cas.helper.OperateServiceHelper;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper
 * @className QCFeedBackHelper
 * @author: hst
 * @createDate: 2022/11/14
 * @version: v1.0
 */
public class QCFeedBackHelper {

    /* --------------------------- 表头 --------------------------- */
    public final static String MAIN_ENTITY = "yd_scp_qcfeedback";
    public final static String PRODUCER_FIELD = "yd_manufacturer";      //生产商
    public final static String BILLNO_FIELD = "billno";                   //单据编号
    public final static String PROLOT_FIELD = "yd_manufacturerlot";     //厂家批次
    public final static String SUPPLIER_FIELD = "yd_supplier";          //供应商
    public final static String SERIALNO_FIELD = "yd_serialno";          //流水单号
    public final static String SUPSTATUS_FIELD = "yd_supplierstatus";  //供应商确认状态
    public final static String BILLSTATUS_FIELD = "billstatus";         //单据状态
    public final static String CREATEDATE_FIELD = "createtime";         //创建时间
    public final static String PROCESSCYCLE_FILED = "yd_processcycle";  //处理周期
    public final static String ISSEND_FIELD = "yd_issend";               //是否发送供应商
    public final static String ISAUDIT_FILED = "yd_isaudit";             //是否内部审核
    public final static String SUPTYPE_FIELD = "yd_suppliertype";       //供应商类型
    public final static String ISCHECK_FIELD = "yd_ischeck";             //是否纳入质量异常考核
    /* --------------------------- 分录 --------------------------- */
    public final static String MATERIAL_ENTRY = "yd_materialentity";
    public final static String MATERIAL_COLUMN = "yd_material";         //物料
    public final static String ERRTYPE_COLUMN = "yd_errtype";           //问题类型
    public final static String RISKLEVEL_COLUMN = "yd_risklevel";       //风险等级
    public final static String PRODUCENO_COLUMN = "yd_productno";       //厂家批号
    public final static String LOTBILLNO_COLUMN = "yd_lotbillno";       //入库批号
    public final static String REPLYDATEP_FIELD = "yd_replydate";       //供应商回复时间
    public final static String INITDATE_FILED = "yd_initdate";          //发起质量反馈单时间
    public final static String CYCLE_FIELD = "yd_cycle";                //质量反馈闭环周期
    public final static String DELAY_FILED = "yd_delay";                //供应商延期回复时间
    public final static String SUPUSER_FIELD = "yd_mulsupuser";         //供应商用户（多个）
    public final static String MATERIALNAME_COLUMN = "yd_materialname"; //物料名称
    /* --------------------- 生成异常批次库字段 --------------------*/
    public final static String ERRLOTFIELDS = PRODUCER_FIELD + "," + PROLOT_FIELD + "," + SUPPLIER_FIELD + "," + SERIALNO_FIELD + "," + SUPSTATUS_FIELD + ","
            + BILLNO_FIELD + "," + MATERIAL_ENTRY + "." + PRODUCENO_COLUMN + "," + MATERIAL_ENTRY + "." + MATERIAL_COLUMN + "," + MATERIAL_ENTRY
            + "." + ERRTYPE_COLUMN + "," + MATERIAL_ENTRY + "." + RISKLEVEL_COLUMN + "," + MATERIAL_ENTRY + "." + LOTBILLNO_COLUMN;

    /**
     * 根据供应商产品质量反馈单生成异常批次库（生产商+物料编码+厂家批次 维度)
     * @author: hst
     * @createDate: 2022/11/14
     * @param dynamicObjects 单据信息
     */
    public static void createAbnormalBatch (DynamicObject[] dynamicObjects) {
        Arrays.stream(dynamicObjects).forEach(dynamicObject -> {
            DynamicObject supplier = dynamicObject.getDynamicObject(SUPPLIER_FIELD);      //供应商
            if (Objects.nonNull(supplier)){
                dynamicObject.getDynamicObjectCollection(MATERIAL_ENTRY).stream().forEach(entry -> {
                    DynamicObject material = entry.getDynamicObject(MATERIAL_COLUMN);       //物料
                    if (Objects.nonNull(material)) {
                        String matNumber = material.getString("number");      //物料编码
                        String lotNo = entry.getString(PRODUCENO_COLUMN);           //厂家批次
                        // update by hst 2023/08/18 删除高风险判断条件
                        if (StringUtils.isNotBlank(matNumber)) {
                            try {
                                //以生产商+物料编码+厂家批次 维度查找异常批次库
                                DynamicObject abnormalBatch = getAbnormalBatchInfo(supplier.getString("number"), lotNo, matNumber);
                                //根据质量反馈单给异常批次库赋值
                                abnormalBatch.set(ScpErrorLotHelper.SUPPLIER_FIELD, dynamicObject.getDynamicObject(SUPPLIER_FIELD));     //供应商
                                abnormalBatch.set(ScpErrorLotHelper.PRODUCOR_FIELD, StringUtils.isNotBlank(dynamicObject.getString(PRODUCER_FIELD)) ?
                                        dynamicObject.getString(PRODUCER_FIELD) : Objects.nonNull(dynamicObject.getDynamicObject(SUPPLIER_FIELD)) ?
                                        dynamicObject.getDynamicObject(SUPPLIER_FIELD).getString("name") : "");    //生产商
                                abnormalBatch.set(ScpErrorLotHelper.MATERIAL_FIELD, entry.getDynamicObject(MATERIAL_COLUMN));            //物料
                                // update by hst 2023/12/06 去除物料编码中的版本号
                                if (StringUtils.isNotBlank(matNumber)) {
                                    if (matNumber.indexOf("-") > -1) {
                                        abnormalBatch.set("yd_materialno",matNumber.substring(0,matNumber.indexOf("-")));
                                    } else {
                                        abnormalBatch.set("yd_materialno",matNumber);
                                    }
                                }
                                abnormalBatch.set(ScpErrorLotHelper.MATNAME_FIELD, Objects.nonNull(entry.getDynamicObject(MATERIAL_COLUMN)) ?
                                        entry.getDynamicObject(MATERIAL_COLUMN).get("name") : "");      //物料名称
                                abnormalBatch.set(ScpErrorLotHelper.MODEL_FIELD, Objects.nonNull(entry.getDynamicObject(MATERIAL_COLUMN)) ?
                                        entry.getDynamicObject(MATERIAL_COLUMN).get("modelnum") : "");      //规格型号
                                abnormalBatch.set(ScpErrorLotHelper.QCNO_FIEDL, dynamicObject.getString(BILLNO_FIELD));     //质量反馈单号
                                abnormalBatch.set(ScpErrorLotHelper.ERRTYPE_FIELD, entry.getString(ERRTYPE_COLUMN));     //问题类型
                                abnormalBatch.set(ScpErrorLotHelper.LOTNO_FIELD, entry.getString(LOTBILLNO_COLUMN));     //批号
                                abnormalBatch.set(ScpErrorLotHelper.ENABLE_FIELD, "1");
                                abnormalBatch.set(ScpErrorLotHelper.SUPPLIERNO_FIELD, lotNo);
                                if (StringUtils.isBlank(abnormalBatch.getString(ScpErrorLotHelper.NUMBER_FIELD))) {
                                    abnormalBatch.set(ScpErrorLotHelper.NUMBER_FIELD, "编码" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS"));
                                    abnormalBatch.set(ScpErrorLotHelper.CEATOR_FIELD, UserServiceHelper.getCurrentUserId());
                                    abnormalBatch.set(ScpErrorLotHelper.CREATETIME_FILED, new Date());
                                } else {
                                    abnormalBatch.set(ScpErrorLotHelper.MODIFIER_FIELD, UserServiceHelper.getCurrentUserId());
                                    abnormalBatch.set(ScpErrorLotHelper.MODIFYTIME_FILED, new Date());
                                }
                                OperationResult result = OperateServiceHelper.execOperate("save", ScpErrorLotHelper.MAIN_ENTITY,
                                        new DynamicObject[]{abnormalBatch}, OperateOption.create());
                                if (!result.isSuccess()) {
                                    throw new KDBizException(result.getMessage());
                                }
                            } catch (Exception e){
                                throw new KDBizException("保存异常批次库失败，原因：" + e.getMessage());
                            }
                        }
                    }
                });
            } else {
                throw new KDBizException("获取不到供应商信息！");
            }
        });
    }

    /**
     * 以生产商+物料编码+厂家批次 维度获取异常批次库
     * @author: hst
     * @createDate: 2022/11/14
     * @param supNumber   供应商编码
     * @param lotNo      厂家批次
     * @param matNumber 物料编码
     * @return
     */
    public static DynamicObject getAbnormalBatchInfo (String supNumber, String lotNo, String matNumber) {
        QFilter qFilter = QFilter.of("yd_supplier.number = ? and yd_supplierno = ? and yd_material.number = ?",supNumber,lotNo,matNumber);
        DynamicObject bill = BusinessDataServiceHelper.loadSingle(ScpErrorLotHelper.MAIN_ENTITY,"id",new QFilter[]{qFilter});
        if (Objects.nonNull(bill)) {
            return BusinessDataServiceHelper.loadSingle(bill.get("id"),ScpErrorLotHelper.MAIN_ENTITY);
        } else {
            return BusinessDataServiceHelper.newDynamicObject(ScpErrorLotHelper.MAIN_ENTITY);
        }
    }

    /**
     * 校验单据是否符合发送状态，并发送邮件
     * @author: hst
     * @createDate: 2022/11/14
     * @param bills
     * @return
     */
    public static ApiResult sendMessage (DynamicObject[] bills) {
        ApiResult apiResult = new ApiResult();
        apiResult.setSuccess(true);
        StringBuffer errStr = new StringBuffer();
        Arrays.stream(bills).forEach(bill -> {
            ApiResult result = sendMessage(bill);
            if (!result.getSuccess()) {
                errStr.append(result.getMessage());
            }
        });
        if (errStr.length() > 0) {
            apiResult.setSuccess(false);
            apiResult.setMessage(errStr.deleteCharAt(errStr.length() - 1).toString());
        }
        return apiResult;
    }

    /**
     * 发送邮件
     * @author: hst
     * @createDate: 2022/11/14
     * @param bill
     * @return
     */
    public static ApiResult sendMessage (DynamicObject bill) {
        ApiResult apiResult = new ApiResult();
        apiResult.setSuccess(true);
        StringBuffer errStr = new StringBuffer();
        Map<String, Object> result = new HashMap<>();
        if (!"A".equals(bill.getString(SUPSTATUS_FIELD))) {
            apiResult.setSuccess(false);
            apiResult.setMessage("单据：" + bill.getString("billno") + "无需供应商确认或已确认；");
            return apiResult;
        }

        // update by hst 2024/03/22 向多个供应商用户发送短信
        // 获取对应供应商用户数据
        DynamicObjectCollection supplierUserInfo = bill.getDynamicObjectCollection("yd_mulsupuser");
        // 手机号码
        List<String> phones = supplierUserInfo.stream().filter(info ->
                Objects.nonNull(info.getDynamicObject("fbasedataid"))
                        && StringUtils.isNotBlank(info.getDynamicObject("fbasedataid").getString("phone")))
                .map(user -> user.getDynamicObject("fbasedataid").getString("phone")).collect(Collectors.toList());
        // 邮箱
        List<String> emails = supplierUserInfo.stream().filter(info ->
                Objects.nonNull(info.getDynamicObject("fbasedataid"))
                        && StringUtils.isNotBlank(info.getDynamicObject("fbasedataid").getString("email"))).map(user ->
                user.getDynamicObject("fbasedataid").getString("email")).collect(Collectors.toList());
        // 发送内容
        String materilas = bill.getDynamicObjectCollection("yd_materialentity").stream()
                .map(entry -> entry.getString(MATERIALNAME_COLUMN)).collect(Collectors.joining(","));
        String context = "收到质量反馈，物料名称为【" + (Objects.nonNull(materilas) ? materilas : "")
                + "】，请及时登录SRM系统回复。路径：消息中心—待办任务";

        // 发送短信
        if (phones.size() > 0) {
            result = MessageUtil.sendShortMessage(phones, context);
            if (result != null) {
                String code = GeneralFormatUtils.getString(result.get("code"));
                if (!"0".equals(code)) {
                    errStr.append("单据：" + bill.getString("billno") + "的短信发送失败，原因：" +
                            GeneralFormatUtils.getString(result.get("description") + "\n" + ";"));
                }
            }
        } else {
            errStr.append("单据：" + bill.getString("billno") + "获取不到供应商用户手机号码；");
        }
        // 发送邮件
        if (emails.size() > 0) {
            result = MessageUtil.sendEmail(emails, "质量反馈单确认提醒", context);
            if (result != null) {
                String code = GeneralFormatUtils.getString(result.get("code"));
                if (!"0".equals(code)) {
                    errStr.append("单据：" + bill.getString("billno") + "的邮件发送失败，原因：" +
                            GeneralFormatUtils.getString(result.get("description") + "\n") + ";");
                }
            }
        } else {
            errStr.append("单据：" + bill.getString("billno") + "获取不到供应商用户邮箱；");
        }

        if (errStr.length() > 0) {
            apiResult.setSuccess(false);
            apiResult.setMessage(errStr.deleteCharAt(errStr.length() - 1).toString());
        }
        return apiResult;
    }

    /**
     * 计算处理周期
     * @author: hst
     * @createDate: 2022/11/16
     * @param bill 单据
     */
    public void timeDifference (DynamicObject bill) {
        long createTime = bill.getDate(QCFeedBackHelper.CREATEDATE_FIELD).getTime();
        long now = new Date().getTime();
        int day = (int) ((now - createTime) / (1000 * 60 * 60 * 24)) + 1;
        bill.set(QCFeedBackHelper.PROCESSCYCLE_FILED,String.valueOf(day) + "天");
    }
}
