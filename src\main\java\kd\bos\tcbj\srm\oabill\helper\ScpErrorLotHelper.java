package kd.bos.tcbj.srm.oabill.helper;

/**
 * @package kd.bos.tcbj.srm.oabill.helper.ScpErrorLotHelper
 * @className ScpErrorLotHelper
 * @author: hst
 * @createDate: 2022/11/14
 * @version: v1.0
 */
public class ScpErrorLotHelper {
    /* --------------------------- 表头 --------------------------- */
    public final static String MAIN_ENTITY = "yd_scp_errlot";
    public final static String NUMBER_FIELD = "number";
    public final static String PRODUCER_FIELD = "yd_producor";           //生产商
    public final static String PROLOT_FIELD = "yd_manufacturerlot";     //厂家批次
    public final static String MATERIAL_FIELD = "yd_material";          //物料
    public final static String SUPPLIER_FIELD = "yd_supplier";          //供应商
    public final static String PRODUCOR_FIELD = "yd_producor";          //生产商
    public final static String MATNAME_FIELD = "yd_matname";            //物料名称
    public final static String MODEL_FIELD = "yd_modelnum";             //规格型号
    public final static String LOTNO_FIELD = "yd_lotno";                //批号
    public final static String QCNO_FIEDL = "yd_qcno";                  //质量反馈单号
    public final static String ERRTYPE_FIELD = "yd_errtype";            //问题类型
    public final static String ENABLE_FIELD = "enable";                 //状态
    public final static String CEATOR_FIELD = "creator";                //创建人
    public final static String CREATETIME_FILED = "createtime";        //创建时间
    public final static String MODIFIER_FIELD = "modifier";             //修改人
    public final static String MODIFYTIME_FILED = "modifytime";        //修改时间
    public final static String SUPPLIERNO_FIELD = "yd_supplierno";     //供应商批号
}
