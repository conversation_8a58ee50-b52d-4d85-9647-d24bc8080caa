package kd.bos.tcbj.srm.oabill.imp;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.OperateOption;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.oabill.utils.OABillManageUtil;
import kd.taxc.common.util.StringUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.oabill.mservice.OABillManageMservice;

/**
 * @auditor yanzuwei
 * @date 2022年7月28日
 * 
 */
public class OABillManageMserviceImpl implements OABillManageMservice {

	/**
	 * 内部实例化
	 */
	private final static OABillManageMserviceImpl mServiceImpl = new OABillManageMserviceImpl();
	private final static Log logger = LogFactory.getLog("OABillManageMserviceImpl");
	
	/**
	 * (构造函数)
	 * 
	 * @createDate  : 2022-07-28
	 * @createAuthor: yanzuwei
	 * @updateDate  : 
	 * @updateAuthor: 
	 */
	public OABillManageMserviceImpl() {
		
	}
	
	/**
	 * 描述：获取实现类实例
	 */
	public static OABillManageMservice getInstance() {
		return mServiceImpl;
	}
	
	/**
	 * 解析OA的非生产物料试机申请单单据数据并更新非生产供应商台账
	 * @author: hst
	 * @createDate: 2022/08/29
	 * @param idSet
	 * @return
	 */
	@Override
	public ApiResult updateUnProSupBill(Set<String> idSet) {
		ApiResult result = new ApiResult();
		result.setSuccess(true);
		result.setMessage("解析成功");
		StringBuffer error = new StringBuffer();

		OABillManageUtil.analysisOABillData(idSet.stream().collect(Collectors.toList()),"yd_unpromatcommissbill","yd_oadata_tag","yd_beused");
		
		for (String billId : idSet) {
			DynamicObject billInfo = BusinessDataServiceHelper.loadSingle(billId, "yd_unpromatcommissbill");

			if (!billInfo.getBoolean("yd_beused")) {
				continue;
			}

			try {
				DynamicObjectCollection matEnCol = billInfo.getDynamicObjectCollection("yd_matdetail_en");
				DynamicObjectCollection supEnCol = billInfo.getDynamicObjectCollection("yd_commissionresult_en");
				
				if (matEnCol.size() == 0 || supEnCol.size() == 0) {
					// update by hst 2023/11/17 优化提示
//					continue;
					billInfo = BusinessDataServiceHelper.loadSingle(billId, "yd_unpromatcommissbill");
					billInfo.set("yd_errmsg","打样、试机物料明细表/试机结果评定分录不能为空");
				} else {
					boolean checkedInfo = true;
					Set<String> matSet = new HashSet<String>();
					Map<String, DynamicObject> matMap = new HashMap<String, DynamicObject>();
					Set<String> supSet = new HashSet<String>();
					Map<String, DynamicObject> supMap = new HashMap<String, DynamicObject>();

					for (DynamicObject matEnInfo : matEnCol) {
						String matno = matEnInfo.getString("yd_matno");
//					QFilter matFilter = new QFilter("number", QCP.equals, matno.trim());
//					DynamicObjectCollection matCol = QueryServiceHelper.query("bd_material", "id,number", matFilter.toArray());
//					for (DynamicObject matInfo : matCol) {
//						matSet.add(matInfo.getString("id"));
//						matMap.put(matInfo.getString("id"), matEnInfo);
//					}
						matSet.add(matno);
						matMap.put(matno, matEnInfo);
					}

					for (DynamicObject supEnInfo : supEnCol) {
						String supname = supEnInfo.getString("yd_ressup");
//					QFilter supFilter = new QFilter("name", QCP.equals, supname.trim());
//					DynamicObjectCollection supCol = QueryServiceHelper.query("srm_supplier", "id,number,name", supFilter.toArray());
//					for (DynamicObject supInfo : supCol) {
//						supSet.add(supInfo.getString("id"));
//						supMap.put(supInfo.getString("id"), supEnInfo);
//					}
						supSet.add(supname);
						supMap.put(supname, supEnInfo);
					}

//				if (matSet.size() == 0) {
//					billInfo.set("yd_notexistmat", true);
//					checkedInfo = false;
//				}
//				if (supSet.size() == 0) {
//					billInfo.set("yd_notexistsup", true);
//					checkedInfo = false;
//				}

					if (checkedInfo) {
						// 根据物料集合和供应商集合创建台账
						String creatorId = RequestContext.get().getUserId().toString();
						DynamicObject creator = BusinessDataServiceHelper.loadSingle(creatorId, "bos_user", "id");
						DynamicObject newBookInfo = null;
						for (String matno : matSet) {
							for (String supname : supSet) {
								QFilter bookFilter = new QFilter("yd_matno", QCP.equals, matno);
								bookFilter.and(new QFilter("yd_supname", QCP.equals, supname));
								if (!QueryServiceHelper.exists("yd_unprosupsumbill", bookFilter.toArray())) {
									// 创建台账
									newBookInfo = BusinessDataServiceHelper.newDynamicObject("yd_unprosupsumbill");
									newBookInfo.set("billno", DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS"));
									newBookInfo.set("billstatus", "A");
									newBookInfo.set("creator", creator);
									newBookInfo.set("modifier", creator);
									newBookInfo.set("auditor", creator);
									newBookInfo.set("createtime", new Date());
									newBookInfo.set("modifytime", new Date());
									newBookInfo.set("auditdate", new Date());

									newBookInfo.set("yd_matprop", "1");  // 物料属性
									newBookInfo.set("yd_matno", matno);  // 物料代码
									QFilter matFilter = new QFilter("number", QCP.equals, matno.trim());
									DynamicObjectCollection matCol = QueryServiceHelper.query("bd_material", "id,number", matFilter.toArray());
									// update by hst 2023/10/17 通过助记码查询物料
									if (matCol.size() == 0) {
										QFilter helpFilter = new QFilter("helpcode", QCP.equals, matno.trim());
										matCol = QueryServiceHelper.query("bd_material", "id,number", helpFilter.toArray());
									}
									if (matCol.size() > 0) {
										newBookInfo.set("yd_material", BusinessDataServiceHelper.loadSingle(matCol.get(0).getString("id"), "bd_material"));  // 物料F7
									} else {
										billInfo.set("yd_notexistmat", true);
									}
									newBookInfo.set("yd_supname", supname);  // 供应商名称
									QFilter supFilter = new QFilter("name", QCP.equals, supname.trim());
									DynamicObjectCollection supCol = QueryServiceHelper.query("srm_supplier", "id,number,name", supFilter.toArray());
									if (supCol.size() > 0) {
										newBookInfo.set("yd_supplier", BusinessDataServiceHelper.loadSingle(supCol.get(0).getString("id"), "srm_supplier"));  // 供应商F7
									} else {
										billInfo.set("yd_notexistsup", true);
									}
									newBookInfo.set("yd_process", supMap.get(supname).getString("yd_rescraft"));  // 工艺
									newBookInfo.set("yd_size", supMap.get(supname).getString("yd_ressize"));  // 尺寸
									if ("珠海厂".equals(billInfo.getString("yd_producetype"))) {
										newBookInfo.set("yd_betouch", "1");  // 是否与产品接触-是
									} else {
										newBookInfo.set("yd_betouch", "2");  // 是否与产品接触-否
									}
									newBookInfo.set("yd_supstatus", "1");  // 供应商状态-合格
								} else {
									DynamicObject tempInfo = QueryServiceHelper.queryOne("yd_unprosupsumbill", "id", bookFilter.toArray());
									newBookInfo = BusinessDataServiceHelper.loadSingle(tempInfo.getString("id"), "yd_unprosupsumbill");
									newBookInfo.set("yd_process", supMap.get(supname).getString("yd_rescraft"));  // 工艺
									newBookInfo.set("yd_size", supMap.get(supname).getString("yd_ressize"));  // 尺寸
									if ("珠海厂".equals(billInfo.getString("yd_producetype"))) {
										newBookInfo.set("yd_betouch", "1");  // 是否与产品接触-是
									} else {
										newBookInfo.set("yd_betouch", "2");  // 是否与产品接触-否
									}
								}
								//根据供应商获取最新一张《非生产物料供应商新增/变更申请》
								DataSet unProScpBills = QueryServiceHelper.queryDataSet("OABillManageMserviceImpl", "yd_unproscpbill",
										"yd_companyaddress,yd_taxrate,yd_depositbank,yd_bankaccount,yd_payway,yd_odmchartervalid," +
												"yd_odmqsvaild", new QFilter[]{new QFilter("yd_suppliername", QFilter.equals,
												supname)}, "createtime desc");
								DataSet bills = unProScpBills.top(1);
								Map<String, Object> mapping = new HashMap<>();
								for (Row bill : bills) {
									mapping.put("yd_supaddress", bill.get("yd_companyaddress"));
									mapping.put("yd_taxrate", bill.get("yd_taxrate"));
									mapping.put("yd_bankname", bill.get("yd_depositbank"));
									mapping.put("yd_recaccount", bill.get("yd_bankaccount"));
									mapping.put("yd_paytype", bill.get("yd_payway"));
									mapping.put("yd_businesslicensedate", bill.get("yd_odmchartervalid"));
									mapping.put("yd_productlicensedate", bill.get("yd_odmqsvaild"));
									OABillManageUtil.updateField(newBookInfo, mapping);
								}
								SaveServiceHelper.save(new DynamicObject[]{newBookInfo});
							}
						}

						// 标记为已更新台账
						billInfo.set("yd_beupdatedbook", true);
					}
				}
				
			} catch (Exception e1) {
				System.out.println(e1.getLocalizedMessage());
				error.append(e1.getLocalizedMessage()+";");
				billInfo.set("yd_errmsg",e1.getMessage());
				continue;
			}
			
			SaveServiceHelper.save(new DynamicObject[]{billInfo});
		}
		
		if (error.length() > 0) {
			result.setSuccess(false);
			result.setMessage(error.toString());
		}
		return result;
	}

	/**
	 * 解析OA的非生产物料供应商新增/变更申请数据
	 * <AUTHOR>
	 * @createDate 2022/08/29
	 */
	@Override
	public ApiResult analysiUnProScpBill(List<String> ids) {
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		apiResult.setMessage("解析成功！");
		try {
			OABillManageUtil.analysisOABillData(ids,"yd_unproscpbill","yd_oadata_tag","yd_beused");
			DynamicObject[] analyBills = BusinessDataServiceHelper.load("yd_unproscpbill","yd_supplier,yd_suppliername",
					new QFilter[]{new QFilter("id",QFilter.in,ids)});
			Arrays.asList(analyBills).stream().forEach(analyBill -> {
				if (StringUtil.isNotEmpty(analyBill.getString("yd_suppliername"))) {
					analyBill.set("yd_supplier",BusinessDataServiceHelper.loadSingle("bd_supplier","id,number,name",
							new QFilter[]{new QFilter("name",QFilter.equals,analyBill.getString("yd_suppliername"))}));
				}
			});
			SaveServiceHelper.save(analyBills);
		} catch (Exception e) {
			logger.error(e.getLocalizedMessage());
			apiResult.setSuccess(false);
			apiResult.setMessage(e.getLocalizedMessage()+";");
		}
		return apiResult;
	}

	/**
	 * 解析质量反馈单数据
	 * <AUTHOR>
	 * @createDate 2022/11/24
	 */
	@Override
	public ApiResult analysiQCFeedBack(List<String> ids,String userName) {
		ApiResult apiResult = new ApiResult();
		apiResult.setSuccess(true);
		apiResult.setMessage("解析成功！");
		try {
			// 解析OA数据
			OABillManageUtil.analysisBillData(ids,"yd_scp_qcfeedback","yd_oadata_tag","yd_beused");
			// 数据整理
			DynamicObject[] analyBills = BusinessDataServiceHelper.load(ids.toArray(),BusinessDataServiceHelper.newDynamicObject("yd_scp_qcfeedback").getDynamicObjectType());
			Arrays.asList(analyBills).stream().forEach(analyBill -> {
		 		// update by hst 2023/05/09 是否进行提交
				boolean isSumbit = true;
				// 入库批号
				StringBuffer lotNo = new StringBuffer();
				// 厂家批号
				StringBuffer manuLot = new StringBuffer();
				// 物料名称
				StringBuffer matName = new StringBuffer();
				// 校验用户是否存在
				if (StringUtil.isNotEmpty(analyBill.getString("yd_loginname"))) {
					DynamicObject user = BusinessDataServiceHelper.loadSingle("bos_user","id,number,name",
							new QFilter[]{new QFilter("username",QFilter.equals,analyBill.getString("yd_loginname"))});
					if (Objects.isNull(user)) {
					    // 通过配置的默认用户名去找用户
                        user = BusinessDataServiceHelper.loadSingle("bos_user","id,number,name",
                                new QFilter[]{new QFilter("username",QFilter.equals,userName)});
                        analyBill.set("creator",user);
					} else {
						analyBill.set("creator",user);
					}
				} else {

				}
				// 校验供应商是否存在
				// update by hst 2023/07/12 修改为用代理商匹配供应商
				// update by hst 2024/03/22 修改为匹配供应商库的供应商，取供应商库中的供应商的主数据信息
				if (StringUtil.isNotEmpty(analyBill.getString("yd_agent"))) {
					DynamicObject supplier = BusinessDataServiceHelper.loadSingle("srm_supplier","id,number,name,supplier",
							new QFilter[]{new QFilter("name",QFilter.equals,analyBill.getString("yd_agent"))});
					if (Objects.nonNull(supplier)) {
						if (Objects.isNull(supplier.getDynamicObject("supplier"))) {
							analyBill.set("yd_nosupplier", true);
							// update by hst 2023/05/09 供应商不存在时，不自动提交
							// update by hst 2024/09-06 供应商不存在，自动提交，由审批人员在流程中补充
//							isSumbit = false;
						} else {
							analyBill.set("yd_supplier", supplier.getDynamicObject("supplier"));
							analyBill.set("yd_nosupplier", false);
						}
					} else {
						analyBill.set("yd_nosupplier", true);
						// update by hst 2024/09-06 供应商不存在，自动提交，由审批人员在流程中补充
//						isSumbit = false;
					}
				}
				// 物料分录处理
				for (DynamicObject material : analyBill.getDynamicObjectCollection("yd_materialentity")) {
					// 入库批号统计
					if (StringUtils.isNotBlank(material.getString("yd_lotbillno"))) {
						lotNo.append(material.getString("yd_lotbillno") + "/");
					}
					// 厂家批号统计
					if (StringUtils.isNotBlank(material.getString("yd_productno"))) {
						manuLot.append(material.getString("yd_productno") + "/");
					}
					// 物料名称统计
					if (StringUtils.isNotBlank(material.getString("yd_materialname"))) {
						matName.append(material.getString("yd_materialname") + "/");
					}
					// 分录风险等级为空，取表头
					if (StringUtils.isBlank(material.getString("yd_risklevel"))) {
						material.set("yd_risklevel",analyBill.get("yd_level"));
					}
					// 校验物料是否存在
					if (StringUtils.isNotBlank(material.getString("yd_materialno"))) {
						// update by hst 2023/07/11 修改物料查询条件
						// update by hst 2023/11/1 携带物料分类
						DynamicObject bdMaterial = BusinessDataServiceHelper.loadSingle("bd_material"
								,"id,number,name，entry_groupstandard.groupid,entry_groupstandard.standardid",
								new QFilter[]{new QFilter("number",QFilter.equals,material.getString("yd_materialno")).
										or(new QFilter("number",QFilter.like,"%" + material.getString("yd_materialno")))});
						if (Objects.isNull(bdMaterial)) {
							material.set("yd_nomaterial",true);
							// update by hst 2023/05/09 物料不存在时，不自动提交
							// update by hst 2024/09-06 物料不存在，自动提交，由审批人员在流程中补充
//							isSumbit = false;
						} else {
							material.set("yd_material",bdMaterial);
							material.set("yd_nomaterial",false);
							// update by hst 2023/11/1 查询物料基本分类标准
							DynamicObject standard = BusinessDataServiceHelper.loadSingle("bd_materialgroupdetail",
									"id,group", new QFilter[]{
											new QFilter("material.id",QFilter.equals,bdMaterial.getPkValue())
											, new QFilter("standard.number",QFilter.equals,"JBFLBZ")
									});
							if (Objects.nonNull(standard)) {
								material.set("yd_group", standard.get("group"));
							}

						}
					}
				}
				// update by hst 2023/05/09 根据是否已解析判断是否自动提交
				if (!analyBill.getBoolean("yd_beused")) {
					isSumbit = false;
				}
				// update by hst 2023/11/14 获取单据所属月份（上月26号-当月25号）
				analyBill.set("yd_billmonth",this.getBillBelongMonth(analyBill.getDate("yd_feeddate")));
				// 物料名称
				analyBill.set("yd_rmname",matName.length() > 0 ? matName.deleteCharAt(matName.length() - 1).toString() : "");
				// 入库单号
				analyBill.set("yd_lotno",lotNo.length() > 0 ? lotNo.deleteCharAt(lotNo.length() - 1) : "");
				// 厂家批次
				analyBill.set("yd_manufacturerlot", manuLot.length() > 0 ? manuLot.deleteCharAt(manuLot.length() - 1) : "");
				// update by hst 2024/06/18 高风险才默认纳入异常批次库
				if ("1".equals(analyBill.get("yd_level"))) {
					analyBill.set("yd_ischeck", "1");
				} else {
					analyBill.set("yd_ischeck", "0");
				}
				// update by hst 2023/05/09 单条保存，提交失败是失败原因反写
				// 保存单据
				SaveServiceHelper.save(new DynamicObject[]{analyBill});
				// 已解析才进行提交
				if (isSumbit) {
					// 保存完毕触发提交
					OperationResult result = OperationServiceHelper.executeOperate("submit", "yd_scp_qcfeedback",
							new DynamicObject[]{analyBill}, OperateOption.create());
					if (!result.isSuccess()) {
						analyBill.set("yd_errorreason", result.getMessage());
						SaveServiceHelper.save(new DynamicObject[]{analyBill});
					}
				}
			});
		} catch (Exception e) {
			logger.error(e.getLocalizedMessage());
			apiResult.setSuccess(true);
			apiResult.setMessage(e.getLocalizedMessage()+";");
		}
		return apiResult;
	}

	/**
	 * 获取单据所属月份（上月26号-当月25号）
	 * @param date
	 * @return
	 * @author: hst
	 * @createDate: 2023/11/14
	 */
	private String getBillBelongMonth (Date date) {
		if (Objects.nonNull(date)) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(date);
			int month = cal.get(Calendar.MONTH) + 1;
			int day = cal.get(Calendar.DAY_OF_MONTH);
			if (day >= 26) {
				month = month + 1;
			}
			return String.valueOf(month);
		}
		return "";
	}
}
