package kd.bos.tcbj.srm.oabill.mservice;

import java.util.List;
import java.util.Set;

import kd.bos.entity.api.ApiResult;

/**
 * OA表单管理模块接口类
 * @auditor yanzuwei
 * @date 2022年7月28日
 * 
 */
public interface OABillManageMservice {
	
	/**
	 * 解析OA的非生产物料试机申请单单据数据并更新非生产供应商台账
	 * @param idSet
	 * @return
	 */
	public ApiResult updateUnProSupBill(Set<String> idSet);

	/**
	 * 解析OA的非生产物料供应商新增/变更申请数据
	 */
	public ApiResult analysiUnProScpBill(List<String> ids);

	/**
	 * 解析质量反馈单数据
	 */
	public ApiResult analysiQCFeedBack(List<String> ids,String userName);
}
