package kd.bos.tcbj.srm.oabill.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 供应商主数据查找供应商库关联用户
 * @auditor hst
 * @date 2022/11/15
 * 
 */
public class GetSupplierUserOp extends AbstractOperationServicePlugIn {
	
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		String opKey = e.getOperationKey();
		DynamicObject[] bills = e.getDataEntities();
		for (DynamicObject bill : bills) {
			String billName = bill.getDataEntityType().getName();
			DynamicObject info=BusinessDataServiceHelper.loadSingle(bill.getPkValue(), billName);
			// update by hst 2023/08/22
			if (Objects.nonNull(info.get("yd_supplier"))) {
				DynamicObject supInfo = (DynamicObject) info.get("yd_supplier");
				DynamicObject purUser = SupplyStockHelper.getSrmSupplierUserBySupplierId(GeneralFormatUtils.getString(supInfo.getPkValue()));
				if (purUser != null) {
					String userId = GeneralFormatUtils.getString(purUser.get("user.id"));
					if (!StringUtils.isEmpty(userId)) {
						DynamicObject bdUser = BusinessDataServiceHelper.loadSingle(userId, "bos_user");
						info.set("yd_supplieruser", bdUser);
					}
				}
				SaveServiceHelper.save(new DynamicObject[]{info});
			}
		}
	}
	
}
