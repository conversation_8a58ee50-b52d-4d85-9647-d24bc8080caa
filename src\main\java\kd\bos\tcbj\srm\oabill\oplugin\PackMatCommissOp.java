package kd.bos.tcbj.srm.oabill.oplugin;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.tcbj.srm.oabill.helper.PackMatCommissHelper;
import kd.bos.tcbj.srm.oabill.schedulejob.OABillManageTask;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.oabill.oplugin.PackMatCommissOp
 * @className: PackMatCommissOp
 * @description: 包装物料试机打样申请表单操作插件
 * @author: hst
 * @createDate: 2022/08/26
 * @version: v1.0
 */
public class PackMatCommissOp extends AbstractOperationServicePlugIn {

    private final static String BTN_UPDATEACCOUNT = "updateaccount";

    /**
     * 描述：开启事务，未提交数据库事件
     * <AUTHOR>
     * @createDate 2022/08/26
     * @param e
     */
    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        String key = e.getOperationKey();
        DynamicObject[] packsUpApplys = e.getDataEntities();
        switch (key) {
            case BTN_UPDATEACCOUNT : {
                Map<String,Object> map = new HashMap<>();
                List<String> ids = Arrays.stream(packsUpApplys).map(bill -> bill.getString("id")).collect(Collectors.toList());
                map.put("ids",ids);
                map.put("key","packMatCommiss");
                new OABillManageTask().execute(RequestContext.get(),map);
                break;
            }
        }
    }
}
