package kd.bos.tcbj.srm.oabill.oplugin;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.metadata.dynamicobject.DynamicProperty;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.oabill.helper.PacksUpApplyBillHelper;
import kd.bos.tcbj.srm.oabill.schedulejob.OABillManageTask;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.oabill.oplugin.PacksUpApplyBillOp
 * @className: PacksUpApplyBillOp
 * @description: 包材供应商新增/变更表单操作插件
 * @author: hst
 * @createDate: 2022/08/24
 * @version: v1.0
 */
public class PacksUpApplyBillOp extends AbstractOperationServicePlugIn {

    private final static String MAINENTITY = "yd_packsupapplybill";
    private final static String FIELD_BEUSED = "yd_beused";
    private final static String FIELD_OADATA = "yd_oadata_tag";
    private final static String OP_ANALYSISAOA = "analysisoabill";

    /**
     * 描述：开启事务，未提交数据库事件
     *      解析OA表单数据并保存到单据上
     * <AUTHOR>
     * @createDate 2022/08/23
     * @param e
     */
    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        String key = e.getOperationKey();
        DynamicObject[] packsUpApplys = e.getDataEntities();
        switch (key) {
            case OP_ANALYSISAOA : {
                Map<String,Object> map = new HashMap<>();
                List<String> ids = Arrays.stream(packsUpApplys).map(bill -> bill.getString("id")).collect(Collectors.toList());
                map.put("ids",ids);
                map.put("key","analysispacksUpApply");
                new OABillManageTask().execute(RequestContext.get(),map);
                break;
            }
        }
    }
}
