package kd.bos.tcbj.srm.oabill.oplugin;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper;
import kd.bos.tcbj.srm.oabill.schedulejob.QCFeedBackTask;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.oabill.oplugin.QCFeedBackBillOp
 * @className QCFeedBackBillOp
 * @author: hst
 * @createDate: 2022/11/14
 * @description: 供应商产品质量反馈单单据操作插件
 * @version: v1.0
 */
public class QCFeedBackBillOp extends AbstractOperationServicePlugIn {

    private final static String ABNORMALBATCH_OP = "abnormalbatch";     //操作_产生异常批次库
    private final static String SUBMIT_OP = "submit";                     //操作_提交
    private final static String AUDIT_OP = "audit";                       //操作_审核
    private final static String UNSUBMIT_OP = "unsubmit";                //操作_撤销提交
    private final static String UNAUDIT_OP = "unaudit";                  //操作_反审核
    private final static String SENDEMAIL_OP = "sendemail";              //操作_发送短信邮件
    private final static String ANALYSI_OP = "analysidata";              //操作_解析数据
    private final static String SUP_COMFIRM = "supcomfirm";              //操作_供应商确认
    private final static String SUP_RECEIVE = "supreceive";              //操作_供应商接收


    /**
     * 添加返回字段
     * @param e
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add(QCFeedBackHelper.PRODUCER_FIELD);              //生产商
        e.getFieldKeys().add(QCFeedBackHelper.BILLNO_FIELD);                //单据编号
        e.getFieldKeys().add(QCFeedBackHelper.SUPPLIER_FIELD);              //供应商
        e.getFieldKeys().add(QCFeedBackHelper.SERIALNO_FIELD );             //流水单号
        e.getFieldKeys().add(QCFeedBackHelper.SUPSTATUS_FIELD);             //供应商确认状态
        e.getFieldKeys().add(QCFeedBackHelper.BILLSTATUS_FIELD);            //单据状态
        e.getFieldKeys().add(QCFeedBackHelper.CREATEDATE_FIELD);            //创建时间
        e.getFieldKeys().add(QCFeedBackHelper.PROCESSCYCLE_FILED);          //处理周期
        e.getFieldKeys().add(QCFeedBackHelper.MATERIAL_COLUMN);             //物料
        e.getFieldKeys().add(QCFeedBackHelper.ERRTYPE_COLUMN);              //问题类型
        e.getFieldKeys().add(QCFeedBackHelper.RISKLEVEL_COLUMN);            //风险等级
        e.getFieldKeys().add(QCFeedBackHelper.PRODUCENO_COLUMN);            //厂家批次
        e.getFieldKeys().add(QCFeedBackHelper.ISSEND_FIELD);                 //是否发送供应商
        e.getFieldKeys().add(QCFeedBackHelper.ISAUDIT_FILED);                //是否内部审核
        e.getFieldKeys().add(QCFeedBackHelper.LOTBILLNO_COLUMN);             //入库批号
        e.getFieldKeys().add(QCFeedBackHelper.ISCHECK_FIELD);                //是否纳入质量异常考核
        e.getFieldKeys().add(QCFeedBackHelper.SUPTYPE_FIELD);                //供应商类型
        e.getFieldKeys().add(QCFeedBackHelper.REPLYDATEP_FIELD);             //供应商回复时间 update by hst 2023/11/1
        e.getFieldKeys().add(QCFeedBackHelper.INITDATE_FILED);              //供应商回复时间 update by hst 2023/11/1
        e.getFieldKeys().add(QCFeedBackHelper.CYCLE_FIELD);                 // 质量反馈闭环周期 update by hst 2023/11/14
        e.getFieldKeys().add(QCFeedBackHelper.DELAY_FILED);                 // 供应商延期回复时间 update by hst 2023/11/14
        e.getFieldKeys().add(QCFeedBackHelper.SUPUSER_FIELD);               // 供应商用户（多个） update by hst 2024/03/25
        e.getFieldKeys().add(QCFeedBackHelper.MATERIALNAME_COLUMN);               // 物料名称 update by hst 2024/03/25
    }

    /**
     *  事务开启，数据未提交前调用
     * @param e
     */
    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        String key = e.getOperationKey();
        DynamicObject[] datas = e.getDataEntities();
        switch (key) {
            case ABNORMALBATCH_OP : {
                // 产生异常批次库
                QCFeedBackHelper.createAbnormalBatch(datas);
                break;
            }
            case ANALYSI_OP : {
                if (datas.length > 0) {
                    String entityName = datas[0].getDynamicObjectType().getName();
                    Map<String,Object> map = new HashMap<>();
                    List<String> ids = Arrays.stream(datas).map(dynamicObject -> {return dynamicObject.getPkValue().toString();}).collect(Collectors.toList());
                    map.put("key","analysisQCFeedBack");
                    map.put("ids",ids);
                    DynamicObject user = UserServiceHelper.getCurrentUser("userName");
                    map.put("userName",user.getString("userName"));
                    try {
                        new QCFeedBackTask().execute(RequestContext.get(), map);
                    } catch (Exception exception) {
                        throw new KDBizException(exception.getMessage());
                    }
                }
                break;
            }
            case SUP_COMFIRM : {
                // update by hst 2023/11/1 获取供应商回复节点提交时间
                Arrays.stream(datas).forEach(data -> {
                    Date date = new Date();
                    data.set(QCFeedBackHelper.REPLYDATEP_FIELD,date);
                    Date initDate = data.getDate(QCFeedBackHelper.INITDATE_FILED);
                    if (Objects.nonNull(initDate)) {
                        // 质量反馈闭环周期
                        data.set(QCFeedBackHelper.CYCLE_FIELD,DateUtil.getDifferDay(initDate,date));
                        // 供应商延期回复时间
                        double DayM = (double) 1000 * 60 * 60 * 24;
                        double differ = (double) date.getTime() - (double) initDate.getTime();
                        double day =differ/DayM - 7f;
                        if (day > 0) {
                            data.set(QCFeedBackHelper.DELAY_FILED,String.format("%.2f",day));
                        } else {
                            data.set(QCFeedBackHelper.DELAY_FILED,"");
                        }
                    }
                });
                SaveServiceHelper.save(datas);
                break;
            } case SUP_RECEIVE : {
                // update by hst 2023/11/1 获取供应商回复节点接收时间
                Arrays.stream(datas).forEach(data -> {
                    if (Objects.isNull(data.get(QCFeedBackHelper.INITDATE_FILED))) {
                        data.set(QCFeedBackHelper.INITDATE_FILED, new Date());
                    }
                });
                SaveServiceHelper.save(datas);
                break;
            }
        }
    }

    /**
     *  数据已经提交到数据库之后，事务未提交之前，触发此事件
     * @param e
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        String key = e.getOperationKey();
        DynamicObject[] bills = e.getDataEntities();
        switch (key) {
            case SUBMIT_OP : {
                // 提交时修改处理周期，单据状态为提交时：当前日期-创建日期
                Arrays.stream(bills).forEach(bill -> {
                    if ("B".equals(bill.getString(QCFeedBackHelper.BILLSTATUS_FIELD))) {
                        new QCFeedBackHelper().timeDifference(bill);
                    }
                });
                SaveServiceHelper.save(bills);
                break;
            }
            case AUDIT_OP : {
                // 审核时修改处理周期，单据状态为审核时：当前日期-创建日期
                Arrays.stream(bills).forEach(bill -> {
                    if ("C".equals(bill.getString(QCFeedBackHelper.BILLSTATUS_FIELD))) {
                        new QCFeedBackHelper().timeDifference(bill);
                    }
                });
                SaveServiceHelper.save(bills);
                break;
            }
            case UNSUBMIT_OP : {
                //撤销提交，清空处理周期
                Arrays.stream(bills).forEach(bill -> {
                    if ("A".equals(bill.getString(QCFeedBackHelper.BILLSTATUS_FIELD))) {
                        bill.set(QCFeedBackHelper.PROCESSCYCLE_FILED,"");
                    }
                });
                SaveServiceHelper.save(bills);
                break;
            }
            case UNAUDIT_OP : {
                //反审核，清空处理周期
                Arrays.stream(bills).forEach(bill -> {
                        if ("A".equals(bill.getString(QCFeedBackHelper.BILLSTATUS_FIELD))) {
                        bill.set(QCFeedBackHelper.PROCESSCYCLE_FILED,"");
                    }
                });
                SaveServiceHelper.save(bills);
                break;
            }
            case SENDEMAIL_OP : {
                // 发送邮件、短信
                Arrays.stream(bills).forEach(bill -> {
                    new QCFeedBackHelper().sendMessage(bill);
                });
                break;
            }
        }
    }
}
