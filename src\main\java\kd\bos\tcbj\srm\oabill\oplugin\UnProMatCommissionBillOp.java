package kd.bos.tcbj.srm.oabill.oplugin;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.kingdee.bos.ctrl.common.util.StringUtil;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.price.helper.PriceMServiceHelper;
import kd.bos.tcbj.srm.oabill.helper.OABillManageHelper;
import kd.bos.tcbj.srm.oabill.schedulejob.OABillManageTask;

/**
 * @auditor yanzuwei
 * @date 2022年7月28日
 * 
 */
public class UnProMatCommissionBillOp extends AbstractOperationServicePlugIn {
	/**
	 * 
	 * 描述：操作执行，加载单据数据包之前，触发此事件；
	 * 
	 * @createDate  : 2022年7月28日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 加载单据数据包事件
	 */
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		super.onPreparePropertys(e);
		e.getFieldKeys().add("yd_oadata");
		e.getFieldKeys().add("yd_oadata_tag");
		e.getFieldKeys().add("billstatus");
		e.getFieldKeys().add("billno");
		e.getFieldKeys().add("yd_beused");
	}
	
	/**
	 * 
	 * 描述：开启事务，未提交数据库事件
	 * 1.解析OA表单数据并保存到单据上
	 * 
	 * @createDate  : 2022年7月28日
	 * @createAuthor: 严祖威
	 * @updateDate  : 
	 * @updateAuthor: 
	 * @param e 开启事务，未提交数据库事件
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		String opKey=e.getOperationKey();
		if(StringUtil.equalsIgnoreCase("analysisOAbill", opKey)) {
//			DynamicObject[] dynamicObjectArray=e.getDataEntities();
//			Set<String> idSet = new HashSet<String>();
//			for (DynamicObject oaBill : dynamicObjectArray) {
//				if (!oaBill.getBoolean("yd_beused")) {
//					idSet.add(oaBill.getString("id"));
//				}
//			}
//			if (idSet.size() > 0) {
//				ApiResult result = OABillManageHelper.updateUnProSupBill(idSet);
//				if (!result.getSuccess()) {
//					throw new KDBizException("解析异常："+result.getMessage());
//				}
//			}
//			SaveServiceHelper.save(dynamicObjectArray);
			Map<String,Object> map = new HashMap<>();
			List<String> ids = Arrays.stream(e.getDataEntities()).map(bill -> bill.getString("id")).collect(Collectors.toList());
			map.put("ids",ids);
			map.put("key","unProScpBillUpApply");
			new OABillManageTask().execute(RequestContext.get(),map);
		}
	}
}
