package kd.bos.tcbj.srm.oabill.oplugin;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.oabill.helper.OABillManageHelper;
import kd.bos.tcbj.srm.oabill.schedulejob.OABillManageTask;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.oabill.oplugin.UnProScpBillOp
 * @className UnProScpBillOp
 * @author: hst
 * @createDate: 2022/08/29
 * @description: 非生产物料供应商新增/变更申请单据操作插件
 * @version: v1.0
 */
public class UnProScpBillOp extends AbstractOperationServicePlugIn {

    private final static String OP_ANALYSISAOA = "analysisoabill";
    /**
     * 开启事务，未提交数据库事件
     * 解析OA表单数据并保存到单据上
     * @author: hst
     * @createDate: 2022/08/29
     * @param e
     */
    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        String key = e.getOperationKey();
        switch (key) {
            case OP_ANALYSISAOA : {
                Map<String,Object> map = new HashMap<>();
                List<String> ids = Arrays.stream(e.getDataEntities()).map(bill -> bill.getString("id")).collect(Collectors.toList());
                map.put("ids",ids);
                map.put("key","analysiUnProScpBill");
                new OABillManageTask().execute(RequestContext.get(),map);
                break;
            }
        }
    }


}
