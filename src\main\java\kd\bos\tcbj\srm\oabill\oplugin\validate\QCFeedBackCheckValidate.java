package kd.bos.tcbj.srm.oabill.oplugin.validate;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.oabill.oplugin.validate.QCFeedBackCheckValidate
 * @className: QCFeedBackCheckValidate
 * @author: hst
 * @createDate: 2022/11/15
 */
public class QCFeedBackCheckValidate extends AbstractValidator {

    @Override
    public Set<String> preparePropertys() {
        return super.preparePropertys();
    }

    @Override
    public void validate() {
        ExtendedDataEntity[] datas = this.getDataEntities();
        for(ExtendedDataEntity dataEntity : datas)
        {
            DynamicObject data = dataEntity.getDataEntity();
            try
            {
                check(data);
            }catch(Exception e)
            {
                this.addErrorMessage(dataEntity, e.getMessage());
            }
        }
    }

    /**
     * 校验用户是否选择了是否发送供应商、是否内部审核
     * @param bill
     */
    public void check(DynamicObject bill) {
        //是否发送供应商
        String isSend = bill.getString(QCFeedBackHelper.ISSEND_FIELD);
        //是否内部审核
        String isAduit = bill.getString(QCFeedBackHelper.ISAUDIT_FILED);
        // update by hst 2024/03/25 一次性抛出异常
        String errMsg = "";

        if (StringUtils.isBlank(isSend)) {
            errMsg = errMsg + "请选择是否发送供应商！\n";
        }
        if ("0".equals(isSend)) {
            bill.set(QCFeedBackHelper.SUPSTATUS_FIELD,"B");

            if (StringUtils.isBlank(bill.getString(QCFeedBackHelper.ISCHECK_FIELD))) {
                errMsg = errMsg + "请选择是否纳入质量异常考核！\n";
            }
        } else {
            bill.set(QCFeedBackHelper.SUPSTATUS_FIELD,"A");

            if (StringUtils.isBlank(bill.getString(QCFeedBackHelper.SUPTYPE_FIELD))) {
                errMsg = errMsg + "请选择供应商类型！\n";
            }

            // update by hst 2024/03/25 确认发送供应商需上传质量反馈图片
            DynamicObjectCollection billEntry = bill.getDynamicObjectCollection("yd_materialentity");
            for (DynamicObject entry : billEntry) {
                if (entry.getDynamicObjectCollection("yd_picture").size() == 0) {
                    errMsg = errMsg + "请录入物料品质异常状态分录的第" + entry.getString("seq") + "行分录的质量反馈图片！\n";
                }
            }
        }

        if (errMsg.length() > 0) {
            throw new KDBizException(errMsg);
        }

        SaveServiceHelper.save(new DynamicObject[]{bill});
    }
}
