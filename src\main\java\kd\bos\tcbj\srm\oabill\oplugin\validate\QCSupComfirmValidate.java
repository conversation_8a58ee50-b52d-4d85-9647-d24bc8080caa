package kd.bos.tcbj.srm.oabill.oplugin.validate;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.exception.KDBizException;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.oabill.oplugin.validate.QCSupComfirmValidate
 * @className: QCSupComfirmValidate
 * @author: hst
 * @createDate: 2022/11/18
 */
public class QCSupComfirmValidate extends AbstractValidator {

    @Override
    public Set<String> preparePropertys() {
        return super.preparePropertys();
    }

    /**
     * 供应商确认状态修改
     */
    @Override
    public void validate() {
        ExtendedDataEntity[] datas = this.getDataEntities();
        for (ExtendedDataEntity dataEntity : datas) {
            DynamicObject bill = dataEntity.getDataEntity();
            bill.set(QCFeedBackHelper.SUPSTATUS_FIELD, "C");
            SaveServiceHelper.save(new DynamicObject[]{bill});
        }
    }
}
