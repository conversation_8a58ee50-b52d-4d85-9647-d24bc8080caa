package kd.bos.tcbj.srm.oabill.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.form.ConfirmCallBackListener;
import kd.bos.form.MessageBoxOptions;
import kd.bos.form.MessageBoxResult;
import kd.bos.form.container.Container;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.list.ListShowParameter;
import kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper;
import kd.bos.tcbj.srm.quo.helper.QuoQuoteHelper;
import kd.scm.common.constant.BillAssistConstant;

import java.util.EventObject;
import java.util.Objects;

/**
 * @package kd.bos.tcbj.srm.oabill.plugin.QCFeedBackEditPlugin
 * @className QCFeedBackEditPlugin
 * @author: hst
 * @createDate: 2022/11/14
 * @description: 供应商产品质量反馈单单据界面插件
 * @version: v1.0
 */
public class QCFeedBackEditPlugin extends AbstractBillPlugIn {

    private final static String SENDMESS_BTN = "yd_sendmessage";

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Container container = this.getView().getControl("tbmain");
        container.addItemClickListener(this);
        this.addF7SelectorListener();
    }

    @Override
    public void beforeItemClick(BeforeItemClickEvent evt) {
        String key = evt.getItemKey();
        switch (key) {
            case SENDMESS_BTN : {
                // 供应商催办功能
                // 确认用户是否要发送
                ConfirmCallBackListener confirmCallBackListener = new ConfirmCallBackListener("sendMessage", this);
                this.getView().showConfirm("是否确定向供应商发送短信、邮箱通知？", MessageBoxOptions.YesNo,
                        confirmCallBackListener);
                evt.setCancel(true);
                break;
            }
        }
    }

    /**
     * 确认弹窗点击确认后回调事件
     * @param messageBoxClosedEvent
     */
    @Override
    public void confirmCallBack(MessageBoxClosedEvent messageBoxClosedEvent) {
        super.confirmCallBack(messageBoxClosedEvent);
        if("sendMessage".equals(messageBoxClosedEvent.getCallBackId())) {
            if (MessageBoxResult.Yes.equals(messageBoxClosedEvent.getResult())) {
                //向供应商发送短信，邮件
                DynamicObject bill = this.getModel().getDataEntity();
                ApiResult result = QCFeedBackHelper.sendMessage(new DynamicObject[]{bill});
                if (!result.getSuccess()) {
                    this.getView().showErrorNotification(result.getMessage());
                } else {
                    this.getView().showSuccessNotification("发送成功！");
                }
            }
        }
    }

    /**
     * F7监听注册
     * @author: hst
     * @createDate: 2022/11/15
     */
    public void addF7SelectorListener() {
        BasedataEdit supplyEdit = this.getView().getControl(QCFeedBackHelper.SUPPLIER_FIELD);
        //弹窗前过滤
        supplyEdit.addBeforeF7SelectListener((evt) -> {
            ListShowParameter showParameter = (ListShowParameter) evt.getFormShowParameter();
            showParameter.setCustomParam("groupStandard", BillAssistConstant.GROUP_STANDARD_ID);
            showParameter.setCustomParam("setSupStatusShowAll", "true");
        });
    }
}
