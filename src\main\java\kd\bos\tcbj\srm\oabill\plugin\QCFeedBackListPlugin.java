package kd.bos.tcbj.srm.oabill.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.form.ConfirmCallBackListener;
import kd.bos.form.MessageBoxOptions;
import kd.bos.form.MessageBoxResult;
import kd.bos.form.container.Container;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.list.ListShowParameter;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.mvc.list.ListView;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper;
import kd.scm.common.constant.BillAssistConstant;

import java.util.EventObject;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @package kd.bos.tcbj.srm.oabill.plugin.QCFeedBackListPlugin
 * @className QCFeedBackListPlugin
 * @author: hst
 * @createDate: 2022/11/8
 * @description: 质量反馈单列表界面插件
 * @version: v1.0
 */
public class QCFeedBackListPlugin extends AbstractListPlugin {

    private final static String SENDMESS_BTN = "yd_sendmessage";

    @Override
    public void beforeItemClick(BeforeItemClickEvent evt) {
        String key = evt.getItemKey();
        switch (key) {
            case SENDMESS_BTN : {
                // 供应商催办功能
                // 确认用户是否要发送
                ConfirmCallBackListener confirmCallBackListener = new ConfirmCallBackListener("sendMessage", this);
                this.getView().showConfirm("是否确定向供应商发送短信、邮箱通知？", MessageBoxOptions.YesNo,
                        confirmCallBackListener);
                evt.setCancel(true);
                break;
            }
        }
    }

    /**
     * 确认弹窗点击确认后回调事件
     * @param messageBoxClosedEvent
     */
    @Override
    public void confirmCallBack(MessageBoxClosedEvent messageBoxClosedEvent) {
        super.confirmCallBack(messageBoxClosedEvent);
        if("sendMessage".equals(messageBoxClosedEvent.getCallBackId())) {
            if (MessageBoxResult.Yes.equals(messageBoxClosedEvent.getResult())) {
                // 向供应商发送短信，邮件
                ListSelectedRowCollection selectedRows = this.getSelectedRows();
                Set<Object> ids = new HashSet<>();
                // 获取所选数据
                for (ListSelectedRow row : selectedRows) {
                    ids.add(row.getPrimaryKeyValue().toString());
                }
                String type = ((ListView) this.getView()).getBillFormId();
                DynamicObject[] bills = BusinessDataServiceHelper.load(ids.toArray(),
                        BusinessDataServiceHelper.newDynamicObject(type).getDynamicObjectType());
                // 错误信息
                StringBuffer errStr = new StringBuffer();
                for (DynamicObject bill : bills) {
                    // 发送邮件、短信
                    ApiResult result = QCFeedBackHelper.sendMessage(new DynamicObject[]{bill});
                    if (!result.getSuccess()) {
                        errStr.append(result.getMessage());
                    }
                }
                if (errStr.length() > 0) {
                    this.getView().showErrorNotification(errStr.toString());
                } else {
                    this.getView().showSuccessNotification("发送成功！");
                }
            }
        }
    }
}
