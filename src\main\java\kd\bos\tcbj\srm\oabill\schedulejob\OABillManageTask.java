package kd.bos.tcbj.srm.oabill.schedulejob;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.oabill.helper.OABillManageHelper;
import kd.bos.tcbj.srm.oabill.helper.PackMatCommissHelper;
import kd.bos.tcbj.srm.oabill.helper.PacksUpApplyBillHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.oabill.schedulejob.OABillManageTask
 * @className OABillManageTask
 * @author: hst
 * @createDate: 2022/08/26
 * @version: v1.0
 */
public class OABillManageTask extends AbstractTask {

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        String key = map.get("key").toString();
        List<String> ids = map.containsKey("ids") ? (List<String>) map.get("ids") : null;
        switch (key) {
            //包装物料试机打样申请单更新台账
            case "packMatCommiss" : {
                if (Objects.isNull(ids) || ids.size() == 0) {
                    ids = this.getData(new QFilter[]{new QFilter("yd_beused", QFilter.equals, true),
                                    new QFilter("yd_beupdatedbook", QFilter.equals, false)},
                            PackMatCommissHelper.ENTITY_MAIN, "id");
                }
                if (ids.size() > 0) {
                    PackMatCommissHelper.updatePacksUpSumBill(ids);
                }
                break;
            }
            //解析包材供应商新增/变更申请单并更新台账
            case "analysispacksUpApply" : {
                if (Objects.isNull(ids) || ids.size() == 0) {
                    ids = this.getData(new QFilter[]{new QFilter("yd_beupdatedbook", QFilter.equals, false)},
                            PacksUpApplyBillHelper.MAINENTITY, "id");
                }
                if (ids.size() > 0) {
                    PacksUpApplyBillHelper.analysisOABillData(ids);
                    PacksUpApplyBillHelper.updatePacksUpSumBill(ids);
                }
                break;
            }
            //解析非生产物料供应商新增/变更申请单并更新台账
            case "analysiUnProScpBill" : {
                if (Objects.isNull(ids) || ids.size() == 0) {
                    ids = this.getData(new QFilter[]{new QFilter("yd_beupdatedbook", QFilter.equals, false)},
                            "yd_unproscpbill", "id");
                }
                if (ids.size() > 0) {
                    OABillManageHelper.analysiUnProScpBill(ids);
                    OABillManageHelper.updateUnProSupSumNill(ids);
                }
                break;
            }
            //非生产物料试机打样更新台账
            case "unProScpBillUpApply" : {
                if (Objects.isNull(ids) || ids.size() == 0) {
                    ids = this.getData(new QFilter[]{new QFilter("yd_beupdatedbook", QFilter.equals, false)},
                            "yd_unpromatcommissbill", "id");
                }
                if (ids.size() > 0) {
                    OABillManageHelper.updateUnProSupBill(ids.stream().collect(Collectors.toSet()));
                }
                break;
            }
        }
    }

    public List<String> getData(QFilter[] qFilters, String entityName, String fields) {
        List<String> ids = new ArrayList<>();
        DataSet id = QueryServiceHelper.queryDataSet("OABillManageTask", entityName,fields,qFilters,null);
        for(Row row : id) {
            ids.add(row.getString("id"));
        }
        return ids;
    }
}
