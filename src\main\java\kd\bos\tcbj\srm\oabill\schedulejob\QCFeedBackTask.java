package kd.bos.tcbj.srm.oabill.schedulejob;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.oabill.helper.OABillManageHelper;
import kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper;
import kd.taxc.common.util.StringUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @package kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper
 * @className QCFeedBackHelper
 * @author: hst
 * @createDate: 2022/11/15
 * @version: v1.0
 */
public class QCFeedBackTask extends AbstractTask {

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        String key = map.get("key").toString();
        switch (key) {
            case "sendEmail" : {
                // 向超时未确认供应商发送邮件
                sendMessage();
                break;
            }
            case "analysisQCFeedBack" : {
                List<String> ids;
                if (map.containsKey("ids")) {
                    ids = (List<String>) map.get("ids");
                } else {
                    ids = this.getData(new QFilter[]{new QFilter("yd_beused", QFilter.equals, false)},
                            "yd_scp_qcfeedback", "id");
                }
                if (ids.size() > 0) {
                    String userName = map.get("userName").toString();
                    if (StringUtil.isBlank(userName)) {
                        throw new KDBizException("请在自定义参数中设置默认用户名userName");
                    }
                    ApiResult result = OABillManageHelper.analysiQCFeedBack(ids,userName);
                    if (!result.getSuccess()) {
                        throw new KDBizException(result.getMessage());
                    }
                }
                break;
            }
        }
    }

    /**
     * 向超时未确认供应商发送邮件
     * @author: hst
     * @createDate: 2022/11/24
     */
    public void sendMessage() {
        String homeDate = DateUtil.date2strByLocalDateTime(DateUtil.addDays(new Date(),-5),"yyyy-MM-dd HH:mm:ss"); //国内供应商未确认天数
        String abroadDate = DateUtil.date2strByLocalDateTime(DateUtil.addDays(new Date(),-7),"yyyy-MM-dd HH:mm:ss"); //国外供应商未确认天数
        QFilter qFilter = QFilter.of("yd_supplierstatus = ? and ((yd_suppliertype = '1' and createtime <= ?) " +
                "or (yd_suppliertype = '2' and createtime <= ?))","A",homeDate,abroadDate);
        // 查询符合日期条件的未确认单据(供应商产品质量反馈单）
        DynamicObject[] qcData = BusinessDataServiceHelper.load(QCFeedBackHelper.MAIN_ENTITY,
                QCFeedBackHelper.ERRLOTFIELDS,new QFilter[]{qFilter});
        // 发送邮件
        if (qcData.length > 0) {
            QCFeedBackHelper.sendMessage(qcData);
        }
    }

    /**
     * 查询未解析的质量反馈单
     * @param qFilters
     * @param entityName
     * @param fields
     * @return
     */
    public List<String> getData(QFilter[] qFilters, String entityName, String fields) {
        List<String> ids = new ArrayList<>();
        DataSet id = QueryServiceHelper.queryDataSet(this.getClass().getName(), entityName,fields,qFilters,null);
        for(Row row : id) {
            ids.add(row.getString("id"));
        }
        return ids;
    }
}
