package kd.bos.tcbj.srm.oabill.utils;

import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import json.JSONArray;
import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.utils.ImportBillUtils;
import kd.taxc.common.util.StringUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.oabill.utils.OABillManageUtil
 * @className OABillManageUtil
 * @author: hst
 * @createDate: 2022/08/29
 * @version: v1.0
 */
public class OABillManageUtil {

    /**
     * @author: hst
     * @createDate: 2022/08/29
     * @param json
     * @return
     */
    public static Map parseJson(String json)
    {
        Map jsonMap=new Gson().fromJson(json, Map.class);
        return jsonMap;
    }

    /**
     * OA格式转换为普通格式
     * <AUTHOR>
     * @createDate 2022/08/29
     * @param oaMap
     * @return
     */
    public static Map transMap(Map oaMap)
    {
        Map tarMap=new HashMap();
        List fields=(List)oaMap.get("fields");
        for(int i = 0;i < fields.size();i++)
        {
            Map fieldMap = (Map) fields.get(i);
            System.out.println(fieldMap);
            String type = (String) fieldMap.get("type");
            String item = (String) fieldMap.get("item");
            if(type.equals("List"))
            {
                List vList = (List) fieldMap.get("val");
                List tarList = new ArrayList();
                if(vList!=null)
                {
                    for(int j = 0;j < vList.size();j++)
                    {
                        Map entryMap = (Map) vList.get(j);
                        List entryList = (List) entryMap.get("line");
                        if(entryList != null)
                        {
                            Map tarEntMap = new HashMap();
                            for(int k = 0;k < entryList.size();k++)
                            {
                                Map entMap = (Map) entryList.get(k);
                                tansItem(entMap,tarEntMap);
                            }
                            tarList.add(tarEntMap);
                        }
                    }
                }
                tarMap.put(item,tarList);

            }else
            {//基础数据类型
                tansItem(fieldMap,tarMap);
            }
        }
        return tarMap;
    }

    /**
     * @author: hst
     * @createDate: 2022/08/29
     * @param fieldMap
     * @param tarMap
     */
    private static void tansItem(Map fieldMap,Map tarMap) {
        String type = (String) fieldMap.get("type");
        String item = (String) fieldMap.get("item");
        Object value = null;
        if (type.equals("Enumer")) {
            Map vMap = (Map) fieldMap.get("val");
            if (vMap != null) {
                Iterator it = vMap.values().iterator();
                StringBuffer bf = new StringBuffer();
                while (it.hasNext()) {
                    Object emValue = it.next();
                    bf.append(emValue).append(",");
                }
                if (bf.length() > 0) {
                    bf.deleteCharAt(bf.length() - 1);
                }
                value = bf.toString();
            }
        } else {
            value = fieldMap.get("val");
        }
        tarMap.put(item, value);
    }

    /**
     * 更新元数据字段，为空不更新
     * <AUTHOR>
     * @createDate 2022/8/29
     * @param dynamicObject
     * @param map
     */
    public static void updateField (DynamicObject dynamicObject, Map<String,Object> map) {
        for (Map.Entry<String,Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                try {
                    dynamicObject.set(entry.getKey(),entry.getValue());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 大文本字段处理（截取前20位）
     * <AUTHOR>
     * @createDate 2022/8/29
     */
    public static void handleLargeText (Object[][] mapping,List<DynamicObject> analyBills) {
        Arrays.stream(mapping).collect(Collectors.toList()).stream().forEach(map -> {
            if (map[1].toString().endsWith("_tag")) {
                analyBills.stream().forEach(analyBill -> {
                    analyBill.set(map[1].toString().substring(0, map[1].toString().lastIndexOf("_tag")),
                            analyBill.getString(map[1].toString()).length() > 15 ?
                                    analyBill.getString(map[1].toString()) + "..." : analyBill.getString(map[1].toString()));
                });
            }
        });
    }

    public static void analysisOABillData (List<String> ids,String EntityName, String jsonField, String statusField) {
        DynamicObject[] analyBills = BusinessDataServiceHelper.load(ids.toArray(new Object[ids.size()]),
                BusinessDataServiceHelper.newDynamicObject(EntityName).getDynamicObjectType());
        Arrays.asList(analyBills).stream().forEach(analyBill -> {
            try {
                String jsonStr = analyBill.getString(jsonField);
                boolean isBeused = analyBill.getBoolean("yd_beused");
                if (StringUtil.isNotBlank(jsonStr) && !isBeused) {
                    Map oaData = OABillManageUtil.parseJson(jsonStr);
                    String formId = oaData.get("fdTemplateId").toString();
                    Map<String, Object> result = getFieldMapping(formId);
                    if (Objects.isNull(result) || result.size() == 0) {
                        throw new KDBizException("formId:" + formId + "未配置字段映射");
                    } else {
                        Map cosmicData = OABillManageUtil.transMap(oaData);
                        Object[][] cfgMap = (Object[][]) result.get("yd_mapping_tag");
                        ImportBillUtils.convertBill(analyBill, cosmicData, cfgMap, null);
                        if (StringUtil.isNotEmpty(statusField)) {
                            analyBill.set(statusField, true);
                        }
                        OABillManageUtil.handleLargeText(cfgMap, Arrays.asList(analyBill));
                    }
                }
            } catch (Exception e) {
                if (StringUtils.isNotBlank(e.getMessage())) {
                    String errMsg = e.getMessage();
                    analyBill.set("yd_errmsg", errMsg.length() > 500 ? errMsg.substring(0,500) : errMsg);
                }
            }
        });
        SaveServiceHelper.save(analyBills);
    }

    /**
     * 增加对枚举的处理，避免字符过长报错
     * <AUTHOR>
     * @createDate 2022/11/24
     * @param oaMap
     * @return
     */
    public static Map transJsonToMap(Map oaMap, Map<String, String> formatMap)
    {
        Map tarMap=new HashMap();
        List fields=(List)oaMap.get("fields");
        for(int i = 0;i < fields.size();i++)
        {
            Map fieldMap = (Map) fields.get(i);
            String type = (String) fieldMap.get("type");
            String item = (String) fieldMap.get("item");
            item = Objects.nonNull(formatMap) && formatMap.containsKey(item) ? formatMap.get(item) : item;
            if(type.equals("List"))
            {
                List vList = (List) fieldMap.get("val");
                List tarList = null;
                if (tarMap.containsKey(item)) {
                    tarList = (List) tarMap.get(item);
                } else {
                    tarList = new ArrayList();
                }
                if(vList!=null)
                {
                    for(int j = 0;j < vList.size();j++)
                    {
                        Map entryMap = (Map) vList.get(j);
                        List entryList = (List) entryMap.get("line");
                        if(entryList != null)
                        {
                            Map tarEntMap = new HashMap();
                            for(int k = 0;k < entryList.size();k++)
                            {
                                Map entMap = (Map) entryList.get(k);
                                tansFeedBackItem(entMap,tarEntMap,formatMap);
                            }
                            tarList.add(tarEntMap);
                        }
                    }
                }
                tarMap.put(item,tarList);

            } else if (type.equals("Enumer")){
                tansEnumer(fieldMap,tarMap,formatMap);
            }else
            {//基础数据类型
                tansFeedBackItem(fieldMap,tarMap,formatMap);
            }
        }
        return tarMap;
    }

    /**
     * OA传过来的枚举value太长，导致报错，修改为取key
     * @author: hst
     * @createDate: 2022/11/24
     * @param ids
     * @param EntityName
     * @param jsonField
     * @param statusField
     */
    public static void analysisBillData (List<String> ids,String EntityName, String jsonField, String statusField) {
        DynamicObject[] analyBills = BusinessDataServiceHelper.load(ids.toArray(),BusinessDataServiceHelper.newDynamicObject(EntityName).getDynamicObjectType());
        Arrays.asList(analyBills).stream().forEach(analyBill -> {
            String jsonStr = analyBill.getString(jsonField);
            if (StringUtil.isNotBlank(jsonStr)) {
                if (!analyBill.getBoolean(statusField)) {
                    Map oaData = OABillManageUtil.parseJson(jsonStr);
                    // update by hst 2023/05/05 字段映射修改为可配置
                    String formId = oaData.get("fdTemplateId").toString();
                    // update by hst 2023/08/15 增加单据类型
                    Map<String,Object> result = getFieldMapping(formId);
                    if (Objects.isNull(result)) {
                        analyBill.set("yd_errorreason","formId:" + formId + "未配置字段映射");
                    } else {
                        try {
                            String type = result.get("billtype").toString();
                            Object[][] mapping = (Object[][]) result.get("yd_mapping_tag");
                            Map<String, String> formatMap = (Map<String, String>) result.get("yd_format_tag");
                            analyBill.set("yd_billtype",type);
                            Map cosmicData = OABillManageUtil.transJsonToMap(oaData, formatMap);
                            ImportBillUtils.convertBill(analyBill, cosmicData, mapping, null);
                            if (StringUtil.isNotEmpty(statusField)) {
                                analyBill.set(statusField, true);
                            }
                            // update by hst 2022/12/05 记录OA登录账号
                            Map baseInfo = (LinkedTreeMap) oaData.get("baseInfo");
                            String loginName = baseInfo.get("fdLoginName").toString();
                            analyBill.set("yd_loginname", StringUtil.isNotBlank(loginName) ? loginName.substring(0, loginName.indexOf("@")) : "");
                            // oa流程地址
                            String fdTemplateId = oaData.get("formId").toString();
                            analyBill.set("yd_oaurl", "http://eip.by-health.com/km/review/km_review_main/kmReviewMain.do?method=view&fdId=" + fdTemplateId + "&s_css=default");
                            OABillManageUtil.handleLargeText(mapping, Arrays.asList(analyBill));
                            // update by hst 2023/05/09 下拉框处理,传递下拉值携带中文字符
                            analyBill.getDynamicObjectCollection("yd_materialentity").stream().forEach(entry -> {
                                String riskLevel = entry.getString("yd_risklevel");
                                entry.set("yd_risklevel",riskLevel.equals("高风险") ? "1" : riskLevel.equals("中风险") ? "2" : riskLevel.equals("低风险") ? "3" : riskLevel);
                            });
                            analyBill.set("yd_errorreason","");

                            // 分录处理
                            List<DynamicObject> delEntries = new ArrayList<>();
                            DynamicObjectCollection entries = analyBill.getDynamicObjectCollection("yd_materialentity");
                            for (DynamicObject entry : entries) {
                                String matNo = entry.getString("yd_materialno");
                                if (StringUtils.isBlank(matNo)) {
                                    delEntries.add(entry);
                                }
                            }
                            if (delEntries.size() > 0) {
                                for (DynamicObject entry : delEntries) {
                                    entries.remove(entry);
                                }
                            }
                        } catch (Exception e) {
                            String erroeMessage = e.getMessage();
                            if (erroeMessage.length() > 0) {
                                analyBill = BusinessDataServiceHelper.loadSingle(analyBill.getPkValue(),"yd_scp_qcfeedback");
                                analyBill.set("yd_errorreason",erroeMessage.length() > 200 ? erroeMessage.substring(0,200) : erroeMessage);
                            }
                        }
                    }
                }
            }
            SaveServiceHelper.save(new DynamicObject[]{analyBill});
        });
    }

    /**
     * 枚举类型处理
     * @param fieldMap
     * @param tarMap
     */
    private static void tansEnumer(Map fieldMap,Map tarMap,Map<String, String> formatMap) {
        String item = (String) fieldMap.get("item");
        item = Objects.nonNull(formatMap) && formatMap.containsKey(item) ? formatMap.get(item) : item;
        Object value = null;
        Map vMap = (Map) fieldMap.get("val");
        if (vMap != null) {
            Iterator it = vMap.keySet().iterator();
            StringBuffer bf = new StringBuffer();
            while (it.hasNext()) {
                Object emValue = it.next();
                bf.append(emValue).append(",");
            }
            if (bf.length() > 0) {
                bf.deleteCharAt(bf.length() - 1);
            }
            value = bf.toString();
        }
        // update by hst 2023/05/06 优化取数逻辑，仅保存不为空的数据
        if (Objects.nonNull(value) && StringUtils.isNotBlank(value.toString())) {
            tarMap.put(item, value);
        }
    }

    /**
     * 获取供应商质量反馈单字段映射
     * @author: hst
     * @createDate: 2022/11/24
     * @return
     */
    public static Object[][] getFeedBackFieldMapping(Map oaData) {
        String formId = oaData.get("fdTemplateId").toString();
        // 原辅料
        if ("184a31de8156de9d719430f4ac985aea".equals(formId)) {
            Object[][] mapping = {
                    {"fd_331f0754328d50", "billno"},  //流水单号
                    {"fd_3391ca4da0b862","yd_preparer"}, //填表人
                    {"fd_331f0744748332", "yd_manufacturer"},  //生产商
                    {"fd_3391cac8bba206", "yd_agent"},  //代理商
                    {"fd_39807e9a6eb55a", "yd_level"},  //风险等级
                    {"fd_339faf33a37072", "yd_materialentity"},  // 物料分录
                    {"fd_331f0610fd94b6", "yd_materialno"},  //物料编码
                    {"fd_331f061bafb22a", "yd_materialname"},  //物料名称
                    {"fd_331f0628793c10", "yd_lotbillno"},  //批号
                    {"fd_331f062ee61d72", "yd_productno"},  //厂家批号
                    {"fd_331f0636a1be66", "yd_lotnum"},  //批数量
                    {"fd_3748959e706648", "yd_errmsg"},  //质量问题
                    {"fd_374895ca6393f0", "yd_inspectvalue"},  //供应商出厂（检验值）
                    {"fd_374895e96499de", "yd_limits"},  //行业（国家）限值
                    {"fd_3395755b19de20", "yd_errtype"},  //问题类型
                    {"fd_39807f35bab9bc", "yd_risklevel"},  //风险等级
                    {"fd_35d7f37f217ab0", "yd_rejectsnum"},  //不良品数量（袋）
                    {"fd_35d7f39d2a5db4", "yd_rejectsscale"},  //不良品重量（kg）
                    {"fd_35d7f3ad8dab34", "yd_rejectrate"},  //不良比例（%）
                    {"fd_35d7f3c86738f2", "yd_surplus"},  //库存剩余量（kg)
                    {"fd_3a2a622a6aa124", "yd_difference"},  //相同厂家批号不同入库批号
                    {"fd_3a2a623bc9331a", "yd_residuestock"}  //剩余库存（kg）
            };
            return mapping;
        } else {
            // update by hst 2023/03/29 修改字段映射
            Object[][] mapping = {
                    {"fd_33919f87342456", "billno"},  //流水单号
                    {"fd_33919fc28a4396","yd_preparer"}, //填表人
                    {"fd_3b829f0711be9c", "yd_manufacturer"},  //生产商
                    {"fd_3b829f133a496c", "yd_agent"},  //代理商
                    {"fd_3b829c75e5eed6", "yd_risklevel"},  //风险等级（分录）
                    {"fd_33919fd86f83a6", "yd_materialentity"},  // 物料分录
                    {"fd_3391a002cdc0b0", "yd_materialno"},  //物料编码
                    {"fd_3391a004a9a048", "yd_materialname"},  //物料名称
                    {"fd_3391a00614ee4c", "yd_lotbillno"},  //批号
                    {"fd_3391a00d029a70", "yd_productno"},  //厂家批号
                    {"fd_3b9622cccc4dd4", "yd_lotnum"},  //批数量
                    {"fd_3b8335b0c63148", "yd_feedback"},  //反馈问题类型
                    {"fd_3b829e6e8bd420", "yd_errnum"},  //不良数量及比例
                    {"fd_38f0b9e24eedac", "yd_requantity"},  //同厂家批次号库存剩余量
                    {"fd_3b897bec19b7b6", "yd_errmsg"},  //质量问题
                    {"fd_3a1392ff1dfffe", "yd_level"},  //风险等级（内包材）
                    {"fd_3a13935aafedce", "yd_level"},  //风险等级（外包材）
            };
            return mapping;
        }
    }

    /**
     * 质量反馈单需特殊处理
     * @author: hst
     * @createDate: 2022/11/25
     * @param fieldMap
     * @param tarMap
     */
    private static void tansFeedBackItem(Map fieldMap,Map tarMap,Map<String, String> formatMap)
    {
        String type=(String)fieldMap.get("type");
        String item=(String)fieldMap.get("item");
        item = Objects.nonNull(formatMap) && formatMap.containsKey(item) ? formatMap.get(item) : item;
        Object value=null;
        if(type.equals("Enumer"))
        {
            Map vMap=(Map)fieldMap.get("val");
            if(vMap!=null)
            {
                Iterator it;
                it = vMap.values().iterator();
                StringBuffer bf=new StringBuffer();
                while(it.hasNext())
                {
                    Object emValue=it.next();
                    bf.append(emValue).append(",");
                }
                if(bf.length()>0)
                {
                    bf.deleteCharAt(bf.length()-1);
                }
                value=bf.toString();
            }
        } else
        {
            value=fieldMap.get("val");
        }
        tarMap.put(item, value);
    }

    /**
     * 解析字段映射
     * @author: hst
     * @createDate: 2023/05/06
     */
    public static Map<String,Object> getFieldMapping (String formId) {
        Map<String,Object> resutls = new HashMap<>();
        //获取参数配置
        QFilter typeFilter = new QFilter("yd_bill",QFilter.equals,"3");
        QFilter formFilter = new QFilter("yd_formid",QFilter.equals,formId);
        QFilter statusFilter = new QFilter("billstatus",QFilter.equals,"C");
        // update by hst 2023/08/15 增加单据类型
        DataSet configs = QueryServiceHelper.queryDataSet("OABillManageHelper","yd_paramconfigure",
                "yd_mapping_tag,yd_billtype,yd_format_tag",new QFilter[]{typeFilter,formFilter,statusFilter}, null);
        if (configs.hasNext()) {
            Row row = configs.next();
            String type = row.getString("yd_billtype");
            String json = row.getString("yd_mapping_tag");
            String format = row.getString("yd_format_tag");
            Object[][] mappings = jsonArrayToArray(json);
            Map<String, String> formats = OABillManageUtil.jsonArrayToMap(format);
            resutls.put("billtype",type);
            resutls.put("yd_mapping_tag",mappings);
            resutls.put("yd_format_tag",formats);
            return resutls;
        }
        return resutls;
    }

    /**
     * 将JSONArrau转换为二维数组
     * @param json
     * @return
     * @author: hst
     * @createDate: 2023/05/06
     */
    public static Object[][] jsonArrayToArray (String json) {
        if (kd.bos.tcbj.srm.utils.StringUtils.isNotBlank(json)) {
            JSONArray jsonArray = JSONArray.parseArray(json);
            int size = jsonArray.size();
            Object[][] temps = new Object[size][2];
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONArray arr = (JSONArray)jsonArray.get(i);;
                for (int j = 0; j < arr.size(); j++) {
                    temps[i][j] = arr.get(j);
                }
            }
            return temps;
        }
        return null;
    }

    /**
     * 将JSONArrau转换为map
     * @param json
     * @return
     * @author: hst
     * @createDate: 2024/07/19
     */
    public static Map<String,String> jsonArrayToMap (String json) {
        if (StringUtils.isNotBlank(json)) {
            JSONArray jsonArray = JSONArray.parseArray(json);
            Map<String,String> temps = new HashMap<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject object = (JSONObject) jsonArray.get(i);
                for (Map.Entry<String, Object> entry : object.entrySet()) {
                    temps.put(entry.getKey(), entry.getValue().toString());
                }
            }
            return temps;
        }
        return null;
    }
}
