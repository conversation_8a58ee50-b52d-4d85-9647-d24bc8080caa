package kd.bos.tcbj.srm.onsiteaudit.helper;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.srm.admittance.helper.PhoneBizHelper;
import kd.taxc.common.util.StringUtil;

import java.util.*;

/**
 * @package kd.bos.tcbj.srm.onsiteaudit.helper.CorrectionBillHelper
 * @className CorrectionBillHelper
 * @author: hst
 * @createDate: 2022/09/16
 * @version: v1.0
 */
public class CorrectionBillHelper {

    private final Log logger = LogFactory.getLog(this.getClass().getName());
    public final static String ENTITY_MAIN = "yd_correctionbill";
    public final static String FIELD_CREATEDATE = "createtime";
    public final static String FIELD_BILLSTATUS = "billstatus";
    public final static String FIELD_SUPPLIERUSER = "yd_supplieruser";

    /**
     * 供应商不符合项整改单超时未处理提醒（20天）
     * @author: hst
     * @createDate: 2022/09/16
     */
    public void timeoutReminder() {
        //获取20天前的日期
        String date = DateTimeUtils.format(DateTimeUtils.addDate(new Date(),-20),"yyyy-MM-dd HH:mm:ss");
        //日期过滤
        QFilter qFilter_1 = new QFilter(FIELD_CREATEDATE, QFilter.less_equals,date);
        //状态过滤
        QFilter qFilter_2 = new QFilter(FIELD_BILLSTATUS, QFilter.equals,"B");
        Set<String> phones = new HashSet<>();
        DynamicObjectCollection correctionBills = QueryServiceHelper.query(ENTITY_MAIN,FIELD_SUPPLIERUSER + ".phone",
                new QFilter[]{qFilter_1,qFilter_2});
        for (DynamicObject correctionBill : correctionBills) {
            String phone = correctionBill.getString(FIELD_SUPPLIERUSER + ".phone");
            if (StringUtil.isNotBlank(phone)) {
                phones.add(phone);
            }
        }
        if (phones.size() > 0) {
            this.sendShortMessage(phones);
        }
    }

    /**
     * 发送短信
     * @author: hst
     * @createDate: 2022/09/16
     * @param phones 手机号码集合
     */
    public void sendShortMessage(Set<String> phones) {
        String path = RequestContext.get().getClientFullContextPath();
        String url = "";
        if (path.endsWith("/")) {
            url = path + "login.html";
        } else {
            url = path + "/login.html";
        }
        String content = "请登录汤臣倍健SRM系统（" + url + "）回复不符合项整改情况，谢谢。";
        ShortMessageInfo messageInfo = new ShortMessageInfo();
        messageInfo.setPhone(new ArrayList<>(phones));
        messageInfo.setCountryCode(CountryCode.CN);
        messageInfo.setMessage(content);
        Map<String,Object> result = MessageHandler.sendShortMessage(messageInfo);
        if (!(boolean) result.get("result")) {
            logger.error(result.get("description").toString());
        }
    }
}
