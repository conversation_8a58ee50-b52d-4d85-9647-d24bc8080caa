package kd.bos.tcbj.srm.onsiteaudit.plugin;

import kd.bos.context.RequestContext;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.tcbj.srm.onsiteaudit.schedulejob.CorrectionBillTask;

import java.util.AbstractList;
import java.util.HashMap;
import java.util.Map;

/**
 * @package kd.bos.tcbj.srm.onsiteaudit.plugin.CorrectionBillListPlugin
 * @className CorrectionBillListPlugin
 * @author: hst
 * @createDate: 2022/09/16
 * @version: v1.0
 */
public class CorrectionBillListPlugin extends AbstractListPlugin {

    private final static String BTN_TIMEOUT = "yd_timeout";
    /**
     * 按钮点击事件
     * @author: hst
     * @createDate: 2022/09/16
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case BTN_TIMEOUT : {
                Map<String,Object> map = new HashMap<>();
                map.put("key","timeout");
                new CorrectionBillTask().execute(RequestContext.get(),map);
                break;
            }
        }
    }
}
