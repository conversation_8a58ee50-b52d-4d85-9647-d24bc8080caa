package kd.bos.tcbj.srm.onsiteaudit.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.srm.onsiteaudit.helper.CorrectionBillHelper;

import java.util.Map;

/**
 * @package kd.bos.tcbj.srm.onsiteaudit.schedulejob.CorrectionBillTask
 * @className CorrectionBillTask
 * @author: hst
 * @createDate: 2022/09/16
 * @version: v1.0
 */
public class CorrectionBillTask  extends AbstractTask {

    private final static String TIMEOUT = "timeout";

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        String key = map.get("key").toString();
        switch (key) {
            case TIMEOUT : {
                //超时提醒
                new CorrectionBillHelper().timeoutReminder();
                break;
            }
        }
    }
}
