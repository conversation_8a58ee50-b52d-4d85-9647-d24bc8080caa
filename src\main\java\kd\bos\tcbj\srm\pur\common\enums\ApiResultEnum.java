package kd.bos.tcbj.srm.pur.common.enums;

/**
 * @package: kd.bos.tcbj.srm.pur.common.enums.ApiResultEnum
 * @className ApiResultEnum
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 * @descrition: 接口请求返回码枚举
 */
public enum ApiResultEnum {

    // 参数值为空
    NONUL("pur.yd.100001","参数不能为空！");

    private String code;
    private String value;
    ApiResultEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
