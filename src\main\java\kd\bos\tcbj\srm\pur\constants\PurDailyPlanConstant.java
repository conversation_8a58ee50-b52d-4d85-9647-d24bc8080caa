package kd.bos.tcbj.srm.pur.constants;

/**
 * @package: kd.bos.tcbj.srm.pur.constants.PurDailyPlanConstant
 * @className PurDailyplanConstants
 * @author: hst
 * @createDate: 2024/06/07
 * @version: v1.0
 * @descrition: APS日需求计划常量类
 */
public class PurDailyPlanConstant {

    // 单据标识
    public static String MAIN_ENTITY = "yd_pur_dailyplan";
    // 创建人
    public static String CREATOR_FIELD = "creator";
    // 创建时间
    public static String CREATETIME_FIELD = "createtime";
    // 修改时间
    public static String MODIFYTIME_FIELD = "modifytime";
    // 物料编码
    public static String MATNUM_FIELD = "yd_matnum";
    // 物料名称
    public static String MATNAME_FIELD = "yd_matname";
    // 物料
    public static String MATERIAL_FIELD = "yd_material";
    // 业务日期
    public static String BIZDATE_FIELD = "yd_bizdate";
    // 日需求计划
    public static String PLANQRY_FIELD = "yd_planqty";
    // 是否已计算
    public static String ISCAL_FIELD = "yd_iscalculate";
    // 已分配数量
    public static String ALLQTY_FIELD = "yd_allocatedqty";
    // 未分配数量
    public static String UNALLQTY_FIELD = "yd_unallocatedqty";
    // 计算失败原因
    public static String ERRMSG_FIELD = "yd_errmsg";
    // 单据状态
    public static String BILLSTATUS_FIELD = "billstatus";

    /**
     * 获取字段
     * @return
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static String getFields () {
        return MATNUM_FIELD + "," + MATNAME_FIELD + "," + BIZDATE_FIELD + ","
                + PLANQRY_FIELD + "," + ISCAL_FIELD + "," + ALLQTY_FIELD + ","
                + UNALLQTY_FIELD + "," + ERRMSG_FIELD + "," + BILLSTATUS_FIELD + ","
                + MATERIAL_FIELD + "," + MODIFYTIME_FIELD;
    }
}
