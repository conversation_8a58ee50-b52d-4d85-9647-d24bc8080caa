package kd.bos.tcbj.srm.pur.constants;

/**
 * @package: kd.bos.tcbj.srm.pur.constants.PurMatQuotaConstant
 * @className PurMatQuotaConstant
 * @author: hst
 * @createDate: 2024/06/07
 * @version: v1.0
 * @descrition: 供应商物料配额常量类
 */
public class PurMatQuotaConstant {
    // 单据标识
    public static String MAIN_ENTITY = "yd_pur_matquota";
    // 供应商
    public static String SUPPLIER_FIELD = "yd_supplier";
    // 物料
    public static String MATERIAL_FIELD = "yd_material";
    // 业务日期
    public static String BIZDATE_FIELD = "yd_bizdate";
    // 配额数量
    public static String QTY_FIELD = "yd_quotaqty";
    // 需求计划ID
    public static String ORIID_FIELD = "yd_oribillid";

    /**
     * 获取字段
     * @return
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static String getFields () {
        return SUPPLIER_FIELD + "," + MATERIAL_FIELD + "," + BIZDATE_FIELD + ","
                + QTY_FIELD + "," + ORIID_FIELD;
    }
}
