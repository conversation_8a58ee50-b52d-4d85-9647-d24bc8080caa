package kd.bos.tcbj.srm.pur.constants;

/**
 * @package: kd.bos.tcbj.srm.pur.constants.PurDailyPlanConstant
 * @className PurMaterialAttrConstant
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 * @descrition: 物料属性常量类
 */
public class PurMaterialAttrConstant {

    // 单据标识
    public static String MAIN_ENTITY = "yd_pur_materialattr";
    // 物料编码
    public static String MATERIAL_FIELD = "yd_materiel";
    // ABC-XYZ分类
    public static String CLASSIFY_FIELD = "yd_classify";
    // 物料生命周期
    public static String LIFECYCLE_FIELD = "yd_lifecycle";
    // 物料活跃度
    public static String ACTIVITY_FIELD = "yd_activity";
    // 采购小类
    public static String SUBCLASS_FIELD = "yd_subclass";
    // 单据状态
    public static String BILLSTATUS_FIELD = "billstatus";

    /**
     * 获取字段
     * @return
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static String getFields () {
        return MATERIAL_FIELD + "," + CLASSIFY_FIELD + "," + LIFECYCLE_FIELD + ","
                + ACTIVITY_FIELD + "," + SUBCLASS_FIELD + "," + BILLSTATUS_FIELD;
    }
}
