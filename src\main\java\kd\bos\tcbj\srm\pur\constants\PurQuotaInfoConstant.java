package kd.bos.tcbj.srm.pur.constants;

/**
 * @package: kd.bos.tcbj.srm.pur.constants.PurQuotaInfoConstant
 * @className PurQuotaInfoConstant
 * @author: hst
 * @createDate: 2024/06/07
 * @version: v1.0
 * @descrition: 供应商配额信息常量类
 */
public class PurQuotaInfoConstant {
    // 单据标识
    public static String MAIN_ENTITY = "yd_pur_quotainfo";
    // 供应商
    public static String SUPPLIER_FIELD = "yd_supplier";
    // 物料编码
    public static String MATERIAL_FIELD = "yd_material";
    // 配额比例
    public static String RATE_FIELD = "yd_quotarate";
    // 核准状态
    public static String ISUSEABLE = "yd_isuseable";

    /**
     * 获取字段
     * @return
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static String getFields () {
        return SUPPLIER_FIELD + "," + MATERIAL_FIELD + ","
                + RATE_FIELD + "," + ISUSEABLE;
    }
}
