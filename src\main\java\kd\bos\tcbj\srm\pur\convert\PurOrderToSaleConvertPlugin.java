package kd.bos.tcbj.srm.pur.convert;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.ExtendedDataEntitySet;
import kd.bos.entity.botp.plugin.AbstractConvertPlugIn;
import kd.bos.entity.botp.plugin.args.AfterFieldMappingEventArgs;
import kd.bos.tcbj.srm.utils.StringUtils;

/**
 * 采购订单下推销售出库单版本号处理
 * @auditor hst
 * @createDate: 2023/04/23
 */
public class PurOrderToSaleConvertPlugin extends AbstractConvertPlugIn {

    @Override
    public void afterFieldMapping(AfterFieldMappingEventArgs e) {
        super.afterFieldMapping(e);
        ExtendedDataEntitySet entitySet = e.getTargetExtDataEntitySet();
        ExtendedDataEntity[] materialEntries = entitySet.FindByEntityKey("materialentry");
        for (ExtendedDataEntity materialEntry : materialEntries) {
            DynamicObject data = materialEntry.getDataEntity();
            String note = data.getString("note");
            try {
                if (StringUtils.isNotBlank(note)) {
                    // 截取前两位
                    String version = note.substring(0,note.length() > 2 ? 2 : note.length());
                    data.set("yd_version",version.replaceAll("[^0-9]", ""));
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
    }
}
