package kd.bos.tcbj.srm.pur.helper;

import kd.bos.cache.CacheFactory;
import kd.bos.cache.redis.RedisSessionlessCache;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.dataentity.utils.StringUtils;
import kd.bos.fileservice.FileItem;
import kd.bos.fileservice.FileService;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.fileservice.extension.FileServiceExtFactory;
import kd.bos.form.IFormView;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.threads.ThreadPools;
import kd.bos.url.UrlService;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @description（类描述）:
 * @author（创建人）: lzp
 * @createDate（创建时间）: 2025/4/11
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class AttachmentHelper {
    /**日志对象*/
    public static final Log logger = LogFactory.getLog(AttachmentHelper.class);

    /**
     * 描述：批量下载
     * @param attachEntryCol     附件分录
     * @param attachProperties   附件标识
     * @param view               视图
     * @param fileName           附件zip名称
     * @createDate : 2021年4月2日
     * @createAuthor: 陈钲尹
     */
    public static void batchDownLoad(DynamicObjectCollection attachEntryCol, String[] attachProperties, IFormView view, String fileName) {

        ZipOutputStream zipOutputStream = null;
        FileInputStream in = null;
        try {
            final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            final String date = dateFormat.format(new Date());
            fileName = ResManager.loadKDString(fileName, "AttachmentAction_4", "bos-webactions", new Object[0]) + date + ".zip";
            FileService fs = FileServiceFactory.getAttachmentFileService();
            // 创建 ZipOutputStream
            File zipfile = new File("/kddata/zip/");
            if(!zipfile.exists() || !zipfile.isDirectory()) {
                zipfile.mkdirs();
            }
            File zip = new File("/kddata/zip/"+fileName);
            if(!zip.exists()) {
                zip.createNewFile();
            }
            FileOutputStream fileout = new FileOutputStream(zip);
            zipOutputStream = new ZipOutputStream(fileout);
            // 创建 ZipEntry 对象
            ZipEntry zipEntry = null;
            String path = "";
            int fileseq = 1;
            for (DynamicObject attachEntryInfo : attachEntryCol) {
                for (String attachProperty : attachProperties) {
                    DynamicObjectCollection atts = attachEntryInfo.getDynamicObjectCollection(attachProperty);
                    for (int j = 0; j < atts.size(); j++) {
                        DynamicObject base = atts.get(j).getDynamicObject("fbasedataid");
                        String url = base.getString("url");
                        String name = base.getString("name");
                        if (url.indexOf("path=") > 0) {
                            path = getPathfromDownloadUrl(url);
                        } else {
                            path = url;
                        }
                        //先生成真实文件
                        InputStream is = fs.getInputStream(path);
                        File file = new File("/kddata/zip/" + name);
                        if (!file.exists()) {
                            file.createNewFile();
                        }
                        FileOutputStream fos = new FileOutputStream(file);
                        byte[] bf = new byte[1024];
                        int l;
                        while ((l = is.read(bf)) > -1) {
                            fos.write(bf, 0, l);
                        }
                        is.close();
                        fos.close();

                        // 实例化 ZipEntry 对象，源文件数组中的当前文件
                        zipEntry = new ZipEntry((fileseq++) + "、" + name);
                        zipOutputStream.putNextEntry(zipEntry);
                        // 定义每次读取的字节数组
                        byte[] buffer = new byte[1024];
                        int len;
                        FileInputStream _in = new FileInputStream(file);
                        while ((len = _in.read(buffer)) > -1) {
                            zipOutputStream.write(buffer, 0, len);
                        }
                        _in.close();
                    }
                }
            }
            zipOutputStream.finish();
            zipOutputStream.closeEntry();
            zipOutputStream.close();
            fileout.close();

            path = path.substring(0, path.lastIndexOf("/")+1)+fileName;
            in = new FileInputStream(zip);
            FileItem fi = new FileItem(fileName,path,in);
            fi.setCreateNewFileWhenExists(false);
            String zippath= fs.upload(fi);
            String loadurl = "";
            loadurl = UrlService.getAttachmentFullUrl(zippath);
            view.openUrl(loadurl);

            in.close();
            zip.delete();

            //缓存中保留路径
            RedisSessionlessCache cache = (RedisSessionlessCache) CacheFactory.getCommonCacheFactory().getDistributeSessionlessCache("downloadall");
            long t = System.currentTimeMillis();
            cache.addToSet("downloadall", new String[] {t+"_"+zippath});
            ThreadPools.executeOnceIncludeRequestContext("", new Runnable() {
                @Override
                public void run() {
                    FileService fs = FileServiceFactory.getAttachmentFileService();
                    RedisSessionlessCache cache = (RedisSessionlessCache) CacheFactory.getCommonCacheFactory().getDistributeSessionlessCache("downloadall");
                    String[] strs = cache.getSetValues("downloadall");
                    long t = System.currentTimeMillis();
                    for (int i = 0; i < strs.length; i++) {
                        String zippath = strs[i];
                        String _t = zippath.substring(0, zippath.indexOf("_"));
                        //最少保留一天后删除
                        if(t-Long.parseLong(_t) > 24 * 60 * 60 * 1000) {
                            fs.delete(zippath.substring(zippath.indexOf("_")+1));
                            cache.removeSetValues("downloadall", new String[] {zippath});
                        }
                    }
                }
            });

        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            logger.error(sw.toString());
            view.showErrMessage(String.format(sw.toString()),"下载异常：");
        }
    }


    private static String getPathfromDownloadUrl(final String url) throws IOException {
        String path = StringUtils.substringAfter(url, "path=");
        path = URLDecoder.decode(path, "UTF-8");
        if (path.contains("kdedcba")) {
            path = StringUtils.substringBefore(path, "&kdedcba");

        }
        if (path.contains("?v=1")) {
            path = path.replace("?v=1", "");

        }

        path = FileServiceExtFactory.getAttachFileServiceExt().getRealPath(path);
        return path;
    }

}
