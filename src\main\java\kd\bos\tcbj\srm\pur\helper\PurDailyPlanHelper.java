package kd.bos.tcbj.srm.pur.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.ILocaleString;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.db.tx.TX;
import kd.bos.db.tx.TXHandle;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.openapi.common.result.CustomApiResult;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.api.JobInfo;
import kd.bos.schedule.api.JobType;
import kd.bos.schedule.executor.JobClient;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.pur.common.enums.ApiResultEnum;
import kd.bos.tcbj.srm.pur.constants.PurDailyPlanConstant;
import kd.bos.tcbj.srm.pur.constants.PurMatQuotaConstant;
import kd.bos.tcbj.srm.pur.constants.PurQuotaInfoConstant;
import kd.bos.tcbj.srm.pur.schedulejob.PurDailyPlanTask;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.bos.workflow.engine.msg.info.MessageInfo;
import kd.scm.bid.formplugin.bill.util.DateUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.pur.helper.PurDailyPlanHelper
 * @className PurDailyPlanHelper
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 * @descrition: APS日需求计划业务类
 */
public class PurDailyPlanHelper {

    private final Log logger = LogFactory.getLog(this.getClass().getName());

    /**
     * 需求配额计算（指定数据）
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static void dailyPlanAllocation (List<Object> billIds) {
        QFilter qFilter = new QFilter("id", QFilter.in, billIds);

        DynamicObject[] plans = BusinessDataServiceHelper.load(PurDailyPlanConstant.MAIN_ENTITY,
                PurDailyPlanConstant.getFields(), new QFilter[]{qFilter});

        Map<String,Object> context = new HashMap<>();
        List<DynamicObject> errBills = new ArrayList<>();
        for (DynamicObject plan : plans) {
            try {
                // 校验日需求计划信息
                checkDailyPlanInfo(plan, context);
                // 计划分配
                plannedDistribute (plan, context);
            } catch (Exception e) {
                String errMsg = e.getMessage().length() > 200 ?
                        e.getMessage().substring(0,200) : e.getMessage();
                plan.set(PurDailyPlanConstant.ERRMSG_FIELD,errMsg);
                plan.set(PurDailyPlanConstant.ISCAL_FIELD, false);

                errBills.add(plan);
            }

        }

        if (errBills.size() > 0) {
            SaveServiceHelper.save(errBills.toArray(new DynamicObject[errBills.size()]));
        }
    }

    /**
     * 需求配额计算（获取当前未计算的）
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static void dailyPlanAllocation (String bizDate) {
        // 未计算
        QFilter qFilter = new QFilter(PurDailyPlanConstant.ISCAL_FIELD, QFilter.equals, false);
        // 大于指定日期
        if (StringUtils.isBlank(bizDate)) {
            bizDate = DateUtil.date2str(new Date(), DateUtil.FORMAT_PARTEN);
        }
        qFilter = qFilter.and(new QFilter(PurDailyPlanConstant.BIZDATE_FIELD, QFilter.large_equals, bizDate));

        DynamicObject[] plans = BusinessDataServiceHelper.load(PurDailyPlanConstant.MAIN_ENTITY,
                PurDailyPlanConstant.getFields(), new QFilter[]{qFilter});

        Map<String,Object> context = new HashMap<>();
        List<DynamicObject> errBills = new ArrayList<>();
        for (DynamicObject plan : plans) {
            try {
                // 校验日需求计划信息
                checkDailyPlanInfo(plan, context);
                // 计划分配
                plannedDistribute (plan, context);
            } catch (Exception e) {
                String errMsg = e.getMessage().length() > 200 ?
                        e.getMessage().substring(0,200) : e.getMessage();
                plan.set(PurDailyPlanConstant.ERRMSG_FIELD,errMsg);
                plan.set(PurDailyPlanConstant.ISCAL_FIELD, false);

                errBills.add(plan);
            }

        }

        if (errBills.size() > 0) {
            SaveServiceHelper.save(errBills.toArray(new DynamicObject[errBills.size()]));
        }
    }

    /**
     * 校验日需求计划信息
     * @param plan 日需求计划
     * @param context 上下文
     * @author: hst
     * @createDate: 2024/06/08
     */
    private static void checkDailyPlanInfo (DynamicObject plan, Map<String,Object> context) {
        // 校验物料是否存在
        String matNum = plan.getString(PurDailyPlanConstant.MATNUM_FIELD);
        DynamicObject material = plan.getDynamicObject(PurDailyPlanConstant.MATERIAL_FIELD);

        if (Objects.isNull(material)) {
            if (context.containsKey("material")) {
                Map<String, DynamicObject> materialMap = (Map<String, DynamicObject>) context.get("material");

                if (materialMap.containsKey(matNum)) {
                    material = materialMap.get(matNum);
                }
            }

            if (Objects.isNull(material)) {
                QFilter qFilter = new QFilter("number", QFilter.equals, matNum);

                material = BusinessDataServiceHelper.loadSingle("bd_material",
                        new QFilter[]{qFilter});

                if (Objects.nonNull(material)) {

                    if (context.containsKey("material")) {
                        Map<String, DynamicObject> materialMap = (Map<String, DynamicObject>) context.get("material");
                        materialMap.put(matNum, material);
                    } else {
                        Map<String, DynamicObject> materialMap = new HashMap<>();
                        materialMap.put(matNum, material);
                        context.put("material", materialMap);
                    }
                } else {
                    throw new KDBizException("物料不存在");
                }
            }

            plan.set(PurDailyPlanConstant.MATERIAL_FIELD, material);
        }
    }

    /**
     * 计划分配
     * @param plan 日需求计划
     * @param context 上下文
     * @author: hst
     * @createDate: 2024/06/08
     */
    private static void plannedDistribute (DynamicObject plan, Map<String,Object> context) {
        DynamicObject material = plan.getDynamicObject(PurQuotaInfoConstant.MATERIAL_FIELD);

        // 获取供应商配额信息
        DynamicObjectCollection quotas = getSupplierQuota(material,context);
        List<DynamicObject> quotaBills;
        if (quotas == null || quotas.size() == 0) {
            quotaBills = new ArrayList<>();

            plan.set(PurDailyPlanConstant.ISCAL_FIELD, false);
            plan.set(PurDailyPlanConstant.ALLQTY_FIELD, BigDecimal.ZERO);
            plan.set(PurDailyPlanConstant.UNALLQTY_FIELD, BigDecimal.ZERO);
            plan.set(PurDailyPlanConstant.ERRMSG_FIELD, "获取不到物料：" + material.getString("number") + "配额信息");
        } else {
            // 新增供应商物料配额记录
            quotaBills = createPurMatQuota(plan, quotas);
        }

        // 按物料+业务日期维度查询当前日期是否已有配额
        DynamicObject[] hisRecords = queryHistoricalRecords(plan);

        // 判断供应商配额是否有变化
        Map<String,String> message = compareQuotaDiff(plan, quotaBills, hisRecords);

        // 新配额保存，若存在旧配额则删除，同时保存需求计划为以计算
        try (TXHandle h = TX.requiresNew()) {
            try {
               SaveServiceHelper.save(quotaBills.toArray(new DynamicObject[quotaBills.size()]));

               if (hisRecords.length > 0) {
                   List<Object> recordIds = Arrays.asList(hisRecords).stream().map(record ->
                           record.getPkValue()).collect(Collectors.toList());
                   DeleteServiceHelper.delete(PurMatQuotaConstant.MAIN_ENTITY,
                           new QFilter[]{new QFilter("id", QFilter.in, recordIds)});
               }

               SaveServiceHelper.save(new DynamicObject[]{plan});

            } catch (Exception e) {
                h.markRollback();
                throw e;
            }
        }

        // 向供应商发送短信
//        if (message.size() > 0) {
//            sendMessageToSupplierUser(message);
//        }
    }

    /**
     * 根据物料获取供应商配额
     * @param material
     * @return
     */
    private static DynamicObjectCollection getSupplierQuota (DynamicObject material, Map<String,Object> context) {
        String matNum = material.getString("number");
        DynamicObjectCollection quotas = null;

        if (context.containsKey("materialQuota")) {
            Map<String,DynamicObjectCollection> quotaMap = (Map<String,DynamicObjectCollection>) context.get("materialQuota");

            if (quotaMap.containsKey(matNum)) {
                quotas = quotaMap.get(matNum);
            }
        }

        if (Objects.isNull(quotas) || quotas.size() == 0) {
            QFilter qFilter = new QFilter(PurQuotaInfoConstant.MATERIAL_FIELD, QFilter.equals, material.getPkValue());
            qFilter = qFilter.and(new QFilter(PurQuotaInfoConstant.ISUSEABLE, QFilter.equals, "1"));
            qFilter = qFilter.and(new QFilter(PurQuotaInfoConstant.RATE_FIELD, QFilter.large_than, 0));

            quotas = QueryServiceHelper.query(PurQuotaInfoConstant.MAIN_ENTITY, PurQuotaInfoConstant.getFields(),
                    new QFilter[]{qFilter}, PurQuotaInfoConstant.RATE_FIELD + " desc");

            if (Objects.nonNull(quotas) && quotas.size() > 0) {

                if (context.containsKey("materialQuota")) {
                    Map<String,DynamicObjectCollection> materialMap = (Map<String,DynamicObjectCollection>) context.get("materialQuota");
                    materialMap.put(matNum,quotas);
                } else {
                    Map<String,DynamicObjectCollection> quotaMap = new HashMap<>();
                    quotaMap.put(matNum,quotas);
                    context.put("materialQuota",quotaMap);
                }

                return quotas;
            }
        }
        return quotas;
    }

    /**
     * 新增供应商物料配额记录
     * @param plan 日需求计划
     * @param quotas 配额信息
     * @author: hst
     * @createDate: 2024/06/08
     */
    private static List<DynamicObject> createPurMatQuota (DynamicObject plan, DynamicObjectCollection quotas) {
        List<DynamicObject> quotaBills = new ArrayList<>();
        BigDecimal planQty = plan.getBigDecimal(PurDailyPlanConstant.PLANQRY_FIELD);
        BigDecimal allQty = BigDecimal.ZERO;

        for (DynamicObject quota : quotas) {
            BigDecimal ratio = quota.getBigDecimal(PurQuotaInfoConstant.RATE_FIELD);
            BigDecimal qty = planQty.multiply(ratio.divide(new BigDecimal("100")));

            if (allQty.add(qty).compareTo(planQty) >= 0) {
                qty = planQty.subtract(allQty);
            }

            allQty = allQty.add(qty);

            // 分配数量大于0，新增一条记录
            if (qty.compareTo(BigDecimal.ZERO) > 0) {

                DynamicObject quotaBill = BusinessDataServiceHelper.newDynamicObject(PurMatQuotaConstant.MAIN_ENTITY);
                // 供应商
                quotaBill.set(PurMatQuotaConstant.SUPPLIER_FIELD, quota.getString(PurQuotaInfoConstant.SUPPLIER_FIELD));
                // 物料
                quotaBill.set(PurMatQuotaConstant.MATERIAL_FIELD, quota.getString(PurQuotaInfoConstant.MATERIAL_FIELD));
                // 业务日期
                quotaBill.set(PurMatQuotaConstant.BIZDATE_FIELD, plan.getDate(PurDailyPlanConstant.BIZDATE_FIELD));
                // 配额数量
                quotaBill.set(PurMatQuotaConstant.QTY_FIELD, qty);
                // 需求计划ID
                quotaBill.set(PurMatQuotaConstant.ORIID_FIELD, plan.getString("id"));

                quotaBills.add(quotaBill);
            }
        }

        // 判断是否已有分配
        if (allQty.compareTo(BigDecimal.ZERO) > 0) {
            plan.set(PurDailyPlanConstant.ISCAL_FIELD, true);
            plan.set(PurDailyPlanConstant.ALLQTY_FIELD, allQty);
            plan.set(PurDailyPlanConstant.UNALLQTY_FIELD, planQty.subtract(allQty));
            plan.set(PurDailyPlanConstant.ERRMSG_FIELD, "");
        }

        return quotaBills;
    }

    /**
     * 按物料+业务日期查询是否已有分配记录
     * @param plan 日需求计划
     * @return
     * @author: hst
     * @createDate: 2024/06/08
     */
    private static DynamicObject[] queryHistoricalRecords (DynamicObject plan) {
        QFilter qFilter = new QFilter(PurMatQuotaConstant.MATERIAL_FIELD, QFilter.equals,
                plan.getDynamicObject(PurDailyPlanConstant.MATERIAL_FIELD).getPkValue());
        qFilter = qFilter.and(new QFilter(PurMatQuotaConstant.BIZDATE_FIELD, QFilter.equals,
                plan.getDate(PurDailyPlanConstant.BIZDATE_FIELD)));

        return BusinessDataServiceHelper.load(PurMatQuotaConstant.MAIN_ENTITY, PurMatQuotaConstant.getFields(),
                new QFilter[]{qFilter});
    }

    /**
     * 比较配额差，用于给供应商发送短信
     * @param plan 日需求计划
     * @param newBills 新配额
     * @param oldBills 旧配额
     * @return
     * @author: hst
     * @createDate: 2024/06/08
     */
    private static Map<String,String> compareQuotaDiff (DynamicObject plan, List<DynamicObject> newBills, DynamicObject[] oldBills) {
        Map<String,String> messages = new HashMap<>();
        String bizDate = DateUtil.date2str(plan.getDate(PurDailyPlanConstant.BIZDATE_FIELD), "yyyy年MM月dd日");

        // 新配额
        Map<String,BigDecimal> newQuotas = newBills.stream().collect(Collectors.toMap(newBill ->
                        newBill.getString(PurMatQuotaConstant.SUPPLIER_FIELD),
                newBill -> newBill.getBigDecimal(PurMatQuotaConstant.QTY_FIELD)));

        // 旧配额
        Map<String,BigDecimal> oldQuotas = Arrays.asList(oldBills).stream().collect(Collectors.toMap(oldBill ->
                        oldBill.getString(PurMatQuotaConstant.SUPPLIER_FIELD + ".id"),
                oldBill -> oldBill.getBigDecimal(PurMatQuotaConstant.QTY_FIELD)));

        String matNo = plan.getString(PurDailyPlanConstant.MATERIAL_FIELD + ".number");
        String matName = plan.getString(PurDailyPlanConstant.MATERIAL_FIELD + ".name");
        // 已有配额，发生变化
        for (Map.Entry<String,BigDecimal> oldQuota : oldQuotas.entrySet()) {
            String supplierId = oldQuota.getKey();
            BigDecimal oldQty = oldQuota.getValue();

            if (newQuotas.containsKey(supplierId)) {
                BigDecimal newQty = newQuotas.get(supplierId);
                if (oldQty.compareTo(newQty.setScale(2,4)) != 0) {
                    messages.put(supplierId + "&" + matNo, "物料需求配额变更：物料：" + matNo + matName +
                            " 的 " + bizDate + " 计划需求配额发送变化，由" + oldQty.setScale(2, 4) + " 调整为 " + newQty.setScale(2,4));
                }
            } else {
                messages.put(supplierId + "&" + matNo,"物料需求配额变更：物料：" + plan.getString(PurDailyPlanConstant.MATERIAL_FIELD + ".number") +
                        " 的 " + bizDate + " 计划需求配额发送变化，由" + oldQty.setScale(2, 4) + " 调整为 " + 0);
            }
        }

        // 新收到配额
        for (Map.Entry<String,BigDecimal> newQuota : newQuotas.entrySet()) {
            String supplierId = newQuota.getKey();
            BigDecimal newQty = newQuota.getValue();

            if (!oldQuotas.containsKey(supplierId)) {
                messages.put(supplierId,"物料需求配额变更：新收到物料：" + matNo + matName +
                        " 的 " + bizDate + " 计划需求配额，数量为" + newQty.setScale(2, 4));
            }
        }
        return messages;
    }

    /**
     * 向供应商用户发送消息
     * @param messages 短信内容
     * @author: hst
     * @createDate: 2024/06/08
     */
    private static void sendMessageToSupplierUser (Map<String,String> messages) {
        for (Map.Entry<String,String> message : messages.entrySet()) {
            // 获取供应商用户
            DynamicObjectCollection supplierUsers = new BizPartnerBizHelper().getMulBdUserBySupplierId(message.getKey().split("&")[0]);
            if (Objects.nonNull(supplierUsers) && supplierUsers.size() > 0) {
                List<Long> userIds = supplierUsers.stream().map(user -> user.getLong("id")).collect(Collectors.toList());
                // 默认标题标题
                String title = "物料需求计划配额变化";
                String context = message.getValue();
                MessageUtil.sendMessage(userIds, title,context);
            }
        }
    }

    /**
     * 新增/更新需求计划
     * @param dataList
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    public CustomApiResult addNewDailyPlan(JSONArray dataList) {
        List<DynamicObject> addList = new ArrayList<>();
        List<DynamicObject> updateList = new ArrayList<>();
        List<DynamicObject> sameList = new ArrayList<>();
        if (Objects.isNull(dataList)) {
            return CustomApiResult.fail(ApiResultEnum.NONUL.getCode(), ApiResultEnum.NONUL.getValue());
        }

        for (int i = 0; i <= dataList.size() - 1; i++) {
            JSONObject data = dataList.getJSONObject(i);
            // 单据编号
            String matNum = data.getString("matNum");
            String matName = data.getString("matName");
            Date bizDate = DateUtil.formatDate(data.getDate("bizDate"), DateUtil.FORMAT_PARTEN);
            BigDecimal qty = data.getBigDecimal("qty");

            DynamicObject bill = BusinessDataServiceHelper.loadSingle(PurDailyPlanConstant.MAIN_ENTITY,
                    PurDailyPlanConstant.getFields(),
                    new QFilter[]{new QFilter(PurDailyPlanConstant.MATNUM_FIELD,QFilter.equals,matNum)
                            , new QFilter(PurDailyPlanConstant.BIZDATE_FIELD,QFilter.equals,bizDate)});

            if (Objects.nonNull(bill)) {
                BigDecimal oldQty = bill.getBigDecimal(PurDailyPlanConstant.PLANQRY_FIELD);
                if (qty.setScale(2,4).compareTo(oldQty) != 0) {
                    bill.set(PurDailyPlanConstant.PLANQRY_FIELD,qty.setScale(2,4));
                    bill.set(PurDailyPlanConstant.ISCAL_FIELD,false);
                    bill.set(PurDailyPlanConstant.MODIFYTIME_FIELD,new Date());
                    updateList.add(bill);
                } else {
                    sameList.add(bill);
                }
            } else {
                bill = BusinessDataServiceHelper.newDynamicObject(PurDailyPlanConstant.MAIN_ENTITY);
                bill.set(PurDailyPlanConstant.MATNUM_FIELD, matNum);
                bill.set(PurDailyPlanConstant.MATNAME_FIELD, matName);
                bill.set(PurDailyPlanConstant.PLANQRY_FIELD, qty);
                bill.set(PurDailyPlanConstant.BIZDATE_FIELD, bizDate);
                bill.set(PurDailyPlanConstant.ISCAL_FIELD, false);
                bill.set(PurDailyPlanConstant.CREATOR_FIELD, UserServiceHelper.getCurrentUserId());
                bill.set(PurDailyPlanConstant.CREATETIME_FIELD, new Date());
                bill.set(PurDailyPlanConstant.BILLSTATUS_FIELD, "A");
                addList.add(bill);
            }
        }

        // 获取需要删除的数据
        List<String> deleteIds = this.deleteHistoricalData(dataList);

        try (TXHandle h = TX.requiresNew()) {
            try {
                if (updateList.size() > 0) {
                    SaveServiceHelper.save(updateList.toArray(new DynamicObject[updateList.size()]));
                }
                if (addList.size() > 0) {
                    SaveServiceHelper.save(addList.toArray(new DynamicObject[addList.size()]));
                }

                if (deleteIds.size() > 0) {
                    DeleteServiceHelper.delete(PurDailyPlanConstant.MAIN_ENTITY,
                            new QFilter[]{new QFilter("id", QFilter.in, deleteIds)});
                }
            } catch (Exception e) {
                h.markRollback();
                throw e;
            }
        }

        // 获取最早日期
        Date date = getEarlyDate(dataList);
        // 启动调度任务
        try {
            createOnceJob(date,deleteIds);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        // 构建返回参数
        Map<String,Integer> result = new HashMap<>();
        result.put("add", addList.size());
        result.put("update", updateList.size());
        result.put("same", sameList.size());

        return CustomApiResult.success(result);
    }

    /**
     * 创建任务
     * @param date
     * @author: hst
     * @createDate: 2024/06/21
     */
    private void createOnceJob(Date date, List<String> deleteList) {
        if (Objects.nonNull(date)) {
            Map<String, Object> jobParams = new HashMap<>();
            jobParams.put("key", "plan_calculate");
            jobParams.put("date", DateUtil.date2str(date, DateUtil.FORMAT_PARTEN));
            jobParams.put("deleteList", deleteList);

            JobInfo jobInfo = new JobInfo();
            jobInfo.setNumber("yd_pur_matquota_distribute_job");
            jobInfo.setName("APS日需求计划分配");
            jobInfo.setJobType(JobType.REALTIME);
            jobInfo.setId(UUID.randomUUID().toString());
            //设置传给作业类的参数
            jobInfo.setParams(jobParams);
            //指定关联的应用
            jobInfo.setAppId("pur");
            //设置调度作业类
            jobInfo.setTaskClassname(PurDailyPlanTask.class.getName());
            //是否并行执行
            jobInfo.setRunConcurrently(false);

            //创建并执行一次性调度任务，不持久化调度作业，前台查询不到，只能看到调度任务
            JobClient.dispatch(jobInfo);
        }
    }

    /**
     * 删除历史数据（物料 + 日期未包含在APS推送数据中）
     * @author: hst
     * @createDate: 2024/06/21
     */
    private List<String> deleteHistoricalData (JSONArray dataList) {
        List<String> billIds = new ArrayList<>();

        // 候选键集合
        Set<String> dataKeys = dataList.stream().map(data -> ((JSONObject) data).getString("matNum") + "&"
                + DateUtil.date2str(((JSONObject) data).getDate("bizDate"), DateUtil.FORMAT_PARTEN)).collect(Collectors.toSet());

        Set<String> dateLists = dataList.stream().map(data -> DateUtil.date2str(((JSONObject) data).getDate("bizDate"),
                DateUtil.FORMAT_PARTEN)).collect(Collectors.toSet());

        // 查找APS中传过来的日期
        QFilter qFilter = new QFilter(PurDailyPlanConstant.BIZDATE_FIELD, QFilter.in, dateLists);
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),PurDailyPlanConstant.MAIN_ENTITY,
                "id," + PurDailyPlanConstant.getFields(),new QFilter[]{qFilter},null);

        for (Row row : dataSet) {
            String matNum = row.getString(PurDailyPlanConstant.MATNUM_FIELD);
            String bizDate = DateUtil.date2str(row.getDate(PurDailyPlanConstant.BIZDATE_FIELD),DateUtil.FORMAT_PARTEN);

            String key = matNum + "&" + bizDate;

            if (!dataKeys.contains(key)) {
                billIds.add(row.getString("id"));
            }
        }

        return billIds;
    }

    /**
     * 获取最早的日期
     * @param dataList
     */
    private Date getEarlyDate (JSONArray dataList) {
        Date date = null;

        for (Object data : dataList) {
            Date bizDate = ((JSONObject) data).getDate("bizDate");
            if (Objects.nonNull(bizDate) && (date == null || date.compareTo(bizDate) > 0)) {
                date = bizDate;
            }
        }

        return date;
    }

    /**
     * 通过需求计划ID删除配额
     * @param billIds
     * @author: hst
     * @createDate: 2024/06/21
     */
    public static void deletePurMatQuotaById (List<String> billIds) {
        Map<String,String> messages = new HashMap<>();

        QFilter qFilter = new QFilter(PurMatQuotaConstant.ORIID_FIELD, QFilter.in ,billIds);

        DataSet dataSet = QueryServiceHelper.queryDataSet("deletePurMatQuotaById", PurMatQuotaConstant.MAIN_ENTITY,
                PurMatQuotaConstant.getFields() + "," + PurMatQuotaConstant.MATERIAL_FIELD + ".number,"
                        + PurMatQuotaConstant.MATERIAL_FIELD + ".name", new QFilter[]{qFilter}, null);

        for (Row row : dataSet) {
            String supplierId = row.getString(PurMatQuotaConstant.SUPPLIER_FIELD);
            String bizDate = DateUtil.date2str(row.getDate(PurMatQuotaConstant.BIZDATE_FIELD), "yyyy年MM月dd日");
            BigDecimal oldQty = row.getBigDecimal(PurMatQuotaConstant.QTY_FIELD);
            String matNo = row.getString(PurMatQuotaConstant.MATERIAL_FIELD + ".number");
            String matName = row.getString(PurMatQuotaConstant.MATERIAL_FIELD + ".name");

            messages.put(supplierId + "&" + matNo,"物料需求配额变更：物料：" + matNo + matName +
                    " 的 " + bizDate + " 计划需求配额发送变化，由" + oldQty.setScale(2, 4) + " 调整为 " + 0);
        }

        // 向供应商发送短信
//        if (messages.size() > 0) {
//            sendMessageToSupplierUser(messages);
//        }

        // 删除配额
        DeleteServiceHelper.delete(PurMatQuotaConstant.MAIN_ENTITY,
                new QFilter[]{new QFilter(PurMatQuotaConstant.ORIID_FIELD, QFilter.in, billIds)});
    }
}
