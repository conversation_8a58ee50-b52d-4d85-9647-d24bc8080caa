package kd.bos.tcbj.srm.pur.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.IFormView;
import kd.bos.openapi.common.result.CustomApiResult;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.ABillServiceHelper;

import java.text.ParseException;
import java.util.*;

/**
 * @package（包）: kd.bos.tcbj.srm.pur.helper.PurRequestHelper
 * @className（类名称）: PurRequestHelper
 * @description（类描述）: 退货申请单帮助类
 * @author（创建人）: hst
 * @createDate（创建时间）: 2023/05/18
 * @version（版本）: v1.0
 */
public class PurRequestHelper {

    /* 单据标识 */
    private final static String ENTITY_MAIN = "pur_request";

    /**
     * 新增/更新退货申请单
     * @param dataList
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    public CustomApiResult addNewPurRequestBill(JSONArray dataList) {
        ArrayList resultList = new ArrayList<>();
        if (Objects.isNull(dataList)) {
            return CustomApiResult.fail("pur.yd.100001","参数不能为空！");
        }
        for (int i = 0; i <= dataList.size() - 1; i++) {
            JSONObject data = dataList.getJSONObject(i);
            // 单据编号
            String billNo = data.getString("billno");
            DynamicObject bill = QueryServiceHelper.queryOne(ENTITY_MAIN,"id",
                    new QFilter[]{new QFilter("billno",QFilter.equals,billNo)});
            // 获取单据编辑视图（已存在则更新，不存在则新增）
            IFormView saleView = this.createView(bill);
            IDataModel saleModel = saleView.getModel();
            try {
                fieldAssignment(saleModel, data);
                OperationResult result = ABillServiceHelper.saveOperate(saleView);
                Map<String,Object> map = new HashMap<>();
                if (!result.isSuccess()) {
                    map.put("billno",saleModel.getValue("billno"));
                    map.put("message",result.getMessage());
                    map.put("status",false);
                } else {
                    map.put("billno",saleModel.getValue("billno"));
                    map.put("message","");
                    map.put("status",true);
                }
                resultList.add(map);
            } catch (Exception e) {
                Map<String,Object> map = new HashMap<>();
                map.put("billno",saleModel.getValue("billno"));
                map.put("message",e.getMessage());
                map.put("status",false);
                resultList.add(map);
            }
        }
        return CustomApiResult.success(resultList);
    }

    /**
     * 新增/更新退货申请单
     * @param billNos
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    public CustomApiResult deletePurRequest(JSONArray billNos) {
        ArrayList resultList = new ArrayList<>();
        if (Objects.isNull(billNos)) {
            return CustomApiResult.fail("pur.yd.100001","参数不能为空！");
        }
        for (int i = 0; i <= billNos.size() - 1; i++) {
            Object billNo = billNos.get(i);
            try {
                Map<String,Object> map = new HashMap<>();
                int count = DeleteServiceHelper.delete(ENTITY_MAIN,new QFilter[]{
                        new QFilter("billno",QFilter.equals,billNo)});
                if (count == 0) {
                    map.put("billno",billNo.toString());
                    map.put("message","通过单据编号删除失败！");
                    map.put("status",false);
                } else {
                    map.put("billno",billNo.toString());
                    map.put("message","");
                    map.put("status",true);
                }
                resultList.add(map);
            } catch (Exception e) {
                Map<String,Object> map = new HashMap<>();
                map.put("billno",billNo.toString());
                map.put("message",e.getMessage());
                map.put("status",false);
                resultList.add(map);
            }
        }
        return CustomApiResult.success(resultList);
    }

    /**
     * 创建单据视图
     * @param bill
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    private IFormView createView (DynamicObject bill) {
        if (Objects.nonNull(bill)) {
            return this.createView(ENTITY_MAIN,bill.getString("id"));
        } else {
            return this.createView(ENTITY_MAIN);
        }
    }

    /**
     * 创建单据视图
     * @param formId
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    private IFormView createView (String formId) {
        return createView(formId,"");
    }

    /**
     * 创建单据视图
     * @param formId
     * @param billId
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    private IFormView createView (String formId, String billId) {
        IFormView saleView = null;
        if (StringUtils.isNotBlank(billId)) {
            saleView = ABillServiceHelper.createModifyView(formId,billId);
        } else {
            saleView = ABillServiceHelper.createAddView(formId);
        }
        return saleView;
    }

    /**
     * 字段赋值
     * @param model
     * @param data
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    private IDataModel fieldAssignment (IDataModel model, JSONObject data) throws ParseException {
        model.setValue("billno",getItemValue(data,"billno"));
        model.setValue("billstatus","C");
        model.setItemValueByID("creator", UserServiceHelper.getCurrentUserId());
        model.setItemValueByID("modifier", UserServiceHelper.getCurrentUserId());
        model.setItemValueByID("auditor", UserServiceHelper.getCurrentUserId());
        model.setValue("createtime",new Date());
        model.setValue("modifytime",new Date());
        model.setValue("auditdate",new Date());
        model.setItemValueByNumber("org",this.getItemValue(data,"org").toString());
        model.setValue("businesstype","688847172015446016");
        model.setValue("exchrate",this.getItemValue(data,"exchrate"));
        model.setValue("sumamount",this.getItemValue(data,"sumamount"));
        model.setValue("sumtaxamount",this.getItemValue(data,"sumtaxamount"));
        model.setValue("sumtax",this.getItemValue(data,"sumtax"));
        model.getDataEntity().set("taxtype",this.getItemValue(data,"taxtype"));
        model.setValue("curr",this.getBasicObjectByName("bd_currency",
                this.getItemValue(data,"curr").toString()));
        model.setValue("loccurr",this.getBasicObjectByName("bd_currency",
                this.getItemValue(data,"loccurr").toString()));
        model.setValue("billdate", this.getDateValue(data,"billdate"));
        model.setItemValueByNumber("purorg",this.getItemValue(data,"purorg").toString());
        model.setItemValueByNumber("supplier",this.getItemValue(data,"supplier").toString());
        model.setValue("replenishtype",this.getItemValue(data,"replenishtype"));
        model.setValue("rettype",this.getItemValue(data,"rettype"));
        model.setValue("origin","2");
        model.setValue("platform","0");
        JSONArray entries = data.getJSONArray("materialentry");
        // 清空分录
        model.deleteEntryData("materialentry");
        for (int i = 0; i <= entries.size() - 1; i++) {
            JSONObject entry = entries.getJSONObject(i);
            int index = model.createNewEntryRow("materialentry");
            model.setItemValueByNumber("material",this.getItemValue(entry,"material").toString(),index);
            model.setValue("qty",this.getItemValue(entry,"qty"),index);
            model.setValue("price",this.getItemValue(entry,"price"),index);
            model.setValue("taxprice",this.getItemValue(entry,"taxprice"),index);
            model.setValue("amount",this.getItemValue(entry,"amount"),index);
            model.setValue("taxrate",this.getItemValue(entry,"taxrate"),index);
            model.setValue("tax",this.getItemValue(entry,"tax"),index);
            model.setValue("taxamount",this.getItemValue(entry,"taxamount"),index);
            model.setValue("asstqty",this.getItemValue(entry,"asstqty"),index);
            model.setValue("note",this.getItemValue(entry,"note"),index);
            model.setValue("basicqty",this.getItemValue(entry,"basicqty"),index);
            model.setValue("locamount",this.getItemValue(entry,"locamount"),index);
            model.setValue("loctax",this.getItemValue(entry,"loctax"),index);
            model.setValue("loctaxamount",this.getItemValue(entry,"loctaxamount"),index);
            model.setValue("unit",this.getBasicObjectByName("bd_measureunits",
                    this.getItemValue(entry,"unit").toString()),index);
            model.setValue("basicunit",this.getBasicObjectByName("bd_measureunits",
                    this.getItemValue(entry,"basicunit").toString()),index);
            model.setValue("pobillno",this.getItemValue(entry,"pobillno"),index);
            model.setValue("poentryid",this.getItemValue(entry,"poentryid"),index);
            model.setItemValueByNumber("warehouse",this.getItemValue(entry,"warehouse").toString(),index);
            model.setValue("suplot",this.getItemValue(entry,"suplot"),index);
            model.setValue("retdate",this.getDateValue(entry,"retdate"),index);
            model.setValue("ispresent",this.getBooleanValue(entry,"ispresent"),index);
            model.setValue("retreason",this.getItemValue(entry,"retreason"),index);
            model.setValue("lotnumber",this.getItemValue(entry,"lotnumber"),index);
        }
        return model;
    }

    /**
     * 获取布尔值
     * @param data
     * @param field
     * @return
     */
    private boolean getBooleanValue(JSONObject data, String field){
        Object value = this.getItemValue(data,field);
        if (Objects.nonNull(value)) {
            if ("1".equals(value)) {
                return true;
            } else if ("0".equals(value)) {
                return false;
            } else {
                return Boolean.valueOf(value.toString());
            }
        }
        return false;
    }

    /**
     * 获取日期值
     * @param data
     * @param field
     * @return
     */
    private Date getDateValue(JSONObject data, String field) throws ParseException {
        Object value = this.getItemValue(data,field);
        if (value instanceof Date) {
            return (Date) value;
        } else {
            return DateUtil.parseDate(value.toString());
        }
    }

    /**
     * 通过字段名获取对应存储的值
     * @param data
     * @param field
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    private Object getItemValue(JSONObject data, String field) {
        if (Objects.nonNull(data) && data.containsKey(field)) {
            return data.get(field);
        } else {
            return "";
        }
    }

    /**
     * 通过名称获取基础资料
     * @param entityName
     * @param name
     * @return
     * @author: hst
     * @createDate: 2023/05/19
     */
    private DynamicObject getBasicObjectByName (String entityName, String name) {
        return BusinessDataServiceHelper.loadSingle(entityName,new QFilter[]{
                new QFilter("name",QFilter.equals,name)});
    }

}
