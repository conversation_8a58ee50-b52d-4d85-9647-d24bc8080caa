package kd.bos.tcbj.srm.pur.helper;

import kd.bos.algo.Algo;
import kd.bos.algo.DataSet;
import kd.bos.algo.DataType;
import kd.bos.algo.Field;
import kd.bos.algo.Row;
import kd.bos.algo.RowMeta;
import kd.bos.algo.RowMetaFactory;
import kd.bos.algo.input.CollectionInput;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.utils.ArrayUtils;
import kd.bos.entity.report.DecimalReportColumn;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.report.ReportList;
import kd.bos.report.events.CellStyleRule;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.pur.constants.PurMatQuotaConstant;
import kd.bos.tcbj.srm.pur.constants.PurMaterialAttrConstant;
import kd.bos.tcbj.srm.scp.constants.ScpHandInvConstant;
import kd.bos.tcbj.srm.scp.constants.ScpPurCycleConstant;
import kd.bos.tcbj.srm.utils.RptUtil;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.pur.helper.PurSDBalanceRptHelper
 * @className: PurSDBalanceRptHelper
 * @description: 供应商库存动态高低水位即供需平衡报表业务类
 * @author: hst
 * @createDate: 2024/06/09
 * @version: v1.0
 */
public class PurSDBalanceRptHelper {

    // 物料属性字段
    private static String matAttrField = PurMaterialAttrConstant.MATERIAL_FIELD + " yd_material_attr," + PurMaterialAttrConstant.ACTIVITY_FIELD
            + " yd_activity," + PurMaterialAttrConstant.CLASSIFY_FIELD + " yd_classify," + PurMaterialAttrConstant.LIFECYCLE_FIELD
            + " yd_lifecycle," + PurMaterialAttrConstant.SUBCLASS_FIELD + " yd_subclass";

    // 供应商采购周期单
    private static String purCycleField = ScpPurCycleConstant.SUPPLIER_FIELD + " yd_supplier_cyc," + ScpPurCycleConstant.MATERIAL_FIELD
            + " yd_material_cyc," + ScpPurCycleConstant.DELDAYS_FIELD + " yd_deliverydays," + ScpPurCycleConstant.SAFEDAYS_FIELD
            + " yd_safetydays," + ScpPurCycleConstant.ADVDAYS_FIELD + " yd_advancedays," + ScpPurCycleConstant.CPFRDAYS_FIELD
            + " yd_cpfrdays," + ScpPurCycleConstant.ENERGY_FIELD + " yd_energy" ;

    // 供应商在手库存信息
    private static String scpInvField = ScpHandInvConstant.SUPNO_FIELD + " yd_supno_inv," + ScpHandInvConstant.MATNO_FIELD
            + " yd_matno_inv," + ScpHandInvConstant.TRANSITINV + " yd_transitinv," + ScpHandInvConstant.FERINV_FIELD
            + " yd_fertinv," + ScpHandInvConstant.WIPINV_FIELD + " yd_wipinv, id yd_id_inv";

    // 物料配额
    private static String quotaField = PurMatQuotaConstant.SUPPLIER_FIELD + " yd_supplier," + PurMatQuotaConstant.MATERIAL_FIELD
            + " yd_material," + PurMatQuotaConstant.BIZDATE_FIELD + " yd_bizdate," + PurMatQuotaConstant.QTY_FIELD
            + " yd_qty";

    /**
     * 报表列表动态创建表格字段分组
     * @param reportList
     * @param filters
     * @param filterInfos
     * @return
     * @author: hst
     * @createDate: 2024/06/09
     */
    public static void createGroupColumnList(List<FilterItemInfo> filterInfos, List<QFilter> filters, ReportList reportList) {
        // 获取展示类型
        FilterItemInfo typeItemInfo = filterInfos.stream().filter(filterInfo -> "yd_type".equals(filterInfo.getPropName())).findFirst().get();
        String type = Objects.nonNull(typeItemInfo) ? typeItemInfo.getString() : "1";

        // 判断是按天还是按周展示
        String keyField = "1".equals(type) ?  "CONCAT('yd_day',dayofyear(yd_bizdate)) yd_key"
                : "CONCAT('yd_week_',week(yd_bizdate)) yd_key";
        String nameField = "1".equals(type) ?  "to_char(yd_bizDate,'yyyy-MM-dd') yd_name" : "CONCAT('第',CONCAT(week(yd_bizdate),'周')) yd_name";
        // 物料配额信息
        DataSet dataSet = QueryServiceHelper.queryDataSet("createGroupColumnList", PurMatQuotaConstant.MAIN_ENTITY,
                PurMatQuotaConstant.MATERIAL_FIELD + " yd_material," + PurMatQuotaConstant.SUPPLIER_FIELD
                        + " yd_supplier," + PurMatQuotaConstant.BIZDATE_FIELD + " yd_bizdate," + keyField + "," + nameField,
                filters.toArray(new QFilter[filters.size()]), PurMatQuotaConstant.BIZDATE_FIELD + " asc");

        //显示字段信息获取
        dataSet = dataSet.groupBy(new String[]{"yd_bizDate","yd_key","yd_name"}).finish()
                .select(new String[]{"yd_bizdate", "yd_key", "yd_name"})
                .orderBy(new String[]{"yd_bizdate asc"});

        Set<String> keySet = new HashSet<>();
        for (Row row : dataSet) {
            String key = row.getString("yd_key");
            String name = row.getString("yd_name");
            if (!keySet.contains(key)) {
                if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(name)) {
                    DecimalReportColumn column = RptUtil.createDecimalReportColumn(new String[]{
                            key, name, "decimal"});
                    column.setScale(2);
                    reportList.getColumns().add(column);
                }
                keySet.add(key);
            }
        }
    }

    /**
     * 设置单元格样式规则
     * @param cellStyleRules
     * @author: hst
     * @createDate: 2024/06/09
     */
    public static void setCellStyle(List<CellStyleRule> cellStyleRules) {
        // 低水位
        CellStyleRule lowStatusRule = new CellStyleRule();
        lowStatusRule.setFieldKey("yd_levelstatus");
        lowStatusRule.setForeColor("#FF0000");
        lowStatusRule.setDegree(100);
        lowStatusRule.setCondition("yd_levelstatus = \"低\"");
        cellStyleRules.add(lowStatusRule);

        // 高水位
        CellStyleRule highStatusRule = new CellStyleRule();
        highStatusRule.setFieldKey("yd_levelstatus");
        highStatusRule.setForeColor("#FF9900");
        highStatusRule.setDegree(100);
        highStatusRule.setCondition("yd_levelstatus = \"高\"");
        cellStyleRules.add(highStatusRule);

        // 供需比
        CellStyleRule ratioStatusRule = new CellStyleRule();
        ratioStatusRule.setFieldKey("yd_sdratio");
        ratioStatusRule.setForeColor("#FF0000");
        ratioStatusRule.setDegree(100);
        ratioStatusRule.setCondition("yd_sdratio > 2 or yd_sdratio < 0.5");
        cellStyleRules.add(ratioStatusRule);

        // 物料活跃度
        CellStyleRule activityRule = new CellStyleRule();
        activityRule.setFieldKey("yd_activity");
        activityRule.setForeColor("#FF0000");
        activityRule.setDegree(100);
        activityRule.setCondition("yd_activity = \"呆滞\"");
        cellStyleRules.add(activityRule);
    }

    /**
     * 构建过滤条件
     * @param queryParam
     * @author: hst
     * @createDate: 2024/06/09
     */
    public static void buildQueryFilter (ReportQueryParam queryParam) {
        List<FilterItemInfo> filterInfos = queryParam.getFilter().getFilterItems();

        DynamicObject material = null;
        DynamicObject supplier = null;
        Date beginDate = null;
        Date endDate = null;
        for (FilterItemInfo filterItem : filterInfos) {
            String itemName = filterItem.getPropName();
            if ("yd_materialfitler".equals(itemName)) {
                material = (DynamicObject) filterItem.getValue();
            } else if ("yd_supplierfilter".equals(itemName)) {
                supplier = (DynamicObject) filterItem.getValue();
            } else if ("yd_begindate".equals(itemName)) {
                beginDate = filterItem.getDate();
            } else if ("yd_enddate".equals(itemName)) {
                endDate = filterItem.getDate();
            }
        }

        List<QFilter> filters = queryParam.getCustomFilter();
        filters.clear();
        if (Objects.nonNull(material)) {
            filters.add(new QFilter("yd_material",QFilter.equals,material.getPkValue()));
        }
        if (Objects.nonNull(supplier)) {
            filters.add(new QFilter("yd_supplier",QFilter.equals,supplier.getPkValue()));
        }
        if (Objects.nonNull(beginDate)) {
            filters.add(new QFilter("yd_bizdate",QFilter.large_equals,beginDate));
        }

        // 通过参数控制报表查询天数范围
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting",
                "name", new QFilter("number", QCP.equals, "SRM_S&D_RPT_DATERANGE").toArray());
        if (Objects.nonNull(param)) {
            int days = param.getInt("name");
            if (Objects.nonNull(endDate)) {
                int diff = DateUtil.getDifferDays(beginDate, endDate);
                if (diff > days) {
                    throw new KDBizException("当前查询天数为 " + diff + " 天，已超过查询天数范围 " + days + "，请缩小查询天数！");
                } else {
                    filters.add(new QFilter("yd_bizdate",QFilter.less_equals,endDate));
                }
            } else {
                endDate = DateUtil.addDays(beginDate, days);
                filters.add(new QFilter("yd_bizdate",QFilter.less_equals,endDate));
            }
        } else {
            throw new KDBizException("未设置报表查询天数范围，请联系管理员！");
        }
    }

    /**
     * 获取DataSet的字段名
     * @param dataSet
     * @return
     */
    public static String[] getDataSetFields (DataSet dataSet) {
        Field[] fields = dataSet.getRowMeta().getFields();
        List<String> fieldList = Arrays.stream(fields).map(field -> field.getName()).collect(Collectors.toList());
        return fieldList.toArray(new String[fieldList.size()]);
    }

    /**
     * 关联物料属性
     * @param dataSet
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static DataSet getMateriAttribute (DataSet dataSet) {
        // 查询物料属性
        DataSet attrSet = QueryServiceHelper.queryDataSet("getMateriAttribute",
                PurMaterialAttrConstant.MAIN_ENTITY,matAttrField,
                new QFilter[]{new QFilter("billstatus", QFilter.equals, "C")}, null);

        String[] fields = getDataSetFields(dataSet);

        dataSet = dataSet.leftJoin(attrSet).on("yd_material","yd_material_attr").select(
                ArrayUtils.addAll(fields, new String[]{"yd_activity","yd_classify","yd_lifecycle","yd_subclass"})).finish();

        return dataSet;
    }

    /**
     * 关联采购周期
     * @param dataSet
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static DataSet getPurCycle (DataSet dataSet) {
        // 查询供应商采购周期
        DataSet cycleSet = QueryServiceHelper.queryDataSet("getPurCycle",
                ScpPurCycleConstant.MAIN_ENTITY,purCycleField,
                new QFilter[]{new QFilter(ScpPurCycleConstant.CONSTATUS_FIELD, QFilter.equals, "B")}, null);

        String[] fields = getDataSetFields(dataSet);

        dataSet = dataSet.leftJoin(cycleSet).on("yd_material","yd_material_cyc")
                .on("yd_supplier","yd_supplier_cyc").select(ArrayUtils.addAll(fields,
                        new String[]{"yd_deliverydays","yd_safetydays","yd_advancedays","yd_cpfrdays",
                                "yd_safetydays + yd_advancedays yd_lowleveldays",
                                "yd_safetydays + yd_advancedays + yd_cpfrdays yd_highleveldays","yd_energy"})).finish();

        // 查询供应商在手库存
        DataSet invSet = QueryServiceHelper.queryDataSet("getPurCycle",
                ScpHandInvConstant.MAIN_ENTITY,scpInvField,
                null, null);

        fields = getDataSetFields(dataSet);

        dataSet = dataSet.leftJoin(invSet).on("yd_matNum","yd_matno_inv")
                .on("yd_supNum","yd_supno_inv").select(ArrayUtils.addAll(fields,
                        new String[]{"yd_transitinv","yd_fertinv","yd_wipinv","yd_id_inv"})).finish();

        return dataSet;
    }

    /**
     * 关联高低水位天数
     * @param dataSet
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static DataSet getLowAndHighWaterLevel (DataSet dataSet, String supKey, String matKey) {
        // 查询供应商采购周期
        DataSet cycleSet = QueryServiceHelper.queryDataSet("getLowAndHighWaterLevel",
                ScpPurCycleConstant.MAIN_ENTITY,purCycleField,
                new QFilter[]{new QFilter(ScpPurCycleConstant.CONSTATUS_FIELD, QFilter.equals, "B")}, null);

        String[] fields = getDataSetFields(dataSet);

        dataSet = dataSet.leftJoin(cycleSet).on(matKey,"yd_material_cyc")
                .on(supKey,"yd_supplier_cyc").select(ArrayUtils.addAll(fields,
                        new String[]{"yd_safetydays + yd_advancedays yd_lowleveldays",
                                "yd_safetydays + yd_advancedays + yd_cpfrdays yd_highleveldays"})).finish();

        return dataSet;
    }

    /**
     * 关联汤臣库存
     * @param dataSet
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static DataSet getRawMaterialInventory (DataSet dataSet) {
        // 查询汤臣库存
        DataSet invSet = QueryServiceHelper.queryDataSet("getRawMaterialInventory",
                        "yd_pur_matinventory","yd_matNum yd_matNum_inv,yd_qty yd_invqty", null, null)
                .groupBy(new String[]{"yd_matNum_inv"}).sum("yd_invqty").finish();

        String[] fields = getDataSetFields(dataSet);

        dataSet = dataSet.leftJoin(invSet).on("yd_matNum","yd_matNum_inv").select(ArrayUtils.addAll(fields,
                new String[]{"yd_invqty"})).finish();

        return dataSet;
    }

    /**
     * 关联VMI库存
     * @param dataSet
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static DataSet getVMIInventory (DataSet dataSet) {
        // 查询汤臣库存
        DataSet invSet = QueryServiceHelper.queryDataSet("getVMIInventory",
                        "yd_vmiinventory","yd_matnum yd_matNum_vmi,yd_qty yd_vmiqty", null, null)
                .groupBy(new String[]{"yd_matNum_vmi"}).sum("yd_vmiqty").finish();

        String[] fields = getDataSetFields(dataSet);

        dataSet = dataSet.leftJoin(invSet).on("yd_matNum","yd_matNum_vmi").select(ArrayUtils.addAll(fields,
                new String[]{"yd_vmiqty"})).finish();

        return dataSet;
    }

    /**
     * 获取供应商配额信息
     * @param filterInfos
     * @param dataSet
     * @param filters
     * @return
     */
    public static DataSet getSupplierMaterialQuota (List<FilterItemInfo> filterInfos, DataSet dataSet, List<QFilter> filters) {
        // 获取展示类型
        FilterItemInfo typeItemInfo = filterInfos.stream().filter(filterInfo -> "yd_type".equals(filterInfo.getPropName())).findFirst().get();
        String type = Objects.nonNull(typeItemInfo) ? typeItemInfo.getString() : "1";

        // 判断是按天还是按周展示
        String keyField = "1".equals(type) ?  "CONCAT('yd_day',dayofyear(yd_bizdate)) yd_key"
                : "CONCAT('yd_week_',week(yd_bizdate)) yd_key";
        // 获取需显示的日期
        DataSet dateSet = QueryServiceHelper.queryDataSet("getSupplierMaterialQuota", PurMatQuotaConstant.MAIN_ENTITY,
                PurMatQuotaConstant.MATERIAL_FIELD + " yd_material," + PurMatQuotaConstant.SUPPLIER_FIELD
                        + " yd_supplier," + PurMatQuotaConstant.BIZDATE_FIELD + " yd_bizdate," + keyField,
                filters.toArray(new QFilter[filters.size()]), PurMatQuotaConstant.BIZDATE_FIELD + " asc");

        // 判断是按天还是按周展示
        dateSet = dateSet.groupBy(new String[]{"yd_bizDate","yd_key"}).finish()
                .select(new String[]{"yd_bizDate","yd_key"}).orderBy(new String[]{"yd_bizDate asc"});

        List<String> dateFields = new ArrayList<>();
        List<DataType> dataTypes = new ArrayList<>();
        Set<String> keySet = new HashSet<>();
        for (Row row : dateSet) {
            String key = row.getString("yd_key");
            if (!keySet.contains(key)) {
                dateFields.add(key);
                dataTypes.add(DataType.BigDecimalType);
                keySet.add(key);
            }
        }
        dateFields.add("yd_lowlevelnum");
        dateFields.add("yd_highlevelnum");
        dataTypes.add(DataType.BigDecimalType);
        dataTypes.add(DataType.BigDecimalType);

        // 判断是按天还是按周展示
        keyField = "1".equals(type) ?  "CONCAT('yd_day',dayofyear(yd_bizdate)) yd_key"
                : "CONCAT('yd_week_',week(yd_bizdate)) yd_key";
        // 获取配额信息
        DataSet quotaSet = QueryServiceHelper.queryDataSet("getSupplierMaterialQuota", PurMatQuotaConstant.MAIN_ENTITY,
                quotaField + "," + keyField, filters.toArray(new QFilter[filters.size()]), null);

        quotaSet = quotaSet.select(new String[]{"yd_supplier yd_supplier_quo","yd_material yd_material_quo",
                        "yd_bizdate yd_bizdate_quo","yd_qty yd_qty_quo", "yd_key"})
                .orderBy(new String[]{"yd_supplier_quo asc","yd_material_quo asc","yd_bizdate_quo asc"});

        // 获取高低水位信息
        quotaSet = getLowAndHighWaterLevel(quotaSet,"yd_supplier_quo","yd_material_quo");

        // 将行数据转成列
        String[] groupFields = ArrayUtils.addAll(new String[]{"yd_supplier_quo","yd_material_quo"},
                dateFields.toArray(new String[dateFields.size()]));
        DataType[] groupTypes = ArrayUtils.addAll(new DataType[]{DataType.LongType,DataType.LongType},
                dataTypes.toArray(new DataType[dataTypes.size()]));
        DataSet resultSet = columnDataToRowData(quotaSet,"yd_key","yd_qty_quo",
                groupFields, groupTypes, new String[]{"yd_supplier_quo", "yd_material_quo"});

        String[] fields = getDataSetFields(dataSet);

        dataSet = dataSet.leftJoin(resultSet).on("yd_supplier","yd_supplier_quo")
                .on("yd_material","yd_material_quo")
                .select(ArrayUtils.addAll(fields,dateFields.toArray(new String[dateFields.size()])))
                .finish();

        return dataSet;
    }

    /**
     * 将统计的列数据转换为行数据
     * @author: hst
     * @createDate: 2024/06/08
     * @param groupDataSet 需要进行行转列的数据
     * @param nameFile 存储转换后列名的字段名
     * @param valueFile 存储转换后值的字段名
     * @param filed 转换后返回的dataset 的字段数组
     * @param DATATYPES
     * @param groupFiles 转换过程中进行分组的字段
     * @return
     */
    public static DataSet columnDataToRowData (DataSet groupDataSet, String nameFile, String valueFile, String[] filed,
                                               DataType[] DATATYPES, String... groupFiles) {
        Collection<Object[]> coll = new ArrayList<>();
        //创建显示行字段
        RowMeta createRowMeta = RowMetaFactory.createRowMeta(filed, DATATYPES);
        CollectionInput collectionInput = new CollectionInput(createRowMeta, coll);
        DataSet createDataSet = Algo.create("RptUtil").createDataSet(collectionInput);
        List<String> fieldList = Arrays.asList(filed);

        //初始创建一个空的报表数据行
        Map<String,Object> compares = new HashMap<>();
        if (groupDataSet.hasNext()) {
            for (Row row : groupDataSet.copy()) {
                for (String file : groupFiles) {
                    compares.put(file, row.getString(file));
                }
                break;
            }
            Object[] tempData = new Object[filed.length];
            coll.add(tempData);
            int day = 1;
            // Row游标消费完一个结果集之后，不能再消费该结果集
            for (Row row : groupDataSet.copy()) {
                boolean isSame = true;
                for (Map.Entry<String, Object> compare : compares.entrySet()) {
                    if (!StringUtils.equals(compare.getValue().toString(), row.getString(compare.getKey()))) {
                        isSame = false;
                        break;
                    }
                }
                if (!isSame) {
                    day = 1;
                    tempData = new Object[filed.length];
                    coll.add(tempData);
                    compares.clear();
                    for (String file : groupFiles) {
                        compares.put(file, row.getString(file));
                    }
                }
                for (int i = 0; i < groupFiles.length; i++) {
                    tempData[i] = row.get(groupFiles[i]);
                }
                String file = row.getString(nameFile);
                int index = Arrays.asList(filed).indexOf(file);
                if (index != -1) {
                    // 低水位天数
                    int lowLevel = Objects.nonNull(row.get("yd_lowleveldays")) ?
                            row.getInteger("yd_lowleveldays") : -1;
                    // 高水位天数
                    int highLevel = Objects.nonNull(row.get("yd_highleveldays")) ?
                            row.getInteger("yd_highleveldays") : -1;;
                    if (day <= lowLevel || day <= highLevel) {
                        int lowIndex = fieldList.indexOf("yd_lowlevelnum");
                        int highIndex = fieldList.indexOf("yd_highlevelnum");
                        if (day <= lowLevel && lowIndex > -1) {
                            if (Objects.nonNull(tempData[lowIndex])) {
                                tempData[lowIndex] = ((BigDecimal) tempData[lowIndex]).add(row.getBigDecimal(valueFile));
                            } else {
                                tempData[lowIndex] = row.getBigDecimal(valueFile);
                            }
                        }
                        if (day <= highLevel && highIndex > -1) {
                            if (Objects.nonNull(tempData[highIndex])) {
                                tempData[highIndex] = ((BigDecimal) tempData[highIndex]).add(row.getBigDecimal(valueFile));
                            } else {
                                tempData[highIndex] = row.getBigDecimal(valueFile);
                            }
                        }

                        day = day + 1;
                    }

                    if (Objects.nonNull(tempData[index])) {
                        tempData[index] = ((BigDecimal) tempData[index]).add(row.getBigDecimal(valueFile));
                    } else {
                        tempData[index] = row.get(valueFile);
                    }
                }
            }
        }
        return createDataSet;
    }

    /**
     * 数据分析，计算活跃度、供需比等
     * @param dataSet
     * @author: hst
     * @createDate: 2024/06/09
     */
    public static DataSet dataAnalysis(DataSet dataSet) {
        String[] fields = getDataSetFields(dataSet);

        dataSet = dataSet.select(ArrayUtils.addAll(fields,
                new String[]{"(case when yd_fertinv > 0 and yd_lowlevelnum > 0 and yd_highlevelnum > 0 then "
                        + "(case when yd_fertinv < yd_lowlevelnum then '低' else "
                        + "(case when yd_fertinv > yd_highlevelnum then '高' else 'OK' end) end) end) as yd_levelstatus"
                        , "(case when yd_fertinv > 0 then yd_fertinv / yd_lowlevelnum else 9999 end) as yd_sdratio"
                        , "yd_fertinv - yd_lowlevelnum as yd_sdgap"}));

        return dataSet;
    }
}
