package kd.bos.tcbj.srm.pur.helper;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.AppInfo;
import kd.bos.entity.AppMetadataCache;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.permission.PermissionServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description（类描述）: 人员数据查询
 * @author（创建人）: lzp
 * @createDate（创建时间）: 2025/3/12
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class UserServerHelper {

    /**
     * @description（方法描述）: 查看审批人审批的单据过滤条件
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/3/12
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    public static QFilter getAuditorDataFilter() {
        return getAuditorDataFilter(UserServiceHelper.getCurrentUserId());
    }

    /**
     * @description（方法描述）: 查看审批人审批的单据过滤条件
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/3/12
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     * @param userId 人员ID
     */
    public static QFilter getAuditorDataFilter(Long userId) {
        // 按单据ID过滤
        return new QFilter("id", QCP.in, getAuditorDataBillIdSet(userId));
    }

    /**
     * @description（方法描述）: 查看审批人审批的单据过滤条件
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/3/12
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     * @param userId 人员ID
     */
    public static Set<Long> getAuditorDataBillIdSet(Long userId) {

        DynamicObjectCollection col = QueryServiceHelper.query("wf_hicomment", "businesskey", new QFilter("userid", QCP.equals, userId).toArray());
        return col.stream().map(item -> Long.parseLong(item.getString("businesskey"))).collect(Collectors.toSet());
    }

    /**
     * 根据单据标识获取应用id
     * @param billType 单据标识
     * @return 应用id
     */
    public static String getAppId(String billType) {
        MainEntityType mainEntityType = EntityMetadataCache.getDataEntityType(billType);
        AppInfo appInfo = AppMetadataCache.getAppInfo(mainEntityType.getAppId());
        return appInfo != null ? appInfo.getId() : null;
    }

    /**
     * 判断指定用户在特定条件下是否有权（指定应用指定表单的指定权限项，不考虑组织的情况）
     *
     * @param userId         当前用户id
     * @param appId          是应用id
     * @param entityNum      实体的标识
     * @param permItemNumber 权限项编码
     * @return 返回值是一个Boolean，如果为true，表明验证有权，否则无权。
     */
    public static Boolean getHasSpecificPerm(Long userId, String appId, String entityNum, String permItemNumber) {
        String permItemId = getPermItemId(permItemNumber);
        if (StringUtils.isNotEmpty(appId) && StringUtils.isNotEmpty(permItemId)) {
            //方式1，推荐使用
            return PermissionServiceHelper.checkPermission(userId, appId, entityNum, permItemId);
        }
        return false;
    }

    /**
     * 根据权限项编码获取权限项ID
     *
     * @param permItemNum 权限项编码
     * @return 权限项Id
     */
    public static String getPermItemId(String permItemNum) {
        String permItemId = "";
        DynamicObject permPermitem = BusinessDataServiceHelper.loadSingle("perm_permitem", "id,number",
                new QFilter("number", QCP.equals, permItemNum).toArray());
        if (permPermitem != null) {
            permItemId = permPermitem.getString("id");
        }
        return permItemId;
    }
}
