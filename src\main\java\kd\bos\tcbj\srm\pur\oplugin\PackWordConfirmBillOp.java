package kd.bos.tcbj.srm.pur.oplugin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * 包材稿件确认单操作类插件
 * 1、汇总分录文件发放状态到表头字段；
 * 2、反写上游采购订单；
 * 3、包材战略采购部审核后将分录文件发放状态将2更新为3；
 * @auditor yanzuliang
 * @date 2022年11月20日
 * 
 */
public class PackWordConfirmBillOp extends AbstractOperationServicePlugIn {
	
	@Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> filds = e.getFieldKeys();
        filds.add("id");
        filds.add("billno");
        filds.add("org");
        filds.add("yd_filestatesum");
        filds.add("yd_pobillid");
        filds.add("entryentity.id");
        filds.add("entryentity.yd_fileoutstate");
        filds.add("entryentity.yd_poenid");
        filds.add("entryentity.yd_signstate");
	}
	
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		
		// 将分录的文件发放状态汇总到表头，使得工作流可以获取并判断走向
		if ("sumFileState".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				StringBuffer stateSum = new StringBuffer();
				DynamicObjectCollection enCol = bill.getDynamicObjectCollection("entryentity");
				for (DynamicObject enObj : enCol) {
					stateSum.append(enObj.getString("yd_fileoutstate")+"&");
				}
				bill.set("yd_filestatesum", stateSum.toString());
				SaveServiceHelper.save(new DynamicObject[] {bill});
			}
		}
		
		// 审批通过后，反写上游采购订单分录的签稿状态
		if ("updatePoBill".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				String poBillId = bill.getString("yd_pobillid");
				if (QueryServiceHelper.exists("pur_order", poBillId)) {
					DynamicObject poBillObj = BusinessDataServiceHelper.loadSingle(poBillId, "pur_order");
					DynamicObjectCollection poEnCol = poBillObj.getDynamicObjectCollection("materialentry");
					
					DynamicObjectCollection enCol = bill.getDynamicObjectCollection("entryentity");
					Map<String,Object> poEnStateMap = new HashMap<String,Object>();
					for (DynamicObject enObj : enCol) {
						poEnStateMap.put(enObj.getString("yd_poenid"), enObj.get("yd_signstate"));
					}
					
					for (DynamicObject poEnObj : poEnCol) {
						poEnObj.set("yd_signstate", poEnStateMap.get(poEnObj.getString("id")));
					}
					
					SaveServiceHelper.save(new DynamicObject[] {poBillObj});
				}
			}
		}
		
		// 如果分录存在文件发放状态为2的就更新为3
		if ("updateFileOutState".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				DynamicObjectCollection enCol = bill.getDynamicObjectCollection("entryentity");
				for (DynamicObject enObj : enCol) {
					if ("2".equalsIgnoreCase(enObj.getString("yd_fileoutstate"))) {
						enObj.set("yd_fileoutstate", "3");
					}
				}
				SaveServiceHelper.save(new DynamicObject[] {bill});
			}
		}
	}
}
