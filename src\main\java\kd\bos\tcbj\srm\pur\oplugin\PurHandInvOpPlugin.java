package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;

import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurHandInvOpPlugin
 * @className PurHandInvOpPlugin
 * @author: hst
 * @createDate: 2024/07/31
 * @description: 供应商在手库存保存插件
 * @version: v1.0
 */
public class PurHandInvOpPlugin extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("yd_matno");
        e.getFieldKeys().add("yd_material");
        e.getFieldKeys().add("yd_isnotexist");
    }

    /** 保存操作 **/
    private final static String SAVE_OP = "save";

    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        super.beginOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case SAVE_OP : {
                /** 获取物料信息 **/
                this.getMaterialInfo(bills);
                break;
            }
            default : {}
        }
    }

    /**
     * 获取物料信息
     * @param bills
     */
    private void getMaterialInfo (DynamicObject[] bills) {
        for (DynamicObject bill : bills) {
            DynamicObject material = bill.getDynamicObject("yd_material");

            if (Objects.isNull(material)) {
                String matNo = bill.getString("yd_matno");

                Map<Object, DynamicObject> result = BusinessDataServiceHelper.loadFromCache("bd_material",
                        "id", new QFilter[]{new QFilter("number", QFilter.like, "%" + matNo)});

                if (result.isEmpty()) {
                    result = BusinessDataServiceHelper.loadFromCache("bd_material",
                            "id", new QFilter[]{new QFilter("number", QFilter.like, "%" + matNo)});
                }

                if (!result.isEmpty()) {
                    material = result.values().iterator().next();
                    bill.set("yd_material", material);
                } else {
                    bill.set("yd_isnotexist", true);
                }
            }
        }
    }
}
