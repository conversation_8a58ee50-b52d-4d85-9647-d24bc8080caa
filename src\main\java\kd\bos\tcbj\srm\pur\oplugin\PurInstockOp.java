package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.constants.SumBillTempConstant;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurInstockOp
 * @className PurInstockOp
 * @author: hst
 * @createDate: 2024/05/21
 * @description: 采购入库操作插件
 * @version: v1.0
 */
public class PurInstockOp extends AbstractOperationServicePlugIn {

    // 临时采购关闭
    private final static String PURCLOSE_OP = "yd_closepur";

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/05/21
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("supplier");
        e.getFieldKeys().add("material");
        e.getFieldKeys().add("poentryid");
    }

    /**
     * 操作执行完，事务未提交时触发
     * @param e
     * @author: hst
     * @createDate: 2024/05/21
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        super.endOperationTransaction(e);
        String key = e.getOperationKey();
        DynamicObject[] bills = e.getDataEntities();
        switch (key) {
            case PURCLOSE_OP : {
                this.closeTempAllowPurStand(bills);
                break;
            }
        }
    }

    /**
     * 关闭临时允许采购的供应商目录
     * @param bills
     * @author: hst
     * @createDate: 2024/05/21
     */
    private void closeTempAllowPurStand (DynamicObject[] bills) {
        List<String> entryIds = new ArrayList<>();
        // 获取订单分录ID
        for (DynamicObject bill : bills) {
            DynamicObjectCollection entryInfos = bill.getDynamicObjectCollection("materialentry");
            for (DynamicObject entryInfo : entryInfos) {
                String entryId = entryInfo.getString("poentryid");
                if (StringUtils.isNotBlank(entryId)) {
                    entryIds.add(entryId);
                }
            }
        }

        // 获取订单数据
        Map<String, List<String>> orderMap = new HashMap<>();
        DataSet orders = QueryServiceHelper.queryDataSet(this.getClass().getName(), "scp_order",
                "supplier,materialentry.material,materialentry.yd_producer.name",
                new QFilter[]{new QFilter("materialentry.poentryid", QFilter.in, entryIds)}, null);

        for (Row row : orders) {
            String supplierId = row.getString("supplier");
            String proName = row.getString("materialentry.yd_producer.name");
            String materialId = row.getString("materialentry.material");
            List<String> materials = null;
            if (StringUtils.isNotBlank(supplierId) && StringUtils.isNotBlank(proName)
                    && StringUtils.isNotBlank(materialId)) {
                if (orderMap.containsKey(supplierId + "&" + proName)) {
                    materials = orderMap.get(supplierId + "&" + proName);
                } else {
                    materials = new ArrayList<>();
                }
                materials.add(materialId);
                orderMap.put(supplierId + "&" + proName, materials);
            }
        }

        for (Map.Entry<String, List<String>> order : orderMap.entrySet()) {
            String supplierId = order.getKey().split("&")[0];
            String proName = order.getKey().split("&")[1];
            List<String> materials = order.getValue();

            if (materials.size() > 0) {
                QFilter qFilter = new QFilter(SumBillTempConstant.SUPPLIER_FILED, QFilter.equals, supplierId);
                qFilter = qFilter.and(new QFilter(SumBillTempConstant.PRONAME_FILED, QFilter.equals, proName));
                qFilter = qFilter.and(new QFilter(SumBillTempConstant.MATERIAL_FIELD, QFilter.in, materials));
                qFilter = qFilter.and(new QFilter(SumBillTempConstant.ISCLOSE_FIELD,QFilter.equals,"0"));

                // 设置申请单为关闭状态
                DynamicObject[] purApplys = BusinessDataServiceHelper.load(SumBillTempConstant.MAIN_ENTITY,
                        SumBillTempConstant.getField(), qFilter.toArray());
                Arrays.asList(purApplys).stream().forEach(purApply -> purApply.set("yd_isclose", "1"));

                // 还原合格供应商目录状态
                Map<String, String> statusMap = Arrays.asList(purApplys).stream()
                        .filter(purApply -> "yd_packsupsumbill".equals(purApply.getString(SumBillTempConstant.BILLTYPE_FIELD)))
                        .collect(Collectors.toMap(purApply -> purApply.getString(SumBillTempConstant.BIZNO_FIELD)
                                , purApply -> purApply.getString(SumBillTempConstant.OLDSTATUS_FIELD), (k, v) -> k));

                DynamicObject[] stands = BusinessDataServiceHelper.load("yd_packsupsumbill",
                        "billno,yd_supstatus", new QFilter[]{new QFilter("billno",QFilter.in,statusMap.keySet())});
                for (DynamicObject stand : stands) {
                    if (statusMap.containsKey(stand.getString("billno"))) {
                        stand.set("yd_supstatus", statusMap.get(stand.getString("billno")));
                    }
                }

                SaveServiceHelper.save(stands);
                SaveServiceHelper.save(purApplys);
                // 同步EAS
                OperationServiceHelper.executeOperate("yd_sendeas", "yd_packsupsumbill",
                        stands, OperateOption.create());
            }
        }
    }
}
