package kd.bos.tcbj.srm.pur.oplugin;

import java.util.Date;
import java.util.List;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.scm.common.util.ExceptionUtil;

/**
 * 采购方协同采购订单操作插件类
 * 1.创建包材稿件确认单
 * 2.反审核删除采购订单时需要同步删除包材稿件确认单
 * @auditor yanzuliang
 * @date 2022年11月19日
 * 
 */
public class PurOrderCusOp extends AbstractOperationServicePlugIn {
	
	@Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> filds = e.getFieldKeys();
        filds.add("billno");
        filds.add("org");
        filds.add("supplier");
        filds.add("billdate");
        filds.add("creator");
        filds.add("businesstype");
        filds.add("cfmstatus");
        filds.add("billstatus");
        filds.add("materialentry.id");
        filds.add("materialentry.yd_benewword");
        filds.add("materialentry.material");
        filds.add("materialentry.qty");
	}
	
	@SuppressWarnings("deprecation")
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		
		// 遍历采购订单分录，创建包材稿件确认单
		if ("createPackConBill".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				
				DynamicObject packBillInfo = null;
				DynamicObjectType packEnType = null;
				
				DynamicObjectCollection enCol = bill.getDynamicObjectCollection("materialentry");
				for (DynamicObject enObj : enCol) {
					if (enObj.getBoolean("yd_benewword")) {
						if (packBillInfo == null) {
							packBillInfo = BusinessDataServiceHelper.newDynamicObject("yd_packwordconfbill");
							packBillInfo.set("billno", DateTimeUtils.format(new Date(), "yyyyMMddHHmmssSSS"));
							packBillInfo.set("billstatus", "A");
							packBillInfo.set("org", bill.get("org"));
							packBillInfo.set("yd_pobillno", bill.get("billno"));
							packBillInfo.set("yd_pobillid", bill.get("id"));
							packBillInfo.set("yd_supplier", bill.get("supplier"));
							packBillInfo.set("yd_bizdate", bill.get("billdate"));
							
							packBillInfo.set("creator", bill.get("creator"));
							packBillInfo.set("createtime", new Date());
							packBillInfo.set("modifier", bill.get("creator"));
							packBillInfo.set("modifytime", new Date());
							packEnType = packBillInfo.getDynamicObjectCollection("entryentity").getDynamicObjectType();
						}
						
						if (packEnType != null) {
							DynamicObject newEnObj = new DynamicObject(packEnType);
							newEnObj.set("yd_material", enObj.get("material"));
							newEnObj.set("yd_model", enObj.get("material.modelnum"));
							String matNum = enObj.getString("material.number");
							String[] matNumSplit = matNum.split("-");
							if (matNumSplit.length > 0) {
								newEnObj.set("yd_version", matNumSplit[matNumSplit.length-1]);
							} else {
								newEnObj.set("yd_version", "0");
							}
							newEnObj.set("yd_qty", enObj.get("qty"));
							newEnObj.set("yd_fileoutstate", "3");
							newEnObj.set("yd_poenid", enObj.getPkValue());
							newEnObj.set("yd_signstate", "1");
							packBillInfo.getDynamicObjectCollection("entryentity").add(newEnObj);
						}
					}
				}
				
				if (packBillInfo != null) {
//					SaveServiceHelper.save(new DynamicObject[] {packBillInfo});
					String billName = "yd_packwordconfbill";
					// 保存目标单据
			        OperationResult saveResult = SaveServiceHelper.saveOperate(billName, new DynamicObject[]{packBillInfo}, OperateOption.create());
			        if (!saveResult.isSuccess()) {
			            // 错误摘要
			            String errMessage = "保存"+billName+"失败：" + saveResult.getMessage();
//			          	throw new KDException("保存"+billName+"失败：" + errMessage);
			            System.out.println(errMessage);
			        }

			        // 提交目标单据
			        List<Object> successPkIds = saveResult.getSuccessPkIds();
			        System.out.println("提交单据前前"+successPkIds);
			        for (Object successPkId : successPkIds) {
			        	System.out.println("提交单据前"+successPkId);
			        	String pk = saveResult.getSuccessPkIds().get(0).toString();
			        	QFilter qFilter1 = new QFilter("id", QCP.equals, pk);
			        	Object billNo = BusinessDataServiceHelper.loadSingle(billName, "id,billno", qFilter1.toArray()).get("billno");
			        	DynamicObject billObj = BusinessDataServiceHelper.loadSingle(pk, billName);
			        	String oriUserId = RequestContext.get().getUserId();  // 原用户ID
//			        	DynamicObject newUserObj = BusinessDataServiceHelper.loadSingle("bos_user", "id", new QFilter("username", QCP.equals, "zhongtaijiqiren01").toArray());
//			        	RequestContext.get().setUserId(newUserObj.getString("id"));
			        	OperateOption op = OperateOption.create();
			        	op.setVariableValue("OperateOptionConst.ISHASRIGHT", "true");
			        	OperationResult submitResult = OperationServiceHelper.executeOperate("submit", billName, new DynamicObject[]{billObj}, op);
			        	if (!submitResult.isSuccess()) {
			        		// 错误摘要
			        		String errMessage = billName + billNo + "生成成功，但提交失败，请到"+billName+"手动提交，查看提交失败原因：" + submitResult.getAllErrorOrValidateInfo().toString();
//			        		throw new KDException(billName + billNo + "生成成功，但提交失败，请到"+billName+"手动提交，查看提交失败原因：" + errMessage);
			        		System.out.println(errMessage);
			        	}
//			        	RequestContext.get().setUserId(oriUserId);
			        	System.out.println("提交单据后"+successPkId);
					}
				}
			}
		}
		
		// 根据采购订单删除包材稿件确认单
		if ("deletePackConBill".equalsIgnoreCase(e.getOperationKey())) {
			String billName = "yd_packwordconfbill";
			OperateOption createOp = OperateOption.create();
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				String poBillno = bill.getString("billno");
				QFilter filter = new QFilter("yd_pobillno", QCP.equals, poBillno);
				DynamicObject[] packBills = BusinessDataServiceHelper.load(billName, "id,billno,billstatus", filter.toArray());
				if (packBills.length == 0) {
					return;
				}
				for (int i=0;i<packBills.length;i++) {
					DynamicObject packBill = packBills[i];
					OperationResult opRes = null;
					if ("B".equalsIgnoreCase(packBill.getString("billstatus"))) {
						opRes = OperationServiceHelper.executeOperate("unsubmit",
								billName, new Object[] {packBill.getPkValue()}, createOp);
					}
					if ("C".equalsIgnoreCase(packBill.getString("billstatus"))) {
						opRes = OperationServiceHelper.executeOperate("unaudit",
								billName, new Object[] {packBill.getPkValue()}, createOp);
					}
					if (opRes != null) {
						if (!opRes.isSuccess()) {
							System.out.println(ExceptionUtil.getErrorInfoDetails(opRes.getAllErrorOrValidateInfo()));
						} else {
							opRes = OperationServiceHelper.executeOperate("delete",
									billName, new Object[] {packBill.getPkValue()}, createOp);
							if (!opRes.isSuccess()) {
								System.out.println(ExceptionUtil.getErrorInfoDetails(opRes.getAllErrorOrValidateInfo()));
							}
						}
					}
				}
			}
		}
		
		// 对于VMI类采购订单是关闭状态，同步过来之后需要设置确认状态为E-自动确认
		if ("autoConCfmstatus".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				DynamicObject bus = bill.getDynamicObject("businesstype");
				
				if (bus != null && "D".equalsIgnoreCase(bill.getString("billstatus")) && "130".equalsIgnoreCase(bus.getString("number"))) {
					bill.set("cfmstatus", "E");
				}
				SaveServiceHelper.save(new DynamicObject[] {bill});
			}
		}
		
	}
}
