package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.oabill.helper.QCFeedBackHelper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurOrderOp
 * @className PurOrderOp
 * @author: hst
 * @createDate: 2024/03/14
 * @description: 采购入库校验异常批次
 * @version: v1.0
 */
public class PurOrderOp extends AbstractOperationServicePlugIn {

    private final static String PURCLOSE_OP = "yd_closepur";

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/03/14
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("supplier");
        e.getFieldKeys().add("yd_srmproducer");
        e.getFieldKeys().add("material");
    }

    /**
     * 操作执行完，事务未提交时触发
     * @param e
     * @author: hst
     * @createDate: 2024/03/14
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        super.endOperationTransaction(e);
        String key = e.getOperationKey();
        DynamicObject[] bills = e.getDataEntities();
        switch (key) {
            case PURCLOSE_OP : {
                this.closeTempAllowPurStand(bills);
                break;
            }
        }
    }

    /**
     * 关闭临时允许采购的供应商目录
     * @param bills
     * @author: hst
     * @createDate: 2024/03/14
     */
    private void closeTempAllowPurStand (DynamicObject[] bills) {
        for (DynamicObject bill : bills) {
            DynamicObject supplier = bill.getDynamicObject("supplier");
            DynamicObject producer = bill.getDynamicObject("yd_srmproducer");

            if (Objects.nonNull(supplier) && Objects.nonNull(producer)) {
                DynamicObjectCollection details = bill.getDynamicObjectCollection("materialentry");

                List<String> materialNum = details.stream().filter(detail -> Objects.nonNull(detail.getDynamicObject("material")))
                        .map(detail -> detail.getDynamicObject("material").getString("number"))
                        .collect(Collectors.toList());

                if (materialNum.size() > 0) {
                    QFilter qFilter = new QFilter("yd_supplier",QFilter.equals,supplier.getPkValue());
                    qFilter = qFilter.and(new QFilter("yd_supname",QFilter.equals,producer.getString("name")));
                    qFilter = qFilter.and(new QFilter("yd_material.number",QFilter.in,materialNum));
                    DynamicObject[] stands = BusinessDataServiceHelper.load("yd_packsupsumbill",
                            "billno,yd_supstatus",qFilter.toArray());

                    List<String> standNos = Arrays.asList(stands).stream().filter(stand ->
                                    "临时允许采购".equals(stand.getString("yd_supstatus")))
                            .map(stand -> stand.getString("billno")).collect(Collectors.toList());

                    qFilter = new QFilter("yd_billno",QFilter.in,standNos);
                    qFilter = qFilter.and(new QFilter("yd_isclose",QFilter.equals,"0"));
                    DynamicObject[] purApplys = BusinessDataServiceHelper.load("yd_supsumbill_temp",
                            "yd_billno,yd_supstatus,yd_isclose",qFilter.toArray());

                    Map<String, String> statusMap = Arrays.asList(purApplys).stream().collect(Collectors.toMap(
                            purApply -> purApply.getString("yd_billno"), purApply -> purApply.getString("yd_supstatus"),
                            (k,v) -> k));

                    // 设置申请单为关闭状态
                    Arrays.asList(purApplys).stream().forEach(purApply -> purApply.set("yd_isclose","1"));

                    for (DynamicObject stand : stands) {
                        if (statusMap.containsKey(stand.getString("billno"))) {
                            stand.set("yd_supstatus", statusMap.get(stand.getString("billno")));
                        }
                    }

                    SaveServiceHelper.save(stands);
                    SaveServiceHelper.save(purApplys);
                    // 同步EAS
                    OperationServiceHelper.executeOperate("yd_sendeas","yd_packsupsumbill",
                            stands, OperateOption.create());
                }
            }
        }
    }
}
