package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @description（类描述）: 采购价目表操作插件
 * @author（创建人）: oyzw
 * @createDate（创建时间）: 2025/1/21
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class PurPriceOp extends AbstractOperationServicePlugIn {
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        e.getFieldKeys().add("yd_materialtype");
        e.getFieldKeys().add("yd_material.number");
        e.getFieldKeys().add("material");
    }

    @Override
    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
        String key = e.getOperationKey();
        DynamicObject[] dataEntities = e.getDataEntities();
        //分录物料-->版本
        Map<String,Long> materialMap = new HashMap<>();
        if(StringUtils.equalsAny(key,"save","submit")){
            for (DynamicObject data : dataEntities) {
                setStatusMaterial(data);
                String materialType = data.getString("yd_materialtype");
                DynamicObjectCollection entryColl = data.getDynamicObjectCollection("priceentryentity");
                for (int i = 0; i < entryColl.size(); i++) {
                    DynamicObject row = entryColl.get(i);
                    String materialNum = row.getString("yd_material.number");
                    if(!checkMaterialNum(materialNum,materialType)){
                        throw new KDBizException("价格明细第"+(i+1)+"行，物料编码与物料类型不匹配");
                    }
                }
                //包材物料校验版本
                if(StringUtils.equals(data.getString("yd_materialtype"),"C")){
                    checkMaterialVersion(dataEntities,materialMap);
                }
            }

        }
    }
    /**
     * @description（方法描述）: 设置标准产品的物料
     * @author（创建人）: oyzw
     * @createDate（创建时间）: 2025/01/16
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    private void setStatusMaterial(DynamicObject info) {
        DynamicObject material = BusinessDataServiceHelper.loadSingle("bd_materialpurchaseinfo", QFilter.of("1=1").toArray());
        DynamicObjectCollection entry = info.getDynamicObjectCollection("priceentryentity");
        for (DynamicObject row : entry) {
            row.set("material",material);
        }

    }

    /**
     * @description（方法描述）: 检查是否有最新版本
     * @author（创建人）: oyzw
     * @createDate（创建时间）: 2025/01/16
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    private void checkMaterialVersion(DynamicObject[] dataEntities,Map<String,Long> materialMap) {
        for (DynamicObject data : dataEntities) {
            DynamicObjectCollection entryColl = data.getDynamicObjectCollection("priceentryentity");
            for (DynamicObject row : entryColl) {
                String materialNum = row.getString("yd_material.number");
                String materialPre = getMaterialPre(materialNum);
                //map只映射最小版本
                if (!materialMap.containsKey(materialPre) || getMatVersion(materialNum) < materialMap.get(materialPre)) {
                    materialMap.put(materialPre, getMatVersion(materialNum));
                }
            }
        }
        QFilter qFilter = QFilter.of("1 = 0");
        for (String matNum : materialMap.keySet()) {
            qFilter.or("number",QCP.like,matNum+"%");
        }
        DynamicObject[] materialArr = BusinessDataServiceHelper.load("bd_material", "number",
                qFilter.toArray());
        for (DynamicObject mat : materialArr) {
            String matNum = mat.getString("number");
            String materialPre = getMaterialPre(matNum);
            if(getMatVersion(matNum)>materialMap.get(materialPre)){
                throw new KDBizException("物料："+materialPre+"不是最新版本，请检查");
            }
        }
    }

    /**
     * @description（方法描述）: 获取不含版本的物料编码
     * @author（创建人）: oyzw
     * @createDate（创建时间）: 2025/01/16
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    private String getMaterialPre(String materialNum) {
        String material = materialNum;
        if(materialNum.contains("-")){
            int index = materialNum.lastIndexOf("-");
            material = materialNum.substring(0,index);
        }
        return material;
    }
    /**
     * @description（方法描述）: 获取物料版本
     * @author（创建人）: oyzw
     * @createDate（创建时间）: 2025/01/16
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    private Long getMatVersion(String materialNum) {
        long version =0;
        if(materialNum.contains("-")){
            int index = materialNum.lastIndexOf("-");
            version = Long.parseLong(materialNum.substring(index+1));
        }
        return version;
    }

    /**
     * @description（方法描述）: 根据物料类型校验物料编码
     * @author（创建人）: oyzw
     * @createDate（创建时间）: 2025/01/16
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     * @param materialNum 物料编码
     * @param materialType 物料类型
     * @return 是否匹配
     */
    private boolean checkMaterialNum(String materialNum, String materialType) {
        if(StringUtils.isBlank(materialNum)){
            return true;
        }
        if("A".equals(materialType) && !materialNum.startsWith("L")){
            return false;
        }else if("B".equals(materialType) && !materialNum.startsWith("C")){
            return false;
        }
        else if("C".equals(materialType)&&
                !(materialNum.startsWith("D")||materialNum.startsWith("E")||materialNum.startsWith("F")||materialNum.startsWith("G"))){
            return false;
        }
        return true;
    }
}
