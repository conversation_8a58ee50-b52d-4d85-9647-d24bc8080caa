package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.pur.constants.PurDailyPlanConstant;
import kd.bos.tcbj.srm.pur.constants.PurQuotaInfoConstant;
import kd.bos.tcbj.srm.pur.helper.PurDailyPlanHelper;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurQuotaInfoOp
 * @className: PurQuotaInfoOp
 * @description: 供应商配额信息操作插件
 * @author: hst
 * @createDate: 2024/06/09
 * @version: v1.0
 */
public class PurQuotaInfoOp extends AbstractOperationServicePlugIn {

    // 核准操作标识
    private final static String APPROVAL_OP = "yd_approval";
    // 反核准操作标识
    private final static String UNAPPROVAL_OP = "yd_unapproval";

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/06/10
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add(PurQuotaInfoConstant.SUPPLIER_FIELD);
        e.getFieldKeys().add(PurQuotaInfoConstant.MATERIAL_FIELD);
    }

    /**
     * 操作执行完，事务已提交
     * @param e
     * @author: hst
     * @createDate: 2024/06/10
     */
    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        super.afterExecuteOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case APPROVAL_OP : {

            }
            case UNAPPROVAL_OP : {
                // 更新日需求计划计算结果
                this.updateDailyPlanAllocation(bills);
                break;
            }
        }
    }

    /**
     * 更新日需求计划计算结果
     * @author: hst
     * @createDate: 2024/06/08
     */
    private void updateDailyPlanAllocation (DynamicObject[] bills) {
        // 涉及的物料
        List<String> matNums = Arrays.stream(bills).filter(bill -> !"0".equals(bill.getString("id"))).map(bill ->
                bill.getString(PurQuotaInfoConstant.MATERIAL_FIELD + ".number")).collect(Collectors.toList());

        if (matNums.size() > 0) {
            // 业务日期大于等于今天的
            QFilter qFilter = new QFilter(PurDailyPlanConstant.MATNUM_FIELD, QFilter.in, matNums);
            qFilter = qFilter.and(new QFilter(PurDailyPlanConstant.BIZDATE_FIELD, QFilter.large_equals,
                    DateUtil.date2str(new Date(),DateUtil.FORMAT_PARTEN)));

            DynamicObjectCollection plans = QueryServiceHelper.query(PurDailyPlanConstant.MAIN_ENTITY,
                    "id", new QFilter[]{qFilter});

            List<Object> planIds = plans.stream().map(plan -> plan.get("id")).collect(Collectors.toList());

            if (planIds.size() > 0) {
                PurDailyPlanHelper.dailyPlanAllocation(planIds);
            }
        }
    }
}
