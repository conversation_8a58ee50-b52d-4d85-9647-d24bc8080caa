package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.scp.constants.ScpSalOutStockConstants;

import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurReceiptErrLotOp
 * @className PurReceiptErrLotOp
 * @author: hst
 * @createDate: 2022/11/17
 * @description: 采购入库校验异常批次
 * @version: v1.0
 */
public class PurReceiptErrLotOp extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        e.getFieldKeys().add("supplier");
        e.getFieldKeys().add("material");
        e.getFieldKeys().add("suplot");
    }

    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        DynamicObject[] bills = e.getDataEntities();
        for (DynamicObject bill : bills) {
            boolean isErrLot = false;
            DynamicObject supplier = bill.getDynamicObject("supplier");     //供应商
            if (Objects.isNull(supplier)) {
                continue;
            }
            //遍历分录，校验生产商+物料编码+厂家批号 是否在异常批次库中
            int index = 1;
            StringBuffer errStr = new StringBuffer();
            for (DynamicObject entry : bill.getDynamicObjectCollection("materialentry")) {
                DynamicObject material = entry.getDynamicObject("material");   //物料
                if (Objects.nonNull(material) && StringUtils.isNotBlank(entry.getString("suplot"))) {
                    // 生产商+物料编码+厂家批号+不允许发货
                    QFilter qFilter = QFilter.of("yd_supplier.number = ? and yd_material.number = ? and yd_supplierno = ? and enable = 1",
                            supplier.getString("number"), material.getString("number"), entry.getString("suplot"));
                    // 校验是否存在
                    boolean isExist = QueryServiceHelper.exists("yd_scp_errlot",new QFilter[]{qFilter});
                    if (isExist) {
                        isErrLot = true;
                        entry.set("yd_errlot",true);
                    }
                }
                index ++;
            }
            if (isErrLot) {
                bill.set("yd_iserrlot",true);       //异常批次
            } else {
                bill.set("yd_iserrlot",false);      //不是异常批次
            }
            SaveServiceHelper.save(new DynamicObject[]{bill});
        }
    }
}
