package kd.bos.tcbj.srm.pur.oplugin;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.util.StringUtils;

/**
 * 采购收货单操作插件类
 * @auditor yanzuliang
 * @date 2022年10月24日
 * 
 */
public class PurReceiptOp extends AbstractOperationServicePlugIn {
	
	@Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> filds = e.getFieldKeys();
        filds.add("billno");
        filds.add("billdate");
        filds.add("materialentry.yd_saloutbillno");
        filds.add("materialentry.yd_saloutid");
        filds.add("materialentry.yd_saloutnum");
        filds.add("materialentry.lotnumber");
        filds.add("materialentry.qty");
        filds.add("materialentry.material.id");
        filds.add("materialentry.material.name");
        filds.add("materialentry.material.number");
	}
	
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		
		if ("updateSaleDeliveryBill".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				DynamicObjectCollection enCol = bill.getDynamicObjectCollection("materialentry");
				Date recDate = bill.getDate("billdate");
				for (DynamicObject enObj : enCol) {
					String saloutbillNo = enObj.getString("yd_saloutbillno");
					String saloutbillId = enObj.getString("yd_saloutid");
					String saloutSeq = enObj.getString("yd_saloutnum");
					String saledeliveryenid = enObj.getString("yd_saledeliveryenid");
					String lot = enObj.getString("lotnumber");
					String matNum = enObj.getString("material.number");
					String matName = enObj.getString("material.name");
					BigDecimal qty = enObj.getBigDecimal("qty");
					
					// 反写销售发货单的【物流状态】为已收货
					if (StringUtils.isNotEmpty(saloutbillId)) {
						QFilter billF = new QFilter("id", QCP.equals, saloutbillId);
						DynamicObject[] saloutStockBills = BusinessDataServiceHelper.load("scp_saloutstock", "id,billno,logstatus", billF.toArray());
						if (saloutStockBills.length > 0) {
							DynamicObject saloutStockBill = BusinessDataServiceHelper.loadSingle(saloutStockBills[0].get("id"), "scp_saloutstock");
							saloutStockBill.set("logstatus", "E");
							
							// 对销售发货单分录的附件进行重命名
							if (StringUtils.isNotEmpty(saledeliveryenid)) {
								DynamicObjectCollection saloutEnCol = saloutStockBill.getDynamicObjectCollection("materialentry");
								for (DynamicObject saloutEnObj : saloutEnCol) {
									saloutEnObj.set("yd_reclot", lot);  // 将收货入库批号反写到销售发货单，用于后续的附件传递接口使用
									if (saledeliveryenid.equalsIgnoreCase(saloutEnObj.getString("id"))) {
										reNameFiles(matNum,lot,matName,"COA",saloutEnObj.getDynamicObjectCollection("yd_coa"));
										reNameFiles(matNum,lot,matName,"CIQ",saloutEnObj.getDynamicObjectCollection("yd_ciqattachment"));
										reNameFiles(matNum,lot,matName,"报关单",saloutEnObj.getDynamicObjectCollection("yd_customsattachment"));
										reNameFiles(matNum,lot,matName,"声明",saloutEnObj.getDynamicObjectCollection("yd_stateattachment"));
									}
								}
							}
							
							SaveServiceHelper.save(new DynamicObject[] {saloutStockBill});
						}
					}
					
					// 反写预约台账记录
					if (StringUtils.isNotEmpty(saloutbillId) && StringUtils.isNotEmpty(saledeliveryenid)) {
						QFilter saleEnIdFilter = new QFilter("yd_materialentry.yd_stockentryid", QCP.equals, saledeliveryenid);
						DynamicObjectCollection bookCol = QueryServiceHelper.query("yd_pur_deliverstand", "id,billno", saleEnIdFilter.toArray());
						for (DynamicObject tmpBookObj : bookCol) {
							DynamicObject bookObj = BusinessDataServiceHelper.loadSingle(tmpBookObj.getString("id"), "yd_pur_deliverstand");
							// 查看日期是否一致
							bookObj.set("yd_actualdate", recDate);
							String recDateStr = DateTimeUtils.format(recDate, "yyyyMMdd");
							String planDateStr = DateTimeUtils.format(bookObj.getDate("yd_arrivaldate"), "yyyyMMdd");
							if (recDateStr.equalsIgnoreCase(planDateStr)) {
								bookObj.set("yd_isaccdate", true);
							}
							
							DynamicObjectCollection bookEnCol = bookObj.getDynamicObjectCollection("yd_materialentry");
							for (DynamicObject bookEnObj : bookEnCol) {
								if (saledeliveryenid.equalsIgnoreCase(bookEnObj.getString("yd_stockentryid"))) {
									BigDecimal recQty = countRecQty(saledeliveryenid, qty);
									bookEnObj.set("yd_actqty", recQty);
									BigDecimal planQty = bookEnObj.getBigDecimal("yd_qty");
									if (recQty.compareTo(planQty) >= 0) {
										bookEnObj.set("yd_isaccqty", true);
									}
								}
							}
							bookObj.set("billstatus", "C");
							SaveServiceHelper.save(new DynamicObject[] {bookObj});
						}
					}
					
				}
			}
		}
	}

	/**
	 * 根据销售发货单分录ID统计所有采购收货单数量
	 * @param saledeliveryenid
	 * @param curRecQty 当前收货单收货数量
	 * @return
	 */
	private BigDecimal countRecQty(String saledeliveryenid, BigDecimal curRecQty) {
		QFilter filter = new QFilter("materialentry.yd_saledeliveryenid", QCP.equals, saledeliveryenid);
		DynamicObjectCollection recCol = QueryServiceHelper.query("pur_receipt", "id,billno,materialentry.qty recQty,materialentry.yd_saledeliveryenid", filter.toArray());
		BigDecimal recQty = BigDecimal.ZERO;
		for (DynamicObject recObj : recCol) {
			recQty = recQty.add(recObj.getBigDecimal("recQty")==null?BigDecimal.ZERO:recObj.getBigDecimal("recQty"));
		}
		if (recQty.compareTo(BigDecimal.ZERO)==0) {
			return curRecQty;
		} else {
			return recQty;
		}
	}

	/**
	 * 对销售发货单的分录附件进行重命名
	 * @param matNum
	 * @param lot
	 * @param matName
	 * @param fileType
	 * @param fileCol
	 */
	private void reNameFiles(String matNum, String lot, String matName, String fileType,
			DynamicObjectCollection fileCol) {
		int seq = 1;
		for (DynamicObject fileObj : fileCol) {
			DynamicObject basedata = fileObj.getDynamicObject("fbasedataid");
			basedata = BusinessDataServiceHelper.loadSingle(basedata.getPkValue(), "bd_attachment");
//			basedata.set("name", fileType+"-"+matNum+"-"+lot+"-"+matName+"-"+basedata.getString("name"));
			basedata.set("name", fileType+"-"+matNum+"-"+lot+"-"+matName+"-"+seq);
			seq++;
//			basedata.set("status", "B");
			SaveServiceHelper.save(new DynamicObject[] {basedata});
//			SaveServiceHelper.update(new DynamicObject[] {basedata});
		}
	}
}
