package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurReturnWriteBackOp
 * @className PurReturnWriteBackOp
 * @author: hst
 * @createDate: 2023/04/25
 * @description: 退货入库反写插件
 * @version: v1.0
 */
public class PurReturnWriteBackOp extends AbstractOperationServicePlugIn {

    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        super.beginOperationTransaction(e);
        Set<String> oriBillNos = new HashSet<>();
        DynamicObject[] bills = e.getDataEntities();
        for (DynamicObject bill : bills) {
            // 获取需要反写的源单
            for (DynamicObject entry : bill.getDynamicObjectCollection("materialentry")) {
                String oriBillNo = entry.getString("yd_srcbillnum");
                if (StringUtils.isNotBlank(oriBillNo)) {
                    oriBillNos.add(oriBillNo);
                }
            }
        }
        // 修改源单状态为已完成
        DynamicObject[] oriBIlls = BusinessDataServiceHelper.load("pur_request","" +
                "cfmstatus",new QFilter[]{new QFilter("billno",QFilter.in,oriBillNos)});
        for (DynamicObject oriBill : oriBIlls) {
            oriBill.set("cfmstatus","F");
        }
        SaveServiceHelper.update(oriBIlls);
    }
}
