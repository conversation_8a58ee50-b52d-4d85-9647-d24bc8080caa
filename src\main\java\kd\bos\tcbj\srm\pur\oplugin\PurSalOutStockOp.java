package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * 采购端销售发货单插件
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurSalOutStockOp
 * @className PurSalOutStockOp
 * @author: hst
 * @createDate: 2023/11/29
 * @version: v1.0
 */
public class PurSalOutStockOp extends AbstractOperationServicePlugIn {

    /** 加急操作 */
    private final static String URGENT_OP = "yd_urgent";

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2023/11/29
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("billno");
        e.getFieldKeys().add("yd_isurgent");
    }

    /**
     * 操作执行完，事务未提交
     * @param e
     * @author: hst
     * @createDate: 2023/11/29
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        super.endOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case URGENT_OP : {
                this.markUrgentShippingOrder(bills);
                break;
            }
        }
    }

    /**
     * 标记发货单是否为加急，并更新预约送货台账
     * @param bills 销售发货单
     * @author: hst
     * @createDate: 2023/11/29
     */
    private void markUrgentShippingOrder (DynamicObject[] bills) {
        List<String> billNos = new ArrayList<>();
        for (DynamicObject bill : bills) {
            bill.set("yd_isurgent", true);

            String billNo = bill.getString("billno");
            billNos.add(billNo);
        }

        this.updateDeliverStand(billNos);
        SaveServiceHelper.save(bills);
    }

    /**
     * 更新预约送货台账
     * @param billNos 销售发货单号
     * @author: hst
     * @createDate: 2023/11/29
     */
    private void updateDeliverStand (List<String> billNos) {
        DynamicObject[] stocks = BusinessDataServiceHelper.load("yd_pur_deliverstand", "yd_isurgent",
                new QFilter[]{new QFilter("yd_saloutno",QFilter.in,billNos)});
        for (DynamicObject stock :stocks) {
            stock.set("yd_isurgent",true);
        }
        SaveServiceHelper.save(stocks);
    }
}
