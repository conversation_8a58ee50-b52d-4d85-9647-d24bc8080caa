package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.servicehelper.operation.SaveServiceHelper;

import java.util.Date;

public class PurStandAuditOp extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("billstatus");
        e.getFieldKeys().add("yd_warehouse");
        e.getFieldKeys().add("yd_material");
        e.getFieldKeys().add("yd_comfirmtime");
        e.getFieldKeys().add("yd_billstatus");
    }

    /**
     * 事务开始时
     * @param e
     * @author: hst
     * @createDate: 2023/11/07
     */
    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        super.beginOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case "audit" : {
                for (DynamicObject bill : bills) {
                    bill.set("yd_comfirmtime",new Date());
                    bill.set("yd_billstatus","D");
                }
                break;
            }
            case "unaudit" : {
                for (DynamicObject bill : bills) {
                    bill.set("yd_comfirmtime",new Date());
                    bill.set("yd_billstatus","B");
                }
                break;
            }
        }
        SaveServiceHelper.save(bills);
    }
}
