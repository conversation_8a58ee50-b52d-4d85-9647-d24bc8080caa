package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurStandSaveMobileOp
 * @className PurStandSaveMobileOp
 * @author: hst
 * @createDate: 2023/07/13
 * @version: v1.0
 */
public class PurStandSaveMobileOp extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("billstatus");
        e.getFieldKeys().add("yd_warehouse");
        e.getFieldKeys().add("yd_material");
        e.getFieldKeys().add("billno");
        e.getFieldKeys().add("yd_shipper");
        e.getFieldKeys().add("yd_contact");
        e.getFieldKeys().add("yd_orderdate");
        e.getFieldKeys().add("yd_createtime");
        e.getFieldKeys().add("yd_billstatus");
    }

    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        super.afterExecuteOperationTransaction(e);
        DynamicObject[] bills =e.getDataEntities();
        for (DynamicObject bill : bills) {
            sendDeliveryReservation(bill);
            bill.set("yd_orderdate",new Date());
            // update by hst 2023/11/07 将待预约状态修改为待确认状态
            bill.set("yd_billstatus","B");
        }
        SaveServiceHelper.save(bills);
    }

    /**
     * 向仓库管理员发送预约送货信息
     * @param bill
     * @author: hst
     * @createDate: 2023/07/13
     */
    private void sendDeliveryReservation (DynamicObject bill) {
        Map<String,String> temp = new HashMap();
        DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_materialentry");
        // update by hst 2023/11/06 修改短信格式
        DynamicObject supply = bill.getDynamicObject("yd_supplier");
        if (Objects.nonNull(supply)) {
            for (DynamicObject entry : entries) {
                DynamicObject warehouse = entry.getDynamicObject("yd_warehouse");
                DynamicObject material = entry.getDynamicObject("yd_material");
                if (Objects.nonNull(material)) {
                    String context = material.getString("number") + " " + material.getString("name")
                            + " " + entry.getBigDecimal("yd_qty").setScale(2) + " " + (Objects.nonNull(entry.getDynamicObject("yd_unit"))
                            ? entry.getDynamicObject("yd_unit").getString("name") : "");
                    if (Objects.isNull(warehouse)) {
                        if (temp.containsKey("regular")) {
                            temp.put("regular", temp.get("regular") + "、" + context);
                        } else {
                            temp.put("regular", context);
                        }
                    } else if (Objects.nonNull(warehouse)) {
                        if (temp.containsKey(warehouse.getString("id"))) {
                            temp.put(warehouse.getString("id"), temp.get(warehouse.getString("id")) +
                                    "、" + context);
                        } else {
                            temp.put(warehouse.getString("id"), context);
                        }
                    }
                }
            }
            if (temp.size() > 0) {
                Map<String, String> infos = new HashMap<>();
                // 先获取仓库基础资料
                List<String> ids = temp.keySet().stream().collect(Collectors.toList());
                QFilter filter = new QFilter("id", QFilter.in, ids);
                DynamicObject[] warehouses = BusinessDataServiceHelper.load("yd_warehouse",
                        "name,yd_address,yd_management,yd_phone,yd_management_res,yd_contact", new QFilter[]{filter});
                String billNo = bill.getString("billno");
                // update by hst 2023/11/06 修改短信格式
                String context = "预约送货台账" + billNo + "由司机" + bill.getString("yd_shipper") + "，电话为"
                        + bill.getString("yd_contact") + "，预约送货时间"
                        + DateUtil.date2str(bill.getDate("yd_arrivaldate"), "yyyy年MM月dd日HH时mm分") +
                        "。   物料供应商为：" + supply.getString("name") + "。   物料明细为：";
                for (DynamicObject store : warehouses) {
                    String matNums = temp.get(store.getString("id"));
                    String phone = StringUtils.isNotBlank(store.getString("yd_phone"))
                            ? store.getString("yd_phone") : store.getString("yd_contact");
                    if (StringUtils.isNotBlank(phone)) {
                        infos.put(store.getString("yd_phone"), context + matNums + "。   请及时前往SRM系统确认预约。");
                    }
                }
                if (temp.containsKey("regular")) {
                    DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name",
                            new QFilter[]{new QFilter("number", QFilter.equals, "SCM_DELIVER_RESEND_PHONE")});
                    if (Objects.nonNull(param)) {
                        String matNums = temp.get("regular");
                        infos.put(param.getString("name"), context + matNums + "。   请及时前往SRM系统确认预约。");
                    }
                }
                this.sendMessages(infos);
            }
        }
    }

    /**
     * 发送短信
     * @param infos
     * @author: hst
     * @createDate: 2023/07/13
     */
    private void sendMessages (Map<String,String> infos) {
        for (Map.Entry<String,String> info : infos.entrySet()) {
            ShortMessageInfo shortMessageInfo = new ShortMessageInfo();
            String driverPhone = info.getKey();
            shortMessageInfo.setCountryCode(CountryCode.CN);
            shortMessageInfo.setMessage(info.getValue());
            shortMessageInfo.setPhone(Arrays.asList(driverPhone));
            Map<String, Object> result =  MessageHandler.sendShortMessage(shortMessageInfo);
        }
    }
}
