package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class PurStandSubmitOp extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("billstatus");
        e.getFieldKeys().add("yd_warehouse");
        e.getFieldKeys().add("yd_material");
        e.getFieldKeys().add("yd_comfirmtime");
        e.getFieldKeys().add("yd_billstatus");
        e.getFieldKeys().add("yd_qty");
        e.getFieldKeys().add("yd_unit");
    }

    /**
     * 事务开始时
     * @param e
     * @author: hst
     * @createDate: 2023/11/07
     */
    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        super.beginOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case "submit" : {
                for (DynamicObject bill : bills) {
                    bill.set("yd_comfirmtime",new Date());
                    bill.set("yd_billstatus","C");
                }
                break;
            }
            case "unsubmit" : {
                for (DynamicObject bill : bills) {
                    bill.set("yd_comfirmtime",new Date());
                    bill.set("yd_billstatus","B");
                }
                break;
            }
        }
        SaveServiceHelper.save(bills);
    }

    /**
     * 确认预约后向司机发送短信，若发送失败不回滚（若多条中一条失败会整批回滚）
     * @author; hst
     * @createDate: 2022/11/23
     * @param e
     */
    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        DynamicObject[] bills = e.getDataEntities();
        List<Object> ids = Arrays.stream(bills).map(DynamicObject::getPkValue).collect(Collectors.toList());
        bills = BusinessDataServiceHelper.load(ids.toArray(),BusinessDataServiceHelper.newDynamicObject("yd_pur_deliverstand").getDynamicObjectType());
//        StringBuffer errStr = new StringBuffer();
//        Arrays.stream(bills).forEach(bill -> {
//            if ("B".equals(bill.getString("billstatus"))) {
//                // 向司机发送短信
//                ShortMessageInfo shortMessageInfo = new ShortMessageInfo();
//                List<String> phone = new ArrayList<>();
//                phone.add(bill.getString("yd_contact"));
//                shortMessageInfo.setPhone(phone);
//                shortMessageInfo.setCountryCode(CountryCode.CN);
//                String content = "销售发货单："+ bill.getString("yd_saloutno") +"预约送货时间已由仓库人员确认，时间为" +
//                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(bill.getDate("yd_arrivaldate")) +
//                        ",如需修改时间请联系仓库管理员，联系方式：" + bill.getString("yd_silotubecontact");
//                shortMessageInfo.setMessage(content);
//                Map<String, Object> result =  MessageHandler.sendShortMessage(shortMessageInfo);
//                if (!(boolean)result.get("result")) {
//                    errStr.append("单据：" + bill.getString("billno") + "发送短信失败，原因：" + result.get("description").toString() + ";");
//                }
//            }
//        });
        try {
            this.writeBackMaterialWareHouse(bills);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        StringBuilder errorMsg = this.sendReservationConfirmation(bills);
        if (errorMsg.length() > 0) {
            throw new KDBizException(errorMsg.deleteCharAt(errorMsg.length() - 1).toString());
        }
    }

    /**
     * 确认预约后发送短信
     * @param bills
     * @author: hst
     * @createDate: 2023/06/29
     */
    private StringBuilder sendReservationConfirmation(DynamicObject[] bills) {
        StringBuilder errorMsg = new StringBuilder();
        // 根据仓库进行分类
        Arrays.stream(bills).forEach(bill -> {
            if ("B".equals(bill.getString("billstatus"))) {
                Map<String,String> temp = new HashMap();
                DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_materialentry");
                for (DynamicObject entry : entries) {
                    DynamicObject warehouse = entry.getDynamicObject("yd_warehouse");
                    DynamicObject material = entry.getDynamicObject("yd_material");
                    if (Objects.nonNull(material)) {
                        String context = material.getString("number") + " " + material.getString("name")
                                + " " + entry.getBigDecimal("yd_qty").setScale(2) + " " + (Objects.nonNull(entry.getDynamicObject("yd_unit"))
                                ? entry.getDynamicObject("yd_unit").getString("name") : "");
                        if (temp.containsKey(warehouse.getString("id"))) {
                            temp.put(warehouse.getString("id"), temp.get(warehouse.getString("id")) +
                                    "、" + context);
                        } else {
                            temp.put(warehouse.getString("id"), context);
                        }
                    }
                }
                if (temp.size() > 0) {
                    // 先获取仓库基础资料
                    List<String> ids = temp.keySet().stream().collect(Collectors.toList());
                    QFilter filter = new QFilter("id",QFilter.in,ids);
                    DynamicObject[] warehouses = BusinessDataServiceHelper.load("yd_warehouse",
                            "name,yd_address,yd_management,yd_phone",new QFilter[]{filter});
                    StringBuffer materialMsg = new StringBuffer();
                    for (DynamicObject warehouse : warehouses) {
                        String address = warehouse.getString("yd_address");
                        DynamicObject management = warehouse.getDynamicObject("yd_management");
                        String phone = warehouse.getString("yd_phone");
                        String materials = temp.get(warehouse.getString("id"));
                        materialMsg.append("物料" + materials + "请配送至[" + address + "]，联系人：" + management.getString("name") +
                                "，联系电话：" + phone + "；");
                    }
                    String content = "销售发货单："+ bill.getString("yd_saloutno") +"预约送货时间已由仓库人员确认，收货时间为" +
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(bill.getDate("yd_arrivaldate")) +
                            "，请及时到达。   物料明细为：" + materialMsg;
                    ShortMessageInfo shortMessageInfo = new ShortMessageInfo();
                    String driverPhone = bill.getString("yd_contact");
                    shortMessageInfo.setCountryCode(CountryCode.CN);
                    shortMessageInfo.setMessage(content);
                    shortMessageInfo.setPhone(Arrays.asList(driverPhone));
                    Map<String, Object> result =  MessageHandler.sendShortMessage(shortMessageInfo);
                    if (!(boolean)result.get("result")) {
                        errorMsg.append("单据：" + bill.getString("billno") + "发送短信失败，原因：" + result.get("description").toString() + ";");
                    }
                }
            }
        });
        return errorMsg;
    }

    /**
     * 确认预约时，若物料基础资料未维护实体仓库，反写填写的实体仓库到物料基础资料
     * @param bills
     * @author: hst
     * @createDate: 2023/07/27
     */
    private void writeBackMaterialWareHouse (DynamicObject[] bills) {
        Map<Object,Object> temp = new HashMap<>();
        for (DynamicObject bill : bills) {
            DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_materialentry");
            for (DynamicObject entry : entries) {
                DynamicObject material = entry.getDynamicObject("yd_material");
                DynamicObject wareHouse = entry.getDynamicObject("yd_warehouse");
                if (Objects.nonNull(material) && Objects.nonNull(wareHouse)) {
                    temp.put(material.getPkValue(),wareHouse.getPkValue());
                }
            }
        }
        if (temp.size() > 0) {
            Set<Object> materialId = temp.keySet();
            DynamicObject[] materials = BusinessDataServiceHelper.load("bd_material","yd_warehouse",
                    new QFilter[]{new QFilter("id",QFilter.in,materialId)});
            for (DynamicObject material : materials) {
                DynamicObject wareHouse = material.getDynamicObject("yd_warehouse");
                if (Objects.isNull(wareHouse)) {
                    material.set("yd_warehouse",temp.get(material.getPkValue()));
                }
            }
            SaveServiceHelper.save(materials);
        }
    }
}
