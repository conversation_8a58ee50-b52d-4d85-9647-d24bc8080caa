package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.exception.KDBizException;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.pur.oplugin.PurStockUpSendOp
 * @className PurStockUpSendOp
 * @author: hst
 * @createDate: 2024/01/24
 * @version: v1.0
 */
public class PurStockUpSendOp extends AbstractOperationServicePlugIn {

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/01/24
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("yd_supplier");
    }

    /**
     * 事务提交后,发送短信
     * @param e
     * @author: hst
     * @createDate: 2024/01/24
     */
    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        super.afterExecuteOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        // 向供应商发送短信
        this.sendShortMessageToSupplier(bills);
    }

    /**
     * 向供应商发送短信
     * @param bills
     * @author: hst
     * @createDate: 2024/01/24
     */
    private void sendShortMessageToSupplier (DynamicObject[] bills) {
        StringBuffer errStr = new StringBuffer();
        Map<String, Object> result = new HashMap<>();
        for (DynamicObject bill : bills) {
            // 供应商
            DynamicObject supplier = bill.getDynamicObject("yd_supplier");
            if (Objects.nonNull(supplier)) {
                // 获取供应商管理员
                DynamicObject supUserInfo = SupplyStockHelper.getSrmSupplierUserBySupplierId((supplier.getString("id")));
                if (supUserInfo != null) {
                    // 发送短信
                    if (StringUtils.isNotBlank(supUserInfo.getString("user.phone"))) {
                        result = MessageUtil.sendShortMessage(supUserInfo.getString("user.phone"),
                                "【汤臣倍健】已接收到来自汤臣倍健的备货单（该备货单非采购订单，仅作为备货参考依据），单据编号为" +
                                        bill.getString("billno") + "，请及时登录汤臣倍健SRM系统确认单据，" +
                                        "系统菜单路径为：供应协同—订单管理—备货单，谢谢！");
                        if (result != null) {
                            String code = GeneralFormatUtils.getString(result.get("code"));
                            if (!"0".equals(code)) {
                                errStr.append("的短信发送失败，原因：单据：" + bill.getString("billno") + "的短信发送失败，原因：" +
                                        GeneralFormatUtils.getString(result.get("description") + ";" +  "\n"));
                            }
                        }
                    }
                }
            }
        }

        if (errStr.length() > 0) {
            throw new KDBizException(errStr.toString());
        }
    }
}
