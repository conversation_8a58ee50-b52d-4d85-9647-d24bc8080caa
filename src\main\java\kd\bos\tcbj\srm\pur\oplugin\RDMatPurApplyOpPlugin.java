package kd.bos.tcbj.srm.pur.oplugin;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.CloneUtils;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IMetadata;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.exception.ErrorCode;
import kd.bos.exception.KDException;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.tcbj.im.vo.R;
import kd.bos.tcbj.srm.admittance.utils.MetasUtils;
import kd.bos.tcbj.srm.pur.oplugin.validator.RDMatPurApplyValidator;
import kd.bos.tcbj.srm.utils.ConvertHelper;
import kd.bos.yd.tcyp.utils.BizHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description（类描述）: 研发物料采购申请单
 * @author（创建人）: lzp
 * @createDate（创建时间）: 2025/2/18
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class RDMatPurApplyOpPlugin extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        e.getFieldKeys().addAll(MetasUtils.getAllSelector(this.billEntityType.getName()));
        e.getFieldKeys().add("entryentity.yd_isgift");
        e.getFieldKeys().add("entryentity.yd_price");
    }

    @Override
    public void onAddValidators(AddValidatorsEventArgs e) {
        e.addValidator(new RDMatPurApplyValidator());
    }

    @Override
    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
        DynamicObject[] dataEntities = e.getDataEntities();
        String operationKey = e.getOperationKey();
        if (StringUtils.equalsAny(operationKey, "save", "submit")) {
            beforeSave(operationKey, dataEntities);
        }
    }

    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        DynamicObject[] dataEntities = e.getDataEntities();
        String operationKey = e.getOperationKey();
        if (StringUtils.equals(operationKey, "audit")) {
            actionAudit_after(dataEntities);
        }
    }

    /**
     * @description（方法描述）: 保存前处理
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/4/11
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     * @param operationKey 操作标识
     */
    private void beforeSave(String operationKey, DynamicObject[] dataEntities) {
        // 获取明细分录所有的物料编码和名称
        for (DynamicObject dataEntity : dataEntities) {
            String materialInfo = dataEntity.getDynamicObjectCollection("entryentity").stream()
                    .filter(entryInfo -> entryInfo.getDynamicObject("yd_material") != null)
                    .map(entryInfo -> entryInfo.getString("yd_material.number") + entryInfo.getString("yd_material.name"))
                    .distinct()
                    .collect(Collectors.joining(","));
            // 分录物料信息拼接
            dataEntity.set("yd_entrymatinfo", materialInfo);
        }
    }

    private void actionAudit_after(DynamicObject[] dataEntities) {
        CloneUtils cloneUtils = new CloneUtils(true, true);
        OperationResult billOpResult = this.getOperationResult();
        for (DynamicObject dataEntity : dataEntities) {
            // 执行失败或采购类型不为“供应链采购”，则不生成下游单据
            if (!billOpResult.getSuccessPkIds().contains(dataEntity.getPkValue()) || !StringUtils.equals(dataEntity.getString("yd_purtype"), "2")) {
                continue;
            }
            // 分录
            List<ListSelectedRow> selRowList = new ArrayList<>(1);
            selRowList.add(new ListSelectedRow(dataEntity.getPkValue()));
            R pushReportResult = ConvertHelper.push(this.billEntityType.getName(), "yd_rdpurorder", selRowList, null);
            if (pushReportResult.getCode() != 0) {
                throw new KDException(new ErrorCode("transRdPurOrderError", pushReportResult.getMessage()));
            }
            // 获取转换结果
            ConvertOperationResult pushResult = (ConvertOperationResult) pushReportResult.get("result");
            Set<Object> targetBillIds = pushResult.getTargetBillIds();
            DynamicObject[] rdPurOrderCol = getDynamicObject("yd_rdpurorder", targetBillIds.toArray());
            DynamicObject rdPurOrderInfo = rdPurOrderCol[0];
            // 按供应商分组
            DynamicObjectCollection rdPurOrderEntryCol = rdPurOrderInfo.getDynamicObjectCollection("entryentity");
            Map<Long, List<DynamicObject>> supGrpEntryMap = rdPurOrderEntryCol.stream().collect(Collectors.groupingBy(entryInfo -> entryInfo.getLong("yd_supplier_m_id")));
            long headSupId = rdPurOrderInfo.getLong("yd_supplier_id");
            List<String> entryPropList = rdPurOrderEntryCol.getDynamicObjectType().getProperties().stream().map(IMetadata::getName).filter(propName->!propName.equals("id")).collect(Collectors.toList());

            int index = 0;
            DynamicObject[] gCol = new DynamicObject[supGrpEntryMap.size()];
            DynamicObject tempInfo = null;
            for (Long supId : supGrpEntryMap.keySet()) {
                List<DynamicObject> entryGList = supGrpEntryMap.get(supId);
                if (headSupId == supId) {
                    tempInfo = rdPurOrderInfo;
                }else {
                    tempInfo = (DynamicObject) cloneUtils.clone(rdPurOrderInfo);
                    tempInfo.set("billno", null);
                    // 供应商
                    DynamicObject supplierInfo = BusinessDataServiceHelper.newDynamicObject("bd_supplier");
                    supplierInfo.set("id", supId);
                    tempInfo.set("yd_supplier", supplierInfo);
                }
                DynamicObjectCollection tempEntryCol = tempInfo.getDynamicObjectCollection("entryentity");
                tempEntryCol.clear();
                // 赋值分录的值
                for (DynamicObject entryGInfo : entryGList) {
                    DynamicObject tempEntryInfo = tempEntryCol.addNew();
                    for (String entryProp : entryPropList) {
                        tempEntryInfo.set(entryProp, entryGInfo.get(entryProp));
                    }
                }

                gCol[index++] = tempInfo;
            }
            // 批量提交、审核
            OperationResult operationResult = operate("submit", "yd_rdpurorder", gCol);
            if (!operationResult.isSuccess()) {
                throw new KDException(new ErrorCode("transRdPurOrderSplitError", BizHelper.getOperationErrorMessage(operationResult)));
            }
            List<Object> successPkIds = operationResult.getSuccessPkIds();
            operationResult = BizHelper.operate("audit", "yd_rdpurorder", successPkIds.toArray());
            if (!operationResult.isSuccess()) {
                throw new KDException(new ErrorCode("transRdPurOrderSplitError", BizHelper.getOperationErrorMessage(operationResult)));
            }
        }
    }

    /**
     * 根据过滤信息查询对象
     * @param billType 单据标识
     * @param pks 单据ID集合
     * @return 获取对应对象
     * <AUTHOR>
     * @date 2021-12-25
     */
    public static DynamicObject[] getDynamicObject(String billType, Object[] pks) {
        return BusinessDataServiceHelper.load(pks, EntityMetadataCache.getDataEntityType(billType));
    }


    /**
     * 执行单据的操作（通常为单据的提交/审核/反审核）
     * @param operationKey 操作的标识
     * @param billType 单据标识
     * @param dataEntities 处理的单据集合
     * @return 返回处理的结果信息
     * <AUTHOR>
     * @date 2022-06-15
     */
    public static OperationResult operate(String operationKey, String billType, DynamicObject[] dataEntities) {
        return OperationServiceHelper.executeOperate(operationKey, billType, dataEntities, OperateOption.create());
    }
}
