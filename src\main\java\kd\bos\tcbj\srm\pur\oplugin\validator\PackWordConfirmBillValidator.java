package kd.bos.tcbj.srm.pur.oplugin.validator;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;

/**
 * 包材稿件确认单校验器
 * 包材战略采购部审核时控制不能选择文件发放状态为3-待发送
 * @auditor yanzuliang
 * @date 2022年11月23日
 * 
 */
public class PackWordConfirmBillValidator extends AbstractValidator {

	@Override
	public void validate() {
		ExtendedDataEntity[] datas = this.getDataEntities();
		String opKey = this.getOperateKey();

		for (ExtendedDataEntity dataEntity : datas) {
			DynamicObject ov = dataEntity.getDataEntity();

			if ("checkFileOutState".equals(opKey)) {
				Boolean hasUnCheck = false;
				DynamicObjectCollection enCol = ov.getDynamicObjectCollection("entryentity");
				for (DynamicObject enObj : enCol) {
					if ("3".equalsIgnoreCase(enObj.getString("yd_fileoutstate"))) {
						hasUnCheck = true;
						break;
					}
				}
				if (hasUnCheck) {
					this.addErrorMessage(dataEntity, "单号:"+ov.getString("billno")+",明细不能存在待发送状态,请检查!");
				}
			}
		}
	}

}
