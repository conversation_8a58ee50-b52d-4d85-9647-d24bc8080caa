package kd.bos.tcbj.srm.pur.oplugin.validator;

import kd.bos.algo.sql.tree.In;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashSet;

/**
 * @description（类描述）: 研发采购申请单检验插件处理
 * @author（创建人）: lzp
 * @createDate（创建时间）: 2025/3/12
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class RDMatPurApplyValidator extends AbstractValidator {

    @Override
    public void validate() {
        ExtendedDataEntity[] dataEntities = this.getDataEntities();
        String operateKey = this.getOperateKey();
        // 提交的时候校验
        if (StringUtils.equals(operateKey, "submit")) {
            for (int index = 0; index < dataEntities.length; index++) {
                ExtendedDataEntity dataEntity = dataEntities[index];
                checkStandardAttachmentUpload(dataEntity);
                //
                checkNonGiftAmounts(dataEntity);
            }
        }
    }
    
    /**
     * @description（方法描述）: 检查检验标准的质量文件是否上传
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/3/12
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    private void checkStandardAttachmentUpload(ExtendedDataEntity dataEntity) {
        DynamicObject info = dataEntity.getDataEntity();
        DynamicObjectCollection entryCol = info.getDynamicObjectCollection("entryentity");
        boolean hasRDStandard = false;
        for (DynamicObject entryInfo : entryCol) {
            // 研发标准
            if (StringUtils.equals(entryInfo.getString("yd_standard"), "2")) {
                hasRDStandard = true;
                break;
            }
        }
        // 存在研发标准，要求质量文件必需上传
        if (hasRDStandard && info.getInt("yd_attachmentcountfield") == 0) {
            this.addErrorMessage(dataEntity, "物料信息中存在研发标准，必需上传质量文件！");
        }
    }


    /**
     * @description（方法描述）: 非赠品采购单价必录
     * @author（创建人）: pjl
     * @createDate（创建时间）: 2025/5/8
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    private void checkNonGiftAmounts(ExtendedDataEntity dataEntity) {
        DynamicObject info = dataEntity.getDataEntity();
        DynamicObjectCollection entryCol = info.getDynamicObjectCollection("entryentity");
        HashSet<String> rows = new HashSet<>();
        for (int i = 0; i < entryCol.size(); i++) {
            DynamicObject entryInfo = entryCol.get(i);
            // 研发标准
            if (!entryInfo.getBoolean("yd_isgift") && entryInfo.getBigDecimal("yd_price").compareTo(BigDecimal.ZERO) == 0) {
                Integer rowNum = i+1;
                rows.add(rowNum+"");
            }
        }
        if(rows.size()>0){
            String join = StringUtils.join(rows, ",");
            this.addErrorMessage(dataEntity, "物料信息：第"+join+"行物料为非赠品，请录入采购单价！");
        }
    }
}
