package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QFilter;

/**
 * @package: kd.bos.tcbj.srm.pur.plugin.PurCycleListPlugin
 * @className: PurCycleListPlugin
 * @description: 供应商采购周期单列表插件
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 */
public class PurCycleListPlugin extends AbstractListPlugin {

    @Override
    public void setFilter(SetFilterEvent e) {
        super.setFilter(e);
        e.getQFilters().add(new QFilter("billstatus",QFilter.equals, "C"));
    }
}
