package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.list.IListView;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.tcbj.srm.pur.helper.PurDailyPlanHelper;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.pur.plugin.PurDailyPlanListPlugin
 * @className PurDailyPlanListPlugin
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 * @descrition: APS日需求计划列表插件
 */
public class PurDailyPlanListPlugin extends AbstractListPlugin {

    // 计算配额按钮标识
    private final static String CALCULATE_BTN = "yd_calculate";

    /**
     * 按钮点击事件
     * @param evt
     * @author: hst
     * @createDate: 2024/06/08
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case CALCULATE_BTN : {
                // 配额计算
                this.planCalculate();
                break;
            }
        }
    }

    /**
     * 配额计算
     * @author: hst
     * @createDate: 2024/06/08
     */
    private void planCalculate () {
        List<Object> billIds = ((IListView) this.getView()).getSelectedRows().stream()
                .map(ListSelectedRow::getPrimaryKeyValue).distinct().collect(Collectors.toList());

        if (billIds.size() > 0) {
            PurDailyPlanHelper.dailyPlanAllocation(billIds);
            this.getView().invokeOperation("refresh");
        } else {
            this.getView().showTipNotification("请选择至少选择一行数据");
        }
    }
}
