package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;

import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.pur.plugin.PurDeliverFilterListPlugin
 * @className PurDeliverFilterListPlugin
 * @author: hst
 * @createDate: 2023/11/23
 * @description: 预约送货台账物料类型过滤插件
 * @version: v1.0
 */
public class PurDeliverFilterListPlugin extends AbstractListPlugin {

    /**
     * 根据页面参数设置过滤条件（物料类型）
     * @param e
     */
    @Override
    public void setFilter(SetFilterEvent e) {
        List filterLists = e.getQFilters();
        if (this.getView().getFormShowParameter().getCustomParam("mattype") != null) {
            String matType = this.getView().getFormShowParameter().getCustomParam("mattype");
            String[] values = matType.split(",");
            filterLists.add(new QFilter("yd_mattype", QCP.in, values));
        }
        super.setFilter(e);
    }
}
