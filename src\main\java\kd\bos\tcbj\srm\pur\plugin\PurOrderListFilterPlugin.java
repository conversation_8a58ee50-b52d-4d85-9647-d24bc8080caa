package kd.bos.tcbj.srm.pur.plugin;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;

/**
 * 采购订单列表界面参数过滤VMI采购订单
 * @auditor yanzuliang
 * @date 2023年4月17日
 * 
 */
public class PurOrderListFilterPlugin extends AbstractListPlugin {
	
	@Override
	public void setFilter(SetFilterEvent e) {
		super.setFilter(e);
		
		List qFilterList = e.getQFilters();
		if (this.getView().getFormShowParameter().getCustomParam("isVmi") != null) {
			String beVmi = this.getView().getFormShowParameter().getCustomParam("isVmi").toString();
			Set<String> filterSet = new HashSet<String>();filterSet.add("130");filterSet.add("1301");
			if (StringUtils.isNotEmpty(beVmi) && "true".equalsIgnoreCase(beVmi)) {
				qFilterList.add(new QFilter("businesstype.number", QCP.in, filterSet));//过滤VMI类采购订单
			} else {
				qFilterList.add(new QFilter("businesstype.number", QCP.not_in, filterSet));//过滤VMI类采购订单
			}
		}
	}
}
