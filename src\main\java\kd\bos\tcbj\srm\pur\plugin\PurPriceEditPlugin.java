package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.form.IFormView;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.ComboEdit;
import kd.bos.form.field.ComboItem;
import kd.bos.form.field.events.BeforeF7SelectEvent;
import kd.bos.form.field.events.BeforeF7SelectListener;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.sdk.plugin.Plugin;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.EventObject;
import java.util.List;

/**
 * @description（类描述）: 采购价目表编辑界面插件类
 * @author（创建人）: oyzw
 * @createDate（创建时间）: 2025/1/21
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class PurPriceEditPlugin extends AbstractBillPlugIn implements Plugin, BeforeF7SelectListener {
    @Override
    public void registerListener(EventObject e) {
        BasedataEdit material = this.getView().getControl("yd_material");
        material.addBeforeF7SelectListener(this);
    }

    @Override
    public void afterCreateNewData(EventObject e) {
        IFormView pView = this.getView().getParentView();
        String materialType = pView.getFormShowParameter().getCustomParam("materialType");
        this.getModel().setValue("yd_materialtype",materialType);
        setMaterialType(materialType);
        setYearTotalAmt(materialType);
    }
    /**
     * @description（方法描述）: 根据物料类型设置年度采购总额下拉选项
     * @author（创建人）: oyzw
     * @createDate（创建时间）: 2025/4/22
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    private void setYearTotalAmt(String materialType) {
        List<ComboItem> comboItems = new ArrayList<>();
        if(StringUtils.equalsAny(materialType,"B","C")){
            ComboItem cbB1 = new ComboItem();
            cbB1.setValue("D");
            cbB1.setCaption(new LocaleString("年度采购金额<=200万  "));
            ComboItem cbB2 = new ComboItem();
            cbB2.setValue("E");
            cbB2.setCaption(new LocaleString("年度采购金额>200万"));
            comboItems.add(cbB1);
            comboItems.add(cbB2);
        }else if (StringUtils.equals(materialType,"A")){
            ComboItem cbB1 = new ComboItem();
            cbB1.setValue("A");
            cbB1.setCaption(new LocaleString("年度采购金额<=10万"));
            ComboItem cbB2 = new ComboItem();
            cbB2.setValue("B");
            cbB2.setCaption(new LocaleString("10万<年度采购金额<=100万"));
            ComboItem cbB3 = new ComboItem();
            cbB3.setValue("C");
            cbB3.setCaption(new LocaleString("年度采购全额>100万"));
            comboItems.add(cbB1);
            comboItems.add(cbB2);
            comboItems.add(cbB3);
        }
        ComboEdit comboEdit =  this.getControl("yd_yeartotalamount");
        comboEdit.setComboItems(comboItems);
    }

    /**
     * 根据列表参数设置物料类型下拉选项
     */
    private void setMaterialType(String materialType) {
        List<ComboItem> comboItems = new ArrayList<>();
        ComboItem cbB1 = new ComboItem();
        switch (materialType){
            case "A":
                cbB1.setValue("A");
                cbB1.setCaption(new LocaleString("非生产物料"));
                break;
            case "B":
                cbB1.setValue("B");
                cbB1.setCaption(new LocaleString("生产物料（原辅料）"));
                break;
            case "C":
                cbB1.setValue("C");
                cbB1.setCaption(new LocaleString("生产物料（包材）"));
                break;
        }
        ComboItem cbB2 = new ComboItem();
        cbB2.setValue("D");
        cbB2.setCaption(new LocaleString("OEM"));
        ComboItem cbB3 = new ComboItem();
        cbB3.setValue("E");
        cbB3.setCaption(new LocaleString("价格延期"));
        comboItems.add(cbB1);
        comboItems.add(cbB2);
        comboItems.add(cbB3);
        ComboEdit comboEdit =  this.getControl("yd_materialtype");
        comboEdit.setComboItems(comboItems);
    }

    @Override
    public void beforeF7Select(BeforeF7SelectEvent e) {
        String name = e.getProperty().getName();
        IDataModel model = this.getModel();
        if("yd_material".equals(name)){
            String materialType = (String)model.getValue("yd_materialtype");
            if(StringUtils.isBlank(materialType)){
                this.getView().showTipNotification("请先选择物料类型");
                e.setCancel(true);
                return;
            }
            //非生产物料
            if("A".equals(materialType)){
                e.getCustomQFilters().add(new QFilter("number", QCP.like,"L%"));
            }
            //原辅料
            if("B".equals(materialType)){
                e.getCustomQFilters().add(new QFilter("number", QCP.like,"C%"));
            }
            //包材
            if("C".equals(materialType)){
                e.getCustomQFilters().add(
                        new QFilter("number", QCP.like,"D%")
                                .or("number", QCP.like,"E%")
                                .or("number", QCP.like,"F%")
                                .or("number", QCP.like,"G%"));
            }
        }
    }

}

