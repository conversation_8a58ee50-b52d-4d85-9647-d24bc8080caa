package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.srm.pur.helper.UserServerHelper;
import kd.sdk.plugin.Plugin;

import java.util.Arrays;

/**
 * @description（类描述）: 采购价目表列表插件
 * @author（创建人）: oyzw
 * @createDate（创建时间）: 2025/1/21
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class PurPriceListPlugin extends AbstractListPlugin implements Plugin {


    @Override
    public void setFilter(SetFilterEvent e) {
        String materialType = this.getView().getFormShowParameter().getCustomParam("materialType");
        QFilter filter = e.getSpecialDataPermQFilter();
        if (filter != null) {
            QFilter auditorLookFilter = UserServerHelper.getAuditorDataFilter();
            filter.or(auditorLookFilter).and("yd_materialtype", QCP.in, Arrays.asList("D","E",materialType));
            e.setSpecialDataPermQFilter(filter);
        }
    }
}
