package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

/**
 * 采购方退货申请单编辑界面插件类
 * @auditor yanzuliang
 * @date 2022年11月23日
 * 
 */
public class PurRequestEditPlugin extends AbstractBillPlugIn {
	
	@Override
	public void afterDoOperation(AfterDoOperationEventArgs e) {
		super.afterDoOperation(e);
		
		if ("resetPickType".equalsIgnoreCase(e.getOperateKey())) {
			Object pk = this.getModel().getDataEntity().getPkValue();
			DynamicObject bill = BusinessDataServiceHelper.loadSingle(pk, "pur_request");
			bill.set("yd_returnmethod", null);
			bill.set("yd_pickuptime", null);
			bill.set("yd_recipient", null);
			bill.set("yd_contact", null);
			bill.set("yd_address", null);
			bill.set("yd_logistics", null);
			bill.set("yd_failreason", null);
			bill.set("cfmstatus", "A");  // 反写为待确认状态
//			bill.get("yd_attachment").getDynamicObjectCollection("yd_attachment");
			QFilter qFilter_1 = new QFilter("finterid", QFilter.equals, pk.toString());
			QFilter qFilter_2 = new QFilter("fbilltype", QFilter.equals, "pur_request");
			QFilter qFilter_3 = new QFilter("fattachmentpanel", QFilter.equals, "yd_attachment");
			if (QueryServiceHelper.exists("bos_attachment", new QFilter[] { qFilter_1, qFilter_2, qFilter_3 })) {
				int result = DeleteServiceHelper.delete("bos_attachment", new QFilter[] { qFilter_1, qFilter_2, qFilter_3 });
			}
			SaveServiceHelper.save(new DynamicObject[] {bill});
			this.getView().invokeOperation("refresh");
		}
	}
}
