package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.events.ChangeData;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.property.BasedataProp;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.IFormView;
import kd.bos.form.ShowType;
import kd.bos.form.control.Html;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.field.AmountEdit;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.ComboEdit;
import kd.bos.form.field.ComboItem;
import kd.bos.form.field.QtyEdit;
import kd.bos.form.field.events.BeforeF7SelectEvent;
import kd.bos.form.field.events.BeforeF7SelectListener;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.pur.helper.AttachmentHelper;
import kd.bos.tcbj.srm.pur.helper.UserServerHelper;
import kd.bos.yd.tcyp.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.EventObject;
import java.util.HashMap;
import java.util.Map;

/**
 * @description（类描述）: 研发物料采购申请单
 * @author（创建人）: lzp
 * @createDate（创建时间）: 2025/2/8
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class RDMatPurApplyBillPlugin extends AbstractBillPlugIn implements BeforeF7SelectListener {

    @Override
    public void registerListener(EventObject e) {
        BasedataEdit materialF7 = this.getControl("yd_material");
        materialF7.addBeforeF7SelectListener(this);
    }

    @Override
    public void afterBindData(EventObject e) {
        // 设置HTML文本的值
        setOtherHtmlValue();
        // 判断是否有“研发申请单发票分录查看权限”，没有则隐藏分录发票分录
        String billType = this.getModel().getDataEntityType().getName();
        if (!UserServerHelper.getHasSpecificPerm(UserServiceHelper.getCurrentUserId(), UserServerHelper.getAppId(billType), billType, "PURSEQ_INVOICE_ONLOAD")) {
            // 发票分录、采购含税单价、税率、采购含税总价查看权限
            this.getView().setVisible(false, "yd_flexpanelap3", "yd_price", "yd_taxrate", "yd_amount","yd_attachbjd");
        }
        IDataModel model = this.getModel();
        Boolean isChooseAlready = (Boolean)model.getValue("yd_ischoosealready");
        this.setEntryVisiable(isChooseAlready);
    }

    @Override
    public void propertyChanged(PropertyChangedArgs e) {
        String propName = e.getProperty().getName();
        ChangeData changeData = e.getChangeSet()[0];
        // 是否需要送检
        if (StringUtils.equals(propName, "yd_needsubmission")) {
            // 选是，来货入库只能选“四期研发物料(珠海)待检仓”、“研发注册待检仓”、“四期包材(珠海)待检仓”，选否，只能选“工艺技术仓”、“四期包材(珠海)待检仓”
            String needSubmissionVal = (String) changeData.getNewValue();
            // 清空原有值
            this.getModel().setValue("yd_instore", null);
            // 是
            ComboEdit instoreComboEdit = this.getControl("yd_instore");
            if (StringUtils.equals(needSubmissionVal, "1")) {
                instoreComboEdit.setComboItems(Arrays.asList(new ComboItem(new LocaleString("四期研发物料(珠海)待检仓"), "1"), new ComboItem(new LocaleString("研发注册待检仓"), "2"), new ComboItem(new LocaleString("四期包材(珠海)待检仓"), "4")));
            }else {
                instoreComboEdit.setComboItems(Arrays.asList(new ComboItem(new LocaleString("工艺技术仓"), "3"), new ComboItem(new LocaleString("四期包材(珠海)待检仓"), "4")));
            }
        }
        // 采购方式
        else if (StringUtils.equals(propName, "yd_purtype")) {
            // 勾选研发负责人采购时，分录数量和单价是必填项，并且单据审批通过后不生成【研发采购订单】
            String purTypeVal = (String) changeData.getNewValue();
            boolean isPurTypeReq = StringUtils.equals(purTypeVal, "1");
            // 采购数量
            QtyEdit qtyEdit = this.getControl("yd_qty");
            // 采购含税单价
            AmountEdit priceEdit = this.getControl("yd_price");
            // 设置必录
            qtyEdit.setMustInput(isPurTypeReq);
            priceEdit.setMustInput(isPurTypeReq);

            // 设置HTML文本的值
            setOtherHtmlValue();
        }
        // 来货入库
        else if (StringUtils.equals(propName, "yd_instore")) {
//            inStore_propertyChanged(changeData);
        }
        // 是否选已有物料
        else if (StringUtils.equals(propName, "yd_ischoosealready")) {
            Boolean ischoosealready = (Boolean) changeData.getNewValue();
            this.setEntryVisiable(ischoosealready);
            // 清空分录
            this.getModel().deleteEntryData("entryentity");
        }
    }

    /**
     * @description（方法描述）: 来贷入库值变更（禁用，不需要隐藏）
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/3/12
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     * @param 
     * @return 
     */
    @Deprecated
    private void inStore_propertyChanged(ChangeData changeData) {

        String inStoreValue = (String) changeData.getNewValue();
        // 如果是“研发注册仓”，则清空仓库的数据，且设置为不可见
        this.getView().setVisible(!StringUtils.equals(inStoreValue, "2"), "yd_warehouse");
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs evt) {
        String operateKey = evt.getOperateKey();
        OperationResult operationResult = evt.getOperationResult();
        // 入库记录更新
        if (StringUtils.equals(operateKey, "updateinrecord") && operationResult.isSuccess()) {
            // 自定义携带单据的ID处理
            Map<String, Object> param = new HashMap<>();
            param.put("billId", this.getModel().getValue("id"));
//
//            HashMap<String, Object> paramMap = new HashMap<>();
//            paramMap.put("formType", "1");
//            paramMap.put("formId", "yd_rdpurinput");
//            paramMap.put("customParam", param);
//            paramMap.put("needCallBack", Boolean.TRUE);
//            paramMap.put("formName", null);
//            // 访问页面
//            ShowPageUtils.showPage(paramMap, this);

            // 创建弹出页面对象，FormShowParameter表示弹出页面为动态表单
            FormShowParameter formShowParameter = new FormShowParameter();
            // 设置弹出页面的编码
            formShowParameter.setFormId("yd_rdpurinput");
            // 设置参数
            formShowParameter.setCustomParams(param);
            // 设置弹出页面标题
            formShowParameter.setCloseCallBack(new CloseCallBack(this, formShowParameter.getFormId()));
            // 设置弹出页面打开方式，支持模态，新标签等
            formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
            // 弹出页面对象赋值给父页面
            this.getView().showForm(formShowParameter);
        }
        // 一键下载
        else if (StringUtils.equals(operateKey, "downloadattr")) {
            // 批量下载
            AttachmentHelper.batchDownLoad(this.getModel().getEntryEntity("yd_entryentity"), new String[]{"yd_invattach", "yd_purscanattr"}, this.getView(), "发票附件");
        }
    }

    @Override
    public void closedCallBack(ClosedCallBackEvent evt) {
        String actionId = evt.getActionId();
        Object returnData = evt.getReturnData();
        if (StringUtils.equals(actionId, "yd_rdpurinput")) {
            if (returnData != null) {
                this.getView().invokeOperation("refresh");
            }
        }
    }

    /**
     * @description（方法描述）: 设置其他HTML的内容
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/2/17
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     */
    private void setOtherHtmlValue() {

        // 获取供应链采购/工艺研发员入库的HTML控件值
        String otherValueHtml = "<style><style>.tb_normal { background-color: #ffffff; border-collapse: collapse; border: 1px #d2d2d2 solid; padding: 8px !important; text-align: left; margin: 0 auto; } .tb_normal th, .tb_normal td { padding: 8px; word-break: break-word; border: 1px #d2d2d2 solid; white-space: normal; } .colwid { width: 100px; } .display { display: table-row; } .none { display: none; }</style></style><table class = \"tb_normal\" style=\"border-spacing: 0px;\"><tbody><tr><th>供应链采购</th><td style=\"text-align: left;\">已于</td><td class=\"colwid\">%s</td><td>下单采购</td></tr><tr class=\"%s\"><th>工艺研发员入库</th><td style=\"text-align: left;\">已于</td><td class=\"colwid\">%s</td><td>入工艺技术仓</td></tr></tbody></table>";
        // EAS采购订单审批日期
        Date easOrderAuditDate = (Date) this.getModel().getValue("yd_easorderauditdate");
        // 工艺入库日期
        Date warehouseDate = (Date) this.getModel().getValue("yd_warehousedate");

        // 采购方式
        String purTypeVal = (String) this.getModel().getValue("yd_purtype");
        // “采购方式”为“研发负责人采购”时，显示
        String displayVal = StringUtils.equals(purTypeVal, "1")?"display":"none";
        // 填充HTML控件值
        String otherFullHtml = String.format(otherValueHtml, easOrderAuditDate!=null?DateTimeUtils.format(easOrderAuditDate, DateTimeUtils.SDF_DATE):"", displayVal, warehouseDate!=null?DateTimeUtils.format(warehouseDate, DateTimeUtils.SDF_DATE):"");
        // 更新HTML控件值
        Html otherHtml = this.getControl("yd_otherhtml");
        otherHtml.setConent(otherFullHtml);
    }

    @Override
    public void beforeF7Select(BeforeF7SelectEvent evt) {
        String propName = evt.getProperty().getName();
        //  物料按物料类型过滤
        if (StringUtils.equals(propName, "yd_material")) {
            // 包材：物料编码：D、E、F、G开头
            // 原辅料：物料编码：C开头
            String matTypeVal = (String) this.getModel().getValue("yd_mattype");
            QFilter qFilter = null;
            // 原辅料
            if (StringUtils.equals(matTypeVal, "1")) {
                qFilter = QFilter.of("number like 'C%'");
            }
            // 包材
            else if (StringUtils.equals(matTypeVal, "2")) {
                qFilter = QFilter.of("number like 'D%' or number like 'E%' or number like 'F%' or number like 'G%'");
            }
            if (qFilter != null) {
                evt.addCustomQFilter(qFilter);
            }
        }
    }

    /**
     * 根据“是否选已有物料”：显示或隐藏分录字段信息，隐藏关闭必录校验
     * @param isChooseAlready
     */
    private void setEntryVisiable(Boolean isChooseAlready){
        IFormView view = this.getView();
        if(isChooseAlready){
            // 设置必录，隐藏文本字段，显示基础资料字段
            BasedataEdit material=this.getControl("yd_material");
            material.setMustInput(true);
            BasedataProp materialproperty = (BasedataProp) material.getProperty();
            materialproperty.setMustInput(true);

            BasedataEdit supplier=this.getControl("yd_supplier");
            supplier.setMustInput(true);
            BasedataProp supplierproperty = (BasedataProp) supplier.getProperty();
            supplierproperty.setMustInput(true);
            view.setVisible(true,new String[]{"yd_material","yd_materialname","yd_srmproducers","yd_supplier"});
            view.setVisible(false,new String[]{"yd_material1","yd_materialname1","yd_srmproducers1","yd_supplier1"});
        }else {
            // 关闭必录，显示文本字段，隐藏基础资料字段
            BasedataEdit material=this.getControl("yd_material");
            material.setMustInput(true);
            BasedataProp materialproperty = (BasedataProp) material.getProperty();
            materialproperty.setMustInput(true);

            BasedataEdit supplier=this.getControl("yd_supplier");
            supplier.setMustInput(true);
            BasedataProp supplierproperty = (BasedataProp) supplier.getProperty();
            supplierproperty.setMustInput(true);
            view.setVisible(false,new String[]{"yd_material","yd_materialname","yd_srmproducers","yd_supplier"});
            view.setVisible(true,new String[]{"yd_material1","yd_materialname1","yd_srmproducers1","yd_supplier1"});
        }
    }
}
