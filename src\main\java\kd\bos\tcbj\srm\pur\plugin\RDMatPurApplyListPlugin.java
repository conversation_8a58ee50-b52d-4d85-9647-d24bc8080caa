package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.srm.pur.helper.UserServerHelper;

/**
 * @description（类描述）: 研发采购申请单
 * @author（创建人）: lzp
 * @createDate（创建时间）: 2025/3/12
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class RDMatPurApplyListPlugin extends AbstractListPlugin {

    @Override
    public void setFilter(SetFilterEvent e) {

        QFilter qFilter = e.getSpecialDataPermQFilter();
        if (qFilter != null) {
            QFilter auditorLookFilter = UserServerHelper.getAuditorDataFilter();
            qFilter.or(auditorLookFilter);
            e.setSpecialDataPermQFilter(qFilter);
        }
    }
}
