package kd.bos.tcbj.srm.pur.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.control.events.BeforeClickEvent;
import kd.bos.tcbj.im.plugin.KdepFormPlugin;
import kd.bos.yd.tcyp.utils.BizHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @description（类描述）: 研发入库记录更新
 * @author（创建人）: lzp
 * @createDate（创建时间）: 2025/2/17
 * @updateUser（修改人）:
 * @updateDate（修改时间）:
 * @updateRemark（修改备注）:
 */
public class RDPurInputFormPlugin extends KdepFormPlugin {

    /**确认按钮*/
    protected static final String KEY_BTN_OK = "btnok";
    /**取消按钮*/
    protected static final String KEY_BTN_CANCEL = "btncancel";

    @Override
    public void initialize() {
        this.addClickListeners(KEY_BTN_CANCEL, KEY_BTN_OK);
    }

    @Override
    public void beforeClick(BeforeClickEvent evt) {
        String key = this.getKey(evt);
        if (StringUtils.equals(key, KEY_BTN_OK)) {
            actionOk(evt);
        }else if (StringUtils.equals(key, KEY_BTN_CANCEL)) {
            actionCancel(evt);
        }
    }

    /**
     * @description（方法描述）: 确认
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/2/17
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     * @param
     * @return
     */
    private void actionOk(BeforeClickEvent evt) {

        // 校验字段是否已经填写
        Date warehouseDate = (Date) this.getModel().getValue("yd_warehousedate");
        if (warehouseDate == null) {
            this.getView().showTipNotification("工艺入库日期不能为空！");
            evt.setCancel(true);
            return;
        }

        // 获取上游的研发申请单ID
        Object billId = this.getView().getFormShowParameter().getCustomParam("billId");
        // 反写5、研发物料采购申请单的”工艺入库日期“
        DynamicObject info = BizHelper.getDynamicObjectById("yd_rdmatpurapply", billId, "yd_warehousedate");
        info.set("yd_warehousedate", warehouseDate);
        BizHelper.save(info);

        this.getView().returnDataToParent(warehouseDate);
        this.getView().close();
    }

    /**
     * @description（方法描述）: 取消
     * @author（创建人）: lzp
     * @createDate（创建时间）: 2025/2/17
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     * @param 
     * @return 
     */
    private void actionCancel(BeforeClickEvent evt) {

        this.getView().returnDataToParent(null);
        this.getView().close();
    }
}
