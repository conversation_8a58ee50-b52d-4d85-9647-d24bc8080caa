package kd.bos.tcbj.srm.pur.rpt.data;

import kd.bos.algo.DataSet;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.RptUtil;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.pur.rpt.data.PackMatRptListDataPlugin
 * @className: PackMatRptListDataPlugin
 * @description: 包材交货情况报表查询插件
 * @author: hst
 * @createDate: 2023/1/4
 * @version: v1.0
 */
public class PackMatRptListDataPlugin  extends AbstractReportListDataPlugin {
    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        FilterInfo filterInfo = reportQueryParam.getFilter();
        // 物料、供应商过滤
        List<QFilter> filters = buildFilter(filterInfo);
        // 查询协同采购订单
        DataSet order = QueryServiceHelper.queryDataSet(this.getClass().getName(),"pur_order",
                "billno yd_orderno,billstatus yd_orderstatus,materialentry.material yd_ordermat,materialentry.id yd_orderid,supplier yd_supplier," +
                        "materialentry.unit yd_orderunit,materialentry.qty yd_orderqty,materialentry.entrydelidate yd_orderdate",
                filters.toArray(new QFilter[filters.size()]),null);
        order = order.select(new String[]{"yd_orderno","yd_orderstatus","yd_ordermat","LEFT (yd_orderid,19) yd_orderid","yd_orderunit","yd_orderqty",
                "yd_orderdate","yd_supplier"});
        // 查询供应商发货
        DataSet salout = QueryServiceHelper.queryDataSet(this.getClass().getName(),"pur_saloutstock","billno yd_saloutno," +
                "delidate yd_saloutdate,materialentry.qty yd_saloutqty,materialentry.srcentryid yd_saloutid,materialentry.id yd_salid",
                null,null);
        order = order.leftJoin(salout).on("yd_orderid","yd_saloutid").select(new String[]{"yd_orderno","yd_orderstatus","yd_ordermat","yd_supplier",
                "yd_orderid","yd_orderunit","yd_orderqty","yd_orderdate","yd_saloutno","LEFT(yd_saloutdate,10) yd_saloutdate",
                "yd_saloutqty","yd_saloutid","LEFT (yd_salid,19) yd_salid"}).finish();
        // 查询采购收货
        DataSet receipt = QueryServiceHelper.queryDataSet(this.getClass().getName(),"pur_receipt","billno yd_reno,billdate yd_redate," +
                "materialentry.qty yd_reqty,materialentry.yd_saledeliveryenid yd_reid",null,null);
        order = order.leftJoin(receipt).on("yd_salid","yd_reid").select(new String[]{"yd_orderno","yd_orderstatus","yd_ordermat","yd_supplier",
                "yd_orderid","yd_orderunit","yd_orderqty","yd_orderdate","yd_saloutno","yd_saloutdate","yd_saloutqty","yd_saloutid","yd_salid",
                "yd_reno","yd_redate","yd_reqty","yd_reid"}).finish();
        order = order.groupBy(new String[]{"yd_supplier","yd_ordermat","yd_orderunit","yd_saloutdate"})
                .sum("yd_orderqty").sum("yd_saloutqty").sum("yd_reqty").finish();
        // 预计到货日期
        QFilter dateFilter = RptUtil.buildDateFilter(filterInfo,"yyyy-MM-dd","yd_date",
                "yd_saloutdate",QFilter.equals);
        if (Objects.nonNull(dateFilter)) {
            order = order.filter(dateFilter.toString());
        }
        return order;
    }

    /**
     * 筛选条件构造过滤条件（供应商、物料）
     * @author: hst
     * @createDate: 2023/1/4
     * @param filterInfo
     * @return
     */
    public List<QFilter> buildFilter (FilterInfo filterInfo) {
        //筛选条件
        List<QFilter> qFilters = new ArrayList<>();
        QFilter supFilter = RptUtil.buildF7Filter(filterInfo,"yd_supply","supplier.number",QFilter.equals);
        QFilter materialFilter = RptUtil.buildF7Filter(filterInfo,"yd_material","materialentry.material.number",QFilter.equals);
        if (Objects.nonNull(supFilter)) {
            qFilters.add(supFilter);
        }
        if (Objects.nonNull(materialFilter)) {
            qFilters.add(materialFilter);
        }
        qFilters.add(QFilter.of("materialentry.material.number is not null"));
        return qFilters;
    }

    /**
     * 筛选条件构造时间过滤条件
     * @author: hst
     * @createDate: 2022/10/21
     * @param filterInfo
     * @return
     */
    public static QFilter buildDateFilter (FilterInfo filterInfo, String pattern, String propName, String property, String cp) throws ParseException { FilterItemInfo beginDate = filterInfo.getFilterItem(propName);
        QFilter qFilter = null;
        if (Objects.nonNull(beginDate)) {
            Date date = (Date) beginDate.getValue();
            if (Objects.nonNull(date)) {
                date = DateUtil.parseDate(DateUtil.date2str(date,pattern));
                qFilter = new QFilter(property, cp, date);
            }
        }
        return qFilter;
    }
}
