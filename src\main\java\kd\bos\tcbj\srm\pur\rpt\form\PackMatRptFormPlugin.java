package kd.bos.tcbj.srm.pur.rpt.form;

import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.ShowType;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.report.ReportList;
import kd.bos.report.plugin.AbstractReportFormPlugin;

import java.util.EventObject;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.pur.rpt.form.PackMatRptFormPlugin
 * @className: PackMatRptFormPlugin
 * @description: 包材交货情况报表界面插件
 * @author: hst
 * @createDate: 2023/1/4
 * @version: v1.0
 */
public class PackMatRptFormPlugin extends AbstractReportFormPlugin implements HyperLinkClickListener {

    /**
     * 注册监听
     * <AUTHOR>
     * @createDate: 2023/1/4
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        ReportList reportList = getControl("reportlistap");
        reportList.addHyperClickListener(this);
    }

    /**
     * 监听用户点击列表上的超链接，跳转对应供应商库页面
     * <AUTHOR>
     * @createDate: 2023/1/4
     * @param hyperLinkClickEvent
     */
    @Override
    public void hyperLinkClick(HyperLinkClickEvent hyperLinkClickEvent) {
        String key = hyperLinkClickEvent.getFieldName();
        switch (key) {
            case "yd_ordermat" : {
                this.forwardToPage(hyperLinkClickEvent,"bd_material");
                break;
            }
            case "yd_supplier" : {
                this.forwardToPage(hyperLinkClickEvent,"bd_supplier");
                break;
            }
        }
    }

    /**
     * 跳转对应页面
     * @author: hst
     * @createDate: 2023/1/4
     * @param hyperLinkClickEvent
     */
    public void forwardToPage(HyperLinkClickEvent hyperLinkClickEvent,String type) {
        int index = hyperLinkClickEvent.getRowIndex();
        DynamicObject data = ((ReportList)this.getControl("reportlistap")).getReportModel().getRowData(index);
        String title = "";
        String field = "";
        switch (type) {
            case "bd_material" : {
                title = "物料";
                field = "yd_ordermat";
                break;
            }
            case "bd_supplier" : {
                title = "供应商";
                field = "yd_supplier";
                break;
            }
        }
        DynamicObject dynamicObject = data.getDynamicObject(field);
        if (Objects.nonNull(dynamicObject)) {
            BillShowParameter billShowParameter = new BillShowParameter();
            billShowParameter.setFormId(type);
            billShowParameter.setPkId(dynamicObject.get("id"));
            billShowParameter.setCaption(String.format(title));
            billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
            this.getView().showForm(billShowParameter);
        } else {
            this.getView().showErrorNotification("系统异常，请联系管理员！");
        }
    }
}
