package kd.bos.tcbj.srm.pur.rpt.form;

import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.report.ReportList;
import kd.bos.report.events.CellStyleRule;
import kd.bos.report.plugin.AbstractReportFormPlugin;
import kd.bos.tcbj.srm.pur.helper.PurSDBalanceRptHelper;

import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.pur.rpt.form.PurSDBalanceRptFormPlugin
 * @className: PurSDBalanceRptFormPlugin
 * @description: 供应商库存动态高低水位即供需平衡报表界面插件
 * @author: hst
 * @createDate: 2024/06/09
 * @version: v1.0
 */
public class PurSDBalanceRptFormPlugin extends AbstractReportFormPlugin {

    /**
     * 取数前事件
     * @param queryParam
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void beforeQuery(ReportQueryParam queryParam) {
        ReportList reportList = getControl("reportlistap");
        List<FilterItemInfo> filterInfos = this.getQueryParam().getFilter().getFilterItems();
        List<QFilter> filters = this.getQueryParam().getCustomFilter();
        // 报表列表中动态添加表格字段
        PurSDBalanceRptHelper.createGroupColumnList(filterInfos,filters,reportList);
        super.beforeQuery(queryParam);
    }

    /**
     * 校验过滤条件
     * @param queryParam
     * @return
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public boolean verifyQuery(ReportQueryParam queryParam) {
        // 构建过滤条件
        try {
            PurSDBalanceRptHelper.buildQueryFilter(queryParam);
            return true;
        } catch (Exception e) {
            this.getView().showTipNotification(e.getMessage());
            return false;
        }
    }

    /**
     * 设置单元格样式规则
     * @param cellStyleRules
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void setCellStyleRules(List<CellStyleRule> cellStyleRules) {
        super.setCellStyleRules(cellStyleRules);
        PurSDBalanceRptHelper.setCellStyle(cellStyleRules);
    }
}
