package kd.bos.tcbj.srm.pur.schedulejob;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package kd.bos.tcbj.srm.pur.schedulejob.DeliverStandTask
 * @className DeliverStandTask
 * @author: hst
 * @createDate: 2023/01/05
 * @version: v1.0
 */
public class DeliverStandTask extends AbstractTask {

    // 到货提醒
    private final static String ARRIVAL_REMINDER = "arrival_reminder";
    // 取消预约
    private final static String CANCEL_RESERVATION = "cancel_reservation";
    // 长时未确认再次提醒
    private final static String AGAIN_RESERVATION = "again_reservation";

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        if (map.containsKey("key")) {
            String key = map.get("key").toString();
            switch (key) {
                case ARRIVAL_REMINDER : {
                    // 提醒仓库人员当天到货单据
                    this.arrivalReminder();
                    break;
                }
                case CANCEL_RESERVATION : {
                    // 取消预约,让司机可以重新填写预约日期
                    this.cancelReservation();
                    break;
                }
                case AGAIN_RESERVATION : {
                    // 长时未确认预约再次提醒仓管员
                    this.timeoutNotConfirmedReminderAgain();
                }
            }
        }
    }

    /**
     * 向仓管发送短信
     * @author: hst
     * @createDate: 2022/01/05
     */
    public void arrivalReminder () {
        // 存储物料信息
        Map<String,String> materials = new HashMap<>();
        // 获取单天到货的单据
        Date today = new Date();
        String beginDate = DateUtil.date2str(today,"yyyy-MM-dd  00:00:00");
        String endDate = DateUtil.date2str(today,"yyyy-MM-dd  23:59:59");
        QFilter beginFilter = new QFilter("yd_arrivaldate",QFilter.large_equals,beginDate);
        QFilter endFilter = new QFilter("yd_arrivaldate",QFilter.less_equals,endDate);
        QFilter statusFilter = new QFilter("billstatus",QFilter.equals,"B");
        DataSet bills = QueryServiceHelper.queryDataSet(this.getClass().getName(),"yd_pur_deliverstand",
                "yd_silotubecontact,yd_materialentry.yd_material.name",new QFilter[]{beginFilter,endFilter,statusFilter},null);
        while (bills.hasNext()) {
            Row row = bills.next();
            // 联系方式
            String contact = row.getString("yd_silotubecontact");
            // 物料名称
            String material = row.getString("yd_materialentry.yd_material.name");
            if (StringUtils.isNotBlank(contact) && StringUtils.isNotBlank(material)) {
                if (materials.containsKey(contact)) {
                    String materialStr = materials.get(contact).toString();
                    if (materialStr.indexOf(material) == -1) {
                        materialStr = materialStr + material + "、";
                        materials.put(contact, materialStr);
                    }
                } else {
                    materials.put(contact,material + "、");
                }
            }
        }
        // 根据获取到的信息发送短信
        if (materials.size() > 0) {
            for (Map.Entry<String,String> entry : materials.entrySet()) {
                ShortMessageInfo messageInfo = new ShortMessageInfo();
                messageInfo.setPhone(Arrays.asList(new String[]{entry.getKey()}));
                messageInfo.setCountryCode(CountryCode.CN);
                String message = "今日预计到货物料：" + entry.getValue().substring(0,entry.getValue().length() - 1) +
                        ",请及时办理。";
                messageInfo.setMessage(message);
                MessageHandler.sendShortMessage(messageInfo);
            }
        }
    }

    /**
     * 如果当前时间与预约时间差值大于N小时，则撤销提交预约送货台帐
     * @author: hst
     * @createDate: 2023/01/12
     */
    public void cancelReservation () {
        // 查询配置的参数
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"PUR_DELIVERSTAND_HOUR")});
        if (Objects.nonNull(param)) {
            String value = param.getString("name");
            int hour;
            try {
                hour = Integer.parseInt(value);
            } catch (Exception e) {
                throw new KDBizException("PUR_DELIVERSTAND_HOUR参数值转换异常，请输入整数!");
            }
            // 计算过期时间
            Date now = new Date();
            Date expTime = DateUtil.addHour(now,-hour);
            // 日期过滤
            QFilter dateFilter = new QFilter("yd_arrivaldate",QFilter.less_equals,DateUtil.date2str(expTime,"yyyy-MM-dd HH:mm:ss"));
            QFilter statusFilter = new QFilter("billstatus",QFilter.equals,"B");
            DataSet bills = QueryServiceHelper.queryDataSet(this.getClass().getName(),"yd_pur_deliverstand","id",
                    new QFilter[]{dateFilter,statusFilter},null);
            // 存储符合条件的id
            List<String> ids = new ArrayList<>();
            while (bills.hasNext()) {
                Row row = bills.next();
                if (StringUtils.isNotBlank(row.getString("id"))) {
                    ids.add(row.getString("id"));
                }
            }
            if (ids.size() > 0) {
                // 反提交
                OperationServiceHelper.executeOperate("unsubmit","yd_pur_deliverstand",ids.toArray(), OperateOption.create());
            }
        } else {
            throw new KDBizException("获取不到PUR_DELIVERSTAND_HOUR参数信息，请前往参数配置表中配置！");
        }
    }

    /**
     * 超时未确认再次提醒
     * @author: hst
     * @createDate: 2023/07/26
     */
    private void timeoutNotConfirmedReminderAgain(){
        Date now = new Date();
        int hour = 2;
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"SCM_DELIVER_RESEND_TIME")});
        if (Objects.nonNull(param)) {
            hour = param.getInt("name");
        }
        Date time = DateUtil.addHour(now,-hour);
        // update by hst 2023/11/06 修改短信格式
        DynamicObject[] bills = BusinessDataServiceHelper.load("yd_pur_deliverstand",
                "billno,yd_supplier,yd_shipper,yd_contact,yd_arrivaldate,yd_materialentry.yd_warehouse," +
                        "yd_materialentry.yd_material,yd_materialentry.yd_qty,yd_materialentry.yd_unit",
                new QFilter[]{new QFilter("yd_orderdate",QFilter.large_equals,time),
                new QFilter("yd_billstatus",QFilter.equals,"B")});
        for (DynamicObject bill : bills) {
            sendDeliveryReservation(bill);
        }
    }

    /**
     * 向仓库管理员发送预约送货信息
     * @param bill
     * @author: hst
     * @createDate: 2023/07/13
     */
    private void sendDeliveryReservation (DynamicObject bill) {
        Map<String,String> temp = new HashMap();
        DynamicObjectCollection entries = bill.getDynamicObjectCollection("yd_materialentry");
        // update by hst 2023/11/06 修改短信格式
        DynamicObject supply = bill.getDynamicObject("yd_supplier");
        if (Objects.nonNull(supply)) {
            for (DynamicObject entry : entries) {
                DynamicObject warehouse = entry.getDynamicObject("yd_warehouse");
                DynamicObject material = entry.getDynamicObject("yd_material");
                if (Objects.nonNull(material)) {
                    String context = material.getString("number") + " " + material.getString("name")
                            + " " + entry.getBigDecimal("yd_qty").setScale(2) + " " + (Objects.nonNull(entry.getDynamicObject("yd_unit"))
                            ? entry.getDynamicObject("yd_unit").getString("name") : "");
                    if (Objects.isNull(warehouse)) {
                        if (temp.containsKey("regular")) {
                            temp.put("regular", temp.get("regular") +
                                    "、" + context);
                        } else {
                            temp.put("regular", context);
                        }
                    } else if (Objects.nonNull(warehouse)) {
                        if (temp.containsKey(warehouse.getString("id"))) {
                            temp.put(warehouse.getString("id"), temp.get(warehouse.getString("id")) +
                                    "、" + context);
                        } else {
                            temp.put(warehouse.getString("id"), context);
                        }
                    }
                }
            }
            if (temp.size() > 0) {
                Map<String, String> infos = new HashMap<>();
                // 先获取仓库基础资料
                List<String> ids = temp.keySet().stream().collect(Collectors.toList());
                QFilter filter = new QFilter("id", QFilter.in, ids);
                DynamicObject[] warehouses = BusinessDataServiceHelper.load("yd_warehouse",
                        "name,yd_management,yd_phone,yd_address,yd_management_res,yd_contact", new QFilter[]{filter});
                String billNo = bill.getString("billno");
                // update by hst 2023/11/06 修改短信格式
                String context = "预约送货台账" + billNo + "由司机" + bill.getString("yd_shipper") + "，电话为"
                        + bill.getString("yd_contact") + "，预约送货时间"
                        + DateUtil.date2str(bill.getDate("yd_arrivaldate"), "yyyy年MM月dd日HH时mm分") +
                        "。   物料供应商为：" + supply.getString("name") + "。   物料明细为：";
                for (DynamicObject store : warehouses) {
                    String matNums = temp.get(store.getString("id"));
                    if (StringUtils.isNotBlank(store.getString("yd_contact"))) {
                        infos.put(store.getString("yd_contact"), context + matNums + "。   请及时前往SRM系统确认预约。");
                    } else {
                        if (temp.containsKey("regular")) {
                            temp.put("regular", temp.get("regular") +
                                    "," + matNums);
                        } else {
                            temp.put("regular", matNums);
                        }
                    }
                }
                if (temp.containsKey("regular")) {
                    DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name",
                            new QFilter[]{new QFilter("number", QFilter.equals, "SCM_DELIVER_RESEND_PHONE")});
                    if (Objects.nonNull(param)) {
                        String matNums = temp.get("regular");
                        infos.put(param.getString("name"), context + matNums + "。   请及时前往SRM系统确认预约。");
                    }
                }
                this.sendMessages(infos);
            }
        }
    }

    /**
     * 发送短信
     * @param infos
     * @author: hst
     * @createDate: 2023/07/13
     */
    private void sendMessages (Map<String,String> infos) {
        for (Map.Entry<String,String> info : infos.entrySet()) {
            ShortMessageInfo shortMessageInfo = new ShortMessageInfo();
            String driverPhone = info.getKey();
            shortMessageInfo.setCountryCode(CountryCode.CN);
            shortMessageInfo.setMessage(info.getValue());
            shortMessageInfo.setPhone(Arrays.asList(driverPhone));
            Map<String, Object> result =  MessageHandler.sendShortMessage(shortMessageInfo);
        }
    }
}
