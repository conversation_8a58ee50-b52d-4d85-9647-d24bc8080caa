package kd.bos.tcbj.srm.pur.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.srm.pur.helper.PurDailyPlanHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.pur.schedulejob.PurDailyPlanTask
 * @className PurDailyPlanTask
 * @author: hst
 * @createDate: 2024/06/07
 * @version: v1.0
 * @descrition: APS日需求计划计算调度任务
 */
public class PurDailyPlanTask extends AbstractTask {

    private static final Log log = LogFactory.getLog(PurDailyPlanTask.class.getName());
    private final static String PLAN_CALCULATE = "plan_calculate";

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        try {
            String key = map.containsKey("key") ? map.get("key").toString() : "";
            String bizDate = map.containsKey("date") ? map.get("date").toString() : "";
            List<String> deleteList = map.containsKey("deleteList") ? (List<String>) map.get("deleteList") : new ArrayList<>();
            switch (key) {
                case PLAN_CALCULATE: {
                    // 通过需求计划ID删除配额
                    PurDailyPlanHelper.deletePurMatQuotaById(deleteList);
                    // 需求配额计算（获取当前未计算的）
                    PurDailyPlanHelper.dailyPlanAllocation(bizDate);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
