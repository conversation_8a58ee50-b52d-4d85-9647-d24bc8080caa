package kd.bos.tcbj.srm.pur.webapi;

import com.alibaba.fastjson.JSONArray;
import kd.bos.openapi.common.custom.annotation.ApiController;
import kd.bos.openapi.common.custom.annotation.ApiMapping;
import kd.bos.openapi.common.custom.annotation.ApiParam;
import kd.bos.openapi.common.custom.annotation.ApiPostMapping;
import kd.bos.openapi.common.result.CustomApiResult;
import kd.bos.tcbj.srm.pur.helper.PurDailyPlanHelper;

/**
 * @package（包）: kd.bos.tcbj.srm.pur.webapi.PurDailyPlanCallBackPlugin
 * @className（类名称）: PurDailyPlanCallBackPlugin
 * @description（类描述）: APS日需求计划接口
 * @author（创建人）: hst
 * @createDate（创建时间）: 2024/06/08
 * @version（版本）: v1.0
 */
@ApiController(value = "pur", desc = "采购协同")
@ApiMapping(value = "dailyPlan")
public class PurDailyPlanCallBackPlugin {

    @ApiPostMapping(value = "save", desc = "APS日需求计划新增接口")
    public CustomApiResult<Object> save (@ApiParam("datas") JSONArray datas) {
        return new PurDailyPlanHelper().addNewDailyPlan(datas);
    }
}
