package kd.bos.tcbj.srm.quo.oplugin;

import com.kingdee.bos.ctrl.common.util.StringUtil;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.quo.helper.QuoQuoteHelper;
import kd.bos.tcbj.srm.sou.helper.SouInquiryHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.quo.oplugin.QuoQuoteOp
 * @className QuoQuoteOp
 * @author: hst
 * @createDate: 2022/09/09
 * @description: 代录报价操作插件
 * @version: v1.0
 */
public class QuoQuoteOp extends AbstractOperationServicePlugIn {

    /**
     * 预加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/04/24
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add(QuoQuoteHelper.FIELD_BEHALF);
    }

    private final static String OP_AUDIT = "audit";
    private final static String OP_SUMBIT = "submit";
    private final static String OP_UNAUDIT = "unaudit";

    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        String key = e.getOperationKey();
        DynamicObject[] quotes = e.getDataEntities();
        switch (key) {
            case OP_AUDIT : {
                this.updateInquiry(quotes,true);
                break;
            }
            case OP_UNAUDIT : {
                this.updateInquiry(quotes,false);
            }
        }
    }

    /**
     * 更新询价单中参与供应商的是否代录供应商字段
     * @author: hst
     * @createDate: 2022/09/13
     * @param quotes
     */
    public void updateInquiry(DynamicObject[] quotes, boolean isBehalf) {
         for (DynamicObject quote : quotes) {
            if ((boolean) quote.get(QuoQuoteHelper.FIELD_BEHALF)) {
                String supplyNumber = quote.getDynamicObject(QuoQuoteHelper.FIELD_SUPPLIER).getString("number");
                String inquiryNo = quote.getString(QuoQuoteHelper.FIELD_INQUIRYNO);
                DynamicObject inquiry = BusinessDataServiceHelper.loadSingle(SouInquiryHelper.ENTITY_MAIN,
                        "turns,entryentity,entryentity.supplier,entryentity.yd_behalf,entryentity.entryturns",
                        new QFilter[]{new QFilter("billno", QFilter.equals, inquiryNo)});
                if (inquiry != null) {
                    DynamicObjectCollection entity = inquiry.getDynamicObjectCollection("entryentity");
                    String turns = inquiry.getString("turns");
                    for (DynamicObject entry : entity) {
                        DynamicObject supply = entry.getDynamicObject(SouInquiryHelper.COLUNM_SUPPLIER);
                        if (StringUtil.equals(supply.get("number").toString(), supplyNumber)
                                && StringUtil.equals(entry.getString("entryturns"),turns)) {
                            entry.set("yd_behalf", isBehalf);
                            SaveServiceHelper.save(new DynamicObject[]{inquiry});
                        }
                    }
                }
            }
        }
    }

    /**
     * 用于提交后直接做审核操作
     * @author: hst
     * @createDate: 2022/09/13
     * @param quotes
     */
    public void auditQuote(DynamicObject[] quotes) {
        List<Object> list = new ArrayList<>();
        for (DynamicObject quote : quotes) {
            if ((boolean) quote.get(QuoQuoteHelper.FIELD_BEHALF)) {
                list.add(Long.valueOf(quote.get("id").toString()));
            }
        }
        if (list.size() > 0) {
            OperationResult result = OperationServiceHelper.executeOperate("audit", QuoQuoteHelper.ENTITY_MAIN,
                    list.toArray(new Object[list.size()]), OperateOption.create());
        }
    }
}
