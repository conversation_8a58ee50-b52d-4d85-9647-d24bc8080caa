package kd.bos.tcbj.srm.quo.oplugin;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.quo.helper.QuoQuoteHelper;
import kd.fi.cas.helper.OperateServiceHelper;

/**
 * @package: kd.bos.tcbj.srm.quo.oplugin.QuoQuoteSubmitOp
 * @className QuoQuoteSubmitOp
 * @author: hst
 * @createDate: 2022/11/08
 * @description: 代录报价提交按钮插件
 * @version: v1.0
 */
public class QuoQuoteSubmitOp extends AbstractOperationServicePlugIn {

    private final static String OP_SUMBIT = "submit";
    private final static Log logger = LogFactory.getLog("QuoQuoteSubmitOp");

// update by hst -2022/11/08 表单插件回写询价单异常
//    @Override
//    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
//        String key = e.getOperationKey();
//        DynamicObject[] quotes = e.getDataEntities();
//        switch (key) {
//            case "submit" : {
//                for (DynamicObject quote : quotes) {
//                    logger.info(quote.getString("billNo") + "开始提交！字段yd_behalf值为：" +
//                            quote.get(QuoQuoteHelper.FIELD_BEHALF));
//                    boolean beHalf = (boolean) quote.get(QuoQuoteHelper.FIELD_BEHALF);
//                    if (beHalf) {
//                        DynamicObject supplier = quote.getDynamicObject("supplier");
//                        DynamicObject bizpartner = BusinessDataServiceHelper.loadSingle("bd_bizpartner", "id,number,name",
//                                new QFilter[]{new QFilter("number", QFilter.equals, supplier.get("number"))});
//                        quote.set("bizpartner", bizpartner);
//                    }
//                }
//            }
//        }
//    }

//    @Override
//    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
//        String key = e.getOperationKey();
//        DynamicObject[] quotes = e.getDataEntities();
//        switch (key) {
//            case "submit" : {
//                for (DynamicObject quote : quotes) {
//                    if ((boolean) quote.get(QuoQuoteHelper.FIELD_BEHALF)) {
//                        logger.info(quote.getString("billNo") + "提交完毕！");
//                        OperateServiceHelper.execOperate("audit",QuoQuoteHelper.ENTITY_MAIN,new DynamicObject[]{quote},OperateOption.create());
//                    }
//                }
//            }
//        }
//    }

    /**
     * 测试环境调试提交并审核
     * @param e
     */
    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        DynamicObject[] quotes = e.getDataEntities();
        for (DynamicObject quote : quotes) {
            if ((boolean) quote.get(QuoQuoteHelper.FIELD_BEHALF) && "B".equals(quote.getString("billstatus"))) {
                logger.info(quote.getString("billNo") + "提交成功，开始执行审核操作！字段yd_behalf值为：" +
                        quote.get(QuoQuoteHelper.FIELD_BEHALF));
                OperationResult result = OperateServiceHelper.execOperate("audit",
                        QuoQuoteHelper.ENTITY_MAIN, new DynamicObject[]{quote}, OperateOption.create());
                if (result.isSuccess()) {
                    logger.info(quote.getString("billNo") + "审核成功！");
                }
            } else {
                logger.info(quote.getString("billNo") + "提交失败！");
            }
        }
    }
}
