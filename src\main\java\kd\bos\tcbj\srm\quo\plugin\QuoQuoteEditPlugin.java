package kd.bos.tcbj.srm.quo.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.datamodel.AbstractFormDataModel;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.entity.operate.Submit;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.container.Container;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.list.ListShowParameter;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.quo.helper.QuoQuoteHelper;
import kd.fi.cas.helper.OperateServiceHelper;
import kd.scm.common.constant.BillAssistConstant;

import java.util.EventObject;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.quo.plugin.QuoQuoteEditPlugin
 * @className QuoQuoteEditPlugin
 * @author: hst
 * @createDate: 2022/09/08
 * @description:  报价单表单操作插件
 * @version: v1.0
 */
public class QuoQuoteEditPlugin extends AbstractBillPlugIn {

    private final static String BTN_SUBMIT = "bar_submit";
    private final static Log logger = LogFactory.getLog("QuoQuoteEditPlugin");

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Container container = this.getView().getControl("tbmain");
        container.addItemClickListener(this);
        this.addF7SelectorListener();
    }
// update by hst -2022/11/08 表单插件回写询价单异常
//    @Override
//    public void beforeDoOperation(BeforeDoOperationEventArgs args) {
//        super.beforeDoOperation(args);
//        String key = "";
//        if (args.getSource() instanceof Submit) {
//            key = ((Submit) args.getSource()).getOperateKey();
//        }
//        switch (key) {
//            case "submit" : {
//                boolean beHalf = (boolean) this.getModel().getValue(QuoQuoteHelper.FIELD_BEHALF);
//                if (beHalf) {
//                    DynamicObject quote = this.getModel().getDataEntity();
//                    DynamicObject supplier = quote.getDynamicObject("supplier");
//                    DynamicObject bizpartner = BusinessDataServiceHelper.loadSingle("bd_bizpartner", "id,number,name",
//                            new QFilter[]{new QFilter("number", QFilter.equals, supplier.get("number"))});
//                    quote.set("bizpartner", bizpartner);
//                }
//            }
//        }
//    }

//    /**
//     * 替换按钮点击事件，提交成功后再执行审核
//     * @author: hst
//     * @createDate: 2022/11/1
//     * @param afterDoOperationEventArgs
//     */
//    @Override
//    public void afterDoOperation(AfterDoOperationEventArgs afterDoOperationEventArgs) {
//        super.afterDoOperation(afterDoOperationEventArgs);
//        String key = "";
//        if (afterDoOperationEventArgs.getSource() instanceof Submit) {
//            key = ((Submit) afterDoOperationEventArgs.getSource()).getOperateKey();
//        }
//        switch (key) {
//            case "submit" : {
//                DynamicObject quote = this.getModel().getDataEntity();
//                if ((boolean) quote.get(QuoQuoteHelper.FIELD_BEHALF)) {
//                    OperationResult result = afterDoOperationEventArgs.getOperationResult();
//                    if (result.isSuccess()) {
//                        //代录的报价单自动提交并审核
//                        logger.info(quote.getString("billNo") + "提交完毕！");
//                        this.getView().invokeOperation("audit");
//                    }
//                }
//            }
//        }
//    }

    /**
     * 字段绑定数据后触发
     * @author: hst
     * @createDate: 2022/09/08
     * @param e
     */
    @Override
    public void afterBindData(EventObject e) {
        FormShowParameter showParameter = this.getView().getFormShowParameter();
        if(showParameter.getCustomParam("inquiryId")!=null
                && !((boolean) this.getModel().getValue("yd_behalf"))) {
            this.getView().setVisible(false, "bar_save");
            if (!"C".equals(this.getModel().getValue("billstatus"))) {
                this.getView().setVisible(false, "bar_audit");
            } else {
                this.getView().setVisible(true, "bar_audit");
            }
            this.getView().setEnable(true, "supplier");
            Long inquiryId = showParameter.getCustomParam("inquiryId");
            DynamicObject qutoe = this.getModel().getDataEntity();
            //初始化赋值
            new QuoQuoteHelper().initCreateData(qutoe,inquiryId,(AbstractFormDataModel)this.getModel());
            showParameter.setCustomParam("inquiryId",null);
            this.getView().updateView("pl_baseinfo");
            this.getView().updateView("pl_bizinfo");
            this.getView().updateView("materialentry");
            this.getView().updateView("titlepanelflex");
        }
        else {
            this.getView().setEnable(false, "supplier");
        }
    }

    /**
     * F7监听注册
     * @author: hst
     * @createDate: 2022/09/13
     */
    public void addF7SelectorListener() {
        BasedataEdit supplyEdit = this.getView().getControl(QuoQuoteHelper.FIELD_SUPPLIER);
        //弹窗前过滤
        supplyEdit.addBeforeF7SelectListener((evt) -> {
            ListShowParameter showParameter = (ListShowParameter) evt.getFormShowParameter();
            showParameter.setCustomParam("groupStandard", BillAssistConstant.GROUP_STANDARD_ID);
            showParameter.setCustomParam("setSupStatusShowAll", "true");
            DynamicObject newObject = this.getModel().getDataEntity(true);
//            showParameter.setCloseCallBack(new CloseCallBack(this, "supplier"));
        });
        supplyEdit.addAfterF7SelectListener((evt) -> {
            if ((boolean) this.getModel().getValue(QuoQuoteHelper.FIELD_BEHALF)) {
                String supply = this.getView().getFormShowParameter().getCustomParam("supply");
                if (supply != null) {
                    DynamicObject supplier = this.getModel().getDataEntity().getDynamicObject(QuoQuoteHelper.FIELD_SUPPLIER);
                    if (Objects.nonNull(supplier)) {
                        String supplyId = supplier.getString("id");
                        if (supply.indexOf(supplyId) == -1) {
                            this.getView().showTipNotification("该供应商非指定供应商，请重新选择！");
                            this.getModel().getDataEntity().set(QuoQuoteHelper.FIELD_SUPPLIER, null);
                            this.getView().updateView(QuoQuoteHelper.FIELD_SUPPLIER);
                        }
                    }
                }
            }
        });
    }
    
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
    	super.closedCallBack(closedCallBackEvent);
    	
    	String actionId = closedCallBackEvent.getActionId();
		Object returnData = closedCallBackEvent.getReturnData();
		
		if (actionId.equals("supplier")) {
			ListSelectedRowCollection supplierRows = (ListSelectedRowCollection) returnData;
			if (supplierRows != null) {
				this.getModel().setValue(QuoQuoteHelper.FIELD_SUPPLIER, supplierRows.getPrimaryKeyValues()[0]);
			}
		}
    }
}
