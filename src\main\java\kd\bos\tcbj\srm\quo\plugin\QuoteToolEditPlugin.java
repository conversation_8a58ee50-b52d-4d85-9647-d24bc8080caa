package kd.bos.tcbj.srm.quo.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.RowDataEntity;
import kd.bos.entity.datamodel.events.AfterAddRowEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.quo.helper.QuoQuoteHelper;
import kd.bos.tcbj.srm.sou.helper.SouInquiryHelper;

import java.util.EventObject;
import java.util.HashSet;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.quo.plugin.QuoteToolEditPlugin
 * @className QuoteToolEditPlugin
 * @author: hst
 * @createDate: 2022/10/26
 * @description:  报价助手表单操作插件
 * @version: v1.0
 */
public class QuoteToolEditPlugin extends AbstractFormPlugin {

    /**
     * 询价查询—>立即报价，生产厂家字段赋值
     * @author: hst
     * @createDate: 2022/10/26
     * @param e
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        DynamicObjectCollection details = this.getModel().getDataEntity().getDynamicObjectCollection("detail_entryentity");
        if (details.size() > 0) {
            //取出所有询价单分录id
            Set<String> ids = new HashSet<>();
            for (DynamicObject detail : details) {
                if (StringUtils.isNotBlank(detail.getString("entryid")))
                ids.add(detail.getString("entryid"));
            }
            if (ids.size() > 0) {
                assignmentField(ids,details);
            }
        }
    }

    /**
     * 报价助手—>待报价查询，生产厂家字段赋值
     * @author: hst
     * @createDate: 2022/10/26
     * @param e
     */
    @Override
    public void afterAddRow(AfterAddRowEventArgs e) {
        if ("detail_entryentity".equals(e.getEntryProp().getName())) {
            RowDataEntity[] rowDataEntities = e.getRowDataEntities();
            DynamicObjectCollection details = new DynamicObjectCollection();
            if (rowDataEntities.length > 0) {
                Set<String> ids = new HashSet<>();
                //取出所有询价单分录id
                for (RowDataEntity rowDataEntity : rowDataEntities) {
                    ids.add(rowDataEntity.getDataEntity().getString("entryid"));
                    details.add(rowDataEntity.getDataEntity());
                }
                //查询出所有询价单分录，后续为生产厂家赋值
                if (ids.size() > 0) {
                    assignmentField(ids, details);
                    this.getView().updateView("detail_entryentity");
                }
            }
        }
    }

    /**
     * 根据询价单分录id为生产厂商字段赋值
     * @author: hst
     * @createDate: 2022/10/26
     * @param ids 询价单分录id
     * @param collection 待赋值的分录
     */
    public void assignmentField (Set<String> ids, DynamicObjectCollection collection) {
        if (ids.size() > 0) {
            DataSet inquiries = QueryServiceHelper.queryDataSet(this.getClass().getName(), SouInquiryHelper.ENTITY_MAIN,
                    SouInquiryHelper.ENTITY_MATERIAL + "." + SouInquiryHelper.COLUNM_ENTRYID + "," +
                            SouInquiryHelper.ENTITY_MATERIAL + "." + SouInquiryHelper.COLUMN_PRODUCE,
                    new QFilter[]{new QFilter(SouInquiryHelper.ENTITY_MATERIAL + "." + SouInquiryHelper.COLUNM_ENTRYID, QFilter.in, ids)}, null);
            if (inquiries.hasNext()) {
                while (inquiries.hasNext()) {
                    Row inquiry = inquiries.next();
                    String entryId = inquiry.getString(SouInquiryHelper.ENTITY_MATERIAL + "." + SouInquiryHelper.COLUNM_ENTRYID);
                    String produce = inquiry.getString(SouInquiryHelper.ENTITY_MATERIAL + "." + SouInquiryHelper.COLUMN_PRODUCE);
                    for (DynamicObject dynamicObject : collection) {
                        if (StringUtils.isNotBlank(dynamicObject.getString("entryid"))
                                && StringUtils.isNotBlank(entryId) && dynamicObject.getString("entryid").equals(entryId)) {
                            dynamicObject.set("yd_produce", produce);
                        }
                    }
                }
            }
        }
    }
}
