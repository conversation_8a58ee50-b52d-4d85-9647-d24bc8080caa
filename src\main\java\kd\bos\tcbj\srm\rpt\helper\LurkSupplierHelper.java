package kd.bos.tcbj.srm.rpt.helper;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.license.api.ILicenseService;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.tcbj.im.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.rpt.helper.LurkSupplierHelper
 * @className: LurkSupplierHelper
 * @description: 潜在供应商目录报表处理类
 * @author: hst
 * @createDate: 2022/10/09
 * @version: v1.0
 */
public class LurkSupplierHelper {

    public final static String ENTITY_SUPPLIER = "srm_supplier";
    public final static String FILED_SUPNAME = "yd_supname";
    public final static String FILED_SUPID = "yd_srmsupid";
    public final static String ENTITY_SUPUSER = "pur_supuser";

    /**
     * 供应商库查询字段
     * @author: hst
     * @createDate: 2022/10/09
     * @return
     */
    public static String buildFields () {
        return "number,supplier.number,id,name,summary,areacode,entry_goods.yd_easmat.number,entry_goods.goodsdesc,yd_coreproc,yd_coremat," +
                "entry_goods.yearcapacity,yd_coretec,yd_presupcoretec,yd_matsup,yd_indrank,yd_volatility,yd_bizconditions,yd_salesmrg,yd_planmrg," +
                "yd_qcmrg,yd_purcycle,yd_prodcycle,yd_inspcycle,yd_logicycle,yd_purproportion,yd_iscustom,yd_special,yd_prdsub,yd_priceadj," +
                "yd_mattype,yd_isexclusivesup,yd_price,yd_corproducts,yd_iskeyproduct,yd_issharewithsub,yd_supnventory,yd_othercoremat,yd_conperiod," +
                "yd_purquota,yd_qfrate,yd_deliveryrate,yd_capacity,entry_goods.yd_easmat.name,entry_goods.goodsname,bizpartner.id,status";
    }

    /**
     * 供应商用户查询字段
     * @author: hst
     * @createDate: 2022/10/09
     * @return
     */
    public static String buildSupUserFields () {
        return "bizpartner.id,user.phone,user.email,user.username";
    }

    /**
     * 报表筛选条件拆分，信息提取
     * @author: hst
     * @createDate: 2022/10/09
     * @param filterInfo 筛选条件过滤信息
     * @return
     */
    public static QFilter[] bulidQfilter (FilterInfo filterInfo) {
        List<QFilter> qFilters = new ArrayList<>();
        QFilter supQfilter = bulidSupplerFilter(filterInfo);
        QFilter matQfilter = buildMaterialFilter(filterInfo);
        if (Objects.nonNull(supQfilter)) {
            qFilters.add(supQfilter);
        }
        if (Objects.nonNull(matQfilter)) {
            qFilters.add(matQfilter);
        }
        qFilters.add(new QFilter("status", "=", "C"));
        return qFilters.toArray(new QFilter[qFilters.size()]);
    }

    /**
     * 构建供应商筛选条件
     * @author: hst
     * @createDate: 2022/10/09
     * @param filterInfo 筛选条件过滤信息
     * @return
     */
    protected static QFilter bulidSupplerFilter (FilterInfo filterInfo) {
        FilterItemInfo supplier = filterInfo.getFilterItem("yd_supplier");
        QFilter qFilter = null;
        if (Objects.nonNull(supplier)) {
            DynamicObject supplierValues = (DynamicObject) supplier.getValue();
            if (Objects.nonNull(supplierValues)) {
                qFilter = new QFilter("number", QFilter.equals, supplierValues.get("number"));
            }
        }
        return qFilter;
    }

    /**
     * 构建供应商产品名称筛选条件
     * @author: hst
     * @createDate: 2022/10/09
     * @param filterInfo 筛选条件过滤信息
     * @return
     */
    public static String buildProduceFilter (FilterInfo filterInfo) {
        FilterItemInfo produce = filterInfo.getFilterItem("yd_produces");
        StringBuffer qFilter = new StringBuffer();
        if (Objects.nonNull(produce)) {
            String produceValues = produce.getString();
            if (StringUtils.isNotBlank(produceValues)) {
                //EAS物料不为空
                QFilter qFilter_notnull = new QFilter("entry_goods.yd_easmat.name", QFilter.like, "%" + produceValues + "%");
                //EAS物料为空
                QFilter qFilter_null = QFilter.isNull("entry_goods.yd_easmat.name").and(
                        new QFilter("entry_goods.goodsname", QFilter.like, "%" + produceValues + "%"));
                qFilter.append(qFilter_notnull.toString() + "or (" + qFilter_null.toString() + ")");
            }
        }
        return qFilter.toString();
    }

    /**
     * 构建EAS物料筛选条件
     * @author: hst
     * @createDate: 2022/10/09
     * @param filterInfo 筛选条件过滤信息
     * @return
     */
    protected static QFilter buildMaterialFilter (FilterInfo filterInfo) {
        FilterItemInfo material = filterInfo.getFilterItem("yd_masterial");
        QFilter qFilter = null;
        if (Objects.nonNull(material)) {
            DynamicObject materialValues = (DynamicObject) material.getValue();
            if (Objects.nonNull(materialValues)) {
                qFilter = new QFilter("entry_goods.yd_easmat.id", QFilter.equals, materialValues.getPkValue());
            }
        }
        return qFilter;
    }
}
