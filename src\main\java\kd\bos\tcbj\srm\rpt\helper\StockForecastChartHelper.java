package kd.bos.tcbj.srm.rpt.helper;

import java.math.BigDecimal;
import java.util.*;

import com.google.common.collect.Maps;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.form.chart.Axis;
import kd.bos.form.chart.AxisType;
import kd.bos.form.chart.Label;
import kd.bos.form.chart.LineSeries;
import kd.bos.form.chart.PointLineChart;
import kd.bos.form.chart.Position;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.entity.report.FilterInfo;
import kd.bos.tcbj.srm.rpt.report.data.StockForecastRptListDataPlugin;
import kd.bos.tcbj.srm.utils.RptUtil;

/**
 * @package: kd.bos.tcbj.srm.rpt.helper.StockForecastChartHelper
 * @className: StockForecastChartHelper
 * @description: 预测实际对比情况点线图
 * @author: hst
 * @createDate: 2022/11/25
 * @version: v1.0
 */
public class StockForecastChartHelper {

    /**
     * 绘制点线图
     * @author: hst
     * @createDate: 2022/11/25
     * @param pointLineChart
     */
    public void drawChart(PointLineChart pointLineChart,FilterInfo filterInfo) {
        // 标记是否是x轴
        boolean isX = true;
        // 创建分类轴，X轴方式展现
        Axis categoryAxis = this.createCategoryAxis(pointLineChart, "月份", isX);
        // 设置分类轴nametextstyle属性，
        Map<String, Object> nametextstyle = Maps.newHashMap();
        nametextstyle.put("color", "#000000");
        nametextstyle.put("fontSize", 18);
        categoryAxis.setPropValue("nameTextStyle", nametextstyle);
        // 设置分类轴名称位置属性，end表示在最后
        categoryAxis.setPropValue("nameLocation", new String("end"));
        // 设置分类轴分类值显示位置，bottom表示在下
        categoryAxis.setPropValue("position", "bottom");
        // 设置分类轴分类值liaxisLabel属性
        Map<String, Object> axislabel = Maps.newHashMap();
        Map<String, Object> textstyle = Maps.newHashMap();
        textstyle.put("color", "#000000");
        textstyle.put("fontSize", "16");
        axislabel.put("textStyle", textstyle);
        categoryAxis.setPropValue("axisLabel", axislabel);
        // 创建数据轴，name为其名字。
        Axis ValueAxis = this.createValueAxis(pointLineChart, "数量", !isX);
        // 设置数据轴的nameTextStyle属性
        Map<String, Object> yAxisnametextstyle = Maps.newHashMap();
        yAxisnametextstyle.put("color", "#000000");
        yAxisnametextstyle.put("fontSize", 18);
        // yAxisnametextstyle.put("fontStyle", "oblique");
        ValueAxis.setPropValue("nameTextStyle", yAxisnametextstyle);
        // 设置分类轴数据
        categoryAxis.setCategorys(contructCatetoryData());
        // 创建预测需求数量折线并赋值
        this.createLineSeries(pointLineChart, "预测需求数量", contructStockData(filterInfo), "red");
        // 创建实际下单数量折线并赋值
        this.createLineSeries(pointLineChart, "实际下单数量", contructOrderData(filterInfo), "green");
        // this.createLineSeries(pointLineChart,"高度", contructValue3Data(), "#282828");
        // 设置图的边距
        pointLineChart.setMargin(Position.right, "80px");
        pointLineChart.setMargin(Position.top, "80px");
        pointLineChart.setMargin(Position.left, "80px");
        // 设置图例的位置
        pointLineChart.setLegendPropValue("top", "8%");
        // 设置图例中文字的字体大小和颜色等
        Map<String, Object> legendtextstyle = Maps.newHashMap();
        legendtextstyle.put("fontSize", 18);
        legendtextstyle.put("color", "#000000");
        pointLineChart.setLegendPropValue("textStyle", legendtextstyle);
        // 刷新图标
        pointLineChart.refresh();
    }

    /**
     * 创建类目型坐标轴
     * @author: hst
     * @createDate: 2022/11/25
     * @param name 坐标轴名称
     * @param isx 是否X轴，ture创建X轴，false创建Y轴
     */
    private Axis createCategoryAxis(PointLineChart pointLineChart, String name, boolean isx) {
        Axis axis = null;
        //
        if (isx)
            axis = pointLineChart.createXAxis(name, AxisType.category);
        else
            axis = pointLineChart.createYAxis(name, AxisType.category);
        // 创建一个map存储x轴的复杂属性的属性-值对
        Map<String, Object> axisTick = Maps.newHashMap();
        axisTick.put("interval", Integer.valueOf(0));
        axisTick.put("show", true);
        axisTick.put("grid", Position.left);
        axis.setPropValue("axisTick", axisTick);
        return axis;
    }

    /**
     * 创建值类型坐标轴
     * @author: hst
     * @createDate: 2022/11/25
     * @param name 坐标轴名称
     * @param isx 是否X轴，ture创建X轴，false创建Y轴
     */
    private Axis createValueAxis(PointLineChart pointLineChart, String name, boolean isx) {
        Axis axis = null;
        if (isx)
            axis = pointLineChart.createXAxis(name, AxisType.value);
        else
            axis = pointLineChart.createYAxis(name, AxisType.value);
        // 创建一个map存储y轴的复杂属性的属性-值对
        Map<String, Object> axisTick = Maps.newHashMap();
        axisTick.put("show", true);
        axis.setPropValue("axisTick", axisTick);
        // 创建一个map存储y轴的复杂属性的属性-值对
        Map<String, Object> splitLine = Maps.newHashMap();
        Map<String, Object> lineStyle = Maps.newHashMap();
        lineStyle.put("type", "dotted");
        lineStyle.put("color", "#E2E2E2");
        splitLine.put("lineStyle", lineStyle);
        axis.setPropValue("splitLine", splitLine);
        pointLineChart.setShowTooltip(true);
        return axis;
    }

    /**
     * x轴列名
     * @author: hst
     * @createDate: 2022/11/25
     * @return
     */
    private List<String> contructCatetoryData() {
        // 此处需修改成实际分类数据，以下为案例数据
        List<String> categoryData = new ArrayList<>();
        for(int i = 1; i <= 12; i++) {
            categoryData.add(i + "月");
        }
        return categoryData;
    };

    /**
     * 需求数量
     * @author: hst
     * @createDate: 2022/11/25
     * @return
     */
    private List<BigDecimal> contructStockData(FilterInfo filterInfo) {
        // 查询需求数量
        List<QFilter> qFilters = new StockForecastRptListDataPlugin().buildStockFilter(filterInfo);
        DataSet stockSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_supstockup",
                "month(yd_bizdate) month," +
                        "yd_materialentry.yd_qty as qty", qFilters.toArray(new QFilter[qFilters.size()]), null);
        stockSet = stockSet.groupBy(new String[]{"month"}).sum("qty").finish();
        BigDecimal[] data = this.statisticsMonthlydata(stockSet);
        List<BigDecimal> valueData = new ArrayList<>();
        for (BigDecimal num : data) {
            valueData.add(num);
        }
        return valueData;
    }

    /**
     * 实际数量
     * @author: hst
     * @createDate: 2022/11/25
     * @return
     */
    private List<BigDecimal> contructOrderData(FilterInfo filterInfo) {
        // 查询实际数量
        List<QFilter> qFilters = new StockForecastRptListDataPlugin().buildPurFilter(filterInfo);
        DataSet stockSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), "pur_order",
                "month(billdate) month," +
                        "materialentry.qty as qty", qFilters.toArray(new QFilter[qFilters.size()]), null);
        stockSet = stockSet.groupBy(new String[]{"month"}).sum("qty").finish();
        BigDecimal[] data = this.statisticsMonthlydata(stockSet);
        List<BigDecimal> valueData = new ArrayList<>();
        for (BigDecimal num : data) {
            valueData.add(num);
        }
        return valueData;
    }

    /**
     * 创建折线
     * @author: hst
     * @createDate: 2022/11/25
     * @param pointLineChart
     * @param name
     * @param values
     * @param color
     */
    private void createLineSeries(PointLineChart pointLineChart, String name, List<BigDecimal> values, String color) {
        // 折线的名字
        LineSeries expireSeries = pointLineChart.createSeries(name);
        // 设置折线上文本的相关属性
        Label label = new Label();
        label.setShow(true);
        label.setColor("#000000");
        expireSeries.setLabel(label);
        // 连线颜色
        expireSeries.setItemColor(color);
        // 动画效果
        expireSeries.setAnimationDuration(2000);
        // 该点纵坐标的值setData(Number[] data)
        expireSeries.setData((Number[]) values.toArray(new Number[0]));
    }

    /**
     * 统计月度数据
     * @author: hst
     * @createDate: 2022/11/25
     * @param dataSet
     * @return
     */
    private BigDecimal[] statisticsMonthlydata (DataSet dataSet) {
        BigDecimal[] monthNum = new BigDecimal[12];
        for (int i = 0; i < monthNum.length; i++) {
            monthNum[i] = new BigDecimal("0");
        }
        if (dataSet.hasNext()) {
            for (Row row : dataSet.copy()) {
                String month = row.getString("month");
                String num = row.getString("qty");
                monthNum[Integer.parseInt(month) - 1] = monthNum[Integer.parseInt(month) - 1].add(new BigDecimal(num));
            }
        }
        return monthNum;
    }
}

