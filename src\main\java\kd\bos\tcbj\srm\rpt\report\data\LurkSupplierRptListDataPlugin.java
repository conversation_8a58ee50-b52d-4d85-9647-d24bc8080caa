package kd.bos.tcbj.srm.rpt.report.data;

import kd.bos.algo.DataSet;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.rpt.helper.LurkSupplierHelper;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.data.LurkSupplierRptListDataPlugin
 * @className: LurkSupplierRptListDataPlugin
 * @description: 潜在供应商目录报表数据过滤逻辑
 * @author: hst
 * @createDate: 2022/10/09
 * @version: v1.0
 */
public class LurkSupplierRptListDataPlugin extends AbstractReportListDataPlugin {

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        FilterInfo filterInfo = reportQueryParam.getFilter();
        QFilter[] qFilters = LurkSupplierHelper.bulidQfilter(filterInfo);
        //供应商库数据
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),LurkSupplierHelper.ENTITY_SUPPLIER,LurkSupplierHelper.buildFields(),
                qFilters,null);
        String proQfilter = LurkSupplierHelper.buildProduceFilter(filterInfo);
        //产品名称过滤
        if (StringUtils.isNotBlank(proQfilter)) {
            dataSet = dataSet.where(proQfilter);
        }
        //EAS物料为空时，产品列取产品名称字段
        String supField = LurkSupplierHelper.buildFields() + ",(case when entry_goods.yd_easmat.name is null then entry_goods.goodsname " +
                "else entry_goods.yd_easmat.name end ) as produceName";
        dataSet = dataSet.select(supField);
        //供应商用户数据（管理员）
        DataSet userSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),LurkSupplierHelper.ENTITY_SUPUSER,LurkSupplierHelper.buildSupUserFields(),
                new QFilter[]{new QFilter("isadmin",QFilter.equals,true)},null);
        //关联
        dataSet = dataSet.leftJoin(userSet).on("bizpartner.id","bizpartner.id").
                select(supField.split(","),"user.phone,user.email,user.username".split(",")).finish();
        return dataSet;
    }
}
