package kd.bos.tcbj.srm.rpt.report.data;

import kd.bos.algo.DataSet;
import kd.bos.algo.DataType;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.RptUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.data.MatUseTrendRptListDataPlugin
 * @className: MatUseTrendRptListDataPlugin
 * @description: 潜在供应商目录报表数据过滤逻辑
 * @author: hst
 * @createDate: 2022/10/09
 * @version: v1.0
 */
public class MatUseTrendRptListDataPlugin extends AbstractReportListDataPlugin {

    private static String REQOUT_ENTITY = "im_materialreqoutbill";
    private static String PURIN_ENTITY = "im_purinbill";
    private String[] REQOUT_FIELD = { "yd_material","yd_matnum","yd_matname","yd_picking_1", "yd_picking_2",
            "yd_picking_3", "yd_picking_4", "yd_picking_5","yd_picking_6","yd_picking_7","yd_picking_8","yd_picking_9",
            "yd_picking_10","yd_picking_11","yd_picking_12"};
    private String[] REQSUP_FIELD = { "yd_material","yd_matnum","yd_matname","yd_supplier","yd_picking_1", "yd_picking_2",
            "yd_picking_3", "yd_picking_4", "yd_picking_5","yd_picking_6","yd_picking_7","yd_picking_8","yd_picking_9",
            "yd_picking_10","yd_picking_11","yd_picking_12"};
    private DataType[] DATATYPES_REQ = { DataType.StringType,DataType.StringType,DataType.StringType, DataType.DoubleType,
            DataType.DoubleType, DataType.DoubleType, DataType.DoubleType, DataType.DoubleType,DataType.DoubleType, DataType.DoubleType,
            DataType.DoubleType,DataType.DoubleType,DataType.DoubleType,DataType.DoubleType, DataType.DoubleType };
    private DataType[] DATATYPES_SUP = { DataType.StringType,DataType.StringType,DataType.StringType,DataType.StringType,
            DataType.DoubleType, DataType.DoubleType, DataType.DoubleType, DataType.DoubleType, DataType.DoubleType,DataType.DoubleType,
            DataType.DoubleType, DataType.DoubleType,DataType.DoubleType,DataType.DoubleType,DataType.DoubleType, DataType.DoubleType };

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        String sortInfo = reportQueryParam.getSortInfo();
        List<QFilter> qFilters = buildReqOutFilter(reportQueryParam.getFilter());
        boolean isSupplier = reportQueryParam.getFilter().getFilterItem("yd_issupplier").getBoolean();
        DataSet reqOutSet;
        //按供应商+物料维度统计领料出库单的数量形成物料使用趋势
        if (isSupplier) {
            //物料查找领料出库单的数量和批次
            reqOutSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), REQOUT_ENTITY,
                    "billentry.material.masterid.id as yd_material,billentry.qty as qty, billentry.lotnumber as lotnumber," +
                            "billentry.material.masterid.number as yd_matnum,billentry.materialname as yd_matname," +
                            "concat('yd_picking_',month(biztime)) months", qFilters.toArray(new QFilter[qFilters.size()]),null);
            //根据物料+批次查找采购入库单对应的供应商
            DataSet purSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), PURIN_ENTITY,
                    "billentry.material.masterid.id as pur_material,billentry.yd_ph as put_lotnumber, " +
                            "supplier.name as yd_supplier", new QFilter[]{new QFilter("billstatus", QFilter.equals, "C")},null);
            purSet = purSet.distinct();
            reqOutSet = reqOutSet.leftJoin(purSet).on("yd_material","pur_material").on("lotnumber","put_lotnumber").
                    select(new String[]{"yd_material","yd_matnum","yd_matname","months","qty"},new String[]{"yd_supplier"}).finish();
            //过滤掉根据物料+批次查询不到供应商的数据
            reqOutSet = reqOutSet.where("yd_supplier is not null");
            reqOutSet = reqOutSet.groupBy(new String[]{"yd_material","yd_matnum","yd_matname","yd_supplier","months"}).sum("qty").finish();
            reqOutSet = RptUtil.columnDataToRowData(reqOutSet, "months", "qty", REQSUP_FIELD, DATATYPES_SUP,
                    new String[]{"yd_material", "yd_matnum", "yd_matname", "yd_supplier"});
        } else {
            //按照物料维度统计领料出库单的物料使用趋势
            reqOutSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), REQOUT_ENTITY,
                    "concat('yd_picking_',month(biztime)) months,billentry.material.masterid.id as yd_material," +
                            "billentry.material.masterid.number as yd_matnum,billentry.materialname as yd_matname,billentry.qty as qty",
                    qFilters.toArray(new QFilter[qFilters.size()]), null);
            reqOutSet = reqOutSet.groupBy(new String[]{"yd_material", "yd_matnum", "yd_matname", "months"}).sum("qty").finish();
            reqOutSet = RptUtil.columnDataToRowData(reqOutSet, "months", "qty", REQOUT_FIELD, DATATYPES_REQ,
                    new String[]{"yd_material", "yd_matnum", "yd_matname"});
        }
        //是否进行排序
        if (sortInfo != null) {
            reqOutSet = reqOutSet.orderBy(new String[]{sortInfo});
        }
        //表头过滤
        if (!"".equals(RptUtil.buildHeadFilter(reportQueryParam.getFilter()))) {
            reqOutSet = reqOutSet.where(RptUtil.buildHeadFilter(reportQueryParam.getFilter()));
        }
        return reqOutSet;
    }

    /**
     * 构造领料出库单过滤条件(筛选条件)
     * @author: hst
     * @createDate: 2022/10/24
     * @param filterInfo
     * @return
     */
    public List<QFilter> buildReqOutFilter (FilterInfo filterInfo) {
        //筛选条件
        List<QFilter> qFilters = new ArrayList<>();
        QFilter orgFilter = RptUtil.buildF7Filter(filterInfo,"yd_org","bizorg.number",QFilter.equals);
        QFilter dateFilter = RptUtil.buildDateFilter(filterInfo,"yyyy","yd_year",
                "year(biztime)",QFilter.equals);
        QFilter matFilter = RptUtil.buildF7Filter(filterInfo,"yd_materials","billentry.material.masterid.number",QFilter.equals);
        if (Objects.nonNull(orgFilter)) {
            qFilters.add(orgFilter);
        }
        if (Objects.nonNull(dateFilter)) {
            qFilters.add(dateFilter);
        }
        if (Objects.nonNull(matFilter)) {
            qFilters.add(matFilter);
        }
        qFilters.add(new QFilter("billentry.material", QFilter.is_notnull, null));
        qFilters.add(new QFilter("billentry.lotnumber", QFilter.not_equals, ""));
        qFilters.add(new QFilter("billstatus", QFilter.equals, "C"));
        return qFilters;
    }
}
