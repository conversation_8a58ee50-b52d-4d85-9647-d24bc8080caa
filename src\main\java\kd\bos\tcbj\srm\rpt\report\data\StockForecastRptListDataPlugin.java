package kd.bos.tcbj.srm.rpt.report.data;

import kd.bos.algo.*;
import kd.bos.dataentity.utils.ArrayUtils;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.RptUtil;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.data.StockForecastRptListDataPlugin
 * @className: StockForecastRptListDataPlugin
 * @description: 预测实际对比情况表取数逻辑
 * @author: hst
 * @createDate: 2022/10/21
 * @version: v1.0
 */
public class StockForecastRptListDataPlugin extends AbstractReportListDataPlugin {

    private static String STOCK_ENTITY = "yd_supstockup";
    private static String PUR_ENTITY = "pur_order";
    // update by hst 2022/11/25 增加供应商列
    private String[] STOCK_FIELD = { "yd_material","yd_matnum","yd_matname","yd_supplier","yd_stock_1", "yd_stock_2",
            "yd_stock_3", "yd_stock_4", "yd_stock_5","yd_stock_6","yd_stock_7","yd_stock_8","yd_stock_9",
            "yd_stock_10","yd_stock_11","yd_stock_12"};
    // update by hst 2022/11/25 增加供应商列
    private String[] PUR_FILED = { "yd_material_pur","yd_matnum_pur","yd_matname_pur","yd_supplier_pur","yd_pur_1", "yd_pur_2",
            "yd_pur_3", "yd_pur_4", "yd_pur_5","yd_pur_6","yd_pur_7","yd_pur_8","yd_pur_9","yd_pur_10","yd_pur_11","yd_pur_12"};
    private String[] FIT_FILED = { "yd_pur_1/yd_stock_1*100 yd_fit_1", "yd_pur_2/yd_stock_2*100 yd_fit_2",
            "yd_pur_3/yd_stock_3*100 yd_fit_3","yd_pur_4/yd_stock_4*100 yd_fit_4", "yd_pur_5/yd_stock_5*100 yd_fit_5",
            "yd_pur_6/yd_stock_6*100 yd_fit_6","yd_pur_7/yd_stock_7*100 yd_fit_7","yd_pur_8/yd_stock_8*100 yd_fit_8",
            "yd_pur_9/yd_stock_9*100 yd_fit_9","yd_pur_10/yd_stock_10*100 yd_fit_10","yd_pur_11/yd_stock_11*100 yd_fit_11",
            "yd_pur_12/yd_stock_12*100 yd_fit_12"};
    // update by hst 2022/11/25 增加供应商列
    private DataType[] DATATYPES = { DataType.StringType,DataType.StringType,DataType.StringType,DataType.StringType,DataType.DoubleType, DataType.DoubleType, DataType.DoubleType, DataType.DoubleType,
            DataType.DoubleType,DataType.DoubleType,DataType.DoubleType,DataType.DoubleType,DataType.DoubleType,DataType.DoubleType,DataType.DoubleType,
            DataType.DoubleType };

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        String sortInfo = reportQueryParam.getSortInfo();
        //备货单过滤条件
        List<QFilter> stoQFilters = buildStockFilter(reportQueryParam.getFilter());
        //采购单过滤条件
        List<QFilter> purQFilters = buildPurFilter(reportQueryParam.getFilter());
        //统计备货物料
        // update by hst 2022/11/25 分组增加供应商
        DataSet stockSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), STOCK_ENTITY,
                "concat('yd_stock_',month(yd_bizdate)) months,yd_materialentry.yd_material.id as yd_material, " +
                        "yd_materialentry.yd_material.number as yd_matnum,yd_materialentry.yd_material.name as yd_matname," +
                        "yd_materialentry.yd_qty as qty,yd_supplier.name as yd_supplier", stoQFilters.toArray(new QFilter[stoQFilters.size()]), null);
        stockSet = stockSet.groupBy(new String[]{"yd_material","yd_matnum","yd_matname","months","yd_supplier"}).sum("qty").finish();
        stockSet = RptUtil.columnDataToRowData(stockSet, "months", "qty", STOCK_FIELD,DATATYPES,
                new String[]{"yd_material","yd_matnum","yd_matname","yd_supplier"});
        //统计采购物料
        DataSet purSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), PUR_ENTITY,
                "concat('yd_pur_',month(billdate)) months, materialentry.material.id as yd_material_pur," +
                        "materialentry.material.number as yd_matnum_pur, materialentry.material.name as yd_matname_pur," +
                        "materialentry.qty as qty,supplier.name as yd_supplier_pur",purQFilters.toArray(new QFilter[purQFilters.size()]), null);
        purSet = purSet.groupBy(new String[]{"yd_material_pur","yd_matnum_pur","yd_matname_pur","months","yd_supplier_pur"}).sum("qty").finish();
        purSet = RptUtil.columnDataToRowData(purSet, "months", "qty", PUR_FILED,DATATYPES,
                new String[]{"yd_material_pur","yd_matnum_pur","yd_matname_pur","yd_supplier_pur"});
        //计算匹配率
        String[] FIELD = ArrayUtils.addAll(STOCK_FIELD, PUR_FILED);
        FIELD = ArrayUtils.addAll(FIELD, FIT_FILED);
        stockSet = stockSet.leftJoin(purSet).on("yd_material", "yd_material_pur").select(FIELD).finish();
        //是否进行排序
        if (sortInfo != null) {
            stockSet = stockSet.orderBy(new String[]{sortInfo});
        }
        //表头过滤
        if (!"".equals(RptUtil.buildHeadFilter(reportQueryParam.getFilter()))) {
            stockSet = stockSet.where(RptUtil.buildHeadFilter(reportQueryParam.getFilter()));
        }
        return stockSet;
    }

    /**
     * 构造备货单过滤条件(筛选条件)
     * @author: hst
     * @createDate: 2022/10/24
     * @param filterInfo
     * @return
     */
    public List<QFilter> buildStockFilter (FilterInfo filterInfo) {
        //筛选条件
        List<QFilter> qFilters = new ArrayList<>();
        QFilter orgFilter = RptUtil.buildF7Filter(filterInfo,"yd_org","org.number",QFilter.equals);
        QFilter dateFilter = RptUtil.buildDateFilter(filterInfo,"yyyy","yd_year",
                "year(yd_bizdate)",QFilter.equals);
        QFilter periodFilter = RptUtil.buildF7Filter(filterInfo,"yd_matneed","yd_needperiod.number",QFilter.equals);
        QFilter supplier = RptUtil.buildF7Filter(filterInfo,"yd_supply","yd_supplier.number",QFilter.equals);
        QFilter material = RptUtil.buildF7Filter(filterInfo,"yd_materials","yd_materialentry.yd_material.number",QFilter.equals);
        if (Objects.nonNull(orgFilter)) {
            qFilters.add(orgFilter);
        }
        if (Objects.nonNull(dateFilter)) {
            qFilters.add(dateFilter);
        } else if (Objects.isNull(dateFilter)) {
            qFilters.add(new QFilter("year(yd_bizdate)",QFilter.equals, DateUtil.date2str(new Date(),"yyyy")));
        }
        if (Objects.nonNull(periodFilter)) {
            qFilters.add(periodFilter);
        }
        if (Objects.nonNull(supplier)) {
            qFilters.add(supplier);
        }
        if (Objects.nonNull(material)) {
            qFilters.add(material);
        }
        qFilters.add(new QFilter(" yd_materialentry.yd_material", QFilter.is_notnull, null));
        return qFilters;
    }

    /**
     * 构造采购单过滤条件(筛选条件)
     * @author: hst
     * @createDate: 2022/10/24
     * @param filterInfo
     * @return
     */
    public List<QFilter> buildPurFilter (FilterInfo filterInfo) {
        //筛选条件
        List<QFilter> qFilters = new ArrayList<>();
        QFilter orgFilter = RptUtil.buildF7Filter(filterInfo,"yd_org","org.number",QFilter.equals);
        QFilter dateFilter = RptUtil.buildDateFilter(filterInfo,"yyyy","yd_year",
                "year(billdate)",QFilter.equals);
        QFilter supplier = RptUtil.buildF7Filter(filterInfo,"yd_supply","supplier.number",QFilter.equals);
        QFilter material = RptUtil.buildF7Filter(filterInfo,"yd_materials","materialentry.material.number",QFilter.equals);
        if (Objects.nonNull(orgFilter)) {
            qFilters.add(orgFilter);
        }
        if (Objects.nonNull(dateFilter)) {
            qFilters.add(dateFilter);
        } else if (Objects.isNull(dateFilter)) {
            qFilters.add(new QFilter("year(billdate)",QFilter.equals, DateUtil.date2str(new Date(),"yyyy")));
        }
        if (Objects.nonNull(supplier)) {
            qFilters.add(supplier);
        }
        if (Objects.nonNull(material)) {
            qFilters.add(material);
        }
        qFilters.add(new QFilter("materialentry.material", QFilter.not_equals, "0"));
        qFilters.add(new QFilter("billstatus", QFilter.equals, "C"));
        return qFilters;
    }
}
