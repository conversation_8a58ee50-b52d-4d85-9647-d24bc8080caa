package kd.bos.tcbj.srm.rpt.report.data;

import kd.bos.algo.*;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.utils.RptUtil;
import org.apache.poi.util.StringUtil;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.data.SupStockUpRptListDataPlugin
 * @className: SupStockUpRptListDataPlugin
 * @description: 备货满足情况报表取数逻辑
 * @author: hst
 * @createDate: 2022/10/21
 * @version: v1.0
 */
public class SupStockUpRptListDataPlugin extends AbstractReportListDataPlugin {

    private static String ENTITYNAME = "yd_supstockup";
    private static String[] FIELDS = {"org as yd_org",
                                        "yd_materialentry.yd_material as yd_material",
                                        "yd_materialentry.yd_material.number as yd_matnumber",
                                        "yd_materialentry.yd_material.name as yd_matname",
                                        "yd_materialentry.yd_material.modelnum as yd_matmodel",
                                        "yd_supplier.name as yd_supply",
                                        "yd_materialentry.yd_qty as yd_neednum",
                                        "yd_materialentry.yd_supcomfirmqty as yd_comfirenum",
                                        "abs(yd_materialentry.yd_supcomfirmqty - yd_materialentry.yd_qty) as yd_variancenum",
                                        "yd_materialentry.yd_needdate as yd_needdate",
                                        "yd_materialentry.yd_supcomfirmdate as yd_supcomdate",
                                        "datediff(yd_materialentry.yd_needdate,yd_materialentry.yd_supcomfirmdate)/24/60/60 as yd_varianceday",
                                        "yd_materialentry.yd_supremark as yd_remark"};

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        FilterInfo filterInfo = reportQueryParam.getFilter();
        QFilter[] qFilters = buildFilter(filterInfo);
        String fields = StringUtil.join(FIELDS,",");
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),ENTITYNAME,fields,qFilters,null);
        //是否过滤出差异数据
        FilterItemInfo box = filterInfo.getFilterItem("yd_iserror");
        if (Objects.nonNull(box)) {
            boolean isError = box.getBoolean();
            if (isError) {
                dataSet = dataSet.where("yd_variancenum > 0 or yd_varianceday > 0");
            }
        }
        return dataSet;
    }

    /**
     * 构造过滤条件
     * @author: hst
     * @createDate: 2022/10/21
     * @param filterInfo
     * @return
     */
    public QFilter[] buildFilter (FilterInfo filterInfo) {
        List<QFilter> qFilters = new ArrayList<>();
        QFilter orgFilter = RptUtil.buildF7Filter(filterInfo,"yd_org","org.number",QFilter.equals);
        QFilter supFilter = RptUtil.buildF7Filter(filterInfo,"yd_supplier","yd_supplier.number",QFilter.equals);
        QFilter periodFilter = RptUtil.buildF7Filter(filterInfo,"yd_needperiod","yd_needperiod",QFilter.equals);
        QFilter beginFilter = RptUtil.buildDateFilter(filterInfo,"yyyy-MM-dd HH:mm:ss",
                "yd_begindate","yd_bizdate",QFilter.large_equals);
        QFilter endFilter = RptUtil.buildDateFilter(filterInfo, "yyyy-MM-dd HH:mm:ss",
                "yd_enddate","yd_bizdate",QFilter.less_equals);
        QFilter matFilter = RptUtil.buildF7Filter(filterInfo,"yd_materials","yd_materialentry.yd_material.number",
                QFilter.equals);
        QFilter unComFilter = RptUtil.buildBoxFilter(filterInfo,"yd_uncomfirm","billstatus",QFilter.equals,"B");
        if (Objects.nonNull(orgFilter)) {
            qFilters.add(orgFilter);
        }
        if (Objects.nonNull(supFilter)) {
            qFilters.add(supFilter);
        }
        if (Objects.nonNull(periodFilter)) {
            qFilters.add(periodFilter);
        }
        if (Objects.nonNull(beginFilter)) {
            qFilters.add(beginFilter);
        }
        if (Objects.nonNull(endFilter)) {
            qFilters.add(endFilter);
        }
        if (Objects.nonNull(matFilter)) {
            qFilters.add(matFilter);
        }
        if (Objects.nonNull(unComFilter)) {
            qFilters.add(unComFilter);
        }
        return qFilters.toArray(new QFilter[qFilters.size()]);
    }
}
