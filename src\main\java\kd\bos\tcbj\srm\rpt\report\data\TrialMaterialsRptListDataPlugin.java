package kd.bos.tcbj.srm.rpt.report.data;

import kd.bos.algo.DataSet;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.utils.RptUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.data.TrialMaterialsRptListDataPlugin
 * @className: TrialMaterialsRptListDataPlugin
 * @description: 试产物料申请报表查询逻辑插件
 * @author: hst
 * @createDate: 2022/12/6
 * @version: v1.0
 */
public class TrialMaterialsRptListDataPlugin extends AbstractReportListDataPlugin {

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        String sortInfo = reportQueryParam.getSortInfo();
        FilterInfo filterInfo = reportQueryParam.getFilter();
        QFilter[] qFilters = buildFilter(filterInfo);
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),"yd_trialpurreqbill",
                "id yd_billid,billno yd_billno,yd_applydate yd_applydate,entryentity.yd_entrymaterial.number yd_material," +
                        "entryentity.yd_entrymaterial.name yd_matname,entryentity.yd_materialunit yd_unit,entryentity.yd_demandqty yd_qty," +
                        "entryentity.yd_price yd_price,entryentity.yd_amount yd_amount,entryentity.yd_materiallot yd_materiallot,yd_supplier.name yd_supname",
                qFilters,null);
        //是否进行排序
        if (sortInfo != null) {
            dataSet = dataSet.orderBy(new String[]{sortInfo});
        }
        //表头过滤
        if (!"".equals(RptUtil.buildHeadFilter(reportQueryParam.getFilter()))) {
            dataSet = dataSet.where(RptUtil.buildHeadFilter(reportQueryParam.getFilter()));
        }
        return dataSet;
    }

    /**
     * 构造筛选条件
     * @author: hst
     * @createDate: 2022/12/06
     * @param filterInfo
     * @return
     */
    private QFilter[] buildFilter(FilterInfo filterInfo) {
        List<QFilter> qFilters = new ArrayList<>();
        QFilter dateFilter = RptUtil.buildDateFilter(filterInfo,"yyyy-MM-dd HH:mm:ss",
                "yd_date","yd_applydate",QFilter.equals);
        QFilter supFilter = RptUtil.buildF7Filter(filterInfo,"yd_supplier","yd_supplier.supplier.number",QFilter.equals);
        if (Objects.nonNull(dateFilter)) {
            qFilters.add(dateFilter);
        }
        if (Objects.nonNull(supFilter)) {
            qFilters.add(supFilter);
        }
        return qFilters.toArray(new QFilter[qFilters.size()]);
    }
}
