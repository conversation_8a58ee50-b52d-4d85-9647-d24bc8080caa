package kd.bos.tcbj.srm.rpt.report.data;

import kd.bos.algo.*;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.utils.RptUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.data.VarianceAnalysisRptListDataPlugin
 * @className: VarianceAnalysisRptListDataPlugin
 * @description: 到货时间差异分析表数据过滤逻辑
 * @author: hst
 * @createDate: 2022/12/08
 * @version: v1.0
 */
public class VarianceAnalysisRptListDataPlugin extends AbstractReportListDataPlugin {

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        String sortInfo = reportQueryParam.getSortInfo();
        FilterInfo filterInfo = reportQueryParam.getFilter();
        QFilter[] qFilters = buildFilter(filterInfo);
        // 采购订单信息
        DataSet purSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),"scp_order",
                "billno,materialentry.material.number yd_material,person yd_purchaser",qFilters,null);
        // 预约发货台账信息
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),"yd_pur_deliverstand",
                "yd_materialentry.yd_material.number yd_matnum,yd_materialentry.yd_material.name yd_matname," +
                        "yd_materialentry.yd_qty yd_planarrivalqty,yd_arrivaldate yd_plannarrivaldate,yd_materialentry.yd_orderno yd_orderno," +
                        "yd_materialentry.yd_unit yd_unit,yd_materialentry.yd_actqty yd_actarrivalqtys," +
                        "yd_actualdate yd_actarrivaldate,yd_isaccdate,yd_materialentry.yd_isaccqty yd_isaccqty",
                qFilters,null);
        dataSet = dataSet.orderBy(new String[]{"yd_matnum desc"});
        // 物料分组统计总送货次数
        DataSet total = dataSet.copy().groupBy(new String[]{"yd_matnum"}).count("yd_totalnum").finish();
        // 统计到货数量准确次数
        DataSet accQty = dataSet.copy().where("yd_isaccqty = true").groupBy(new String[]{"yd_matnum"}).count("yd_accqtynum").finish();
        // 统计到货时间准确次数
        DataSet accDate = dataSet.copy().where("yd_isaccdate = true").groupBy(new String[]{"yd_matnum"}).count("yd_accdatenum").finish();
        // 次数汇总
        total = total.leftJoin(accQty).on("yd_matnum","yd_matnum").select(new String[]{"yd_matnum","yd_accqtynum","yd_totalnum"}).finish();
        total = total.leftJoin(accDate).on("yd_matnum","yd_matnum").select(new String[]{"yd_matnum",
                "CAST(yd_accqtynum AS decimal(10,2)) yd_accqtynum","CAST(yd_accdatenum AS decimal(10,2)) yd_accdatenum",
                "CAST(yd_totalnum AS decimal(10,2)) yd_totalnum"}).finish();
        // 关联主表
        dataSet = dataSet.join(total).on("yd_matnum","yd_matnum").select(new String[]{"yd_matnum","yd_matname","yd_planarrivalqty",
                "yd_plannarrivaldate","yd_unit","yd_actarrivalqtys","yd_actarrivaldate","yd_totalnum","yd_accqtynum","yd_accdatenum",
                "case when yd_accqtynum is null then 0 else yd_accqtynum/yd_totalnum*100 end yd_arrivalaccuracy",
                "case when yd_accdatenum is null then 0 else yd_accdatenum/yd_totalnum*100 end yd_variancedays","yd_orderno"}).finish();
        // 关联采购订单
        dataSet = dataSet.join(purSet).on("yd_orderno","billno").select(new String[]{"yd_matnum","yd_matname","yd_planarrivalqty",
                "yd_plannarrivaldate","yd_unit","yd_actarrivalqtys","yd_actarrivaldate","yd_totalnum","yd_accqtynum","yd_accdatenum",
                "yd_arrivalaccuracy", "yd_variancedays","yd_orderno"}).finish();
        return dataSet;
    }

    /**
     * 构造筛选条件
     * @author: hst
     * @createDate: 2022/12/08
     * @param filterInfo
     * @return
     */
    private QFilter[] buildFilter(FilterInfo filterInfo) {
        List<QFilter> qFilters = new ArrayList<>();
        QFilter matFilter = RptUtil.buildF7Filter(filterInfo,"yd_materiel","yd_materialentry.yd_material.number",QFilter.equals);
        if (Objects.nonNull(matFilter)) {
            qFilters.add(matFilter);
        }
        return qFilters.toArray(new QFilter[qFilters.size()]);
    }
}
