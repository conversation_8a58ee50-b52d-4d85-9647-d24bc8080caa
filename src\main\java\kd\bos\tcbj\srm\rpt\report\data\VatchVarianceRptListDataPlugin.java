package kd.bos.tcbj.srm.rpt.report.data;

import kd.bos.algo.DataSet;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.utils.RptUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.data.VatchVarianceRptListDataPlugin
 * @className: VatchVarianceRptListDataPlugin
 * @description: 批次差异分析表数据过滤逻辑
 * @author: hst
 * @createDate: 2022/11/16
 * @version: v1.0
 */
public class VatchVarianceRptListDataPlugin extends AbstractReportListDataPlugin {

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        FilterInfo filterInfo = reportQueryParam.getFilter();
        List<QFilter> outFilter = buildOutFilter(filterInfo);
        // 查询销售发货单
        DataSet out = QueryServiceHelper.queryDataSet(this.getClass().getName(),"scp_saloutstock",
                "supplier.name as yd_suppliername,supplier.number as yd_suppliernumber,materialentry.yd_producers " +
                        "as yd_producorname,materialentry.material.number as yd_materialno,materialentry.material.name " +
                        "as yd_materialname,materialentry.material.modelnum as yd_modelnum,billno as yd_outno,materialentry.seq " +
                        "as yd_outseq,materialentry.suplot as yd_outbatchno,supplier.id as yd_supplierid,materialentry as yd_id",
                outFilter.toArray(new QFilter[outFilter.size()]),null);
        out = out.select("yd_suppliername","yd_suppliernumber","yd_producorname","yd_materialno","yd_materialname","yd_modelnum",
                "yd_outno","yd_outseq","yd_outbatchno","LEFT (yd_id,19) yd_id","yd_supplierid");
        // 查询采购收货单
        DataSet in = QueryServiceHelper.queryDataSet(this.getClass().getName(),"pur_receipt","materialentry.material.number " +
                        "as yd_inmaterialno,billno as yd_inno,materialentry.seq as yd_inseq,materialentry.suplot as yd_inbatchno," +
                        "materialentry.yd_saloutbillno as saloutnum,materialentry.yd_saledeliveryenid as yd_saloutid,materialentry.yd_errlot as yd_iserrlot",
                outFilter.toArray(new QFilter[outFilter.size()]),null);
        //关联（出库单号->收货单号，分录ID）
        out = out.join(in).on("yd_outno","saloutnum").on("yd_id","yd_saloutid").select("yd_suppliername","yd_suppliernumber",
                "yd_producorname","yd_materialno", "yd_materialname","yd_modelnum","yd_outno","yd_outseq","yd_outbatchno","yd_inno","yd_inseq",
                "yd_inbatchno","yd_supplierid","yd_id","yd_saloutid","yd_iserrlot").finish();
        //是否过滤出批次不一致
        FilterItemInfo isFit = filterInfo.getFilterItem("yd_isnotfit");
        if (Objects.nonNull(isFit) && (boolean) isFit.getValue()) {
            out = out.where("yd_inbatchno <> yd_outbatchno");
        }
        return out;
    }

    /**
     * 筛选条件构造过滤条件
     * @author: hst
     * @createDate: 2022/11/16
     * @param filterInfo
     * @return
     */
    public List<QFilter> buildOutFilter (FilterInfo filterInfo) {
        //筛选条件
        List<QFilter> qFilters = new ArrayList<>();
        QFilter supFilter = RptUtil.buildF7Filter(filterInfo,"yd_supplier","supplier.number",QFilter.equals);
        QFilter materialFilter = RptUtil.buildF7Filter(filterInfo,"yd_materiel","materialentry.material.number",QFilter.equals);
        QFilter beginFilter = RptUtil.buildDateFilter(filterInfo,"yyyy-MM-dd","yd_date_startdate",
                "billdate",QFilter.large_equals);
        QFilter endFilter = RptUtil.buildDateFilter(filterInfo,"yyyy-MM-dd","yd_date_enddate",
                "billdate",QFilter.less_equals);
        if (Objects.nonNull(supFilter)) {
            qFilters.add(supFilter);
        }
        if (Objects.nonNull(materialFilter)) {
            qFilters.add(materialFilter);
        }
        if (Objects.nonNull(beginFilter)) {
            qFilters.add(beginFilter);
        }
        if (Objects.nonNull(endFilter)) {
            qFilters.add(endFilter);
        }
        return qFilters;
    }
}
