package kd.bos.tcbj.srm.rpt.report.form;

import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.ShowType;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.report.ReportList;
import kd.bos.report.plugin.AbstractReportFormPlugin;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.rpt.helper.LurkSupplierHelper;

import java.util.EventObject;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.form.LurkSupplierRptFormPlugin
 * @className: LurkSupplierRptFormPlugin
 * @description: 潜在供应商目录报表界面类
 * @author: hst
 * @createDate: 2022/10/09
 * @version: v1.0
 */
public class LurkSupplierRptFormPlugin extends AbstractReportFormPlugin implements HyperLinkClickListener {
    /**
     * 注册监听
     * <AUTHOR>
     * @createDate: 2022/08/22
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        ReportList reportList = getControl("reportlistap");
        reportList.addHyperClickListener(this);
    }

    /**
     * 监听用户点击列表上的超链接，跳转对应供应商库页面
     * <AUTHOR>
     * @createDate: 2022/10/09
     * @param hyperLinkClickEvent
     */
    @Override
    public void hyperLinkClick(HyperLinkClickEvent hyperLinkClickEvent) {
        String key = hyperLinkClickEvent.getFieldName();
        switch (key) {
            case LurkSupplierHelper.FILED_SUPNAME : {
                this.forwardToSrmSupplier(hyperLinkClickEvent);
                break;
            }
        }
    }

    /**
     * 跳转对应供应商库页面
     * @author: hst
     * @createDate: 2022/10/09
     * @param hyperLinkClickEvent
     */
    public void forwardToSrmSupplier(HyperLinkClickEvent hyperLinkClickEvent) {
        int index = hyperLinkClickEvent.getRowIndex();
        DynamicObject data = ((ReportList)this.getControl("reportlistap")).getReportModel().getRowData(index);
        String supplierId = data.getString(LurkSupplierHelper.FILED_SUPID);
        if (StringUtils.isNotBlank(supplierId)) {
            BillShowParameter billShowParameter = new BillShowParameter();
            billShowParameter.setFormId("srm_supplier");
            billShowParameter.setPkId(supplierId);
            billShowParameter.setCaption(String.format("供应商"));
            billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
            this.getView().showForm(billShowParameter);
        } else {
            this.getView().showErrorNotification("系统异常，请联系管理员！");
        }
    }
}
