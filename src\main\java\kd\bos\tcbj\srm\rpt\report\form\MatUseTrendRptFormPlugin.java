package kd.bos.tcbj.srm.rpt.report.form;

import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.report.AbstractReportColumn;
import kd.bos.entity.report.ReportColumn;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.form.ShowType;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.report.ReportList;
import kd.bos.report.events.CreateColumnEvent;
import kd.bos.report.events.SortAndFilterEvent;
import kd.bos.report.plugin.AbstractReportFormPlugin;

import java.util.EventObject;
import java.util.List;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.form.MatUseTrendRptFormPlugin
 * @className: MatUseTrendRptFormPlugin
 * @description: 物料使用趋势报表界面插件
 * @author: hst
 * @createDate: 2022/10/24
 * @version: v1.0
 */
public class MatUseTrendRptFormPlugin extends AbstractReportFormPlugin implements HyperLinkClickListener {

    @Override
    public void setSortAndFilter(List<SortAndFilterEvent> allColumns) {
        super.setSortAndFilter(allColumns);
        for (SortAndFilterEvent sortAndFilterEvent : allColumns) {
            if (sortAndFilterEvent.getColumnName().equals("yd_matnum")
                    || sortAndFilterEvent.getColumnName().equals("yd_matname")) {
                sortAndFilterEvent.setSort(true);
                sortAndFilterEvent.setFilter(true);
            }
        }
    }

    /**
     * 注册监听
     * <AUTHOR>
     * @createDate: 2022/10/24
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        ReportList reportList = getControl("reportlistap");
        reportList.addHyperClickListener(this);
    }

    /**
     * 监听用户点击列表上的超链接，跳转对应供应商库页面
     * <AUTHOR>
     * @createDate: 2022/10/24
     * @param hyperLinkClickEvent
     */
    @Override
    public void hyperLinkClick(HyperLinkClickEvent hyperLinkClickEvent) {
        String key = hyperLinkClickEvent.getFieldName();
        switch (key) {
            case "yd_matnum" : {
                this.forwardToMaterialPage(hyperLinkClickEvent);
                break;
            }
        }
    }

    /**
     * 控制是否显示供应商字段
     * @author: hst
     * @createDate: 2022/10/25
     * @param event
     */
    @Override
    public void afterCreateColumn(CreateColumnEvent event) {
        super.afterCreateColumn(event);
        ReportQueryParam reportQueryParam = getQueryParam();
        boolean isSupplier = reportQueryParam.getFilter().getFilterItem("yd_issupplier").getBoolean();
        if (!isSupplier) {
            List<AbstractReportColumn> columns = event.getColumns();
            for (AbstractReportColumn column : columns) {
                if ("yd_supplier".equals(((ReportColumn) column).getFieldKey())) {
                    ((ReportColumn) column).setHide(true);
                }
            }
        }
    }



    /**
     * 跳转对应物料页面
     * @author: hst
     * @createDate: 2022/10/24
     * @param hyperLinkClickEvent
     */
    public void forwardToMaterialPage(HyperLinkClickEvent hyperLinkClickEvent) {
        int index = hyperLinkClickEvent.getRowIndex();
        DynamicObject data = ((ReportList)this.getControl("reportlistap")).getReportModel().getRowData(index);
        DynamicObject material = data.getDynamicObject("yd_material");
        if (Objects.nonNull(material)) {
            BillShowParameter billShowParameter = new BillShowParameter();
            billShowParameter.setFormId("bd_material");
            billShowParameter.setPkId(material.get("id"));
            billShowParameter.setCaption(String.format("物料"));
            billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
            this.getView().showForm(billShowParameter);
        } else {
            this.getView().showErrorNotification("系统异常，请联系管理员！");
        }
    }
}
