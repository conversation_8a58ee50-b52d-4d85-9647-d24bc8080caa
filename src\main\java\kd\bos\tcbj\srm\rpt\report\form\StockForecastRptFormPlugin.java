package kd.bos.tcbj.srm.rpt.report.form;

import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.form.ShowType;
import kd.bos.form.chart.PointLineChart;
import kd.bos.form.container.Tab;
import kd.bos.form.control.events.TabSelectEvent;
import kd.bos.form.control.events.TabSelectListener;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.report.ReportList;
import kd.bos.report.events.SortAndFilterEvent;
import kd.bos.report.plugin.AbstractReportFormPlugin;
import kd.bos.tcbj.srm.rpt.helper.StockForecastChartHelper;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.form.StockForecastRptFormPlugin
 * @className: StockForecastRptFormPlugin
 * @description: 预测实际对比情况表报表界面插件
 * @author: hst
 * @createDate: 2022/10/24
 * @version: v1.0
 */
public class StockForecastRptFormPlugin extends AbstractReportFormPlugin implements HyperLinkClickListener {

    @Override
    public void setSortAndFilter(List<SortAndFilterEvent> allColumns) {
        super.setSortAndFilter(allColumns);
        for (SortAndFilterEvent sortAndFilterEvent : allColumns) {
            // update by hst 2022/11/25 增加供应商列筛选
            if (sortAndFilterEvent.getColumnName().equals("yd_matnum")
                    || sortAndFilterEvent.getColumnName().equals("yd_matname")
                        || sortAndFilterEvent.getColumnName().equals("yd_supplier")) {
                sortAndFilterEvent.setSort(true);
                sortAndFilterEvent.setFilter(true);
            }
        }
    }

    /**
     * 注册监听
     * <AUTHOR>
     * @createDate: 2022/10/24
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        ReportList reportList = getControl("reportlistap");
        reportList.addHyperClickListener(this);
    }

    /**
     * 监听用户点击列表上的超链接，跳转对应供应商库页面
     * <AUTHOR>
     * @createDate: 2022/10/24
     * @param hyperLinkClickEvent
     */
    @Override
    public void hyperLinkClick(HyperLinkClickEvent hyperLinkClickEvent) {
        String key = hyperLinkClickEvent.getFieldName();
        switch (key) {
            case "yd_material" : {
                this.forwardToMaterialPage(hyperLinkClickEvent);
                break;
            }
        }
    }

    /**
     * 跳转对应物料页面
     * @author: hst
     * @createDate: 2022/10/24
     * @param hyperLinkClickEvent
     */
    public void forwardToMaterialPage(HyperLinkClickEvent hyperLinkClickEvent) {
        int index = hyperLinkClickEvent.getRowIndex();
        DynamicObject data = ((ReportList)this.getControl("reportlistap")).getReportModel().getRowData(index);
        DynamicObject material = data.getDynamicObject("yd_material");
        if (Objects.nonNull(material)) {
            BillShowParameter billShowParameter = new BillShowParameter();
            billShowParameter.setFormId("bd_material");
            billShowParameter.setPkId(material.get("id"));
            billShowParameter.setCaption(String.format("物料"));
            billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
            this.getView().showForm(billShowParameter);
        } else {
            this.getView().showErrorNotification("系统异常，请联系管理员！");
        }
    }

    @Override
    public void beforeQuery(ReportQueryParam queryParam) {
        super.beforeQuery(queryParam);
        FilterInfo filterInfo =queryParam.getFilter();
        PointLineChart pointLineChart = this.getControl("yd_pointlinechartap");
        StockForecastChartHelper pointLineChartHelper = new StockForecastChartHelper();
        pointLineChartHelper.drawChart(pointLineChart,filterInfo);
    }
}
