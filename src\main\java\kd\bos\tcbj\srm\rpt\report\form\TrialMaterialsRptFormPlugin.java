package kd.bos.tcbj.srm.rpt.report.form;

import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.ShowType;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.report.ReportList;
import kd.bos.report.events.SortAndFilterEvent;
import kd.bos.report.plugin.AbstractReportFormPlugin;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.rpt.helper.LurkSupplierHelper;

import java.util.EventObject;
import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.rpt.report.form.TrialMaterialsRptFormPlugin
 * @className: TrialMaterialsRptFormPlugin
 * @description: 试产物料申请报表界面插件
 * @author: hst
 * @createDate: 2022/12/6
 * @version: v1.0
 */
public class TrialMaterialsRptFormPlugin extends AbstractReportFormPlugin implements HyperLinkClickListener {

    /**
     * 设置排序和过滤字段
     * @param allColumns
     */
    @Override
    public void setSortAndFilter(List<SortAndFilterEvent> allColumns) {
        super.setSortAndFilter(allColumns);
        for (SortAndFilterEvent sortAndFilterEvent : allColumns) {
            if (sortAndFilterEvent.getColumnName().equals("yd_material")
                    || sortAndFilterEvent.getColumnName().equals("yd_matname")) {
                sortAndFilterEvent.setSort(true);
                sortAndFilterEvent.setFilter(true);
            }
        }
    }

    /**
     * 注册监听
     * <AUTHOR>
     * @createDate: 2022/12/06
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        ReportList reportList = getControl("reportlistap");
        reportList.addHyperClickListener(this);
    }

    /**
     * 监听用户点击列表上的超链接，跳转对应供应商库页面
     * <AUTHOR>
     * @createDate: 2022/12/06
     * @param hyperLinkClickEvent
     */
    @Override
    public void hyperLinkClick(HyperLinkClickEvent hyperLinkClickEvent) {
        String key = hyperLinkClickEvent.getFieldName();
        switch (key) {
            case "yd_billno" : {
                this.forwardToSrmSupplier(hyperLinkClickEvent);
                break;
            }
        }
    }

    /**
     * 跳转对应供应商库页面
     * @author: hst
     * @createDate: 2022/12/06
     * @param hyperLinkClickEvent
     */
    public void forwardToSrmSupplier(HyperLinkClickEvent hyperLinkClickEvent) {
        int index = hyperLinkClickEvent.getRowIndex();
        DynamicObject data = ((ReportList)this.getControl("reportlistap")).getReportModel().getRowData(index);
        String billId = data.getString("yd_billid");
        if (StringUtils.isNotBlank(billId)) {
            BillShowParameter billShowParameter = new BillShowParameter();
            billShowParameter.setFormId("yd_trialpurreqbill");
            billShowParameter.setPkId(billId);
            billShowParameter.setCaption(String.format("试产物料采购申请、反馈表"));
            billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
                this.getView().showForm(billShowParameter);
        } else {
            this.getView().showErrorNotification("系统异常，请联系管理员！");
        }
    }
}
