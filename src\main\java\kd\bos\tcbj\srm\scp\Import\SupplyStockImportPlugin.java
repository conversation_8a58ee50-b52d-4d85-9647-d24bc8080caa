package kd.bos.tcbj.srm.scp.Import;


import com.alibaba.fastjson.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.plugin.ImportLogger;
import kd.bos.form.plugin.impt.BatchImportPlugin;
import kd.bos.form.plugin.impt.ImportBillData;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.scp.Import.SupplyStockImportPlugin
 * @className SupplyStockImportPlugin
 * @author: hst
 * @createDate: 2022/09/05
 * @description: 供应商库存单引入引出插件
 * @version: v1.0
 */
public class SupplyStockImportPlugin extends BatchImportPlugin {

    /**
     * 作废该供应商的其他单据
     * @param rowdatas
     * @param logger
     * @return
     */
    @Override
    protected ApiResult save(List<ImportBillData> rowdatas, ImportLogger logger) {
        Iterator<ImportBillData> it = rowdatas.iterator();
        while(it.hasNext()) {
            List<DynamicObject> updateList = new ArrayList<>();
            ImportBillData data = it.next();
            Map<String, Object> billData = data.getData();
            JSONObject supply = (JSONObject) billData.get("yd_supply");
            String supplyName = supply.getString("name");
            //过滤条件一：相同供应商
            QFilter qFilter_1 = new QFilter(SupplyStockHelper.FIELD_SUPPLY + ".name", QFilter.equals, supplyName);
            //过滤条件二：不是已作废单据
            QFilter qFilter_2 = new QFilter(SupplyStockHelper.FIELD_STATUS, QFilter.not_equals, "C");
            DynamicObject[] oldBills = BusinessDataServiceHelper.load(SupplyStockHelper.ENTITY_MAIN,
                    SupplyStockHelper.FIELD_STATUS,new QFilter[]{qFilter_1,qFilter_2});
            for (DynamicObject oldBill : oldBills) {
                oldBill.set(SupplyStockHelper.FIELD_STATUS,"C");
                updateList.add(oldBill);
            }
            if (updateList.size() > 0) {
                SaveServiceHelper.save(updateList.toArray(new DynamicObject[updateList.size()]));
            }
        }
        return super.save(rowdatas, logger);
    }
}
