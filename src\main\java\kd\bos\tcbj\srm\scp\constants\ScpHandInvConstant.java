package kd.bos.tcbj.srm.scp.constants;

/**
 * @package: kd.bos.tcbj.srm.scp.constants.ScpHandInvConstant
 * @className ScpHandInvConstant
 * @author: hst
 * @createDate: 2024/06/19
 * @version: v1.0
 * @descrition: 供应商在手库存常量类
 */
public class ScpHandInvConstant {
    // 单据标识
    public static String MAIN_ENTITY = "yd_scp_handinv";
    // 供应商
    public static String SUPPLIER_FIELD = "yd_supplier";
    // 供应商编码
    public static String SUPNO_FIELD = "yd_supplier.number";
    // 供应商名称
    public static String SUPNAME_FIELD = "yd_supname";
    // 物料
    public static String MATERIAL_FIELD = "yd_material";
    // 物料编码
    public static String MATNO_FIELD = "yd_material.number";
    // 物料名称
    public static String MATNAME_FIELD = "yd_matname";
    // 在手成品库存
    public static String FERINV_FIELD = "yd_fertinv";
    // 在制品库存
    public static String WIPINV_FIELD = "yd_wipinv";
    // 供应商在途库存
    public static String TRANSITINV = "yd_transitinv";

    /**
     * 获取字段
     * @return
     */
    public static String getFields () {
        return SUPPLIER_FIELD + "," + SUPNO_FIELD + "," + SUPNAME_FIELD
                + "," + MATERIAL_FIELD + "," + MATNO_FIELD + "," + MATNAME_FIELD
                + "," + FERINV_FIELD + "," + WIPINV_FIELD + "," + TRANSITINV;
    }
}
