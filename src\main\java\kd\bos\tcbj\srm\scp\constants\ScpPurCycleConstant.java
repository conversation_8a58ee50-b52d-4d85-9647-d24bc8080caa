package kd.bos.tcbj.srm.scp.constants;

/**
 * @package: kd.bos.tcbj.srm.scp.constants.ScpPurCycleConstant
 * @className ScpPurCycleConstant
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 * @descrition: 供应商采购周期常量类
 */
public class ScpPurCycleConstant {
    // 单据标识
    public static String MAIN_ENTITY = "yd_scp_purcycle";
    // 供应商
    public static String SUPPLIER_FIELD = "yd_supplier";
    // 物料
    public static String MATERIAL_FIELD = "yd_material";
    // 计划交货天数
    public static String DELDAYS_FIELD = "yd_deliverydays";
    // 安全库存（天）
    public static String SAFEDAYS_FIELD = "yd_safetydays";
    // 到货提前期（天）
    public static String ADVDAYS_FIELD = "yd_advancedays";
    // 补货批量（天）
    public static String CPFRDAYS_FIELD = "yd_cpfrdays";
    // 日产能
    public static String ENERGY_FIELD = "yd_energy";
    // 采购方确认状态
    public static String CONSTATUS_FIELD = "yd_confirmstatus";
    // 版本
    public static String VERSION_FIELD = "yd_version";
    // 分录
    public static String ENTRY_ENTITY = "yd_entryentity";
    // 计划交货天数
    public static String PLAN_COLUMN = "yd_plandays";
    // 安全库存（天）
    public static String SAFEDAY_COLUMN = "yd_safetyday";
    // 到货提前期（天）
    public static String EARLYDAY_COLUMN = "yd_earlyday";
    // 补货批量（天）
    public static String REPLENISH_COLUMN = "yd_replenish";
    // 日产能
    public static String CAPACITY_COLUMN = "yd_capacity";
    // 版本
    public static String HISVERSION_COLUMN = "yd_hisversion";

    /**
     * 获取字段
     * @return
     * @author: hst
     * @createDate: 2024/06/08
     */
    public static String getFields () {
        return SUPPLIER_FIELD + "," + MATERIAL_FIELD + "," + SAFEDAYS_FIELD + ","
                + DELDAYS_FIELD + "," + ADVDAYS_FIELD + "," + CPFRDAYS_FIELD + ","
                + ENERGY_FIELD + "," +  CONSTATUS_FIELD + "," + VERSION_FIELD;
    }
}
