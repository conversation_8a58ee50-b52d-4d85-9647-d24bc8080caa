package kd.bos.tcbj.srm.scp.helper;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.math.BigDecimal;

/**
 * 开票单业务类
 * @package kd.bos.tcbj.srm.scp.helper.ScpInvoiceHelper
 * @className ScpInvoiceHelper
 * @author: hst
 * @createDate: 2024/05/13
 * @version: v1.0
 */
public class ScpInvoiceHelper {

    /**
     * 记录开票单应收金额与开票金额之差
     * @param bills
     * @author: hst
     * @createDate: 2024/05/13
     */
    public void recordAmountDifference (DynamicObject[] bills) {
        for (DynamicObject bill : bills) {
            // 应收金额
            BigDecimal sumAmount = bill.getBigDecimal("sumtaxamount");
            // 开票金额
            BigDecimal invAmount = BigDecimal.ZERO;
            // 入库明细税额
            BigDecimal totalTax = BigDecimal.ZERO;
            // 开票税额
            BigDecimal invTax = BigDecimal.ZERO;

            // 计算录入的总开票金额
            for (DynamicObject invoice : bill.getDynamicObjectCollection("entryentity")) {
                invAmount = invAmount.add(invoice.getBigDecimal("invamount"));
                invTax = invTax.add(invoice.getBigDecimal("invtax"));
            }

            // 计算入库明细税额
            for (DynamicObject detail : bill.getDynamicObjectCollection("entryentity1")) {
                totalTax = totalTax.add(detail.getBigDecimal("tax1"));
            }

            StringBuffer errMsg = new StringBuffer();
            // 校验是否一致
            if (sumAmount.compareTo(invAmount) != 0) {
                errMsg.append("发票金额与开票金额不一致，差额为" + invAmount.subtract(sumAmount).setScale(2) + "；");
            }
            if (totalTax.compareTo(invTax) != 0) {
                errMsg.append("发票税额与开票税额不一致，差额为" + invTax.subtract(totalTax).setScale(2) + "；");
            }

            if (errMsg.length() > 0) {
                if (StringUtils.isNotBlank(bill.getString("remark"))) {
                    bill.set("remark", bill.getString("remark")
                            + "--" + errMsg.toString());
                } else {
                    bill.set("remark", "--" + errMsg.toString());
                }
            }
        }
    }

    /**
     * 清除开票单应收金额与开票金额之差的记录
     * @param bills
     * @author: hst
     * @createDate: 2024/05/13
     */
    public void clearAmountDifference (DynamicObject[] bills) {
        for (DynamicObject bill : bills) {
            String remark = bill.getString("remark");
            if (StringUtils.isNotBlank(remark)) {
                if (remark.contains("--")) {
                    bill.set("remark",remark.substring(0,remark.indexOf("--")));
                }
            }
        }
    }
}
