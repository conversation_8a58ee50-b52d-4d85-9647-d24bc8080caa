package kd.bos.tcbj.srm.scp.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.ssc.task.util.DateUtil;
import kd.taxc.common.util.StringUtil;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.helper.ScpOrderHelper
 * @className: ScpOrderHelper
 * @author: hst
 * @createDate: 2022/09/21
 * @version: v1.0
 */
public class ScpOrderHelper {

    private Log logger = LogFactory.getLog(this.getClass().getName());
    public final static String ENTITY_MAIN = "scp_order";
    public final static String FIELD_BILLDATE = "billdate";
    public final static String FIELD_BILLNO = "billno";
    public final static String FIELD_BILLSTATUS = "billstatus";
    public final static String FIELD_CFMSTATUS = "cfmstatus";
    public final static String FIELD_SUPPLIER = "supplier";

    /**
     * 超过3自然日未确认订单，提醒供应商
     * @author: hst
     * @createDate: 2022/09/21
     */
    public void timeoutReminder() {
        Map<String,String> phones = new HashMap<>();
        Map<String,String> emails = new HashMap<>();
        Map<String,String> userIds = new HashMap<>();
        String date = DateUtil.format(DateTimeUtils.addDate(new Date(),-3),"yyyy-MM-dd HH:mm:ss");
        //日期过滤
        QFilter qFilter_1 = new QFilter(FIELD_BILLDATE,QFilter.less_than,date);
        //确认状态过滤
        QFilter qFilter_2 = new QFilter(FIELD_CFMSTATUS,QFilter.equals,"A");
        //单据状态确认
        QFilter qFilter_3 = new QFilter(FIELD_BILLSTATUS,QFilter.equals,"C");
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),ENTITY_MAIN,FIELD_BILLNO + "," +FIELD_SUPPLIER + ".id",
                new QFilter[]{qFilter_1,qFilter_2,qFilter_3},null);
        for (Row row : dataSet) {
            String supplyId = row.getString(FIELD_SUPPLIER + ".id");
            if (StringUtil.isNotBlank(supplyId)) {
                //获取对应供应商用户数据
            	DynamicObjectCollection supplierUserCol = SupplyStockHelper.getSrmSupplierUsersBySupplierId(supplyId);
                if (supplierUserCol != null) {
                	for (DynamicObject supplierUserInfo : supplierUserCol) {
                		if (supplierUserInfo != null) {
                			if (StringUtil.isNotBlank(supplierUserInfo.getString("user.phone"))) {
                				phones.put(row.getString(FIELD_BILLNO),supplierUserInfo.getString("user.phone"));
                			}
                			if (StringUtil.isNotBlank(supplierUserInfo.getString("user.email"))) {
                				emails.put(row.getString(FIELD_BILLNO),supplierUserInfo.getString("user.email"));
                			}
                			if (StringUtil.isNotBlank(supplierUserInfo.getString("id"))) {
                				userIds.put(row.getString(FIELD_BILLNO),supplierUserInfo.getString("user.id"));
                			}
                		}
                	}
                }
            }
        }
        this.sendMessage(phones,emails,userIds);
    }

    /**
     * 发送信息
     * @author: hst
     * @createDate: 2022/09/21
     * @param phones 手机号码集合
     * @param emails 邮箱集合
     * @param userIds 用户id集合
     */
    public void sendMessage(Map<String,String> phones, Map<String,String> emails, Map<String,String> userIds) {
        Map<String,Object> result = null;
        if (phones.size() > 0) {
            //发送短信
            for (Map.Entry<String,String> entry : phones.entrySet()) {
                result = MessageUtil.sendShortMessage(entry.getValue(), "采购订单" + entry.getKey() + "单号已超过3个自然日未确认，请及时进行确认！");
                if(result != null) {
                    String code = GeneralFormatUtils.getString(result.get("code"));
                    if(!"0".equals(code)) {
                        logger.error("单据：" +entry.getKey() + "的短信发送失败，原因：" +
                                GeneralFormatUtils.getString(result.get("description") + "\n"));
                    }
                }

            }
        }
        if (emails.size() > 0) {
            //发送邮件
            for (Map.Entry<String,String> entry : emails.entrySet()) {
                result = MessageUtil.sendEmail(entry.getValue(), "采购订单确认提醒",
                        "采购订单" + entry.getKey() + "单号已超过3自然日未确认，请及时进行确认！");
                if(result != null) {
                    String code = GeneralFormatUtils.getString(result.get("code"));
                    if(!"0".equals(code)) {
                        logger.error("单据：" +entry.getKey() + "的邮件发送失败，原因：" +
                                GeneralFormatUtils.getString(result.get("description") + "\n"));
                    }
                }
            }
        }
        if (userIds.size() > 0) {
            //发送消息中心信息
            for (Map.Entry<String,String> entry : userIds.entrySet()) {
                Long msgId = MessageUtil.sendMessage(entry.getValue(), "采购订单确认提醒",
                        "采购订单" + entry.getKey() + "单号已超过3自然日未确认，请及时进行确认！");
                if (msgId == 0L) {
                    logger.error("单据：" +entry.getKey() + "消息中心推送失败！");
                }
            }
        }
    }
}
