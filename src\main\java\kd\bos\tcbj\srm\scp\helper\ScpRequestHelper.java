package kd.bos.tcbj.srm.scp.helper;

import kd.bos.cache.CacheFactory;
import kd.bos.cache.TempFileCache;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.operate.StatusConvert;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.fileservice.FileService;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.operate.Modify;
import kd.bos.form.operate.View;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.message.api.EmailInfo;
import kd.bos.message.service.handler.EmailHandler;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.AttachmentServiceHelper;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.utils.AttachmentUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.bos.workflow.engine.msg.info.MessageAttachment;
import kd.taxc.common.util.StringUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.helper.ScpRequestHelper
 * @className: ScpRequestHelper
 * @author: hst
 * @createDate: 2022/09/14
 * @version: v1.0
 */
public class ScpRequestHelper {

    private final static Log logger = LogFactory.getLog("ScpRequestHelper");
    public final static String ENTITY_MAIN = "scp_request";
    public final static String FILED_METHOD = "yd_returnmethod";
    public final static String FIELD_BILLNO = "billno";
    public final static String FIELD_SUPPLIER = "supplier";
    public final static String FIELD_PICKTMIEM = "yd_pickuptime";
    public final static String FIELD_RECIPIENT = "yd_recipient";
    public final static String FIELD_CONTACT = "yd_contact";
    public final static String FIELD_ADDRESS = "yd_address";
    public final static String FIELD_LOGISTICS = "yd_logistics";
    public final static String FIELD_ATTACHMENT = "yd_attachment";

    /**
     * 获取操作对象的标识
     * @param afterDoOperationEventArgs
     * @return
     */
    public String operationObjectType(AfterDoOperationEventArgs afterDoOperationEventArgs) {
        if (afterDoOperationEventArgs.getSource() instanceof StatusConvert) {
            return((StatusConvert) afterDoOperationEventArgs.getSource()).getOperateKey();
        } else if (afterDoOperationEventArgs.getSource() instanceof View) {
            return((View) afterDoOperationEventArgs.getSource()).getOperateKey();
        } else if (afterDoOperationEventArgs.getSource() instanceof Modify) {
            return((Modify) afterDoOperationEventArgs.getSource()).getOperateKey();
        } else {
            return "";
        }
    }

    /**
     * 字段赋值
     * @author: hst
     * @createDate: 2022/09/14
     * @param arrayPk
     * @param data
     */
    public void assignmentField (Object[] arrayPk, Map<String,Object> data) {
        DynamicObject[] requests = BusinessDataServiceHelper.load(arrayPk,
                BusinessDataServiceHelper.newDynamicObject(ENTITY_MAIN).getDynamicObjectType());
        if (requests.length > 0) {
            for (DynamicObject request : requests) {
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    if (StringUtil.equals(FIELD_ATTACHMENT,entry.getKey())) {
                        if (entry.getValue() == null) {
                            this.deleteAllAttachmentData(request.get("id"));
                        } else {
                            this.uploadAttachment((List<Map<String, Object>>) entry.getValue(), request.get("id"));
                        }
                    } else {
                        request.set(entry.getKey(), entry.getValue());
                    }
                }
            }
            SaveServiceHelper.save(requests);
        }
    }

    /**
     * 发送邮件
     * @author: hst
     * @createDate: 2022/09/14
     * @param masterIds 单据id列表
     */
    public ApiResult sendEmail(List<Object> masterIds) {
        DynamicObject[] requests = BusinessDataServiceHelper.load(masterIds.toArray(new Object[masterIds.size()]),
                BusinessDataServiceHelper.newDynamicObject(ENTITY_MAIN).getDynamicObjectType());
        List<Map<String,Object>> emailInfos = new ArrayList<>();
        String title = "退货单确认通知";
        // update by hst -2022/11/07 仓库邮箱可配置化
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
                new QFilter[]{new QFilter("number",QFilter.equals,"SCP_REQUEST_EMAIL")});
        if (Objects.isNull(param)) {
            throw new KDBizException("未配置退货通知仓库邮箱，请联系管理员！");
        }
        String receiver = param.getString("name");
        String[] receivers = receiver.split(";");
        MessageAttachment messageAttachment = null;
        if (requests.length > 0) {
            for (DynamicObject request : requests) {
                String type = request.getString(FILED_METHOD);
                EmailInfo emailInfo = new EmailInfo();
                String content = "";
                switch (type) {
                    case "1" : {
                        content = "采购退货申请单" + request.getString(FIELD_BILLNO) + "单号，" + "供应商："
                                + (Objects.nonNull(request.getDynamicObject(FIELD_SUPPLIER)) ? request.getDynamicObject(FIELD_SUPPLIER).getString("name") : "") +
                                "已选择自提，预计取货时间：" + new SimpleDateFormat("yyyy-MM-dd").format(request.get(FIELD_PICKTMIEM)) + "日期。";
                        break;
                    }
                    case "2" : {
                        content = "采购退货申请单" + request.getString(FIELD_BILLNO) + "单号，" + "供应商："
                                + (Objects.nonNull(request.getDynamicObject(FIELD_SUPPLIER)) ? request.getDynamicObject(FIELD_SUPPLIER).getString("name") : "") +
                                "供应商已选择邮寄，收货人：" + request.getString(FIELD_RECIPIENT) + " ，联系方式：" + request.getString(FIELD_CONTACT) +
                                "，地址：" + request.getString(FIELD_ADDRESS) + "，偏好物流商：" + request.getString(FIELD_LOGISTICS) + "。" ;
                        break;
                    }
                    case "3" : {
                        content = "采购退货申请单" + request.getString(FIELD_BILLNO) + "单号，" + "供应商："
                                + (Objects.nonNull(request.getDynamicObject(FIELD_SUPPLIER)) ? request.getDynamicObject(FIELD_SUPPLIER).getString("name") : "") +
                                "供应商已选择废弃,附件为供应商情况说明";
                        messageAttachment = this.buildAttachment(request.get("id"));
                        break;
                    }
                    default: {
                        content = "";
                    }
                }
                if (StringUtil.isNotBlank(content)) {
                    Map<String,Object> map = new HashMap<>();
                    emailInfo.setContent(content);
                    if (messageAttachment != null) {
                        emailInfo.setAttachments(messageAttachment.getAttachments());
                        emailInfo.setAttachmentNames(messageAttachment.getAttachmentNames());
                    }
                    map.put("billNo",request.getString(FIELD_BILLNO));
                    map.put("emailInfo",emailInfo);
                    emailInfos.add(map);
                }
            }
        }
        
        ApiResult tmpResult = new ApiResult();tmpResult.setSuccess(true);
        for (String tmpReceiver : receivers) {
        	tmpResult = this.sendEmail(tmpReceiver,title,emailInfos);
		}
        return tmpResult;
    }

    /**
     * 向同一个收件人发送多封邮件
     * @author: hst
     * @createDate: 2022/09/14
     * @param receiver  接收人
     * @param title  邮件标题
     * @param maps  发送数据
     * @return
     */
    public ApiResult sendEmail(String receiver, String title, List<Map<String,Object>> maps) {
        ApiResult apiResult = new ApiResult();
        apiResult.setSuccess(true);
        try {
            StringBuffer errorInfo = new StringBuffer();
            for (Map<String,Object> map : maps) {
                EmailInfo emailInfo = (EmailInfo) map.get("emailInfo");
                emailInfo.setTitle(title);
                emailInfo.setReceiver(Arrays.asList(new String[]{receiver}));
                Map<String,Object> sendResult = EmailHandler.sendEmail(emailInfo);
                if(sendResult != null) {
                    String code = GeneralFormatUtils.getString(sendResult.get("code"));
                    if(!"0".equals(code)) {
                        errorInfo.append(map.get("billNo") + "、");
                    }
                }
            }
            if (errorInfo.length() > 0) {
                apiResult.setSuccess(false);
                apiResult.setMessage("单据：" + errorInfo.deleteCharAt(errorInfo.length() - 1).toString() + "的邮件发送失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            apiResult.setSuccess(false);
            apiResult.setMessage(e.getMessage());
        }
        return apiResult;
    }

    /**
     * 上传附件
     * @author: hst
     * @createDate: 2022/09/15
     * @param attachments
     * @param id
     */
    public void uploadAttachment (List<Map<String,Object>> attachments,Object id) {
        for (Map<String, Object> map : attachments) {
            String url = (String) map.get("url");
            String name = (String) map.get("name");
            int size = (int) map.get("size");
            if (url.contains("configKey=redis.serversForCache&id=tempfile")) {
                //持久化附件到服务器
                uploadTempfile(url, name,size,id);
            }
        }
    }

    /**
     * 上传临时文件到服务器中
     * @author: hst
     * @createDate: 2022/09/15
     * @param: url
     * @param: name
     * @return
    */
    private String uploadTempfile(String url, String name,int size, Object id) {
        try {
            TempFileCache cache = CacheFactory.getCommonCacheFactory().getTempFileCache();
            InputStream in = cache.getInputStream(url);
            // 需要改为上传到采购方单据上，20221123
            AttachmentUtil.uploadBillAttachment("pur_request",id.toString(),FIELD_ATTACHMENT,url,name,in,size);
        } catch (Exception e) {
            e.printStackTrace();
            throw new KDException("系统异常，请联系管理员！");
        }
        return url;
    }

    /**
     * 删除附件面板上的所有附件方法
     * @author: hst
     * @createDate: 2022/09/14
     */
    private void deleteAllAttachmentData(Object id) {
        QFilter qFilter_1 = new QFilter("finterid",QFilter.equals,id.toString());
        QFilter qFilter_2 = new QFilter("fbilltype",QFilter.equals,"pur_request");  // 需要改为上传到采购方单据上，20221123
        QFilter qFilter_3 = new QFilter("fattachmentpanel",QFilter.equals,FIELD_ATTACHMENT);
        int result = DeleteServiceHelper.delete("bos_attachment",new QFilter[]{qFilter_1,qFilter_2,qFilter_3});
    }

    /**
     * 构造附件
     * @author: hst
     * @createDate: 2022/09/15
     * @param: 业务单据id
     * @return
     */
    private MessageAttachment buildAttachment(Object id) {
        MessageAttachment aMessageAttachment =new MessageAttachment();
        List<byte[]> bytelist = new ArrayList<>();
        List<String> attachmentNames = new ArrayList<>();
        QFilter idQFilter = new QFilter("finterid",QFilter.equals,id);
        QFilter typeQFilter = new QFilter("fbilltype",QFilter.equals,ScpRequestHelper.ENTITY_MAIN);
        try {
            //获取附件明细中单据绑定的附件
            DynamicObject[] attachments = BusinessDataServiceHelper.load("bos_attachment","fattachmentname,ffileid",new QFilter[]{idQFilter,typeQFilter});
            for (DynamicObject attachment : attachments) {
                bytelist.add(urltobyte(attachment.getString("ffileid")));
                attachmentNames.add(attachment.getString("fattachmentname"));
            }
        } catch (Exception e) {
            logger.error(e);
            throw new KDBizException(e.getMessage());
        }
        aMessageAttachment.setAttachments(bytelist);
        aMessageAttachment.setAttachmentNames(attachmentNames);
        return aMessageAttachment;
    }

    /**
     * 获取文件字节流
     * @author: hst
     * @createDate: 2022/11/10
     * @param path 路径
     * @return
     * @throws Exception
     */
    private static byte[] urltobyte(String path) throws Exception {
        try {
            path = URLDecoder.decode(path, "UTF-8");
            while (path.startsWith("//")) {
                path = path.replaceFirst("//", "/");
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        FileService attachmentFileService = FileServiceFactory.getAttachmentFileService();
        if ((!attachmentFileService.exists(path))) {
            throw new NullPointerException("path参数错误!文件不存在 : " + path);
        }
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        attachmentFileService.download(path, outStream, null);
        byte[] file = outStream.toByteArray();
        return file;
    }
}
