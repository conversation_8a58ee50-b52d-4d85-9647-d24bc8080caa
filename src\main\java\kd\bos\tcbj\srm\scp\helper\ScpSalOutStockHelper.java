package kd.bos.tcbj.srm.scp.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.armor.core.util.StringUtil;
import kd.bos.botp.ConvertDataService;
import kd.bos.context.RequestContext;
import kd.bos.data.BusinessDataReader;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.entity.MulBasedataDynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityType;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.botp.runtime.BFRow;
import kd.bos.entity.botp.runtime.BFRowId;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.IRefrencedataProvider;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.openapi.common.result.CustomApiResult;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.BFTrackerServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.admittance.utils.BotpUtils;
import kd.bos.tcbj.srm.base.common.business.ConfigParamHelper;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.scp.constants.ScpAddAttachmentConstants;
import kd.bos.tcbj.srm.scp.constants.ScpSalOutStockConstants;
import kd.fi.cas.helper.OperateServiceHelper;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.helper.ScpSalOutStockHelper
 * @className ScpSalOutStockHelper
 * @author: hst
 * @createDate: 2022/09/23
 * @version: v1.0
 */
public class ScpSalOutStockHelper {

    /**
     * 附件字段数据回写
     * @author: hst
     * @createDate: 2022/09/23
     * @param pkId 单据主键
     * @param returnDatas 需要回写的数据
     */
    public void writeBackAttachment(Long pkId, DynamicObjectCollection returnDatas) {
        DynamicObject dynamicObject = BusinessDataServiceHelper.loadSingle(pkId, ScpSalOutStockConstants.ENTITY_MAIN);
        for (DynamicObject returnData : returnDatas) {
            for (DynamicObject material : dynamicObject.getDynamicObjectCollection(ScpSalOutStockConstants.ENTITY_MATERIAL)) {
                if (StringUtil.equals(returnData.getString(ScpAddAttachmentConstants.COLUMN_ENTRYID),
                        material.getString("id"))) {
                    this.assignmentAttachmentField(material, ScpSalOutStockConstants.COLUMN_CIQ,
                            returnData.get(ScpAddAttachmentConstants.COLUMN_CIQ));
                    this.assignmentAttachmentField(material, ScpSalOutStockConstants.COLUMN_COA,
                            returnData.get(ScpAddAttachmentConstants.COLUMN_COA));
                    this.assignmentAttachmentField(material, ScpSalOutStockConstants.COLUMN_CUSTOM,
                            returnData.get(ScpAddAttachmentConstants.COLUMN_CUSTOM));
                    this.assignmentAttachmentField(material, ScpSalOutStockConstants.COLUMN_STATE,
                            returnData.get(ScpAddAttachmentConstants.COLUMN_STATE));
                }
            }
        }
        OperateServiceHelper.execOperate("save", ScpSalOutStockConstants.ENTITY_MAIN,
                new DynamicObject[]{dynamicObject}, OperateOption.create());
    }

    /**
     * 附件字段赋值
     * @author: hst
     * @createDate: 2022/09/23
     * @param dynamicObject 赋值对象
     * @param field 字段名
     * @param value 值
     */
    public void assignmentAttachmentField (DynamicObject dynamicObject, String field, Object value) {
        DynamicObjectCollection attachments = (DynamicObjectCollection) value;
        DynamicObjectCollection billAttachments = dynamicObject.getDynamicObjectCollection(field);
        for (DynamicObject attachment : attachments) {
            billAttachments.add(attachment);
        }
    }

    /**
     * 单据状态校验
     * @author: hst
     * @createDate: 2022/09/23
     * @param dynamicObject 目标单据
     * @param Field 字段标识
     * @param status 状态
     * @return
     */
    public boolean checkBillStatus (DynamicObject dynamicObject, String Field, String status) {
        if (dynamicObject != null) {
            return StringUtil.equals(status, dynamicObject.getString(Field));
        } else {
            return false;
        }
    }

    /**
     * 校验生产商+物料编码+厂家批号 是否在库中
     * @author: hst
     * @createDate: 2022/11/16
     * @param bill
     */
    public Map<String,Object> checkInErrerLot (DynamicObject bill) {
        Map<String, Object> resutl = new HashMap<>();
        // 编辑界面会出现拿不到分录的情况，导致保存后分录被置空，重新查找一次单据 2022/11/18
//            if (!"0".equals(bill.getPkValue().toString())) {
//                bill = BusinessDataServiceHelper.loadSingle(bill.getPkValue(), bill.getDynamicObjectType());
//            }
        resutl.put("isSuccess",true);
        if (Objects.nonNull(bill)) {
            DynamicObject supplier = bill.getDynamicObject(ScpSalOutStockConstants.FIELD_SUPPLIER);     //销售公司
            if (Objects.isNull(supplier)) {
                resutl.put("isSuccess",false);
                resutl.put("message","请填写销售公司!");
                return resutl;
            }
            //遍历分录，校验生产商+物料编码+厂家批号 是否在异常批次库中
            int index = 1;
            StringBuffer errStr = new StringBuffer();
            for (DynamicObject entry : bill.getDynamicObjectCollection(ScpSalOutStockConstants.ENTITY_MATERIAL)) {
                DynamicObject material = entry.getDynamicObject(ScpSalOutStockConstants.COLUMN_MATERIAL);   //物料
                if (Objects.nonNull(material) && StringUtils.isNotBlank(entry.getString(ScpSalOutStockConstants.COLUMN_SUPLOT))) {
                    // 生产商+物料编码+厂家批号+不允许发货
                    QFilter qFilter = QFilter.of("yd_supplier.number = ? and yd_material.number = ? and yd_supplierno = ? and enable = 1",
                            supplier.getString("number"), material.getString("number"), entry.getString(ScpSalOutStockConstants.COLUMN_SUPLOT));
                    // 校验是否存在
                    boolean isExist = QueryServiceHelper.exists("yd_scp_errlot",new QFilter[]{qFilter});
                    if (isExist) {
                        errStr.append(String.valueOf(index) + "、");
                    }
                }
                index ++;
            }
            if (errStr.length() > 0) {
                resutl.put("isSuccess",false);
                resutl.put("message","发货单第"+ errStr.deleteCharAt(errStr.length() - 1) +"行的批次为异常管控批次不允许发货，如需让步放行请联系汤臣质量部门!");
                bill.set("yd_iserrlot",true);       //异常批次
            } else {
                bill.set("yd_iserrlot",false);      //不是异常批次
            }
//            SaveServiceHelper.save(new DynamicObject[]{bill});
        } else {
            resutl.put("isSuccess",false);
            resutl.put("message","系统异常，请联系管理员！");
        }
        return resutl;
    }

    /**
     * 根据销售发货单生成预约送货台账，
     * 并生成预约送货台账移动端访问url
     * @author: hst
     * @createDate: 2022/11/22
     * @param bill
     */
    public void createDeliverStand (DynamicObject bill) {
        // 审核通过后才创建
        if ("C".equals(bill.getString(ScpSalOutStockConstants.FIELD_STATUS))) {
            // 下推生成预约送货台账
//            DynamicObject stand = this.pushBill(bill.getPkValue().toString(),ScpSalOutStockConstants.ENTITY_MAIN,"yd_pur_deliverstand",
//                    "1559441367409654784","预约送货台账");
            // update by hst 2023/01/13 需要携带单据id和分录id，修改为用代码下推
            DynamicObject stand = this.convertDeliverStand(bill);
            try {
                OperationResult result = OperateServiceHelper.execOperate("save", "yd_pur_deliverstand",
                        new DynamicObject[]{stand}, OperateOption.create());
                if (result.isSuccess()) {
                    // update by hst 2023/01/13 保存关联关系
                    BotpUtils.createRelation(bill.getDynamicObjectType().getName(),Long.valueOf(bill.getPkValue().toString()),
                            stand.getDynamicObjectType().getName(),Long.valueOf(stand.getPkValue().toString()));

                    String url = "";
                    Object path = ConfigParamHelper.getConfigParam("scp_saloutstock", "yd_weburl");
                    url = path + "?state=#/home?rcode=" + bill.getString("billno");

                    bill.set(ScpSalOutStockConstants.FILED_URL, url);
                    SaveServiceHelper.save(new DynamicObject[]{bill});
                } else {
                    // 如果生成失败，则将单据撤销
                    this.revokeBill(bill);
                }
            } catch (Exception e) {
                // 将单据撤销
                this.revokeBill(bill);
                // update by hst 2023/01/13 如果执行异常，则删除预约送货台账
                this.deleteBotpBill(bill.getPkValue().toString(),bill.getDynamicObjectType().getName());
                throw new KDBizException("系统异常，请联系系统管理员！");
            }
        }
    }

    /**
     * 删除预约送货台账
     * 清除url
     * @author: hst
     * @createDate: 2022/11/22
     * @param bill
     */
    public void deleteDeliverStand (DynamicObject bill) {
        // 删除预约送货台账
        this.deleteBotpBill(bill.getPkValue().toString(), ScpSalOutStockConstants.ENTITY_MAIN);
        bill = BusinessDataServiceHelper.loadSingle(bill.getPkValue(),ScpSalOutStockConstants.ENTITY_MAIN);
        // 清除url
        bill.set(ScpSalOutStockConstants.FILED_URL, "");
        OperationResult result = OperateServiceHelper.execOperate("save", ScpSalOutStockConstants.ENTITY_MAIN,
                new DynamicObject[]{bill}, OperateOption.create());
        if (!result.isSuccess()) {
            throw new KDBizException("系统异常，请联系系统管理员！");
        }
    }

    /**
     * 下推单据
     * @param oriBillId
     * @param oriBillEntity
     * @param desBillEntity
     * @param ruleId
     * @return
     */
    private DynamicObject pushBill(String oriBillId, String oriBillEntity, String desBillEntity, String ruleId, String billName) {
        // 将源单ID，源单标识，目标单标识，目标单名称，下推规则作为参数
        // 返回目标单ID
        // 构建下推参数
        PushArgs pushArgs = new PushArgs();
        pushArgs.setSourceEntityNumber(oriBillEntity);  // 源单标志
        pushArgs.setTargetEntityNumber(desBillEntity);  // 目标单标志
        pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告
        pushArgs.setRuleId(ruleId);  // 固定下推规则

        //需要下推的单据
        List<ListSelectedRow> selectedRows = new ArrayList<>();
        ListSelectedRow srcBill = new ListSelectedRow(oriBillId);
        selectedRows.add(srcBill);
        pushArgs.setSelectedRows(selectedRows);

        //调用下推引擎，下推目标单
        ConvertOperationResult pushResult = ConvertServiceHelper.push(pushArgs);

        //判断下推是否成功，如果失败，提炼失败消息
        if(!pushResult.isSuccess()){
            //错误摘要
            String errMessage = pushResult.getMessage();
            throw new KDException("下推"+billName+"失败" + errMessage);
        }

        //获取生成的目标单数据包
        MainEntityType targetMainType = EntityMetadataCache.getDataEntityType(desBillEntity);
        List<DynamicObject> targetBillObjs = pushResult.loadTargetDataObjects(new IRefrencedataProvider() {
            @Override
            public void fillReferenceData(Object[] objs, IDataEntityType dType) {
                BusinessDataReader.loadRefence(objs, dType);
            }
        }, targetMainType);

        return targetBillObjs.get(0);
    }

    /**
     * 删除通过BOTP下推生成的送货预约台账
     * @param id
     */
    public void deleteBotpBill(String id, String srcBillEntity) {
        ConvertDataService dataService = new ConvertDataService();
        LinkedList<String> billRelList = new LinkedList<String>();
        while(org.apache.commons.lang3.StringUtils.isNotEmpty(id)) {
            // 遍历单据，获取下游单据数据
            Map<Long, List<BFRow>> dirTargetBill = BFTrackerServiceHelper.findDirtTargetBills(srcBillEntity, new Long[] {Long.parseLong(id)});
            if (dirTargetBill.size() == 1) {
                Long tempLongId = dirTargetBill.keySet().iterator().next();
                List<BFRow> targetRow = dirTargetBill.get(tempLongId);
                BFRowId targetBillRow = targetRow.get(0).getId();
                srcBillEntity = dataService.loadTableDefine(targetBillRow.getTableId()).getEntityNumber();
                id = targetBillRow.getBillId().toString();
                billRelList.add(srcBillEntity+"&"+id);
            } else {
                id = "";
                srcBillEntity = "";
            }
        }
        // 从下到上删单
        for(int i=billRelList.size()-1;i>=0;i--) {
            String[] keys = billRelList.get(i).split("&");
            String entity = keys[0];
            String tempBillId = keys[1];
            DynamicObject billObj = BusinessDataServiceHelper.loadSingle(tempBillId, entity);
            String billstatus = billObj.getString("billstatus");
            String billName = "";
            billName = "预约送货台账";
            if ("B".equals(billstatus)) {
                // 撤销再删除
                execOperation("unsubmit","撤销提交", entity, billName,billObj);
            } else if ("C".equals(billstatus)) {
                // 反审再删除
                execOperation("unaudit","反审核",  entity, billName,billObj);
            }
            // 删除单据
            execOperation("delete","删除",  entity, billName,billObj);
        }
    }

    /**
     *  撤销或销审单据
     * @author: hst
     * @createDate: 2022/11/22
     * @param bill
     */
    public void revokeBill (DynamicObject bill) {
        String status = bill.getString(ScpSalOutStockConstants.FIELD_STATUS);
        if ("B".equals(status)) {
            OperateServiceHelper.execOperate("unsubmit", ScpSalOutStockConstants.ENTITY_MAIN,
                    new Object[]{bill.getPkValue()}, OperateOption.create());
        } else if ("C".equals(status)) {
            OperateServiceHelper.execOperate("unaudit", ScpSalOutStockConstants.ENTITY_MAIN,
                    new Object[]{bill.getPkValue()}, OperateOption.create());
        }
    }

    /**
     * 执行特定操作
     * @param operationKey 操作标识
     * @param operateName 操作名称
     * @param entityName 实体标识
     * @param bill 单据
     * @param billName 菜单名称
     * @author: hst
     * @createDate: 2022/11/23
     * @return
     */
    public static void execOperation(String operationKey, String operateName, String entityName, String billName, DynamicObject bill) {
        OperationResult result = OperationServiceHelper.executeOperate(operationKey, entityName, new DynamicObject[]{bill}, OperateOption.create());
        if (!result.isSuccess()) {
            // 错误摘要
            throw new KDException("单据"+bill.getString("billno") + operateName +
                    "失败，请到"+billName+"手动撤销提交，查看撤销提交失败原因：" + result.getMessage());
        }
    }

    /**
     * 获取已发货并且COA文件(CIQ文件或报关单)为空的销售发货单，然后发货7天后每天给供应商发送短信通知
     * @author: hst
     * @createDate: 2022/01/06
     * @param type 文件类型
     */
    public void lackFileReminder (String type) {
        // 当前时间
        Date today = new Date();
        // 6天前
        Date date = DateUtil.addDays(today, -6);
        String auditDate = DateUtil.date2str(date, "yyyy-MM-dd");
        // 查询发货超过7天的单据
        QFilter dateFilter = new QFilter("auditdate", QFilter.less_equals, auditDate);
        QFilter statusFilter = new QFilter("billstatus", QFilter.equals, "C");
        // 查询符合条件的单据
        DataSet bills = QueryServiceHelper.queryDataSet(this.getClass().getName(), "scp_saloutstock", "id",
                new QFilter[]{statusFilter, dateFilter}, null);
        while (bills.hasNext()) {
            boolean isSend = false;
            String message = "";
            Row row = bills.next();
            String billId = row.getString("id");
            if (StringUtils.isNotBlank(billId)) {
                DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId, "scp_saloutstock");
                String billNo = bill.getString("billno");
                DynamicObject supplier = bill.getDynamicObject("supplier");
                DynamicObjectCollection entries = bill.getDynamicObjectCollection("materialentry");
                if (Objects.nonNull(supplier) && StringUtils.isNotBlank(billNo) && entries.size() > 0) {
                    switch (type) {
                        case "COA": {
                            for (DynamicObject entry : entries) {
                                DynamicObjectCollection coaFiles = entry.getDynamicObjectCollection("yd_coa");
                                if (coaFiles.size() == 0) {
                                    isSend = true;
                                    message = "系统检测到" + billNo + "销售发货单缺少COA文件，请登录SRM系统并到销售发货单模块进行补录。";
                                    break;
                                }
                            }
                            break;
                        }
                        case "CIQ": {
                            // 相隔天数
                            Date billDate = bill.getDate("auditdate");
                            int day = Double.valueOf(DateUtil.getDifferDay(DateUtil.formatDate(billDate,"yyyy-MM-dd"),
                                    DateUtil.formatDate(today,"yyyy-MM-dd"))).intValue();
                            if (day > 0 && day%7 == 0) {
                                for (DynamicObject entry : entries) {
                                    DynamicObjectCollection ciqFiles = entry.getDynamicObjectCollection("yd_ciqattachment");
                                    DynamicObjectCollection customsFiles = entry.getDynamicObjectCollection("yd_customsattachment");
                                    if (ciqFiles.size() == 0 || customsFiles.size() == 0) {
                                        isSend = true;
                                        message = "系统检测到" + billNo + "销售发货单缺少CIQ文件/报关单，请登录SRM系统并到销售发货单模块进行补录。";
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                    }
                    if (isSend) {
                        //获取对应供应商用户数据
                    	DynamicObjectCollection supplierUserCol = SupplyStockHelper.getSrmSupplierUsersBySupplierId(supplier.getString("id"));
                        if (supplierUserCol != null) {
                        	for (DynamicObject supplierUserInfo : supplierUserCol) {
                        		if (StringUtil.isNotBlank(supplierUserInfo.getString("user.phone"))) {
                        			String phone = supplierUserInfo.getString("user.phone");
                        			MessageUtil.sendShortMessage(phone, message);
                        		}
                        	}
                        }
                    }
                }
            }
        }
    }

    /**
     * 下推生成预约送货台账，因为需要携带单据id和分录id，所以手工赋值
     * @author: hst
     * @createDate: 2023/01/13
     * @param stock
     */
    public DynamicObject convertDeliverStand (DynamicObject stock) {
        DynamicObject deliver = BusinessDataServiceHelper.newDynamicObject("yd_pur_deliverstand");
        // 表头字段赋值
        deliver.set("yd_saloutno",stock.get(ScpSalOutStockConstants.FIELD_BILLNO));                     // 销售发货单号
        deliver.set("yd_shipper",stock.get(ScpSalOutStockConstants.FIELD_DRIVER));                      // 送货人
        deliver.set("yd_contact",stock.get(ScpSalOutStockConstants.FIELD_CONTACT));                     // 联系方式
//        deliver.set("yd_arrivaldate",stock.get(ScpSalOutStockConstants.FIELD_DELIDATE));               // 预计到货日期
        // update by hst 2023/11/07 修改创建时单据状态
        deliver.set("billstatus","A");                                                                   // 单据状态
        deliver.set("yd_billstatus","A");
        deliver.set("createtime",new Date());                                                            // 创建时间
        deliver.set("creator", UserServiceHelper.getCurrentUserId());                                     // 创建人
        // update by hst 2023/05/16 携带供应商
        deliver.set("yd_supplier",stock.get(ScpSalOutStockConstants.FIELD_SUPPLIER));
        DynamicObjectCollection stockEntries = stock.getDynamicObjectCollection(ScpSalOutStockConstants.ENTITY_MATERIAL);
        // 分录赋值
        for (DynamicObject stockEntry : stockEntries) {
            DynamicObject deliverEntry = deliver.getDynamicObjectCollection("yd_materialentry").addNew();
            deliverEntry.set("yd_material",stockEntry.get(ScpSalOutStockConstants.COLUMN_MATERIAL));   // 物料编码
            deliverEntry.set("yd_qty",stockEntry.get(ScpSalOutStockConstants.COLUMN_QTY));              // 发货数量
            deliverEntry.set("yd_unit",stockEntry.get(ScpSalOutStockConstants.COLUMN_UNIT));            // 计量单位
            deliverEntry.set("yd_orderno",stockEntry.get(ScpSalOutStockConstants.COLUMN_POBILLNO));    // 订单号
            deliverEntry.set("yd_stockid", stock.get("id"));                 // 销售发货单id
            deliverEntry.set("yd_stockentryid",stockEntry.get("id"));  // 销售发货分录id
            // update by hst 2023/06/28 携带物料对应实体仓
            DynamicObject material = stockEntry.getDynamicObject(ScpSalOutStockConstants.COLUMN_MATERIAL);
            if (Objects.nonNull(material)) {
                DynamicObject wareHouse = material.getDynamicObject("yd_warehouse");
                deliverEntry.set("yd_warehouse",wareHouse);
            }

        }
        // update by hst 2023/11/07 增加物料类型，以第一行分录物料，不做区分
        if (stockEntries.size() > 0) {
            DynamicObject stockEntry = stockEntries.get(0);
            DynamicObject material = stockEntry.getDynamicObject("material");
            if (Objects.nonNull(material)) {
                String matNumber = material.getString("number");
                deliver.set("yd_mattype",matNumber.substring(0,1));
            }
        }
        return deliver;
    }

    /**
     * 根据物料编码及收货入库批号获取附件
     * @param material
     * @param lot
     * @return
     * @author: hst
     * @createDate: 2023/05/06
     */
    public CustomApiResult getMaterialAttachment(String material,String lot) {
        List<Map<String,Object>> result = new ArrayList<>();
        // 参数信息
        if (StringUtils.isBlank(material) || StringUtils.isBlank(lot)) {
            return CustomApiResult.fail("scp.yd.100001","查询失败, 参数为空 !");
        }
        // 过滤条件
        QFilter matFilter = new QFilter("materialentry.material.number",QFilter.equals,material);
        QFilter lotFilter = new QFilter("materialentry.yd_reclot",QFilter.equals,lot);
        // 查询指定数据
        DynamicObject[] bills = BusinessDataServiceHelper.load(ScpSalOutStockConstants.ENTITY_MAIN,
                "materialentry.material,materialentry.yd_reclot,materialentry.yd_coa,materialentry.yd_ciqattachment," +
                        "materialentry.yd_customsattachment,materialentry.yd_stateattachment", new QFilter[]{matFilter,lotFilter});
        Map<String,Object> materialAtt = new HashMap<>();
        materialAtt.put("material",material);
        materialAtt.put("lot",lot);
        Map<String,Object> attachUrls = new HashMap<>();
        for (DynamicObject bill : bills) {
            for (DynamicObject entry : bill.getDynamicObjectCollection("materialentry")) {
                String matNum = Objects.nonNull(entry.getDynamicObject("material")) ? entry.getDynamicObject("material").getString("number") : "";
                String matLot = entry.getString("yd_reclot");
                if (StringUtils.isNotBlank(matNum) && StringUtils.isNotBlank(lot) && material.equals(matNum) && matLot.equals(lot)) {
                    this.getAttachmentUrl(attachUrls,"COA",entry.getDynamicObjectCollection("yd_coa"));
                    this.getAttachmentUrl(attachUrls,"CIQ",entry.getDynamicObjectCollection("yd_ciqattachment"));
                    this.getAttachmentUrl(attachUrls,"C/D",entry.getDynamicObjectCollection("yd_customsattachment"));
                    this.getAttachmentUrl(attachUrls,"ST",entry.getDynamicObjectCollection("yd_stateattachment"));
                }
            }
        }
        materialAtt.put("attachments",attachUrls);
        result.add(materialAtt);
        return CustomApiResult.success(result);
    }

    /**
     * 获取附件地址
     * @param attachUrls
     * @param type
     * @param attachments
     * @author: hst
     * @createDate: 2023/05/06
     */
    public void getAttachmentUrl (Map<String,Object> attachUrls, String type, DynamicObjectCollection attachments) {
        List<String> urls = new ArrayList<>();
        String path = RequestContext.get().getClientFullContextPath();
        attachments.stream().forEach(attachment -> {
            urls.add(path + "attachment/download.do?path=" +
                    attachment.getDynamicObject("fbasedataid").getString("url"));
        });
        if (attachUrls.containsKey(type)) {
            ((ArrayList)attachUrls.get(type)).addAll(urls);
        } else {
            attachUrls.put(type,urls);
        }
    }

    /**
     * 批量附件上传数据处理
     * @param bill
     * @param data
     * @param model
     * @author: hst
     * @createDate: 2023/06/05
     */
    public void batchFileUpload (DynamicObject bill, Object data, IDataModel model) {
        Map<String,Object> returnsData = (Map<String,Object>) data;
        if (Objects.nonNull(returnsData)) {
            String field = returnsData.get("field").toString();
            List<String> ids = (List<String>) returnsData.get("ids");
            DynamicObjectCollection attachments = (DynamicObjectCollection) returnsData.get("attachments");
            this.attachmentPanelBuildAttachmentFields(bill,field,ids,attachments,model);
        }
    }

    /**
     * 附件面板数据构建单据附件字段
     * @param bill
     * @param attachments
     * @param field
     * @param ids
     * @return
     * @author: hst
     * @createDate: 2023/06/05
     */
    private void attachmentPanelBuildAttachmentFields (DynamicObject bill, String field, List<String> ids,
                                                       DynamicObjectCollection attachments, IDataModel model) {
        List<Object> attaIdList = new ArrayList<Object>();
        for (DynamicObject temp : attachments) {
            attaIdList.add(temp.getString("fbasedataid_id"));
        }
        DynamicObjectCollection targetAtts = bill.getDynamicObjectCollection(ScpSalOutStockConstants.ENTITY_MATERIAL);
        for (int i = 0; i < targetAtts.size(); i++) {
            DynamicObject entry = targetAtts.get(i);
            if (ids.indexOf(entry.getString("id")) != -1) {
                List<Object> tempIdList = new ArrayList<>();
                for (DynamicObject attachment : entry.getDynamicObjectCollection(field)) {
                    tempIdList.add(attachment.getString("fbasedataid_id"));
                }
                tempIdList.addAll(attaIdList);
                model.setValue(field,tempIdList.toArray(),i);
            }
        }
        OperateServiceHelper.execOperate("save",ScpSalOutStockConstants.ENTITY_MAIN,
                new DynamicObject[]{bill},OperateOption.create());
    }

    /**
     * 合格供应商及不合格批次获取
     * @param supplierNum 供应商编码
     * @param producerName 生产厂家名称
     * @param materialNum 物料编码
     * @param supLot 厂家批次
     * @return
     * @author: hst
     * @createDate: 2023/08/21
     */
    public CustomApiResult getQualifiedSupplyAndAbnormalBatch(String supplierNum,String producerName, String materialNum, String supLot) {
        // 查询合格供应商目录
        JSONObject returnData = new JSONObject();
        if (StringUtils.isBlank(supplierNum) || StringUtils.isBlank(producerName)
                || StringUtils.isBlank(materialNum) || StringUtils.isBlank(supLot)) {
            return CustomApiResult.fail("scp.yd.100002","查询失败, 查询参数不能为空!");
        }
        // （原辅料）合格供应商及不合格批次获取
        returnData = this.getRawSupSumBillAndAbnormalBatch(supplierNum,producerName,materialNum,supLot);
        // 当（原辅料）合格供应商目录中查询不到时，查询（包材）合格供应商目录
        if (Objects.isNull(returnData)) {
            returnData = this.getPackSupSumBillAndAbnormalBatch(supplierNum,producerName,materialNum,supLot);
        }
        // 校验是否在（原辅料）合格供应商目录中，或（包材）合格供应商目录中能查询到该供应商信息
        if (Objects.isNull(returnData)) {
            return CustomApiResult.fail("scp.yd.100003","查询失败, 在合格供应商目录中获取不到对应的供应商信息!");
        }
        // 获取异常批次
        QFilter supFilter = new QFilter("yd_supplier.number",QFilter.equals,supplierNum);
        QFilter proFilter = new QFilter("yd_producor",QFilter.equals,producerName);
        QFilter matFilter = new QFilter("yd_materialno",QFilter.equals,materialNum);
        QFilter lotFilter = new QFilter("yd_supplierno",QFilter.equals,supLot);
        DynamicObject[] batchs = BusinessDataServiceHelper.load("yd_scp_errlot","yd_supplierno,enable,yd_errtype",
                new QFilter[]{supFilter,proFilter,matFilter,lotFilter});
        JSONArray jsonArray = new JSONArray();
        for (DynamicObject batch : batchs) {
            JSONObject temp = new JSONObject();
            temp.put("lot",batch.getString("yd_supplierno"));
            temp.put("lotState",batch.getString("enable"));
            temp.put("reason",batch.getString("yd_errtype"));
            jsonArray.add(temp);
        }
        returnData.put("unableLotList",jsonArray);
        return CustomApiResult.success(returnData);
    }

    /**
     * （原辅料）合格供应商及不合格批次获取
     * @param supplierNum 供应商编码
     * @param producerName 生产厂家名称
     * @param materialNum 物料编码
     * @param supLot 厂家批次
     * @return
     * @author: hst
     * @createDate: 2023/08/21
     */
    private JSONObject getRawSupSumBillAndAbnormalBatch (String supplierNum,String producerName, String materialNum, String supLot) {
        JSONObject returnData = new JSONObject();
        // （原辅料）合格供应商目录
        QFilter supFilter = new QFilter("yd_agent.number",QFilter.equals,supplierNum);
        QFilter proFilter = new QFilter("yd_producers.name",QFilter.equals,producerName);
        QFilter matFilter = new QFilter("yd_material.number",QFilter.equals,materialNum);
        DynamicObject bill = BusinessDataServiceHelper.loadSingle("yd_rawsupsumbill","yd_agent,yd_producers,yd_material," +
                "yd_supplieraccesspot",new QFilter[]{supFilter,proFilter,matFilter});
        // 校验是否能在（原辅料）合格供应商目录中查询到该供应商信息
        if (Objects.isNull(bill)) {
            return null;
        }
        returnData.put("supplierNum",this.getBaseDataAttribute(bill.getDynamicObject("yd_agent"),"number"));
        returnData.put("supplierName",this.getBaseDataAttribute(bill.getDynamicObject("yd_agent"),"name"));
        returnData.put("producerName",this.getBaseDataAttribute(bill.getDynamicObject("yd_producers"),"name"));
        returnData.put("materialNum",this.getBaseDataAttribute(bill.getDynamicObject("yd_material"),"number"));
        returnData.put("materialName",this.getBaseDataAttribute(bill.getDynamicObject("yd_material"),"name"));
        returnData.put("supLot",supLot);
        DynamicObject accesspot = bill.getDynamicObject("yd_supplieraccesspot");
        if (Objects.nonNull(accesspot)) {
            Object supStatusId = this.getBaseDataAttribute(accesspot.getDynamicObject("group"), "id");
            DynamicObject supStatus = BusinessDataServiceHelper.loadSingle("yd_supplierstatus",
                    new QFilter[]{new QFilter("id",QFilter.equals,supStatusId)});
            returnData.put("supSumBillState",Objects.nonNull(supStatus.getDynamicObject("yd_wmstype")) ?
                    supStatus.getDynamicObject("yd_wmstype").getString("number") : "");
        } else {
            returnData.put("supSumBillState", "");
        }
        return returnData;
    }

    /**
     * （包材）合格供应商及不合格批次获取
     * @param supplierNum 供应商编码
     * @param producerName 生产厂家名称
     * @param materialNum 物料编码
     * @param supLot 厂家批次
     * @return
     * @author: hst
     * @createDate: 2023/08/21
     */
    private JSONObject getPackSupSumBillAndAbnormalBatch (String supplierNum,String producerName, String materialNum, String supLot) {
        JSONObject returnData = new JSONObject();
        // （包材）合格供应商目录
        QFilter supFilter = new QFilter("yd_supplier.number",QFilter.equals,supplierNum);
        QFilter proFilter = new QFilter("yd_supname",QFilter.equals,producerName);
        // update by hst 2023/12/06 不通过物料基础资料编码匹配
        QFilter matFilter = new QFilter("yd_materialno",QFilter.equals,materialNum);
        DynamicObject bill = BusinessDataServiceHelper.loadSingle("yd_packsupsumbill","yd_supplier,yd_supname,yd_material," +
                "yd_supstatus",new QFilter[]{supFilter,proFilter,matFilter});
        // 校验是否能在（包材）合格供应商目录中查询到该供应商信息
        if (Objects.isNull(bill)) {
            return null;
        }
        returnData.put("supplierNum",this.getBaseDataAttribute(bill.getDynamicObject("yd_supplier"),"number"));
        returnData.put("supplierName",this.getBaseDataAttribute(bill.getDynamicObject("yd_supplier"),"name"));
        returnData.put("producerName",bill.getString("yd_supname"));
        returnData.put("materialNum",this.getBaseDataAttribute(bill.getDynamicObject("yd_material"),"number"));
        returnData.put("materialName",this.getBaseDataAttribute(bill.getDynamicObject("yd_material"),"name"));
        returnData.put("supLot",supLot);
        // 获取参数配置表的配置信息
        String status = bill.getString("yd_supstatus");
        QFilter staFilter = new QFilter("yd_packconfigs.yd_supstatus",QFilter.equals,status);
        QFilter billFilter = new QFilter("yd_bill",QFilter.equals,"5");
        QFilter filter = new QFilter("billstatus",QFilter.equals,"C");
        DataSet dateSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),"yd_paramconfigure","yd_packconfigs.yd_wmstypenum",
                new QFilter[]{staFilter,billFilter,filter},null);
        if (dateSet.hasNext()) {
            Row row = dateSet.next();
            returnData.put("supSumBillState", row.getString("yd_packconfigs.yd_wmstypenum"));
        } else {
            returnData.put("supSumBillState", "");
        }
        return returnData;
    }

    /**
     * 获取基础资料某个属性值
     * @param baseData
     * @param field
     * @return
     * @author: hst
     * @createDate: 2023/08/21
     */
    private Object getBaseDataAttribute (DynamicObject baseData, String field) {
        if (Objects.nonNull(baseData)) {
            return baseData.getString(field);
        }
        return "";
    }

    /**
     * 单据状态校验(数组）
     * @author: hst
     * @createDate: 2024/03/25
     * @param dynamicObject 目标单据
     * @param Field 字段标识
     * @param status 状态
     * @return
     */
    public boolean checkBillStatus (DynamicObject dynamicObject, String Field, String[] status) {
        if (dynamicObject != null && StringUtils.isNotBlank(dynamicObject.getString(Field))) {
            return Arrays.asList(status).contains(dynamicObject.getString(Field));
        } else {
            return false;
        }
    }

    /**
     * 通过销售发货单分录ID获取收货单号
     * @return
     * @author: hst
     * @createDate: 2024/04/08
     */
    private List<String> getPurReceiptNoBySalOutEntryId (String entryId) {
        List<String> receiptNo = new ArrayList<>();

        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),"pur_receipt","billno",
                new QFilter[]{new QFilter("materialentry.yd_saledeliveryenid",QFilter.equals,entryId)},null);

        for (Row row : dataSet) {
            String billNo = row.getString("billno");

            if (StringUtils.isNotBlank(billNo)) {
                receiptNo.add(billNo);
            }
        }

        return receiptNo;
    }

    /**
     * 如果是国外生产商则需要校验CIQ及报关单必须上传，如果是国内生产商则只需要校验COA文件必须上传
     * @param entry
     * @author: hst
     * @createDate: 2024/04/24
     */
    public static String checkAttachment(DynamicObject entry, int i) {
        StringBuffer errMsg = new StringBuffer();
        DynamicObject producer = entry.getDynamicObject("yd_producer");
        if (Objects.nonNull(producer)) {
            String type = producer.getString("yd_suptype");
            if ("domestic".equals(type)) {
                errMsg = checkAttachmentEmpty(entry, "yd_coa")
                        ? errMsg : errMsg.append("第" + (i + 1) + "行分录：国内生产商的【COA文件】未上传；\n");
            } else if ("foreign".equals(type)) {
                errMsg = checkAttachmentEmpty(entry, "yd_coa")
                        ? errMsg : errMsg.append("第" + (i + 1) + "行分录：国内生产商的【COA文件】未上传；\n");

            }
        }
        return errMsg.toString();
    }

    /**
     * 如果是国外生产商则需要校验CIQ及报关单必须上传，如果是国内生产商则只需要校验COA文件必须上传
     * @param entry
     * @author: hst
     * @createDate: 2024/04/24
     */
    public static boolean checkAttachment(DynamicObject entry) {
        DynamicObject producer = entry.getDynamicObject("yd_producer");
        if (Objects.nonNull(producer)) {
            String type = producer.getString("yd_suptype");
            if ("domestic".equals(type)) {
                return checkAttachmentEmpty(entry, "yd_coa");
            } else if ("foreign".equals(type)) {
                return checkAttachmentEmpty(entry, "yd_coa");
            }
        }
        return true;
    }

    /**
     * 校验附件是否为空
     * @param entryInfo
     * @param prop
     * @return
     * @author: hst
     * @createDate: 2024/04/24
     */
    private static boolean checkAttachmentEmpty(DynamicObject entryInfo,String prop) {
        MulBasedataDynamicObjectCollection atts = (MulBasedataDynamicObjectCollection) entryInfo.get(prop);
        if (atts == null || atts.size() == 0) {
            return false;
        }
        return true;
    }

    /**
     * 校验是否为包材物料
     * 化学试剂五金配件、包材不用录入生产日期、不用录入COA文件
     * @param material
     * @return
     * @author: hst
     * @createDate: 2024/04/25
     */
    public static boolean checkIsPackagingMaterial(DynamicObject material) {
        if (Objects.nonNull(material)) {
            String matNum = material.getString("number");
            if (matNum.startsWith("D") || matNum.startsWith("E")) {
                return true;
            }
            if (matNum.startsWith("F") || matNum.startsWith("L")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 通过合格目录获取物料对应的送货车间
     * @param matId 物料ID
     * @param supId 供应商ID
     * @param proId 生产商ID
     * @return
     */
    public static Long getWareHouseBySumBill (Long matId, Long supId, Long proId) {
        QFilter qFilter = QFilter.of("yd_material.id = ? and yd_agent.supplier.id = ? and yd_producers.id = ?",
                matId, supId, proId);

        DynamicObject data = QueryServiceHelper.queryOne("yd_rawsupsumbill", "yd_warehouse",
                qFilter.toArray());

        if (Objects.nonNull(data)) {
            return data.getLong("yd_warehouse");
        } else {
            return 0L;
        }
    }

    /**
     * 通过合格目录获取物料对应的送货车间
     * @param matId 物料ID
     * @param supId 供应商ID
     * @param proId 生产商ID
     * @return
     */
    public static Set<Long> getWareHousesIdBySumBill (Long matId, Long supId, Long proId) {
        QFilter qFilter = QFilter.of("yd_material.id = ? and yd_agent.supplier.id = ? and yd_producers.id = ?",
                matId, supId, proId);

        DynamicObjectCollection datas = QueryServiceHelper.query("yd_rawsupsumbill", "yd_warehouse",
                qFilter.toArray());

        Set<Long> wareHouseId = new HashSet<>();
        for (DynamicObject data : datas) {
            wareHouseId.add(data.getLong("yd_warehouse"));
        }

        return wareHouseId;
    }

    /**
     * 通过合格目录获取物料对应的送货车间
     * @param matId 物料ID
     * @param supId 供应商ID
     * @param proId 生产商ID
     * @return
     */
    public static DynamicObject getWareHousesBySumBill (Long matId, Long supId, Long proId) {
        QFilter qFilter = QFilter.of("yd_material.id = ? and yd_agent.supplier.id = ? and yd_producers.id = ?",
                matId, supId, proId);

        DynamicObject data = BusinessDataServiceHelper.loadSingle("yd_rawsupsumbill",
                qFilter.toArray());

        return data;
    }

    /**
     * 根据实体仓库获取车间
     * @param wareHouse
     * @author: hongsitao
     * @createDate: 2025/02/24
     * @return
     */
    public static Set<Long> getShopIdByWareHouse (DynamicObject wareHouse) {
        Set<Long> shopIds = new HashSet<>();
        QFilter qFilter = QFilter.of("id = ?", wareHouse.getLong("id"));
        DataSet dataSet = QueryServiceHelper.queryDataSet(ScpSalOutStockHelper.class.getName(), "yd_warehouse",
                "yd_workshop.yd_shop.id", qFilter.toArray(), null);

        for (Row row : dataSet) {
            Long shopId = row.getLong("yd_workshop.yd_shop.id");

            if (shopId != 0L) {
                shopIds.add(shopId);
            }
        }

        return shopIds;
    }

    /**
     * 根据供应商 + 物料，查询合格供应商目录中不为暂停采购和停止采购的 生产商
     * @param supplier
     * @param material
     * @return
     */
    public static Set<String> getQualifiedProducers (DynamicObject supplier, DynamicObject material) {
        Set<String> producerIds = new HashSet<>();
        if (Objects.nonNull(supplier) && Objects.nonNull(material)) {
            /* 原辅料 */
            QFilter qFilter = QFilter.of("yd_material.number = ? and yd_agent.supplier.number = ? and " +
                            "yd_supplieraccesspot.group.number != ? and yd_supplieraccesspot.group.number != ?",
                    material.getString("number"), supplier.getString("number"), "08", "09");
            DataSet dataSet = QueryServiceHelper.queryDataSet("getQualifiedProducers", "yd_rawsupsumbill",
                    "yd_producers", qFilter.toArray(), null);
            for (Row row : dataSet) {
                String producerId = row.getString("yd_producers");
                if (StringUtils.isNotBlank(producerId)) {
                    producerIds.add(producerId);
                }
            }

            /* 包材 */
            qFilter = QFilter.of("yd_material.number = ? and yd_supplier.number = ? and " +
                            "yd_supstatus != ? and yd_supstatus != ?",
                    material.getString("number"), supplier.getString("number"),
                    "停止采购", "暂停采购");
            dataSet = QueryServiceHelper.queryDataSet("getQualifiedProducers", "yd_packsupsumbill",
                    "yd_producer", qFilter.toArray(), null);
            for (Row row : dataSet) {
                String producerId = row.getString("yd_producer");
                if (StringUtils.isNotBlank(producerId)) {
                    producerIds.add(producerId);
                }
            }
        }

        return producerIds;
    }
}