package kd.bos.tcbj.srm.scp.helper;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.JsonUtils;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.scm.common.util.ExceptionUtil;

import java.math.BigDecimal;
import java.util.*;

/**
 * 供应商备货单辅助类
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-11-2
 */
public class SupStockUpHelper {

    private static Log log = LogFactory.getLog(SupStockUpHelper.class);

    public static void revokeFacadeToEas(BeginOperationTransactionArgs e, Object billId) {
        // 根据已经确认成功的id获取info对象
        DynamicObject info = BizHelper.getDynamicObjectById("yd_stockup", billId);

        Map<String, Object> paramMap = new HashMap<>();
        Map<String, Object> paramDataMap = new HashMap<>();
        List<Map<String, Object>> entryList = new ArrayList<>();
        paramMap.put("data", paramDataMap);
        paramDataMap.put("number", info.getString("billno")); // 单据编码
        paramDataMap.put("entrys", entryList);

        DynamicObjectCollection entryCol = info.getDynamicObjectCollection("yd_materialentry");
        for (DynamicObject entryInfo : entryCol) {
            Map<String, Object> entryMap = new HashMap<>();
            // 供应商确认数量和日期
            BigDecimal supComfirmQty = entryInfo.getBigDecimal("yd_supcomfirmqty");
            BigDecimal diffQty = entryInfo.getBigDecimal("yd_diffqty"); // 差异数量
            Date supComfirmDate = entryInfo.getDate("yd_supcomfirmdate");

            entryMap.put("entryId", entryInfo.getString("yd_easentryid")); // EAS分录ID
            entryMap.put("supComfirmQty", supComfirmQty); // 供应商确认数量
            entryMap.put("supComfirmDate", supComfirmDate!=null? DateTimeUtils.format(supComfirmDate, DateTimeUtils.SDF_TIME):null); // 供应商确认时间
            entryMap.put("diffQty", diffQty); // 差异数量
            entryMap.put("supRemark", entryInfo.getString("yd_supremark")); // 供应商别再
            entryList.add(entryMap);
        }

        // 调用EAS功能，触发数据集成
        String result = (String) ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doSupStockupConfirm", paramMap);
        if (result == null) {
            throw new KDBizException("接口调用异常，EAS回传结果为空，请联系管理员！");
        }
        try {
            log.info("供应商备货单确认返回结果："+result);
            Map<String, Object> resultMap = JsonUtils.toMap(result);
            boolean isSuccess = (Boolean) resultMap.get("isSuccess");
            if (!isSuccess) {
                throw new KDBizException("供应商备货单确认失败，详细信息："+resultMap.get("message"));
            }
            // 已经反写，更新已经反写的状态
            info.set("yd_iswriteeas", true);
            BizHelper.save(info); // 更新
        } catch (Exception err) {
            log.error("供应商备货单确认失败" + ExceptionUtil.getStackTrace(err));
        }
    }
}
