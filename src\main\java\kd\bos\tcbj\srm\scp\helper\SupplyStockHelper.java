package kd.bos.tcbj.srm.scp.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Field;
import kd.bos.algo.Row;
import kd.bos.cache.CacheFactory;
import kd.bos.cache.TempFileCache;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.fileservice.FileItem;
import kd.bos.fileservice.FileService;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.message.api.EmailInfo;
import kd.bos.message.service.handler.EmailHandler;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.DeleteServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.taxc.common.util.StringUtil;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import java.awt.*;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.scp.helper.SupplyStockHelper
 * @className: SupplyStockHelper
 * @author: hst
 * @createDate: 2022/09/05
 * @version: v1.0
 */
public class SupplyStockHelper {

    public  static final String ENTITY_MAIN = "yd_scp_supplystock";
    public static final String ENTITY_WAREDETAIL = "yd_waredetail";
    public static final String FIELD_PURCHASING = "yd_purchasing";
    public static final String FIELD_UPDATEDATE = "yd_updatedate";
    public static final String FIELD_STATUS = "billstatus";
    public static final String FIELD_SUPPLY = "yd_supply";
    public static final String COLUMN_WARECODE = "yd_warecode";
    public static final String COLUMN_UNIT = "yd_unit";

    /**
     * 每次保存前将更新时间修改为当前时间
     * @author: hst
     * @createDate: 2022/09/05
     * @param: e
     */
    public static void moditfyUpdateDate(BeginOperationTransactionArgs e) {
        String key = e.getOperationKey();
        DynamicObject[] bills = e.getDataEntities();
        for (DynamicObject bill : bills) {
            String id = bill.getString("id");
            bill.set(FIELD_UPDATEDATE,new Date());
        }
    }

    /**
     * 提交后作废该供应商的其他单据
     * @author: hst
     * @createDate: 2022/09/05
     * @param: e
     */
    public static void nullifyOldSupplyStockBill (BeginOperationTransactionArgs e) {
        DynamicObject[] bills = e.getDataEntities();
        List<DynamicObject> updateList = new ArrayList<>();
        for (DynamicObject bill : bills) {
            String id = bill.getString("id");
            bill = BusinessDataServiceHelper.loadSingle(id,ENTITY_MAIN);
            DynamicObject supply = bill.getDynamicObject(FIELD_SUPPLY);
            String supplyCode = supply.getString("number");
            //过滤条件一：不是当前单据
            QFilter qFilter_1 = new QFilter("id", QFilter.not_equals, id);
            //过滤条件二：相同供应商
            QFilter qFilter_2 = new QFilter(FIELD_SUPPLY + ".number", QFilter.equals, supplyCode);
            //过滤条件三：不是已作废单据
            QFilter qFilter_3 = new QFilter(FIELD_STATUS, QFilter.not_equals, "C");
            DynamicObject[] oldBills = BusinessDataServiceHelper.load(ENTITY_MAIN, FIELD_STATUS,new QFilter[]{qFilter_1,qFilter_2,qFilter_3});
            for (DynamicObject oldBill : oldBills) {
                oldBill.set(FIELD_STATUS,"C");
                updateList.add(oldBill);
            }
        }
        if (updateList.size() > 0) {
            SaveServiceHelper.save(updateList.toArray(new DynamicObject[updateList.size()]));
        }
    }

    /**
     * 自动删除一个月之前的供应商库存单
     * @author: hst
     * @createDate: 2022/09/05
     * @param month
     */
    public static void deleteSupplyStockMonthAgo(int month) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -month);
        Date date = calendar.getTime();
        QFilter qFilter = new QFilter(FIELD_UPDATEDATE,QFilter.less_than,format.format(date));
        DeleteServiceHelper.delete(ENTITY_MAIN,new QFilter[]{qFilter});
    }

    /**
     * 查询供应商在一周内（根据更新日期）是否存在供应商库存单，如果不存在则发送邮件给供应商
     * @author: hst
     * @createDate: 2022/09/05
     */
    public static void notifyUpdateInventory() {
        List<String> receiverList = new ArrayList<>();
        //获取库存共享供应商
        QFilter qFilter_1 = new QFilter("yd_sharestock",QFilter.equals,true);
        DataSet supplies = QueryServiceHelper.queryDataSet("SupplyStockHelper","bd_supplier","id as id,number as supplyNumber",
                new QFilter[]{qFilter_1},null);
        //获取近一周,已审核的供应商库存单
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        Date date = calendar.getTime();
        qFilter_1 = new QFilter(FIELD_UPDATEDATE,QFilter.large_equals,format.format(date));
        QFilter qFilter_2 = new QFilter(FIELD_STATUS, QFilter.equals, "B");
        DataSet stockBills = QueryServiceHelper.queryDataSet("SupplyStockHelper",ENTITY_MAIN,FIELD_SUPPLY + ".number," + "billNo as billNo",
                new QFilter[]{qFilter_1,qFilter_2},null);
        DataSet JoinDataSet = supplies.leftJoin(stockBills).on("supplyNumber",FIELD_SUPPLY + ".number").
                select(new String[]{"id"},new String[]{"billNo"}).finish();
        for (Row row :JoinDataSet) {
            if (StringUtils.isBlank(row.getString("billNo"))) {
                //获取对应供应商用户数据
                DynamicObject supplierUserInfo = getSrmSupplierUserBySupplierId(row.getString("id"));
                if (supplierUserInfo != null) {
                    System.out.println("");
                    if (StringUtil.isNotBlank(supplierUserInfo.getString("user.email"))) {
                        receiverList.add(supplierUserInfo.getString("user.email"));
                    }
                }
            }
        }
        if (receiverList.size() > 0) {
            sendEmail(receiverList);
        }
    }

    /**
     * 根据对应供应商主数据返回对应供应商用户对象
     * @author: hst
     * @createDate: 2022/09/06
     * @param srmSupplierId
     * @return
     */
    public static DynamicObject getSrmSupplierUserBySupplierId(String srmSupplierId) {
        DynamicObject result = null;
        if(org.apache.commons.lang3.StringUtils.isEmpty(srmSupplierId)) {
            return result;
        }
        QFilter qFilter = new QFilter("supplier.id", QCP.equals, srmSupplierId);//根据id = srm供应商id过滤封装
        DynamicObjectCollection srmSupplierColl = QueryServiceHelper.query("srm_supplier", "id,number,bizpartner,bizpartner.id", new QFilter[] {qFilter});
        if(srmSupplierColl == null || srmSupplierColl.size() == 0 || srmSupplierColl.get(0) == null) {
            return result;
        }
        String bizPartnerId = GeneralFormatUtils.getString(srmSupplierColl.get(0).get("bizpartner"));
        if(org.apache.commons.lang3.StringUtils.isEmpty(bizPartnerId)) {
            return result;
        }
        qFilter = new QFilter("bizpartner", QCP.equals, bizPartnerId);  //供应商用户.商务伙伴id判断过滤封装
        qFilter.and(new QFilter("isadmin", QCP.equals, true));  // 取供应商管理员用户
        // update by hst 2024/08/22 增加使用状态过滤
        qFilter = qFilter.and(new QFilter("enable", QFilter.equals, "1"));
        DynamicObjectCollection supplierUserColl = QueryServiceHelper.
                query("pur_supuser", "id,user.phone,user.email,user.username,bizpartner,bizpartner.id,user,user.id",
                        new QFilter[] {qFilter});
        result = supplierUserColl != null && supplierUserColl.size() > 0 && supplierUserColl.get(0) != null ?
                supplierUserColl.get(0) : null;
        return result;
    }
    
    /**
     * 根据对应供应商主数据返回对应供应商用户对象，存在多个供应商用户
     * @author: yzl
     * @createDate: 2023/03/15
     * @param srmSupplierId
     * @return
     */
    public static DynamicObjectCollection getSrmSupplierUsersBySupplierId(String srmSupplierId) {
    	DynamicObjectCollection result = new DynamicObjectCollection();
        if(org.apache.commons.lang3.StringUtils.isEmpty(srmSupplierId)) {
            return result;
        }
        QFilter qFilter = new QFilter("supplier.id", QCP.equals, srmSupplierId);//根据id = srm供应商id过滤封装
        DynamicObjectCollection srmSupplierColl = QueryServiceHelper.query("srm_supplier", "id,number,bizpartner,bizpartner.id", new QFilter[] {qFilter});
        if(srmSupplierColl == null || srmSupplierColl.size() == 0 || srmSupplierColl.get(0) == null) {
            return result;
        }
        String bizPartnerId = GeneralFormatUtils.getString(srmSupplierColl.get(0).get("bizpartner"));
        if(org.apache.commons.lang3.StringUtils.isEmpty(bizPartnerId)) {
            return result;
        }
        qFilter = new QFilter("bizpartner", QCP.equals, bizPartnerId);  //供应商用户.商务伙伴id判断过滤封装
        qFilter.and(new QFilter("yd_neednotify", QCP.equals, true));  // 取需要短信通知的供应商用户
        // update by hst 2024/08/22 增加使用状态过滤
        qFilter = qFilter.and(new QFilter("enable", QFilter.equals, "1"));
        DynamicObjectCollection supplierUserColl = QueryServiceHelper.
                query("pur_supuser", "id,user.phone,user.email,user.username,bizpartner,bizpartner.id,user,user.id",
                        new QFilter[] {qFilter});
        result.addAll(supplierUserColl);
//        result = supplierUserColl != null && supplierUserColl.size() > 0 && supplierUserColl.get(0) != null ?
//                supplierUserColl.get(0) : null;
        return result;
    }

    /**
     * 批量发送邮件
     * @param receiverList
     * @return
     */
    public static void sendEmail(List<String> receiverList) {
        EmailInfo emailInfo = new EmailInfo();
        emailInfo.setTitle("更新库存通知");
        emailInfo.setContent("请更新库存信息！");
        emailInfo.setReceiver(receiverList);
        Map<String,Object> sendResult = EmailHandler.sendEmail(emailInfo);
        if(sendResult != null) {
            String code = GeneralFormatUtils.getString(sendResult.get("code"));
            if(!"0".equals(code)) {
                throw new KDException(sendResult.get("description").toString());
            }
        }
    }

    /**
     * 获取供应商对应的供货清单数据
     * @author: hst
     * @createDate: 2022/11/30
     * @param supplier
     * @return
     */
    private DataSet getMaterialBillData (DynamicObject supplier) {
        // 供应商编码过滤条件
        if (Objects.nonNull(supplier)) {
            QFilter statusFilter = new QFilter("status",QFilter.equals,"C");
            QFilter qFilter = new QFilter("yd_supnum", QFilter.equals,supplier.getString("number"));
            DataSet materialBills = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_supplymatlist",
                    "number,name,yd_supnum,yd_supname,yd_matnum,yd_matname,yd_unitname",new QFilter[]{qFilter,statusFilter},null);
            return materialBills;
        } else {
            return null;
        }
    }

    /**
     * 将DataSet转化为List
     * @author: hst
     * @createDate: 2022/11/30
     * @param dataSet
     * @return
     */
    private List<List<String>> dataSetTolist (DataSet dataSet) {
        // excelList用于提供数据源给 excel工作簿，外层的list是行，里面的List<String>是列，存储value
        List<List<String>> excelList = new ArrayList<>();
        // 添加列头
        List<String> titles = Arrays.asList(new String[]{"组织编码","组织名称","供应商编码","供应商名称","物料编码","物料名称","单位"});
        excelList.add(titles);
        // 查询的字段
        Field[] fields = dataSet.getRowMeta().getFields();
        // 解析DataSet数据
        while (dataSet.hasNext()) {
            Row row = dataSet.next();
            List<String> strings = new ArrayList<>();
            for (Field field : fields) {
                String value = row.getString(field.getName());
                strings.add(value);
            }
            excelList.add(strings);
        }
        return excelList;
    }

    /**
     * 将数据存储到excel中
     * @param excelList
     * @return
     */
    private XSSFWorkbook exportExcel (List<List<String>> excelList, String title) {
        //创建excel工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        //创建工作表sheet
        XSSFSheet sheet = workbook.createSheet();
        //设置默认列宽
        sheet.setDefaultColumnWidth(20);
        // 创建标题
        XSSFRow headRow = sheet.createRow(0);
        XSSFCell headCell = headRow.createCell(0);
        // 设置标题的值
        headCell.setCellValue(title);
        // 设置首行标题的一些风格样式
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        // 设置背景色
        //设置填充方案
//        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);;
        // 设置水平居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置字
        XSSFFont font = workbook.createFont();
        // 设置字号
        font.setFontHeight(20);
        // 设置字体
        font.setFontName("黑体");
        titleStyle.setFont(font);
        headCell.setCellStyle(titleStyle);
        // 合并第1行的前几列，合并列数 = excel的列数
        CellRangeAddress titleCellAddresses = new CellRangeAddress(0, 0, 0, excelList.get(0).size()-1);
        sheet.addMergedRegion(titleCellAddresses);
        // 普通单元格的style
        // 单据列表数据风格样式，设置字体为黑体，字号15
        XSSFCellStyle billStyle = workbook.createCellStyle();
        // 设置背景色以及填充方案
//        billStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 设置字体
        XSSFFont billFont = workbook.createFont();
        billFont.setFontName("宋体");
        billFont.setFontHeight(10);
        billStyle.setFont(billFont);
        // 第1列风格
        XSSFCellStyle firstColumnStyle = workbook.createCellStyle();
        XSSFFont firstColunmFont = workbook.createFont();
        firstColunmFont.setFontName("宋体");
        firstColunmFont.setFontHeight(10);
        firstColumnStyle.setFont(firstColunmFont);
        // 第1行风格
        XSSFCellStyle firstRowStyle = workbook.createCellStyle();
//        firstRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        firstRowStyle.setFont(billFont);
        //写入单据列表数据
        for (int i = 0; i < excelList.size(); i++) {
            // i+1是因为前面第1行加了一个标题，单据列表数据是从Excel的第2行开始的，所以要+1
            XSSFRow nrow = sheet.createRow(i+1);
            for (int u=0;u<excelList.get(i).size();u++){
                XSSFCell ncell = nrow.createCell(u);
                if (i == 0) {
                    ncell.setCellStyle(firstRowStyle);
                } else if (u == 0) {
                    ncell.setCellStyle(firstColumnStyle);
                } else {
                    ncell.setCellStyle(billStyle);
                }
                ncell.setCellValue(excelList.get(i).get(u));
            }
        }
        // 冻结前2行
        sheet.createFreezePane(0, 2, 0, 2);
        return workbook;
    }

    /**
     * @author: hst
     * @createDate: 2022/11/30
     * @param fileName
     * @param workbook
     * @return path 路径
     */
    private String upload (String fileName, XSSFWorkbook workbook) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        fileName =fileName + ".xlsx";
        try {
            OutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            InputStream inputStream = this.parse(outputStream);
            TempFileCache tempFileCache = CacheFactory.getCommonCacheFactory().getTempFileCache();
            String path = tempFileCache.saveAsUrl(fileName,inputStream,30000);
            return path;
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return "";
    }

    /**
     * outputStream转inputStream
     * @author: hst
     * @createDate: 2022/11/30
     * @param out outputStream
     * @return inputStream
     * @throws Exception
     */
    private ByteArrayInputStream parse(final OutputStream out) throws Exception {
        ByteArrayOutputStream baos = (ByteArrayOutputStream) out;
        final ByteArrayInputStream swapStream = new ByteArrayInputStream(baos.toByteArray());
        return swapStream;
    }

    /**
     * 获取供应商供货清单
     * @param supplier
     * @return
     */
    public String getMaterialBillExcel(DynamicObject supplier) {
        try {
            if (Objects.nonNull(supplier)) {
                // 查询数据
                DataSet dataSet = this.getMaterialBillData(supplier);
                // DataSet转化为List
                List<List<String>> excelList = this.dataSetTolist(dataSet);
                // 数据存储到excel中
                XSSFWorkbook excel = this.exportExcel(excelList,
                        supplier.getString("name") + "供货清单");
                String fileName = supplier.getString("name") + "供货清单"
                        + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                String path = this.upload(fileName, excel);
                return path;
            }
            return "";
        } catch (Exception e) {
            e.printStackTrace();
            throw new KDBizException("系统异常，请联系管理员！");
        }
    }
}
