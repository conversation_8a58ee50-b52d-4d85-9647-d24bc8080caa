package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.scp.constants.ScpSalOutStockConstants;
import kd.bos.tcbj.srm.scp.helper.ScpSalOutStockHelper;

import java.util.Arrays;
import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.scp.oplugin.SalOutStockAuditOp
 * @className SalOutStockAuditOp
 * @author: hst
 * @createDate: 2023/01/06
 * @description: 销售发货单审核逻辑操作插件
 * @version: v1.0
 */
public class SalOutStockAuditOp extends AbstractOperationServicePlugIn {

    /**
     * 加载字段
     * @author: hst
     * @createDate: 2023/01/13
     * @param e
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        e.getFieldKeys().add(ScpSalOutStockConstants.FIELD_BILLNO);                      // 单据编码
        e.getFieldKeys().add(ScpSalOutStockConstants.FIELD_DRIVER);                      // 送货人
        e.getFieldKeys().add(ScpSalOutStockConstants.FIELD_CONTACT);                     // 联系方式
        e.getFieldKeys().add(ScpSalOutStockConstants.FIELD_DELIDATE);                    // 预计到货日期
        e.getFieldKeys().add(ScpSalOutStockConstants.COLUMN_MATERIAL);                   // 物料编码
        e.getFieldKeys().add(ScpSalOutStockConstants.COLUMN_QTY);                         // 发货数量
        e.getFieldKeys().add(ScpSalOutStockConstants.COLUMN_UNIT);                        // 计量单位
        e.getFieldKeys().add(ScpSalOutStockConstants.COLUMN_POBILLNO);                   // 订单号
        e.getFieldKeys().add(ScpSalOutStockConstants.FILED_URL);                          // 预约送货台账移动端访问地址
        e.getFieldKeys().add(ScpSalOutStockConstants.FIELD_BILLDATE);                     // 业务日期
        e.getFieldKeys().add(ScpSalOutStockConstants.FIELD_SUPPLIER);                     // 供应商 update by hst 2023/05/16
    }

    /**
     * 修改为事务提交前
     * @author: hst
     * @createDate: 2023/01/13
     * @param e
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        StringBuffer errStr = new StringBuffer();
        DynamicObject[] bills = e.getDataEntities();
        Arrays.stream(bills).forEach(bill -> {
            try {
                new ScpSalOutStockHelper().createDeliverStand(bill);
            } catch (Exception exception) {
                errStr.append(bill.getString(ScpSalOutStockConstants.FIELD_BILLNO) + "、");
            }
        });
        if (errStr.length() > 0) {
            throw new KDBizException("单据" + errStr.deleteCharAt(errStr.length() - 1) + "生成预约发货台账失败，请联系管理员！");
        }
    }
}
