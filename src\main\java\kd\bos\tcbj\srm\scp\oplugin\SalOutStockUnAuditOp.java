package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.bos.tcbj.srm.scp.constants.ScpSalOutStockConstants;
import kd.bos.tcbj.srm.scp.helper.ScpSalOutStockHelper;
import kd.scm.common.util.ExceptionUtil;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;

/**
 * @package: kd.bos.tcbj.srm.scp.oplugin.SalOutStockUnAuditOp
 * @className SalOutStockUnAuditOp
 * @author: hst
 * @createDate: 2022/11/22
 * @description: 销售发货单撤销审核逻辑操作插件
 * @version: v1.0
 */
public class SalOutStockUnAuditOp extends AbstractOperationServicePlugIn {

    private final static String AUDIT_OP = "audit";
    private final static String UNAUDIT_OP = "unaudit";


    @Override
    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
        DynamicObject[] bills = e.getDataEntities();
        Set<String> billNoSet = new HashSet<String>();
        Arrays.stream(bills).forEach(bill -> {
            if ("C".equals(bill.getString(ScpSalOutStockConstants.FIELD_STATUS))) {
                // 删除下游单据
                new ScpSalOutStockHelper().deleteDeliverStand(bill);
                billNoSet.add(bill.getString("billno"));
            }
        });
        
        // 调用EAS接口删除销售发货单
        if (billNoSet.size() > 0) {
        	String msg = "500";
        	Map dataParam = new HashMap();  // 最外层报文Map
        	HashMap param = new HashMap();
        	String entityType = bills[0].getDataEntityType().getName();;
        	HashMap ex = new HashMap();
			ex.put("billno", billNoSet);
			param.put("data", ex);
			param.put("billtype", entityType);
			param.put("action", "reback");
			param.put("code", "200");
			
			dataParam.put("data", param);  // 调用EAS接口时，会只保留data中的参数，所以要再封装一层再调facade
			
			try {
				msg = ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "rebackSaleoutStockBill", dataParam).toString();
			} catch (Exception arg10) {
				System.out.println("销售发货单撤销失败" + ExceptionUtil.getStackTrace(arg10));
			}
			
			System.out.println("回撤销售发货单提示错误信息："+msg);
			
			try {
				Map respMap = (Map) JSONObject.parseObject(msg, Map.class);
				String isSuccess = respMap.get("readStatus").toString();
				if ("error".equalsIgnoreCase(isSuccess)) {
					List<Map<String,String>> list = (List<Map<String, String>>) respMap.get("readList");
					Set<String> errorBillnoSet = new HashSet<String>();
					StringBuffer errorMsg = new StringBuffer();
					for (Map<String, String> billMap : list) {
						String billNo = billMap.keySet().iterator().next();
						errorMsg.append("单据"+billNo+"回撤失败，"+billMap.get(billNo)+"；");
						errorBillnoSet.add(billNo);
					}
					
					// 清理单据推送EAS的标识
					for (DynamicObject bill : bills) {
						String billNo = bill.getString("billno");
						if (!errorBillnoSet.contains(billNo)) {
							DynamicObject tmpBill = BusinessDataServiceHelper.loadSingle(bill.getPkValue(),ScpSalOutStockConstants.ENTITY_MAIN);
							tmpBill.set("yd_istoeas", false);
							SaveServiceHelper.save(new DynamicObject[] {tmpBill});
						}
					}
					
					e.cancel = true;
					e.setCancelMessage("回撤失败，EAS返回原因如下："+errorMsg.toString());
					
				} else {  // 全部都回撤成功的
					// 清理单据推送EAS的标识
					for (DynamicObject bill : bills) {
						DynamicObject tmpBill = BusinessDataServiceHelper.loadSingle(bill.getPkValue(),ScpSalOutStockConstants.ENTITY_MAIN);
						tmpBill.set("yd_istoeas", false);
						SaveServiceHelper.save(new DynamicObject[] {tmpBill});
					}
				}
			} catch (Exception arg9) {
				System.out.println("销售发货单撤销失败" + ExceptionUtil.getStackTrace(arg9));
			}
		}
    }
}
