package kd.bos.tcbj.srm.scp.oplugin;

import java.util.List;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.util.StringUtils;

/**
 * 销售发货单操作类
 * @auditor yanzuliang
 * @date 2022年12月29日
 * 
 */
public class SaleOutStockOp extends AbstractOperationServicePlugIn {
	
	@Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> filds = e.getFieldKeys();
        filds.add("id");
        filds.add("billno");
        filds.add("person");
        filds.add("yd_purperson");
    }
	
	/**
	 * 操作处理方法
	 * 1、getpurperson获取采购员，在流程提交节点后配置，用于带出采购员用作第一个审批节点的审批人
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		
		String key = e.getOperationKey();
		if ("getpurperson".equalsIgnoreCase(key)) {
			DynamicObject[] objs = e.getDataEntities();
			if (objs.length == 0) return;
			for (DynamicObject obj : objs) {
				DynamicObject person = obj.getDynamicObject("person");
				if (person!=null) {
					person = BusinessDataServiceHelper.loadSingle(person.getPkValue(), "pur_bizperson");
					obj.set("yd_purperson", person.getDynamicObject("user"));
				}
				// 没有就默认一个采购员，获取参数配置
				if (obj.get("yd_purperson") == null) {
					DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting","name",
							new QFilter[]{new QFilter("number",QFilter.equals,"SCP_SALEOUTSTOCK_PURPERSON")});
					String value = param.getString("name");
					QFilter filter = new QFilter("username", QCP.equals, value);
					DynamicObject defaultPerson = BusinessDataServiceHelper.loadSingle("bos_user", "id,name,username", filter.toArray());
					obj.set("yd_purperson", defaultPerson);
				}
			}
			SaveServiceHelper.save(objs);
		}
	}
	
}
