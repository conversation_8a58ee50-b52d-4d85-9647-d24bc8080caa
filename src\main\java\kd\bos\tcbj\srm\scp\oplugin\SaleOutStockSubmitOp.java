package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.coderule.CodeRuleServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.scp.constants.ScpSalOutStockConstants;
import kd.bos.tcbj.srm.scp.helper.ScpSalOutStockHelper;
import kd.scm.common.util.CommonUtil;
import kd.scm.scp.opplugin.ScpOrderCfmStatusValidator;
import kd.scm.scp.opplugin.ScpSalOutSubmitOp;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.math.BigDecimal;
import java.util.*;

/**
 * 销售发货单发货插件触发
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-21
 */
public class SaleOutStockSubmitOp extends AbstractOperationServicePlugIn {

    private static final Log log = LogFactory.getLog(SaleOutStockSubmitOp.class);

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> filds = e.getFieldKeys();
        filds.add("supplier");
        filds.add("billno");
        filds.add("billdate");
        filds.add("delidate");
        filds.add("person");
        filds.add("pobillno");
        filds.add("materialentry.qty");
        filds.add("materialentry.pobillid");
        filds.add("materialentry.pobillno");
        filds.add("materialentry.poentryid");
        filds.add("materialentry.proddate"); // 生产日期
        filds.add("materialentry.duedate"); // 到期日
        filds.add("materialentry.id"); // 分录ID
        filds.add("materialentry.yd_cqentryid"); // 套打分录ID
        filds.add("materialentry.srcentryid"); // 源单分录ID
        filds.add("id");
        filds.add("yd_inflow");
        filds.add("yd_iserrlot");           // 是否异常批次 update by hst 2022/11/18
        filds.add("materialentry.yd_coa"); // coa文件     update by hst 2023/01/06
        filds.add("yd_deliveryerrortype"); //发货异常类型 update by hst 2023/01/09
        filds.add("materialentry.material");// 物料 update by hst 2023/05/22
        filds.add("yd_confirmrateup");  //超发 update by hst 2023/06/28
        filds.add("yd_version");        //版本号 update by hst 2023/06/28
    }

    public void onAddValidators(AddValidatorsEventArgs e) {
        e.addValidator(new ScpOrderCfmStatusValidator());
        // update by hst 2023/05/22 二开校验逻辑
        e.addValidator(new SaleOutStockSubmitValidator());
    }

    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
        List<ExtendedDataEntity> dataEntitys = e.getValidExtDataEntities();
        Iterator iterator = dataEntitys.iterator();

        while(iterator.hasNext()) {
            ExtendedDataEntity dataEntity = (ExtendedDataEntity)iterator.next();
            DynamicObject data = dataEntity.getDataEntity();
            Calendar calendar = Calendar.getInstance();
            Date deliDate = data.getDate("delidate");
            Date curDate = new Date();
            if (deliDate != null && deliDate.compareTo(curDate) <= 0) {
                calendar.setTime(curDate);
                calendar.add(11, 1);
                data.set("delidate", calendar.getTime());
            }

            String billNo = data.getString("billno");
            if (billNo == null) {
                data.set("billno", CodeRuleServiceHelper.getNumber(data.getDynamicObjectType().getName(), data, data.getString("org")));
            }

            // 增加个性化字段检验
            boolean isContinue = checkSend(data);
            data.set("yd_inflow", !isContinue); // 是否进入工作流，如果符合条件，则不进入工作流且置为审核，如果是不符合条件，则需要走流程
        }
    }

    public void afterExecuteOperationTransaction(AfterOperationArgs e) {

        List<Object> successPkIds = this.getOperationResult().getSuccessPkIds();
        DynamicObject[] dataCol = e.getDataEntities();
        // 获取要执行的数据
        List<Object> execList = new ArrayList<>();
        execList.addAll(successPkIds);
        
        // 将分录ID复制到套打字段上,yzl
        for (DynamicObject info : dataCol) {
        	if (info.getPkValue() == null) continue;
            DynamicObject tmpInfo = BusinessDataServiceHelper.loadSingle(info.getPkValue(), "scp_saloutstock");
            DynamicObjectCollection enCol = tmpInfo.getDynamicObjectCollection("materialentry");
            for (DynamicObject enObj : enCol) {
            	enObj.set("yd_cqentryid", enObj.getPkValue().toString());
			}
            SaveServiceHelper.save(new DynamicObject[] {tmpInfo});
        }

        // 遍历执行的数，如果存在要进入工作流的发货数据，则需要将当前要执行审批的单据移除
        for (DynamicObject info : dataCol) {
            if (info.getBoolean("yd_inflow")) {
                execList.remove(info.getPkValue());
            }
        }
        // 如果不存在执行的数据，则退出
        if (execList.size() == 0) return;

        OperateOption option = OperateOption.create();
        option.setVariableValue("isStrict", String.valueOf(Boolean.FALSE));
        if (this.getOption() != null) {
            option.setVariableValue("ishasright", this.getOption().getVariableValue("ishasright", String.valueOf(false)));
        }

//        StringBuffer errStr = new StringBuffer();
        try {
            OperationResult auditResult = OperationServiceHelper.executeOperate("audit", "scp_saloutstock", execList.toArray(), option);
            if (auditResult.isSuccess()) {
                log.info("提交即审核成功！");
                // update by hst 2022/11/22 审核成功后创建预约送货台账
//                DynamicObject[] bills = BusinessDataServiceHelper.load(auditResult.getSuccessPkIds().toArray(),
//                        BusinessDataServiceHelper.newDynamicObject(ScpSalOutStockConstants.ENTITY_MAIN).getDynamicObjectType());
//                Arrays.stream(bills).forEach(bill -> {
//                    try {
//                        new ScpSalOutStockHelper().createDeliverStand(bill);
//                    } catch (Exception exception) {
//                        errStr.append(bill.getString(ScpSalOutStockConstants.FIELD_BILLNO) + "、");
//                    }
//                });
            } else {
                log.info(auditResult.getMessage());
                OperationServiceHelper.executeOperate("unsubmit", "scp_saloutstock", this.getOperationResult().getSuccessPkIds().toArray(), option);
            }
        } catch (Exception var4) {
            OperationServiceHelper.executeOperate("unsubmit", "scp_saloutstock", this.getOperationResult().getSuccessPkIds().toArray(), option);
        }
        // update by hst 2022/11/22 输出生成预约送货台账失败的信息
//        if (errStr.length() > 0) {
//            throw new KDBizException("单据" + errStr.deleteCharAt(errStr.length() - 1) + "生成预约发货台账失败，请联系管理员！");
//        }
    }

    private boolean checkSend(DynamicObject info) {
        DynamicObjectCollection entryCol = info.getDynamicObjectCollection("materialentry");
        info.set("yd_deliveryerrortype", "0");  // 增加默认置为无异常，后面有异常就赋值，yzl,20230406
        for (DynamicObject entryInfo : entryCol) {
            //是否异常批次 update by hst 2022/11/18 是否异常批次校验
            if (info.getBoolean("yd_iserrlot")) {
                //不允许发货
                return false;
            }
            // 计算总有效日期
            Date proddate = entryInfo.getDate("proddate");// 生产日期
            Date duedate = entryInfo.getDate("duedate");// 到日期
            if (proddate == null || duedate == null) continue;
            Boolean dayError = false;
            // 如果没有COA的也不允许发货
            Boolean coaError = false;
            // update by hst 2023/05/22 物料编码非D和E开头的才判断是否进工作流
            DynamicObject material = entryInfo.getDynamicObject("material");
            if (Objects.nonNull(material)) {
                String matNum = material.getString("number");
                if (!matNum.startsWith("D") && !matNum.startsWith("E")) {
                    long diffDay = DateTimeUtils.diffDay(proddate, duedate); // 总有效日期天数
                    // 计算出生产日期到今天数
                    long validDay = DateTimeUtils.diffDay(proddate, new Date()); // 时效数
                    if (diffDay <= validDay * 2) {
                        // 有效日期不满足不允许发货
                        info.set("yd_deliveryerrortype", "1");
                        dayError = true;
//                      return false;
                    }
                    // 没有COA的也不允许发货
                    if (entryInfo.get("yd_coa") == null || entryInfo.getDynamicObjectCollection("yd_coa").size() == 0) {
                        info.set("yd_deliveryerrortype", "2");
                        coaError = true;
//            	        return false;
                    }
                }
            }
            
            // 如果两种都有就标记为类型3
            if (dayError && coaError) {
            	info.set("yd_deliveryerrortype", "3");
            }
            // 如果任何一个缺失都返回错误
            if (dayError || coaError) {
            	return false;
            }
            
            // 如果超收的也要进入工作流,yzl,20230425
            if (entryInfo.getBoolean("yd_confirmrateup")) {
            	info.set("yd_deliveryerrortype", "4");
            	return false;
            }
            
        }
        return true;
    }
}

/**
 * 确认发货校验逻辑
 * @author: hst
 * @createDate: 2023/05/22
 */
class SaleOutStockSubmitValidator extends AbstractValidator {

    @Override
    public void validate() {
        ExtendedDataEntity[] dataEntities = this.getDataEntities();
        for (ExtendedDataEntity dataEntity : dataEntities) {
            DynamicObject bill = dataEntity.getDataEntity();
            DynamicObjectCollection entries = bill.getDynamicObjectCollection("materialentry");
            // update by hst 2024-05-24 超发数量计算有误
            Map<String,BigDecimal> qtyMap = new HashMap<>();
            for (int i = 0; i < entries.size(); i++) {
                DynamicObject material = entries.get(i).getDynamicObject("material");
                if (Objects.nonNull(material)) {
                    String matNum = material.getString("number");
                    // 物料编码非D和E开头
                    if (!matNum.startsWith("D") && !matNum.startsWith("E")) {
                        Date duedate = entries.get(i).getDate("duedate");
                        // 到期日期必填
                        if (Objects.isNull(duedate)) {
                            this.addErrorMessage(dataEntity,String.format(ResManager.loadKDString("请填写第" + (i + 1)
                                            + "行分录编码非D和E开头的物料的到期日期", "SaleOutStockSubmitValidator_1", "yd_srm")));
                        }
                    }

                    DynamicObject entryDynamicObject = entries.get(i);
                    String key = entryDynamicObject.getString("pobillno") + "&" + entryDynamicObject.getLong("srcentryid");
                    if (qtyMap.containsKey(key)) {
                        qtyMap.put(key,qtyMap.get(key).add(CommonUtil.getBigDecimalPro(Long.valueOf(entryDynamicObject.getLong("qty")))));
                    } else {
                        qtyMap.put(key,CommonUtil.getBigDecimalPro(Long.valueOf(entryDynamicObject.getLong("qty"))));
                    }
                    // update by hst 2024-05-23
//                    BigDecimal newQty = CommonUtil.getBigDecimalPro(Long.valueOf(entryDynamicObject.getLong("qty")));
//    				DynamicObject scpOrderObject = getScpOrderObject(entryDynamicObject.getString("pobillno"),
//    						Long.valueOf(entryDynamicObject.getLong("srcentryid")));
//    				BigDecimal sumOutStockQty = BigDecimal.ZERO;
//    				if (scpOrderObject != null) {
//    					BigDecimal rate = BigDecimal.ZERO;
//    					if (scpOrderObject.getBigDecimal("saloutrateup") != null && scpOrderObject.getBigDecimal("saloutrateup").compareTo(BigDecimal.ZERO) > 0) {
//    						rate = scpOrderObject.getBigDecimal("saloutrateup").divide(new BigDecimal(100)).setScale(6);
//    					}
//    					// 数量乘以1+比例得到发货上限数量
//    					BigDecimal saloutqtyup = scpOrderObject.getBigDecimal("qty").multiply(BigDecimal.ONE.add(rate));
//    					// 如果发货数量大于入库数量，则用入库数量进行扣减后校验，要再加上退货数量,20230807,yzl
//    					if (scpOrderObject.getBigDecimal("sumoutstockqty").compareTo(scpOrderObject.getBigDecimal("suminstockqty")) > 0) {
//    						sumOutStockQty = saloutqtyup.subtract(scpOrderObject.getBigDecimal("sumoutstockqty")).add(scpOrderObject.getBigDecimal("sumrecretqty"));
//    					} else {
//    						sumOutStockQty = saloutqtyup.subtract(scpOrderObject.getBigDecimal("suminstockqty")).add(scpOrderObject.getBigDecimal("sumrecretqty"));
//    					}
//    					if (newQty.compareTo(sumOutStockQty) > 0) {
//    						if (rate.compareTo(BigDecimal.ZERO) > 0) {
//    							this.addErrorMessage(dataEntity,String.format(ResManager.loadKDString("发货明细分录中第" + (i + 1)
//    									+ "行已超出发货数量的10％，不允许发货！", "SaleOutStockSubmitValidator_1", "yd_srm")));
//    						} else {
//    							this.addErrorMessage(dataEntity,String.format(ResManager.loadKDString("发货明细分录中第" + (i + 1)
//    									+ "行已超出订单数量，不允许发货！", "SaleOutStockSubmitValidator_1", "yd_srm")));
//    						}
//    					}
//    				}
                }
            }

            for (Map.Entry<String,BigDecimal> map : qtyMap.entrySet()) {
                String key = map.getKey();
                if (key.contains("&") && key.split("&").length == 2) {
                    String poBillNo = key.split("&")[0];
                    String srcentryid = key.split("&")[1];
                    BigDecimal newQty = map.getValue();
    				DynamicObject scpOrderObject = getScpOrderObject(poBillNo, Long.valueOf(srcentryid));
    				BigDecimal sumOutStockQty = BigDecimal.ZERO;
    				if (scpOrderObject != null) {
    					BigDecimal rate = BigDecimal.ZERO;
    					if (scpOrderObject.getBigDecimal("saloutrateup") != null && scpOrderObject.getBigDecimal("saloutrateup").compareTo(BigDecimal.ZERO) > 0) {
    						rate = scpOrderObject.getBigDecimal("saloutrateup").divide(new BigDecimal(100)).setScale(6);
    					}
    					// 数量乘以1+比例得到发货上限数量
    					BigDecimal saloutqtyup = scpOrderObject.getBigDecimal("qty").multiply(BigDecimal.ONE.add(rate));
    					// 如果发货数量大于入库数量，则用入库数量进行扣减后校验，要再加上退货数量,20230807,yzl
    					if (scpOrderObject.getBigDecimal("sumoutstockqty").compareTo(scpOrderObject.getBigDecimal("suminstockqty")) > 0) {
    						sumOutStockQty = saloutqtyup.subtract(scpOrderObject.getBigDecimal("sumoutstockqty")).add(scpOrderObject.getBigDecimal("sumrecretqty"));
    					} else {
    						sumOutStockQty = saloutqtyup.subtract(scpOrderObject.getBigDecimal("suminstockqty")).add(scpOrderObject.getBigDecimal("sumrecretqty"));
    					}
    					if (newQty.compareTo(sumOutStockQty) > 0) {
    						if (rate.compareTo(BigDecimal.ZERO) > 0) {
    							this.addErrorMessage(dataEntity,String.format(ResManager.loadKDString("发货明细分录中物料："
                                        + scpOrderObject.getString("materialentry.material.number")
    									+ "已超出发货数量的" + rate.multiply(new BigDecimal(100)).setScale(2) + "％，不允许发货！", "SaleOutStockSubmitValidator_1", "yd_srm")));
    						} else {
    							this.addErrorMessage(dataEntity,String.format(ResManager.loadKDString("发货明细分录中物料：" +
                                        scpOrderObject.getString("materialentry.material.number")
    									+ "已超出订单数量，不允许发货！", "SaleOutStockSubmitValidator_1", "yd_srm")));
    						}
    					}
    				}
                }
            }
        }
    }
    
    private static DynamicObject getScpOrderObject(String billNo, Long materialId) {
		QFilter filter = new QFilter("billno", "=", billNo);
		filter.and("materialentry.id", "=", materialId);
		String selectFields = "materialentry.material.number,materialentry.sumoutstockqty as sumOutStockQty,materialentry.qty as qty,materialentry.iscontrolqty as iscontrolqty,materialentry.saloutrateup as saloutrateup,materialentry.suminstockqty as suminstockqty,materialentry.sumrecretqty as sumrecretqty";
		DynamicObject dynamicObject = QueryServiceHelper.queryOne("scp_order", selectFields, new QFilter[]{filter});
		return dynamicObject;
	}
}


