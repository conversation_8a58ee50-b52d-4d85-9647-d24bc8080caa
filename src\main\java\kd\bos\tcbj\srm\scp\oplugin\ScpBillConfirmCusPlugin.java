package kd.bos.tcbj.srm.scp.oplugin;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.scm.common.enums.ConfirmStatusEnum;
import kd.scm.common.isc.util.ApiOperateUtil;
import kd.scm.common.util.ApiConfigUtil;
import kd.scm.common.util.ExceptionUtil;

/**
 * 苍穹供应商端采购订单操作插件类
 * @auditor yanzuliang
 * @date 2022年10月12日
 * 
 */
public class ScpBillConfirmCusPlugin extends AbstractOperationServicePlugIn {
	
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		List filds = e.getFieldKeys();
		filds.add("logstatus");
		filds.add("qty");
		filds.add("basicqty");
		filds.add("pobillid");
		filds.add("poentryid");
		filds.add("pobillno");
		filds.add("billno");
		filds.add("supplier");
		filds.add("person");
		filds.add("srctype");
		filds.add("srcbillid");
		filds.add("srcentryid");
		filds.add("sumreceiptqty");
		filds.add("suminstockqty");
		filds.add("yd_becheckedattach");
		filds.add("yd_deliveryplan.yd_orderid");
		filds.add("yd_deliveryplan.yd_supdeliverydate");
		filds.add("yd_deliveryplan.yd_deliveryqty");
	}
	
	/**
	 * 操作校验通过，开启了事务之后，还没有把数据提交到数据库之前触发此事件
	 * <AUTHOR>
	 * @date 2023-02-24
	 */
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		String key = e.getOperationKey();
		
		if ("testop".equalsIgnoreCase(key)) {
			System.out.println("beginOperationTransaction");
			DynamicObject[] datas = e.getDataEntities();
			for (DynamicObject tmpBillObj : datas) {
				String billId = tmpBillObj.get("id").toString();
				DynamicObject billObj = BusinessDataServiceHelper.loadSingle(billId, "scp_order");
				billObj.set("yd_becheckedattach", tmpBillObj.get("yd_becheckedattach"));
				DynamicObjectCollection planCol = billObj.getDynamicObjectCollection("yd_deliveryplan");
				
				DynamicObjectCollection tmpPlanCol = tmpBillObj.getDynamicObjectCollection("yd_deliveryplan");
				for (DynamicObject tmpPlanObj : tmpPlanCol) {
					for (DynamicObject planObj : planCol) {
						if (tmpPlanObj.get("id").toString().equalsIgnoreCase(planObj.getString("id"))) {
							planObj.set("yd_supdeliverydate", tmpPlanObj.getDate("yd_supdeliverydate"));
							System.out.println("打印实际日期："+tmpPlanObj.getDate("yd_supdeliverydate"));
						}
					}
					
				}
				
				SaveServiceHelper.save(new DynamicObject[] {billObj});
			}
		}
	}
	
	/**
	 * 启动事务操作事件，调用EAS功能接口推送EAS采购订单到WMS
	 */
	@Override
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		super.beforeExecuteOperationTransaction(e);
		System.out.println("beforeExecuteOperationTransaction");
		
		String key = e.getOperationKey();
		DynamicObject[] objs = e.getDataEntities();
		e.getValidExtDataEntities();
		String entityType = null;
		if (objs.length != 0) {
			entityType = objs[0].getDataEntityType().getName();
			HashMap param = new HashMap();
			
			// 确认在途计划，反写单据状态并推送EAS
			if ("updatepurzaitu".equalsIgnoreCase(key) && ApiConfigUtil.hasEASConfig()) {
				Set billnoSet = this.getBillnoStr(objs, "billno");
				String msg = "500";
				
				Map dataParam = new HashMap();
				
				if (billnoSet.size() > 0) {
					HashMap ex = new HashMap();
					ex.put("billno", billnoSet);
					param.put("data", ex);
					param.put("billtype", entityType);
					param.put("action", "zaituconfirm");
					param.put("code", "200");
					
					dataParam.put("data", param);

					try {
						msg = ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doOrderZaituConfirm", dataParam).toString();
//						msg = ApiOperateUtil.doExec(param, "facade://com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doOrderZaituConfirm");
					} catch (Exception arg10) {
						System.out.println("订单交货计划确认失败" + ExceptionUtil.getStackTrace(arg10));
					}

				}

				if ("500".equals(msg)) {
					e.cancel = true;
					e.setCancelMessage(ResManager.loadKDString("确认失败，请联系管理员！", "ScpBillConfirmCusPlugin_0",
							"scm-scp-opplugin", new Object[0]));
				} else if (!"500".equals(msg) && msg.length() > 0) {
					try {
						Map ex1 = (Map) JSONObject.parseObject(msg, Map.class);
						boolean IsSuccess = ((Boolean) ex1.get("IsSuccess")).booleanValue();
						if (!IsSuccess) {
							e.cancel = true;
							e.setCancelMessage(ResManager.loadKDString("确认失败，请联系管理员！", "ScpBillConfirmCusPlugin_0",
									"scm-scp-opplugin", new Object[0]));
						}
					} catch (Exception arg9) {
						System.out.println("订单交货计划确认失败" + ExceptionUtil.getStackTrace(arg9));
					}
				}
			}

		}
	}
	
	/**
	 * 供应商确认交货计划
	 */
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		
		DynamicObject[] objs = e.getDataEntities();
		String key = e.getOperationKey();
		if ("updatepurzaitu".equalsIgnoreCase(key)) {
			Set<String> billnoSet = this.getBillnoStr(objs, "billno");
			String selectFields = "id,billno,yd_supplyconfirm";
			QFilter filter = new QFilter("billno", "in", billnoSet);
			DynamicObject[] bills = BusinessDataServiceHelper.load("scp_order", selectFields, new QFilter[]{filter});
			for(int i=0;i<bills.length;i++) {
				bills[i].set("yd_supplyconfirm", true);
			}
			if (bills.length > 0) {
				SaveServiceHelper.save(bills);
			}
		}
		
		// 因为反写EAS单据状态的方法是在before，从而因为状态没反写触发WMS的时候没触发到，现在改成放到after执行,yzl,20230426
		if (("agreeorder".equalsIgnoreCase(key)||"confirm".equalsIgnoreCase(key)||"notifywms".equalsIgnoreCase(key)) && ApiConfigUtil.hasEASConfig()) {
			String entityType = objs[0].getDataEntityType().getName();
			HashMap param = new HashMap();
			Set billnoSet = this.getBillnoStr(objs, "billno");
			String msg = "500";
			Map dataParam = new HashMap();
			if (billnoSet.size() > 0) {
				HashMap ex = new HashMap();
				ex.put("billno", billnoSet);
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Map<String,List<Map<String,String>>> dataMap = new HashMap<String,List<Map<String,String>>>();
				for (DynamicObject tmpObj : objs) {
					List<Map<String,String>> zaituList = new ArrayList<Map<String,String>>();
					
					String billNo = tmpObj.getString("billno");
					DynamicObjectCollection planCol = tmpObj.getDynamicObjectCollection("yd_deliveryplan");
					for (DynamicObject planObj : planCol) {
						Map<String,String> enMap = new HashMap<String,String>();
						enMap.put("poenId", planObj.getString("yd_orderid"));
						enMap.put("planQty", planObj.getString("yd_deliveryqty"));
						// update by hst 2024/08/19
						Date deliveryDate = planObj.getDate("yd_supdeliverydate");
						if (Objects.nonNull(deliveryDate)) {
							enMap.put("deliveryDate", sdf.format(planObj.getDate("yd_supdeliverydate")));
						} else {
							enMap.put("deliveryDate", "");
						}
						enMap.put("remark", sdf.format(new Date()) + "供应商确认交期");
						zaituList.add(enMap);
					}
					dataMap.put(billNo, zaituList);
				}
				
				ex.put("dataMap", dataMap);
				
				param.put("data", ex);
				param.put("billtype", entityType);
				param.put("action", "confirm_cus");
				param.put("code", "200");
				
				dataParam.put("data", param);  // 调用EAS接口时，会只保留data中的参数，所以要再封装一层再调facade
				
				try {
					msg = ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doOrderConfirm", dataParam).toString();
				} catch (Exception arg10) {
					System.out.println("订单确认失败" + ExceptionUtil.getStackTrace(arg10));
				}
				
			}
			
//			if ("500".equals(msg)) {
//				e.cancel = true;
//				e.setCancelMessage(ResManager.loadKDString("确认失败，请联系管理员！", "ScpBillConfirmCusPlugin_0",
//						"scm-scp-opplugin", new Object[0]));
//			} else if (!"500".equals(msg) && msg.length() > 0) {
//				try {
//					Map ex1 = (Map) JSONObject.parseObject(msg, Map.class);
//					boolean IsSuccess = ((Boolean) ex1.get("IsSuccess")).booleanValue();
//					if (!IsSuccess) {
//						e.cancel = true;
//						e.setCancelMessage(ResManager.loadKDString("确认失败，请联系管理员！", "ScpBillConfirmCusPlugin_0",
//								"scm-scp-opplugin", new Object[0]));
//					}
//				} catch (Exception arg9) {
//					System.out.println("订单确认失败" + ExceptionUtil.getStackTrace(arg9));
//				}
//			}
		}
	}
	
	private Set<String> getBillnoStr(DynamicObject[] objs, String billnoProperty) {
		HashSet numbers = new HashSet();
		DynamicObject[] arg3 = objs;
		int arg4 = objs.length;

		for (int arg5 = 0; arg5 < arg4; ++arg5) {
			DynamicObject dynamicObject = arg3[arg5];
			String billno = dynamicObject.getString(billnoProperty);
			if (!numbers.contains(billno) || billno.trim().length() > 0) {
				numbers.add(billno);
			}
		}

		return numbers;
	}
}
