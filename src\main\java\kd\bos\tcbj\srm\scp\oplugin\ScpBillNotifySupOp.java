package kd.bos.tcbj.srm.scp.oplugin;

import java.util.Objects;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.taxc.common.util.StringUtil;

/**
 * 苍穹接收到协同类单据后发送短信通知供应商-公共类方法
 * @auditor yanzuliang
 * @date 2022年11月9日
 * 
 */
public class ScpBillNotifySupOp extends AbstractOperationServicePlugIn {
	
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		
		if (e.getDataEntities().length == 0) {
			return;
		}
		String billName = e.getDataEntities()[0].getDataEntityType().getName();
		String billRealName = "单据";
		String supFieldName = "supplier";
		String businessName = "businesstype";
		String menuName = "订单管理-订单查询";
		switch (billName) {
		case "pur_order":
			supFieldName = "supplier";
			billRealName = "采购订单";
			businessName = "businesstype";
			menuName = "订单管理-订单查询";
			break;
		
		case "pur_ordchange":
			supFieldName = "supplier";
			billRealName = "采购订单变更单";
			businessName = "businesstype";
			menuName = "订单管理-订单变更";
			break;
		default:
			break;
		}
		
		if ("notifysup".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				// 如果业务类型是110、130的才需要通知供应商
				DynamicObject businessObj = bill.getDynamicObject(businessName);
				DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name",
	                    new QFilter[]{QFilter.of("number = ? and status = ?", "SRM_PUR_BUSINESS_NUM", "C")});
	            if (businessObj != null && !Objects.isNull(param)) {
	            	String value = param.getString("name");
	            	if (!value.contains(businessObj.getString("number"))) {
	            		continue;
	            	}
	            }
				DynamicObject supObj = bill.getDynamicObject(supFieldName);
				//获取对应供应商用户数据
                DynamicObjectCollection supplierUserCol = SupplyStockHelper.getSrmSupplierUsersBySupplierId(supObj.getString("id"));
                if (supplierUserCol != null) {
                	for (DynamicObject supplierUserInfo : supplierUserCol) {
						// update by hst 2023/06/16 增加通过邮件通知供应商，通知内容与短信的一致
						String context = "已接收到来自汤臣倍健的"+billRealName+"，单据单号为" + bill.getString("billno") + "，请及时登录汤臣倍健SRM系统确认单据，系统菜单路径为："+menuName+"，谢谢！";
                		if (StringUtil.isNotBlank(supplierUserInfo.getString("user.phone"))) {
                			String phone = supplierUserInfo.getString("user.phone");
                			MessageUtil.sendShortMessage(phone, context);
                		}
						if (StringUtil.isNotBlank(supplierUserInfo.getString("user.email"))) {
							String email = supplierUserInfo.getString("user.email");
							MessageUtil.sendEmail(email,billRealName,context);
						}
					}
                }
				
			}
		}
	}
}
