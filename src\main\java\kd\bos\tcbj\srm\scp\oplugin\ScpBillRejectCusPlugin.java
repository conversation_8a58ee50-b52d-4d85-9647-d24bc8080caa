package kd.bos.tcbj.srm.scp.oplugin;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.scm.common.util.ApiConfigUtil;
import kd.scm.common.util.ExceptionUtil;

/**
 * 苍穹供应商订单打回操作
 * @auditor yanzuliang
 * @date 2022年10月15日
 * 
 */
public class ScpBillRejectCusPlugin extends AbstractOperationServicePlugIn {
	
	private static Log log = LogFactory.getLog(ScpBillRejectCusPlugin.class);

	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		List filds = e.getFieldKeys();
		filds.add("pobillno");
		filds.add("logstatus");
		filds.add("pobillid");
		filds.add("billno");
		filds.add("supplier");
		filds.add("person");
		filds.add("poentryid");
		filds.add("srcbilltype");
		filds.add("srcbillid");
		filds.add("materialentry");
		filds.add("qty");
		filds.add("rejectreason");
		filds.add("yd_isnumerror");
		filds.add("yd_isdateerr");
	}
	
	/**
	 * 启动事务操作事件，调用EAS功能接口反写打回原因到采购订单上
	 */
	@Override
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		super.beforeExecuteOperationTransaction(e);
		
	}
	
	/**
	 * 启动事务操作事件，调用EAS功能接口反写打回原因到采购订单上
	 */
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		
		String key = e.getOperationKey();
		DynamicObject[] objs = e.getDataEntities();
		String entityType = null;
		if (objs.length != 0) {
			entityType = objs[0].getDataEntityType().getName();
			HashMap param = new HashMap();
			if ("rejectreason".equalsIgnoreCase(key) && ApiConfigUtil.hasEASConfig()) {
				Set billnoSet = this.getBillnoStr(objs, "billno");
				
				String rejectReason = objs[0].getString("rejectreason");  // 打回原因
				Boolean isnumerror = objs[0].getBoolean("yd_isnumerror");  // 交货数量不满足
				Boolean isdateerr = objs[0].getBoolean("yd_isdateerr");  // 交货日期不满足
				
				Map dataParam = new HashMap();
				
				String msg = "500";
				if (billnoSet.size() > 0) {
					HashMap ex = new HashMap();
					ex.put("billno", billnoSet);
					param.put("data", ex);
					param.put("billtype", entityType);
					param.put("action", "reject");
					param.put("code", "200");
					
					param.put("rejectReason", rejectReason);  // 打回原因
					param.put("isnumerror", isnumerror);  // 交货数量不满足
					param.put("isdateerr", isdateerr);  // 交货日期不满足
					
					dataParam.put("data", param);

					try {
						msg = ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doOrderReject", dataParam).toString();
					} catch (Exception arg10) {
						System.out.println("订单打回失败" + ExceptionUtil.getStackTrace(arg10));
					}

				}

				if ("500".equals(msg)) {
//					e.cancel = true;
//					e.setCancelMessage(ResManager.loadKDString("打回失败，请联系管理员！", "ScpBillRejectCusPlugin_0", "scm-scp-opplugin", new Object[0]));
					log.error(ResManager.loadKDString("打回失败，请联系管理员！", "ScpBillRejectCusPlugin_0", "scm-scp-opplugin", new Object[0]));
				} else if (!"500".equals(msg) && msg.length() > 0) {
					try {
						Map ex1 = (Map) JSONObject.parseObject(msg, Map.class);
						boolean IsSuccess = ((Boolean) ex1.get("IsSuccess")).booleanValue();
						if (!IsSuccess) {
							log.info(ResManager.loadKDString("打回失败，请联系管理员！", "ScpBillRejectCusPlugin_0",
									"scm-scp-opplugin", new Object[0]));
//							e.cancel = true;
//							e.setCancelMessage(ResManager.loadKDString("打回失败，请联系管理员！", "ScpBillRejectCusPlugin_0",
//									"scm-scp-opplugin", new Object[0]));
						}
					} catch (Exception arg9) {
						System.out.println("订单打回失败" + ExceptionUtil.getStackTrace(arg9));
					}
				}
			}

		}
	}
	
	private Set<String> getBillnoStr(DynamicObject[] objs, String billnoProperty) {
		HashSet numbers = new HashSet();
		DynamicObject[] arg3 = objs;
		int arg4 = objs.length;

		for (int arg5 = 0; arg5 < arg4; ++arg5) {
			DynamicObject dynamicObject = arg3[arg5];
			String billno = dynamicObject.getString(billnoProperty);
			if (!numbers.contains(billno) || billno.trim().length() > 0) {
				numbers.add(billno);
			}
		}

		return numbers;
	}
}
