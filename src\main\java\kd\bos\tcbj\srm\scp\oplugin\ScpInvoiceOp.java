package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.tcbj.srm.scp.helper.ScpInvoiceHelper;

/**
 * 开票单操作插件
 * @package kd.bos.tcbj.srm.scp.oplugin.ScpInvoiceOp
 * @className ScpInvoiceOp
 * @author: hst
 * @createDate: 2024/05/13
 * @version: v1.0
 */
public class ScpInvoiceOp extends AbstractOperationServicePlugIn {

    // 提交操作插件
    private final static String SUBMIT_OP = "submit";
    // 撤销操作
    private final static String UNSUBMIT_OP = "unsubmit";
    // 反审核操作
    private final static String UNAUDIT_OP = "unaudit";

    /**
     * 预加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/05/13
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("sumtaxamount");
        e.getFieldKeys().add("invamount");
        e.getFieldKeys().add("invtax");
        e.getFieldKeys().add("tax1");
        e.getFieldKeys().add("invoiceamount");
        e.getFieldKeys().add("remark");
    }

    /**
     * 事务开启后
     * @param e
     * @author: hst
     * @createDate: 2024/05/13
     */
    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        super.beginOperationTransaction(e);
        String key = e.getOperationKey();
        DynamicObject[] bills = e.getDataEntities();
        switch (key) {
            case SUBMIT_OP : {
                // 记录开票单应收金额与开票金额之差
                new ScpInvoiceHelper().recordAmountDifference(bills);
                break;
            }
            case UNSUBMIT_OP : {
                // 清除开票单应收金额与开票金额之差的记录
                new ScpInvoiceHelper().clearAmountDifference(bills);
                break;
            }
            case UNAUDIT_OP : {
                // 清除开票单应收金额与开票金额之差的记录
                new ScpInvoiceHelper().clearAmountDifference(bills);
                break;
            }
        }
    }
}
