package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.scp.util.EASUtil;

import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 进口物料预付申请单操作插件
 */
public class ScpMaterialLoanBillOp extends AbstractOperationServicePlugIn {

    /**
     * 预加载字段
     * @param e
     * @author: pjl
     * @createDate: 2025/02/18
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add("billno");
    }

    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        super.beginOperationTransaction(e);
        String key = e.getOperationKey();
        DynamicObject[] objs = e.getDataEntities();
        for (DynamicObject obj : objs) {
            if("unaudit".equals(key)){
                String sql = "select fnumber status from T_SM_PurOrder where fnumber=?";
                String billno = obj.getString("billno");
                //值列表
                List<Object> values = Arrays.asList(billno);
                //类型
                List<Integer> types = Arrays.asList(Types.VARCHAR);
                List<Map<String, Object>> data = EASUtil.executeQuerySQL(sql, values, types, 1);
                if (data.size() > 0) {
                    throw new KDBizException("Eas存在下游采购订单，请删除过后在进行反核准！");
                }

                boolean isExistDaily = QueryServiceHelper.exists("er_dailyloanbill", QFilter.of("yd_topkey = ?", billno).toArray());
                if(isExistDaily){
                    throw new KDBizException("存在下游预付款请款单，请删除过后在进行反核准！");
                }
            }
        }


    }
}
