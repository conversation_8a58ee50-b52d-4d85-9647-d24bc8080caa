package kd.bos.tcbj.srm.scp.oplugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.scm.common.util.ExceptionUtil;

/**
 * 采购订单变更插件类
 * @auditor yanzuliang
 * @date 2022年10月28日
 */
public class ScpOrderChangeConfirmCusPlugin extends AbstractOperationServicePlugIn {
	
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		List fieldsKeys = e.getFieldKeys();
		fieldsKeys.add("pobillno");
		fieldsKeys.add("materialentry.entrydelidate");
		fieldsKeys.add("materialentry.poentryid");
		fieldsKeys.add("pobillid");
		fieldsKeys.add("materialentry.qty");
		fieldsKeys.add("billstatus");
		e.setFieldKeys(fieldsKeys);
	}
	
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		super.beforeExecuteOperationTransaction(e);
		HashMap logStatusMap = new HashMap();
		String key = e.getOperationKey();
		DynamicObject[] orderChangeObjs = e.getDataEntities();
		String entityType = this.billEntityType.getName();
		if (("agreeorderchange".equalsIgnoreCase(key) || "confirm".equalsIgnoreCase(key))
				&& entityType.equals("scp_ordchange")) {
			int idList = orderChangeObjs.length;
			
			Set billnoSet = this.getBillnoStr(orderChangeObjs, "pobillno");
			String msg = "500";
			Map dataParam = new HashMap();
			if (billnoSet.size() > 0) {
				HashMap param = new HashMap();
				HashMap ex = new HashMap();
				
				ex.put("billno", billnoSet);
				param.put("data", ex);
				param.put("billtype", entityType);
				param.put("action", "alertConfirm_cus");
				param.put("code", "200");
				
				dataParam.put("data", param);  // 调用EAS接口时，会只保留data中的参数，所以要再封装一层再调facade

				try {
					msg = ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doOrderAlertConfirm", dataParam).toString();
				} catch (Exception arg10) {
					System.out.println("订单变更确认失败" + ExceptionUtil.getStackTrace(arg10));
				}

			}

			if ("500".equals(msg)) {
				e.cancel = true;
				e.setCancelMessage(ResManager.loadKDString("变更确认失败，请联系管理员！", "ScpOrderChangeConfirmCusPlugin_0",
						"scm-scp-opplugin", new Object[0]));
			} else if (!"500".equals(msg) && msg.length() > 0) {
				try {
					Map ex1 = (Map) JSONObject.parseObject(msg, Map.class);
					boolean IsSuccess = ((Boolean) ex1.get("IsSuccess")).booleanValue();
					if (!IsSuccess) {
						e.cancel = true;
						e.setCancelMessage(ResManager.loadKDString("变更确认失败，请联系管理员！", "ScpOrderChangeConfirmCusPlugin_0",
								"scm-scp-opplugin", new Object[0]));
					}
				} catch (Exception arg9) {
					System.out.println("订单变更确认失败" + ExceptionUtil.getStackTrace(arg9));
				}
			}
		}
	}
	
	private Set<String> getBillnoStr(DynamicObject[] objs, String billnoProperty) {
		HashSet numbers = new HashSet();
		DynamicObject[] arg3 = objs;
		int arg4 = objs.length;

		for (int arg5 = 0; arg5 < arg4; ++arg5) {
			DynamicObject dynamicObject = arg3[arg5];
			String billno = dynamicObject.getString(billnoProperty);
			if (!numbers.contains(billno) || billno.trim().length() > 0) {
				numbers.add(billno);
			}
		}

		return numbers;
	}
}
