package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.EndOperationTransactionArgs;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.scp.constants.ScpPurCycleConstant;

/**
 * @package: kd.bos.tcbj.srm.scp.oplugin.ScpPurCycleOp
 * @className: ScpPurCycleOp
 * @description: 供应商采购周期单操作插件
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 */
public class ScpPurCycleOp extends AbstractOperationServicePlugIn {

    /**
     * 加载字段
     * @param e
     * @author: hst
     * @createDate: 2024/06/08
     */
    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        super.onPreparePropertys(e);
        e.getFieldKeys().add(ScpPurCycleConstant.CONSTATUS_FIELD);
    }

    // 审核操作
    private final static String AUDIT_OP = "audit";
    // 反审核操作
    private final static String UNAUDIT_OP = "unaudit";

    /**
     * 操作逻辑执行完，事务提交前
     * @param e
     */
    @Override
    public void endOperationTransaction(EndOperationTransactionArgs e) {
        super.endOperationTransaction(e);
        DynamicObject[] bills = e.getDataEntities();
        String key = e.getOperationKey();
        switch (key) {
            case AUDIT_OP : {
                this.setPurConfirmStatus(bills,"A");
                break;
            }
            case UNAUDIT_OP : {
                this.setPurConfirmStatus(bills,"");
                break;
            }
        }
    }

    /**
     * 设置采购方审核状态
     * @param bills
     * @param status
     * @author: hst
     * @createDate: 2024/06/08
     */
    private void setPurConfirmStatus (DynamicObject[] bills, String status) {
        for (DynamicObject bill : bills) {
            bill.set(ScpPurCycleConstant.CONSTATUS_FIELD,status);
        }
        SaveServiceHelper.save(bills);
    }
}
