package kd.bos.tcbj.srm.scp.oplugin;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.fi.cas.helper.OperateServiceHelper;
import kd.scm.common.util.ApiConfigUtil;
import kd.scm.common.util.ExceptionUtil;
import kd.taxc.common.util.StringUtil;

/**
 * 补货单功能插件类
 * @auditor yanzuliang
 * @date 2022年10月22日
 * 
 */
public class ScpReplenishOp extends AbstractOperationServicePlugIn {
	
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		List filds = e.getFieldKeys();
		filds.add("billno");
		filds.add("entryentity.yd_easentryid");
		filds.add("entryentity.yd_confirmqty");
		filds.add("entryentity.yd_confirmdate");
		filds.add("entryentity.yd_logbillno");
		filds.add("yd_supplier");
		filds.add("billstatus");
		filds.add("auditor");
		filds.add("auditdate");
		filds.add("entryentity.yd_replenishqty");
		filds.add("entryentity.yd_supmatname");
		filds.add("entryentity.yd_producers");
	}
	
	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		super.beginOperationTransaction(e);
		
		String operationKey = e.getOperationKey();
        
        if (!StringUtils.equals("confirm", operationKey)) return;
        
        DynamicObject[] col = e.getDataEntities();
        if (col.length == 0) return;

        for (DynamicObject info : col) {
            info.set("billstatus", "C"); // 设置状态为已确认
            info.set("auditor", BizHelper.getUserInfo()); // 确认人
            info.set("auditdate", new Date()); // 确认时间
        }
        // 保存单据
        OperationResult result = SaveServiceHelper.saveOperate(col[0].getDataEntityType().getName(), col, OperateOption.create());
        if (result.isSuccess()) {
            List<Object> successPkIds = result.getSuccessPkIds();
            for (Object successPkId : successPkIds) {
                // 已经确认后，调用EAS集成同步数据
            	DynamicObject dynamicObject = BusinessDataServiceHelper.loadSingle(successPkId, "yd_scp_replenish");
            	OperateServiceHelper.execOperate("confirmtoeas","yd_scp_replenish", new DynamicObject[]{dynamicObject}, OperateOption.create());
            }
        }
	}
	
	/**
	 * 启动事务操作事件，调用EAS功能接口推送供应商确认信息
	 */
	@Override
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		super.beforeExecuteOperationTransaction(e);
		
		String key = e.getOperationKey();
		DynamicObject[] objs = e.getDataEntities();
		e.getValidExtDataEntities();
		String entityType = null;
		if (objs.length != 0) {
			entityType = objs[0].getDataEntityType().getName();
			HashMap param = new HashMap();
			param.put("billtype", entityType);
			param.put("action", "confirm");
			param.put("code", "200");
			
			if ("confirmtoeas".equalsIgnoreCase(key) && ApiConfigUtil.hasEASConfig()) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd");
				Set billnoSet = this.getBillnoStr(objs, "billno");
				String msg = "500";
				
				Map dataParam = new HashMap();
				if (billnoSet.size() > 0) {
//					List<Map> dataList = new ArrayList<Map>();
					for (DynamicObject bill : objs) {
						Map billMap = new HashMap();
						String billNo = bill.getString("billno");
						billMap.put("billNo", billNo);
						DynamicObjectCollection entryCol = bill.getDynamicObjectCollection("entryentity");
						List<Map<String,String>> enList = new ArrayList<Map<String,String>>();
						for (DynamicObject enInfo : entryCol) {
							if (enInfo.get("yd_confirmdate") != null) {
								HashMap ex = new HashMap();
								ex.put("easEntryId", enInfo.getString("yd_easentryid"));
								ex.put("supConfirmQty", enInfo.getBigDecimal("yd_confirmqty"));
								ex.put("supConfirmDate", sdf.format(enInfo.getDate("yd_confirmdate")));
								ex.put("logBillNo", enInfo.getString("yd_logbillno"));
								enList.add(ex);
							}
						}
						billMap.put("entry", enList);
						
//						dataList.add(billMap);
						param.put("datas", billMap);
					}
					
					dataParam.put("data", param);

					try {
						msg = ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doReplenishConfirm", dataParam).toString();
					} catch (Exception arg10) {
						System.out.println("补货单确认失败" + ExceptionUtil.getStackTrace(arg10));
					}

				}

				if ("500".equals(msg)) {
					e.cancel = true;
					e.setCancelMessage(ResManager.loadKDString("确认失败，请联系管理员！", "ScpReplenishOp_0",
							"scm-scp-opplugin", new Object[0]));
				} else if (!"500".equals(msg) && msg.length() > 0) {
					try {
						Map ex1 = (Map) JSONObject.parseObject(msg, Map.class);
						boolean IsSuccess = ((Boolean) ex1.get("IsSuccess")).booleanValue();
						if (!IsSuccess) {
							e.cancel = true;
							e.setCancelMessage(ResManager.loadKDString("确认失败，请联系管理员！", "ScpReplenishOp_0",
									"scm-scp-opplugin", new Object[0]));
						}
					} catch (Exception arg9) {
						System.out.println("补货单订单确认失败" + ExceptionUtil.getStackTrace(arg9));
					}
				}
			}
			
		}
	}
	
	/**
	 * 发短信通知供应商处理单据
	 */
	@Override
	public void afterExecuteOperationTransaction(AfterOperationArgs e) {
		super.afterExecuteOperationTransaction(e);
		
		if ("notifysup".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject[] objs = e.getDataEntities();
			for (DynamicObject bill : objs) {
				DynamicObject supObj = bill.getDynamicObject("yd_supplier");
				//获取对应供应商用户数据
				DynamicObjectCollection supplierUserCol = SupplyStockHelper.getSrmSupplierUsersBySupplierId(supObj.getString("id"));
                if (supplierUserCol != null) {
                	for (DynamicObject supplierUserInfo : supplierUserCol) {
                		if (supplierUserInfo != null) {
                			if (StringUtil.isNotBlank(supplierUserInfo.getString("user.phone"))) {
                				String phone = supplierUserInfo.getString("user.phone");
                				MessageUtil.sendShortMessage(phone, "已接收到来自汤臣倍健的补货申请，补货单单号为" + bill.getString("billno") + "，请及时登录汤臣倍健SRM系统确认补货计划，谢谢！");
                			}
                		}
                		
                	}
                }
				
			}
		}
	}
	
	private Set<String> getBillnoStr(DynamicObject[] objs, String billnoProperty) {
		HashSet numbers = new HashSet();
		DynamicObject[] arg3 = objs;
		int arg4 = objs.length;

		for (int arg5 = 0; arg5 < arg4; ++arg5) {
			DynamicObject dynamicObject = arg3[arg5];
			String billno = dynamicObject.getString(billnoProperty);
			if (!numbers.contains(billno) || billno.trim().length() > 0) {
				numbers.add(billno);
			}
		}

		return numbers;
	}
}
