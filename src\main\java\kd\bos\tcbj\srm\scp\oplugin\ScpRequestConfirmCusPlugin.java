package kd.bos.tcbj.srm.scp.oplugin;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.scm.common.enums.ConfirmStatusEnum;
import kd.scm.common.util.ApiConfigUtil;
import kd.scm.common.util.ExceptionUtil;

/**
 * 苍穹供应商端退货通知单操作插件类
 * @auditor yanzuliang
 * @date 2022年10月15日
 * 
 */
public class ScpRequestConfirmCusPlugin extends AbstractOperationServicePlugIn {
	
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		List filds = e.getFieldKeys();
		filds.add("billno");
		filds.add("yd_returnmethod");
		filds.add("yd_pickuptime");
		filds.add("yd_recipient");
		filds.add("yd_contact");
		filds.add("yd_address");
		filds.add("yd_logistics");
	}
	
	/**
	 * 启动事务操作事件，调用EAS功能接口推送反写供应商确认信息
	 */
	public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
		
		String key = e.getOperationKey();
		DynamicObject[] objs = e.getDataEntities();
		e.getValidExtDataEntities();
		String entityType = null;
		if (objs.length != 0) {
			entityType = objs[0].getDataEntityType().getName();
			HashMap param = new HashMap();
			if (("confirm".equalsIgnoreCase(key) || "adopt".equalsIgnoreCase(key)) && ApiConfigUtil.hasEASConfig()) {
				SimpleDateFormat DATE_SDF =new SimpleDateFormat("yyyy-MM-dd");
				Set billnoSet = this.getBillnoStr(objs, "billno");
				String returnType = objs[0].getString("yd_returnmethod");
				String planRecDate = "";
				if (objs[0].getDate("yd_pickuptime") != null) {
					planRecDate = DATE_SDF.format(objs[0].getDate("yd_pickuptime"));
				}
				String recipient = objs[0].getString("yd_recipient");
				String contact = objs[0].getString("yd_contact");
				String address = objs[0].getString("yd_address");
				String logistics = objs[0].getString("yd_logistics");
				
				Map dataParam = new HashMap();
				
				String msg = "500";
				if (billnoSet.size() > 0) {
					HashMap ex = new HashMap();
					ex.put("billno", billnoSet);
					param.put("data", ex);
					param.put("billtype", entityType);
					param.put("action", "confirm");
					param.put("code", "200");
					
					param.put("returnType", returnType);  // 供应商退货方式
					param.put("planRecDate", planRecDate);  // 预计取货时间
					param.put("recipient", recipient);  // 邮寄收件人
					param.put("contact", contact);  // 联系方式
					param.put("address", address);  // 地址
					param.put("logistics", logistics);  // 偏好物流商
					
					dataParam.put("data", param);

					try {
						msg = ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doReturnBillConfirm", dataParam).toString();
					} catch (Exception arg10) {
						System.out.println("退货通知单确认失败" + ExceptionUtil.getStackTrace(arg10));
					}

				}

				if ("500".equals(msg)) {
					e.cancel = true;
					e.setCancelMessage(ResManager.loadKDString("确认失败，请联系管理员！", "ScpRequestConfirmCusPlugin_0",
							"scm-scp-opplugin", new Object[0]));
				} else if (!"500".equals(msg) && msg.length() > 0) {
					try {
						Map ex1 = (Map) JSONObject.parseObject(msg, Map.class);
						boolean IsSuccess = ((Boolean) ex1.get("IsSuccess")).booleanValue();
						if (!IsSuccess) {
							e.cancel = true;
							e.setCancelMessage(ResManager.loadKDString("确认失败，请联系管理员！", "ScpRequestConfirmCusPlugin_0",
									"scm-scp-opplugin", new Object[0]));
						}
					} catch (Exception arg9) {
						System.out.println("退货通知单确认失败" + ExceptionUtil.getStackTrace(arg9));
					}
				}
			}

		}
	}
	
	private Set<String> getBillnoStr(DynamicObject[] objs, String billnoProperty) {
		HashSet numbers = new HashSet();
		DynamicObject[] arg3 = objs;
		int arg4 = objs.length;

		for (int arg5 = 0; arg5 < arg4; ++arg5) {
			DynamicObject dynamicObject = arg3[arg5];
			String billno = dynamicObject.getString(billnoProperty);
			if (!numbers.contains(billno) || billno.trim().length() > 0) {
				numbers.add(billno);
			}
		}

		return numbers;
	}
}
