package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.MessageHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 供应商备货单变更单确认--采购方使用
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-18
 */
public class StockUpChConfirmOp extends AbstractOperationServicePlugIn {

    protected static final Log log = LogFactory.getLog(StockUpChConfirmOp.class.getName());

    /**
     * 采购方变更——更新需求方字段信息后，执行确认变更后，清控供应方字段且保存单据
     * <AUTHOR>
     * @date 2022-10-19
     */
    @Override
    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
        List<ExtendedDataEntity> validExtDataEntities = e.getValidExtDataEntities();
        for (ExtendedDataEntity extendedDataEntity : validExtDataEntities) {
            // 对应更新采购方的需求数量和需求日期，清空供应方的字段信息
            DynamicObject info = extendedDataEntity.getDataEntity();
            DynamicObjectCollection entryCol = info.getDynamicObjectCollection("yd_materialentry");
            // 清空供应商的字段（供应商确认数量、供应商确认时间、供应商备注）
            for (DynamicObject entryInfo : entryCol) {
                entryInfo.set("yd_supcomfirmqty", 0); // 供应商确认数量
                entryInfo.set("yd_supcomfirmdate", null); // 供应商确认时间
                entryInfo.set("yd_supremark", null); // 供应商备注
                entryInfo.set("yd_diffqty", entryInfo.getBigDecimal("yd_supcomfirmqty").subtract(entryInfo.getBigDecimal("yd_qty"))); // 差异数量
            }
            // 状态更新为待确认
            info.set("billstatus", "B"); // 待确认状态
            SaveServiceHelper.saveOperate(info.getDataEntityType().getName(), new DynamicObject[]{info}, OperateOption.create());

            // 获取供应商管理员
            DynamicObject supUserInfo = SupplyStockHelper.getSrmSupplierUserBySupplierId(String.valueOf(info.getDynamicObject("yd_supplier").getLong("id")));
            if (supUserInfo != null && supUserInfo.getLong("user") != 0) {

                Long userId = supUserInfo.getLong("user"); // 供应商对应的用户
//                DynamicObject userInfo = BizHelper.getDynamicObjectById("bos_user", 1338616377744819200L); // 测试用户

                DynamicObject userInfo = BizHelper.getDynamicObjectById("bos_user", userId, "id,username,phone,email");

                // 消息推送
                String billNo = info.getString("billno");
                String title = "备货单信息变更确认";
                String content = String.format("备货单%s已进行变更，请及时确认", billNo);
                String signature = userInfo.getString("username"); // 用户名
                String tagMsg = "消息中心";
                List<String> receivePhones = new ArrayList<>();
                List<String> receiveEmail = new ArrayList<>();
                List<Long> receives = new ArrayList<>();

                // 获取供应商用户ID
                receives.add(userInfo.getLong("id"));
                // 发送站内信
                MessageHelper.sendMessageCenter(title, content, tagMsg, receives);

                // 存在对应的电话号码，则按短信发送
                if (StringUtils.isNotBlank(userInfo.getString("phone"))) {
                    receivePhones.add(userInfo.getString("phone"));
                    // 发送短信通知
                    Map<String, Object> result = MessageHelper.sendSMS(content, signature, receivePhones);
                    if (StringUtils.equals(String.valueOf(result.get("result")), "false")) {
//                        throw new KDBizException((String) result.get("description"));
                        log.error("单据编码："+billNo+"，发送短信失败："+result.get("description"));
                    }
                }
                // 存在对应的邮箱信息，则发送邮件
                if (StringUtils.isNotBlank(userInfo.getString("email"))) {
                    receiveEmail.add(userInfo.getString("email"));// 发送邮件信息
                    Map<String, Object> result = MessageHelper.sendEmail(title, content, receiveEmail);
                    if (StringUtils.equals(String.valueOf(result.get("result")), "false")) {
//                        throw new KDBizException((String) result.get("description"));
                        log.error("单据编码："+billNo+"，发送邮箱失败："+result.get("description"));
                    }
                }
            }
        }
    }
}
