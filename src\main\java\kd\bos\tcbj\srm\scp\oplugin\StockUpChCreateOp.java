package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.EntryType;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.MainEntityType;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.property.EntryProp;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.TimeServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.ORMUtils;
import kd.bos.tcbj.srm.admittance.utils.BillChangeUtils;
import kd.bos.util.JSONUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;

/**
 * 备货单变更——新增备货单变更单--供应方使用
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-19
 */
public class StockUpChCreateOp extends AbstractOperationServicePlugIn {

    private static final Log log = LogFactory.getLog(StockUpChCreateOp.class.getName());

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> fieldKeys = e.getFieldKeys();
        fieldKeys.add("yd_supplier");
        fieldKeys.add("yd_needperiod");
        fieldKeys.add("org");
    }

    @Override
    public void beforeExecuteOperationTransaction(BeforeOperationArgs e) {
        List<ExtendedDataEntity> validExtDataEntities = e.getValidExtDataEntities();
        for (ExtendedDataEntity extendedDataEntity : validExtDataEntities) {
            DynamicObject info = extendedDataEntity.getDataEntity();
            Map<String, Object> resultMap = createSupStockCh(info);
            if (StringUtils.equals((String)resultMap.get("status"), "error")) {
                e.setCancel(true);
                e.setCancelMessage(resultMap.get("errMsg").toString());
            }
        }
    }

    /**
     * 创建备货单变更单
     * @param obj 备货单
     * <AUTHOR>
     * @date 2022-10-19
     */
    private Map<String, Object> createSupStockCh(DynamicObject obj) {
        Map<String, Object> resultMap = new HashMap<>();
        LinkedHashMap<String, List<Object>> newValueMap = new LinkedHashMap<String, List<Object>>();
        LinkedHashMap<String, List<Object>> oldValueMap = new LinkedHashMap<String, List<Object>>();
        MainEntityType mainEntityType = (MainEntityType) obj.getDataEntityType();
        DynamicObject newDataEntity = obj;
        DynamicObject tgtDataEntity = BusinessDataServiceHelper.newDynamicObject("yd_supstockch");
        EntryProp entryProp = (EntryProp) tgtDataEntity.getDataEntityType().getProperties().get("entryentity");
        EntryType entryType = (EntryType) entryProp.getItemType();
        DynamicObject oldDataEntity=BusinessDataServiceHelper.loadSingle(obj.getPkValue(), obj.getDynamicObjectType());
        if (oldDataEntity == null) {
            resultMap.put("errMsg","无变更内容");
            resultMap.put("status","error");
            return resultMap;
        }else {
            DynamicObjectCollection tgtChangeEntry = tgtDataEntity.getDynamicObjectCollection("entryentity");
            tgtChangeEntry.clear();
            BillChangeUtils.readPropValue(oldDataEntity, null, null, null, mainEntityType, 0, true, newValueMap, oldValueMap, null, null);
            HashMap<String, String> oldEntryIdSeqMap = new HashMap<String, String>();
            for (Map.Entry<String, List<Object>> oldEntry : oldValueMap.entrySet()) {
                String[] splitStrArr = oldEntry.getKey().split("\\.");
                if (splitStrArr.length != 4)
                    continue;
                oldEntryIdSeqMap.put(splitStrArr[2], splitStrArr[3]);
            }
            BillChangeUtils.readPropValue(newDataEntity, null, null, null, mainEntityType, 0, false, newValueMap, oldValueMap,null, oldEntryIdSeqMap);
            log.info("开始差异比较，并新增分录");
            compareAndCreateEntry(newValueMap, oldValueMap, entryType, entryProp, tgtChangeEntry);
            setDefaultValue(tgtDataEntity, obj);

            if(tgtChangeEntry.size()==0){
                resultMap.put("errMsg","无变更内容");
                resultMap.put("status","error");
                return resultMap;
            }

            OperationResult result= SaveServiceHelper.saveOperate(tgtDataEntity.getDataEntityType().getName(),new DynamicObject[] {tgtDataEntity}, OperateOption.create());
            resultMap.put("status","ok");
            resultMap.put("billid",tgtDataEntity.getPkValue());
        }
        return resultMap;
    }

    /**
     * 设置表头默认值
     * <AUTHOR>
     * @date 2022-10-19
     */
    private void setDefaultValue(DynamicObject tgtDataEntity, DynamicObject obj) {

        String billNo = obj.getString("billno");

        tgtDataEntity.set("yd_supplier", obj.getDynamicObject("yd_supplier"));
        tgtDataEntity.set("yd_needperiod", obj.getDynamicObject("yd_needperiod"));
        tgtDataEntity.set("yd_srcbillid", obj.getPkValue().toString());
        tgtDataEntity.set("yd_srcbillno", billNo);

        DynamicObject userInfo = BizHelper.getUserInfo();

        tgtDataEntity.set("org",obj.getDynamicObject("org")); // 组织
        tgtDataEntity.set("billstatus","A"); // 保存状态
        tgtDataEntity.set("yd_datastatus","A"); // 未变更
        tgtDataEntity.set("creator", userInfo); // 当前用户
        tgtDataEntity.set("modifier", userInfo); // 当前用户
        tgtDataEntity.set("createtime", new Date()); // 创建时间
        tgtDataEntity.set("modifytime", new Date()); // 修改时间
        tgtDataEntity.set("billno", getSupStockNewNumber(billNo));
    }

    private String getSupStockNewNumber(String billNo) {
        DataSet dataSet = null;
        try {
            dataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), "yd_supstockch", "billno", QFilter.of("yd_srcbillno=?", billNo).toArray(), "billno desc");
            if (!dataSet.hasNext()) return billNo+"_CH0001";
            Row row = dataSet.next();
            String maxBillNo =  row.getString("billno");

            StringBuilder subBillNo = new StringBuilder(maxBillNo.substring(maxBillNo.length()-4));
            subBillNo = new StringBuilder(Integer.parseInt(subBillNo.toString()) + 1 + "");
            for (int index=subBillNo.length(); index<4; index++) {
                subBillNo.insert(0, "0");
            }
            return subBillNo.insert(0, "_CH").insert(0, billNo).toString();
        }finally {
            ORMUtils.close(dataSet);
        }
    }


    public void compareAndCreateEntry(Map<String, List<Object>> newValueMap,
                                             Map<String, List<Object>> oldValueMap, EntryType entryType, EntryProp entryProp,
                                             DynamicObjectCollection tgtChangeEntry) {
        List<Object> oldList;
        List<Object> newList;
        for (Map.Entry<String, List<Object>> entryMap : oldValueMap.entrySet()) {
            oldList = entryMap.getValue();
            newList = newValueMap.get(entryMap.getKey());
            if (oldList != null && newList != null) {
                if (oldList.size() < 2 || newList.size() < 2 || Objects.isNull(oldList.get(1).toString())
                        || Objects.isNull(oldList.get(2).toString()) || Objects.isNull(newList.get(1).toString())
                        || Objects.isNull(newList.get(2).toString())) {
                    newValueMap.remove(entryMap.getKey());
                    continue;
                }
                if (oldList.get(1).toString().concat(oldList.get(2).toString())
                        .equals(newList.get(1).toString().concat(newList.get(2).toString()))) {
                    newValueMap.remove(entryMap.getKey());
                    continue;
                }
            }
            insertRow(entryMap.getKey(), oldList, newList, entryType, entryProp, tgtChangeEntry);
            newValueMap.remove(entryMap.getKey());
        }
        for (Map.Entry<String, List<Object>> newValueEntryMap : newValueMap.entrySet()) {
            oldList = oldValueMap.get(newValueEntryMap.getKey());
            newList = newValueEntryMap.getValue();
            if (oldList != null && newList != null
                    && (oldList.size() < 2 || newList.size() < 2 || Objects.isNull(oldList.get(1).toString())
                    || Objects.isNull(oldList.get(2).toString()) || Objects.isNull(newList.get(1).toString())
                    || Objects.isNull(newList.get(2).toString())
                    || oldList.get(1).toString().concat(oldList.get(2).toString())
                    .equals(newList.get(1).toString().concat(newList.get(2).toString()))))
                continue;
            insertRow(newValueEntryMap.getKey(), oldList, newList, entryType, entryProp, tgtChangeEntry);
        }
    }


    public void insertRow(String key, List<Object> oldList, List<Object> newList, EntryType entryType,
                                 EntryProp entryProp, DynamicObjectCollection tgtChangeEntry)
    {
        log.info("新增分录开始" + key);
        String v1 = oldList == null ? null : oldList.get(1).toString().trim();
        String v2 = newList == null ? null : newList.get(1).toString().trim();
        log.info("新增分录开始V1:" + v1 + " ,v2:" + v2);
        if ((v1 == null || v1.equals("")) && (v2 == null || v2.equals(""))) {
            return;
        }

        DynamicObject tempRow = new DynamicObject((DynamicObjectType) entryType);
        tempRow.set("yd_chgfield", newList == null ? oldList.get(2) : newList.get(2));
        tempRow.set("yd_oldvalue", (Object) (oldList == null ? null : oldList.get(1).toString()));
        tempRow.set("yd_newvalue", (Object) (newList == null ? null : newList.get(1).toString()));
        tempRow.set("yd_fieldname", newList == null ? oldList.get(0) : newList.get(0));
        tempRow.set("yd_infotype", newList == null ? oldList.get(6) : newList.get(6));
        tempRow.set("yd_entryname", newList == null ? oldList.get(4) : newList.get(4));
        if (StringUtils.isBlank(tempRow.getString("yd_entryname"))) {
            tempRow.set("yd_entryfieldname", tempRow.get("yd_fieldname"));
        }else {
            tempRow.set("yd_entryfieldname", newList == null ? oldList.get(0)+"."+oldList.get(4)+"."+oldList.get(5) : newList.get(0)+"."+newList.get(4)+"."+newList.get(5));
        }

        if("Z".equals(tempRow.getString("yd_infotype")))
        {
            //3,9
            if(StringUtils.isNotBlank(tempRow.getString("yd_oldvalue")))
            {
                Map oldExtMap=new HashMap();
                oldExtMap.put("value", ""+oldList.get(3));
                oldExtMap.put("enttype", oldList.get(9));
                try {
                    tempRow.set("yd_oldvalueex", JSONUtils.toString(oldExtMap));
                } catch (IOException e)
                {
                    e.printStackTrace();
                }

            }

            if(StringUtils.isNotBlank(tempRow.getString("yd_newvalue")))
            {

                Map extMap=new HashMap();
                extMap.put("value", ""+newList.get(3));
                extMap.put("enttype", newList.get(9));
                try {
                    tempRow.set("yd_newvalueex", JSONUtils.toString(extMap));
                } catch (IOException e)
                {
                    e.printStackTrace();
                }

            }

        }


        if (tempRow.get("yd_fieldname").equals("isdefault") || tempRow.get("yd_fieldname").equals("isdefault_link"))
        {
            if (tempRow.get("yd_oldvalue").equals("0")) {
                tempRow.set("yd_oldvalue", (Object) ResManager.loadKDString((String) "非默认", (String) "SrmSupChgUtil_1",
                        (String) "scm-common", (Object[]) new Object[0]));
            } else {
                tempRow.set("yd_oldvalue", (Object) ResManager.loadKDString((String) "默认", (String) "SrmSupChgUtil_2",
                        (String) "scm-common", (Object[]) new Object[0]));
            }
            if (tempRow.get("yd_newvalue").equals("0")) {
                tempRow.set("yd_newvalue", (Object) ResManager.loadKDString((String) "非默认", (String) "SrmSupChgUtil_1",
                        (String) "scm-common", (Object[]) new Object[0]));
            } else {
                tempRow.set("yd_newvalue", (Object) ResManager.loadKDString((String) "默认", (String) "SrmSupChgUtil_2",
                        (String) "scm-common", (Object[]) new Object[0]));
            }
        }
        tgtChangeEntry.add(tempRow);
        log.info("新增分录完成");
    }
}
