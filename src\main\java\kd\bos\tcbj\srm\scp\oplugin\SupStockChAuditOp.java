package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.AddValidatorsEventArgs;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.admittance.helper.SupplyListBizHelper;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;
import kd.bos.tcbj.srm.scp.helper.SupStockUpHelper;
import kd.bos.tcbj.srm.utils.GeneralFormatUtils;
import kd.bos.util.JSONUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 供应商备货单变更审批处理
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-20
 */
public class SupStockChAuditOp extends AbstractOperationServicePlugIn {

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        e.getFieldKeys().add("billstatus");
        e.getFieldKeys().add("billno");
        e.getFieldKeys().add("yd_datastatus");
        e.getFieldKeys().add("yd_srcbillid");
        e.getFieldKeys().add("entryentity.yd_infotype");
        e.getFieldKeys().add("entryentity.yd_fieldname");
        e.getFieldKeys().add("entryentity.yd_newvalue");
        e.getFieldKeys().add("entryentity.yd_newvalueex");
        e.getFieldKeys().add("entryentity.yd_entryname");
        e.getFieldKeys().add("entryentity.yd_entryfieldname");
    }

    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        DynamicObject[] col = e.getDataEntities();
        // 审核时，将对应的信息反写到备货单上
        for (DynamicObject info : col) {
            changeValue(info);
            // 更新完后，调用EAS的确认功能进行数据反写
            SupStockUpHelper.revokeFacadeToEas(e, info.getString("yd_srcbillid"));
        }
    }

    @Override
    public void onAddValidators(AddValidatorsEventArgs e)
    {
        e.addValidator(new SupStockAuditValidate());
    }

    private void changeValue(DynamicObject ov) {
        String srcBillId=ov.getString("yd_srcbillid");
        DynamicObject srcInfo= BusinessDataServiceHelper.loadSingle(srcBillId,"yd_supstockup");
        DynamicObjectCollection entrys= ov.getDynamicObjectCollection("entryentity");
        for( DynamicObject entryInfo:entrys) {
            String field=entryInfo.getString("yd_fieldname");
            String type=entryInfo.getString("yd_infotype");
            String value=entryInfo.getString("yd_newvalue");
            String valueEx=entryInfo.getString("yd_newvalueex");
            String entryName = entryInfo.getString("yd_entryname");
            if (StringUtils.isNotBlank(entryName)) {
                String entryFieldName = entryInfo.getString("yd_entryfieldname");
                String[] fieldArr = entryFieldName.split("\\.");
                DynamicObjectCollection entryCol = srcInfo.getDynamicObjectCollection(entryName);
                setValue(entryCol.get(Integer.parseInt(fieldArr[2])-1),field,type,value,valueEx);
            }else {
                setValue(srcInfo,field,type,value,valueEx);
            }
        }
        srcInfo.set("billstatus", "C"); // 状态为已确认
        srcInfo.set("yd_iswriteeas", false); // 是否反写EAS
        updateEntry(srcInfo);
        SaveServiceHelper.save(new DynamicObject[] {srcInfo});

        ov.set("yd_datastatus", "B"); // 更新状态为已变更
        SaveServiceHelper.save(new DynamicObject[] {ov});
    }

    private void updateEntry(DynamicObject ov) {
        DynamicObjectCollection entryCol = ov.getDynamicObjectCollection("yd_materialentry");
        for (DynamicObject entryInfo : entryCol) {
            // 差异数量 = 供应商数量 - 需求数量
            entryInfo.set("yd_diffqty", entryInfo.getBigDecimal("yd_supcomfirmqty").subtract(entryInfo.getBigDecimal("yd_qty")));
        }
    }

    private void  setValue(DynamicObject srcInfo,String field,String type,String value,String valueEx) {
        if(StringUtils.isEmpty(value))
        {
            srcInfo.set(field,null);
            return;
        }

        if("Z".equals(type))
        {//基础:Z
            if(StringUtils.isNotEmpty(valueEx))
            {
                try
                {
                    Map exMap= JSONUtils.cast(valueEx, Map.class);
                    String bdId=(String)exMap.get("value");
                    String entType=(String)exMap.get("enttype");
                    if(StringUtils.isNoneBlank(entType))
                    {
                        srcInfo.set(field, BusinessDataServiceHelper.loadSingle(bdId,entType));

                    }else
                    {
                        srcInfo.set(field,bdId);
                    }
                } catch (IOException e)
                {
                    e.printStackTrace();
                }

            }
        }else if("D".equals(type))
        {//日期:D
            srcInfo.set(field, DateUtil.string2date(value, "yyyy-MM-dd"));
        }else if("B".equals(type))
        {//布尔:B
            srcInfo.set(field,"是".equals(value)||"1".equals(value));
        }else if("L".equals(type))
        {//正数
            srcInfo.set(field,Long.parseLong(value));
        }else if("N".equals(type))
        {//数字
            srcInfo.set(field,new BigDecimal(value));
        }else
        {
            srcInfo.set(field,value);
        }
    }
}

class SupStockAuditValidate extends AbstractValidator {

    @Override
    public void validate() {
        ExtendedDataEntity[] datas = this.getDataEntities();
        for(ExtendedDataEntity dataEntity : datas) {
            DynamicObject ov=dataEntity.getDataEntity();
            if("B".equals(ov.getString("yd_datastatus"))) {
                this.addErrorMessage(dataEntity, "变更已生效，不允许操作");
            }
        }
    }
}