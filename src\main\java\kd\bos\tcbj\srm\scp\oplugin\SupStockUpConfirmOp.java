package kd.bos.tcbj.srm.scp.oplugin;

import com.alibaba.fastjson.JSONObject;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.JsonUtils;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.bos.tcbj.srm.scp.helper.SupStockUpHelper;
import kd.scm.common.util.ExceptionUtil;
import org.apache.commons.lang3.StringUtils;
import scala.annotation.meta.param;

import java.math.BigDecimal;
import java.util.*;

/**
 * 供应商备货单确认
 * <AUTHOR>
 * @Description:
 * @date 2022-10-18
 */
public class SupStockUpConfirmOp extends AbstractOperationServicePlugIn {

    protected static Log log = LogFactory.getLog(SupStockUpConfirmOp.class);

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> fieldKeys = e.getFieldKeys();
        fieldKeys.add("billstatus");
        fieldKeys.add("auditor");
        fieldKeys.add("auditdate");
        fieldKeys.add("yd_iswriteeas");
        fieldKeys.add("yd_materialentry.yd_qty");
        fieldKeys.add("yd_materialentry.yd_supcomfirmqty");
        fieldKeys.add("yd_materialentry.yd_diffqty");
    }

    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        String operationKey = e.getOperationKey();
        
        if (!(StringUtils.equals("confirm", operationKey) || StringUtils.equals("reject", operationKey))) return;
        
        DynamicObject[] col = e.getDataEntities();
        if (col.length == 0) return;

        for (DynamicObject info : col) {
            info.set("billstatus", "C"); // 设置状态为已确认
            info.set("auditor", BizHelper.getUserInfo()); // 确认人
            info.set("auditdate", new Date()); // 确认时间
            info.set("yd_iswriteeas", false); // 是否反写EAS

            DynamicObjectCollection entryCol = info.getDynamicObjectCollection("yd_materialentry");
            for (DynamicObject entryInfo : entryCol) {
            	//  打回订单：将确认数量都反写为0，然后执行确认功能反写EAS,yzl,20221124
            	if (StringUtils.equals("reject", operationKey)) {
            		entryInfo.set("yd_supcomfirmqty", BigDecimal.ZERO);
            		entryInfo.set("yd_supcomfirmdate", new Date());
            		entryInfo.set("yd_supremark", "供应商无货打回");
            	}
            	
                BigDecimal qty = entryInfo.getBigDecimal("yd_qty"); // 需求数量
                BigDecimal supConfirmQty = entryInfo.getBigDecimal("yd_supcomfirmqty");// 供应商确认数量
                // 差异数量 = 供应商数量 - 需求数量
                entryInfo.set("yd_diffqty", supConfirmQty.subtract(qty));
            }
        }
        // 保存单据
        OperationResult result = SaveServiceHelper.saveOperate(col[0].getDataEntityType().getName(), col, OperateOption.create());
        if (result.isSuccess()) {
            List<Object> successPkIds = result.getSuccessPkIds();
            for (Object successPkId : successPkIds) {
                // 已经确认后，调用EAS集成同步数据
                SupStockUpHelper.revokeFacadeToEas(e, successPkId);
            }
        }
    }
}
