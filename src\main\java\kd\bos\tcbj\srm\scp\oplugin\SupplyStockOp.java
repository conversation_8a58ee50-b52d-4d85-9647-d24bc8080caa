package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.bos.tcbj.srm.scp.schedulejob.SupplyStockTask;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.scp.oplugin.SupplyStockOp
 * @className SupplyStockOp
 * @author: hst
 * @createDate: 2022/09/05
 * @description: 供应商库存单操作插件
 * @version: v1.0
 */
public class SupplyStockOp extends AbstractOperationServicePlugIn {

    private final static String BTN_AUDIT = "audit";
    private final static String BTN_SAVE = "save";
    private final static String BTN_SUMBIT = "submit";

    /**
     * 操作校验通过，开启了事务，准备把数据提交到数据库之前触发此事件
     * 审核后，将该供应商之前的供应商库存单状态改为作废
     * @author: hst
     * @createDate: 2022/09/05
     * @param e
     */
    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        String key = e.getOperationKey();
        switch (key) {
            case BTN_SUMBIT : {
                SupplyStockHelper.nullifyOldSupplyStockBill(e);
                break;
            }
            case BTN_SAVE : {
                SupplyStockHelper.moditfyUpdateDate(e);
                break;
            }
            case "tasking" : {
                Map<String,Object> map = new HashMap<>();
                map.put("key","deleteSupplyStock");
                new SupplyStockTask().execute(RequestContext.get(),map);
                map.put("key","notifyUpdateInventory");
                new SupplyStockTask().execute(RequestContext.get(),map);
                break;
            }
        }
    }
}
