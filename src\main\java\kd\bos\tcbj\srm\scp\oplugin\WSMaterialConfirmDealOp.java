package kd.bos.tcbj.srm.scp.oplugin;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.AfterOperationArgs;
import kd.bos.entity.plugin.args.BeforeOperationArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.JsonUtils;
import kd.bos.tcbj.im.util.MessageHelper;
import kd.bos.tcbj.srm.admittance.helper.ExecEASHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.scm.common.util.ExceptionUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 包材叫货供应商确认处理
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-11-1
 */
public class WSMaterialConfirmDealOp extends AbstractOperationServicePlugIn {

    private static Log log = LogFactory.getLog(WSMaterialConfirmDealOp.class);

    @Override
    public void onPreparePropertys(PreparePropertysEventArgs e) {
        List<String> fieldKeys = e.getFieldKeys();
        fieldKeys.add("billstatus");
        fieldKeys.add("yd_confirmstatus");
        fieldKeys.add("auditor");
        fieldKeys.add("auditdate");
        fieldKeys.add("yd_senddate");
        fieldKeys.add("yd_iswriteeas");
        fieldKeys.add("entryentity.yd_supconfirmqty");
        fieldKeys.add("entryentity.yd_supconfirmdate");
        fieldKeys.add("entryentity.yd_supremark");
    }

    @Override
    public void beginOperationTransaction(BeginOperationTransactionArgs e) {
        String operationKey = e.getOperationKey();
        if (StringUtils.equals("confirm", operationKey)) {
            confirm(e);
        }
    }

    @Override
    public void afterExecuteOperationTransaction(AfterOperationArgs e) {
        String operationKey = e.getOperationKey();
        if (StringUtils.equals("save", operationKey)) {
            sendSupMsg(e);
        }
    }

    private void sendSupMsg(AfterOperationArgs e) {
        DynamicObject[] col = e.getDataEntities();
        if (col.length == 0) return;
        DynamicObject info = col[0];
        // 获取供应商管理员
        DynamicObject supUserInfo = SupplyStockHelper.getSrmSupplierUserBySupplierId(String.valueOf(info.getDynamicObject("yd_supplier").getLong("id")));
        if (supUserInfo != null && supUserInfo.getLong("user") != 0) {

            Long userId = supUserInfo.getLong("user"); // 供应商对应的用户
//                DynamicObject userInfo = BizHelper.getDynamicObjectById("bos_user", 1338616377744819200L); // 测试用户

            DynamicObject userInfo = BizHelper.getDynamicObjectById("bos_user", userId, "id,username,phone,email");

            // 消息推送
            String billNo = info.getString("billno");
            String title = "供应商包材叫货确认";
            String content = String.format("包材叫货确认单%s已经生成，请确认数量和日期！", billNo);
            String signature = userInfo.getString("username"); // 用户名
            List<String> receivePhones = new ArrayList<>();

            // 存在对应的电话号码，则按短信发送
            if (StringUtils.isNotBlank(userInfo.getString("phone"))) {
                receivePhones.add(userInfo.getString("phone"));
                // 发送短信通知
                Map<String, Object> result = MessageHelper.sendSMS(content, signature, receivePhones);
                if (StringUtils.equals(String.valueOf(result.get("result")), "false")) {
//                        throw new KDBizException((String) result.get("description"));
                    log.error("单据编码："+billNo+"，发送短信失败："+result.get("description"));
                }
            }
        }
    }

    private void confirm(BeginOperationTransactionArgs e) {
        DynamicObject[] col = e.getDataEntities();
        for (DynamicObject info : col) {
            info.set("billstatus", "C"); // 设置状态已审核
            info.set("yd_confirmstatus", "2"); // 设置状态为已确认
            info.set("auditor", BizHelper.getUserInfo()); // 确认人
            info.set("auditdate", new Date()); // 确认时间
            info.set("yd_iswriteeas", false); // 是否反写EAS
        }
        // 保存单据
        OperationResult result = SaveServiceHelper.saveOperate(col[0].getDataEntityType().getName(), col, OperateOption.create());
        if (result.isSuccess() && result.getSuccessPkIds().size() > 0) {
            List<Object> successPkIds = result.getSuccessPkIds();
            Object successPkId = successPkIds.get(0);
            // 已经确认后，调用EAS集成同步数据
            boolean isSuccess = revokeFacadeToEas(e, successPkId);
            if (!isSuccess) {
                for (DynamicObject info : col) {
                    info.set("billstatus", "A"); // 设置状态暂存
                    info.set("yd_confirmstatus", "1"); // 设置状态为未确认
                    info.set("auditor", null); // 确认人
                    info.set("auditdate", null); // 确认时间
                    info.set("yd_iswriteeas", false); // 是否反写EAS
                }
                // 保存单据
                SaveServiceHelper.saveOperate(col[0].getDataEntityType().getName(), col, OperateOption.create());
            }
        }
    }

    private boolean revokeFacadeToEas(BeginOperationTransactionArgs e, Object billId) {

        boolean isSuccess = false;

        // 根据已经确认成功的id获取info对象
        DynamicObject info = BizHelper.getDynamicObjectById("yd_wsmaterialconfirm", billId);

        Map<String, Object> paramMap = new HashMap<>();
        Map<String, Object> paramDataMap = new HashMap<>();
        List<Map<String, Object>> entryList = new ArrayList<>();
        paramMap.put("data", paramDataMap);
        paramDataMap.put("number", info.getString("billno")); // 单据编码
        paramDataMap.put("sendDate", DateTimeUtils.format(info.getDate("yd_senddate"), DateTimeUtils.SDF_TIME)); // 发货日期
        paramDataMap.put("entrys", entryList);

        DynamicObjectCollection entryCol = info.getDynamicObjectCollection("entryentity");
        for (DynamicObject entryInfo : entryCol) {
            Map<String, Object> entryMap = new HashMap<>();
            // 供应商确认数量和日期
            BigDecimal supConfirmQty = entryInfo.getBigDecimal("yd_supconfirmqty");
            Date supConfirmDate = entryInfo.getDate("yd_supconfirmdate"); // 供应商配送日期

            entryMap.put("entryId", entryInfo.getString("yd_easentryid")); // EAS分录ID
            entryMap.put("supComfirmQty", supConfirmQty); // 供应商确认数量
            entryMap.put("supComfirmDate", supConfirmDate!=null? DateTimeUtils.format(supConfirmDate, DateTimeUtils.SDF_TIME):null); // 供应商确认时间
            entryMap.put("supRemark", entryInfo.getString("yd_supremark")); // 供应商别再
            entryMap.put("isError", entryInfo.getBoolean("yd_iserror")); // 是否异常
            entryList.add(entryMap);
        }

        // 调用EAS功能，触发数据集成
        String result = (String) ExecEASHelper.invokeFacade("com.kingdee.eas.custom.common.app.SrmBizDealFacade", "doSupWSMaterialConfirm", paramMap);
        if (result == null) {
            log.error("接口调用异常，EAS回传结果为空，请联系管理员！");
            return false;
        }
        try {
            log.info("供应商包材叫货确认返回结果："+result);
            Map<String, Object> resultMap = JsonUtils.toMap(result);
            isSuccess = (Boolean) resultMap.get("isSuccess");
            if (!isSuccess) {
                log.error("供应商包材叫货确认失败，详细信息："+resultMap.get("message"));
            }
        } catch (Exception err) {
            log.error("供应商包材叫货确认失败" + ExceptionUtil.getStackTrace(err));
        }
        return isSuccess;
    }
}
