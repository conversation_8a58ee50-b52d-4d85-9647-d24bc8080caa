package kd.bos.tcbj.srm.scp.oplugin.validator;

import java.util.List;
import java.util.Map;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.AttachmentServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;

/**
 * 供应方采购订单确认操作校验器
 * 校验是否存在包材稿件确认单，存在未确认的则不允许确认订单
 * @auditor yanzuliang
 * @date 2022年11月20日
 * 
 */
public class ScpOrderPackConfirmValidator extends AbstractValidator {

	@Override
	public void validate() {
		ExtendedDataEntity[] datas = this.getDataEntities();
		String opKey = this.getOperateKey();

		for (ExtendedDataEntity dataEntity : datas) {
			DynamicObject ov = dataEntity.getDataEntity();

			if ("agreeorder".equals(opKey) || "confirm".equals(opKey) || "confirmtest".equals(opKey)) {
				QFilter filter = new QFilter("yd_pobillid", QCP.equals, ov.getString("id"));
				filter.and(new QFilter("entryentity.yd_signstate", QCP.equals, "1"));
				DynamicObjectCollection bills = QueryServiceHelper.query("yd_packwordconfbill", "id,billno", filter.toArray());
				if (bills.size() > 0) {
					this.addErrorMessage(dataEntity, "单号:"+ov.getString("billno")+",存在未确认的包材稿件确认单"+bills.get(0).getString("billno")+",请到包材稿件确认单模块进行确认!");
				}
				
				// 如果有附件的话控制必须勾选附件,20230309,yzl
//				ov.get("attachmentpanel");
				List<Map<String, Object>> attach = AttachmentServiceHelper.getAttachments("pur_order", ov.getString("id"), "attachmentpanel");
				if (attach.size() > 0 && !ov.getBoolean("yd_becheckedattach")) {
					this.addErrorMessage(dataEntity, "单据存在附件，请查收下载阅读附件并勾选“已查阅附件”，再进行单据确认!");
				}
			}
		}
	}

}
