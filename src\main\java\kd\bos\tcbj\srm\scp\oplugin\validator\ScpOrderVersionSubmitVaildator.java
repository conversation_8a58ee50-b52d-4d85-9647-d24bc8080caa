package kd.bos.tcbj.srm.scp.oplugin.validator;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.ExtendedDataEntity;
import kd.bos.entity.validate.AbstractValidator;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.util.Objects;

/**
 * 销售发货单提交时校验版本号是否只包含数字
 * @auditor hst
 * @createDate: 2023/04/23
 */
public class ScpOrderVersionSubmitVaildator extends AbstractValidator {

    @Override
    public void validate() {
        ExtendedDataEntity[] datas = this.getDataEntities();
        int index = 1;
        for (ExtendedDataEntity data : datas) {
            DynamicObject bill = data.getDataEntity();
            DynamicObjectCollection entries = bill.getDynamicObjectCollection("materialentry");
            for (DynamicObject entry : entries) {
                DynamicObject material = entry.getDynamicObject("material");
                if (Objects.nonNull(material)) {
                    String matNum = material.getString("number");
                    if (StringUtils.isNotBlank(matNum)
                            && (matNum.startsWith("D") || matNum.startsWith("E"))) {
                        String version = entry.getString("yd_version");
                        if (StringUtils.isBlank(version)) {
                            this.addErrorMessage(data,"请填写第" + index + "行分录编码以D和E开头的物料的版本号");
                        } else {
                            if (!version.matches("\\d+")) {
                                this.addErrorMessage(data, "第" + index + "行分录版本号存在非数字的字符");
                            }
                        }
                    }
                }
                index ++;
            }
        }
    }
}
