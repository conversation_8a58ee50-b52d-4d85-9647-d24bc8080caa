package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.control.Button;
import kd.bos.form.control.events.BeforeClickEvent;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.servicehelper.operation.SaveServiceHelper;

import java.util.EventObject;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.PbdOperateClosePlugin
 * @className PbdOperateClosePlugin
 * @author: hst
 * @createDate: 2022/11/09
 * @description: 打回原因填写添加采购订单特异功能
 * @version: v1.0
 */
public class PbdOperateClosePlugin extends AbstractFormPlugin {

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Button close = this.getControl("btnok");
        close.addItemClickListener(this);
        close.addClickListener(this);
    }

    @Override
    public void beforeClick(BeforeClickEvent evt) {
        super.beforeClick(evt);
        if (evt.getSource() instanceof Button && "close".equals(((Button) evt.getSource()).getOperationKey())) {
            String parentId = this.getView().getFormShowParameter().getParentFormId();
            //如果不是采购订单页面打开，就不用将数量异常和日期异常回写
            if ("scp_order".equals(parentId)) {
                boolean isNumError = (boolean) this.getModel().getValue("yd_isnumerror");
                boolean isDateError = (boolean) this.getModel().getValue("yd_isdateerr");
                DynamicObject parent = this.getView().getParentView().getModel().getDataEntity();
                parent.set("yd_isnumerror",isNumError);
                parent.set("yd_isdateerr",isDateError);
                SaveServiceHelper.save(new DynamicObject[]{parent});
            }
        }
    }

    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        String parentId = this.getView().getFormShowParameter().getParentFormId();
        //如果不是采购订单页面打开，隐藏特定字段
        if (!"scp_order".equals(parentId)) {
            this.getView().setVisible(false,"yd_flexpanelap");
        }
    }
}
