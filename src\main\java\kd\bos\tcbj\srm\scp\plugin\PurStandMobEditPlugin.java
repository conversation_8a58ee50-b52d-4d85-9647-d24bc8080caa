package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractMobBillPlugIn;
import kd.bos.form.control.MenuItem;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.tcbj.im.util.StringUtils;

import java.util.Date;
import java.util.EventObject;

public class PurStandMobEditPlugin extends AbstractMobBillPlugIn {

    /**
     * 控制按钮显示
     * @author: hst
     * @createDate: 2022/11/23
     * @param e
     */
    @Override
    public void afterLoadData(EventObject e) {
        String status = this.getModel().getValue("billstatus").toString();
        if ("A".equals(status)) {
            this.getView().setVisible(true,"yd_mtoolbarap");
        } else {
            this.getView().setVisible(true,"yd_mtoolbarap");
        }
    }
}
