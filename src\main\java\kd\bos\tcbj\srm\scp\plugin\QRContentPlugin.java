package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.form.FormShowParameter;
import kd.bos.form.control.QRCode;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.tcbj.im.plugin.KdepFormPlugin;

import java.util.EventObject;

/**
 * 二维码内容填充插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-21
 */
public class QRContentPlugin extends AbstractFormPlugin {

    @Override
    public void afterBindData(EventObject e) {
        FormShowParameter showParameter = this.getView().getFormShowParameter();
        String content = showParameter.getCustomParam("content");
        ((QRCode) this.getView().getControl("yd_qrcodeap")).setUrl(content);
    }
}
