package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.form.FormShowParameter;
import kd.bos.form.control.QRCode;
import kd.bos.form.control.Toolbar;
import kd.bos.form.control.events.ItemClickEvent;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.EventObject;

/**
 * @package: QrCodeEditPlugin
 * @className QrCodeEditPlugin
 * @author: hst
 * @createDate: 2022/08/30
 * @description: 二维码弹窗表单插件
 * @version: v1.0
 */
public class QrCodeEditPlugin extends AbstractBillPlugIn {

    /**
     * 监听工具栏点击按钮
     * @author: hst
     * @createDate: 2022/08/30
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Toolbar toolbar = this.getView().getControl("yd_toolbarap");
        toolbar.addItemClickListener(this);
    }

    /**
     * 按钮点击事件
     * @author: hst
     * @createDate: 2022/08/30
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case "yd_copy" : {
                String url = this.getModel().getValue("yd_url").toString();
                this.getView().showSuccessNotification("复制成功！");
                print(url);
                break;
            }
        }
    }

    /**
     * 字段绑定数据之前触发
     * 为二维码和url字段赋值
     * @author: hst
     * @createDate: 2022/08/30
     * @param e
     */
    @Override
    public void beforeBindData(EventObject e) {
        super.beforeBindData(e);
        FormShowParameter showParameter = this.getView().getFormShowParameter();
        // update by hst 2022/11/22 直接获取url
        String url = showParameter.getCustomParam("url");
        ((QRCode) this.getView().getControl("yd_qrcode")).setUrl(url);
        this.getModel().setValue("yd_url",url);
    }

    /**
     * 模拟键盘ctrl + c 复制文本
     * @author: hst
     * @createDate: 2022/08/30
     * @param text
     */
    public void print(String text) {
        try {
            ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
            ScriptEngine scriptEngine = scriptEngineManager.getEngineByName("js");
            scriptEngine.put("url",text);
            String script = "function copyUrl(url){ debugger; var aux = window.document.createElement(\"input\");" +
                    "var content = url;aux.setAttribute(\"value\", url);aux.select();" +
                    "document.execCommand(\"copy\");document.body.removeChild(aux);}";
            scriptEngine.eval(script);
            Invocable invocable = (Invocable) scriptEngine;
            String result = (String) invocable.invokeFunction("copyUrl", true);
            System.out.println(result);
        } catch (ScriptException e) {
            throw new KDException(e.getMessage());
        } catch (NoSuchMethodException e) {
            throw new KDException(e.getMessage());
        }
    }
}
