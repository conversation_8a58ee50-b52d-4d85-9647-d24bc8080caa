package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.list.plugin.IPCListPlugin;
import kd.bos.list.plugin.KDListPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.UIUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 销售发货单里列表校验插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-21
 */
public class SaleOutStockVerifyListPlugin extends KDListPlugin {

    public SaleOutStockVerifyListPlugin(IPCListPlugin plugin) {
        super(plugin);
    }

    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs e) {
        String operateKey = ((AbstractOperate) e.getSource()).getOperateKey();
        if (StringUtils.equals(operateKey, "submit")) {
            confirmSendVerify();
        }
    }

    /**
     * 确认发货校验
     * <AUTHOR>
     * @date 2022-10-21
     */
    private void confirmSendVerify() {

        /*
            如果商品明细分录的“生产日期”和“到期日期”有值时，需要校验有效期是否满足条件：
            ①到期日期-生产日期=总有效期；
            ②总有效期/2=总有效期的一半；
            ③到期日期-当前日期=生产日期距今天数；
            ④当总有效期的一半>生产日期距今天数时，允许操作确认发货；
            ⑤当总有效期的一半<=生产日期距今天数时，不允许操作确认发货，并触发工作流，由采购、质量审批通过后再自动执行确认发货）；
        */
        List<Object> selectIds = UIUtils.getSelectIds(this.getView());
        if (selectIds.size() == 0) return;
        // 遍历选择的单据
        StringBuilder builder = new StringBuilder();
        for (Object selectId : selectIds) {
            StringBuilder billBuilder = new StringBuilder();
            DynamicObject info = BizHelper.getDynamicObjectById("scp_saloutstock", selectId, "materialentry.proddate, materialentry.duedate");
            DynamicObjectCollection entryCol = info.getDynamicObjectCollection("materialentry");
            for (int index=0,size=entryCol.size(); index<size; index++) {
                DynamicObject entryInfo = entryCol.get(index);
                // 计算总有效日期
                Date proddate = entryInfo.getDate("proddate");// 生产日期
                Date duedate = entryInfo.getDate("duedate");// 到日期
                if (proddate == null || duedate == null) continue;
                long diffDay = DateTimeUtils.diffDay(proddate, duedate); // 总有效日期天数
                // 计算出生产日期到今天数
                long validDay = DateTimeUtils.diffDay(proddate, new Date()); // 时效数
                if (diffDay <= validDay*2) {
                    // 不允许发货
                    billBuilder.append("分录[").append(index).append("]总有效期一半>生产日期距今天数");
                }
            }
        }
    }
}
