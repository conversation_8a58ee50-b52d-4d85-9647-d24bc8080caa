package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.control.Button;
import kd.bos.form.control.Control;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.scp.constants.ScpAddAttachmentConstants;
import kd.bos.tcbj.srm.scp.constants.ScpSalOutStockConstants;


import java.util.EventObject;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpAddAttachmentEditPlugin
 * @className ScpAddAttachmentEditPlugin
 * @author: hst
 * @createDate: 2022/09/23
 * @version: v1.0
 */
public class ScpAddAttachmentEditPlugin extends AbstractBillPlugIn {

    private final static String BTN_COMFIRM = "yd_confirm";

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Button button = this.getControl(BTN_COMFIRM);
        button.addClickListener(this);
    }

    /**
     * 监听按钮点击按钮
     * @author: hst
     * @createDate: 2022/09/23
     * @param e
     */
    @Override
    public void click(EventObject e) {
        super.click(e);
        Control control = (Control) e.getSource();
        if (BTN_COMFIRM.equalsIgnoreCase(control.getKey())) {
            this.getView().returnDataToParent(this.getModel().getEntryEntity(ScpAddAttachmentConstants.ENTITY_MATERIAL));
            this.getView().close();
        }
    }

    /**
     * @author: hst
     * @createDate: 2022/09/23
     * @param e
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        Long pkId = this.getView().getFormShowParameter().getCustomParam("pkId");
        this.assignmentField(pkId);
    }

    /**
     * 字段赋值
     * @author: hst
     * @createDate: 2022/09/23
     * @param pkId
     */
    public void assignmentField (Long pkId) {
        if (pkId == null) {
            return;
        }
        DynamicObject salOutStocks = BusinessDataServiceHelper.loadSingle(pkId, ScpSalOutStockConstants.ENTITY_MAIN);
        if (salOutStocks != null) {
            this.getModel().setValue(ScpAddAttachmentConstants.FIELD_BILLNO,salOutStocks.get(ScpSalOutStockConstants.FIELD_BILLNO));
            this.getModel().setValue(ScpAddAttachmentConstants.FIELD_BILLDATE,salOutStocks.get(ScpSalOutStockConstants.FIELD_BILLDATE));
            DynamicObjectCollection materialentry = salOutStocks.getDynamicObjectCollection(ScpSalOutStockConstants.ENTITY_MATERIAL);
            DynamicObjectCollection addEntry = this.getModel().getEntryEntity(ScpAddAttachmentConstants.ENTITY_MATERIAL);
            for (DynamicObject material : materialentry) {
                DynamicObject addNew = addEntry.addNew();
                addNew.set(ScpAddAttachmentConstants.COLUMN_ENTRYID,material.get("id"));
                addNew.set(ScpAddAttachmentConstants.COLUMN_MATERIAL,
                        ((DynamicObject) material.get(ScpSalOutStockConstants.COLUMN_MATERIAL)).get("number"));
                addNew.set(ScpAddAttachmentConstants.COLUMN_MATNAME,
                        ((DynamicObject) material.get(ScpSalOutStockConstants.COLUMN_MATERIAL)).get("name"));
                addNew.set(ScpAddAttachmentConstants.COLUMN_QTY,material.get(ScpSalOutStockConstants.COLUMN_QTY));
                addNew.set(ScpAddAttachmentConstants.COLUMN_SUPMATNAME,material.get(ScpSalOutStockConstants.COLUMN_SUPMATNAME));
                addNew.set(ScpAddAttachmentConstants.COLUMN_PRODUCERS,material.get(ScpSalOutStockConstants.COLUMN_PRODUCERS));
            }
        }
    }
}
