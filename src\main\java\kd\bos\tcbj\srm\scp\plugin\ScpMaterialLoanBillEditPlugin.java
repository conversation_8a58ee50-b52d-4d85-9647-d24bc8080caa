package kd.bos.tcbj.srm.scp.plugin;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.EventObject;
import java.util.HashSet;
import java.util.Set;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.events.ChangeData;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.events.BeforeF7SelectEvent;
import kd.bos.form.field.events.BeforeF7SelectListener;
import kd.bos.list.ListShowParameter;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.epm.eb.common.utils.StringUtils;
import kd.scm.common.util.BizPartnerUtil;


//进口物料预付申请单
public class ScpMaterialLoanBillEditPlugin extends AbstractBillPlugIn implements BeforeF7SelectListener {

    /**
     * 事件注册
     * @param e
     * @author: hst
     * @createDate: 2024/03/25
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);

        // 区域公司选择前监听
        BasedataEdit payerbank = this.getView().getControl("yd_payerbank");
        payerbank.addBeforeF7SelectListener(this);
    }
	
	@Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        this.setDefaultSupplierValue();
    }

    private void setDefaultSupplierValue() {
        IDataModel model = this.getModel();
        // "供应商"默认带出当前登陆人所属供应商
        BizPartnerBizHelper bizPartnerBizHelper = new BizPartnerBizHelper();
        Set<String> currentBdSupplierId = bizPartnerBizHelper.getCurrentBdSupplierId();
        Set<String> currentBdSupplierNums = bizPartnerBizHelper.getCurrentBdSupplierNums();
        currentBdSupplierId.size();
        if(currentBdSupplierNums.contains("1.01.*********")){
            DynamicObject supplierInfo = BusinessDataServiceHelper.loadSingle("bd_supplier", QFilter.of("number = ? and enable = ?", "1.01.*********", "1").toArray());
            model.setValue("yd_supplier",supplierInfo);
            DynamicObjectCollection bankEntrys = supplierInfo.getDynamicObjectCollection("entry_bank");
            DynamicObject supBankInfo=null;
            for(DynamicObject eBankEntryInfo:bankEntrys)
            {
                if(eBankEntryInfo.getBoolean("isdefault_bank"))
                {
                    supBankInfo=eBankEntryInfo;
                }
            }
            if(supBankInfo ==null) {
                return;
            }
            int rowIndex = this.getModel().getEntryCurrentRowIndex("yd_bankentity");
            this.getModel().setValue("yd_payername", supplierInfo.getString("name"), rowIndex);
            this.getModel().setValue("yd_payerbank", supBankInfo.getDynamicObject("bank"), rowIndex);
            this.getModel().setValue("yd_payeraccount", supBankInfo.getString("bankaccount"), rowIndex);
            this.getModel().setValue("yd_payeraccountname", supBankInfo.getString("accountname"), rowIndex);
            if(supBankInfo.getDynamicObject("bank")!=null) {
                DynamicObject bebankInfo = supBankInfo.getDynamicObject("bank");
                bebankInfo = BusinessDataServiceHelper.loadSingle(bebankInfo.getPkValue(),"bd_bebank");
                this.getModel().setValue("yd_banknumber", bebankInfo.getString("union_number"), rowIndex);
                this.getModel().setValue("yd_bankprovince", bebankInfo.getString("provincetxt"), rowIndex);
                this.getModel().setValue("yd_bankcity", bebankInfo.getString("citytxt"), rowIndex);
            }
        }
    }

    @Override
    public void propertyChanged(PropertyChangedArgs e) {
        super.propertyChanged(e);
        IDataModel model = this.getModel();
        ChangeData[] changeDatas = e.getChangeSet();
        //通过值更新事件来触发
        String propertyName = e.getProperty().getName();
        if("yd_payerbank".equals(propertyName)){
            if(changeDatas.length>0){
                ChangeData changeData = changeDatas[0];
                int rowIndex = changeData.getRowIndex();
                DynamicObject supplier = (DynamicObject) model.getValue("yd_supplier");
                if(supplier!=null){
                    supplier = BusinessDataServiceHelper.loadSingle(supplier.getPkValue(),"bd_supplier");
                    DynamicObjectCollection bankEntrys = supplier.getDynamicObjectCollection("entry_bank");
                    DynamicObject payeraccount = (DynamicObject)model.getValue("yd_payerbank");
                    if(payeraccount!=null){
                        for(DynamicObject eBankEntryInfo:bankEntrys)
                        {
                            DynamicObject bank = eBankEntryInfo.getDynamicObject("bank");
                            if(bank == null){
                                continue;
                            }
                            if(bank.getPkValue().equals(payeraccount.getPkValue())){
                                model.setValue("yd_payeraccount", eBankEntryInfo.getString("bankaccount"), rowIndex);
                                model.setValue("yd_payeraccountname", eBankEntryInfo.getString("accountname"), rowIndex);
                            }
                        }
                    }else {
                        model.setValue("yd_payeraccount", "", rowIndex);
                        model.setValue("yd_payeraccountname", "", rowIndex);
                    }
                }
            }
        }else if("yd_locamount".equals(propertyName)){
            DynamicObjectCollection entryentity = model.getEntryEntity("entryentity");
            BigDecimal sum = BigDecimal.ZERO;
            for (DynamicObject entryItem : entryentity) {
                BigDecimal locamount = entryItem.getBigDecimal("yd_locamount");
                sum = sum.add(locamount);
            }
            DynamicObjectCollection bankentity = model.getEntryEntity("yd_bankentity");
            for (int i = 0; i < bankentity.size(); i++) {
                model.setValue("yd_orireceiveamount",sum,i);
            }
        }
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs e) {
        super.afterDoOperation(e);
        String key = e.getOperateKey();
        if ("newentry".equals(key)) {
            DynamicObject entrycostdept = BusinessDataServiceHelper.loadSingle("bos_org", QFilter.of("number = ? and enable = ?", "000263", "1").toArray());
            this.getModel().setValue("yd_entrycostdept",entrycostdept);
            DynamicObject budgetDept = BusinessDataServiceHelper.loadSingle("bos_org", QFilter.of("number = ? and enable = ?", "000392", "1").toArray());
            this.getModel().setValue("yd_budgetdept",budgetDept);

        }
    }


    @Override
    public void beforeF7Select(BeforeF7SelectEvent e) {
        String name = e.getProperty().getName();
        IDataModel model = this.getModel();
        if (StringUtils.equals("yd_payerbank", name)) {
            ListShowParameter showParameter = (ListShowParameter) e.getFormShowParameter();
            Set<String> bankSet = new HashSet<>();
            DynamicObject supplier = (DynamicObject) model.getValue("yd_supplier");
            if(supplier!=null){
                supplier = BusinessDataServiceHelper.loadSingle(supplier.getPkValue(),"bd_supplier");
                DynamicObjectCollection bankEntrys = supplier.getDynamicObjectCollection("entry_bank");
                for(DynamicObject eBankEntryInfo:bankEntrys)
                {
                    DynamicObject bank = eBankEntryInfo.getDynamicObject("bank");
                    if(bank != null){
                        bankSet.add(bank.getString("id"));
                    }
                }
            }
            QFilter qFilter = new QFilter("id", QCP.in, bankSet);
            showParameter.getListFilterParameter().setFilter(qFilter);
        }
    }
}
