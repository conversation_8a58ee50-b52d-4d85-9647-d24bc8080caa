package kd.bos.tcbj.srm.scp.plugin;

import java.util.Set;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;

//进口物料预付申请单
public class ScpMaterialLoanBillListPlugin extends AbstractListPlugin {
	

    @Override
    public void setFilter(SetFilterEvent e) {
        // 获取当前的供应商信息
        Set<String> supplierIds = new BizPartnerBizHelper().getCurrentBdSupplierId();
        // 对供应商做过滤
        if (supplierIds == null) {
            e.getQFilters().add(QFilter.of("1=0"));
        }else {
            e.getQFilters().add(new QFilter("yd_supplier.id", QCP.in, supplierIds));
        }
    }

}
