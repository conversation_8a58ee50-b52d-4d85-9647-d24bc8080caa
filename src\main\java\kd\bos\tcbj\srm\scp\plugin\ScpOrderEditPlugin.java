package kd.bos.tcbj.srm.scp.plugin;

import java.util.Date;
import java.util.EventObject;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.servicehelper.operation.OperationServiceHelper;

/**
 * 供应方采购订单编辑界面插件
 * 1.对交货计划按交货日期进行排序；
 * @auditor yanzuliang
 * @date 2022年11月22日
 * 
 */
public class ScpOrderEditPlugin extends AbstractBillPlugIn {
	
	@Override
	public void afterBindData(EventObject e) {
		super.afterBindData(e);

		DynamicObjectCollection entry = this.getModel().getEntryEntity("yd_deliveryplan");
		// 朴实无华的冒泡排序算法，你可以选用性能更好的排序算法
		for (int i = 0; i < entry.size() - 1; i++) {
			for (int j = 0; j < entry.size() - 1 - i; j++) {
				Date date = entry.get(j).getDate("yd_deliverydate");
				Date date1 = entry.get(j + 1).getDate("yd_deliverydate");
				if (date.after(date1)) {
					// 单据体行下移
					this.getModel().moveEntryRowDown("yd_deliveryplan", j);
				}
			}
		}
		this.getView().updateView("yd_deliveryplan");

	}
	
	/**
	 * 因为已审核单据触发确认操作时数据懒加载，所以要触发一下保存方法，yzl,20230228
	 */
	@Override
	public void beforeItemClick(BeforeItemClickEvent e) {
		super.beforeItemClick(e);
		if ("agreeorder".equalsIgnoreCase(e.getOperationKey())) {
			DynamicObject tmpObj = this.getModel().getDataEntity(true);
			OperationServiceHelper.executeOperate("testop", "scp_order", new DynamicObject[] {tmpObj}, OperateOption.create());
		}
	}
}
