package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;

import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpOrderFilterListPlugin
 * @className ScpOrderFilterListPlugin
 * @author: hst
 * @createDate: 2023/11/23
 * @description: 采购订单业务类型过滤插件
 * @version: v1.0
 */
public class ScpOrderFilterListPlugin extends AbstractListPlugin {

    /**
     * 根据页面参数设置过滤条件（业务类型）
     * @param e
     */
    @Override
    public void setFilter(SetFilterEvent e) {
        List filterLists = e.getQFilters();
        if (this.getView().getFormShowParameter().getCustomParam("businesstype") != null) {
            String businesstype = this.getView().getFormShowParameter().getCustomParam("businesstype");
            String[] values = businesstype.split(",");
            filterLists.add(new QFilter("businesstype.number", QCP.in, values));
        }
        super.setFilter(e);
    }
}
