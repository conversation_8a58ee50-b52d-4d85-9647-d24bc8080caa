package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.context.RequestContext;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.tcbj.srm.scp.schedulejob.ScpOrderTask;

import java.util.HashMap;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpOrderListPlugin
 * @className ScpOrderListPlugin
 * @author: hst
 * @createDate: 2022/09/21
 * @version: v1.0
 */
public class ScpOrderListPlugin extends AbstractListPlugin {

    private final static String BTN_REMINDER = "yd_reminder";

    /**
     * 按钮点击事件
     * @author: hst
     * @createDate: 2022/09/13
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case BTN_REMINDER : {
                Map<String,Object> map = new HashMap<>();
                map.put("key","timeoutReminder");
                new ScpOrderTask().execute(RequestContext.get(),map);
                break;
            }
        }
    }
}

