package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.ConfirmCallBackListener;
import kd.bos.form.ConfirmTypes;
import kd.bos.form.MessageBoxOptions;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.scp.constants.ScpPurCycleConstant;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;

import java.util.EventObject;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpPurCycleChangeEditPlugin
 * @className ScpPurCycleChangeEditPlugin
 * @author: hst
 * @createDate: 2024/06/10
 * @version: v1.0
 * @descrition: 供应商端供应商采购周期变更单表单界面插件
 */
public class ScpPurCycleChangeEditPlugin extends AbstractBillPlugIn {

    // 确认变更按钮标识
    private final static String CONFIRM_BTN = "yd_confirm";
    // 确认弹窗回调标识
    private final static String CONFIRM_CALLBACK = "confirm_callback";

    /**
     * 按钮点击事件
     * @param evt
     * @author: hst
     * @createDate: 2024/06/10
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case CONFIRM_BTN : {
                this.getView().showConfirm("是否继续提交？", MessageBoxOptions.YesNo, ConfirmTypes.Default
                        , new ConfirmCallBackListener(CONFIRM_CALLBACK, this));
                break;
            }
        }
    }

    /**
     * 数据加载完毕之后
     * @param e
     * @author: hst
     * @createDate: 2024/06/10
     */
    @Override
    public void afterLoadData(EventObject e) {
        super.afterLoadData(e);
        this.setHistoryInfo();
    }

    /**
     * 确认弹窗点击确认后回调事件
     * @author: hst
     * @createDate: 2024/06/10
     * @param messageBoxClosedEvent
     */
    @Override
    public void confirmCallBack(MessageBoxClosedEvent messageBoxClosedEvent) {
        super.confirmCallBack(messageBoxClosedEvent);
        if (CONFIRM_CALLBACK.equals(messageBoxClosedEvent.getCallBackId())
                && messageBoxClosedEvent.getResult().getValue() == 6) {
            this.getModel().setValue(ScpPurCycleConstant.CONSTATUS_FIELD, "A");
            SaveServiceHelper.saveOperate(ScpPurCycleConstant.MAIN_ENTITY,
                    new DynamicObject[]{this.getModel().getDataEntity(true)});
            this.getView().showSuccessNotification("保存成功");
            this.getView().close();
        }
    }

    /**
     * 添加历史版本记录
     */
    private void setHistoryInfo () {
        int index = this.getModel().createNewEntryRow(ScpPurCycleConstant.ENTRY_ENTITY);
        this.getModel().setValue(ScpPurCycleConstant.PLAN_COLUMN,
                this.getModel().getValue(ScpPurCycleConstant.DELDAYS_FIELD), index);
        this.getModel().setValue(ScpPurCycleConstant.SAFEDAY_COLUMN,
                this.getModel().getValue(ScpPurCycleConstant.SAFEDAYS_FIELD), index);
        this.getModel().setValue(ScpPurCycleConstant.EARLYDAY_COLUMN,
                this.getModel().getValue(ScpPurCycleConstant.ADVDAYS_FIELD), index);
        this.getModel().setValue(ScpPurCycleConstant.REPLENISH_COLUMN,
                this.getModel().getValue(ScpPurCycleConstant.CPFRDAYS_FIELD), index);
        this.getModel().setValue(ScpPurCycleConstant.CAPACITY_COLUMN,
                this.getModel().getValue(ScpPurCycleConstant.ENERGY_FIELD), index);
        this.getModel().setValue(ScpPurCycleConstant.HISVERSION_COLUMN,
                this.getModel().getValue(ScpPurCycleConstant.VERSION_FIELD), index);

        this.getModel().setValue(ScpPurCycleConstant.VERSION_FIELD,
                this.getModel().getDataEntity().getInt(ScpPurCycleConstant.VERSION_FIELD) + 1);

        DynamicObjectUtil.setBizChanged(this.getModel().getDataEntity(), false,
                new String[]{ScpPurCycleConstant.PLAN_COLUMN, ScpPurCycleConstant.SAFEDAY_COLUMN
                        , ScpPurCycleConstant.EARLYDAY_COLUMN, ScpPurCycleConstant.REPLENISH_COLUMN
                        , ScpPurCycleConstant.CAPACITY_COLUMN, ScpPurCycleConstant.HISVERSION_COLUMN
                        , ScpPurCycleConstant.VERSION_FIELD});
    }
}
