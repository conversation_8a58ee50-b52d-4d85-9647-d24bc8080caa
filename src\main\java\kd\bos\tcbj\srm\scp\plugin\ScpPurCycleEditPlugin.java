package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.field.BasedataEdit;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.scp.constants.ScpPurCycleConstant;
import kd.scm.common.util.BizPartnerUtil;

import java.util.EventObject;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpPurCycleEditPlugin
 * @className ScpPurCycleEditPlugin
 * @author: hst
 * @createDate: 2024/06/07
 * @version: v1.0
 * @descrition: 供应商端供应商采购周期单表单界面插件
 */
public class ScpPurCycleEditPlugin extends AbstractBillPlugIn {

    /**
     * 事件注册
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        // F7弹窗前事件监听
        this.addBeforeF7Select();
    }

    /**
     * 新增单据时触发
     * @param e
     * @author: hst
     * @createDate: 2024/06/07
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        // 设置供应商默认值
        this.setDefaultSupplierValue();
    }

    /**
     * 设置供应商默认值
     * @author: hst
     * @createDat: 2024/06/07
     */
    private void setDefaultSupplierValue() {
        // 获取当前用户关联的供应商
        QFilter supF = BizPartnerUtil.assembleQFilterBizPartner();
        DynamicObject[] supplier = BusinessDataServiceHelper.load("srm_supplier", "id,name,number", supF.toArray());

        if (supplier.length > 0) {
            this.getModel().setValue("yd_supplier",supplier[0]);
        }
    }

    /**
     * F7弹窗前事件监听
     * @author: hst
     * @createDate: 2024/06/07
     */
    private void addBeforeF7Select() {
        // 供应商
        BasedataEdit supplierEdit = this.getView().getControl("yd_supplier");
        supplierEdit.addBeforeF7SelectListener((evt) -> {
            // 获取当前用户关联的供应商
            QFilter supF = BizPartnerUtil.assembleQFilterBizPartner();
            evt.getCustomQFilters().add(supF);
        });

        // 物料
        BasedataEdit materialEdit = this.getView().getControl("yd_material");
        materialEdit.addBeforeF7SelectListener((evt) -> {
            DynamicObject supplier = this.getModel().getDataEntity(true).getDynamicObject(ScpPurCycleConstant.SUPPLIER_FIELD);
            if (Objects.isNull(supplier)) {
                this.getView().showTipNotification("请先选择供应商！");
                evt.setCancel(true);
                return;
            }

            QFilter supNumF = new QFilter("yd_supnum", QCP.equals, supplier.getString("number"));
            Set<String> matNumSet = new HashSet<String>();
            DynamicObjectCollection listCol = QueryServiceHelper.query("yd_supplymatlist",
                    "id,name,number,yd_matnum", supNumF.toArray());
            for (DynamicObject listObj : listCol) {
                matNumSet.add(listObj.getString("yd_matnum"));
            }

            QFilter matNumF = new QFilter("number", QCP.in, matNumSet);
            evt.getCustomQFilters().add(matNumF);
        });
    }
}
