package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.bill.BillShowParameter;
import kd.bos.bill.OperationStatus;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.IListView;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.scp.constants.ScpPurCycleConstant;
import kd.scm.common.util.BizPartnerUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpPurCycleListPlugin
 * @className: ScpPurCycleListPlugin
 * @description: 供应商采购周期单列表插件
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 */
public class ScpPurCycleListPlugin extends AbstractListPlugin {

    // 更新库存按钮标识
    private final static String UPDATEINV_BTN = "yd_updateinv";
    // 更新库存回调标识
    private final static String UPDATE_CALLBACK = "update_callback";
    // 变更按钮标识
    private final static String CHANGE_BTN = "yd_change";
    // 变更回调标识
    private final static String CHANGE_CALLBACK = "change_callback";

    /**
     * 按钮点击事件
     * @param evt
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case UPDATEINV_BTN : {
                // 打开库存更新页面
                this.openUpdateInventryPage();
                break;
            }
            case CHANGE_BTN : {
                // 打开变更单界面
                this.openChangePage();
                break;
            }
        }
    }

    /**
     * 设置过滤条件
     * @param e
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void setFilter(SetFilterEvent e) {
        super.setFilter(e);
        // 获取当前用户关联的供应商
        QFilter supFilter = BizPartnerUtil.assembleQFilterBizPartner();
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),
                "bd_supplier", "id", new QFilter[]{supFilter}, null);

        List<String> supplierIds = new ArrayList<>();
        for (Row row : dataSet) {
            supplierIds.add(row.getString("id"));
        }

        if (supplierIds.size() > 0) {
            e.getQFilters().add(new QFilter(ScpPurCycleConstant.SUPPLIER_FIELD + ".id", QFilter.in, supplierIds));
        } else {
            e.getQFilters().add(QFilter.of("1 != 1"));
        }
    }

    /**
     * 子页面关闭回调事件
     * @param closedCallBackEvent
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        String actionId = closedCallBackEvent.getActionId();
        Object returndata = closedCallBackEvent.getReturnData();
        switch (actionId) {
            case UPDATE_CALLBACK: {
                // 显示更新结果
                if (Objects.nonNull(returndata)) {
                    this.showUpdateResult(returndata);
                }
            }
            case CHANGE_CALLBACK: {
                this.getView().invokeOperation("refresh");
                break;
            }
        }
    }

    /**
     * 打开库存更新页面
     * @author: hst
     * @createDate: 2024/06/09
     */
    private void openUpdateInventryPage() {
        List<Object> billIds = ((IListView) this.getView()).getSelectedRows().stream()
                .map(ListSelectedRow::getPrimaryKeyValue).distinct().collect(Collectors.toList());

        if (billIds.size() == 0) {
            this.getView().showTipNotification("请至少选择一行数据");
        } else {
            FormShowParameter formShowParameter = new FormShowParameter();
            formShowParameter.setFormId("yd_scp_updateinv");
            StyleCss styleCss = new StyleCss();
            formShowParameter.setCustomParam("billIds",billIds);
            formShowParameter.getOpenStyle().setInlineStyleCss(styleCss);
            formShowParameter.setCaption("库存更新");
            formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
            formShowParameter.setCloseCallBack(new CloseCallBack(this, UPDATE_CALLBACK));
            this.getView().showForm(formShowParameter);
        }
    }

    /**
     * 显示更新结果
     * @param returndata
     * @author: hst
     * @createDate: 2024/06/09
     */
    private void showUpdateResult (Object returndata) {
        Map<String,String> result = (Map<String,String>) returndata;
        if ("true".equals(result.get("isSuccess"))) {
            this.getView().showSuccessNotification("更新成功！");
            this.getView().invokeOperation("refresh");
        } else {
            this.getView().showTipNotification("更新失败，请联系管理员，失败原因：" + result.get("errMsg"));
        }
    }

    /**
     * 打开变更单页面
     * @author: hst
     * @createDate: 2024/06/10
     */
    private void openChangePage() {
        List<Object> billIds = ((IListView) this.getView()).getSelectedRows().stream()
                .map(ListSelectedRow::getPrimaryKeyValue).distinct().collect(Collectors.toList());

        if (billIds.size() != 1) {
            this.getView().showTipNotification("请选择一行数据");
        } else {
            Object billId = billIds.get(0);
            DynamicObject bill = BusinessDataServiceHelper.loadSingle(billId,ScpPurCycleConstant.MAIN_ENTITY);

            if (Objects.nonNull(bill)) {
                // 采购方确认状态
                String conStatus = bill.getString(ScpPurCycleConstant.CONSTATUS_FIELD);

                if ("B".equals(conStatus)) {
                    BillShowParameter billShowParameter = new BillShowParameter();
                    billShowParameter.setFormId(ScpPurCycleConstant.MAIN_ENTITY + "_change");
                    StyleCss styleCss = new StyleCss();
                    billShowParameter.setPkId(billId);
                    billShowParameter.getOpenStyle().setInlineStyleCss(styleCss);
                    billShowParameter.setCaption("供应商采购周期变更单");
                    billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
                    billShowParameter.setStatus(OperationStatus.EDIT);
                    billShowParameter.setCloseCallBack(new CloseCallBack(this, CHANGE_CALLBACK));
                    this.getView().showForm(billShowParameter);
                } else {
                    this.getView().showTipNotification("采购方未确认，无需发起变更");
                }
            } else {
                this.getView().showTipNotification("系统异常，所选数据不存在，请刷新后再次尝试");
            }
        }
    }
}
