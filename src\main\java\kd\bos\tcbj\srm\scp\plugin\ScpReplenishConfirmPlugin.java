package kd.bos.tcbj.srm.scp.plugin;

import java.util.EventObject;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.control.Button;
import kd.bos.form.control.Control;
import kd.bos.servicehelper.BusinessDataServiceHelper;

/**
 * 供应商确认补货计划界面插件
 * @auditor yanzuliang
 * @date 2022年10月22日
 * 
 */
public class ScpReplenishConfirmPlugin extends AbstractBillPlugIn {
	
	private final static String BTN_COMFIRM = "btnok";

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Button button = this.getControl(BTN_COMFIRM);
        button.addClickListener(this);
    }

    /**
     * 监听按钮点击按钮
     * @author: yzl
     * @createDate: 2022/10/21
     * @param e
     */
    @Override
    public void click(EventObject e) {
        super.click(e);
        Control control = (Control) e.getSource();
        if (BTN_COMFIRM.equalsIgnoreCase(control.getKey())) {
            this.getView().returnDataToParent(this.getModel().getEntryEntity("yd_entryentity"));
            this.getView().close();
        }
    }

    /**
     * @author: yzl
     * @createDate: 2022/10/22
     * @param e
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        Long pkId = this.getView().getFormShowParameter().getCustomParam("pkId");
        this.assignmentField(pkId);
    }

    /**
     * 字段赋值
     * @author: yzl
     * @createDate: 2022/10/22
     * @param pkId
     */
    public void assignmentField (Long pkId) {
        if (pkId == null) {
            return;
        }
        DynamicObject salOutStocks = BusinessDataServiceHelper.loadSingle(pkId, "yd_scp_replenish");
        if (salOutStocks != null) {
            this.getModel().setValue("yd_billno",salOutStocks.get("billno"));
            this.getModel().setValue("yd_bizdate",salOutStocks.get("yd_billdate"));
            DynamicObjectCollection enCol = salOutStocks.getDynamicObjectCollection("entryentity");
            DynamicObjectCollection addEntry = this.getModel().getEntryEntity("yd_entryentity");
            for (DynamicObject enObj : enCol) {
                DynamicObject addNew = addEntry.addNew();
                addNew.set("yd_entryid",enObj.get("id"));
                addNew.set("yd_material",enObj.getDynamicObject("yd_masterial"));
                addNew.set("yd_model",enObj.get("yd_model"));
                addNew.set("yd_unit",enObj.getDynamicObject("yd_unit"));
                addNew.set("yd_replenishqty",enObj.get("yd_replenishqty"));
                addNew.set("yd_confirmqty",enObj.get("yd_confirmqty"));
                addNew.set("yd_confirmdate",enObj.get("yd_confirmdate"));
                addNew.set("yd_logbillno",enObj.get("yd_logbillno"));
                addNew.set("yd_supmatname",enObj.get("yd_supmatname"));
                addNew.set("yd_producers",enObj.get("yd_producers"));
                if (enObj.get("yd_confirmdate") != null) {  // 之前已经确认过的
                	addNew.set("yd_beconfirmed",true);
                }
            }
        }
    }
}
