package kd.bos.tcbj.srm.scp.plugin;

import java.util.EventObject;

import org.apache.commons.lang3.StringUtils;

import kd.bos.armor.core.util.StringUtil;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.im.util.UIUtils;
import kd.fi.cas.helper.OperateServiceHelper;

/**
 * 补货单插件
 * @auditor yanzuliang
 * @date 2022年10月22日
 * 
 */
public class ScpReplenishEditPlugin extends AbstractBillPlugIn {
	
	private final static String BTN_CONFIRMPLAN = "yd_confirmplan";
	private final static String BTN_BACKPLAN = "yd_backplan";
	private static final String DynamicObjectCollection = null;
	
	@Override
    public void afterBindData(EventObject e) {
        // 设置供应方字段必填
        UIUtils.setRequired(this.getView(), new String[] {"yd_confirmqty","yd_confirmdate"});
        if (StringUtils.equals("C", (String)this.getModel().getValue("billstatus"))) {
            this.getView().setEnable(false, "yd_confirmplan");
        }
    }

    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs evt) {
        String operateKey = ((AbstractOperate) evt.getSource()).getOperateKey();
        if (StringUtils.equals(operateKey, "confirm")) {
            // 检查分录供应方字段是否已经填写
            for (int index=0, size=this.getModel().getEntryRowCount("entryentity"); index<size; index++) {
                Object supComfirmQty = this.getModel().getValue("yd_confirmqty", index);
                Object supComfirmDate = this.getModel().getValue("yd_confirmdate", index);
                // 判断供应商确认数量是否为空
                if (supComfirmQty == null) {
                    this.getView().showTipNotification(String.format("第%s行分录，供应商确认数量不能为空！", index+1));
                    evt.setCancel(true);
                    return;
                }
                // 判断供应商确认日期是否为空
                if (supComfirmDate == null) {
                    this.getView().showTipNotification(String.format("第%s行分录，供应商确认时间不能为空！", index+1));
                    evt.setCancel(true);
                    return;
                }
            }
        }
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs evt) {
        String operateKey = evt.getOperateKey();
        if (StringUtils.equals(operateKey, "confirm")) {
            // 刷新界面
            this.getView().updateView();
        }
    }
//	
//	/**
//	 * 监听工具栏点击按钮
//	 * 
//	 * @author: yzl
//	 * @createDate: 2022/10/22
//	 * @param evt
//	 */
//	@Override
//	public void itemClick(ItemClickEvent evt) {
//		super.itemClick(evt);
//		String key = evt.getItemKey();
//		switch (key) {
//			case BTN_CONFIRMPLAN : {
//				this.confirmPlan();
//				break;
//			}
//		}
//	}
//
//	/**
//	 * 弹出补货确认界面
//	 */
//	private void confirmPlan() {
//		FormShowParameter formShowParameter = new FormShowParameter();
//        formShowParameter.setCaption("确认补货计划");
//        formShowParameter.setFormId("yd_scp_replenish_comfirm");
//        StyleCss css = new StyleCss();
//        css.setWidth("1100");
//        css.setHeight("500");
//        formShowParameter.getOpenStyle().setInlineStyleCss(css);
//        formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
//        formShowParameter.setCustomParam("pkId",this.getModel().getValue("id"));
//        formShowParameter.setCloseCallBack(new CloseCallBack(this, "yd_scp_replenish_comfirm"));
//        this.getView().showForm(formShowParameter);
//	}
//	
//	/**
//     * 子页面关闭后触发
//     * 回写附件信息
//     * @author: yzl
//     * @createDate: 2022/10/22
//     * @param closedCallBackEvent
//     */
//    @Override
//    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
//        super.closedCallBack(closedCallBackEvent);
//        try {
//            if (closedCallBackEvent.getReturnData() != null) {
//                // 将供应商确认的数据反写到补货单上，再调用EAS接口反写数据
//            	String billId = this.getModel().getValue("id").toString();
//            	DynamicObjectCollection returnDatas = (DynamicObjectCollection) closedCallBackEvent.getReturnData();
//            	
//            	DynamicObject dynamicObject = BusinessDataServiceHelper.loadSingle(billId, "yd_scp_replenish");
//                for (DynamicObject returnData : returnDatas) {
//                    for (DynamicObject material : dynamicObject.getDynamicObjectCollection("entryentity")) {
//                        if (StringUtil.equals(returnData.getString("yd_entryid"), material.getString("id"))) {
//                        	material.set("yd_confirmqty", returnData.getBigDecimal("yd_confirmqty"));
//                        	material.set("yd_confirmdate", returnData.getDate("yd_confirmdate"));
//                        	material.set("yd_logbillno", returnData.getString("yd_logbillno"));
//                        }
//                    }
//                }
//                OperateServiceHelper.execOperate("save","yd_scp_replenish", new DynamicObject[]{dynamicObject}, OperateOption.create());
//            	
//                this.getView().showSuccessNotification("保存成功");
//                this.getView().invokeOperation("refresh");
//                
//                OperateServiceHelper.execOperate("confirmtoeas","yd_scp_replenish", new DynamicObject[]{dynamicObject}, OperateOption.create());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            this.getView().showErrorNotification("系统异常，请联系管理员！");
//        }
//    }
}
