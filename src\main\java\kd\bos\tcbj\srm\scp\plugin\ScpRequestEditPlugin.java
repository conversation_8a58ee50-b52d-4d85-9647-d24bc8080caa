package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.entity.api.ApiResult;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.scp.helper.ScpRequestHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpRequestEditPlugin
 * @className ScpRequestEditPlugin
 * @author: hst
 * @createDate: 2022/09/13
 * @description: 退货通知表单操作插件
 * @version: v1.0
 */
public class ScpRequestEditPlugin extends AbstractBillPlugIn {

    private final static String BTN_ADOPT = "yd_adopt";

    /**
     * 按钮点击事件
     * @author: hst
     * @createDate: 2022/09/13
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case BTN_ADOPT : {
                //update by hst 2022/11/07 不允许重复通过
            	// 单据已经打回不允许重复确认,yzl
                String method = this.getModel().getValue("yd_returnmethod").toString();
                String confirmState = this.getModel().getValue("cfmstatus").toString();
                if (StringUtils.isBlank(method) && "A".equalsIgnoreCase(confirmState)) {
                    this.deliveryRequestPop();
                }else {
                    this.getView().showTipNotification("单据已确认或已打回,无需再次确认！");
                }
                break;
            }
        }
    }

    /**
     * 操作执行之后调用
     * @author: hst
     * @createDate: 2022/09/14
     * @param afterDoOperationEventArgs
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs afterDoOperationEventArgs) {
        super.afterDoOperation(afterDoOperationEventArgs);
        String key = new ScpRequestHelper().operationObjectType(afterDoOperationEventArgs);
        switch (key) {
            case "confirm" : {
                List<Object> masterIds = new ArrayList<>();
                masterIds.add(this.getModel().getDataEntity().getPkValue());
                //向仓库发送邮件
                ApiResult result = new ScpRequestHelper().sendEmail(masterIds);
                if (!result.getSuccess()) {
                    this.getView().showErrorNotification(result.getMessage());
                }
                this.getView().invokeOperation("refresh");
                break;
            }
        }
    }

    /**
     * 退货确认框
     * @author: hst
     * @createDate: 2022/09/13
     */
    public void deliveryRequestPop () {
        FormShowParameter formShowParameter = new FormShowParameter();
        formShowParameter.setCaption("退货确认框");
        formShowParameter.setFormId("yd_scp_request_pop");
        StyleCss css = new StyleCss();
        css.setWidth("600");
        css.setHeight("250");
        formShowParameter.getOpenStyle().setInlineStyleCss(css);
        formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
        formShowParameter.setCustomParam("pkIds",this.getModel().getDataEntity().getPkValue());
        formShowParameter.setCloseCallBack(new CloseCallBack(this,"yd_scp_request_pop"));
        this.getView().showForm(formShowParameter);
    }

    /**
     * 获取退货选项弹窗中用户输入的信息
     * @author: hst
     * @createDate: 2022/09/14
     * @param closedCallBackEvent
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        Map<String, Object> data = (Map<String, Object>) closedCallBackEvent.getReturnData();
        if (data != null) {
            new ScpRequestHelper().assignmentField(new Object[]{this.getModel().getDataEntity().getPkValue()}, data);
            this.getView().invokeOperation("confirm");
        }
    }
}
