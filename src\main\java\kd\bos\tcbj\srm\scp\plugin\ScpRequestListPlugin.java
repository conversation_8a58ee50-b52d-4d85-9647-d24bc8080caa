package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.entity.api.ApiResult;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.scp.helper.ScpRequestHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpRequestListPlugin
 * @className ScpRequestListPlugin
 * @author: hst
 * @createDate: 2022/09/13
 * @description: 退货通知列表插件
 * @version: v1.0
 */
public class ScpRequestListPlugin extends AbstractListPlugin {

    private final static String BTN_ADOPT = "yd_tbladopt";

    /**
     * 按钮点击事件
     * @author: hst
     * @createDate: 2022/09/13
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case BTN_ADOPT : {
                this.deliveryRequestPop();
                break;
            }
        }
    }

    /**
     * 操作执行之后调用
     * @author: hst
     * @createDate: 2022/09/14
     * @param afterDoOperationEventArgs
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs afterDoOperationEventArgs) {
        super.afterDoOperation(afterDoOperationEventArgs);
        String key = new ScpRequestHelper().operationObjectType(afterDoOperationEventArgs);
        switch (key) {
            case "confirm" : {
                OperationResult operationResult = afterDoOperationEventArgs.getOperationResult();
                if (operationResult.isSuccess()) {
                    List<Object> successPkIds = operationResult.getSuccessPkIds();
                    //向仓库发送邮件
                    ApiResult result = new ScpRequestHelper().sendEmail(successPkIds);
                    if (!result.getSuccess()) {
                        this.getView().showErrorNotification(result.getMessage());
                    }
                }
                break;
            }
        }
    }



    /**
     * 退货确认框
     * @author: hst
     * @createDate: 2022/09/13
     */
    public void deliveryRequestPop () {
        List<Object> masterIds = new ArrayList<>();
        for (ListSelectedRow masterId : this.getSelectedRows()) {
            masterIds.add(masterId.getPrimaryKeyValue().toString() + ",");
        }
        if (masterIds.size() == 0) {
            return;
        }
        //update by hst 2022/11/07 不允许重复通过
        DataSet data = QueryServiceHelper.queryDataSet(this.getClass().getName(),ScpRequestHelper.ENTITY_MAIN,"billno,yd_returnmethod",
                new QFilter[]{new QFilter("id",QFilter.in,masterIds)},null);
        while (data.hasNext()) {
            Row row = data.next();
            if (StringUtils.isNotBlank(row.getString("yd_returnmethod"))) {
                this.getView().showTipNotification("单据:" + row.getString("billno") + "已确认,无需再次确认！");
                return;
            }
        }
        FormShowParameter formShowParameter = new FormShowParameter();
        formShowParameter.setCaption("退货确认框");
        formShowParameter.setFormId("yd_scp_request_pop");
        StyleCss css = new StyleCss();
        css.setWidth("600");
        css.setHeight("250");
        formShowParameter.getOpenStyle().setInlineStyleCss(css);
        formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
        formShowParameter.setCustomParam("pkIds",masterIds.toString());
        formShowParameter.setCloseCallBack(new CloseCallBack(this,"yd_scp_request_pop"));
        this.getView().showForm(formShowParameter);
    }

    /**
     * 获取退货选项弹窗中用户输入的信息
     * @author: hst
     * @createDate: 2022/09/14
     * @param closedCallBackEvent
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        Map<String,Object> data = (Map<String,Object>) closedCallBackEvent.getReturnData();
        if (data != null) {
            List<Object> masterIds = new ArrayList<>();
            for (ListSelectedRow masterId : this.getSelectedRows()) {
                masterIds.add(masterId.getPrimaryKeyValue());
            }
            if (masterIds.size() > 0) {
                new ScpRequestHelper().assignmentField(masterIds.toArray(new Object[masterIds.size()]), data);
                this.getView().invokeOperation("confirm");
            }
        }
    }
}

