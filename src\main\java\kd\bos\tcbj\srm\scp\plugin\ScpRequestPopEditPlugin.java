package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.cache.CacheFactory;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.form.control.AttachmentPanel;
import kd.bos.form.control.Button;
import kd.bos.form.control.Control;
import kd.bos.servicehelper.AttachmentServiceHelper;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpRequestPopEditPlugin
 * @className ScpRequestListPlugin
 * @author: hst
 * @createDate: 2022/09/14
 * @description: 是否提货选项弹窗插件
 * @version: v1.0
 */
public class ScpRequestPopEditPlugin extends AbstractBillPlugIn {

    public final static String FIELD_METHOD = "yd_returnmethod";
    public final static String FIELD_PICKTIME = "yd_pickuptime";
    public final static String FIELD_RECIPIENT = "yd_recipient";
    public final static String FIELD_CONTANT = "yd_contact";
    public final static String FIELD_ADDRESS = "yd_address";
    public final static String FIELD_LOGISTICES = "yd_logistics";
    public final static String FIELD_ATTACHMENT = "yd_attachment";

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Button button = this.getControl("btnok");
        button.addClickListener(this);

    }

    @Override
    public void click(EventObject e) {
        super.click(e);
        Control control = (Control) e.getSource();
        if ("btnok".equalsIgnoreCase(control.getKey())) {
            boolean check = this.checkData();
            if (check) {
                Map<String,Object> data = this.encapsulateData();
                this.getView().returnDataToParent(data);
                this.getView().close();
            }
        }
    }

    /**
     * 数据校验
     * @author: hst
     * @createDate: 2022/09/14
     */
    public boolean checkData () {
        String type = this.getModel().getValue(FIELD_METHOD).toString();
        switch (type) {
            case "1" : {
                if (this.getModel().getValue(FIELD_PICKTIME) == null) {
                    this.getView().showTipNotification("请填写预计取货时间！");
                    return false;
                }
                return true;
            }
            case "2" : {
                List<String> fields = new ArrayList<>();
                if (this.getModel().getValue(FIELD_RECIPIENT) == "") {
                    fields.add("邮寄收件人");
                }
                if (this.getModel().getValue(FIELD_CONTANT) == "") {
                    fields.add("联系方式");
                }
                if (this.getModel().getValue(FIELD_ADDRESS) == "") {
                    fields.add("地址");
                }
//                if (this.getModel().getValue(FIELD_LOGISTICES) == "") {
//                    fields.add("偏好物流商");
//                }
                if (fields.size() > 0) {
                    StringBuffer tip = new StringBuffer();
                    tip.append("请填写");
                    for (String field : fields) {
                        tip.append(field + "、");
                    }
                    this.getView().showTipNotification(tip.deleteCharAt(tip.length() - 1).toString() + "!");
                    return false;
                }
                return true;
            }
            case "3" : {
                //获取附件字段数据模型
                AttachmentPanel att = this.getView().getControl(FIELD_ATTACHMENT);
                List<Map<String,Object>> attachmentData = att.getAttachmentData();
//                if (attachmentData.size() ==0) {
//                    this.getView().showTipNotification("请上传附件！");
//                    return false;
//                }
                return true;
            }
            default: {
                this.getView().showTipNotification("系统异常，请联系管理员！");
                return false;
            }
        }
    }

    /**
     * 封装回传数据
     * @author: hst
     * @createDate: 2022/09/14
     */
    public Map<String,Object> encapsulateData () {
        Map<String,Object> map = new HashMap<>();
        String type = this.getModel().getValue(FIELD_METHOD).toString();
        map.put(FIELD_METHOD,type);
        switch (type) {
            case "1" : {
                map.put(FIELD_PICKTIME,this.getModel().getValue(FIELD_PICKTIME));
                map.put(FIELD_LOGISTICES,"");
                map.put(FIELD_RECIPIENT,"");
                map.put(FIELD_CONTANT,"");
                map.put(FIELD_ADDRESS,"");
                map.put(FIELD_ATTACHMENT,null);
                break;
            }
            case "2" : {
                map.put(FIELD_PICKTIME,null);
                map.put(FIELD_LOGISTICES,this.getModel().getValue(FIELD_LOGISTICES));
                map.put(FIELD_RECIPIENT,this.getModel().getValue(FIELD_RECIPIENT));
                map.put(FIELD_CONTANT,this.getModel().getValue(FIELD_CONTANT));
                map.put(FIELD_ADDRESS,this.getModel().getValue(FIELD_ADDRESS));
                map.put(FIELD_ATTACHMENT,null);
                break;
            }
            case "3" : {
                AttachmentPanel att = this.getView().getControl(FIELD_ATTACHMENT);
                List<Map<String,Object>> attachmentData = att.getAttachmentData();
                map.put(FIELD_ATTACHMENT,attachmentData);
                break;
            }
        }
        return map;
    }

    /**
     * 根据附件字段数据构造附件面板数据
     * @author: hst
     * @createDate: 2022/09/14
     * @param: sourceAttachCol
     * @return
     */
    public List<Map<String,Object>> buildAttachmentDataFromEdit(DynamicObjectCollection sourceAttachCol){
        List<Map<String,Object>> attachDataList= new ArrayList<>();
        sourceAttachCol.forEach(attach -> {
            DynamicObject attachObj = attach.getDynamicObject("fbasedataid");
            Map<String,Object>attachMap =new HashMap<>();
            attachMap.put("description",attachObj.getString("description"));
            attachMap.put("type",attachObj.getString("type"));
            //获取附件inputstream上传到缓存服务
            InputStream inputStream= FileServiceFactory.getAttachmentFileService().getInputStream(attachObj.getString("url"));
            String saveUrl= CacheFactory.getCommonCacheFactory().getTempFileCache().saveAsFullUrl(attachObj.getString("name"),
                    new BufferedInputStream(inputStream),2*3600);
            attachMap.put("url", saveUrl);
            attachMap.put("uid", UUID.randomUUID().toString());
            attachMap.put("name",attachObj.getString("name"));
            attachMap.put("size",attachObj.get("size"));
            attachMap.put("lastModified",new Date().getTime());
            attachMap.put("uploadTime",new Date().getTime());
            attachMap.put("status","success");
            attachDataList.add(attachMap);
        });
        return attachDataList;
    }

}
