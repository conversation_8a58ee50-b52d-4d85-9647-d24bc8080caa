package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.control.Button;
import kd.bos.form.control.Control;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.plugin.AbstractFormPlugin;

import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpSalOutStockEditPlugin
 * @className ScpSalOutExcelPlugin
 * @author: hst
 * @createDate: 2023/03/07
 * @description: 销售发货分录EXCEL文件上传
 * @version: v1.0
 */
public class ScpSalOutExcelPlugin extends AbstractFormPlugin {

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Button button = this.getControl("btnok");
        button.addClickListener(this);

    }

    @Override
    public void click(EventObject e) {
        super.click(e);
        Control control = (Control) e.getSource();
        if ("btnok".equalsIgnoreCase(control.getKey())) {
            DynamicObjectCollection files = this.getModel().getDataEntity().getDynamicObjectCollection("yd_excel");
            List<String> urls = new ArrayList<>();
            files.stream().forEach(file -> {
                urls.add(Objects.nonNull(file.getDynamicObject("fbasedataid")) ? file.getDynamicObject("fbasedataid").getString("url") : "");
            });
            this.getView().returnDataToParent(urls);
            this.getView().close();
        }
    }
}
