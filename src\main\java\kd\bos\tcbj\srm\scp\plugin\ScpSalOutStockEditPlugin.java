package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.cache.CacheFactory;
import kd.bos.cache.TempFileCache;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.RefObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.FlexEntityType;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.events.AfterDeleteRowEventArgs;
import kd.bos.entity.datamodel.events.ChangeData;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.exception.KDBizException;
import kd.bos.form.*;
import kd.bos.form.control.Control;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.control.Toolbar;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.form.operate.FormOperate;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.im.util.UIUtils;
import kd.bos.tcbj.srm.scp.constants.ScpAddAttachmentConstants;
import kd.bos.tcbj.srm.scp.constants.ScpSalOutStockConstants;
import kd.bos.tcbj.srm.scp.helper.ScpSalOutStockHelper;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;
import kd.bos.tcbj.srm.utils.ExportEntryUtils;
import kd.scm.common.util.CommonUtil;
import kd.scm.common.util.MaterialUtil;
import kd.scm.common.util.SupplierChangeUtil;
import kd.scm.common.util.caldynamic.CalDynamicFactory;
import kd.scm.common.util.caldynamic.ICalDynamic;
import kd.scm.scp.common.helper.ShelfLifeDateUtil;
import kd.scm.scp.common.util.ScpBillUtil;
import kd.scm.scp.formplugin.ScpCoreBillEditPlugin;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpSalOutStockEditPlugin
 * @className ScpSalOutStockEditPlugin
 * @author: hst
 * @createDate: 2022/08/30
 * @description: 销售发货单据插件
 * @version: v1.0
 */
public class ScpSalOutStockEditPlugin extends ScpCoreBillEditPlugin {

    /**按钮_送货预约二维码*/
    private final static String BTN_QRCODE = "yd_qrcode";
    /**按钮_确认发货*/
    private final static String BTN_DELIVERY = "bar_delivery";
    /**按钮_补录附件*/
    private final static String BTN_ADDATTACHMENT = "yd_addattachment";
    /**按钮_打印发货单号*/
    private final static String BTN_PRINTBILLCODE = "yd_printbillcode";
    /**KEY_确认提示框*/
    private final static String KEY_CONFIRM = "key_confirmbill";
    private final static String OPPARAM_AFTERCONFIRM = "afterconfirm";
    /**导出分录**/
    private final static String BTN_EXPROTENTRY = "yd_exportentry";
    /**导入分录**/
    private final static String BTN_IMPORTENTRY = "yd_importentry";
    /**销售分录标识**/
    private final static String entryKey = "materialentry";
    /**标品工厂类**/
    private final static ICalDynamic iCalDynamic = (new CalDynamicFactory()).createCal();
    /**批量销售附件上传**/
    private final static String BTN_UPLOAD = "yd_upload";
    /**填充生产厂家**/
    private final static String BTN_FILL = "yd_fill";


    @Override
    public void afterBindData(EventObject e) {
    	super.afterBindData(e);

    	// 设置供应方字段必填
        UIUtils.setRequired(this.getView(), new String[] {"yd_coa"});
        
        this.resetButtonStatus();
		this.initQtyMap();
		this.initBaseQty();
		this.getModel().setDataChanged(false);
    }

    /**
     * 监听工具栏点击按钮
     * @author: hst
     * @createDate: 2022/08/30
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case BTN_QRCODE : {
                deliveryReservationQRCode();
                break;
            }
            case BTN_DELIVERY : {
                // update by hst 2022/11/22 送货司机填写页面修改未预约送货台账
//                String id = this.getModel().getValue("id").toString();
//                String url = System.getProperty("domain.contextUrl") + "/mobile.html?userId=Guest&form=scp_saloutstock_mob&pkId=" + id;
//                DynamicObject bill = this.getModel().getDataEntity();
//                bill.set("yd_url",url);
//                SaveServiceHelper.save(new DynamicObject[]{bill});
                break;
            }
            case BTN_ADDATTACHMENT : {
                this.deliveryAddAttachment();
                break;
            }
            case BTN_PRINTBILLCODE: {
                this.printBillCode(evt);
                break;
            }
            case BTN_EXPROTENTRY: {
                this.exprotEntryData();
                break;
            }
            case BTN_IMPORTENTRY: {
                this.OpenFileUploadPage("yd_scp_salout_excel","uploadExcel");
                break;
            }
            case BTN_UPLOAD: {
                this.OpenBatchFileUploadPage();
                break;
            }
            case BTN_FILL : {
                // 根据供应商信息填充生产厂家
                this.fillProductBySupplier();
            }
        }
    }

    /**
     * 确认发货前校验生产商+物料编码+厂家批号 是否在异常批次库中
     * @param evt
     */
    @Override
    public void beforeItemClick(BeforeItemClickEvent evt) {
        String key = evt.getItemKey();
        switch (key) {
            case BTN_DELIVERY : {
                Map<String,Object> result = new ScpSalOutStockHelper().checkInErrerLot(this.getModel().getDataEntity(true));
                if (!(boolean) result.get("isSuccess")) {
                    this.getModel().getDataEntity().set("yd_iserrlot",true);
                    this.getModel().setValue("yd_iserrlot", true);
//                    this.getView().updateView();
                    this.getView().showErrorNotification(result.get("message").toString());
//                    throw new KDBizException(result.get("message").toString());
                    evt.setCancel(true);
                } else {
                	this.getModel().setValue("yd_iserrlot", false);
                }

                // 直接点击发货会导致单据编码为空，提示先点击保存按钮,yzl,20230301
                String billno = this.getModel().getDataEntity(true).getString("billno");
				if (billno.trim().isEmpty()) {
					this.getView().showErrorNotification("请先点击保存，再点击确认发货，谢谢！");
					evt.setCancel(true);
				}
                break;
            }
        }
    }

    /**
     * 二维码弹窗
     * @author: hst
     * @createDate: 2022/08/30
     */
    public void deliveryReservationQRCode () {
        // update by hst 2022/11/22 生成预约送货台账时会保存访问url，直接获取
//        String pkId = this.getModel().getValue("id").toString();
        String url = this.getModel().getDataEntity().getString(ScpSalOutStockConstants.FILED_URL);
        FormShowParameter formShowParameter = new FormShowParameter();
        formShowParameter.setCaption("送货预约二维码");
        formShowParameter.setFormId("yd_scp_qrcode");
        StyleCss css = new StyleCss();
        css.setWidth("450");
        css.setHeight("350");
        formShowParameter.getOpenStyle().setInlineStyleCss(css);
        formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
        formShowParameter.setCustomParam("url",url);
        this.getView().showForm(formShowParameter);
    }

    /**
     * 补录附件弹窗
     * @author: hst
     * @createDate: 2022/09/23
     */
    public void deliveryAddAttachment () {
        // update by hst 2024/03/25 调整为通过配置可补录附件的状态
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name",
                new QFilter[]{new QFilter("number", QFilter.equals, "SALOUT_SUPPLE_STATUS")});
        if (Objects.nonNull(param)) {
            String value = param.getString("name");
            String[] status = value.contains(",") ? value.split(",") : value.split("，");

            if (Objects.isNull(status) || status.length == 0) {
                this.getView().showTipNotification("当前未设置允许附件补录的状态，请联系管理员！");
                return;
            }

            boolean check = new ScpSalOutStockHelper().checkBillStatus(this.getModel().getDataEntity(),
                    ScpSalOutStockConstants.FIELD_LOGSTATUS, status);
            if (!check) {
                this.getView().showTipNotification("当前状态不允许进行补录附件！");
                return;
            }
            FormShowParameter formShowParameter = new FormShowParameter();
            formShowParameter.setCaption("补录附件");
            formShowParameter.setFormId(ScpAddAttachmentConstants.ENTITY_MAIN);
            StyleCss css = new StyleCss();
            css.setWidth("1100");
            css.setHeight("500");
            formShowParameter.getOpenStyle().setInlineStyleCss(css);
            formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
            formShowParameter.setCustomParam("pkId", this.getModel().getValue("id"));
            formShowParameter.setCloseCallBack(new CloseCallBack(this, ScpAddAttachmentConstants.ENTITY_MAIN));
            this.getView().showForm(formShowParameter);
        }
    }

    /**
     * 打印发货单号
     * @param
     * @return
     * <AUTHOR>
     * @date 2022-10-21
     */
    private void printBillCode(ItemClickEvent evt) {
        FormShowParameter formShowParameter = new FormShowParameter();
        formShowParameter.setCaption("发货单号二维码");
        formShowParameter.setFormId("yd_qrcode");
        formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
        formShowParameter.setCustomParam("content", this.getModel().getValue("billno"));
        this.getView().showForm(formShowParameter);
    }

    /**
     * 子页面关闭后触发
     * 回写附件信息
     * @author: hst
     * @createDate: 2022/09/23
     * @param closedCallBackEvent
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        try {
            // update by hst 2023/06/02
            switch (closedCallBackEvent.getActionId()) {
                case BTN_UPLOAD : {
                    // 销售附件批量上传
                    this.batchFileUpload(this.getModel().getDataEntity(true),
                            closedCallBackEvent.getReturnData(),this.getModel());
                    this.getView().updateView(ScpSalOutStockConstants.ENTITY_MATERIAL);
                    break;
                }
                case ScpAddAttachmentConstants.ENTITY_MAIN : {
                    // 附件补录
                    if (Objects.nonNull(closedCallBackEvent.getReturnData())) {
                        new ScpSalOutStockHelper().writeBackAttachment(Long.valueOf(this.getModel().getValue("id").toString()),
                                ((DynamicObjectCollection) closedCallBackEvent.getReturnData()));
                        this.getView().showSuccessNotification("保存成功");
                        this.getView().invokeOperation("refresh");
                    }
                    break;
                }
                case "uploadExcel" : {
                    // 分录导入
                    List<String> data = (List<String>) closedCallBackEvent.getReturnData();
                    this.writeBackImportData(data);
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.getView().showErrorNotification("系统异常，请联系管理员！");
        }
    }

    /**
     * 填写生产日期和到期日时不满足时提示,yzl
     */
    @Override
    public void propertyChanged(PropertyChangedArgs e) {
    	IDataEntityProperty property = e.getProperty();
		ChangeData[] arg2 = e.getChangeSet();
		int arg3 = arg2.length;

		for (int arg4 = 0; arg4 < arg3; ++arg4) {
			ChangeData changeData = arg2[arg4];
			IDataModel dataModel = this.getModel();
			int rowIndex = changeData.getRowIndex();
			int currentRowIndex = dataModel.getEntryCurrentRowIndex("materialentry");
			if (rowIndex >= 0 && currentRowIndex != rowIndex) {
				EntryGrid oldValue = (EntryGrid) this.getControl("materialentry");
				oldValue.selectRows(rowIndex, true);
			}

			Object arg12 = changeData.getOldValue();
			Object newValue = changeData.getNewValue();
			if ("qty".equals(property.getName())) {
				if (compareQtyAndSumQty(this.getModel().getDataEntity(true), rowIndex)) {
					this.getView().getPageCache().put("rowIndex", String.valueOf(rowIndex));
//					this.getView().showConfirm(
//							ResManager.loadKDString("累计发货数量将大于源订单数量，请确认是否超额！", "ScpSalOutStockEditPlugin_7",
//									"scm-scp-formplugin", new Object[0]),
//							MessageBoxOptions.OKCancel, new ConfirmCallBackListener("checkqty_action"));
//					this.getView().showErrorNotification("累计发货数量大于原订单数据上限数量，请修正发货数量！");
//					this.getView().showConfirm("累计发货数量大于原订单数据上限数量（只能超收10%），如继续操作发货则会通知相关采购员审批，请确认是否继续操作？", MessageBoxOptions.YesNo, new ConfirmCallBackListener("checkqty_action"));
//					this.getModel().setValue("qty", (Object) arg12, rowIndex);
					// 因为不会自动替换回去，所以有超发的都设置为true
//					this.getModel().setValue("yd_confirmrateup", true, rowIndex);
				} else {
					// 没有超发则标记为不超发
					this.getModel().setValue("yd_confirmrateup", false, rowIndex);
				}

				ScpBillUtil.updateEntryBasicQtyByLineIndex(this.getModel().getEntryRowEntity("materialentry", rowIndex));
			} else {
				String msg;
				if ("proddate".equals(property.getName())) {
					msg = ShelfLifeDateUtil.changeProduceDate(
							this.getModel().getEntryRowEntity("materialentry", rowIndex), (Date) arg12, (Date) newValue,
							this.getModel().getDataEntity().getDynamicObject("org"), rowIndex);
					if (StringUtils.isNotEmpty(msg)) {
						this.getView().showErrorNotification(msg);
						this.getModel().setValue("proddate", (Object) null, rowIndex);
					}
				} else if ("duedate".equals(property.getName())) {
                    // update by hst 2023/05/22 只需要监听物料编码非D和E开头的物料
                    DynamicObject material = (DynamicObject) this.getModel().getValue("material");
                    if (!this.checkIsPackagingMaterial(material)) {
                        msg = ShelfLifeDateUtil.changeExpiryDate(
                                this.getModel().getEntryRowEntity("materialentry", rowIndex), (Date) arg12, (Date) newValue,
                                this.getModel().getDataEntity().getDynamicObject("org"), rowIndex);
                        if (StringUtils.isNotEmpty(msg)) {
                            this.getView().showErrorNotification(msg);
                            this.getModel().setValue("duedate", (Object) null, rowIndex);
                        }
                    }
				}
			}
		}

    	String key = e.getProperty().getName();
    	if ("proddate".equalsIgnoreCase(key) || "duedate".equalsIgnoreCase(key)) {
            int currentRowIndex = this.getModel().getEntryCurrentRowIndex("materialentry");
            // update by hst 2023/05/22 只需要监听物料编码非D和E开头的物料
            DynamicObject material = (DynamicObject) this.getModel().getValue("material");
            if (!this.checkIsPackagingMaterial(material)) {
                Date prodDate = (Date) this.getModel().getValue("proddate", currentRowIndex);
                Date dueDate = (Date) this.getModel().getValue("duedate", currentRowIndex);
                if (prodDate != null && dueDate != null) {
                    long diffDay = DateTimeUtils.diffDay(prodDate, dueDate); // 总有效日期天数
                    // 计算出生产日期到今天数
                    long validDay = DateTimeUtils.diffDay(prodDate, new Date()); // 时效数
                    if (diffDay <= validDay * 2) {
                        // 不允许发货
                        this.getView().showErrorNotification("生产时效天数（生产日期至今天）已过保质期天数（生产日期至到期日）一半，若继续确认发货则系统会自动推送给QA人员进行审批，审批通过后系统会自动完成确认发货！");
                    }
                }
            }
        }

        // update by hst 2023/05/10 分录切换字段时第一次点击会失去焦点，重写父类方法
    	this.scpCoreBillPropertyChanged(e);
//        super.propertyChanged(e);
    }
    
    /**
     * 比较剩余发货数量与当前发货数量，判断是否超发,yzl,20230420
     * @param dynamicObject
     * @param indexRow
     * @return
     */
    public static boolean compareQtyAndSumQty(DynamicObject dynamicObject, int indexRow) {
		DynamicObjectCollection materialDynamicCollection = dynamicObject.getDynamicObjectCollection("materialentry");
		if (materialDynamicCollection == null) {
			return false;
		} else {
			DynamicObject entryDynamicObject = (DynamicObject) materialDynamicCollection.get(indexRow);
			if (entryDynamicObject == null) {
				return false;
			} else {
				BigDecimal newQty = CommonUtil.getBigDecimalPro(Long.valueOf(entryDynamicObject.getLong("qty")));
				DynamicObject scpOrderObject = getScpOrderObject(entryDynamicObject.getString("pobillno"),
						Long.valueOf(entryDynamicObject.getLong("srcentryid")));
				BigDecimal sumOutStockQty = BigDecimal.ZERO;
				if (scpOrderObject != null) {
					// 如果超收比例为0则不用控制
					if (scpOrderObject.getBigDecimal("saloutrateup").compareTo(BigDecimal.ZERO) == 0) {
						return false;
					}

					if (scpOrderObject.getBigDecimal("qty") != null && scpOrderObject.getBigDecimal("saloutrateup") != null ) {
						// 数量乘以1+比例得到发货上限数量
						BigDecimal rate = scpOrderObject.getBigDecimal("saloutrateup").divide(new BigDecimal(100)).setScale(6);
						BigDecimal saloutqtyup = scpOrderObject.getBigDecimal("qty").multiply(BigDecimal.ONE.add(rate));
//						BigDecimal saloutqtyup = scpOrderObject.getBigDecimal("qty");
						// 如果发货数量大于入库数量，则用入库数量进行扣减后校验，要再加上退货数量,20230807,yzl
						if (scpOrderObject.getBigDecimal("sumoutstockqty").compareTo(scpOrderObject.getBigDecimal("suminstockqty")) > 0) {
							sumOutStockQty = saloutqtyup.subtract(scpOrderObject.getBigDecimal("sumoutstockqty")).add(scpOrderObject.getBigDecimal("sumrecretqty"));
						} else {
							sumOutStockQty = saloutqtyup.subtract(scpOrderObject.getBigDecimal("suminstockqty")).add(scpOrderObject.getBigDecimal("sumrecretqty"));
						}
					}
				}

				return newQty.compareTo(sumOutStockQty) > 0;
			}
		}
	}
    
    private static DynamicObject getScpOrderObject(String billNo, Long materialId) {
		QFilter filter = new QFilter("billno", "=", billNo);
		filter.and("materialentry.id", "=", materialId);
		String selectFields = "materialentry.sumoutstockqty as sumOutStockQty,materialentry.qty as qty,materialentry.iscontrolqty as iscontrolqty,materialentry.saloutrateup as saloutrateup,materialentry.suminstockqty as suminstockqty,materialentry.sumrecretqty as sumrecretqty";
		DynamicObject dynamicObject = QueryServiceHelper.queryOne("scp_order", selectFields, new QFilter[]{filter});
		return dynamicObject;
	}

    /**
     * 确认发货前校验是否有COA文件，没有时是否要继续提交
     */
    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs evt) {
    	super.beforeDoOperation(evt);

    	FormOperate operate = (FormOperate) evt.getSource();
    	String operateKey = ((AbstractOperate) evt.getSource()).getOperateKey();
    	RefObject<String> afterConfirm = new RefObject<>();
    	if ("submit".equalsIgnoreCase(operateKey) && !operate.getOption().tryGetVariableValue(OPPARAM_AFTERCONFIRM, afterConfirm)) {
    		int rowCount = this.getModel().getEntryRowCount("materialentry");
    		Boolean hasNullFiles = false;
    		for (int i=0;i<rowCount;i++) {
                DynamicObject material = (DynamicObject) this.getModel().getValue("material",i);
                // update by hst 2023/05/22 非包材物料校验COA
                if (!this.checkIsPackagingMaterial(material)) {
                    DynamicObjectCollection files = (DynamicObjectCollection) this.getModel().getValue("yd_coa", i);
                    if (files == null || files.size() == 0) {
                        hasNullFiles = true;
                        break;
                    }
                }
    		}

    		if (hasNullFiles) {
    			ConfirmCallBackListener confirmCallBacks = new ConfirmCallBackListener(KEY_CONFIRM, this);
    			String confirmTip = "当前销售发货单存在分录行未上传COA文件，继续发货的话系统会发送消息给QA人员，待QA人员审批后系统才能执行发货，请确认是否继续?";
				this.getView().showConfirm(confirmTip, MessageBoxOptions.YesNo, ConfirmTypes.Default, confirmCallBacks);
				// 在没有确认之前，先取消本次操作
				evt.setCancel(true);
    		}

    		// 直接点击发货会导致单据编码为空，提示先点击保存按钮,yzl,20230301
            String billno = this.getModel().getDataEntity(true).getString("billno");
			if (billno.trim().isEmpty()) {
				this.getView().showErrorNotification("请先点击保存，再点击确认发货，谢谢！");
				evt.setCancel(true);
			}

    	} else if ("printpreview".equalsIgnoreCase(operateKey)) {
            // update by hst 2024/08/22 校验打印状态
            this.checkPrintStatus(evt);
        }
    	operate.getOption().setVariableValue(OPPARAM_AFTERCONFIRM, "true");
    }

    /**
     * 确认操作
     */
    @Override
    public void confirmCallBack(MessageBoxClosedEvent messageBoxClosedEvent) {
    	super.confirmCallBack(messageBoxClosedEvent);

		if (StringUtils.equals(KEY_CONFIRM, messageBoxClosedEvent.getCallBackId())) {
			if (messageBoxClosedEvent.getResult() == MessageBoxResult.Yes) {
				// 构建操作自定义参数，标志为确认后再次执行操作，避免重复显示交互提示
				OperateOption operateOption = OperateOption.create();
				operateOption.setVariableValue(OPPARAM_AFTERCONFIRM, "true");

//				this.getModel().getDataEntity().set("yd_inflow", true);
				this.getModel().setValue("yd_inflow", true);

                // 执行确认操作，并传入自定义操作参数
                this.getView().invokeOperation("submit", operateOption);
            }
        } else if ("confirm_exprot".equals(messageBoxClosedEvent.getCallBackId()) && MessageBoxResult.Yes.equals(messageBoxClosedEvent.getResult())) {
            String entryKey = "materialentry";
            String settingKey = this.getView().getFormShowParameter().getSettingKey();
            final IFormView view = this.getView();
            List<String> urls = ExportEntryUtils.startingExport(entryKey,settingKey,view);
            String path = RequestContext.get().getClientFullContextPath();
            for (String url : urls) {
                this.getView().download(path + "attachment/download.do?path=" + url);
            }
        } else if ("checkqty_action".equals(messageBoxClosedEvent.getCallBackId())) {
        	// 取消确认则重置原数量
        	if (messageBoxClosedEvent.getResult().equals(MessageBoxResult.Cancel)) {
        		String index = this.getView().getPageCache().get("rowIndex");
    			int rowIndex1;
    			if (null == index) {
    				rowIndex1 = this.getModel().getEntryCurrentRowIndex("materialentry");
    			} else {
    				rowIndex1 = Integer.parseInt(index.toString());
    			}

    			DynamicObject entryObj = this.getModel().getEntryRowEntity("materialentry", rowIndex1);
    			BigDecimal qty = this.getInitQty(Long.valueOf(entryObj.getLong("id")));
    			ScpBillUtil.resetQty(this.getModel().getDataEntity(true), qty, rowIndex1);
    			this.getModel().setValue("yd_confirmrateup", false, rowIndex1);
        	} else if (MessageBoxResult.Yes.equals(messageBoxClosedEvent.getResult())) {
        		// 确认超收则打上标记
//        		this.getModel().setValue("yd_inflow", true);
        		String index = this.getPageCache().get("rowIndex");
    			int rowIndex1;
    			if (null == index) {
    				rowIndex1 = this.getModel().getEntryCurrentRowIndex("materialentry");
    			} else {
    				rowIndex1 = Integer.parseInt(index.toString());
    			}
    			this.getModel().setValue("yd_confirmrateup", true, rowIndex1);
        	}
        	this.getView().updateView("materialentry");
        	
        }
    }

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Toolbar toolbar = this.getView().getControl("advcontoolbarap");
        toolbar.addItemClickListener(this);
        this.addItemClickListeners("yd_upload");
        /* F7 弹窗监听 */
        this.addBeforeF7Listener();
    }

    /**
     * 导出分录信息
     * @author: hst
     * @createDate: 2023/03/03
     */
    public void exprotEntryData() {
        IClientViewProxy proxy = (IClientViewProxy)this.getView().getService(IClientViewProxy.class);
        Map<String, Object> map = new HashMap();
        map.put("types", new String[]{"cw", "cv", "cs"});
        proxy.invokeControlMethod("materialentry", "getGridInfo", new Object[]{map});
        ConfirmCallBackListener confirmCallBacks = new ConfirmCallBackListener("confirm_exprot", this);
        int rowCount = this.getView().getModel().getEntryRowCount("materialentry");
        String msg = MessageFormat.format(ResManager.loadKDString("本次将引出{0}行数据，点击“确定”开始执行。", "ExportEntry_0", "bos-form-business", new Object[0]), rowCount);
        this.getView().showConfirm(msg, MessageBoxOptions.OKCancel, confirmCallBacks);
    }

    /**
     * 打开附件上传页面
     * @author: hst
     * @createDate: 2023/03/03
     * @param   pageId 页面标识
     *          callBackName 回传标识
     */
    public void OpenFileUploadPage (String pageId, String callBackName) {
        FormShowParameter formShowParameter = new FormShowParameter();
        formShowParameter.setCaption("附件上传");
        formShowParameter.setFormId(pageId);
        StyleCss css = new StyleCss();
        css.setWidth("1100");
        css.setHeight("500");
        formShowParameter.getOpenStyle().setInlineStyleCss(css);
        formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
        formShowParameter.setCloseCallBack(new CloseCallBack(this, callBackName));
        this.getView().showForm(formShowParameter);
    }

    /**
     * 导入分录数据回写
     * @author: hst
     * @createDate: 2023/03/07
     * @param urls
     */
    public void writeBackImportData(List<String> urls) {
        if (Objects.isNull(urls)) {
            return;
        }
        TempFileCache cache = CacheFactory.getCommonCacheFactory().getTempFileCache();
        for (String url : urls) {
            InputStream in = cache.getInputStream(url);
            try {
                XSSFWorkbook excel = new XSSFWorkbook(in);
                Sheet sheet = excel.getSheetAt(0);
                // 获取第二行中的字段名
                Row files = sheet.getRow(1);
                List<String> fileNames = new ArrayList<>();
                for (int i = 0; i < files.getLastCellNum(); i++) {
                    Cell cell = files.getCell(i);
                    fileNames.add(cell.getStringCellValue());
                }
                // 从第三行开始存数据
                Map<String,Object> datas = new HashMap<>();
                for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                    Row data = sheet.getRow(i);
                    // 键 （序号+物料编码+订单号）
                    // update by hst 2023/11/29 序号处理
                    Cell matNum = data.getCell(fileNames.indexOf("material"));
                    Cell pobillno = data.getCell(fileNames.indexOf("pobillno"));
                    Cell seq = data.getCell(fileNames.indexOf("seq"));
                    if (Objects.nonNull(matNum) & Objects.nonNull(pobillno) && Objects.nonNull(seq)) {
                        datas.put(seq.getStringCellValue() + "-" + matNum.getStringCellValue() + "-" + pobillno.getStringCellValue(), data);
                    }
                }
                // 为分录赋值
                DynamicObjectCollection entries = this.getModel().getEntryEntity("materialentry");
                List<String> entryFileds = DynamicObjectUtil.getAllField(entries.getDynamicObjectType());
                int index = 0;
                for (DynamicObject entry : entries) {
                    DynamicObject material = entry.getDynamicObject("material");
                    String billno = entry.getString("pobillno");
                    String seq = entry.getString("seq");
                    if (StringUtils.isNotBlank(billno) && Objects.nonNull(material)) {
                        String materialNum = material.getString("number");
                        // update by hst 2023/11/29 序号处理
                        Row row = datas.containsKey(seq + "-" + materialNum + "-" + billno)
                                ? (Row) datas.get(seq + "-" + materialNum + "-" + billno) : null;
                        if (Objects.nonNull(row)) {
                            for (String filed : fileNames) {
                                if (!"material".equals(filed) && !"pobillno".equals(filed)
                                        && entryFileds.contains(filed)) {
                                    Cell cell = row.getCell(fileNames.indexOf(filed));
                                    if (Objects.nonNull(cell)) {
                                        CellType cellType = cell.getCellType();
                                        Object value = null;
                                        if (cellType.equals(CellType.STRING)) {
                                            value = cell.getStringCellValue();
                                        } else if (cellType.equals(CellType.NUMERIC)) {
                                            if (DateUtil.isCellDateFormatted(cell)) {
                                                value = cell.getDateCellValue();
                                            } else {
                                                double dValue = cell.getNumericCellValue();
                                                DecimalFormat df = new DecimalFormat("0");
                                                value = df.format(dValue);
                                            }
                                        } else if (cellType.equals(CellType.BOOLEAN)) {
                                            value = cell.getBooleanCellValue();
                                        }
                                        if ("unit".equals(filed)) {
                                            // 计量单位基础资料通过名称获取
                                            QFilter qFilter = new QFilter("name",QFilter.equals,value);
                                            DynamicObject[] units = BusinessDataServiceHelper.load("bd_measureunits","id",
                                                    new QFilter[]{qFilter});
                                            if (units.length > 0) {
                                                this.getModel().setValue(filed, units[0], index);
                                            }
                                        } else {
                                            this.getModel().setValue(filed, value, index);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    index++;
                }
                this.getView().updateView("materialentry");
            } catch (Exception e) {
                e.printStackTrace();
                throw new KDBizException("系统异常，请联系管理员！");
            }
        }
    }
    
    @Override
    public void afterCreateNewData(EventObject e) {
    	super.afterCreateNewData(e);
    	
    	this.setCurrentContacter();
    }
    
    /**
	 * 重写标准方法,yzl，20230417
	 */
	private void setCurrentContacter() {
		QFilter qFilter = (new QFilter("user", "=", Long.valueOf(UserServiceHelper.getCurrentUserId())))
				.and(new QFilter("status", "=", "C"));
		DynamicObject dynamicObject = QueryServiceHelper.queryOne("scp_bizperson", "masterid,name,number", new QFilter[] { qFilter });
		if (dynamicObject != null) {
			IDataModel model = this.getModel();
			model.setValue("contacter", dynamicObject.getPkValue());
		}
	}
	
	/**
	 * 重写标准方法,yzl，20230417
	 */
	@Override
	public void afterDeleteRow(AfterDeleteRowEventArgs e) {
		super.afterDeleteRow(e);
		IDataModel model = this.getModel();
		DynamicObjectCollection entrys = model.getEntryEntity("materialentry");
		BigDecimal sumQty = BigDecimal.ZERO;
		BigDecimal qty = BigDecimal.ZERO;

		for (Iterator arg5 = entrys.iterator(); arg5.hasNext(); sumQty = sumQty.add(qty)) {
			DynamicObject entry = (DynamicObject) arg5.next();
			qty = entry.getBigDecimal("qty");
		}

		model.setValue("sumqty", sumQty);
	}
	
	/**
	 * 重写标准方法,yzl，20230417
	 */
	public void resetButtonStatus() {
		DynamicObject recruitObject = this.getModel().getDataEntity(true);
		DynamicObjectCollection Rowcoll = this.getModel().getDataEntity(true)
				.getDynamicObjectCollection("materialentry");
		boolean istrue = false;
		Iterator status = Rowcoll.iterator();

		while (status.hasNext()) {
			DynamicObject obj = (DynamicObject) status.next();
			BigDecimal receiptqty = obj.getBigDecimal("sumreceiptqty");
			BigDecimal instockqty = obj.getBigDecimal("suminstockqty");
			if (receiptqty.compareTo(BigDecimal.ZERO) > 0 || instockqty.compareTo(BigDecimal.ZERO) > 0) {
				istrue = true;
				break;
			}
		}

		if (istrue) {
			this.getView().setVisible(Boolean.TRUE, new String[]{"bar_delfix"});
		} else {
			this.getView().setVisible(Boolean.FALSE, new String[]{"bar_delfix"});
		}

		String status1 = recruitObject.getString("billstatus");
		if (status1 != null) {
			byte receiptqty1 = -1;
			switch (status1.hashCode()) {
				case 65 :
					if (status1.equals("A")) {
						receiptqty1 = 0;
					}
					break;
				case 66 :
					if (status1.equals("B")) {
						receiptqty1 = 1;
					}
					break;
				case 67 :
					if (status1.equals("C")) {
						receiptqty1 = 2;
					}
			}

			switch (receiptqty1) {
				case 0 :
				case 1 :
					this.getView().setVisible(Boolean.TRUE, new String[]{"bar_modify"});
					this.getView().setVisible(Boolean.TRUE, new String[]{"bar_loginput"});
					this.getView().setVisible(Boolean.TRUE, new String[]{"bar_delivery"});
					this.getView().setVisible(Boolean.FALSE, new String[]{"bar_undelivery"});
					break;
				case 2 :
					this.getView().setVisible(Boolean.FALSE, new String[]{"bar_modify"});
					this.getView().setVisible(Boolean.FALSE, new String[]{"bar_loginput"});
					this.getView().setVisible(Boolean.FALSE, new String[]{"bar_delivery"});
					this.getView().setVisible(Boolean.TRUE, new String[]{"bar_undelivery"});
			}

		}
	}
	
	/**
	 * 重写标准方法,yzl，20230417
	 */
	public void initQtyMap() {
		String pageId = this.getView().getPageId();
		DynamicObjectCollection cols = this.getModel().getEntryEntity("materialentry");

		for (int i = 0; i < cols.size(); ++i) {
			BigDecimal qty = ((DynamicObject) cols.get(i)).getBigDecimal("qty");
			Long id = Long.valueOf(((DynamicObject) cols.get(i)).getLong("id"));
			if (qty.compareTo(BigDecimal.ZERO) > 0) {
				System.out.println("初始化时存放的键值对pageId+id:"+pageId+id);
				this.getView().getPageCache().put(pageId + id, qty.toString());
			}
		}

	}
	
	/**
	 * 重写标准方法,yzl，20230417
	 */
	private void initBaseQty() {
		DynamicObjectCollection cols = this.getModel().getEntryEntity("materialentry");
		ScpBillUtil.updateEntryBasicQtyAll(cols);
		this.getView().updateView("materialentry");
	}
	
	/**
	 * 重写标准方法,yzl，20230417
	 */
	public BigDecimal getInitQty(Long id) {
		BigDecimal ret = null;
		String pageId = this.getView().getPageId();
		System.out.println("匹配值时的键值对："+pageId+id);
		String initQtyString = this.getView().getPageCache().get(pageId + id);
		if (!StringUtils.isEmpty(initQtyString)) {
			ret = new BigDecimal(initQtyString);
		}

		return ret;
	}

    /**
     * 重写标准方法的值变更逻辑，不去刷新整个分录，只刷新对应字段
     * @param e
     * @author: hst
     * @createDate: 2023/05/10
     */
    private void scpCoreBillPropertyChanged (PropertyChangedArgs e) {
        String propertyName = e.getProperty().getName();
        IDataModel model = this.getModel();
        String entityKey = this.getView().getEntityId();
        ChangeData[] changeDatas = e.getChangeSet();
        int length = changeDatas.length;

        for(int i = 0; i < length; ++i) {
            ChangeData changeData = changeDatas[i];
            int rowIndex = changeData.getRowIndex();
            int currentRowIndex = model.getEntryCurrentRowIndex(entryKey);
            if (rowIndex >= 0 && currentRowIndex != rowIndex) {
                EntryGrid grid = (EntryGrid)this.getControl(entryKey);
                grid.selectRows(rowIndex, true);
            }

            IDataEntityProperty property = e.getProperty();
            if (property.getParent() != null && property.getParent() instanceof FlexEntityType) {
                return;
            }

            Object modelValue = model.getValue(propertyName, rowIndex);
            if (entityKey.equals("scp_saloutstock") || entityKey.equals("scp_receive")) {
                iCalDynamic.proChanged(this.getModel().getDataEntity(true), entryKey, propertyName, rowIndex);
            }

            switch (propertyName) {
                case "supplier":
                    if (modelValue instanceof DynamicObject) {
                        SupplierChangeUtil.supplierChangeBill(model.getDataEntity(), (DynamicObject)modelValue);
                    }
                    break;
                case "material":
                    if (modelValue instanceof DynamicObject) {
                        MaterialUtil.materialChanged(model.getDataEntity(true), rowIndex);
                    }
            }
            if (rowIndex >= 0) {
                this.getView().updateView("unit", rowIndex);
            }
        }

        if (entityKey.equals("scp_saloutstock") || entityKey.equals("scp_receive")) {
            iCalDynamic.calSum(this.getModel().getDataEntity(true), entryKey);
        }
    }

    /**
     * 校验是否为包材物料
     * 2023/11/10 化学试剂五金配件不用录入生产日期、不用录入COA文件
     * @param material
     * @return
     * @author: hst
     * @createDate: 2023/05/22
     */
    private boolean checkIsPackagingMaterial(DynamicObject material) {
        if (Objects.nonNull(material)) {
            String matNum = material.getString("number");
            if (matNum.startsWith("D") || matNum.startsWith("E")) {
                return true;
            }
            // update by hst 2023/11/10 化学试剂五金配件不用录入生产日期、不用录入COA文件
            if (matNum.startsWith("F") || matNum.startsWith("L")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 打开批量销售附件上传页面
     * @author: hst
     * @createDate: 2023/06/02
     */
    private void OpenBatchFileUploadPage() {
        FormShowParameter formShowParameter = new FormShowParameter();
        formShowParameter.setCaption("附件上传");
        formShowParameter.setFormId("yd_scp_saloutstock_bat");
        StyleCss css = new StyleCss();
        css.setWidth("1100");
        css.setHeight("500");
        formShowParameter.getOpenStyle().setInlineStyleCss(css);
        formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
        formShowParameter.setCustomParam("pkId",this.getModel().getValue("id"));
        formShowParameter.setCloseCallBack(new CloseCallBack(this, BTN_UPLOAD));
        this.getView().showForm(formShowParameter);
    }

    /**
     * 根据供应商信息填充生产厂家
     * @author: hst
     * @createDate: 2023/10/10
     */
    private void fillProductBySupplier() {
        DynamicObject supplier = this.getModel().getDataEntity().getDynamicObject("supplier");
        if (Objects.nonNull(supplier)) {
            int row = this.getModel().getEntryRowCount("materialentry");
            for (int i = 0; i < row; i++) {
                // 如果生产厂家为空，使用供应商名称进行填充
                Object value = this.getModel().getValue("yd_producers", i);
                String product = Objects.nonNull(value) ? value.toString() : "";
                if (StringUtils.isBlank(product.toString())) {
                    this.getModel().setValue("yd_producers", supplier.getString("name"), i);
                }
            }
        }
    }

    /**
     * 批量附件上传数据处理
     * @param bill
     * @param data
     * @param model
     * @author: hst
     * @createDate: 2023/06/05
     */
    private void batchFileUpload (DynamicObject bill, Object data, IDataModel model) {
        Map<String,Object> returnsData = (Map<String,Object>) data;
        if (Objects.nonNull(returnsData)) {
            String field = returnsData.get("field").toString();
            List<String> ids = (List<String>) returnsData.get("ids");
            DynamicObjectCollection attachments = (DynamicObjectCollection) returnsData.get("attachments");
            this.attachmentPanelBuildAttachmentFields(bill,field,ids,attachments,model);
        }
    }

    /**
     * 附件面板数据构建单据附件字段
     * @param bill
     * @param attachments
     * @param field
     * @param ids
     * @return
     * @author: hst
     * @createDate: 2023/06/05
     */
    private void attachmentPanelBuildAttachmentFields (DynamicObject bill, String field, List<String> ids,
                                                       DynamicObjectCollection attachments, IDataModel model) {
        List<Object> attaIdList = new ArrayList<Object>();
        for (DynamicObject temp : attachments) {
            attaIdList.add(temp.getString("fbasedataid_id"));
        }
        DynamicObjectCollection targetAtts = bill.getDynamicObjectCollection(ScpSalOutStockConstants.ENTITY_MATERIAL);
        for (int i = 0; i < targetAtts.size(); i++) {
            DynamicObject entry = targetAtts.get(i);
            if (ids.indexOf(entry.getString("id")) != -1) {
                List<Object> tempIdList = new ArrayList<>();
                for (DynamicObject attachment : entry.getDynamicObjectCollection(field)) {
                    tempIdList.add(attachment.getString("fbasedataid_id"));
                }
                tempIdList.addAll(attaIdList);
                model.setValue(field,tempIdList.toArray(),i);
            }
        }
        this.getView().invokeOperation("save");
    }

    /**
     * 操作执行完
     * @param afterDoOperationEventArgs
     * @author: hst
     * @createDate: 2024/02/20
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs afterDoOperationEventArgs) {
        super.afterDoOperation(afterDoOperationEventArgs);
        String key = afterDoOperationEventArgs.getOperateKey();
        // 确认发货/发货撤销操作执行完刷新后，状态显示不对
        if (("unaudit".equals(key) || "submit".equals(key))
                && afterDoOperationEventArgs.getOperationResult().isSuccess()) {
            this.getView().invokeOperation("refresh");
        }
    }

    /**
     * 校验打印状态
     * @author: hst
     * @createDate: 2024/08/22
     */
    private void checkPrintStatus (BeforeDoOperationEventArgs evt) {
        String billStatus = this.getModel().getDataEntity(true).getString("billstatus");
        if (!"C".equals(billStatus)) {
            this.getView().showTipNotification("发货单未审核，不允许进行打印！");
            evt.setCancel(true);
        }
    }

    /**
     * F7弹窗监听
     * @author: hongsitao
     * @createDate: 2025/02/20
     */
    private void addBeforeF7Listener() {
        /* 实体仓库 */
        Control wareControl = this.getView().getControl("yd_warehouse");
        if (Objects.nonNull(wareControl)) {
            BasedataEdit wareEdit = (BasedataEdit) wareControl;

            wareEdit.addBeforeF7SelectListener(evt -> {
                int index = this.getModel().getEntryCurrentRowIndex("materialentry");
                Object matValue = this.getModel().getValue("material", index);

                if (Objects.nonNull(matValue)) {
                    DynamicObject material = (DynamicObject) matValue;
                    String billType = this.getModel().getDataEntity(true).getString("businesstype.number");

                    if (!"150".equals(billType)) {
                        String matNum = material.getString("number");
                        DynamicObject supplier = this.getModel().getDataEntity().getDynamicObject("supplier");
                        Object producer = this.getModel().getValue("yd_producer", index);

                        /* 如果是原辅料类，则根据合格目录进行带出 */
                        if (matNum.startsWith("C")) {
                            if (Objects.nonNull(supplier) && Objects.nonNull(producer)) {
                                Set<Long> wareHouseId = ScpSalOutStockHelper.getWareHousesIdBySumBill(material.getLong("id"),
                                        supplier.getLong("id"), ((DynamicObject) producer).getLong("id"));

                                if (wareHouseId.size() > 0) {
                                    QFilter qFilter = new QFilter("id", QFilter.in, wareHouseId);
                                    evt.addCustomQFilter(qFilter);
                                } else {
                                    this.getView().showTipNotification("请联系采购员维护原辅料【" + matNum + "】的送货仓库！");
                                    evt.setCancel(true);
                                }
                            }
                        }
                    }
                }
            });
        }

        /* 车间 */
        Control workControl = this.getView().getControl("yd_workshop");
        if (Objects.nonNull(workControl)) {
            BasedataEdit workEdit = (BasedataEdit) workControl;

            /* 根据仓库过滤车间 */
            workEdit.addBeforeF7SelectListener(evt -> {
                int index = this.getModel().getEntryCurrentRowIndex("materialentry");
                Object wareValue = this.getModel().getValue("yd_warehouse", index);
                Object matValue = this.getModel().getValue("material", index);

                /* 是否包材-非JIT物料 */
                if (Objects.nonNull(matValue)) {
                    DynamicObject material = (DynamicObject) matValue;
                    String matNum = material.getString("number");
                    if (matNum.startsWith("D") || matNum.startsWith("E")
                            || matNum.startsWith("F") || matNum.startsWith("L")) {
                        boolean isJit = material.getBoolean("yd_isjit");
                        if (!isJit) {
                            if (Objects.isNull(material.getDynamicObject("yd_warehouse"))) {
                                this.getView().showTipNotification("请联系采购员维护JIT物料【" + material.getString("number") + "】的实体仓库！");
                                evt.setCancel(true);
                            } else {
                                wareValue = material.getDynamicObject("yd_warehouse");
                            }
                        }
                    }
                }
                if (Objects.nonNull(wareValue)) {
                    DynamicObject wareHouse = (DynamicObject) wareValue;
                    Set<Long> shopIds = ScpSalOutStockHelper.getShopIdByWareHouse(wareHouse);
                    if (shopIds.size() > 0) {
                        QFilter qFilter = new QFilter("id", QFilter.in, shopIds);
                        evt.addCustomQFilter(qFilter);
                    } else {
                        this.getView().showTipNotification("请联系采购员维护实体仓库【" + wareHouse.getString("number") + "】的车间！");
                        evt.setCancel(true);
                    }
                }

            });

            /* 根据车间自动带出仓库 */
            workEdit.addAfterF7SelectListener(evt -> {
                int index = evt.getCurrentRowIndex();
                ListSelectedRow selectedRow = evt.getListSelectedRow();
                Object shopId = selectedRow.getPrimaryKeyValue();

                if (Objects.nonNull(shopId)) {
                    QFilter qFilter = new QFilter("yd_workshop.yd_shop.id", QFilter.equals, shopId);
                    DynamicObject wareHouse = QueryServiceHelper.queryOne("yd_warehouse", "id",
                            qFilter.toArray());
                    if (Objects.nonNull(wareHouse)) {
                        this.getModel().setValue("yd_warehouse", wareHouse.getLong("id"), index);
                    } else {
                        this.getView().showTipNotification("请联系采购员维护车间对应的送货仓库！");
                    }
                }
            });
        }
        /* 生产商 */
        Control producerControl = this.getView().getControl("yd_producer");
        if (Objects.nonNull(producerControl)) {
            BasedataEdit producerEdit = (BasedataEdit) producerControl;
            producerEdit.addBeforeF7SelectListener(evt -> {
                int index = this.getModel().getEntryCurrentRowIndex("materialentry");
                Object supValue = this.getModel().getValue("supplier");
                Object materialValue = this.getModel().getValue("material", index);
                if (Objects.isNull(supValue)) {
                    this.getView().showTipNotification("请填写销售公司！");
                    evt.setCancel(true);
                }
                if (Objects.isNull(materialValue)) {
                    this.getView().showTipNotification("请填写第" + (index + 1) + "行的物料！");
                    evt.setCancel(true);
                }

                /* 根据供应商 + 物料 获取不为暂停采购或停止采购的合格目录的生产商 */
                Set<String> producerIds = ScpSalOutStockHelper.getQualifiedProducers((DynamicObject) supValue,
                        (DynamicObject) materialValue);
                if (producerIds.size() > 0) {
                    evt.addCustomQFilter(new QFilter("id", QFilter.in, producerIds));
                } else {
                    evt.addCustomQFilter(QFilter.of("1 != 1"));
                }
            });
        }
    }
}
