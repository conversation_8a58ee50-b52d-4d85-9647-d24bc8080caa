package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.list.BillList;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.scp.constants.ScpAddAttachmentConstants;
import kd.bos.tcbj.srm.scp.constants.ScpSalOutStockConstants;
import kd.bos.tcbj.srm.scp.helper.ScpSalOutStockHelper;
import kd.occ.ocepfp.common.entity.SelectedRow;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpSalOutStockListPlugin
 * @className ScpSalOutStockListPlugin
 * @author: hst
 * @createDate: 2022/08/30
 * @description: 销售发货单列表插件
 * @version: v1.0
 */
public class ScpSalOutStockListPlugin extends AbstractListPlugin {

    private final static String BTN_ADDATTACHMENT = "yd_addattachment";
    /**按钮_确认发货*/
    private final static String BTN_DELIVERY = "tbldelivery";

    /**
     * 监听工具栏点击按钮
     * @author: hst
     * @createDate: 2022/09/23
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case BTN_ADDATTACHMENT : {
                this.deliveryAddAttachment();
                break;
            }
        }
    }

    /**
     * 确认发货前校验生产商+物料编码+厂家批号 是否在异常批次库中
     * @param evt
     */
    @Override
    public void beforeItemClick(BeforeItemClickEvent evt) {
        String key = evt.getItemKey();
        switch (key) {
            case BTN_DELIVERY : {
                ListSelectedRowCollection rows = this.getSelectedRows();
                if (rows.size() == 0) {
                    this.getView().showTipNotification("请至少选择一条数据！");
                    evt.setCancel(true);
                    return;
                }
                Set<Object> ids = new HashSet<>();
                for (ListSelectedRow row : rows) {
                    ids.add(row.getPrimaryKeyValue());
                }
                DynamicObject[] bills = BusinessDataServiceHelper.load(ids.toArray(),
                        BusinessDataServiceHelper.newDynamicObject("scp_saloutstock").getDynamicObjectType());
                for (DynamicObject bill : bills) {
                    Map<String, Object> result = new ScpSalOutStockHelper().checkInErrerLot(bill);
                    if (!(boolean) result.get("isSuccess")) {
                        this.getView().showErrorNotification("单据" + bill.get("billno") + ":" + result.get("message").toString());
                    }
                }
                break;
            }
        }
    }

    /**
     * 操作执行前，增加校验
     * @param args
     * @author: hst
     * @createDate: 2024/08/22
     */
    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs args) {
        super.beforeDoOperation(args);
        String key = ((AbstractOperate) args.getSource()).getOperateKey();
        switch (key) {
            case "printpreview" : {
                // update by hst 2024/08/22 校验打印状态
                this.checkPrintStatus(args);
                break;
            }
            default : {}
        }
    }

    /**
     * 补录附件弹窗
     * @author: hst
     * @createDate: 2022/09/23
     */
    public void deliveryAddAttachment () {
        Set<Long> ids = new HashSet<>();
        ListSelectedRowCollection selects = this.getSelectedRows();
        for(ListSelectedRow select : selects) {
            ids.add(Long.valueOf(select.getPrimaryKeyValue().toString()));
        }
        if (ids.size() != 1) {
            this.getView().showTipNotification("请选择一条数据！");
            return;
        }

        // update by hst 2024/03/25 调整为通过配置可补录附件的状态
        DynamicObject param = BusinessDataServiceHelper.loadSingle("yd_e3paramsetting", "name",
                new QFilter[]{new QFilter("number", QFilter.equals, "SALOUT_SUPPLE_STATUS")});
        if (Objects.nonNull(param)) {
            String value = param.getString("name");
            String[] status = value.contains(",") ? value.split(",") : value.split("，");

            if (Objects.isNull(status) || status.length == 0) {
                this.getView().showTipNotification("当前未设置允许附件补录的状态，请联系管理员！");
                return;
            }
            //单据物流状态校验
            boolean check = new ScpSalOutStockHelper().checkBillStatus(BusinessDataServiceHelper.loadSingle(ids.iterator().next(), ScpSalOutStockConstants.ENTITY_MAIN),
                    ScpSalOutStockConstants.FIELD_LOGSTATUS, status);
            if (!check) {
                this.getView().showTipNotification("当前状态不允许进行补录附件！");
                return;
            }
            FormShowParameter formShowParameter = new FormShowParameter();
            formShowParameter.setCaption("补录附件");
            formShowParameter.setFormId(ScpAddAttachmentConstants.ENTITY_MAIN);
            StyleCss css = new StyleCss();
            css.setWidth("1100");
            css.setHeight("500");
            formShowParameter.getOpenStyle().setInlineStyleCss(css);
            formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
            formShowParameter.setCustomParam("pkId", ids.iterator().next());
            formShowParameter.setCloseCallBack(new CloseCallBack(this, ScpAddAttachmentConstants.ENTITY_MAIN));
            this.getView().showForm(formShowParameter);
        }
    }

    /**
     * 子页面关闭后触发
     * 回写附件信息
     * @author: hst
     * @createDate: 2022/09/23
     * @param closedCallBackEvent
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        ListSelectedRowCollection selects = this.getSelectedRows();
        Set<Long> ids = new HashSet<>();
        for(ListSelectedRow select : selects) {
            ids.add(Long.valueOf(select.getPrimaryKeyValue().toString()));
        }
        try {
            if (closedCallBackEvent.getReturnData() != null) {
                new ScpSalOutStockHelper().writeBackAttachment(ids.iterator().next(),
                        ((DynamicObjectCollection) closedCallBackEvent.getReturnData()));
                this.getView().showSuccessNotification("保存成功！");
                this.getView().updateView();
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.getView().showErrorNotification("系统异常，请联系管理员！");
        }
    }

    /**
     * 校验打印状态
     * @author: hst
     * @createDate: 2024/08/22
     */
    private void checkPrintStatus (BeforeDoOperationEventArgs evt) {
        BillList billList = this.getControl("billlistap");
        // 获取选中行记录,注意:得到的结果是记录的主键ID的集合
        ListSelectedRowCollection listSelectedRows = billList.getSelectedRows();

        List<ListSelectedRow> unStandList = listSelectedRows.stream().filter(row ->
                !"C".equals(row.getBillStatus())).collect(Collectors.toList());

        if (unStandList.size() > 0) {
            Set<String> billNos = unStandList.stream().map(row -> row.getBillNo()).collect(Collectors.toSet());

            this.getView().showTipNotification("销售发货单：" + String.join("、", billNos) + "未审核，不允许进行打印");
            evt.setCancel(true);
        }
    }
}
