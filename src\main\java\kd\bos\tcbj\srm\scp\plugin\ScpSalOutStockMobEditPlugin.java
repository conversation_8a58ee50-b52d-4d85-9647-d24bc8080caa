package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractMobBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.form.control.MenuItem;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.EmailInfo;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.tcbj.srm.admittance.helper.EmailBizHelper;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.ScpSalOutStockMobEditPlugin
 * @className ScpSalOutStockMobEditPlugin
 * @author: hst
 * @createDate: 2022/08/30
 * @description: 销售发货单据移动端插件
 * @version: v1.0
 */
public class ScpSalOutStockMobEditPlugin extends AbstractMobBillPlugIn {

    private final static String BTN_SAVE = "confirm";

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        MenuItem menuItem = (MenuItem)this.getView().getControl("confirm");
        menuItem.addItemClickListener(this);
        menuItem.addClickListener(this);
    }

    @Override
    public void click(EventObject evt) {
        super.click(evt);
        if (evt.getSource() instanceof MenuItem) {
            MenuItem item = (MenuItem) evt.getSource();
            String key = item.getOperationKey();
            switch (key) {
                case "save" : {
                    this.sendEmail();
                }
            }
        }
    }

    /**
     * 向仓库发送收货邮件
     */
    public void sendEmail() {
        String company = ((DynamicObject) this.getModel().getValue("supplier")).getString("name");
        String billNo = this.getModel().getValue("billno").toString();
        String driver = this.getModel().getValue("yd_driver").toString();
        String contact = this.getModel().getValue("yd_contact").toString();
        Date delidate = (Date) this.getModel().getValue("delidate");
        EmailInfo emailInfo = new EmailInfo();
        emailInfo.setTitle(company + "送货预约通知");
        String content = "尊敬的XX仓库：/n" +
                "订单" + billNo + "将由" + driver + "于" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(delidate) + "进行配送，如有疑问请电话联系" + contact + "。/n" +
                "此邮件无需回复。详情点击https://ysfkuat.by-health.com/ierp/mobile.html?userId=Guest&form=scp_saloutstock_mob&pkId=1498397099639731200#/page/rootcfbdd92e48934796b052efac03e02f5a";
        /**
        emailInfo.setContent(content);
        **/
         List<String> receiver = new ArrayList<>();
        receiver.add("<EMAIL>");
        emailInfo.setReceiver(receiver);
        //EmailHandler.sendEmail(emailInfo);
        new EmailBizHelper().sendEmail("<EMAIL>",company + "送货预约通知",content);
        ShortMessageInfo shortMessageInfo = new ShortMessageInfo();
        List<String> phone = new ArrayList<>();
        phone.add("13556354398");
        shortMessageInfo.setPhone(phone);
        shortMessageInfo.setCountryCode(CountryCode.CN);
        shortMessageInfo.setMessage(content);
        Map<String, Object> Result =  MessageHandler.sendShortMessage(shortMessageInfo);
        System.out.println("");
    }
}
