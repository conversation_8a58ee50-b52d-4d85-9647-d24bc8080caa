package kd.bos.tcbj.srm.scp.plugin;

import java.util.Objects;
import java.util.Set;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.ConfirmCallBackListener;
import kd.bos.form.MessageBoxOptions;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.MessageBoxClosedEvent;
import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.scm.common.util.BizPartnerUtil;

/**
 * 供应商库存单列表插件，过滤供应商
 * @auditor yanzuliang
 * @date 2022年11月22日
 * 
 */
public class ScpSupplyStockListPlugin extends AbstractListPlugin {

    private final static String MATERIALBILL_BTN = "yd_materialsbill";

	@Override
    public void setFilter(SetFilterEvent e) {
        // 获取当前的供应商信息
        Set<String> supplierIds = new BizPartnerBizHelper().getCurrentBdSupplierId();
        // 对供应商做过滤
        if (supplierIds == null) {
            e.getQFilters().add(QFilter.of("1=0"));
        }else {
            e.getQFilters().add(new QFilter("yd_supply.id", QCP.in, supplierIds));
        }
    }

    /**
     * 按钮点击前事件
     * @author: hst
     * @param: 2022/11/30
     * @param evt
     */
    @Override
    public void beforeItemClick(BeforeItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case MATERIALBILL_BTN : {
                // 列表增加按钮“导出现供物料清单” 2022/11/30 hst
                ConfirmCallBackListener confirmCallBackListener = new ConfirmCallBackListener("materialsBill", this);
                this.getView().showConfirm("是否确定导出现供物料清单？", MessageBoxOptions.YesNo,
                        confirmCallBackListener);
                evt.setCancel(true);
                break;
            }
        }
    }

    /**
     * 确认弹窗点击确认后回调事件
     * @author: hst
     * @createDate: 2022/11/30
     * @param messageBoxClosedEvent
     */
    @Override
    public void confirmCallBack(MessageBoxClosedEvent messageBoxClosedEvent) {
        super.confirmCallBack(messageBoxClosedEvent);
        if("materialsBill".equals(messageBoxClosedEvent.getCallBackId())) {
            // 查询当前登录用户所属供应商
//            DynamicObject supplier = BusinessDataServiceHelper.loadSingle("bos_bizpartneruser","id,username,customer",
//                    new QFilter[]{new QFilter("isadmin", "=", "1"),
//                            new QFilter("user", "=", Long.valueOf(UserServiceHelper.getCurrentUserId()))});
            QFilter supF = BizPartnerUtil.assembleQFilterBizPartner();
			DynamicObjectCollection supCol = QueryServiceHelper.query("bd_supplier", "id,name,number", supF.toArray());
			for (DynamicObject supplier : supCol) {
				String path = new SupplyStockHelper().getMaterialBillExcel(supplier);
				if (null != path && "" != path) {
					getView().openUrl(RequestContext.get().getClientFullContextPath() + path);
				}
			}
//            if (supCol.size() > 0) {
//            } else {
//                this.getView().showTipNotification("获取不到供应商信息或非供应商管理员！");
//            }
        }
    }
}
