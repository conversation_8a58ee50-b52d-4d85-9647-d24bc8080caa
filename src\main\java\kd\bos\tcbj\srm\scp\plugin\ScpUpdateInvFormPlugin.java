package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.plugin.AbstractFormPlugin;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.scp.constants.ScpHandInvConstant;
import kd.bos.tcbj.srm.scp.constants.ScpPurCycleConstant;

import java.util.ArrayList;
import java.util.EventObject;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.pur.helper.PurSDBalanceRptHelper
 * @className: PurSDBalanceRptHelper
 * @description: 供应商更新库存动态表单插件
 * @author: hst
 * @createDate: 2024/06/09
 * @version: v1.0
 */
public class ScpUpdateInvFormPlugin extends AbstractFormPlugin {

    /**
     * 新建数据包完毕后，触发此事件
     * @param e
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void afterCreateNewData(EventObject e) {
        super.afterCreateNewData(e);
        this.setDefaultValue();
    }

    /**
     * 操作执行后
     * @param e
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void afterDoOperation(AfterDoOperationEventArgs e) {
        super.afterDoOperation(e);
        if ("confirm".equals(e.getOperateKey()) && e.getOperationResult().isSuccess()) {
            this.updateData();
        }
    }

    /**
     * 加载默认数据
     * @author: hst
     * @createDate: 2024/06/09
     */
    private void setDefaultValue() {
        // 已存在供应商在手库存
        Object data = this.getView().getFormShowParameter().getCustomParam("billIds");
        if (Objects.nonNull(data)) {
            List<Object> billIds = (List<Object>) data;

            DynamicObjectCollection bills = QueryServiceHelper.query(ScpHandInvConstant.MAIN_ENTITY,
                    "id," + ScpHandInvConstant.getFields(), new QFilter[]{new QFilter("id",QFilter.in, billIds)});

            for (DynamicObject bill : bills) {
                int index = this.getModel().createNewEntryRow("yd_entryentity");
                this.getModel().setValue("yd_billid", bill.getString("id"), index);
                this.getModel().setValue("yd_supplier", bill.getString(ScpHandInvConstant.SUPPLIER_FIELD), index);
                this.getModel().setValue("yd_material", bill.getString(ScpHandInvConstant.MATERIAL_FIELD), index);
                this.getModel().setValue("yd_fertinv", bill.getBigDecimal(ScpHandInvConstant.FERINV_FIELD), index);
                this.getModel().setValue("yd_wipinv", bill.getString(ScpHandInvConstant.WIPINV_FIELD), index);
                this.getModel().setValue("yd_transitinv", bill.getString(ScpHandInvConstant.TRANSITINV), index);
            }
        }

        // 需要新增供应商在手库存的数据
        data = this.getView().getFormShowParameter().getCustomParam("newDatas");
        if (Objects.nonNull(data)) {
            List<Object> newDatas = (List<Object>) data;

            for (Object newData : newDatas) {
                if (Objects.nonNull(newData)) {
                    String supplierId = newData.toString().split("&")[0];
                    String materialId = newData.toString().split("&")[1];

                    int index = this.getModel().createNewEntryRow("yd_entryentity");
                    this.getModel().setValue("yd_supplier", supplierId, index);
                    this.getModel().setValue("yd_material", materialId, index);
                }
            }
        }
    }

    /**
     * 数据更新
     * @author: hst
     * @createDate: 2024/06/09
     */
    private void updateData () {
        Map<String,String> result = new HashMap<>();
        try {
            Map<String, DynamicObject> billMap = new HashMap<>();
            List<DynamicObject> newDatas = new ArrayList<>();

            DynamicObjectCollection entries = this.getModel().getEntryEntity("yd_entryentity");
            for (DynamicObject entry : entries) {
                String billId = entry.getString("yd_billid");
                if (StringUtils.isNotBlank(billId)) {
                    billMap.put(entry.getString("yd_billid"), entry);
                } else {
                    newDatas.add(entry);
                }
            }

            // 查找供应商在手库存
            DynamicObject[] bills = BusinessDataServiceHelper.load(ScpHandInvConstant.MAIN_ENTITY,
                    ScpHandInvConstant.getFields(), new QFilter[]{new QFilter("id", QFilter.in, billMap.keySet())});

            for (DynamicObject bill : bills) {
                if (billMap.containsKey(bill.getString("id"))) {
                    DynamicObject entry = billMap.get(bill.getString("id"));

                    bill.set(ScpHandInvConstant.FERINV_FIELD, entry.getBigDecimal("yd_fertinv"));
                    bill.set(ScpHandInvConstant.WIPINV_FIELD, entry.getBigDecimal("yd_wipinv"));
                    bill.set(ScpHandInvConstant.TRANSITINV, entry.getBigDecimal("yd_transitinv"));
                }
            }

            SaveServiceHelper.save(bills);

            List<DynamicObject> newBills = new ArrayList<>();
            for (DynamicObject newData : newDatas) {
                DynamicObject bill = BusinessDataServiceHelper.newDynamicObject(ScpHandInvConstant.MAIN_ENTITY);

                bill.set(ScpHandInvConstant.SUPPLIER_FIELD, newData.getDynamicObject("yd_supplier"));
                bill.set(ScpHandInvConstant.SUPNO_FIELD, newData.getString("yd_supplier.number"));
                bill.set(ScpHandInvConstant.SUPNAME_FIELD, newData.getString("yd_supplier.name"));
                bill.set(ScpHandInvConstant.MATERIAL_FIELD, newData.getDynamicObject("yd_material"));
                bill.set(ScpHandInvConstant.MATNO_FIELD, newData.getString("yd_material.number"));
                bill.set(ScpHandInvConstant.MATNAME_FIELD, newData.getString("yd_material.name"));
                bill.set(ScpHandInvConstant.FERINV_FIELD, newData.getBigDecimal("yd_fertinv"));
                bill.set(ScpHandInvConstant.WIPINV_FIELD, newData.getBigDecimal("yd_wipinv"));
                bill.set(ScpHandInvConstant.TRANSITINV, newData.getBigDecimal("yd_transitinv"));
                bill.set("billstatus", "A");

                newBills.add(bill);
            }

            if (newDatas.size() > 0) {
                SaveServiceHelper.saveOperate(ScpHandInvConstant.MAIN_ENTITY,newBills.toArray(new DynamicObject[newBills.size()]));
            }

            result.put("isSuccess","true");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("isSuccess","false");
            result.put("errMsg",e.getMessage());
        }

        this.getView().returnDataToParent(result);
        this.getView().close();
    }
}
