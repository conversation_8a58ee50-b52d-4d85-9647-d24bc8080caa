package kd.bos.tcbj.srm.scp.plugin;

import java.util.Set;

import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;

/**
 * VMI库存列表界面插件，过滤供应商
 * @auditor yanzuliang
 * @date 2022年12月20日
 * 
 */
public class ScpVmiInventoryListPlugin extends AbstractListPlugin {

	@Override
    public void setFilter(SetFilterEvent e) {
        // 获取当前的供应商信息
        Set<String> supplierNums = new BizPartnerBizHelper().getCurrentBdSupplierNums();
        // 对供应商做过滤
        if (supplierNums == null) {
            e.getQFilters().add(QFilter.of("1=0"));
        }else {
            e.getQFilters().add(new QFilter("number", QCP.in, supplierNums));
        }
    }
}
