package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillOperationStatus;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.AbstractGrid;
import kd.bos.form.control.Toolbar;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.ORMUtils;
import kd.bos.tcbj.im.util.UIUtils;
import kd.scm.common.util.OpenFormUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.EventObject;

/**
 * 备货单变更编辑界面
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-19
 */
public class StockUpChEditPlugin extends AbstractBillPlugIn {

    @Override
    public void afterBindData(EventObject e) {
        FormShowParameter parameter = this.getView().getFormShowParameter();
        String billType = parameter.getCustomParam("billType");
        // 供应链方处理
        String[] supKeyFields = new String[] {"yd_supcomfirmqty", "yd_supcomfirmdate", "yd_supremark"};
        String[] purKeyFields = new String[] {"yd_qty", "yd_needdate"};

        // 根据不同的来源单据标识，设置分录列锁定
        if (StringUtils.equals(billType, "yd_supstockup")) {
            // 设置 供应商确认数量、供应商确认时间、供应商备注 可用
            UIUtils.setLock(this.getView(), supKeyFields, false);
            UIUtils.setLock(this.getView(), purKeyFields, true);
            // 设置菜单的按钮是否可见
            this.getView().setVisible(true, "yd_createch");
            this.getView().setVisible(false, "yd_confirmch");
        }
        // 采购方
        else if (StringUtils.equals(billType, "yd_stockup")) {
            // 设置供应方的字段不可用
            UIUtils.setLock(this.getView(), supKeyFields, true);
            // 设置菜单的按钮是否可见
            this.getView().setVisible(false, "yd_createch");
            this.getView().setVisible(true, "yd_confirmch");

//            // 设置如果状态已经是“待确认状态”，则灰显
//            if (StringUtils.equals("B", (String) this.getModel().getValue("billstatus"))) {
//                // 灰显
//                this.getView().setEnable(false, "yd_confirmch");
//                // 设置 需求数量、需求日期 不可用
//                UIUtils.setLock(this.getView(), purKeyFields, true);
//            }else {
//                // 设置 需求数量、需求日期 可用
//                UIUtils.setLock(this.getView(), purKeyFields, false);
//            }
            // 设置 需求数量、需求日期 可用
            UIUtils.setLock(this.getView(), purKeyFields, false);
        }
    }

    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs evt) {
        String operateKey = ((AbstractOperate) evt.getSource()).getOperateKey();
        if (StringUtils.equals(operateKey, "confirmch")) {
//            String billStatus = (String) this.getModel().getValue("billstatus");
//            if (StringUtils.equals(billStatus, "B")) {
//                this.getView().showTipNotification("备货单变更单已经待确认，无须继续确认变更！");
//                evt.setCancel(true);
//            }
        }
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs evt) {
        String operateKey = evt.getOperateKey();
        OperationResult operationResult = evt.getOperationResult();
        boolean success = operationResult.isSuccess();
        if (!success) return;

        // 生成变更单
        if (StringUtils.equals(operateKey, "createch")) {
            // 查询已经生成的变更单数据
            Object billId = null;
            DataSet dataSet = null;
            try {
                // 取最新的单据
                dataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), "yd_supstockch", "id, createtime", QFilter.of("yd_srcbillid=?", this.getModel().getValue("id")).toArray(), "createtime desc");
                if (dataSet.hasNext()) {
                    Row row = dataSet.next();
                    billId = row.getLong("id");
                }
            }finally {
                ORMUtils.close(dataSet);
            }
            // 打开变更界面
            OpenFormUtil.openBasePage(this.getView(), "yd_supstockch", billId, BillOperationStatus.EDIT, ShowType.MainNewTabPage, null, null);
            // 设置生成变更单不可用
            this.getView().setEnable(false, "yd_createch");
        }
        // 确认变更
        else if (StringUtils.equals(operateKey, "confirmch")) {
            // 刷新界面
            getView().updateView();
        }
    }
}
