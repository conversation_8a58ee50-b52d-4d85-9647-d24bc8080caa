package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.BillOperationStatus;
import kd.bos.form.CloseCallBack;
import kd.bos.form.ShowType;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.mvc.list.ListView;
import kd.bos.tcbj.im.util.UIUtils;
import kd.scm.common.util.OpenFormUtil;
import kd.scm.common.util.SrmCommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 采购方备货单列表插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-19
 */
public class StockUpListPlugin extends AbstractListPlugin {

    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs evt) {
        String operateKey = ((AbstractOperate) evt.getSource()).getOperateKey();
        if (StringUtils.equals(operateKey, "tblchange")) {
            UIUtils.checkSelected(this.getView(), "请选择其中一条备货单数据进行变更！");
            // 控制职能选一条
            List<Object> selectIds = UIUtils.getSelectIds(this.getView());
            if (selectIds.size() > 1) {
                this.getView().showTipNotification("只能选择其中条数据进行变更！");
                evt.setCancel(true);
                return;
            }
            this.getPageCache().put("billid", String.valueOf(selectIds.get(0)));
        }
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs evt) {
        String operateKey = evt.getOperateKey();
        if (StringUtils.equals(operateKey, "tblchange")) {
            long billId = Long.parseLong(this.getPageCache().get("billid"));
            Map<String, Object> context = new HashMap<>();
            context.put("billType", ((ListView) this.getView()).getListModel().getDataEntityType().getName()); // 根据标识决定变更的是供应商还是采购方
            OpenFormUtil.openBasePage(this.getView(), "yd_stockupch", billId, BillOperationStatus.EDIT, ShowType.MainNewTabPage, context, (CloseCallBack)null);
        }
    }
}
