package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.BillOperationStatus;
import kd.bos.form.CloseCallBack;
import kd.bos.form.ShowType;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.SetFilterEvent;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.mvc.list.ListView;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.UIUtils;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.scm.common.util.OpenFormUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 供应商备货单变更列表插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-31
 */
public class SupStockChListPlugin extends AbstractListPlugin {

    @Override
    public void setFilter(SetFilterEvent e) {
        // 获取当前的供应商信息
        Set<String> supplierIds = new BizPartnerBizHelper().getCurrentBdSupplierId();
        // 对供应商做过滤
        QFilter qFilter;
        if (supplierIds == null) {
            qFilter = QFilter.of("1=0");
        }else {
            qFilter = new QFilter("yd_supplier.id", QCP.in, supplierIds);
        }
        // 如果是罗程，则不过滤
        Long lcUserId = BizHelper.getQueryOne("bos_user", "id", QFilter.of("name = '罗程'").toArray());
        Long currentUserId = BizHelper.getUserId();
        // 过滤允许其他采购员查看单据
        qFilter.or(QFilter.of("yd_puruser.id = ? or (yd_puruser.id = 0 and "+lcUserId+"=?)", currentUserId, currentUserId));
        e.getQFilters().add(qFilter);
    }
}
