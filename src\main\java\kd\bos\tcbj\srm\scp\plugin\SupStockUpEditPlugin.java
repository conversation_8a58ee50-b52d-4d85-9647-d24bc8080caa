package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillOperationStatus;
import kd.bos.form.CloseCallBack;
import kd.bos.form.ShowType;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.mvc.list.ListView;
import kd.bos.tcbj.im.util.UIUtils;
import kd.scm.common.util.OpenFormUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.EventObject;
import java.util.HashMap;
import java.util.Map;

/**
 * 备货单变更单编辑界面
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-19
 */
public class SupStockUpEditPlugin extends AbstractBillPlugIn {

    @Override
    public void afterBindData(EventObject e) {
        // 设置供应方字段必填
        UIUtils.setRequired(this.getView(), new String[] {"yd_supcomfirmqty","yd_supcomfirmdate"});
        if (StringUtils.equals("C", (String)this.getModel().getValue("billstatus"))) {
            this.getView().setEnable(false, "yd_confirm");
        }
    }

    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs evt) {
        String operateKey = ((AbstractOperate) evt.getSource()).getOperateKey();
        if (StringUtils.equals(operateKey, "confirm")) {
            // 检查分录供应方字段是否已经填写
            for (int index=0, size=this.getModel().getEntryRowCount("yd_materialentry"); index<size; index++) {
                Object supComfirmQty = this.getModel().getValue("yd_supcomfirmqty", index);
                Object supComfirmDate = this.getModel().getValue("yd_supcomfirmdate", index);
                // 判断供应商确认数量是否为空
                if (supComfirmQty == null) {
                    this.getView().showTipNotification(String.format("第%s行分录，供应商确认数量不能为空！", index+1));
                    evt.setCancel(true);
                    return;
                }
                // 判断供应商确认日期是否为空
                if (supComfirmDate == null) {
                    this.getView().showTipNotification(String.format("第%s行分录，供应商确认时间不能为空！", index+1));
                    evt.setCancel(true);
                    return;
                }
            }
        }
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs evt) {
        String operateKey = evt.getOperateKey();
        if (StringUtils.equals(operateKey, "confirm")) {
            // 刷新界面
            this.getView().updateView();
        }
    }
}
