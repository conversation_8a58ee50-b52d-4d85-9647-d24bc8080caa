package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.BillOperationStatus;
import kd.bos.form.CloseCallBack;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.BeforeItemClickEvent;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.events.BeforeDoOperationEventArgs;
import kd.bos.form.events.SetFilterEvent;
import kd.bos.form.operate.AbstractOperate;
import kd.bos.list.BillList;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.mvc.list.ListView;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.im.helper.BizHelper;
import kd.bos.tcbj.im.util.UIUtils;
import kd.bos.tcbj.srm.admittance.helper.BizPartnerBizHelper;
import kd.scm.common.util.OpenFormUtil;
import kd.scm.common.util.SrmCommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 供应商备货单列表插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-10-19
 */
public class SupStockUpListPlugin extends AbstractListPlugin {

    @Override
    public void setFilter(SetFilterEvent e) {
        // 获取当前的供应商信息
        Set<String> supplierIds = new BizPartnerBizHelper().getCurrentBdSupplierId();
        // 对供应商做过滤
        if (supplierIds == null) {
            e.getQFilters().add(QFilter.of("1=0"));
        }else {
            e.getQFilters().add(new QFilter("yd_supplier.id", QCP.in, supplierIds));
        }
    }

    @Override
    public void beforeDoOperation(BeforeDoOperationEventArgs evt) {
        String operateKey = ((AbstractOperate) evt.getSource()).getOperateKey();
        if (StringUtils.equals(operateKey, "tblchange")) {
            UIUtils.checkSelected(this.getView(), "请选择其中一条备货单数据进行变更！");
            // 控制职能选一条
            List<Object> selectIds = UIUtils.getSelectIds(this.getView());
            if (selectIds.size() > 1) {
                this.getView().showTipNotification("只能选择其中条数据进行变更！");
                evt.setCancel(true);
                return;
            }
            // 检查状态是否正确，职能变更状态为“已确认”的备货单
//            String billsStatus = BizHelper.getQueryOne("yd_supstockup", "billstatus", QFilter.of("id=?", selectIds.get(0)).toArray());
//            if (!StringUtils.equals("C", billsStatus)) {
//                this.getView().showTipNotification("不能变更状态还未确认的单据！");
//                evt.setCancel(true);
//                return;
//            }
            String billNo = BizHelper.getQueryOne("yd_supstockch", "billNo", QFilter.of("billstatus <> 'C' and yd_srcbillid = ?", selectIds.get(0)).toArray());
            if (StringUtils.isNotBlank(billNo)) {
                this.getView().showTipNotification("存在还未确认变更的备货单变更单！");
                evt.setCancel(true);
                return;
            }


            // 潘丹选中的单据是否
            this.getPageCache().put("billid", String.valueOf(selectIds.get(0)));
        }
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs evt) {
        String operateKey = evt.getOperateKey();
        if (StringUtils.equals(operateKey, "tblchange")) {
            long billId = Long.parseLong(this.getPageCache().get("billid"));
            Map<String, Object> context = new HashMap<>();
            context.put("billType", ((ListView) this.getView()).getListModel().getDataEntityType().getName()); // 根据标识决定变更的是供应商还是采购方
            OpenFormUtil.openBasePage(this.getView(), "yd_stockupch", billId, BillOperationStatus.EDIT, ShowType.MainNewTabPage, context, (CloseCallBack)null);
        }
    }
}
