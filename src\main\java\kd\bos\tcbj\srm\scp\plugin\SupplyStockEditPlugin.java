package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.field.BasedataEdit;
import kd.bos.form.field.events.BeforeF7SelectEvent;
import kd.bos.form.field.events.BeforeF7SelectListener;
import kd.bos.list.ListShowParameter;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.scm.common.util.BizPartnerUtil;

import java.util.EventObject;
import java.util.HashSet;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.scp.plugin.SupplyStockEditPlugin
 * @className SupplyStockEditPlugin
 * @author: hst
 * @createDate: 2022/09/05
 * @description: 供应商库存单表单插件
 * @version: v1.0
 */
public class SupplyStockEditPlugin extends AbstractBillPlugIn implements BeforeF7SelectListener {

    /**
     * 绑定数据后触发，用于初始化数据
     * @author: hst
     * @createDate: 2022/09/05
     * @param e
     */
    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        Long userId = UserServiceHelper.getCurrentUserId();
        if (this.getModel().getValue(SupplyStockHelper.FIELD_PURCHASING) == null) {
            this.getModel().setValue(SupplyStockHelper.FIELD_PURCHASING, RequestContext.get().getOrgId());
        }
        //获取人员数据
        DynamicObject user = BusinessDataServiceHelper.loadSingle(userId,"bos_user");
        if (user != null) {
            String phone = user.getString("phone");
            String email = user.getString("email");
            if (phone != null || email != null) {
                //获取采购人员资料
                DynamicObject srmUser = BusinessDataServiceHelper.loadSingle("srm_user","supplier",
                        new QFilter[]{QFilter.or(new QFilter("number",QFilter.equals,phone),new QFilter("number",QFilter.equals,email))});
                if (srmUser != null && this.getModel().getValue("yd_supply") == null) {
                    this.getModel().setValue("yd_supply",srmUser.getDynamicObject("supplier"));
                }
            }
        }
    }

    /**
     * 注册监听
     * @author: hst
     * @createDate: 2022/09/05
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
//        this.userChooseWare();
        
        BasedataEdit supplier = (BasedataEdit) this.getControl("yd_supply");
		if (supplier != null) {
			supplier.addBeforeF7SelectListener(this::beforeF7Select);
		}
		
		BasedataEdit material = (BasedataEdit) this.getControl("yd_warecode");
		if (material != null) {
			material.addBeforeF7SelectListener(this::beforeF7Select);
		}
    }
    
    /**
     * 对F7进行过滤
     * @param beforeF7SelectEvent
     */
    public void beforeF7Select(BeforeF7SelectEvent beforeF7SelectEvent) {
		String name = beforeF7SelectEvent.getProperty().getName();
		// 供应商增加过滤条件只能选择自己代理的供应商
		if (name.equals("yd_supply")) {
			((ListShowParameter) beforeF7SelectEvent.getFormShowParameter()).getListFilterParameter().getQFilters().add(BizPartnerUtil.assembleQFilterBizPartner());
		}
		
		//物料过滤只能选货源清单和收货单的物料
		if (name.equals("yd_warecode")) {
			QFilter supF = BizPartnerUtil.assembleQFilterBizPartner();
			DynamicObjectCollection supCol = QueryServiceHelper.query("bd_supplier", "id,name,number", supF.toArray());
			Set<String> supNumSet = new HashSet<String>();
			for (DynamicObject supObj : supCol) {
				supNumSet.add(supObj.getString("number"));
			}
			
			QFilter supNumF = new QFilter("yd_supnum", QCP.in, supNumSet);
			Set<String> matNumSet = new HashSet<String>();
			DynamicObjectCollection listCol = QueryServiceHelper.query("yd_supplymatlist", "id,name,number,yd_matnum", supNumF.toArray());
			for (DynamicObject listObj : listCol) {
				matNumSet.add(listObj.getString("yd_matnum"));
			}
			
			QFilter matNumF = new QFilter("number", QCP.in, matNumSet);
			((ListShowParameter) beforeF7SelectEvent.getFormShowParameter()).getListFilterParameter().getQFilters().add(matNumF);
		}
    }

    /**
     * 监听用户选择的物料
     * 用于为规格型号，单位赋值
     * @author: hst
     * @createDate: 2022/09/05
     */
    public void userChooseWare() {
        BasedataEdit step = (BasedataEdit) this.getView().getControl(SupplyStockHelper.COLUMN_WARECODE);
        step.addAfterF7SelectListener((evt) -> {
            DynamicObjectCollection wareCollection = (DynamicObjectCollection) this.getModel().getEntryEntity(SupplyStockHelper.ENTITY_WAREDETAIL);
            for (DynamicObject ware : wareCollection) {
                ware.set(SupplyStockHelper.COLUMN_UNIT,((DynamicObject) ware.get(SupplyStockHelper.COLUMN_WARECODE)).get("baseunit"));
            }
            this.getView().updateView(SupplyStockHelper.ENTITY_WAREDETAIL);
        });
    }
}
