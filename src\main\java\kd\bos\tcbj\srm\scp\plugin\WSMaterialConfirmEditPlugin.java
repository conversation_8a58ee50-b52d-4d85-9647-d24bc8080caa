package kd.bos.tcbj.srm.scp.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.entity.datamodel.events.ChangeData;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.form.events.AfterDoOperationEventArgs;
import kd.bos.form.field.DateEdit;
import kd.bos.tcbj.im.plugin.KdepBillPlugin;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.EventObject;

/**
 * 包材叫货确认单
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-11-4
 */
public class WSMaterialConfirmEditPlugin extends KdepBillPlugin {

    @Override
    public void afterBindData(EventObject e) {

        // 获取供应商确认状态，如果已经确认，则不允许再次确认
        String confrimStatus = (String) this.getModel().getValue("yd_confirmstatus");
        if (StringUtils.equals("2", confrimStatus)) {
            this.getView().setEnable(false, "yd_confirm");
        }
        
        int size = this.getModel().getDataEntity().getDynamicObjectCollection("entryentity").size();
        Date maxDate = new Date();
        for(int i=0;i<size;i++) {
        	Date needDate = (Date) this.getModel().getValue("yd_needdate", i);
        	if (needDate.after(maxDate)) {
        		maxDate = needDate;
        	}
        }
        DateEdit confirmDate = this.getView().getControl("yd_supconfirmdate");
        confirmDate.setMaxDate(maxDate);
    }

    @Override
    public void afterDoOperation(AfterDoOperationEventArgs e) {
        String operateKey = e.getOperateKey();
        if (StringUtils.equals("confirm", operateKey)) {
            // 确认后，刷新界面
            reLoadPage();
        }
    }

    @Override
    public void propertyChanged(PropertyChangedArgs e, String field) {
        // 供应商填写确认数量，如果需求数量-供应商确认数量>0时勾选
        if (StringUtils.equals(field, "yd_supconfirmqty")) {
            ChangeData[] changeSet = e.getChangeSet();
            for (ChangeData changeData : changeSet) {
                int rowIndex = changeData.getRowIndex();
                BigDecimal supConfirmQty = (BigDecimal) this.getValue(field, rowIndex);
                BigDecimal needQty = (BigDecimal) this.getValue("yd_needqty", rowIndex);
                this.setValue("yd_iserror", needQty.compareTo(supConfirmQty) > 0, rowIndex);
            }
        }
        
        if (StringUtils.equals(field, "yd_supconfirmdate")) {
        	ChangeData[] changeSet = e.getChangeSet();
            for (ChangeData changeData : changeSet) {
                int rowIndex = changeData.getRowIndex();
                if (this.getValue(field, rowIndex) == null) {
                	continue;
                }
                Date confirmDate = (Date) this.getValue(field, rowIndex);
                Date needDate = (Date) this.getValue("yd_needdate", rowIndex);
                if (confirmDate.after(needDate)) {
                	this.getView().showTipNotification("确认送货日期不可大于需求日期，请检查并重新填写！");
                	this.setValue("yd_supconfirmdate", null, rowIndex);
                }
            }
        }
    }
}
