package kd.bos.tcbj.srm.scp.print;

import kd.bos.print.core.data.DataRowSet;
import kd.bos.print.core.data.field.Field;
import kd.bos.print.core.plugin.AbstractPrintPlugin;
import kd.bos.print.core.plugin.event.AfterLoadDataEvent;
import kd.bos.print.core.plugin.event.BeforeOutputWidgetEvent;
import java.util.*;

/**
 * 供应方销售发货单打印插件，分录打印二维码
 * @auditor yanzuliang
 * @date 2022年12月5日
 * 
 */
public class ScpSaloutStockEnPrintPlugin extends AbstractPrintPlugin {
	//单据体标识
    private static final String ENTRY_FIELD = "entryentity";

    //单据体中物料编码标识
    private static final String ENTRY_MATERIA_NUMBER_FIELD = "yd_cqentryid";

    //二维码控件标识
    private static final String BARCODE = "Barcode";

    //主数据源标识
    private static final String MAIN_DATASOURCE = "scp_saloutstock";

    @Override
    public void afterLoadData(AfterLoadDataEvent evt) {
        super.afterLoadData(evt);
        List<DataRowSet> rowSets = evt.getDataRowSets();
        List<DataRowSet> newRows = new ArrayList<>();

        Iterator<DataRowSet> rowIterator = rowSets.iterator();
        while (rowIterator.hasNext()) {
            DataRowSet rowSet = rowIterator.next();
            List<DataRowSet> entryRows = rowSet.getCollectionField(ENTRY_FIELD).getValue();

            entryRows.forEach(entryRow -> {
                //深拷贝新行，每行分录新增一行，如果不需要展示单据头和单据体字段，则可不用深拷贝
                DataRowSet copy = deepCopy(rowSet);
                copy.put(ENTRY_MATERIA_NUMBER_FIELD, entryRow.getField(ENTRY_MATERIA_NUMBER_FIELD));
//            	rowSet.put(ENTRY_MATERIA_NUMBER_FIELD, entryRow.getField(ENTRY_MATERIA_NUMBER_FIELD));
                newRows.add(rowSet);
            });
            //删除当前单据行
            rowIterator.remove();
        }
        //添加所有分录行
        rowSets.addAll(newRows);
    }

    @Override
    public void beforeOutputWidget(BeforeOutputWidgetEvent evt) {
        super.beforeOutputWidget(evt);
        //数据源替换，因为在afterLoadData方法里我们已经把分录数据平铺开了
        if (BARCODE.equals(evt.getWidgetKey())) {
        	evt.bindField(MAIN_DATASOURCE, ENTRY_MATERIA_NUMBER_FIELD);
//        	evt.setOutputValue(null);
//        	System.out.println(111);
        }
    }
    
    public DataRowSet deepCopy(DataRowSet oriData) {
    	DataRowSet dataRowSet = new DataRowSet();
    	String[] fields = new String[]{"createtime","id","auditor.name","auditdate","billno","creator.name"};
    	for (String field : fields) {
    	    Field value = (Field) oriData.getField(field);
    	    dataRowSet.put(field, value);
    	}
    	return dataRowSet;
    }
}
