package kd.bos.tcbj.srm.scp.print;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.print.core.data.DataRowSet;
import kd.bos.print.core.data.field.CollectionField;
import kd.bos.print.core.plugin.AbstractPrintPlugin;
import kd.bos.print.core.plugin.event.AfterLoadDataEvent;
import kd.bos.servicehelper.QueryServiceHelper;

/**
 * 销售发货单分录排序后打印插件
 * @auditor yanzuliang
 * @date 2023年3月31日
 * 
 */
public class ScpSaloutStockEnSortPrintPlugin extends AbstractPrintPlugin {

	// 关联数据源标识
	private static final String DSNAME = "scp_saloutstock";

	// 关联数据源分录的标识
	private static final String ENTRYENTITY = "materialentry";

	@Override
	public void afterLoadData(AfterLoadDataEvent evt) {
		super.afterLoadData(evt);

		// 获取关联数据源
		if (DSNAME.equals(evt.getDataSource().getDsName())) {
			// 获取关联数据源数据包
			List<DataRowSet> dataRowSets = evt.getDataRowSets();
			// 遍历数据包中每条数据，获取分录数据，重新排序并设置回去
			for (DataRowSet drs : dataRowSets) {
				// 获取分录数据
				String billNo = drs.getField("billno").getValue().toString();
				List<DataRowSet> entryentity = drs.getCollectionField(ENTRYENTITY).getValue();
				// 对分录数据进行重新排序，可针对某个字段进行排序，这里的场景只需要反序即可
				Collections.reverse(entryentity);
				sortEn(billNo, entryentity);
				// 将排序好的数据重新设置回去
				drs.put(ENTRYENTITY, new CollectionField(entryentity));
			}

			// 将每条数据里面分录已经排序完的数据包重新设置回去
			evt.setDataRowSets(dataRowSets);
		}
	}

	/**
	 * 对分录进行排序：按物料+生产商+批次，对数量排序从小到大
	 * @param entryentity
	 */
	private void sortEn(String billNo, List<DataRowSet> entryentity) {
		QFilter filter = new QFilter("billno", QCP.equals, billNo);
		String orderBys = "materialentry.material asc, materialentry.yd_producers asc, materialentry.suplot asc, materialentry.qty asc";
//		String orderBys = "materialentry.qty asc";
		String selectFields = "id,billno,materialentry.material mat,materialentry.material.number matNum, materialentry.yd_producers producer, materialentry.suplot lot, materialentry.qty qty";
		DynamicObjectCollection col = QueryServiceHelper.query("scp_saloutstock", selectFields, filter.toArray(), orderBys);
		
		List<DataRowSet> copyRs = new ArrayList<DataRowSet>();
		for (DataRowSet tmpDs : entryentity) {
			copyRs.add(tmpDs);
		}
		
		int index = 0;
		for (DynamicObject tmpObj : col) {
			String matNum = tmpObj.getString("matNum");
			String producer = tmpObj.getString("producer");
			String supLot = tmpObj.getString("lot");
			BigDecimal qty = tmpObj.getBigDecimal("qty");
			String key = matNum+"&"+producer+"&"+supLot+"&"+qty.setScale(5).toString();
//			System.out.println("根据query查询的排序："+key);
			
			for (DataRowSet tmpRs : copyRs) {
				String tmpMatNum = tmpRs.getField("material.number").getValue().toString();
				String tmpProducer = tmpRs.getField("yd_producers").getValue().toString();
				String tmpSupLot = tmpRs.getField("suplot").getValue().toString();
				BigDecimal tmpQty = new BigDecimal(tmpRs.getField("qty").getValue().toString());
				String tmpKey = tmpMatNum+"&"+tmpProducer+"&"+tmpSupLot+"&"+tmpQty.setScale(5).toString();
//				System.out.println("被匹配的tmpKey："+tmpKey);
				if (tmpKey.equalsIgnoreCase(key)) {
					entryentity.set(index, tmpRs);
					break;
				}
			}
			
			index++;
		}
	}
}
