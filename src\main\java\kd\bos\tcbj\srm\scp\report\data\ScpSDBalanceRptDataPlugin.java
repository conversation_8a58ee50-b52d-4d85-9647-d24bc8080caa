package kd.bos.tcbj.srm.scp.report.data;

import kd.bos.algo.DataSet;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.pur.constants.PurMatQuotaConstant;
import kd.bos.tcbj.srm.pur.helper.PurSDBalanceRptHelper;

import java.util.List;

/**
 * @package: kd.bos.tcbj.srm.scp.report.data.ScpSDBalanceRptDataPlugin
 * @className: ScpSDBalanceRptDataPlugin
 * @description: 供应商库存动态高低水位即供需平衡报表查询插件
 * @author: hst
 * @createDate: 2024/06/08
 * @version: v1.0
 */
public class ScpSDBalanceRptDataPlugin extends AbstractReportListDataPlugin {

    // 供应商物料配额字段
    private static String matQuotaField = PurMatQuotaConstant.MATERIAL_FIELD + " yd_material," + PurMatQuotaConstant.SUPPLIER_FIELD
            + " yd_supplier," + PurMatQuotaConstant.BIZDATE_FIELD + " yd_bizdate," + PurMatQuotaConstant.QTY_FIELD + " yd_quotaqty,"
            + PurMatQuotaConstant.MATERIAL_FIELD + ".number yd_matNum," + PurMatQuotaConstant.SUPPLIER_FIELD + ".number yd_supNum";

    /**
     * 报表数据查询
     * @param reportQueryParam
     * @param o
     * @return
     * @throws Throwable
     * @author: hst
     * @createDate: 2024/06/08
     */
    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        List<FilterItemInfo> filterInfos = this.getQueryParam().getFilter().getFilterItems();
        List<QFilter> filters = this.getQueryParam().getCustomFilter();

        // 查询供应商物料配额
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),
                PurMatQuotaConstant.MAIN_ENTITY, matQuotaField, filters.toArray(new QFilter[filters.size()]), null)
                .groupBy(new String[]{"yd_material","yd_supplier","yd_matNum","yd_supNum"}).finish();

        // 关联物料属性
        dataSet = PurSDBalanceRptHelper.getMateriAttribute(dataSet);

        // 关联采购周期
        dataSet = PurSDBalanceRptHelper.getPurCycle(dataSet);

        // 关联汤臣库存
        dataSet = PurSDBalanceRptHelper.getRawMaterialInventory(dataSet);

        // 关联VMI库存
        dataSet = PurSDBalanceRptHelper.getVMIInventory(dataSet);

        // 关联物料配额信息
        dataSet = PurSDBalanceRptHelper.getSupplierMaterialQuota(filterInfos,dataSet,filters);

        dataSet = PurSDBalanceRptHelper.dataAnalysis(dataSet);

        return dataSet;
    }

}
