package kd.bos.tcbj.srm.scp.report.data;

import kd.bos.algo.DataSet;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.ORM;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.report.data.SupplyStockRptListDataPlugin
 * @className: SupplyStockRptListDataPlugin
 * @description: 用于控制供应商库存报表数据过滤逻辑
 * @author: hst
 * @createDate: 2022/08/22
 * @version: v1.0
 */
public class SupplyStockRptListDataPlugin  extends AbstractReportListDataPlugin {

    private final static String ENTITY_NAME = "yd_scp_supplystock";

    @Override
    public DataSet query(ReportQueryParam reportQueryParam, Object o) throws Throwable {
        FilterInfo filterInfo = reportQueryParam.getFilter();
        QFilter[] qFilters = buildFilter(filterInfo);
        ORM orm = ORM.create();
        String fields = "yd_supply.name,yd_waredetail.yd_warecode,yd_waredetail.yd_unit,yd_waredetail.yd_quantity,yd_waredetail.yd_supwarename," +
                "yd_waredetail.yd_manufacturer,yd_updatedate,billstatus,yd_waredetail.yd_warecode.number as materialnum";
        DataSet dataSet = orm.queryDataSet(getClass().getName(), ENTITY_NAME, fields,
                qFilters, "yd_updatedate desc");
        // update by hst 2022/11/25 筛选条件增加物料分类过滤，物料中物料分类分录表为空，由另一个元数据维护，需用join关联
        QFilter sortFilter = buildSortFilter(filterInfo);
        DataSet materialGroup = orm.queryDataSet(this.getClass().getName(),"bd_materialgroupdetail","material.number as matnumber",
                new QFilter[]{sortFilter},null);
        dataSet = dataSet.join(materialGroup).on("materialnum","matnumber").select("yd_supply.name","yd_waredetail.yd_warecode","yd_waredetail.yd_unit","yd_waredetail.yd_quantity","yd_waredetail.yd_supwarename",
                        "yd_waredetail.yd_manufacturer","yd_updatedate","billstatus").finish();
        //取出已审核的数据
        dataSet = dataSet.where("billstatus = 'B'");
        return dataSet;
    }

    /**
     * 构造过滤条件
     * @author: hst
     * @createDate: 2022/10/10
     * @param filterInfo
     * @return
     */
    public QFilter[] buildFilter (FilterInfo filterInfo) {
        List<QFilter> qFilters = new ArrayList<>();
        QFilter matFilter = buildMatFilter(filterInfo);
        QFilter supFilter = buildSupFilter(filterInfo);
        QFilter weekFilter = buildWeekFilter(filterInfo);
        if (Objects.nonNull(matFilter)) {
            qFilters.add(matFilter);
        }
        if (Objects.nonNull(supFilter)) {
            qFilters.add(supFilter);
        }
        if (Objects.nonNull(weekFilter)) {
            qFilters.add(weekFilter);
        }
        return qFilters.toArray(new QFilter[qFilters.size()]);
    }

    /**
     * 物料编码过滤条件
     * @author: hst
     * @createDate: 2022/10/10
     * @param filterInfo
     * @return
     */
    protected static QFilter buildMatFilter (FilterInfo filterInfo) {
        FilterItemInfo material = filterInfo.getFilterItem("yd_material");
        QFilter qFilter = null;
        if (Objects.nonNull(material)) {
            DynamicObject materialValues = (DynamicObject) material.getValue();
            if (Objects.nonNull(materialValues)) {
                qFilter = new QFilter("yd_waredetail.yd_warecode.number", QFilter.equals, materialValues.get("number"));
            }
        }
        return qFilter;
    }

    /**
     * 供应商过滤条件
     * @author: hst
     * @createDate: 2022/10/10
     * @param filterInfo
     * @return
     */
    protected static QFilter buildSupFilter (FilterInfo filterInfo) {
        FilterItemInfo supplier = filterInfo.getFilterItem("yd_supplier");
        QFilter qFilter = null;
        if (Objects.nonNull(supplier)) {
            DynamicObject supplierValues = (DynamicObject) supplier.getValue();
            if (Objects.nonNull(supplierValues)) {
                qFilter = new QFilter("yd_supply.number", QFilter.equals, supplierValues.get("number"));
            }
        }
        return qFilter;
    }

    /**
     * 是否只显示一周内数据
     * @author: hst
     * @createDate: 2022/10/10
     * @param filterInfo
     * @return
     */
    protected static QFilter buildWeekFilter (FilterInfo filterInfo) {
        FilterItemInfo week = filterInfo.getFilterItem("yd_week");
        QFilter qFilter = null;
        if (Objects.nonNull(week)) {
            boolean weekValues = (boolean) week.getValue();
            if (Objects.nonNull(weekValues) && weekValues) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, -7);
                Date date = calendar.getTime();
                qFilter = new QFilter(SupplyStockHelper.FIELD_UPDATEDATE,QFilter.large_equals,format.format(date));
            }
        }
        return qFilter;
    }

    /**
     * 物料类别过滤条件
     * @author: hst
     * @createDate: 2022/11/25
     * @param filterInfo
     * @return
     */
    protected static QFilter buildSortFilter (FilterInfo filterInfo) {
        FilterItemInfo sort = filterInfo.getFilterItem("yd_matgroup");
        QFilter qFilter = null;
        if (Objects.nonNull(sort)) {
            DynamicObject matgroup = (DynamicObject) sort.getValue();
            if (Objects.nonNull(matgroup)) {
                qFilter = new QFilter("group.number", QFilter.equals, matgroup.get("number"));
            }
        }
        return qFilter;
    }
}
