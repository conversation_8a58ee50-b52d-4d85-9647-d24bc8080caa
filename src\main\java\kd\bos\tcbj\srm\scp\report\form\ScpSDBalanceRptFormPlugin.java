package kd.bos.tcbj.srm.scp.report.form;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.exception.KDBizException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.StyleCss;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.list.IListView;
import kd.bos.orm.query.QFilter;
import kd.bos.report.ReportList;
import kd.bos.report.events.CellStyleRule;
import kd.bos.report.plugin.AbstractReportFormPlugin;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.pur.helper.PurSDBalanceRptHelper;
import kd.bos.tcbj.srm.scp.constants.ScpPurCycleConstant;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.scm.common.util.BizPartnerUtil;

import java.util.ArrayList;
import java.util.EventObject;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.scp.report.form.ScpSDBalanceRptFormPlugin
 * @className: ScpSDBalanceRptFormPlugin
 * @description: 供应商库存动态高低水位即供需平衡报表界面插件
 * @author: hst
 * @createDate: 2024/06/09
 * @version: v1.0
 */
public class ScpSDBalanceRptFormPlugin extends AbstractReportFormPlugin {

    // 更新库存按钮标识
    private final static String UPDATEINV_BTN = "yd_updateinv";
    // 索赔单获取维度填写回调标识
    private final static String UPDATE_CALLBACK = "update_callback";

    /**
     * 事件注册
     * @param e
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.addItemClickListeners(new String[]{"toolbarap"});
    }

    /**
     * 按钮点击事件
     * @param evt
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case UPDATEINV_BTN : {
                // 打开库存更新页面
                this.openUpdateInventryPage();
                break;
            }
        }
    }

    /**
     * 取数前事件
     * @param queryParam
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void beforeQuery(ReportQueryParam queryParam) {
        ReportList reportList = getControl("reportlistap");
        List<FilterItemInfo> filterInfos = this.getQueryParam().getFilter().getFilterItems();
        List<QFilter> filters = this.getQueryParam().getCustomFilter();
        // 报表列表中动态添加表格字段
        PurSDBalanceRptHelper.createGroupColumnList(filterInfos,filters,reportList);
        super.beforeQuery(queryParam);
    }

    /**
     * 子页面关闭回调事件
     * @param closedCallBackEvent
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        String actionId = closedCallBackEvent.getActionId();
        Object returndata = closedCallBackEvent.getReturnData();
        if (Objects.nonNull(returndata)) {
            switch (actionId) {
                case UPDATE_CALLBACK : {
                    // 显示更新结果
                    this.showUpdateResult(returndata);
                }
            }
        }
    }

    /**
     * 校验过滤条件
     * @param queryParam
     * @return
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public boolean verifyQuery(ReportQueryParam queryParam) {
        // 构建过滤条件
        try {
            PurSDBalanceRptHelper.buildQueryFilter(queryParam);

            // 构建供应商过滤条件
            this.bulidSupplierFilter(queryParam);

            return true;
        } catch (Exception e) {
            this.getView().showTipNotification(e.getMessage());
            return false;
        }
    }

    /**
     * 设置单元格样式规则
     * @param cellStyleRules
     * @author: hst
     * @createDate: 2024/06/09
     */
    @Override
    public void setCellStyleRules(List<CellStyleRule> cellStyleRules) {
        super.setCellStyleRules(cellStyleRules);
        PurSDBalanceRptHelper.setCellStyle(cellStyleRules);
    }

    /**
     * 打开库存更新页面
     * @author: hst
     * @createDate: 2024/06/09
     */
    private void openUpdateInventryPage() {
        //获取报表列表
        ReportList reportList = this.getView().getControl("reportlistap");

        int[] indexs = reportList.getEntryState().getSelectedRows();

        List<Object> billIds = new ArrayList<>();
        List<String> newDatas = new ArrayList<>();
        if (indexs.length == 0) {
            this.getView().showTipNotification("请至少选择一行数据");
        } else {
            for (int index : indexs) {
                DynamicObject data = reportList.getReportModel().getRowData(index);
                String billId = data.getString("yd_id_inv");

                if (StringUtils.isNotBlank(billId)) {
                    billIds.add(billId);
                } else {
                    String materialId = data.getString("yd_material.id");
                    String supplierId = data.getString("yd_supplier.id");
                    if (StringUtils.isNotBlank(supplierId) && StringUtils.isNotBlank(materialId)) {
                        newDatas.add(supplierId + "&" + materialId);
                    }
                }
            }
            FormShowParameter formShowParameter = new FormShowParameter();
            formShowParameter.setFormId("yd_scp_updateinv");
            StyleCss styleCss = new StyleCss();
            formShowParameter.setCustomParam("billIds",billIds);
            formShowParameter.setCustomParam("newDatas",newDatas);
            formShowParameter.getOpenStyle().setInlineStyleCss(styleCss);
            formShowParameter.setCaption("库存更新");
            formShowParameter.getOpenStyle().setShowType(ShowType.Modal);
            formShowParameter.setCloseCallBack(new CloseCallBack(this, UPDATE_CALLBACK));
            this.getView().showForm(formShowParameter);
        }
    }

    /**
     * 显示更新结果
     * @param returndata
     * @author: hst
     * @createDate: 2024/06/09
     */
    private void showUpdateResult (Object returndata) {
        Map<String,String> result = (Map<String,String>) returndata;
        if ("true".equals(result.get("isSuccess"))) {
            this.getView().showSuccessNotification("更新成功！");
            this.getView().invokeOperation("refresh");
        } else {
            this.getView().showTipNotification("更新失败，请联系管理员，失败原因：" + result.get("errMsg"));
        }
    }

    /**
     * 构建供应商过滤条件
     * @param queryParam
     * @author: hst
     * @createDate: 2024/06/10
     */
    private void bulidSupplierFilter (ReportQueryParam queryParam) {
        // 获取当前用户关联的供应商
        QFilter supFilter = BizPartnerUtil.assembleQFilterBizPartner();
        DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),
                "bd_supplier", "id", new QFilter[]{supFilter}, null);

        List<String> supplierIds = new ArrayList<>();
        for (Row row : dataSet) {
            supplierIds.add(row.getString("id"));
        }

        if (supplierIds.size() > 0) {
            queryParam.getCustomFilter().add(new QFilter(ScpPurCycleConstant.SUPPLIER_FIELD + ".id", QFilter.in, supplierIds));
        } else {
            throw new KDBizException("请切换供应商用户");
        }
    }
}
