package kd.bos.tcbj.srm.scp.report.form;

import kd.bos.entity.datamodel.events.ChangeData;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.form.container.Container;
import kd.bos.form.control.Button;
import kd.bos.form.control.Search;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.control.events.SearchEnterEvent;
import kd.bos.form.control.events.SearchEnterListener;
import kd.bos.form.events.FilterContainerSearchClickArgs;
import kd.bos.form.events.HyperLinkClickEvent;
import kd.bos.form.events.HyperLinkClickListener;
import kd.bos.form.field.ComboEdit;
import kd.bos.form.field.FieldEdit;
import kd.bos.orm.query.QFilter;
import kd.bos.report.plugin.AbstractReportFormPlugin;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.taxc.common.util.StringUtil;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.scp.report.form.SupplyStockRptFormPlugin
 * @className: SupplyStockRptFormPlugin
 * @description: 供应商库存报表插件
 * @author: hst
 * @createDate: 2022/09/06
 * @version: v1.0
 */
public class SupplyStockRptFormPlugin extends AbstractReportFormPlugin implements SearchEnterListener {

    private final static String FIELD_RADIO = "yd_radiogroupfield";

    /**
     * 数据加载完毕后触发
     * @author: hst
     * @createDate: 2022/09/06
     * @param e
     */
    @Override
    public void afterBindData(EventObject e) {
        super.afterBindData(e);
        this.getView().refresh();
    }

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        ((Search)this.getControl("yd_searchap")).addEnterListener(this);
    }

    /**
     * 搜索框事件
     * @author: hst
     * @createDate: 2022/09/06
     * @param searchEnterEvent
     */
    @Override
    public void search(SearchEnterEvent searchEnterEvent) {
        String searchText = searchEnterEvent.getText();
        ReportQueryParam queryParam = getQueryParam();
        List<QFilter> qFilters = queryParam.getFilter().getQFilters();
        QFilter qFilter = null;
        if (StringUtil.isNotBlank(searchText)) {
            qFilters.clear();
            List<LinkedHashMap<String,Object>> searchs = (ArrayList) searchEnterEvent.getSearchFields();
            for (LinkedHashMap<String,Object> search : searchs) {
                List<String> fileds = (ArrayList) search.get("fieldName");
                List<String> values = (ArrayList) search.get("value");
                for (String field : fileds) {
                    for (String value : values) {
                        if (qFilter == null) {
                            qFilter = new QFilter(field.toString(), QFilter.like, "%" + value.toString() + "%");
                        } else {
                            qFilter = QFilter.or(qFilter,new QFilter(field.toString(), QFilter.like, "%" + value.toString() + "%"));
                        }
                    }
                }
            }
            qFilters.add(qFilter);
        } else {
            qFilters.clear();
        }
        //是否只显示一周内数据
        String radio = this.getModel().getValue(FIELD_RADIO).toString();
        if (StringUtil.equals("1",radio)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -1);
            Date date = calendar.getTime();
            qFilters.add(new QFilter(SupplyStockHelper.FIELD_UPDATEDATE,QFilter.large_equals,format.format(date)));
        }
        this.getView().refresh();
    }

    /**
     * 判断当前是只显示一周内数据或显示全部
     * @param e
     */
    @Override
    public void propertyChanged(PropertyChangedArgs e) {
        //是否只显示一周内数据
        String radio = this.getModel().getValue(FIELD_RADIO).toString();
        if (e.getProperty().toString().indexOf(FIELD_RADIO) > -1) {
            List<QFilter> qFilters = getQueryParam().getFilter().getQFilters();
            for (ChangeData change : e.getChangeSet()) {
                if (StringUtil.equals("1",change.getNewValue().toString()) && StringUtil.equals("2",change.getOldValue().toString())) {
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DATE, -1);
                    Date date = calendar.getTime();
                    qFilters.add(new QFilter(SupplyStockHelper.FIELD_UPDATEDATE,QFilter.large_equals,format.format(date)));
                } else if (StringUtil.equals("2",change.getNewValue().toString()) && StringUtil.equals("1",change.getOldValue().toString())) {
                    qFilters.remove(qFilters.size() - 1);
                }
            }
            this.getView().refresh();
        }
    }
}
