package kd.bos.tcbj.srm.scp.schedulejob;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.im.util.DateTimeUtils;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;
import kd.bos.tcbj.srm.scp.util.MessageUtil;
import kd.taxc.common.util.StringUtil;

/**
 * 每天上午10点运行，获取已确认状态、收货数量小于订货数量，交货日期-当前日期=交货提前期的采购订单
 * 获取交货提前期大于0的供应商数据，根据供应商获取采购订单及用户并发送短信通知
 * 获取已确认状态、非已入库状态的采购订单，然后遍历分录订货数量，组成一个map，再遍历交货计划的数量从小到大进行扣减，扣减到那一条的时候日期刚好提前期，就发短信
 * @auditor yanzuliang
 * @date 2023年3月28日
 * 
 */
public class DeliveryNotifyTask extends AbstractTask {

	@Override
	public void execute(RequestContext ctx, Map<String, Object> paramMap) throws KDException {
		QFilter filter = new QFilter("yd_deliveryadvancedays", QCP.large_than, 0);
		DynamicObjectCollection supCol = QueryServiceHelper.query("bd_supplier", "id,name,number,yd_deliveryadvancedays", filter.toArray());
		for (DynamicObject supObj : supCol) {
			String supId = supObj.get("id").toString();
			int advanceDays = supObj.getInt("yd_deliveryadvancedays");
			Calendar cal = Calendar.getInstance();
			cal.setTime(new Date());
			cal.add(5, advanceDays);
			Date deliveryDate = cal.getTime();
			String deliveryDateStr = DateTimeUtils.format(deliveryDate, "yyyyMMdd");
			
			// pur_order
			QFilter billFilter = new QFilter("cfmstatus", QCP.equals, "B");
			billFilter.and(new QFilter("logstatus", QCP.not_equals, "G"));
			billFilter.and(new QFilter("supplier.id", QCP.equals, supId));
			billFilter.and(QFilter.of("materialentry.qty > materialentry.suminstockqty"));
			DynamicObjectCollection orderCol = QueryServiceHelper.query("pur_order", "id,billno,yd_deliveryplan.yd_supdeliverydate supDeliveryDate,yd_deliveryplan.yd_masterial.number matNum", billFilter.toArray());
			if (orderCol.size() == 0) {
				continue;
			}
			
			// 系统检测到您在yyyy年MM-月dd日需要发货的数据，采购订单XXX的物料AAA、BBB，为避免延期发货，请及时登录汤臣倍健SRM系统操作发货，谢谢。
			Map<String,String> orderMap = new HashMap<String,String>();
			for (DynamicObject orderObj : orderCol) {
				String billNum = orderObj.getString("billno");
				String planDateStr = DateTimeUtils.format(orderObj.getDate("supDeliveryDate"), "yyyyMMdd");
				String matNumStr = "";
				if (orderMap.containsKey(billNum)) {
					matNumStr = orderMap.get(billNum);
				}
				
				if (deliveryDateStr.equalsIgnoreCase(planDateStr)) {
					String matNum = orderObj.getString("matNum");
					// 拼接短信内容
					matNumStr = matNumStr+matNum+"、";
				}
				if (matNumStr.length() == 0) {
					continue;
				}
				matNumStr = matNumStr.substring(0, matNumStr.length()-1);
				orderMap.put(billNum, matNumStr);
			}
			
			StringBuffer msg = new StringBuffer();
			String planDateStr = DateTimeUtils.format(deliveryDate, "yyyy年MM月dd日");
			msg.append("系统检测到您在"+planDateStr+"需要发货的数据：");
			
			if (orderMap.keySet().size() == 0) {
				continue;
			}
			
			Iterator<String> ite = orderMap.keySet().iterator();
			while(ite.hasNext()) {
				String billNum = ite.next();
				msg.append("采购订单"+billNum+"的物料"+orderMap.get(billNum)+"；");
			}
			
			msg.append("为避免延期发货，请及时登录汤臣倍健SRM系统操作发货，谢谢。");
			
			// 获取供应商用户发送短信
			//获取对应供应商用户数据
            DynamicObjectCollection supplierUserCol = SupplyStockHelper.getSrmSupplierUsersBySupplierId(supId);
            if (supplierUserCol != null) {
            	for (DynamicObject supplierUserInfo : supplierUserCol) {
            		if (StringUtil.isNotBlank(supplierUserInfo.getString("user.phone"))) {
            			String phone = supplierUserInfo.getString("user.phone");
            			MessageUtil.sendShortMessage(phone, msg.toString());
            		}
				}
            }
			
		}
	}

}
