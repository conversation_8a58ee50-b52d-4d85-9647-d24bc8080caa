package kd.bos.tcbj.srm.scp.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.srm.scp.helper.ScpSalOutStockHelper;

import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.scp.schedulejob.SalOutStockTask
 * @className: SalOutStockTask
 * @author: hst
 * @createDate: 2023/01/06
 * @version: v1.0
 */
public class SalOutStockTask extends AbstractTask {

    // 未上传COA文件
    private final static String COA_REMINDER = "COA_Reminder";
    // 未上传CIQ文件或报关单
    private final static String CIQ_REMINDER = "CIQ_Reminder";
    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        if (map.containsKey("key")) {
            String key = map.get("key").toString();
            switch (key) {
                case COA_REMINDER : {
                    new ScpSalOutStockHelper().lackFileReminder("COA");
                    break;
                }
                case CIQ_REMINDER : {
                    new ScpSalOutStockHelper().lackFileReminder("CIQ");
                    break;
                }
            }
        }
    }
}
