package kd.bos.tcbj.srm.scp.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.scp.oplugin.StockUpChConfirmOp;

import java.util.Map;

/**
 * 预付款请款单付款状态取值
 * @package: kd.bos.tcbj.srm.scp.schedulejob.ScpMaterialLoanPaymenTask
 * @className: ScpMaterialLoanPaymenTask
 * @author: pjl
 * @createDate: 2025/02/20
 * @version: v1.0
 */
public class ScpMaterialLoanPaymenTask extends AbstractTask {

    protected static final Log log = LogFactory.getLog(ScpMaterialLoanPaymenTask.class.getName());

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        DynamicObject[] materialLoanBill = BusinessDataServiceHelper.load("yd_materialloanbill", "id,billno,yd_paybillstate", QFilter.of("yd_paybillstate!=?", "已付款").toArray());
        log.info("预付款申请单开始");
        for (DynamicObject dynamicObject : materialLoanBill) {
            String billno = dynamicObject.getString("billno");
            Object pk = dynamicObject.getPkValue();
            boolean exists = QueryServiceHelper.exists("er_dailyloanbill", QFilter.of("yd_topkey = ? and billstatus = ?", billno, "G").toArray());

            if(exists){
                log.info(billno+"已付款");
                dynamicObject.set("yd_paybillstate","已付款");
            }
        }
        SaveServiceHelper.update(materialLoanBill);
    }
}
