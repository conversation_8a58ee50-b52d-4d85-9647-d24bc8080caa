package kd.bos.tcbj.srm.scp.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.srm.scp.helper.ScpOrderHelper;

import java.util.Map;

/**
 * @package: kd.bos.tcbj.scm.scp.schedulejob.ScpOrderTask
 * @className: ScpOrderTask
 * @author: hst
 * @createDate: 2022/09/21
 * @version: v1.0
 */
public class ScpOrderTask extends AbstractTask {

    private final static String TIME_REMINDER = "timeoutReminder";

    /**
     * 采购订单调度计划
     * 订单确认提醒功能
     * @author: hst
     * @createDate: 2022/09/21
     * @param requestContext
     * @param map
     * @throws KDException
     */
    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        if (map.containsKey("key")) {
            String key = map.get("key").toString();
            switch (key) {
                case TIME_REMINDER : {
                    new ScpOrderHelper().timeoutReminder();
                    break;
                }
            }
        }
    }
}
