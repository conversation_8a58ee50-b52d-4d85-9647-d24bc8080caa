package kd.bos.tcbj.srm.scp.schedulejob;

import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.tcbj.srm.scp.helper.SupplyStockHelper;

import java.util.Map;

/**
 * @package: kd.bos.tcbj.scm.scp.schedulejob.SupplyStockTask
 * @className kd.bos.tcbj.scm.scp.helper.SupplyStockHelper
 * @author: hst
 * @createDate: 2022/09/05
 * @version: v1.0
 */
public class SupplyStockTask extends AbstractTask {

    private final static String DEL_SUPPLYSTOCK = "deleteSupplyStock";
    private final static String NOT_UPDATEINVENTORY = "notifyUpdateInventory";

    /**
     * 供应商库存单定时任务
     * 自动删除一个月之前的供应商库存单
     * 查询供应商在一周内（根据更新日期）是否存在供应商库存单，如果不存在则发送邮件给供应商
     * @param requestContext
     * @param map
     * @throws KDException
     */
    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        if (map.containsKey("key")) {
            String key = map.get("key").toString();
            switch (key) {
                case DEL_SUPPLYSTOCK: {
                    int month = map.containsKey("month") ? Integer.parseInt(map.get("month").toString()) : 1;
                    SupplyStockHelper.deleteSupplyStockMonthAgo(month);
                    break;
                }
                case NOT_UPDATEINVENTORY: {
                    SupplyStockHelper.notifyUpdateInventory();
                    break;
                }
            }
        } else {

        }

    }
}
