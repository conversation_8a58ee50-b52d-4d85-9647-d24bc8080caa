package kd.bos.tcbj.srm.scp.util;

import java.util.List;

import java.util.Map;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.DispatchServiceHelper;

public class EASUtil {

    private final static Log logger = LogFactory.getLog(EASUtil.class);

    /**
     * 查询EAS系统数据
     * @param sql    查询语句
     * @param values 参数值
     * @param types  参数类型  java.sql.Types
     * @param rows   0<返回行数<=1000
     * @return List<Map<String,Object>> 行<列<列名,列值>>
     */
    public static List<Map<String,Object>> executeQuerySQL(String sql,List<Object> values,List<Integer> types,Integer rows) {
        //获取配置编码
        QFilter qFilter = new QFilter("database_type", "=", "eas");
        qFilter.and("state", "=", "S");
        DynamicObject[] load = BusinessDataServiceHelper.load("isc_database_link", "number", qFilter.toArray());
        if(load.length>0) {
            String cnNumber = load[0].getString("number");
            List<Map<String,Object>> invokeBizService = DispatchServiceHelper.invokeBizService("isc", "iscb", "ISCDataCopyService", "executeQuerySQL",
                    new Object[] {cnNumber, sql, values, types, rows});
            return invokeBizService;
        }else {
            throw new KDBizException("请检查eas集成连接配置是否正确!");
        }
    }
}
