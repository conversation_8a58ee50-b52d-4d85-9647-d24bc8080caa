package kd.bos.tcbj.srm.scp.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.bos.Interface.utils.HttpUtil;
import kd.bos.common.QueryUtil;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.exception.KDBizException;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.message.api.CountryCode;
import kd.bos.message.api.EmailInfo;
import kd.bos.message.api.ShortMessageInfo;
import kd.bos.message.service.handler.EmailHandler;
import kd.bos.message.service.handler.MessageHandler;
import kd.bos.metadata.dao.MetaCategory;
import kd.bos.metadata.dao.MetadataDao;
import kd.bos.metadata.form.FormMetadata;
import kd.bos.mvc.list.ListView;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.user.UserServiceHelper;
import kd.bos.servicehelper.workflow.MessageCenterServiceHelper;
import kd.bos.tcbj.srm.admittance.plugin.BillCirculationListPlugin;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.bos.workflow.engine.msg.info.MessageInfo;
import kd.bos.workflow.engine.msg.util.yzj.YunzhijiaToDoUtil;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @package: kd.bos.tcbj.scm.scp.util.MessageUtil
 * @className: MessageUtil
 * @author: hst
 * @createDate: 2022/09/21
 * @version: v1.0
 */
public class MessageUtil {

    private static Log logger = LogFactory.getLog(MessageUtil.class);
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 发送短信
     * @author: hst
     * @createDate: 2022/09/21
     * @param phone 手机号码
     * @param context 短信内容
     * @return
     */
    public static Map<String, Object> sendShortMessage(String phone,String context) {
        return sendShortMessage(Arrays.asList(new String[]{phone}),context);
    }

    /**
     * 发送短信(多个)
     * @author: hst
     * @createDate: 2024/03/22
     * @param phones 手机号码
     * @param context 短信内容
     * @return
     */
    public static Map<String, Object> sendShortMessage(List<String> phones, String context) {
        ShortMessageInfo messageInfo = new ShortMessageInfo();
        messageInfo.setPhone(phones);
        messageInfo.setCountryCode(CountryCode.CN);
        messageInfo.setMessage(context);
        return MessageHandler.sendShortMessage(messageInfo);
    }

    /**
     * 发送邮件
     * @author: hst
     * @createDate: 2022/09/21
     * @param receiver 收件人
     * @param title 标题
     * @param content 内容
     * @return
     */
    public static Map<String,Object> sendEmail(String receiver,String title, String content) {
        return sendEmail(Arrays.asList(new String[]{receiver}),title,content);
    }

    /**
     * 发送邮件(多个
     * @author: hst
     * @createDate: 2024/03/22
     * @param receivers 收件人
     * @param title 标题
     * @param content 内容
     * @return
     */
    public static Map<String,Object> sendEmail(List<String> receivers,String title, String content) {
        EmailInfo emailInfo = new EmailInfo();
        emailInfo.setTitle(title);
        emailInfo.setContent(content);
        emailInfo.setReceiver(receivers);
        return EmailHandler.sendEmail(emailInfo);
    }

    /**
     * 推送消息中心消息
     * @author: hst
     * @createDate: 2022/09/21
     * @param userIds 用户id
     * @param title 标题
     * @param content 内容
     * @return
     */
    public static Long sendMessage(List<Long> userIds, String title, String content) {
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setTitle(title);
        messageInfo.setUserIds(userIds);
        messageInfo.setContent(content);
        return MessageCenterServiceHelper.sendMessage(messageInfo);
    }

    /**
     * 推送消息中心消息
     * @author: hst
     * @createDate: 2022/09/21
     * @param userId 用户id
     * @param title 标题
     * @param content 内容
     * @return
     */
    public static Long sendMessage(String userId, String title, String content) {
        return sendMessage(Arrays.asList(new Long[]{Long.valueOf(userId)}), title, content);
    }

    /**
     * 发送消息中心消息
     * @param selectUserList
     * @param circulationOption
     * @author: hst
     * @createDate: 2023/04/28
     */
    public static MessageInfo sendMessageCenter(String formId, String billId, Set<Long> selectUserList, LocaleString title, LocaleString circulationOption) {
        MessageInfo messageInfo = new MessageInfo();
        // 默认标题标题
        messageInfo.setMessageTitle(title);
        // 消息内容
        messageInfo.setMessageContent(circulationOption);
        // 消息接收人列表
        messageInfo.setUserIds(com.kingdee.bos.util.backport.Arrays.asList(selectUserList.toArray()));
        // 消息类型
        messageInfo.setType(MessageInfo.TYPE_MESSAGE);
        // 发送人
        Long userId = UserServiceHelper.getCurrentUserId();
        messageInfo.setSenderId(userId);
        // 邮件多人单发模式
        messageInfo.setSignleEmail(true);
        messageInfo.setEntityNumber(formId);
        // 日期
        messageInfo.setSendTime(new Date());
        // 业务操作
        messageInfo.setOperation("view");
        messageInfo.setBizDataId(Long.valueOf(billId));
        messageInfo.setContentUrl("/ierp/index.html?formId=" + formId + "&pkId=" + billId);
        MessageCenterServiceHelper.sendMessage(messageInfo);

        return messageInfo;
    }

    /**
     * 发送EAS待办消息
     * @author: hst
     * @createDate: 2024/07/01
     */
    public static void sendESBMessage (MessageInfo message) {
        try {
            String accessToken = "";
            String path = RequestContext.get().getClientFullContextPath();
            if (path.contains("/ierp")) {
                path = path.substring(0, path.indexOf("/ierp"));
            }
            String esbUrl = System.getProperty("esb.url");
            String appId = System.getProperty("esb.appId");
            String appSecret = System.getProperty("esb.appSecret");

            List<MessageInfo> messages = YunzhijiaToDoUtil.rebuildMessage(message);
            logger.info("ESB新增待阅接口 发送ESB链接消息日志：" + message);
            Iterator<MessageInfo> var4 = messages.iterator();
            while (var4.hasNext()) {
                MessageInfo toDoInfo = var4.next();
                String content = toDoInfo.getContent();
                String title = toDoInfo.getTitle();
                Long bizDataId = toDoInfo.getBizDataId();
                if (bizDataId == null || bizDataId.longValue() == 0L)
                    bizDataId = toDoInfo.getId();
                message.getId();
                List userids = toDoInfo.getUserIds();
                String url = toDoInfo.getContentUrl();
                if (url == null)
                    break;
                url = path + url;
                String sendDate = sdf.format((message.getSendTime() != null) ? message.getSendTime() : new Date());
                String founder = "系统发送";
                if (message.getSenderId() != null && message.getSenderId().longValue() != 0L) {
                    String senderId = message.getSenderId().toString();
                    QFilter userFilter = new QFilter("id", "=", senderId);
                    DynamicObject bos_user = QueryUtil.loadSingle("bos_user", userFilter.toArray());
                    founder = bos_user.getString("number");
                }
                JSONObject userName = new JSONObject();
                JSONArray userNames = new JSONArray();
                for (int i = 0; i < userids.size(); i++) {
                    QFilter qFilter = new QFilter("id", "=", userids.get(i));
                    DynamicObject user = BusinessDataServiceHelper.loadSingle("bos_user", "id,username", qFilter.toArray());
                    JSONObject LoginName = new JSONObject();
                    if (user != null) {
                        String wids = user.getString("username");
                        userName.put("LoginName", wids + "@by-healthdc.com");
                        LoginName.put("LoginName", wids + "@by-healthdc.com");
                        userNames.add(LoginName);
                    }
                }

                if (StringUtils.isBlank(accessToken)) {
                    String result = HttpUtil.get(esbUrl + "/ESBServer/sysApi/appAuthenticate?appId=" + appId + "&appSecret=" + appSecret);
                    logger.info("ESBESBTodoIntegrationHandler: ===>Get access token: " + result);
                    if (StringUtils.isNotBlank(result)) {
                        JSONObject resultObject = JSONObject.parseObject(result);
                        String errorCode = resultObject.getString("errorCode");

                        if ("00".equals(errorCode)) {
                            accessToken = resultObject.getJSONObject("returnObject").getString("access_token");
                        }
                    }
                }

                if (StringUtils.isNotBlank(accessToken)) {
                    String tcUrl = "http://eip.by-health.com/third/sso/ssoCommon_redirect.jsp?redirectTo=";
                    url = URLEncoder.encode(url, "utf-8");
                    Map<String, String> postValues = new HashMap<>();
                    postValues.put("appName", "FSSC");
                    postValues.put("modelName", "FSSC");
                    postValues.put("modelId", "" + bizDataId);
                    postValues.put("subject", title + (StringUtils.isNotBlank(content) ? ("：" + content) : ""));
                    postValues.put("link", tcUrl + url);
                    postValues.put("mobileLink", tcUrl + url);
                    postValues.put("padLink", tcUrl + url);
                    postValues.put("type", "2");
                    if (userids.size() > 1) {
                        postValues.put("targets", userNames.toString());
                    } else {
                        postValues.put("targets", userName.toString());
                    }
                    postValues.put("createTime", sendDate);

                    JSONObject creators = new JSONObject();
                    creators.put("LoginName", founder);
                    postValues.put("docCreator", creators.toString());
                    logger.info("ESBESBTodoIntegrationHandler: ===>Get " + postValues);
                    String result = HttpUtil.post(esbUrl + "/ESBServer/SysNotifyApi/sendTodo?access_token=" + accessToken, postValues);
                    logger.info("ESBESBTodoIntegrationHandler:===>Start workflow return: " + result);
                    if (StringUtils.isNotBlank(result)) {
                        JSONObject resultObject = JSONObject.parseObject(result);
                        String errorCode = resultObject.getString("errorCode");

                        if ("2".equals(errorCode)) {
                            logger.info("已保存");
                        } else {
                            String errorMessage = resultObject.getString("errorMessage");
                            throw new KDBizException(errorMessage);
                        }
                    }
                } else {
                    throw new KDBizException("获取EAS登录TOKEN失败，请联系管理员！");
                }
            }
        } catch (Exception e) {
            logger.info("ESBESBTodoIntegrationHandler:  ===> workflow error : " + e.getMessage());
            throw new KDBizException("发送EAS待阅失败：" + e.getMessage());
        }
    }
}
