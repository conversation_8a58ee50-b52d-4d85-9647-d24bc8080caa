package kd.bos.tcbj.srm.scp.webapi;

import kd.bos.openapi.common.custom.annotation.ApiController;
import kd.bos.openapi.common.custom.annotation.ApiGetMapping;
import kd.bos.openapi.common.custom.annotation.ApiMapping;
import kd.bos.openapi.common.custom.annotation.ApiParam;
import kd.bos.openapi.common.result.CustomApiResult;
import kd.bos.tcbj.srm.scp.helper.ScpSalOutStockHelper;

import java.io.Serializable;

/**
 * @package（包）: kd.bos.tcbj.srm.scp.webapi.ScpSalOutMaterialCallbackPlugin
 * @className（类名称）: ScpSalOutMaterialCallbackPlugin
 * @description（类描述）: 物料发货附件信息获取接口
 * @author（创建人）: hst
 * @createDate（创建时间）: 2023/05/06
 * @version（版本）: v1.0
 */
@ApiController(value = "scp", desc = "供应协同")
@ApiMapping(value = "stock")
public class ScpSalOutMaterialCallbackPlugin implements Serializable {

    /**
     * 根据物料编码和批次获取发货分录的附件信息
     * @author: hst
     * @createDate: 2023/05/06
     * @param material 物料编码
     * @param lot 批次号
     * @return
     */
    @ApiGetMapping(value = "getAttachments", desc = "根据物料编码和批次获取发货分录的附件信息")
    public CustomApiResult<Object> getAttachments (@ApiParam("material") String material, @ApiParam("lot") String lot) {
        return new ScpSalOutStockHelper().getMaterialAttachment(material,lot);
    }

    /**
     * 合格供应商及不合格批次获取接口
     * @author: hst
     * @createDate: 2023/08/21
     * @param supplierNum 供应商编码
     * @param producerName 生产厂家名称
     * @param materialNum 物料编码
     * @param supLot 厂家批次
     * @return
     */
    @ApiGetMapping(value = "getQualifiedSupplyAndAbnormalBatch", desc = "合格供应商及不合格批次获取接口")
    public CustomApiResult<Object> getQualifiedSupplyAndAbnormalBatch (@ApiParam("supplierNum") String supplierNum, @ApiParam("producerName") String producerName,
                                                                       @ApiParam("materialNum") String materialNum, @ApiParam("supLot") String supLot) {
        return new ScpSalOutStockHelper().getQualifiedSupplyAndAbnormalBatch(supplierNum,producerName,materialNum,supLot);
    }
}
