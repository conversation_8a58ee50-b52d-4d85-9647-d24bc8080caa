package kd.bos.tcbj.srm.scp.wf;

import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.util.StringUtils;
import kd.bos.workflow.api.AgentExecution;
import kd.bos.workflow.api.constants.WFTaskResultEnum;
import kd.bos.workflow.engine.dynprocess.freeflow.WFDecisionOption;
import kd.bos.workflow.engine.extitf.IWorkflowPlugin;

import java.util.Map;

/**
 * 进口物料预付申请单工作流插件
 */
public class ScpMaterialLoanBillWorkFlowPlugin implements IWorkflowPlugin {
    private static final Log logger = LogFactory.getLog(ScpMaterialLoanBillWorkFlowPlugin.class);
    @Override
    public void notify(AgentExecution execution) {
        IWorkflowPlugin.super.notify(execution);
        logger.info("进入进口物料申请单工作流插件！");
        String auditType = "T";//T：同意授权；F：不同意授权
        Object taskType = execution.getCurrentTaskResult(WFTaskResultEnum.auditType);

        if(taskType!=null && StringUtils.isNotEmpty(taskType.toString()))
        {//人工审批节点
            logger.info("进入进口物料申请单工作流插件！taskType:"+taskType.toString());
            if(WFDecisionOption.AUDIT_TYPE_APPROVE.equals(taskType.toString())){
                auditType = "T";
            }else{
                auditType = "F";
            }

        }else
        {//自动审批节点
            //自动审批节点，参数
            Map pluginMap=execution.getCurrentWFPluginParams();
            String autoAuditResult=(String)pluginMap.get("autoAuditResult");
            logger.info("进入进口物料申请单工作流插件！taskType为空,autoAuditResult:"+autoAuditResult);
            if("true".equals(autoAuditResult))
            {
                auditType = "T";
            }else
            {
                auditType = "F";
            }
        }

        if("T".equals(auditType)){
            logger.info("进入进口物料申请单工作流插件！auditType = T");
            String businessKey = execution.getBusinessKey();//单据的BusinessKey(业务ID)
            logger.info("进入进口物料申请单工作流插件！businessKey:"+businessKey);
            DynamicObject orderObj = BusinessDataServiceHelper.loadSingle(businessKey,"yd_materialloanbill");
            OperationResult operationResult = OperationServiceHelper.executeOperate("audit", "yd_materialloanbill", new DynamicObject[]{orderObj}, OperateOption.create());
            logger.info("进入进口物料申请单工作流插件！"+businessKey+"operationResult:"+operationResult.toString());
            logger.info("进入进口物料申请单工作流插件！"+businessKey+"message:"+operationResult.getMessage());
            logger.info("进入进口物料申请单工作流插件！"+businessKey+"isSuccess:"+operationResult.isSuccess());
        }else {
            logger.info("进入进口物料申请单工作流插件！auditType = F");
        }
    }
}
