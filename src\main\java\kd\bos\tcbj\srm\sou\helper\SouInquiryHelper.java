package kd.bos.tcbj.srm.sou.helper;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.api.ApiResult;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.taxc.common.util.StringUtil;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.sou.helper.SouInquiryHelper
 * @className SouInquiryHelper
 * @author: hst
 * @createDate: 2022/09/07
 * @version: v1.0
 */
public class SouInquiryHelper {

    public final static String ENTITY_MAIN = "sou_inquiry";
    public final static String FIELD_BILLNO = "billno";
    public final static String FIELD_TOTALIN = "totalinquiry";
    public final static String FIELD_DATEFROM = "datefrom";
    public final static String FIELD_DATETO = "dateto";
    public final static String FIELD_ENDDATE = "enddate";
    public final static String FIELD_PAYCOND = "paycond";
    public final static String FIELD_SETTLE = "settletype";
    public final static String FIELD_CURR = "curr";
    public final static String FIELD_INV = "invtype";
    public final static String FIELD_TAX = "taxtype";
    public final static String FIELD_LOCCURR = "loccurr";
    public final static String FIELD_EXCH = "exchtype";
    public final static String FIELD_EXCHRATE = "exchrate";
    public final static String FIELD_SUMQTY = "sumqty";
    public final static String FIELD_RATEDATE = "ratedate";
    public final static String FIELD_SUPCURR = "supcurrtype";
    public final static String FIELD_PERSON = "person";
    public final static String FIELD_ORG = "org";
    public final static String FIELD_TURNS = "turns";
    public final static String FIELD_SUPSCOPE = "supscope";
    public final static String FIELD_ISAUTOFILL = "isautofillprice";
    /* ------ 物料明细分录 ------ */
    public final static String ENTITY_MATERIAL = "materialentry";
    public final static String COLUNM_ENTRYID = "id";
    public final static String COLUMN_MASTERIAL = "material";
    public final static String COLUMN_LASTPRICE = "yd_lastprice";
    public final static String COLUNM_AVERAGEPRICE = "yd_averageprice";
    public final static String COLUMN_MATERIALNAME = "materialnametext";
    public final static String COLUMN_MATERIALDESC = "materialdesc";
    public final static String COLUMN_UNIT = "unit";
    public final static String COLUMN_QTY = "qty";
    public final static String COLUMN_TURNS = "newestturns";
    public final static String COLUMN_PRODUCE = "yd_produce";
    /* ------ 参与供应商分录 ------ */
    public final static String ENTITY_SUPPLIES = "entryentity";
    public final static String COLUNM_SUPPLIER = "supplier";
    /* ------ 供应商状态分录 ------ */
    public final static String ENTITY_SUPPLIESSTATUS = "yd_suppliesstatus";
    public final static String COLUMN_SUPPLIESCODE = "yd_suppliescode";
    public final static String COLUMN_MATERIAL = "yd_material";
    public final static String COLUMN_STATUS = "yd_status";

    /**
     * 获取上次定价&年度平均
     * @author: hst
     * @createDate: 2022/09/07
     * @param materials
     * @return
     */
    public DynamicObjectCollection getLastPricingAndAverage (DynamicObjectCollection materials) {
        for (DynamicObject material : materials) {
            getLastPricingAndAverage(material);
        }
        return materials;
    }

    /**
     * 获取上次定价&年度平均
     * @author: hst
     * @createDate: 2022/09/07
     * @param material
     * @return
     */
    public void getLastPricingAndAverage (DynamicObject material) {
        String materialCode = material.getString(COLUMN_MASTERIAL + ".number");
        if (StringUtil.isNotBlank(materialCode)) {
            //获取该物料最新的一张比价单
            DataSet compares = QueryServiceHelper.queryDataSet(this.getClass().getName(), "sou_compare", "materialentry.taxprice",
                    new QFilter[]{new QFilter("materialentry.material.number", QFilter.equals, materialCode)}, "billdate desc");
            if (compares.hasNext()) {
                Row compare = compares.next();
                material.set(COLUMN_LASTPRICE, compare.get("materialentry.taxprice"));
            } else {
                material.set(COLUMN_LASTPRICE, new BigDecimal("0"));
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar=Calendar.getInstance();
            int lastYear = calendar.get(Calendar.YEAR) - 1;
            //获取某年第一天日期
            calendar.clear();
            calendar.set(Calendar.YEAR, lastYear);
            String firstDay = sdf.format(calendar.getTime());
            //获取某年最后一天日期
            calendar.clear();
            calendar.set(Calendar.YEAR, lastYear);
            calendar.roll(Calendar.DAY_OF_YEAR, -1);
            String endDay = sdf.format(calendar.getTime());
            compares = QueryServiceHelper.queryDataSet(this.getClass().getName(), "sou_compare", "materialentry.taxprice",
                    new QFilter[]{new QFilter("materialentry.material.number", QFilter.equals, materialCode),
                            new QFilter("billdate",QFilter.large_equals,firstDay),
                            new QFilter("billdate",QFilter.less_equals,endDay)}, "billdate desc");
            List<BigDecimal> prices = new ArrayList<>();
            for (Row row : compares) {
                prices.add((BigDecimal) row.get("materialentry.taxprice"));
            }
            if (prices.size() > 0) {
                BigDecimal average = prices.stream().reduce(BigDecimal.ZERO, BigDecimal::add).
                        divide(BigDecimal.valueOf(prices.size()), 2, BigDecimal.ROUND_HALF_UP);
                material.set(COLUNM_AVERAGEPRICE, average);
            } else {
                material.set(COLUNM_AVERAGEPRICE, new BigDecimal("0"));
            }
        }
    }
}
