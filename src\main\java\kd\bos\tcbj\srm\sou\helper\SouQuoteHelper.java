package kd.bos.tcbj.srm.sou.helper;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.AbstractFormDataModel;
import kd.bos.entity.datamodel.TableValueSetter;
import kd.bos.exception.KDBizException;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.tcbj.srm.utils.DynamicObjectUtil;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.sou.helper.SouQuoteHelper
 * @className SouQuoteHelper
 * @author: hst
 * @createDate: 2024/04/24
 * @version: v1.0
 */
public class SouQuoteHelper {

    public final static String ENTITY_MAIN = "sou_quote";
    public final static String FIELD_INQUIRYNO = "inquiryno";
    public final static String FIELD_SUPPLIER = "supplier";
    public final static String FIELD_TOTALIN = "totalinquiry";
    public final static String FIELD_DATEFROM = "datefrom";
    public final static String FIELD_DATETO = "dateto";
    public final static String FIELD_ENDDATE = "enddate";
    public final static String FIELD_PAYCOND = "paycond";
    public final static String FIELD_SETTLE = "settletype";
    public final static String FIELD_CURR = "curr";
    public final static String FIELD_INV = "invtype";
    public final static String FIELD_TAX = "taxtype";
    public final static String FIELD_LOCCURR = "loccurr";
    public final static String FIELD_EXCH = "exchtype";
    public final static String FIELD_EXCHRATE = "exchrate";
    public final static String FIELD_SUMQTY = "sumqty";
    public final static String FIELD_RATEDATE = "ratedate";
    public final static String FIELD_SUPCURR = "supcurrtype";
    public final static String FIELD_PERSON = "person";
    public final static String FIELD_BEHALF = "yd_behalf";
    public final static String FIELD_ORIGIN = "origin";
    public final static String FIELD_TURNS = "turnscount";
    public final static String FIELD_TURN = "turns";
    public final static String FIELD_STATUS = "billstatus";
    public final static String FIELD_ISAUTOFILL = "yd_isautofillprice";
    /* ------ 物料明细分录 ------ */
    public final static String ENTITY_MATERIAL = "materialentry";
    public final static String COLUNM_ENTRYID = "id";
    public final static String COLUMN_MATERIAL = "material";
    public final static String COLUMN_MATERIALNAME = "materialnametext";
    public final static String COLUMN_MATERIALDESC = "materialdesc";
    public final static String COLUMN_UNIT = "unit";
    public final static String COLUMN_QTY = "qty";
    public final static String COLUMN_QUOTECURR = "quotecurr";
    public final static String COLUMN_ORG = "entryrcvorg";
    public final static String COLUMN_STATUS = "entrystatus";
    public final static String COLUMN_QUOTURNS = "quoturns";
    public final static String COLUMN_TURNS = "newestturns";
    public final static String COLUMN_PRICE = "price";
    public final static String COLUMN_TAXRATEID = "taxrateid";
    public final static String COLUMN_EXRATE = "exrate";
    public final static String COLUMN_PRODUCE = "yd_produce";

    /**
     * 初始化代录报价数据
     * @author: hst
     * @createDate: 2022/09/08
     * @param qutoe
     * @param inquiryId
     */
    public void initCreateData(DynamicObject qutoe, Long inquiryId, AbstractFormDataModel model) {
        DynamicObject inquiry = BusinessDataServiceHelper.loadSingle(inquiryId, SouInquiryHelper.ENTITY_MAIN);
        DynamicObject curr = inquiry.getDynamicObject("curr");
        if (curr == null) {
            throw new KDBizException("结算币别不存在,无法报价");
        } else {
            //表头赋值
            List<String> fields = DynamicObjectUtil.getAllField(qutoe.getDynamicObjectType());
            Map result = DynamicObjectUtil.objectMapByQuery(inquiry);
            Iterator field = result.entrySet().iterator();
            while(field.hasNext()) {
                Map.Entry<String, Object> entry = (Map.Entry) field.next();
                String key = (String) entry.getKey();
                Object value = entry.getValue();
                //同名字段赋值
                if (fields.contains(key) && !"billstatus".equals(key)) {
                    model.setValue(key, value);
                }
                model.setValue(FIELD_INQUIRYNO, inquiry.get(SouInquiryHelper.FIELD_BILLNO));
                model.setValue(FIELD_TURNS, inquiry.get(SouInquiryHelper.FIELD_TURNS));
                model.setValue(FIELD_TURN,inquiry.get(SouInquiryHelper.FIELD_TURNS));
                model.setValue(FIELD_BEHALF, true);
                model.setValue(FIELD_SUPCURR, "1");
                model.setValue(FIELD_ORIGIN, "2");
            }
            //分录赋值
            DynamicObjectCollection inquiryMaterials = inquiry.getDynamicObjectCollection(ENTITY_MATERIAL);
            DynamicObjectCollection qutoeMaterials = qutoe.getDynamicObjectCollection(ENTITY_MATERIAL);
            qutoeMaterials.clear();
            int rowCount = 0;
            TableValueSetter vs = new TableValueSetter(new String[0]);
            fields = DynamicObjectUtil.getAllField(qutoeMaterials.getDynamicObjectType());
            if (inquiryMaterials.size() > 0) {
                for (DynamicObject material : inquiryMaterials) {
                    result = DynamicObjectUtil.objectMapByQuery(material);
                    field = result.entrySet().iterator();
                    //同名字段赋值
                    while(field.hasNext()) {
                        Map.Entry<String, Object> entry = (Map.Entry) field.next();
                        String key = (String) entry.getKey();
                        Object value = entry.getValue();
                        if (fields.contains(key)) {
                            vs.set(key, value, rowCount);
                        }
                    }
                    vs.set("quotecurr_id", curr.getLong("id"), rowCount);
                    vs.set("exrate", 1L, rowCount);
                    vs.set("srcbillid",inquiry.getPkValue(),rowCount);
                    vs.set("srcentryid",material.getPkValue(),rowCount);
                    //报价轮次/
//                    vs.set("quoturns", material.get(SouInquiryHelper.COLUMN_TURNS),rowCount);
                    rowCount ++ ;
                }
            }
            model.batchCreateNewEntryRow(ENTITY_MATERIAL, vs);
        }
    }
}
