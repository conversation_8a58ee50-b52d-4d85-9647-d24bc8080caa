package kd.bos.tcbj.srm.sou.helper;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @package: kd.bos.tcbj.srm.sou.helper.SuppliesAccountHelper
 * @className SuppliesAccountHelper
 * @author: hst
 * @createDate: 2022/09/08
 * @description: 供应商台账帮助类
 * @version: v1.0
 */
public class SuppliesAccountHelper {

    /* ------ 非生产供应商台账 ------ */
    public static final String ENTITY_UNPROSUPSUM = "yd_unprosupsumbill";
    public static final String FIELD_SUPNAME = "yd_supname";
    public static final String FIELD_MATNO = "yd_matno";
    public static final String FIELD_MATERIAL = "yd_material";
    public static final String FIELD_SUPPLIER = "yd_supplier";

    /**
     * 获取供应商台账，根据表单页面数据
     * @author: hst
     * @createDate: 2022/09/08
     * @param supplies
     * @param materials
     * @param destination
     * @param map
     */
    public void getSupplierAccount (DynamicObjectCollection supplies, DynamicObjectCollection materials,
                                    DynamicObjectCollection destination, Map<String,String> map) {
        destination.clear();
        Set<String> alreadyAdd = new HashSet<>();
        for (DynamicObject supply : supplies) {
            //如果已经处理了，就不再次查询
            if (supply.getDynamicObject(map.get("supply")) != null) {
                String supplyName = supply.getString(map.get("supply") + ".name");
                if (!alreadyAdd.contains(supplyName)) {
                    for (DynamicObject material : materials) {
                        if (material.getDynamicObject(map.get("material")) != null) {
                            String materialCode = material.getString(map.get("material") + ".number");
                            //查询生产商+物料维度的非生产供应商台账是否存在
                            boolean exit = QueryServiceHelper.exists(ENTITY_UNPROSUPSUM, new QFilter[]{
                                    new QFilter(FIELD_MATERIAL + ".number", QFilter.equals, materialCode),
                                    new QFilter(FIELD_SUPPLIER + ".name", QFilter.equals, supplyName)});
                            if (exit) {
                                DynamicObject supplyStatus = destination.addNew();
                                supplyStatus.set(SouInquiryHelper.COLUMN_SUPPLIESCODE, supply.get(map.get("supply")));
                                supplyStatus.set(SouInquiryHelper.COLUMN_MATERIAL, material.get(map.get("material")));
                                supplyStatus.set(SouInquiryHelper.COLUMN_STATUS, "1");
                            }
                        }
                    }
                    alreadyAdd.add(supplyName);
                }
            }
        }
    }
}
