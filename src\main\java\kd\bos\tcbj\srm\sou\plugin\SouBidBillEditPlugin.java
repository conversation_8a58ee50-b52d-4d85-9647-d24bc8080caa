package kd.bos.tcbj.srm.sou.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.form.container.Container;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.srm.sou.helper.SouInquiryHelper;
import kd.bos.tcbj.srm.sou.helper.SuppliesAccountHelper;

import java.util.EventObject;
import java.util.HashMap;
import java.util.Map;

/**
 * @package: kd.bos.tcbj.srm.sou.plugin.SouBidBillEditPlugin
 * @className SouBidBillEditPlugin
 * @author: hst
 * @createDate: 2022/09/08
 * @description: 发布竞价表单插件
 * @version: v1.0
 */
public class SouBidBillEditPlugin extends AbstractBillPlugIn {

    private final static String BTN_SUPPLYSTATUS = "yd_supplystatus";
    public final static String ENTITY_SUPPLIES = "supquoentry";
    public final static String ENTITY_MATERIAL = "materialentry";
    public final static String ENTITY_SUPPLIESSTATUS = "yd_suppliesstatus";
    /* ------ 物料明细分录 ------ */
    public final static String COLUMN_MASTERIAL = "material";
    /* ------ 参与供应商分录 ------*/
    public final static String COLUNM_SUPPLIER = "entrysupplier";

    /**
     * 注册监听
     * 监听工具栏
     * @author: hst
     * @createDate: 2022/09/08
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.addItemClickListeners("tbmain");
    }

    /**
     * 点击事件
     * @author: hst
     * @createDate: 2022/09/07
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case BTN_SUPPLYSTATUS : {
                //查看供应商状态
                Container container = this.getView().getControl("yd_supplied_status");
                container.setCollapse(false);
                //查找台账
                DynamicObjectCollection supplies = this.getModel().getEntryEntity(ENTITY_SUPPLIES);
                DynamicObjectCollection materials = this.getModel().getEntryEntity(ENTITY_MATERIAL);
                DynamicObjectCollection suppliesStatus = this.getModel().getEntryEntity(ENTITY_SUPPLIESSTATUS);
                Map<String,String> map = new HashMap<>();
                map.put("supply",COLUNM_SUPPLIER);
                map.put("material",COLUMN_MASTERIAL);
                new SuppliesAccountHelper().getSupplierAccount(supplies,materials,suppliesStatus,map);
                //审核状态的单据才执行保存操作
                String billStatus = this.getModel().getValue("billstatus").toString();
                if ("C".equals(billStatus)) {
                    SaveServiceHelper.save(new DynamicObject[]{this.getModel().getDataEntity()});
                }
                this.getView().showSuccessNotification("查询成功！");
                this.getView().updateView(SouInquiryHelper.ENTITY_SUPPLIESSTATUS);
                break;
            }
        }
    }
}
