package kd.bos.tcbj.srm.sou.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.bill.BillShowParameter;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.events.PropertyChangedArgs;
import kd.bos.form.CloseCallBack;
import kd.bos.form.ShowType;
import kd.bos.form.container.AdvContainer;
import kd.bos.form.container.Container;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.tcbj.im.util.StringUtils;
import kd.bos.tcbj.srm.sou.helper.SouInquiryHelper;
import kd.bos.tcbj.srm.sou.helper.SuppliesAccountHelper;
import kd.taxc.common.util.StringUtil;

import java.math.BigDecimal;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.sou.plugin.SouInquiryEditPlugin
 * @className SouInquiryEditPlugin
 * @author: hst
 * @createDate: 2022/09/07
 * @description: 询价单表单插件
 * @version: v1.0
 */
public class SouInquiryEditPlugin extends AbstractBillPlugIn {

    private final static String BTN_LASTPRICE = "yd_getlastpricing";
    private final static String BTN_SUPPLYSTATUS = "yd_supplystatus";
    private final static String BTN_RECORDBEFALF = "yd_recordbehalf";

    /**
     * 注册监听
     * 监听工具栏
     * @author: hst
     * @createDate: 2022/09/07
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        this.addItemClickListeners("tbmain");
    }

    /**
     * 点击事件
     * @author: hst
     * @createDate: 2022/09/07
     * @param evt
     */
    @Override
    public void itemClick(ItemClickEvent evt) {
        super.itemClick(evt);
        String key = evt.getItemKey();
        switch (key) {
            case BTN_LASTPRICE : {
                //获取上月定价&上一年度均价
                DynamicObjectCollection materials = this.getModel().getEntryEntity(SouInquiryHelper.ENTITY_MATERIAL);
                if (materials.size() > 0) {
                    this.getLastPricingAndAverage(materials);
                    this.getView().showSuccessNotification("查询成功！");
                } else {
                    this.getView().showTipNotification("请先录入物料明细！");
                }
                break;
            }
            case BTN_SUPPLYSTATUS : {
                //查看供应商状态
                Container container = this.getView().getControl("yd_supplied_status");
                container.setCollapse(false);
                //查找台账
                DynamicObjectCollection supplies = this.getModel().getEntryEntity(SouInquiryHelper.ENTITY_SUPPLIES);
                DynamicObjectCollection materials = this.getModel().getEntryEntity(SouInquiryHelper.ENTITY_MATERIAL);
                DynamicObjectCollection suppliesStatus = this.getModel().getEntryEntity(SouInquiryHelper.ENTITY_SUPPLIESSTATUS);
                Map<String,String> map = new HashMap<>();
                map.put("supply",SouInquiryHelper.COLUNM_SUPPLIER);
                map.put("material",SouInquiryHelper.COLUMN_MASTERIAL);
                new SuppliesAccountHelper().getSupplierAccount(supplies,materials,suppliesStatus,map);
                //审核状态的单据才执行保存操作
                String billStatus = this.getModel().getValue("billstatus").toString();
                if ("C".equals(billStatus)) {
                    SaveServiceHelper.save(new DynamicObject[]{this.getModel().getDataEntity()});
                }
                this.getView().showSuccessNotification("查询成功！");
                this.getView().updateView(SouInquiryHelper.ENTITY_SUPPLIESSTATUS);
                break;
            }
            case BTN_RECORDBEFALF : {
                //代录报价
                //截止时间控制 - hst 2022/11/1
                Date endDate = (Date) this.getModel().getValue("enddate");
                if (new Date().compareTo(endDate) > 0) {
                    this.getView().showTipNotification("截止报价时间已过");
                    break;
                }
                //项目状态控制
                Object bizStatus = this.getModel().getValue("bizstatus");
                if (bizStatus != null && StringUtil.equals(bizStatus.toString(),"A")) {
                    this.ejectQuoteIframe();
                    break;
                }
                this.getView().showTipNotification("项目状态不是待报价，不允许报价");
                break;
            }
        }
    }

    /**
     * 值变更事件
     * @param e
     * @author: hst
     * @createDate: 2022/11/1
     */
    @Override
    public void propertyChanged(PropertyChangedArgs e) {
        String propertyName = e.getProperty().getName();
        if ("material".equals(propertyName)) {
            //获取上月定价&上一年度均价
            DynamicObjectCollection materials = this.getModel().getEntryEntity(SouInquiryHelper.ENTITY_MATERIAL);
            this.getLastPricingAndAverage(materials);
        }
    }

    /**
     * 弹出报价单新增页面
     * @author: hst
     * @createDate: 2022/09/08
     */
    public void ejectQuoteIframe() {
        DynamicObject inquiry = this.getModel().getDataEntity();
        BillShowParameter billShowParameter = new BillShowParameter();
        billShowParameter.setFormId("sou_quote");
        billShowParameter.setCaption("报价单");
        billShowParameter.setCustomParam("inquiryId",inquiry.get("id"));
        String supscope = this.getModel().getValue(SouInquiryHelper.FIELD_SUPSCOPE).toString();
        if (StringUtil.equals(supscope,"2")) {
            StringBuffer supplier = new StringBuffer();
            for (DynamicObject supply : this.getModel().getEntryEntity(SouInquiryHelper.ENTITY_SUPPLIES)) {
                supplier.append(supply.getDynamicObject(SouInquiryHelper.COLUNM_SUPPLIER).getString("id") + ",");
            }
            billShowParameter.setCustomParam("supply",supplier.toString());
        }
        billShowParameter.getOpenStyle().setShowType(ShowType.MainNewTabPage);
        billShowParameter.setCloseCallBack(new CloseCallBack(this,"PROXY_QUOTA"));
        this.getView().showForm(billShowParameter);
    }

    /**
     * 获取上月定价&上一年度均价
     * @author: hst
     * @createDate: 2022/11/1
     */
    public void getLastPricingAndAverage(DynamicObjectCollection materials) {
        //获取上月定价&上一年度均价
        if (materials.size() > 0) {
            new SouInquiryHelper().getLastPricingAndAverage(materials);
            //判断单据是否审核状态
            DynamicObject bill = this.getModel().getDataEntity();
            String status = bill.getString("billstatus");
            if (StringUtils.isNotBlank(status) && "C".equals(status)) {
                SaveServiceHelper.save(new DynamicObject[]{bill});
            }
            this.getView().updateView(SouInquiryHelper.ENTITY_MATERIAL);
        }
    }

    /**
     * 子页面回调事件
     * @param closedCallBackEvent
     * @author: hst
     * @createDate: 2024/04/24
     */
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        String key = closedCallBackEvent.getActionId();
        switch (key) {
            case "PROXY_QUOTA" : {
                this.getView().invokeOperation("refresh");
                break;
            }
        }
    }
}
