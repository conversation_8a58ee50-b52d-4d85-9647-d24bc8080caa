package kd.bos.tcbj.srm.sou.plugin;

import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.datamodel.AbstractFormDataModel;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.form.FormShowParameter;
import kd.bos.form.container.Container;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.field.BasedataEdit;
import kd.bos.list.ListShowParameter;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.tcbj.srm.quo.helper.QuoQuoteHelper;
import kd.bos.tcbj.srm.sou.helper.SouQuoteHelper;
import kd.scm.common.constant.BillAssistConstant;

import java.util.EventObject;
import java.util.Objects;

/**
 * @package: kd.bos.tcbj.srm.sou.plugin.SouQuoteEditPlugin
 * @className SouQuoteEditPlugin
 * @author: hst
 * @createDate: 2024/04/24
 * @description:  报价单表单插件
 * @version: v1.0
 */
public class SouQuoteEditPlugin extends AbstractBillPlugIn {

    private final static String BTN_SUBMIT = "bar_submit";
    private final static Log logger = LogFactory.getLog("SouQuoteEditPlugin");

    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        Container container = this.getView().getControl("tbmain");
        container.addItemClickListener(this);
        this.addF7SelectorListener();
    }

    /**
     * 字段绑定数据后触发
     * @author: hst
     * @createDate: 2022/09/08
     * @param e
     */
    @Override
    public void afterBindData(EventObject e) {
        FormShowParameter showParameter = this.getView().getFormShowParameter();
        if(showParameter.getCustomParam("inquiryId")!=null
                && !((boolean) this.getModel().getValue("yd_behalf"))) {
            Long inquiryId = showParameter.getCustomParam("inquiryId");
            DynamicObject qutoe = this.getModel().getDataEntity();
            //初始化赋值
            new SouQuoteHelper().initCreateData(qutoe,inquiryId,(AbstractFormDataModel)this.getModel());
            showParameter.setCustomParam("inquiryId",null);
            this.getView().updateView("pl_baseinfo");
            this.getView().updateView("pl_bizinfo");
            this.getView().updateView("materialentry");
            this.getView().updateView("titlepanelflex");
            this.getView().updateView("tbmain");
        }
    }

    /**
     * F7监听注册
     * @author: hst
     * @createDate: 2022/09/13
     */
    public void addF7SelectorListener() {
        BasedataEdit supplyEdit = this.getView().getControl(QuoQuoteHelper.FIELD_SUPPLIER);
        //弹窗前过滤
        supplyEdit.addBeforeF7SelectListener((evt) -> {
            ListShowParameter showParameter = (ListShowParameter) evt.getFormShowParameter();
            showParameter.setCustomParam("groupStandard", BillAssistConstant.GROUP_STANDARD_ID);
            showParameter.setCustomParam("setSupStatusShowAll", "true");
            DynamicObject newObject = this.getModel().getDataEntity(true);
//            showParameter.setCloseCallBack(new CloseCallBack(this, "supplier"));
        });
        supplyEdit.addAfterF7SelectListener((evt) -> {
            if ((boolean) this.getModel().getValue(QuoQuoteHelper.FIELD_BEHALF)) {
                String supply = this.getView().getFormShowParameter().getCustomParam("supply");
                if (supply != null) {
                    DynamicObject supplier = this.getModel().getDataEntity().getDynamicObject(QuoQuoteHelper.FIELD_SUPPLIER);
                    if (Objects.nonNull(supplier)) {
                        String supplyId = supplier.getString("id");
                        if (supply.indexOf(supplyId) == -1) {
                            this.getView().showTipNotification("该供应商非指定供应商，请重新选择！");
                            this.getModel().getDataEntity().set(QuoQuoteHelper.FIELD_SUPPLIER, null);
                            this.getView().updateView(QuoQuoteHelper.FIELD_SUPPLIER);
                        }
                    }
                }
            }
        });
    }
    
    @Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
    	super.closedCallBack(closedCallBackEvent);
    	
    	String actionId = closedCallBackEvent.getActionId();
		Object returnData = closedCallBackEvent.getReturnData();
		
		if (actionId.equals("supplier")) {
			ListSelectedRowCollection supplierRows = (ListSelectedRowCollection) returnData;
			if (supplierRows != null) {
				this.getModel().setValue(QuoQuoteHelper.FIELD_SUPPLIER, supplierRows.getPrimaryKeyValues()[0]);
			}
		}
    }
}
