package kd.bos.tcbj.srm.sou.print;

import kd.bos.algo.DataSet;
import kd.bos.algo.Field;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.query.QFilter;
import kd.bos.print.core.data.DataRowSet;
import kd.bos.print.core.data.field.DateTimeField;
import kd.bos.print.core.data.field.DecimalField;
import kd.bos.print.core.data.field.TextField;
import kd.bos.print.core.plugin.AbstractPrintPlugin;
import kd.bos.print.core.plugin.event.CustomDataLoadEvent;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.tcbj.srm.utils.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.sou.print.SouBidBillPrintPlugin
 * @className SouBidBillPrintPlugin
 * @author: hst
 * @createDate: 2022/12/30
 * @description: 竞价定标打印插件
 * @version: v1.0
 */
public class SouBidBillPrintPlugin extends AbstractPrintPlugin {

    private final Map<String,String> filedType = new HashMap<String,String>(){
        {
            put("quotematerial", "String");
            put("quotematerialnametext", "String");
            put("quotesupplier", "String");
            put("quotesupname", "String");
            put("quotedate", "Date");
            put("quocurrency", "String");
            put("quoteamount", "BigDecimal");
            put("reduceamt", "BigDecimal");
            put("quotebizpartner", "String");
            put("quotetaxprice", "BigDecimal");
            put("quoteprice", "BigDecimal");
        }
    };

    @Override
    public void loadCustomData(CustomDataLoadEvent evt) {
        super.loadCustomData(evt);
        String name = evt.getDataSource().getDsName();
        if ("wintenderentry".equals(name)) {
            String pkId = evt.getDataSource().getPkId().toString();
            String formId = evt.getDataSource().getFormId();
            DynamicObject bill = BusinessDataServiceHelper.loadSingle(pkId,formId);
            // 竞价模式
            String type = bill.getString("quotemode");
            // update by hst 2023/01/11 获取中标供应商
            Map<String,String> supplier = new HashMap<>();
            DynamicObjectCollection entries = bill.getDynamicObjectCollection("entryentity");
            for (DynamicObject entry : entries) {
                if (Objects.nonNull(entry.getDynamicObject("winsupplier")) && Objects.nonNull(entry.getDynamicObject("material"))) {
                    String supplierId = entry.getDynamicObject("winsupplier").getString("id");
                    String materialNum = entry.getDynamicObject("material").getString("number");
                    supplier.put(supplierId,supplier.containsKey(supplierId) ? supplier.get(supplierId) + "、" + materialNum : materialNum);
                }
            }
            List<DataRowSet> dataRowSets = evt.getCustomDataRows();
            // 查询中标的供应商
            DataSet dataSet = QueryServiceHelper.queryDataSet(this.getClass().getName(),"sou_bidbillcfm","entryentity2.quotematerial.number quotematerial," +
                    "entryentity2.quotematerial.name quotematerialnametext,entryentity2.quotesupplier.number quotesupplier,entryentity2.quotesupplier.name quotesupname," +
                    "entryentity2.quotedate quotedate,entryentity2.quocurrency.name quocurrency,entryentity2.quoteamount quoteamount,entryentity2.reduceamt reduceamt," +
                    "entryentity2.quotesupplier.bizpartner.name quotebizpartner,entryentity2.quotetaxprice quotetaxprice,entryentity2.quoteprice quoteprice," +
                    "entryentity2.quotesupplier.id quotesupplierId", QFilter.of("id = ?",pkId).toArray(),null);
            dataSet = dataSet.orderBy(new String[]{"quoteamount asc"});
            List<Field> fields = Arrays.asList(dataSet.copy().getRowMeta().getFields());
            List<String> materials = new ArrayList<>();
            while (dataSet.hasNext()) {
                Row row = dataSet.next();
                DataRowSet dataRowSet = new DataRowSet();
                // 如果是总额竞价，只需取所有记录中报价最低的一条数据
                if ("1".equals(type)) {
                    // update by hst 2023/01/11 按中标供应商获取报价
                    if (StringUtils.isNotBlank(row.getString("quotesupplierId")) && supplier.containsKey(row.getString("quotesupplierId"))) {
                        fields.stream().forEach(field -> {
                            setPrintField(dataRowSet, field.getName(), StringUtils.isNotBlank(row.getString(field.getName())) ? row.getString(field.getName()) : "");
                        });
                        dataRowSets.add(dataRowSet);
                        break;
                    }
                } else {
                    // 如果是行项目竞价，需要取每种物料中报价最低的数据
                    String matNumber = row.getString("quotematerial");
                    if (StringUtils.isNotBlank(matNumber) && materials.indexOf(matNumber) == -1) {
                        String quoteSupplierId = row.getString("quotesupplierId");
                        // update by hst 2023/01/11 按中标供应商 + 物料获取报价
                        if (StringUtils.isNotBlank(quoteSupplierId) && supplier.containsKey(quoteSupplierId) && supplier.get(quoteSupplierId).indexOf(matNumber) >= 0) {
                            materials.add(matNumber);
                            fields.stream().forEach(field -> {
                                setPrintField(dataRowSet, field.getName(), StringUtils.isNotBlank(row.getString(field.getName())) ? row.getString(field.getName()) : "");
                            });
                            dataRowSets.add(dataRowSet);
                        }
                    }
                }
            }
        }
    }

    /**
     * 为单元格设置不同类型的值
     * @author: hst
     * @createDate: 2022/12/30
     * @param dataRowSet
     * @param key
     * @param value
     */
    public void setPrintField(DataRowSet dataRowSet, String key, Object value) {
        if (filedType.containsKey(key)) {
            switch (filedType.get(key)) {
                case "String" : {
                    dataRowSet.put(key,new TextField(value.toString()));
                    break;
                }
                case "Date" : {
                    Timestamp ts = new Timestamp(System.currentTimeMillis());
                    try {
                        ts = Timestamp.valueOf(value.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    dataRowSet.put(key,new DateTimeField(ts));
                    break;
                }
                case "BigDecimal" : {
                    dataRowSet.put(key,new DecimalField(new BigDecimal(value.toString()).setScale(2,BigDecimal.ROUND_HALF_UP)));
                    break;
                }
            }
        }
    }
}
