package kd.bos.tcbj.srm.sou.print;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.orm.query.QFilter;
import kd.bos.print.core.data.DataRowSet;
import kd.bos.print.core.data.datasource.MainDataSource;
import kd.bos.print.core.data.datasource.PrtDataSource;
import kd.bos.print.core.data.field.TextField;
import kd.bos.print.core.plugin.AbstractPrintPlugin;
import kd.bos.print.core.plugin.event.AfterLoadDataEvent;
import kd.bos.print.core.plugin.event.BeforeOutputGridEvent;
import kd.bos.print.core.plugin.event.BeforeOutputWidgetEvent;
import kd.bos.print.core.plugin.event.bo.PWGridCellBo;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;

import java.math.BigDecimal;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.sou.print.SouComparePrintPlugin
 * @className SouComparePrintPlugin
 * @author: hst
 * @createDate: 2022/12/30
 * @description: 竞价定标打印插件
 * @version: v1.0
 */
public class SouComparePrintPlugin extends AbstractPrintPlugin {

    //需动态添加的列的标题
    private List<Map<String,String>> addCols = new ArrayList<>();

    private DynamicObjectCollection quoteCollections = null;

    @Override
    public void afterLoadData(AfterLoadDataEvent evt) {
        PrtDataSource dataSource = evt.getDataSource();
        List<DataRowSet> rowSets = evt.getDataRowSets();
        String formId = ((MainDataSource) dataSource).getFormId();
        int index = 0;
        for (DataRowSet rowSet : rowSets) {
            Map<String,String> addCol = new HashMap<>();
            String pkId = rowSet.getField("id").toString();
            DynamicObject bill = BusinessDataServiceHelper.loadSingle(formId, "inquiryno", QFilter.of("id = ?",pkId).toArray());
            if (Objects.nonNull(bill)) {
                String inquiryNo = bill.getString("inquiryno");
                quoteCollections = QueryServiceHelper.query("quo_quote", "materialentry.material.number matNumber,supplier.name," +
                        "materialentry.taxprice taxprice", new QFilter[]{(new QFilter("inquiryno", "=", inquiryNo)).and(new QFilter("billstatus", "=", "C")).and(new QFilter("materialentry.entrystatus", "=", "A"))});
                String col;
                for (DynamicObject qutoe : quoteCollections) {
                    if (!addCol.containsKey(qutoe.getString("supplier.name"))) {
                        index += 1;
                        col = "quote" + index;
                        addCol.put(qutoe.getString("supplier.name"), col);
                    } else {
                        col = addCol.get(qutoe.getString("supplier.name"));
                    }
                    for (DataRowSet row : rowSet.getCollectionField("materialentry").getValue()) {
                        String matNumber = row.getField("material.number").toString();
                        if (matNumber.equals(qutoe.getString("matNumber"))) {
                            // 动态为数据源赋值
                            row.put(col, new TextField("￥" + qutoe.getBigDecimal("taxprice").setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                        }
                    }
                }
            }
            addCols.add(addCol);
        }
    }

    public void beforeOutputWidget(BeforeOutputWidgetEvent evt) {
        if (addCols.size() > 0) {
            if (evt instanceof BeforeOutputGridEvent) {
                BeforeOutputGridEvent gridEvent = (BeforeOutputGridEvent) evt;
                if ("DataGrid1".equals(evt.getWidgetKey())) {
                    // 指定插入列位置
                    // update by hst 2023/01/11 最后一列插入
                    int index = gridEvent.getRowCell(0).size();
                    if (addCols.size() > 0) {
                        // 计算剩余宽度
                        int avgWidth = (gridEvent.getRightSpace()) / addCols.get(0).size();
                        for (Map.Entry<String, String> addCol : addCols.get(0).entrySet()) {
                            final List<PWGridCellBo> cellBos = gridEvent.insertColumn(index++, avgWidth);
                            // 获取新增列的标题行对应单元格，赋值为列标题名称
                            cellBos.get(0).setCellValue(addCol.getKey());
                            // 获取新增列数据行对应的单元格，并绑定afterLoadData中添加的字段
                            cellBos.get(1).setCellValue("sou_compare.materialentry", addCol.getValue());
                        }
                        addCols.remove(0);
                    }
                }
            }
        }
    }

}
