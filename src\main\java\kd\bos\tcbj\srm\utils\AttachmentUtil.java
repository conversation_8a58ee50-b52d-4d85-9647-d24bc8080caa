package kd.bos.tcbj.srm.utils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.fileservice.FileItem;
import kd.bos.fileservice.FileService;
import kd.bos.fileservice.FileServiceFactory;
import kd.bos.servicehelper.AttachmentServiceHelper;
import kd.bos.tcbj.srm.admittance.helper.AdminAttachHelper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 附件工具类
 *
 * <AUTHOR> yzf
 * @createDate : 2021-06-15
 */
public class AttachmentUtil {
    /**
     * 上传单据附件
     *
     * @param formId    单据标识
     * @param pkId      单据主键Id
     * @param attachKey 单据附件标识
     * @param path      文件相对路径
     * @param fileName  文件名
     * @param stream    文件流
     * @param fileSize  文件大小
     */
    public static void uploadBillAttachment(String formId, String pkId, String attachKey, String path, String fileName, InputStream stream, int fileSize) {
        // 构造一个map，记录即将上传的文件的一些信息
        Map uploadFileMap = new HashMap();
        uploadFileMap.put("entityNum", formId);
        uploadFileMap.put("billPkId", pkId);
        uploadFileMap.put("lastModified", new Date().getTime());
        List<Map<String, Object>> attachments = new ArrayList<>();
        attachments.add(uploadFileMap);

        // 上传附件并关联
        FileService fs = FileServiceFactory.getAttachmentFileService();
        FileItem fi = new FileItem(fileName, path, stream);
        fi.setCreateNewFileWhenExists(true);
        path = fs.upload(fi);

        // 文件上传成功之后，把文件信息存入map表中
        uploadFileMap.put("name", fileName);
        uploadFileMap.put("url", path);
        uploadFileMap.put("size", fileSize);
        uploadFileMap.put("status", "success");
        AttachmentServiceHelper.upload(formId, pkId, attachKey, attachments);
    }

    /**
     * 文件打包成压缩包
     * @return 组装后的zip压缩包字节流数据
     * @author: hst
     * @createDate: 2024/10/22
     */
    public static byte[] zipFiles(List<DynamicObject> attachments) {
        Map<String, Integer> fileNames = new HashMap();
        ByteArrayOutputStream bos = new ByteArrayOutputStream();

        try (ZipOutputStream out = new ZipOutputStream(bos);) {
            for (DynamicObject attachment : attachments) {
                // 文件路径
                String url = attachment.getString("url");
                String path = AdminAttachHelper.getPathFromDownloadUrl(url);
                String fileName = attachment.getString("name");
                fileName = fileName.replaceAll("[&#$%^!/:<>\"|*?]", "_").replace("\\","_");

                if (fileNames.containsKey(fileName)) {
                    int index = fileNames.get(fileName);
                    fileNames.put(fileName, index + 1);
                    fileName = fileName.substring(0, fileName.lastIndexOf(".")) + "_" + (index + 1)
                            + fileName.substring(fileName.lastIndexOf("."), fileName.length());
                } else {
                    fileNames.put(fileName, 0);
                }

                InputStream inputStream = FileServiceFactory.getAttachmentFileService().getInputStream(path);
                //创建要添入的ZIP 文件条目对象
                ZipEntry entry = new ZipEntry(fileName);
                //设置条目的压缩方式
                entry.setMethod(ZipEntry.DEFLATED);
                //写入ZIP 文件条目并将流定位到条目数据的开始处。
                out.putNextEntry(entry);
                out.write(getFileByte(inputStream));
                //关闭当前 ZIP 条目并定位流以写入下一个条目
                out.closeEntry();
                out.flush();
            }
            // 要关闭out，bos才能拿到流
            out.close();
            final byte[] bytes = bos.toByteArray();
            bos.close();
            return bytes;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取文件字节流
     * @return
     * @author: hst
     * @createDate: 2024/10/22
     */
    private static byte[] getFileByte (InputStream inputStream) throws IOException{
        //定义数组
        byte[] buffer = new byte[1024];
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int len;
        while ((len = inputStream.read(buffer)) != -1) {
            baos.write(buffer, 0, len);
        }
        byte[] bytes = baos.toByteArray();
        inputStream.close();
        baos.close();
        return bytes;
    }
}