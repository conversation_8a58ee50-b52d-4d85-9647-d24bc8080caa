package kd.bos.tcbj.srm.utils;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.EntityMetadataCache;
import kd.bos.entity.botp.CRCondition;
import kd.bos.entity.botp.ConvertRuleCache;
import kd.bos.entity.botp.ConvertRuleElement;
import kd.bos.entity.botp.runtime.ConvertOperationResult;
import kd.bos.entity.botp.runtime.PushArgs;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.botp.ConvertServiceHelper;
import kd.bos.tcbj.im.vo.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 单据转换辅助类
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-2-7
 */
public class ConvertHelper {

    /**
     * 获取源单转换成目标单据所有的规则
     *
     * @param srcEntityType  源单单据标识
     * @param destEntityType 目标单据标识
     * @return 所有源单转换成目标单据所有的规则
     * <AUTHOR>
     * @date 2022-2-7
     */
    public static List<ConvertRuleElement> loadRules(String srcEntityType, String destEntityType) {
        return ConvertRuleCache.loadRules(srcEntityType, destEntityType);
    }

    /**
     * 下推单据转换目标单据
     *
     * @param srcEntityType  源单标识
     * @param destEntityType 目标单标识
     * @param srcRows        设置需要下推的源单及分录内码
     * @return 单据转换结果
     * <AUTHOR>
     * @date 2022-2-7
     */
    public static List<R> push(String srcEntityType, String destEntityType, List<ListSelectedRow> srcRows) {
        List<R> resultList = new ArrayList<>();
        List<ConvertRuleElement> rules = loadRules(srcEntityType, destEntityType);
        for (ConvertRuleElement rule : rules) {
            // 生成下推参数PushArgs
            PushArgs pushArgs = new PushArgs();
            pushArgs.setSourceEntityNumber(srcEntityType);     // 必选，源单标识
            pushArgs.setTargetEntityNumber(destEntityType);     // 必选，目标单标识
            pushArgs.setHasRight(true);        // 可选，传入true，不检查目标单新增权
            pushArgs.setRuleId(rule.getId());             // 可选，传入本次下推使用的转换规则id；传入空值，由系统自动寻找合适的转换规则
            pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告
            pushArgs.setSelectedRows(srcRows);     // 必选，设置需要下推的源单及分录内码
            resultList.add(pushAndSave(pushArgs));
        }
        return resultList;
    }

    /**
     * 下推单据转换目标单据
     *
     * @param srcEntityType  源单标识
     * @param destEntityType 目标单标识
     * @param srcRows        设置需要下推的源单及分录内码
     * @param ruleId         是否输出详细错误报告
     * @return 单据转换结果
     * <AUTHOR>
     * @date 2022-2-7
     */
    public static R push(String srcEntityType, String destEntityType, List<ListSelectedRow> srcRows, String ruleId) {
        // 生成下推参数PushArgs
        PushArgs pushArgs = new PushArgs();
        pushArgs.setSourceEntityNumber(srcEntityType);     // 必选，源单标识
        pushArgs.setTargetEntityNumber(destEntityType);     // 必选，目标单标识
        pushArgs.setHasRight(true);        // 可选，传入true，不检查目标单新增权
        pushArgs.setRuleId(ruleId);             // 可选，传入本次下推使用的转换规则id；传入空值，由系统自动寻找合适的转换规则
        pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告
        pushArgs.setSelectedRows(srcRows);     // 必选，设置需要下推的源单及分录内码
        return pushAndSave(pushArgs);
    }

    /**
     * 下推单据转换目标单据
     *
     * @param srcEntityType  源单标识
     * @param destEntityType 目标单标识
     * @param srcRow         设置需要下推的源单及分录内码
     * @param ruleId         是否输出详细错误报告
     * @return 单据转换结果
     * <AUTHOR>
     * @date 2022-2-7
     */
    public static R push(String srcEntityType, String destEntityType, ListSelectedRow srcRow, String ruleId) {
        // 生成下推参数PushArgs
        PushArgs pushArgs = new PushArgs();
        pushArgs.setSourceEntityNumber(srcEntityType);     // 必选，源单标识
        pushArgs.setTargetEntityNumber(destEntityType);     // 必选，目标单标识
        pushArgs.setHasRight(true);        // 可选，传入true，不检查目标单新增权
        pushArgs.setRuleId(ruleId);             // 可选，传入本次下推使用的转换规则id；传入空值，由系统自动寻找合适的转换规则
        pushArgs.setBuildConvReport(true);  // 是否输出详细错误报告
        List<ListSelectedRow> srcRows = new ArrayList<>();
        srcRows.add(srcRow);
        pushArgs.setSelectedRows(srcRows);     // 必选，设置需要下推的源单及分录内码
        return pushAndSave(pushArgs);
    }

    /**
     * 下推单据转换目标单据
     *
     * @param srcEntityType  源单标识
     * @param destEntityType 目标单标识
     * @param srcRows        设置需要下推的源单及分录内码
     * @param isHasRight     可选，不检查目标单新增权
     * @param appId          可选，传入目标单验权使用的应用编码
     * @param defOrgId       可选，传入目标单主组织默认值
     * @param ruleId         是否输出详细错误报告
     * @param isShowError    可选，传入本次下推使用的转换规则id；传入空值，由系统自动寻找合适的转换规则
     * @return 单据转换结果
     * <AUTHOR>
     * @date 2022-2-7
     */
    public static R push(String srcEntityType, String destEntityType, List<ListSelectedRow> srcRows, boolean isHasRight, String appId, Long defOrgId, String ruleId, boolean isShowError) {
        // 生成下推参数PushArgs
        PushArgs pushArgs = new PushArgs();
        pushArgs.setSourceEntityNumber(srcEntityType);     // 必选，源单标识
        pushArgs.setTargetEntityNumber(destEntityType);     // 必选，目标单标识
        pushArgs.setHasRight(isHasRight);        // 可选，传入true，不检查目标单新增权
        pushArgs.setAppId(appId);              // 可选，传入目标单验权使用的应用编码
        pushArgs.setDefOrgId(defOrgId);           // 可选，传入目标单主组织默认值
        pushArgs.setRuleId(ruleId);             // 可选，传入本次下推使用的转换规则id；传入空值，由系统自动寻找合适的转换规则
        pushArgs.setBuildConvReport(isShowError);  // 是否输出详细错误报告
        pushArgs.setSelectedRows(srcRows);     // 必选，设置需要下推的源单及分录内码
        return pushAndSave(pushArgs);
    }

    /**
     * 数据推送
     *
     * @param pushArgs 构建推送的数据
     * @return 返回推送的结果
     * <AUTHOR>
     * @date 2022-2-7
     */
    public static R push(PushArgs pushArgs) {
        // 调用下推引擎，下推目标单
        ConvertOperationResult pushResult = ConvertServiceHelper.push(pushArgs);
        // 显示下推结果，自动打开目标单
        if (!pushResult.isSuccess() || pushResult.getCachePageIds().isEmpty()) {
            // 下推失败，显示错误报告
            String failMessage = pushResult.getMessage();
            return R.error(failMessage).put("result", pushResult);
        }
        // 推搡成功
        return R.ok().put("result", pushResult);
    }

    /**
     * 数据推送
     *
     * @param pushArgs 构建推送的数据
     * @return 返回推送的结果
     * <AUTHOR>
     * @date 2022-2-7
     */
    public static R pushAndSave(PushArgs pushArgs) {
        // 调用下推引擎，下推目标单
        ConvertOperationResult pushResult = ConvertServiceHelper.pushAndSave(pushArgs);
        // 显示下推结果，自动打开目标单
        if (!pushResult.isSuccess() || pushResult.getTargetBillIds().isEmpty()) {
            // 下推失败，显示错误报告
            return R.error("失败").put("result", pushResult);
        }
        // 推搡成功
        return R.ok().put("result", pushResult);
    }

    /**
     * @description（方法描述）:获取满足下推规则的下游单据标示
     * @author（创建人）: cary
     * @createDate（创建时间）: 2024/10/17
     * @updateUser（修改人）:
     * @updateDate（修改时间）:
     * @updateRemark（修改备注）:
     * @param
     * @return java.lang.String
     */
    public static String getTargetEntityName(String srcEntityName,Object billId)
    {
        DynamicObjectCollection rs= QueryServiceHelper.query("botp_crlist","targetentitynumber.number",
                QFilter.of("enabled='1' and sourceentitynumber.number=?",new Object[]{srcEntityName}).toArray());
        for(DynamicObject row:rs)
        {
            String tarEntityName=row.getString("targetentitynumber.number");
            List<ConvertRuleElement> list= loadRules(srcEntityName,tarEntityName);
            if(list!=null&&list.size()>0)
            {
                ConvertRuleElement rule=list.get(0);
                CRCondition cd=rule.getRunCondition();
                String filter= cd.buildFullFormula(EntityMetadataCache.getDataEntityType(srcEntityName));
                QFilter f=QFilter.of(filter,new Object[0]);
                f.and(new QFilter("id","=",billId));
               if(QueryServiceHelper.exists(srcEntityName,f.toArray()))
               {
                   return tarEntityName;
               }
            }
        }
        return null;
    }


    public static ConvertOperationResult getPushResult(R r) {
        return (ConvertOperationResult) r.get("result");
    }
}
