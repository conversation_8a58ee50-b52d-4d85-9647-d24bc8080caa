package kd.bos.tcbj.srm.utils;

import kd.bos.dataentity.entity.LocaleString;
import kd.bos.form.IFormView;
import kd.bos.form.field.ComboEdit;
import kd.bos.form.field.ComboItem;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @package: kd.bos.tcbj.srm.utils.EnumFieldUtils
 * @className EnumFieldUtils
 * @author: hst
 * @createDate: 2024/07/22
 * @description: 下拉枚举控件工具类
 * @version: v1.0
 */
public class EnumFieldUtils {

    /**
     * 初始化下拉控件枚举
     * @param enumInfos
     * @author: hst
     * @createDate: 2024/07/11
     */
    public static void initEnumControl (IFormView iFormView, Map<String, Set<String>> enumInfos) {
        for (Map.Entry<String, Set<String>> enumInfo : enumInfos.entrySet()) {
            String field = enumInfo.getKey();
            Set<String> values = enumInfo.getValue();

            ComboEdit comboEdit = iFormView.getControl(field);
            List<ComboItem> comboItems = new ArrayList<>();
            for (String value : values) {
                String name = value.split("&")[0];
                String num = value.split("&")[1];
                String isShow = value.split("&")[2];

                ComboItem comboItem = new ComboItem();
                comboItem.setCaption(new LocaleString(name));
                comboItem.setValue(num);
                comboItem.setItemVisible("true".equals(isShow) ? true : false);
                comboItems.add(comboItem);
            }
            // 排序
            comboItems = comboItems.stream().sorted(Comparator.comparing(comboItem -> comboItem.getValue())).collect(Collectors.toList());
            comboEdit.setComboItems(comboItems);
        }
    }
}
