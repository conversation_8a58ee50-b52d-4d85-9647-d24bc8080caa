package kd.bos.tcbj.srm.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.dataentity.metadata.IDataEntityProperty;
import kd.bos.dataentity.metadata.clr.DataEntityPropertyCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.dataentity.utils.StringUtils;
import kd.bos.entity.RefEntityType;
import kd.bos.entity.ValueMapItem;
import kd.bos.entity.datamodel.IDataModel;
import kd.bos.entity.datamodel.KeyValue;
import kd.bos.entity.datamodel.NumberPrecision;
import kd.bos.entity.property.*;
import kd.bos.exception.KDBizException;
import kd.bos.form.IFormView;
import kd.bos.form.control.Control;
import kd.bos.form.control.EntryData;
import kd.bos.form.control.EntryGrid;
import kd.bos.form.field.BasedataPropEdit;
import kd.bos.form.field.DateRangeEdit;
import kd.bos.form.field.TimeRangeEdit;
import kd.bos.servicehelper.parameter.SystemParamServiceHelper;
import kd.bos.servicehelper.user.UserConfigServiceHelper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.utils.ExportEntryUtils
 * @className ExportEntryUtils
 * @author: hst
 * @createDate: 2023/03/03
 * @description: 导出分录工具类
 * @version: v1.0
 */
public class ExportEntryUtils {

    public static List<String> startingExport(String entryKey, String settingKey, IFormView view) {
        final IDataModel model = view.getModel();
        final EntryGrid grid = (EntryGrid) view.getControl(entryKey);
        List<String> urls = new ArrayList<>();
        try {
            final Map<String, KeyValue> captions = prepareCaptions(view, model, entryKey, settingKey);
            LocaleString caption = view.getModel().getProperty(entryKey).getDisplayName();
            final String entryName = caption != null && StringUtils.isNotBlank(caption.toString()) ? caption.toString() : entryKey;
            final int total = getTotal(view, entryKey);
            final int[] progress = new int[]{0, total};
            List<String> reqFileds = getEntryMustField (entryKey,model);
            ListExporterUtils exporter = new ListExporterUtils(entryName, new ArrayList(captions.values()),reqFileds);
            ListExporterWriteCharLine(exporter,new ArrayList(captions.values()));
            int pageRows = 1000;
            int pages = total / pageRows + (total % 1000 == 0 ? 0 : 1);
            int index = 2;

            for (int i = 1; i <= pages; ++i) {
                EntryData entrydata = grid.getEntryData(total, (i - 1) * pageRows, Math.min(i * pageRows, total), pages, pageRows, i, true);
                index = exportEntry(entryKey, view, model, exporter, entrydata, captions, index, progress);
            }

            LocalDate now = LocalDate.now();
            String url = exporter.flush(view.getModel().getDataEntityType(), ResManager.loadKDString("引出分录数据_", "ExportEntryConfigPlugin_2", "bos-form-business", new Object[0]) + entryName + String.format("_%02d%02d", now.getMonthValue(), now.getDayOfMonth()));
            if (StringUtils.isNotBlank(url)) {
                urls.add(url);
            }
        } catch (Exception e) {
            if (SystemParamServiceHelper.isShowStackTrace()) {
                throw new RuntimeException(e);
            } else {
                throw new KDBizException(ResManager.loadKDString("引出异常，请查日志分析", "ExportEntryConfigPlugin_3", "kd-bos-tcbj", new Object[0]));
            }
        }
        return urls;
    }

    private static Map<String, KeyValue> prepareCaptions(IFormView view, IDataModel model, String entryKey, String settingkey) {
        Map<String, KeyValue> captions = new LinkedHashMap();
        String json = UserConfigServiceHelper.getSetting(Long.parseLong(RequestContext.get().getUserId()), settingkey);
        JSONObject settings = JSON.parseObject(json).getJSONObject(entryKey);
        JSONArray visibleFields = settings.getJSONArray("cs");
        Iterator visibleField = visibleFields.iterator();

        while (visibleField.hasNext()) {
            Object fieldName = visibleField.next();
            // 暂时先写死导出字段
            List<String> fields = getSaloutstockEntryExportField();
            // update by hst 2023/11/29 序号处理
            if ("seq".equals(fieldName)) {
                fieldName = entryKey + ".seq";
            }
            if (fields.indexOf(fieldName.toString()) == -1) {
                continue;
            }
            IDataEntityProperty prop = model.getProperty((String) fieldName);
            if (!(prop instanceof EntryProp) && !(prop instanceof AttachmentProp) && !(prop instanceof PictureProp)) {
                if (prop != null) {
                    captions.put(prop.getName(), new KeyValue(prop.getName(), new KeyValue(StringUtils.isNotBlank(prop.getDisplayName()) ? prop.getDisplayName().toString() : "", new ArrayList())));
                } else {
                    Control control = view.getControl((String) fieldName);
                    LocaleString displayname;
                    if (control instanceof BasedataPropEdit) {
                        BasedataPropEdit edit = (BasedataPropEdit) control;
                        displayname = edit.getDisplayName();
                        captions.put(control.getKey(), new KeyValue(control.getKey(), new KeyValue(StringUtils.isNotBlank(displayname) ? displayname.toString() : "", new ArrayList())));
                    } else if (control instanceof DateRangeEdit) {
                        DateRangeEdit edit = (DateRangeEdit) control;
                        displayname = edit.getProperty().getDisplayName();
                        captions.put(control.getKey(), new KeyValue(control.getKey(), new KeyValue(StringUtils.isNotBlank(displayname) ? displayname.toString().substring(0, displayname.toString().lastIndexOf(46)) : "", new ArrayList())));
                    } else if (control instanceof TimeRangeEdit) {
                        TimeRangeEdit edit = (TimeRangeEdit) control;
                        displayname = edit.getProperty().getDisplayName();
                        captions.put(control.getKey(), new KeyValue(control.getKey(), new KeyValue(StringUtils.isNotBlank(displayname) ? displayname.toString().substring(0, displayname.toString().lastIndexOf(46)) : "", new ArrayList())));
                    }
                }
            }
        }

        return captions;
    }

    private static int getTotal(IFormView view, String entryKey) {
        return view.getModel().getEntryRowCount(entryKey);
    }

    private static int exportEntry(String entryKey, IFormView view, IDataModel model, ListExporterUtils exporter, EntryData entrydata, Map<String, KeyValue> captions, int excelRowIndex, int[] progress) {
        List<Object> rowdata = new ArrayList();
        List<Object> rows = (List) entrydata.getData().get("rows");
        Map<String, Integer> dataindex = (Map) entrydata.getData().get("dataindex");
        Map<String, Object> fmtinfo = entrydata.getFmtInfo();
        DynamicObject[] dataEntry = entrydata.getDataEntitys();

        for (int i = 0; i < rows.size(); ++i) {
            List<Object> row = (List) rows.get(i);
            Iterator caption = captions.keySet().iterator();

            while (true) {
                while (caption.hasNext()) {
                    String fieldname = (String) caption.next();
                    IDataEntityProperty prop = model.getProperty(fieldname);
                    Object val;
                    if (!(prop instanceof AttachmentProp) && !(prop instanceof PictureProp) && (!(prop instanceof IFieldHandle) || ((IFieldHandle) prop).isExportable())) {
                        if (!fieldname.equals("entryid")) {
                            Integer index = (Integer) dataindex.get(fieldname);
                            val = row.get(index);
                        } else {
                            val = ((DynamicObject) dataEntry[i]).get("id");
                        }
                        if (StringUtils.isNotBlank(val)) {
                            if (prop instanceof BooleanProp) {
                                val = Boolean.TRUE.equals(val) ? "是" : "否";
                            } else {
                                ArrayList valStrs;
                                Iterator var38;
                                if (prop instanceof ComboProp) {
                                    List<String> values = Arrays.asList(val.toString().split(","));
                                    List<ValueMapItem> items = ((ComboProp) prop).getComboItems();
                                    if (items != null && !items.isEmpty()) {
                                        valStrs = new ArrayList();
                                        var38 = items.iterator();

                                        while (var38.hasNext()) {
                                            ValueMapItem item = (ValueMapItem) var38.next();
                                            if (values.contains(item.getValue())) {
                                                valStrs.add(StringUtils.isNotBlank(item.getName()) ? item.getName().toString() : "");
                                            }
                                        }

                                        val = String.join(" ", valStrs);
                                    }
                                } else {
                                    String displayProp;
                                    int displayIndex;
                                    if (prop instanceof MulBasedataProp) {
                                        displayProp = ((MulBasedataProp) prop).getDisplayProp();
                                        displayIndex = "name".equals(displayProp) ? 1 : 0;
                                        valStrs = new ArrayList();
                                        var38 = ((List) val).iterator();

                                        while (var38.hasNext()) {
                                            Object obj = var38.next();
                                            if (obj.getClass().isArray() && ((Object[]) ((Object[]) obj)).length >= 2) {
                                                valStrs.add((String) ((Object[]) ((Object[]) obj))[displayIndex]);
                                            }
                                        }

                                        val = String.join(";", valStrs);
                                    } else if (!(prop instanceof FlexProp)) {
                                        if (prop instanceof BasedataProp) {
                                            displayProp = ((BasedataProp) prop).getDisplayProp();
                                            displayIndex = "name".equals(displayProp) ? 1 : 0;
                                            val = ((Object[]) ((Object[]) val))[displayIndex];
                                        } else if (prop instanceof DecimalProp) {
                                            if (prop instanceof AmountProp) {
                                                if (val.getClass().isArray() && ((Object[]) ((Object[]) val)).length >= 2) {
                                                    val = ((Object[]) ((Object[]) val))[0];
                                                }

                                                val = formatAmount(prop, row, dataindex, fmtinfo, entrydata.getDataEntitys()[i], val);
                                            } else if (prop instanceof QtyProp) {
                                                if (val.getClass().isArray() && ((Object[]) ((Object[]) val)).length >= 2) {
                                                    val = ((Object[]) ((Object[]) val))[0];
                                                }

                                                Integer cppropIndex = (Integer) dataindex.get("cprop");
                                                Map<String, Object> cpprop = (Map) row.get(cppropIndex);
                                                Map<String, Object> materialFmtinfo = (Map) cpprop.get("fmtinfo");
                                                val = formatQty(prop, row, dataindex, materialFmtinfo, fmtinfo, entrydata.getDataEntitys()[i], val);
                                            } else {
                                                NumberPrecision number = new NumberPrecision(((DecimalProp) prop).getScale(), (String) null, false, new BigDecimal(val.toString()));
                                                val = number.toString();
                                            }

                                            if (!((DecimalProp) prop).isZeroShow() && BigDecimal.ZERO.compareTo(new BigDecimal(val.toString().replace(",", ""))) == 0) {
                                                val = null;
                                            }
                                        } else if (val.getClass().isArray() && ((Object[]) ((Object[]) val)).length >= 2) {
                                            val = ((Object[]) ((Object[]) val))[1];
                                        } else if (prop instanceof TimeProp) {
                                            val = formatTimeStr((Integer) val);
                                        } else if (prop == null) {
                                            Control c = view.getControl(fieldname);
                                            String valStr = val.toString().trim();
                                            if (c instanceof TimeRangeEdit) {
                                                if (!valStr.equals(",")) {
                                                    String[] strs = valStr.replaceAll("\\s*", "").split(",");
                                                    int start = Integer.parseInt(strs[0].trim());
                                                    int end = Integer.parseInt(strs[1].trim());
                                                    if (start < 0 && end < 0) {
                                                        val = null;
                                                    } else {
                                                        val = StringUtils.isNotBlank(strs[0]) ? formatTimeStr(start) : "";
                                                        val = val + "~" + (StringUtils.isNotBlank(strs[1]) ? formatTimeStr(end) : "");
                                                    }
                                                } else {
                                                    val = null;
                                                }
                                            } else if (c instanceof DateRangeEdit) {
                                                if (!valStr.equals(",")) {
                                                    val = valStr.replace(",", " ~");
                                                } else {
                                                    val = null;
                                                }
                                            }
                                        }
                                    } else {
                                        List<String> valList = new ArrayList();
                                        Iterator var20 = ((List) val).iterator();

                                        while (var20.hasNext()) {
                                            Object obj = var20.next();
                                            String alias = (String) ((Map) obj).get("alias");
                                            if (StringUtils.isNotBlank(alias)) {
                                                valList.add(alias);
                                            }
                                        }

                                        val = String.join(";", valList);
                                    }
                                }
                            }
                        }

                        rowdata.add(val);
                    } else {
                        rowdata.add((Object) null);
                    }
                }

                exporter.writeLine(rowdata, excelRowIndex++);
                rowdata.clear();
                break;
            }
        }
        return excelRowIndex;
    }

    private static String formatAmount(IDataEntityProperty prop, List<Object> row, Map<String, Integer> dataindex, Map<String, Object> fmtinfo, DynamicObject dyn, Object val) {
        String ctrlKey = ((DecimalProp) prop).getControlPropName();
        Map<String, Object> colfmt = (Map) fmtinfo.get("colfmt");
        Map<String, Object> fmt = (Map) colfmt.get(ctrlKey);
        if (fmt == null) {
            Integer index = (Integer) dataindex.get(ctrlKey);
            Object ctrlVal = null;
            if (index == null) {
                if (((DynamicObjectType) dyn.getDataEntityType()).getProperty(ctrlKey) != null) {
                    DynamicObject ctrl = (DynamicObject) dyn.get(ctrlKey);
                    if (ctrl != null) {
                        ctrlVal = ctrl.get(((BasedataProp) ctrl.getDataEntityType()).getNumberProp());
                    }
                }
            } else {
                ctrlVal = (Object[]) ((Object[]) row.get(index));
                if (ctrlVal != null && ctrlVal.getClass().isArray() && ((Object[]) ((Object[]) ctrlVal)).length >= 2) {
                    ctrlVal = ((Object[]) ((Object[]) ctrlVal))[0];
                }
            }

            if (ctrlVal != null) {
                Map<String, Object> currencyfmt = (Map) fmtinfo.get("currencyfmt");
                fmt = (Map) currencyfmt.get(ctrlVal);
            }
        }

        if (fmt == null) {
            fmt = (Map) colfmt.get("_MainCurrency_");
        }

        if (fmt != null) {
            NumberPrecision number = new NumberPrecision((Integer) fmt.get(prop instanceof PriceProp ? "pp" : "ap"), (String) null, false, new BigDecimal(val.toString()));
            val = number.toString();
        }

        return (String) val;
    }

    private static Object formatQty(IDataEntityProperty prop, List<Object> row, Map<String, Integer> dataindex, Map<String, Object> materialFmtinfo, Map<String, Object> fmtinfo, DynamicObject dyn, Object val) {
        String ctrlKey = ((DecimalProp) prop).getControlPropName();
        Map<String, Object> fmt = null;
        Map colfmt;
        if (materialFmtinfo != null && !materialFmtinfo.isEmpty()) {
            colfmt = (Map) materialFmtinfo.get("unitfmt");
            fmt = (Map) colfmt.get(ctrlKey);
        }

        if (fmt == null) {
            colfmt = (Map) fmtinfo.get("colfmt");
            fmt = (Map) colfmt.get(ctrlKey);
            if (fmt == null) {
                Integer index = (Integer) dataindex.get(ctrlKey);
                Object ctrlVal = null;
                if (index == null) {
                    if (((DynamicObjectType) dyn.getDataEntityType()).getProperty(ctrlKey) != null) {
                        DynamicObject ctrl = (DynamicObject) dyn.get(ctrlKey);
                        if (ctrl != null) {
                            if (ctrl.getDataEntityType() instanceof RefEntityType) {
                                ctrlVal = ctrl.get("number");
                            } else {
                                ctrlVal = ctrl.get(((BasedataProp) ctrl.getDataEntityType()).getNumberProp());
                            }
                        }
                    }
                } else {
                    ctrlVal = (Object[]) ((Object[]) row.get(index));
                    if (ctrlVal != null && ctrlVal.getClass().isArray() && ((Object[]) ((Object[]) ctrlVal)).length >= 2) {
                        ctrlVal = ((Object[]) ((Object[]) ctrlVal))[0];
                    }
                }

                if (ctrlVal != null) {
                    Map<String, Object> unitfmt = (Map) fmtinfo.get("unitfmt");
                    fmt = (Map) unitfmt.get(ctrlVal);
                }
            }
        }

        if (fmt != null) {
            NumberPrecision number = new NumberPrecision((Integer) fmt.get("ps"), (String) null, false, new BigDecimal(val.toString()));
            val = number.toString();
        }

        return val;
    }

    private static String formatTimeStr(Integer timeObj) {
        if (timeObj != null && timeObj >= 0) {
            int time = timeObj;
            int second = time % 60;
            int minute = (time /= 60) % 60;
            int hour = (time /= 60) % 24;
            return String.format("%02d:%02d:%02d", hour, minute, second);
        } else {
            return null;
        }
    }

    public static void ListExporterWriteCharLine(ListExporterUtils listExporter, List<KeyValue> fieldCaptions) {
        List<Object> rows = new ArrayList<>();
        fieldCaptions.stream().forEach(KeyValue -> {
            rows.add(KeyValue.key);
        });
        listExporter.writeLine(rows,1);
    }


    /**
     * 获取销售发货分录导出字段
     * @author: hst
     * @createDate: 2023/03/21
     */
    private static List<String> getSaloutstockEntryExportField() {
        // update by hst 2023/11/29 增加序号、版本号
        String[] fields = new String[]{"materialentry.seq","material","materialname","pobillno","yd_version","yd_producers","yd_supmatname","yd_model",
                "unit","qty","yd_pieceqty","suplot","proddate","duedate","note"};
        return Arrays.asList(fields);
    }

    /**
     * 获取分录必录字段
     * @author: hst
     * @createDate: 2023/03/23
     */
    private static List<String> getEntryMustField(String entryKey,IDataModel model) {
        List<String> fields = new ArrayList<>();
        DataEntityPropertyCollection properties = model.getEntryEntity(entryKey).getDynamicObjectType().getProperties();
        for (IDataEntityProperty property : properties) {
            if (property instanceof BasedataProp) {
                if (((BasedataProp) property).isMustInput()) {
                    fields.add(property.getName());
                }
            } else if (property instanceof FieldProp) {
                if (((FieldProp) property).isMustInput()) {
                    fields.add(property.getName());
                }
            }
        }
        return fields;
    }
}
