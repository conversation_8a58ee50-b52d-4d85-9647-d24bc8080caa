package kd.bos.tcbj.srm.utils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

/**
 * 格式化工具类
 * @auditor liefengWu
 * @date 2022年6月7日
 * 
 */
public class GeneralFormatUtils {
	public static String getString(Object value) {
		if (value == null || value.toString().equals("null")) {
			return "";
		}
		return String.valueOf(value);
	}
	
	
	
	/**
	 * 检查时间文本格式
	 * @param format
	 * @param time
	 * @return
	 */
	public static boolean isTimeFormat(String format,String time){
		 try{
			 DateFormat formatter = new SimpleDateFormat(format);
		     Date date = (Date)formatter.parse(time);
		     return time.equals(formatter.format(date));
		 }catch(Exception e){
		       return false;
		 }
	}

	/**
	 * 判断数字是否有最大精确到exact位数
	 * 
	 * @param value
	 * @param exact
	 * @return
	 */
	public static boolean isNumberExact(String value, int exact) {
		if (value == null) {
			return false;
		}
		// 字符串长度-小数点最后索引-1得到 小数位数
		if (value.trim().length() - value.trim().lastIndexOf(".") - 1 > exact) {
			return false;
		}
		return true;
	}

	public static BigDecimal getBigDecimal(Object obj) {
		BigDecimal result = BigDecimal.ZERO;
		if (obj == null)
			result = new BigDecimal(0.0D);
		if (obj instanceof String)
			result = getBigDecimal((String) obj);
		if (obj instanceof BigDecimal)
			result = ((BigDecimal) obj);
		if (obj instanceof Double)
			result = getBigDecimal((Double) obj);
		if (obj instanceof Integer) {
			result = getBigDecimal((Integer) obj);
		}
		if (result == null)
			result = new BigDecimal(0.0D);
		return result;
	}

	/**
	 * 获取bigdecimal精确到对应位数
	 * 
	 * @param obj
	 * @param excal
	 * @return
	 */
	public static BigDecimal getBigDecimal(Object obj, int excal) {
		BigDecimal result = BigDecimal.ZERO;
		try {
			if (obj == null)
				result = new BigDecimal(0.0D);
			if (obj instanceof String)
				result = getBigDecimal((String) obj);
			if (obj instanceof BigDecimal)
				result = ((BigDecimal) obj);
			if (obj instanceof Double)
				result = getBigDecimal((Double) obj);
			if (obj instanceof Integer) {
				result = getBigDecimal((Integer) obj);
			}
			if (result == null)
				result = new BigDecimal(0.0D);
			result = result.setScale(excal, BigDecimal.ROUND_HALF_UP);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return result;
	}

	public static BigDecimal getBigDecimal(Double value) {
		if (value == null) {
			return new BigDecimal(0.0D);
		}
		return new BigDecimal(value.doubleValue());
	}

	public static BigDecimal getBigDecimal(Integer value) {
		if (value == null) {
			return new BigDecimal(0.0D);
		}
		return new BigDecimal(value.doubleValue());
	}

	public static BigDecimal getBigDecimal(String value) {
		if (value == null || value.trim().length() == 0) {
			return new BigDecimal(0.0D);
		}
		return new BigDecimal(value);
	}
	
	public static Long getLong(Object obj) {
		Long value = null;
		try {
			String strValue = getString(obj);
			value = Long.valueOf(strValue);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return value;
	}
	
	
	public static int getInt(Object obj){
		int result = 0;
		if(obj == null)
			return result;
		try {
			result = Integer.valueOf(obj.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return result;
	}

	public static boolean isNumber(Object value){
		boolean isFlag = true;
		try {
			Double.valueOf(value.toString());
		} catch (Exception e) {
			isFlag = false;
		}
		
		return isFlag;
	}
	
	
	public static String f(String str, Object... data) {
		if (StringUtils.isBlank(str) && data.length == 1)
			return data.toString();
		return MessageFormat.format(str, data);
	}
}
