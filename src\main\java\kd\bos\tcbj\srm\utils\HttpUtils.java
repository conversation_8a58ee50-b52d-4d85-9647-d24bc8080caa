package kd.bos.tcbj.srm.utils;

import javax.net.ssl.SSLContext;

import org.apache.http.NameValuePair;
import org.apache.http.config.Registry;
import org.apache.http.HttpEntity;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.kingdee.bos.qing.util.HttpUtil.createIgnoreVerifySSL;

/**
 * Http工具类
 * <AUTHOR>
 * @Description:
 * @date 2023-11-10
 */
public class HttpUtils {

    /**
     * 链接超时时间
     */
    private static final int COUN_TIME_OUT = 10 * 1000;
    /**
     * 读取超时时间
     */
    private static final int READ_TIME_OUT = 30 * 1000;
    /**
     * 边界符
     */
    private static final String BOUNDARY = "******";
    private static final String PRE_FIX = ("\r\n--" + BOUNDARY + "\r\n");
    private static final String END_FIX = ("\r\n--" + BOUNDARY + "--\r\n");

    /**
     * POST请求数据
     * @param u 请求地址
     * @return 响应回传的结果
     */
    public static String sendPost(String u) {
        return sendPost(u, null, "");
    }

    /**
     * POST请求数据
     * @param u 请求地址
     * @param json 请求的Json数据
     * @return 响应回传的结果
     */
    public static String sendPost(String u, String json) {
        return sendPost(u, null, json);
    }

    /**
     * POST请求数据
     * @param u 请求地址
     * @param headMap 附加的请求头
     * @param params 请求的Json数据
     * @return 响应回传的结果
     */
    public static String sendPost(String u, Map<String, String> headMap, String params) {
        String result = "";
        DataOutputStream dataOutputStreamSend = null;
        InputStream inputStream = null;
        ByteArrayOutputStream dataOutputStream = null;
        try {
            URL httpUrl = new URL(u);
            HttpURLConnection urlConnection = (HttpURLConnection) httpUrl.openConnection();
            // 设置超时时间
            urlConnection.setConnectTimeout(COUN_TIME_OUT);
            urlConnection.setReadTimeout(READ_TIME_OUT);

            urlConnection.setRequestMethod("POST");
            // 设置通用请求类型
            urlConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            urlConnection.setRequestProperty("Charset", "UTF-8");
            // 设置链接状态
            urlConnection.setRequestProperty("connection", "keep-alive");

            if (headMap != null) {
                for (String key : headMap.keySet()) {
                    urlConnection.setRequestProperty(key, headMap.get(key)); // 添加附加的请求头
                }
            }

            // post请求，参数要放在http正文内，因此需要设为true, 默认情况下是false;
            urlConnection.setDoOutput(true);
            // 设置是否从httpUrlConnection读入，默认情况下是true;
            urlConnection.setDoInput(true);
            // Post 请求不能使用缓存
            urlConnection.setUseCaches(false);
            // 设置本次连接是否自动处理重定向
            urlConnection.setInstanceFollowRedirects(true);

            urlConnection.connect();

            // 基本类型和字符串使用DataOutputStream
            dataOutputStreamSend = new DataOutputStream(urlConnection.getOutputStream());
            dataOutputStreamSend.write(params.getBytes());
            dataOutputStreamSend.flush();

            if (urlConnection.getResponseCode() == 200) {
                // 获取返回流
                result = getResult(urlConnection.getInputStream());
            }
        } catch (MalformedURLException e) {
            // url格式错误
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭相应的流
        }
        return result;
    }

    /**
     * 通过GET请求调用接口
     * @param u 访问的URL地址
     * @return 返回相应的数据
     */
    public static String sendGet(String u) {
        StringBuffer sbf = new StringBuffer();
        try {
            URL url = new URL(u);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/json");
            // 设置超时时间
            connection.setConnectTimeout(COUN_TIME_OUT);
            connection.setReadTimeout(READ_TIME_OUT);

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String lines;
            while ((lines = reader.readLine()) != null) {
                lines = new String(lines.getBytes(), StandardCharsets.UTF_8);
                sbf.append(lines);
            }
            reader.close();
            // 断开连接
            connection.disconnect();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sbf.toString();
    }

    private static String getResult(InputStream inputStream) {
        String result = "";
        ByteArrayOutputStream dataOutputStream = null;
        try {
            byte[] buf = new byte[1024];
            int n;
            dataOutputStream = new ByteArrayOutputStream();

            while (((n = inputStream.read(buf)) != -1)) {
                dataOutputStream.write(buf, 0, n);
            }
            dataOutputStream.toByteArray();
            result = new String(dataOutputStream.toByteArray(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    //适用于post请求并传送form-data数据（同样适用于post的Raw类型的application-json格式）
    public static String postParams(String url, Map<String, String> params) {
        SSLContext sslcontext = createIgnoreVerifySSL();
        // 设置协议http和https对应的处理socket链接工厂的对象
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslcontext))
                .build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);

        //创建自定义的httpclient对象
        CloseableHttpClient client = HttpClients.custom().setConnectionManager(connManager).build();
        HttpPost post = new HttpPost(url);
        CloseableHttpResponse res = null;
        try {
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            Set<String> keySet = params.keySet();
            for (String key : keySet) {
                nvps.add(new BasicNameValuePair(key, params.get(key)));
            }
            post.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));
            res = client.execute(post);
            HttpEntity entity = res.getEntity();
            return EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                res.close();
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    public static SSLContext createIgnoreVerifySSL() {
        try {
            SSLContext sc = SSLContext.getInstance("SSLv3");

            // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
            X509TrustManager trustManager = new X509TrustManager() {
                @Override
                public void checkClientTrusted(
                        X509Certificate[] paramArrayOfX509Certificate,
                        String paramString) throws CertificateException {
                }

                @Override
                public void checkServerTrusted(
                        X509Certificate[] paramArrayOfX509Certificate,
                        String paramString) throws CertificateException {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
            };

            sc.init(null, new TrustManager[]{trustManager}, null);
            return sc;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }
}
