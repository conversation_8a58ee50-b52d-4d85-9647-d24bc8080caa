package kd.bos.tcbj.srm.utils;

import kd.bos.dataentity.utils.StringUtils;
import kd.bos.entity.datamodel.KeyValue;
import kd.bos.entity.datamodel.NumberPrecision;
import kd.bos.list.ListColumnType;
import kd.bos.mvc.export.ExcelWriter;
import kd.bos.mvc.export.ExportSheetStyle;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @package: kd.bos.tcbj.srm.utils.ListExporterUtils
 * @className ListExporterUtils
 * @author: hst
 * @createDate: 2023/03/23
 * @version: v1.0
 */
public class ListExporterUtils extends ExcelWriter {

    private SXSSFSheet sheet;
    private ExportSheetStyle styles;
    private List<KeyValue> fieldCaptions;
    private int rowCount = 0;
    private int totalOffset = 0;

    @Deprecated
    public ListExporterUtils(String sheetName, List<KeyValue> fieldCaptions, List<String> reqFields) {
        this.sheet = this.wb.createSheet(sheetName);
        this.styles = new ExportSheetStyle(this.wb);
        this.fieldCaptions = fieldCaptions;
        int col = 0;

        for(int i = 0; i < fieldCaptions.size(); ++i) {
            KeyValue colInfo = (KeyValue)fieldCaptions.get(i);
            int colWidth = 0;
            String align = "default";
            Object value = colInfo.value;
            if (colInfo.extend != null) {
                colWidth = (Integer)colInfo.extend[1];
                if (colInfo.extend[0].equals(3) && colWidth < 80) {
                    colWidth = 80;
                }

                align = (String)colInfo.extend[2];
            } else {
                value = ((KeyValue)value).key;
            }

            // 必录列标红
            if (reqFields.indexOf(colInfo.key) > -1) {
                SXSSFCell cell = createCell(this.wb, getRow(this.sheet, 0), col, this.styles.getTitleMustInputStyle(align));
                cell.setCellValue("*" +(String)value);
            } else {
                SXSSFCell cell = createCell(this.wb, getRow(this.sheet, 0), col, this.styles.getTitleStyle(align));
                cell.setCellValue((String) value);
            }
            this.sheet.setColumnWidth(col, Math.min(256 * Math.max(colWidth / 8, Math.max(((String)value).length(), 8)) * 2, 51200));
            ++col;
        }
    }

    private int[] createHeader(List<KeyValue> captions, int r, Map<Integer, Integer> cellSpanRow) {
        int[] span = new int[]{1, 0};
        Map<Integer, Integer> newCol = new HashMap();

        int i;
        KeyValue colInfo;
        for(i = 0; i < captions.size(); ++i) {
            colInfo = (KeyValue)captions.get(i);
            int var10002;
            if (!(colInfo.value instanceof KeyValue)) {
                newCol.put(i, this.totalOffset);
                this.creatHeaderCell((String)colInfo.value, colInfo.extend, r);
                ++this.totalOffset;
                var10002 = span[1]++;
            } else {
                this.creatHeaderCell(((KeyValue)colInfo.value).key, colInfo.extend, r);
                if (colInfo.extend != null && ((Integer)colInfo.extend[0] == ListColumnType.MergeColumn.getValue() || (Integer)colInfo.extend[0] == ListColumnType.DynamicTextList.getValue())) {
                    newCol.put(i, this.totalOffset);
                    ++this.totalOffset;
                    var10002 = span[1]++;
                } else {
                    int col = this.totalOffset;
                    int[] subSpan = this.createHeader((List)((KeyValue)colInfo.value).value, r + 1, cellSpanRow);
                    if (span[0] < subSpan[0] + 1) {
                        span[0] = subSpan[0] + 1;
                    }

                    span[1] += subSpan[1];
                    if (subSpan[1] > 1) {
                        CellRangeAddress cellRange = new CellRangeAddress(r, r, col, col + Math.max(0, subSpan[1] - 1));
                        this.sheet.addMergedRegion(cellRange);
                        Cell cell = this.sheet.getRow(r).getCell(col);
                        CellStyle cellstyle = cell.getCellStyle();
                        cellstyle.setVerticalAlignment(VerticalAlignment.CENTER);
                        cellstyle.setAlignment(HorizontalAlignment.CENTER);
                        RegionUtil.setBorderTop(BorderStyle.THIN, cellRange, this.sheet);
                        RegionUtil.setBorderRight(BorderStyle.THIN, cellRange, this.sheet);
                        RegionUtil.setBorderBottom(BorderStyle.THIN, cellRange, this.sheet);
                        RegionUtil.setBorderLeft(BorderStyle.THIN, cellRange, this.sheet);
                    }
                }
            }
        }

        for(i = 0; i < captions.size(); ++i) {
            colInfo = (KeyValue)captions.get(i);
            if (!(colInfo.value instanceof KeyValue) || colInfo.extend != null && ((Integer)colInfo.extend[0] == ListColumnType.MergeColumn.getValue() || (Integer)colInfo.extend[0] == ListColumnType.DynamicTextList.getValue())) {
                if (span[0] > 1) {
                    CellRangeAddress cellRange = new CellRangeAddress(r, r + span[0] - 1, (Integer)newCol.get(i), (Integer)newCol.get(i));
                    this.sheet.addMergedRegion(cellRange);
                    Cell cell = this.sheet.getRow(r).getCell((Integer)newCol.get(i));
                    CellStyle cellstyle = cell.getCellStyle();
                    cellstyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    cellstyle.setAlignment(HorizontalAlignment.CENTER);
                    RegionUtil.setBorderTop(BorderStyle.THIN, cellRange, this.sheet);
                    RegionUtil.setBorderRight(BorderStyle.THIN, cellRange, this.sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, cellRange, this.sheet);
                    RegionUtil.setBorderLeft(BorderStyle.THIN, cellRange, this.sheet);
                }

                cellSpanRow.put(newCol.get(i), r + span[0] - 1);
            }
        }

        if (this.rowCount < r) {
            this.rowCount = r;
        }

        return span;
    }

    private void creatHeaderCell(String caption, Object[] extend, int r) {
        int colWidth = 0;
        String align = "default";
        if (extend != null && extend.length >= 3) {
            colWidth = (Integer)extend[1];
            if (extend[0].equals(3) && colWidth < 80) {
                colWidth = 80;
            }

            align = (String)extend[2];
        }

        CellStyle cellStyle = this.styles.getTitleStyle(align);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        SXSSFCell cell = createCell(this.wb, getRow(this.sheet, r), this.totalOffset, cellStyle);
        cell.setCellValue(caption);
        this.sheet.setColumnWidth(this.totalOffset, Math.min(256 * Math.max(colWidth / 8, Math.max(caption.length(), 8)) * 2, 30720));
    }

    public void write(List<List<Object>> dataRows) {
        Iterator var2 = dataRows.iterator();

        while(var2.hasNext()) {
            List<Object> row = (List)var2.next();
            this.totalOffset = 0;
            this.writeLine(this.fieldCaptions, row, ++this.rowCount);
        }

    }

    public void writeLine(List<KeyValue> captions, List<Object> row, int r) {
        for(int i = 0; i < captions.size(); ++i) {
            if (row.size() > this.totalOffset) {
                KeyValue colInfo = (KeyValue)captions.get(i);
                Object value = row.get(this.totalOffset);
                boolean isDynamicTextList = colInfo.extend != null && (Integer)colInfo.extend[0] == ListColumnType.DynamicTextList.getValue();
                if (colInfo.value instanceof KeyValue && !isDynamicTextList) {
                    if (colInfo.extend != null && (Integer)colInfo.extend[0] == ListColumnType.MergeColumn.getValue()) {
                        this.creatValueCell(value, colInfo.extend, r);
                    } else {
                        this.writeLine((List)((KeyValue)colInfo.value).value, row, r);
                    }
                } else {
                    this.creatValueCell(value, colInfo.extend, r);
                }
            }
        }

    }

    private void creatValueCell(Object value, Object[] extend, int r) {
        if (StringUtils.isBlank(value)) {
            ++this.totalOffset;
        } else {
            String align = "default";
            if (extend != null && extend.length >= 3) {
                align = (String)extend[2];
                if (extend[0].equals(2)) {
                    if (!(value instanceof NumberPrecision) && !(value instanceof Map)) {
                        if (value instanceof BigDecimal) {
                            createCell(this.wb, getRow(this.sheet, r), this.totalOffset++, this.styles.getDecimalStyle(this.wb, (Map)null, align)).setCellValue(((BigDecimal)value).doubleValue());
                            return;
                        }

                        createCell(this.wb, getRow(this.sheet, r), this.totalOffset++, this.styles.getTextStyle(align)).setCellValue(value.toString());
                        return;
                    }

                    Map<String, Object> dataMap = new HashMap();
                    if (value instanceof NumberPrecision) {
                        ((Map)dataMap).put("result", ((NumberPrecision)value).getResult() == null ? new BigDecimal(0) : ((NumberPrecision)value).getResult());
                        ((Map)dataMap).put("sign", ((NumberPrecision)value).getSign());
                        ((Map)dataMap).put("precision", ((NumberPrecision)value).getPrecision());
                        ((Map)dataMap).put("showSign", ((NumberPrecision)value).isShowSign());
                        ((Map)dataMap).put("groupingUsed", ((NumberPrecision)value).isGroupingUsed());
                        ((Map)dataMap).put("stripTrailingZeros", ((NumberPrecision)value).isStripTrailingZeros());
                    } else if (value instanceof Map) {
                        dataMap = (Map)value;
                    }

                    BigDecimal decimal = new BigDecimal(((Map)dataMap).getOrDefault("result", 0).toString());
                    createCell(this.wb, getRow(this.sheet, r), this.totalOffset++, this.styles.getDecimalStyle(this.wb, (Map)dataMap, align)).setCellValue(decimal.doubleValue());
                    return;
                }

                if (extend[0].equals(3)) {
                    try {
                        Date datetime = value instanceof Long ? new Date((Long)value) : (new SimpleDateFormat("yyyy-MM-dd H:m:s")).parse(value.toString());
                        createCell(this.wb, getRow(this.sheet, r), this.totalOffset++, this.styles.getDatetimeStyle(align)).setCellValue(datetime);
                    } catch (ParseException var7) {
                        createCell(this.wb, getRow(this.sheet, r), this.totalOffset++, this.styles.getDateStyle(align)).setCellValue(value.toString());
                    }

                    return;
                }
            }

            createCell(this.wb, getRow(this.sheet, r), this.totalOffset++, this.styles.getTextStyle(align)).setCellValue(value instanceof BigDecimal ? ((BigDecimal)value).toPlainString() : (value == null ? "" : value.toString()));
        }
    }

    public void writeLine(List<Object> row, int r) {
        int col = 0;

        for(int i = 0; i < this.fieldCaptions.size(); ++i) {
            Object value = row.get(i);
            if (StringUtils.isBlank(value)) {
                ++col;
            } else {
                Object[] colExtend = ((KeyValue)this.fieldCaptions.get(i)).extend;
                String align = "default";
                if (colExtend != null) {
                    align = (String)colExtend[2];
                    if (colExtend[0].equals(2)) {
                        if (!(value instanceof NumberPrecision) && !(value instanceof Map)) {
                            createCell(this.wb, getRow(this.sheet, r), col++, this.styles.getTextStyle(align)).setCellValue(value.toString());
                            continue;
                        }

                        Map<String, Object> dataMap = new HashMap();
                        if (value instanceof NumberPrecision) {
                            ((Map)dataMap).put("result", ((NumberPrecision)value).getResult() == null ? new BigDecimal(0) : ((NumberPrecision)value).getResult());
                            ((Map)dataMap).put("sign", ((NumberPrecision)value).getSign());
                            ((Map)dataMap).put("precision", ((NumberPrecision)value).getPrecision());
                            ((Map)dataMap).put("showSign", ((NumberPrecision)value).isShowSign());
                            ((Map)dataMap).put("groupingUsed", ((NumberPrecision)value).isGroupingUsed());
                            ((Map)dataMap).put("stripTrailingZeros", ((NumberPrecision)value).isStripTrailingZeros());
                        } else if (value instanceof Map) {
                            dataMap = (Map)value;
                        }

                        BigDecimal decimal = new BigDecimal(((Map)dataMap).getOrDefault("result", 0).toString());
                        createCell(this.wb, getRow(this.sheet, r), col++, this.styles.getDecimalStyle(this.wb, (Map)dataMap, align)).setCellValue(decimal.doubleValue());
                        continue;
                    }

                    if (colExtend[0].equals(3)) {
                        try {
                            Date datetime = value instanceof Long ? new Date((Long)value) : (new SimpleDateFormat("yyyy-MM-dd H:m:s")).parse(value.toString());
                            createCell(this.wb, getRow(this.sheet, r), col++, this.styles.getDatetimeStyle(align)).setCellValue(datetime);
                        } catch (ParseException var10) {
                            createCell(this.wb, getRow(this.sheet, r), col++, this.styles.getDateStyle(align)).setCellValue(value.toString());
                        }
                        continue;
                    }
                }

                createCell(this.wb, getRow(this.sheet, r), col++, this.styles.getTextStyle(align)).setCellValue(value instanceof BigDecimal ? ((BigDecimal)value).toPlainString() : (value == null ? "" : value.toString()));
            }
        }

    }
}
