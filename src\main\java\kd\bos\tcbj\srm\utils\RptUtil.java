package kd.bos.tcbj.srm.utils;

import kd.bos.algo.*;
import kd.bos.algo.input.CollectionInput;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.LocaleString;
import kd.bos.entity.report.DecimalReportColumn;
import kd.bos.entity.report.ReportColumn;
import kd.bos.tcbj.srm.utils.StringUtils;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.orm.query.QFilter;
import kd.bos.tcbj.srm.admittance.utils.DateUtil;

import java.util.*;

/**
 *  报表构造过滤条件工具类
 * @author: hst
 * @createDate: 2022/10/21
 */
public class RptUtil {

    /**
     * 筛选条件构造基础资料过滤条件
     * @author: hst
     * @createDate: 2022/10/21
     * @param filterInfo
     * @return
     */
    public static QFilter buildF7Filter (FilterInfo filterInfo, String propName, String property, String cp) {
        FilterItemInfo org = filterInfo.getFilterItem(propName);
        QFilter qFilter = null;
        if (Objects.nonNull(org)) {
            DynamicObject orgValues = (DynamicObject) org.getValue();
            if (Objects.nonNull(orgValues)) {
                qFilter = new QFilter(property, cp, orgValues.get("number"));
            }
        }
        return qFilter;
    }

    /**
     * 筛选条件构造时间过滤条件
     * @author: hst
     * @createDate: 2022/10/21
     * @param filterInfo
     * @return
     */
    public static QFilter buildDateFilter (FilterInfo filterInfo, String pattern, String propName, String property, String cp) {
        FilterItemInfo beginDate = filterInfo.getFilterItem(propName);
        QFilter qFilter = null;
        if (Objects.nonNull(beginDate)) {
            Date date = (Date) beginDate.getValue();
            if (Objects.nonNull(date)) {
                String dateStr = DateUtil.date2str(date,pattern);
                qFilter = new QFilter(property, cp, dateStr);
            }
        }
        return qFilter;
    }

    /**
     * 筛选条件构造复选框过滤条件
     * @author: hst
     * @createDate: 2022/10/21
     * @param filterInfo
     * @return
     */
    public static QFilter buildBoxFilter (FilterInfo filterInfo, String propName, String property, String cp, Object value) {
        FilterItemInfo box = filterInfo.getFilterItem(propName);
        QFilter qFilter = null;
        if (Objects.nonNull(box)) {
            boolean boxValue = (boolean) box.getValue();
            if (Objects.nonNull(boxValue) && boxValue) {
                qFilter = new QFilter(property, cp, value);
            }
        }
        return qFilter;
    }

    /**
     * 将统计的列数据转换为行数据
     * 例如   name  nameFile   valueFile           name  1月   2月
     *        张三       1月           3     ->    张三    3     5
     *        张三       2月           5           李四    2
     *        李四       1月           2
     * filed = {"name","1月","2月"}, groupFiles = {"name"}
     * @author: hst
     * @createDate: 2022/10/24
     * @param groupDataSet 需要进行行转列的数据
     * @param nameFile 存储转换后列名的字段名
     * @param valueFile 存储转换后值的字段名
     * @param filed 转换后返回的dataset 的字段数组
     * @param DATATYPES
     * @param groupFiles 转换过程中进行分组的字段
     * @return
     */
    public static DataSet columnDataToRowData (DataSet groupDataSet, String nameFile, String valueFile, String[] filed, DataType[] DATATYPES, String... groupFiles) {
        Collection<Object[]> coll = new ArrayList<>();
        //创建显示行字段
        RowMeta createRowMeta = RowMetaFactory.createRowMeta(filed, DATATYPES);
        CollectionInput collectionInput = new CollectionInput(createRowMeta, coll);
        DataSet createDataSet = Algo.create("RptUtil").createDataSet(collectionInput);
        //初始创建一个空的报表数据行
        Map<String,Object> compares = new HashMap<>();
        if (groupDataSet.hasNext()) {
            String name = null;
            for (Row row : groupDataSet.copy()) {
                for (String file : groupFiles) {
                    compares.put(file, row.getString(file));
                }
                break;
            }
            Object[] tempData = new Object[filed.length];
            coll.add(tempData);
            String tempName = null;
            // Row游标消费完一个结果集之后，不能再消费该结果集
            for (Row row : groupDataSet.copy()) {
                boolean isSame = true;
                for (Map.Entry<String, Object> compare : compares.entrySet()) {
                    if (!StringUtils.equals(compare.getValue().toString(), row.getString(compare.getKey()))) {
                        isSame = false;
                        break;
                    }
                }
                if (!isSame) {
                    tempData = new Object[filed.length];
                    coll.add(tempData);
                    compares.clear();
                    for (String file : groupFiles) {
                        compares.put(file, row.getString(file));
                    }
                }
                for (int i = 0; i < groupFiles.length; i++) {
                    tempData[i] = row.get(groupFiles[i]);
                }
                String file = row.getString(nameFile);
                int index = Arrays.asList(filed).indexOf(file);
                if (index != -1) {
                    tempData[index] = row.get(valueFile);
                }
            }
        }
        return createDataSet;
    }

    /**
     * 构造过滤条件(表头字段)
     * @author: hst
     * @createDate: 2022/10/24
     * @param filterInfo
     * @return
     */
    public static String buildHeadFilter (FilterInfo filterInfo) {
        List<QFilter> headFilters = filterInfo.getHeadFilters();
        if (headFilters.size() > 0) {
            StringBuffer str = new StringBuffer();
            for (QFilter headFilter: headFilters) {
                str.append(headFilter.toString() + " and ");
            }
            return str.delete(str.length() - 5,str.length() - 1).toString();
        } else {
            return "";
        }
    }

    /**
     * 筛选条件构造文本过滤条件
     * @author: hst
     * @createDate: 2023/01/09
     * @param filterInfo
     * @return
     */
    public static QFilter buildTextFilter (FilterInfo filterInfo, String propName, String property, String cp) {
        FilterItemInfo text = filterInfo.getFilterItem(propName);
        QFilter qFilter = null;
        if (Objects.nonNull(text) && Objects.nonNull(text.getValue())
                && StringUtils.isNotBlank(text.getValue().toString())) {
            qFilter = new QFilter(property, cp, text.getValue());
        }
        return qFilter;
    }

    /**
     * 动态创建报表列表字段列
     * @param columnInfo
     * @return
     * @author: hst
     * @createDate: 2024/06/09
     */
    public static ReportColumn createReportColumn(String [] columnInfo) {
        ReportColumn reportColumn = new ReportColumn();
        // 设置报表列字段的标识
        reportColumn.setFieldKey(columnInfo[0]);
        // 设置报表列字段的标识
        reportColumn.setCaption(new LocaleString(columnInfo[1]));
        // 设置报表列字段的类型
        reportColumn.setFieldType(columnInfo[2]);
        return reportColumn;
    }

    /**
     * 动态创建报表列表字段列
     * @param columnInfo
     * @return
     * @author: hst
     * @createDate: 2024/06/09
     */
    public static DecimalReportColumn createDecimalReportColumn(String [] columnInfo) {
        DecimalReportColumn reportColumn = new DecimalReportColumn();
        // 设置报表列字段的标识
        reportColumn.setFieldKey(columnInfo[0]);
        // 设置报表列字段的标识
        reportColumn.setCaption(new LocaleString(columnInfo[1]));
        // 设置报表列字段的类型
        reportColumn.setFieldType(columnInfo[2]);
        return reportColumn;
    }
}
