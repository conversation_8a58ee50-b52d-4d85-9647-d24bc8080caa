package kd.bos.yd.hupun;

import json.JSONArray;
import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.operate.result.IOperateInfo;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.IFormView;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.util.StringUtils;
import kd.bos.yd.tcyp.ABillServiceHelper;
import kd.bos.yd.tcyp.utils.OperationLogUtil;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class ApiUtil {
	public static String key="3623547812";
	public static String secret = "bd2a00e2b64acad6a7200b5e694a379c";
	public static String URI = "https://open-api.hupun.com/api/";
	public static String outbill = "cerp/sale/stock/query_outbill_sales/out/query";
	public static String BSDZ = "http://8.142.214.170/e3/webopm/web/?app_act=api/ec&app_mode=func";
	//public static String BSDZ = "http://8.142.214.170/e3/webopm/web/?app_act=api/ec&app_mode=func";
	//public static String BSDZ = "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
	public static SimpleDateFormat sdfrqd = new SimpleDateFormat("yyyy-MM-dd");

	public static String getMd5(String plainText) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(plainText.getBytes());
			byte b[] = md.digest();

			int i;

			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			// 32位加密
			return buf.toString();
			// 16位的加密
			// return buf.toString().substring(8, 24);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			return null;
		}

	}

	public static String doPost(String url, Map<String, String> param) {

		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();

		String resultString = "";
		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			if (param != null) {
				for (String key : param.keySet()) {
					builder.addParameter(key, param.get(key));
				}
			}
			URI uri = builder.build();

			// 创建http post请求
			HttpPost httpGet = new HttpPost(uri);
			httpGet.setHeader("Content-Type", "application/json;charset=utf-8");

			// 执行请求
			response = httpclient.execute(httpGet);
			// 判断返回状态是否为200
			if (response.getStatusLine().getStatusCode() == 200) {
				resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}
	
	public static void E3ReturnListGetNew(String ks, String js) {
		
		JSONObject postjson = new JSONObject();
		int pageTotal = 1;
		
		for (int i = 1; i <= pageTotal; i++) {

			postjson = new JSONObject();
			postjson.put("time_type", 1);// 时间类型为出库时间
			postjson.put("startModified", ks);
			postjson.put("endModified", js);
			postjson.put("return_order_status", 10);
			postjson.put("pageNo", i);
			postjson.put("pageSize", 100);
			String postdata = postjson.toString();
			String result = ApiUtil.PostE3Th(postdata);
			JSONObject json = JSONObject.parseObject(result);
			// 记录调用接口完成之后的时间
			Date kssj = new Date();
			String jg = json.getString("status");
			if ("api-success".equals(jg)) {

				JSONObject data = json.getJSONObject("data");
				JSONObject page = data.getJSONObject("page");
				pageTotal = page.getIntValue("pageTotal");// 页数
				
				JSONArray orderListGets = data.getJSONArray("orderListGets");
				ApiUtil.BillSaveThNew(orderListGets);
			}
			// 记录保存单据之后的时间
			Date jssj = new Date();
			long hs = jssj.getTime() - kssj.getTime();
			// 如果时间只差小于10000毫秒 则延时
			if ((hs) < 1000) {
				try {
					Thread.sleep(1000 - hs);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}
	


	public static void E3ReturnListGet(String ks, String js) {
		JSONObject postjson = new JSONObject();
		postjson.put("time_type", 1);// 时间类型为出库时间
		postjson.put("startModified", ks);// 开始时间
		postjson.put("endModified", js);// 结束时间
		postjson.put("return_order_status", 10);// 正式需要启用

		postjson.put("pageNo", 1);// 页码
		postjson.put("pageSize", 100);// 每页数量
		String postdata = postjson.toString();
		String result = ApiUtil.PostE3Th(postdata);
		JSONObject json = JSONObject.parseObject(result);
		String jg = json.getString("status");
		if ("api-success".equals(jg)) {
//			try {
//				Thread.sleep(1000);
//			} catch (InterruptedException e1) {
//				// TODO Auto-generated catch block
//				e1.printStackTrace();
//			}
			JSONObject data = json.getJSONObject("data");
			JSONObject page = data.getJSONObject("page");
			int pageTotal = page.getIntValue("pageTotal");// 页数
			// JSONArray orderListGets = data.getJSONArray("orderListGets");
			// BillSave(orderListGets);
			for (int i = 1; i <= pageTotal; i++) {

				postjson = new JSONObject();
				postjson.put("time_type", 1);// 时间类型为出库时间
				postjson.put("startModified", ks);
				postjson.put("endModified", js);
				postjson.put("return_order_status", 10);
				postjson.put("pageNo", i);
				postjson.put("pageSize", 100);
				postdata = postjson.toString();
				result = ApiUtil.PostE3Th(postdata);
				json = JSONObject.parseObject(result);
				// 记录调用接口完成之后的时间
				Date kssj = new Date();
				jg = json.getString("status");
				if ("api-success".equals(jg)) {

					data = json.getJSONObject("data");
					page = data.getJSONObject("page");
					JSONArray orderListGets = data.getJSONArray("orderListGets");
					ApiUtil.BillSaveTh(orderListGets);
				}else {
					// 1、获取日志微服务接口
					ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
					// 2、构建日志信息，参考示例如下
					AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细退货接口失败报文"+postdata+"错误"+json.toString(), "sm", "yd_rhzfh");
					// 3、记录操作日志
					logService1.addLog(logInfo1);
				}
				// 记录保存单据之后的时间
				Date jssj = new Date();
				long hs = jssj.getTime() - kssj.getTime();
				// 如果时间只差小于10000毫秒 则延时
				if ((hs) < 1000) {
					try {
						Thread.sleep(1000 - hs);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}else {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细退货接口失败报文"+postdata+"错误"+json.toString(), "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

//	public static void E3OrderListGet(String ks, String js, String dp) {
//		JSONObject postjson = new JSONObject();
//		// postjson.put("status_th", 0);//正常单
//		postjson.put("time_type", 7);// 时间类型为发货时间
//		postjson.put("startModified", ks);// 开始时间
//		postjson.put("endModified", js);// 结束时间
//		postjson.put("shipping_status", 7);// 正式需要启用
//		postjson.put("not_decrypt", 1);// 正式需要启用
//		if (dp != "") {
//			postjson.put("sd_code", dp);// 店铺
//		}
//		// postjson.put("tags_code", "order_type_import");//手工导入
//		postjson.put("pageNo", 1);// 页码
//		postjson.put("pageSize", 100);// 每页数量
//		String postdata = postjson.toString();
//		String result = ApiFf.PostE3(postdata);
//		JSONObject json = JSONObject.parseObject(result);
//		String jg = json.getString("status");
//		if ("api-success".equals(jg)) {
////			try {
////				Thread.sleep(1000);
////			} catch (InterruptedException e1) {
////				// TODO Auto-generated catch block
////				e1.printStackTrace();
////			}
//			JSONObject data = json.getJSONObject("data");
//			JSONObject page = data.getJSONObject("page");
//			int pageTotal = page.getIntValue("pageTotal");// 页数
//			// JSONArray orderListGets = data.getJSONArray("orderListGets");
//			// BillSave(orderListGets);
//			for (int i = 1; i <= pageTotal; i++) {
//
//				postjson = new JSONObject();
//				// postjson.put("status_th", 0);//正常单
//				postjson.put("time_type", 7);// 时间类型为发货时间
//				postjson.put("startModified", ks);
//				postjson.put("endModified", js);
//				postjson.put("shipping_status", 7);
//				postjson.put("not_decrypt", 1);// 正式需要启用
//				if (dp != "") {
//					postjson.put("sd_code", dp);// 店铺
//				}
//				// postjson.put("tags_code", "order_type_import");//手工导入
//				postjson.put("pageNo", i);
//				postjson.put("pageSize", 100);
//				postdata = postjson.toString();
//				result = ApiFf.PostE3(postdata);
//				json = JSONObject.parseObject(result);
//				// 记录调用接口完成之后的时间
//				Date kssj = new Date();
//				jg = json.getString("status");
//				if ("api-success".equals(jg)) {
//
//					data = json.getJSONObject("data");
//					page = data.getJSONObject("page");
//					JSONArray orderListGets = data.getJSONArray("orderListGets");
//					ApiFf.BillSave(orderListGets);
//				}
//				// 记录保存单据之后的时间
//				Date jssj = new Date();
//				long hs = jssj.getTime() - kssj.getTime();
//				// 如果时间只差小于10000毫秒 则延时
//				if ((hs) < 1000) {
//					try {
//						Thread.sleep(1000 - hs);
//					} catch (InterruptedException e) {
//						// TODO Auto-generated catch block
//						e.printStackTrace();
//					}
//				}
//			}
//		}
//	}
	public static void MYE3ReturnListGet(String ks, String js) {
		JSONObject postjson = new JSONObject();
		postjson.put("time_type", 1);// 时间类型为出库时间
		postjson.put("startModified", ks);// 开始时间
		postjson.put("endModified", js);// 结束时间
		postjson.put("return_order_status", 10);// 正式需要启用

		postjson.put("pageNo", 1);// 页码
		postjson.put("pageSize", 100);// 每页数量
		String postdata = postjson.toString();
		String result = ApiUtil.PostMYE3Th(postdata);
		JSONObject json = JSONObject.parseObject(result);
		String jg = json.getString("status");
		if ("api-success".equals(jg)) {
//			try {
//				Thread.sleep(1000);
//			} catch (InterruptedException e1) {
//				// TODO Auto-generated catch block
//				e1.printStackTrace();
//			}
			JSONObject data = json.getJSONObject("data");
			JSONObject page = data.getJSONObject("page");
			int pageTotal = page.getIntValue("pageTotal");// 页数
			// JSONArray orderListGets = data.getJSONArray("orderListGets");
			// BillSave(orderListGets);
			for (int i = 1; i <= pageTotal; i++) {

				postjson = new JSONObject();
				postjson.put("time_type", 1);// 时间类型为出库时间
				postjson.put("startModified", ks);
				postjson.put("endModified", js);
				postjson.put("return_order_status", 10);
				postjson.put("pageNo", i);
				postjson.put("pageSize", 100);
				postdata = postjson.toString();
				result = ApiUtil.PostMYE3Th(postdata);
				json = JSONObject.parseObject(result);
				// 记录调用接口完成之后的时间
				Date kssj = new Date();
				jg = json.getString("status");
				if ("api-success".equals(jg)) {

					data = json.getJSONObject("data");
					page = data.getJSONObject("page");
					JSONArray orderListGets = data.getJSONArray("orderListGets");
					ApiUtil.BillSaveTh(orderListGets);
				}
				// 记录保存单据之后的时间
				Date jssj = new Date();
				long hs = jssj.getTime() - kssj.getTime();
				// 如果时间只差小于10000毫秒 则延时
				if ((hs) < 1000) {
					try {
						Thread.sleep(1000 - hs);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
	}
//	public static void MYE3OrderListGet(String ks, String js, String dp) {
//		JSONObject postjson = new JSONObject();
//		// postjson.put("status_th", 0);//正常单
//		postjson.put("time_type", 7);// 时间类型为发货时间
//		postjson.put("startModified", ks);// 开始时间
//		postjson.put("endModified", js);// 结束时间
//		postjson.put("shipping_status", 7);// 正式需要启用
//		if (dp != "") {
//			postjson.put("sd_code", dp);// 店铺
//		}
//		// postjson.put("tags_code", "order_type_import");//手工导入
//		postjson.put("pageNo", 1);// 页码
//		postjson.put("pageSize", 100);// 每页数量
//		String postdata = postjson.toString();
//		String result = ApiFf.PostMYE3(postdata);
//		JSONObject json = JSONObject.parseObject(result);
//		String jg = json.getString("status");
//		if ("api-success".equals(jg)) {
////			try {
////				Thread.sleep(1000);
////			} catch (InterruptedException e1) {
////				// TODO Auto-generated catch block
////				e1.printStackTrace();
////			}
//			JSONObject data = json.getJSONObject("data");
//			JSONObject page = data.getJSONObject("page");
//			int pageTotal = page.getIntValue("pageTotal");// 页数
//			// JSONArray orderListGets = data.getJSONArray("orderListGets");
//			// BillSave(orderListGets);
//			for (int i = 1; i <= pageTotal; i++) {
//
//				postjson = new JSONObject();
//				// postjson.put("status_th", 0);//正常单
//				postjson.put("time_type", 7);// 时间类型为发货时间
//				postjson.put("startModified", ks);
//				postjson.put("endModified", js);
//				postjson.put("shipping_status", 7);
//				if (dp != "") {
//					postjson.put("sd_code", dp);// 店铺
//				}
//				// postjson.put("tags_code", "order_type_import");//手工导入
//				postjson.put("pageNo", i);
//				postjson.put("pageSize", 100);
//				postdata = postjson.toString();
//				result = ApiFf.PostMYE3(postdata);
//				json = JSONObject.parseObject(result);
//				// 记录调用接口完成之后的时间
//				Date kssj = new Date();
//				jg = json.getString("status");
//				if ("api-success".equals(jg)) {
//
//					data = json.getJSONObject("data");
//					page = data.getJSONObject("page");
//					JSONArray orderListGets = data.getJSONArray("orderListGets");
//					ApiFf.BillSave(orderListGets);
//				}
//				// 记录保存单据之后的时间
//				Date jssj = new Date();
//				long hs = jssj.getTime() - kssj.getTime();
//				// 如果时间只差小于10000毫秒 则延时
//				if ((hs) < 1000) {
//					try {
//						Thread.sleep(1000 - hs);
//					} catch (InterruptedException e) {
//						// TODO Auto-generated catch block
//						e.printStackTrace();
//					}
//				}
//			}
//		}
//	}

	public static void E3Rfhhz(String lx, String rq) {
		JSONObject postjsonBB = new JSONObject();
		postjsonBB.put("type", lx);// 开始时间
		postjsonBB.put("date", rq);// 结束时间
		String postdataBB = postjsonBB.toString();
		String resultBB = ApiUtil.PostE3BB(postdataBB);
		JSONObject jsonBB = JSONObject.parseObject(resultBB);
		String jgBB = jsonBB.getString("status");
		if ("200".equals(jgBB)) {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数成功", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
			List<DynamicObject> objs = new ArrayList<DynamicObject>();
			JSONArray data = jsonBB.getJSONArray("data");
			for (int i = 0; i < data.size(); i++) {
				// String id = data.getJSONObject(i).get("id").toString();
				String sd_code = data.getJSONObject(i).get("sd_code").toString();
				String date = data.getJSONObject(i).get("date").toString();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date tiem = new Date();
				try {
					tiem = sdf.parse(date);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String type = data.getJSONObject(i).get("type").toString();
				if ("1".equals(type)) {
					type = "0";
				} else {
					type = "1";
				}
				String djbh = "E3_" + type + "_" + sd_code + "_" + date;
				QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
				DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_rhzfh", "billno",
						new QFilter[] { qFilter });
				if (dObject == null) {
					String order_num = data.getJSONObject(i).get("order_num").toString();
					String goods_num = data.getJSONObject(i).get("goods_num").toString();
					String money = data.getJSONObject(i).get("money").toString();
					// String ordersns = data.getJSONObject(i).get("ordersns").toString();
					DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_rhzfh");
					// 设置单据属性
					bill.set("billno", djbh); // 单据编号为平台_退货_店铺_日期
					Date time = new Date();
					bill.set("createtime", time);
					bill.set("billstatus", "C");
					bill.set("yd_combofield_pt", "1");
					bill.set("yd_datefield_fhrq", tiem);
					bill.set("yd_checkboxfield_th", type);
					bill.set("yd_integerfield_zs", order_num);
					bill.set("yd_textfield_dpbh", sd_code);
					bill.set("yd_decimalfield_sl", goods_num);
					bill.set("yd_decimalfield_zje", money);
					objs.add(bill);
				}
			}
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
		} else {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数失败", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
	
	public static void MYE3Rfhhz(String lx, String rq) {
		JSONObject postjsonBB = new JSONObject();
		postjsonBB.put("type", lx);// 开始时间
		postjsonBB.put("date", rq);// 结束时间
		String postdataBB = postjsonBB.toString();
		String resultBB = ApiUtil.PostMYE3BB(postdataBB);
		JSONObject jsonBB = JSONObject.parseObject(resultBB);
		String jgBB = jsonBB.getString("status");
		if ("200".equals(jgBB)) {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数成功", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
			List<DynamicObject> objs = new ArrayList<DynamicObject>();
			JSONArray data = jsonBB.getJSONArray("data");
			for (int i = 0; i < data.size(); i++) {
				// String id = data.getJSONObject(i).get("id").toString();
				String sd_code = data.getJSONObject(i).get("sd_code").toString();
				String date = data.getJSONObject(i).get("date").toString();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date tiem = new Date();
				try {
					tiem = sdf.parse(date);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String type = data.getJSONObject(i).get("type").toString();
				if ("1".equals(type)) {
					type = "0";
				} else {
					type = "1";
				}
				String djbh = "E3_" + type + "_" + sd_code + "_" + date;
				QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
				DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_rhzfh", "billno",
						new QFilter[] { qFilter });
				if (dObject == null) {
					String order_num = data.getJSONObject(i).get("order_num").toString();
					String goods_num = data.getJSONObject(i).get("goods_num").toString();
					String money = data.getJSONObject(i).get("money").toString();
					// String ordersns = data.getJSONObject(i).get("ordersns").toString();
					DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_rhzfh");
					// 设置单据属性
					bill.set("billno", djbh); // 单据编号为平台_退货_店铺_日期
					Date time = new Date();
					bill.set("createtime", time);
					bill.set("billstatus", "C");
					bill.set("yd_combofield_pt", "1");
					bill.set("yd_datefield_fhrq", tiem);
					bill.set("yd_checkboxfield_th", type);
					bill.set("yd_integerfield_zs", order_num);
					bill.set("yd_textfield_dpbh", sd_code);
					bill.set("yd_decimalfield_sl", goods_num);
					bill.set("yd_decimalfield_zje", money);
					objs.add(bill);
				}
			}
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
		} else {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数失败", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

//	public static String PostE3(String postdata) {
//		//String key = "TCBJ_E3";
//		//String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";
//		// 1、获取日志微服务接口
//		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
//		// 2、构建日志信息，参考示例如下
//		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3出库单拉单开始", "sm", "yd_fhmxb");
//		// 3、记录操作日志
//		logService1.addLog(logInfo1);
//		String version = "3.0";
//		String serviceType = "order.list.get";
//		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
//		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
//				+ "&serviceType=" + serviceType + "&data=" + postdata;
//		String sign = getMd5(JMZFC);
//		// String BSDZ ="http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
//		//String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
//		String BSDZ = "http://8.142.214.170/e3/webopm/web/?app_act=api/ec&app_mode=func";
//		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
//				+ "&serviceType=" + serviceType + "&sign=" + sign;
//		Map<String, String> map = new HashMap<>();
//		map.put("data", postdata);
//		String result = doPost(url, map);
//		logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3出库单拉单结束", "sm", "yd_fhmxb");
//		// 3、记录操作日志
//		logService1.addLog(logInfo1);
//		return result;
//	}
	// 退货
	public static String PostE3Th(String postdata) {
		//String key = "TCBJ_E3";
		//String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";
		String version = "3.0";
		String serviceType = "return_list_get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		// String BSDZ =
		// "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
		//String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	public static String PostE3BB(String postdata) {
		//String key = "TCBJ_E3";// 正式
		//String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";// 正式
		// String key = "BSAKhmSVtfmkDVGLsRLs";//测试
		// String secret = "BSAKdaVAbhYEjuLPwiOs";//测试
		
		String version = "3.0";
		String serviceType = "ptg.order.list";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api_baison/ec&app_mode=func";//测试
		String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api_baison/ec&app_mode=func";// 正式
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	public static String PostMYE3(String postdata) {
		String key = "jindie";
		String secret = "9a8b7c6d5e4f3g1h5i6j7k11";
		String version = "3.0";
		String serviceType = "order.list.get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		String BSDZ = "http://47.92.84.231/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	// 退货
	public static String PostMYE3Th(String postdata) {
		String key = "jindie";
		String secret = "9a8b7c6d5e4f3g1h5i6j7k11";
		String version = "3.0";
		String serviceType = "return_list_get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		// String BSDZ =
		// "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
		String BSDZ = "http://47.92.84.231/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	public static String PostMYE3BB(String postdata) {
		String key = "jindie";// 正式
		String secret = "9a8b7c6d5e4f3g1h5i6j7k11";// 正式
		// String key = "BSAKhmSVtfmkDVGLsRLs";//测试
		// String secret = "BSAKdaVAbhYEjuLPwiOs";//测试
		String version = "3.0";
		String serviceType = "ptg.order.list";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api_baison/ec&app_mode=func";//测试
		String BSDZ = "http://47.92.84.231/e3test/webopm/web/?app_act=api/ec&app_mode=func";// 正式
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}






	// 退货
	public static void BillSaveThNew(JSONArray orderListGets) {
		List<DynamicObject> objs = new ArrayList<DynamicObject>();

		
		for (int i = 0; i < orderListGets.size(); i++) {
			JSONObject data = orderListGets.getJSONObject(i);

			String order_sn = String.valueOf(data.get("return_order_sn"));// 退单编号
			String sd_code = String.valueOf(data.get("sd_code"));// 商店id
			String djbh = "E3_1_" + sd_code + "_" + order_sn;
			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			QFilter filter1 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno", new QFilter[] { qFilter,filter1 });
			
			if (dObject == null) {
				// 设置表头
				DynamicObject bill = BillSaveThData(data,djbh);

				// 退货单明细
				BillSaveThItem(bill,data,djbh);
				
				objs.add(bill);
			}
		}
		
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = (DynamicObject[]) objs.toArray();

			SaveServiceHelper.save(objsSAVE);
			// SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

	// 退货单明细
	private static DynamicObject BillSaveThData(JSONObject data,String djbh) {
		// 创建单据对象
		DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");

		String order_sn = String.valueOf(data.get("return_order_sn"));// 退单编号
		String sd_code = String.valueOf(data.get("sd_code"));// 商店id
		String fhck = String.valueOf(data.get("fhck"));// 发货仓库
		String orderstatus = String.valueOf(data.get("return_order_status"));// 退单状态
		String shippingstatus = String.valueOf(data.get("return_shipping_status"));// 退单物流状态：0未收货、1已收货,未入库、2已入库、3可入库、4已退回给客户
		String paystatus = String.valueOf(data.get("return_pay_status"));// 财务状态：0未结算、1已结算、2待结算
		String dealcode = String.valueOf(data.get("refund_deal_code"));// 退单交易号
		String lylx = String.valueOf(data.get("lylx"));//店铺来源类型

		String total_amount = String.valueOf(data.get("return_total_amount"));
		String order_amount = String.valueOf(data.get("return_order_amount"));
		String return_payment = String.valueOf(data.get("return_payment"));
		String other_discount_fee = String.valueOf(data.get("return_other_discount_fee"));
		BigDecimal totalamount = "null".equals(total_amount)?null:new BigDecimal(total_amount);// 订单总金额
		BigDecimal orderamount = "null".equals(order_amount)?null:new BigDecimal(order_amount);// 买家应付金额
		BigDecimal payment = "null".equals(return_payment)?null:new BigDecimal(return_payment);// 已付金额
		BigDecimal otherdiscountfee = "null".equals(other_discount_fee)?null:new BigDecimal(other_discount_fee);// 订单其他折让(整单折让)

		String shippingcode = String.valueOf(data.get("return_shipping_code"));// 快递编码
		String shippingname = String.valueOf(data.get("return_shipping_name"));// 快递名称
		String shippingsn = String.valueOf(data.get("return_shipping_sn"));// 快递单号
		String relatingordersn = String.valueOf(data.get("relating_order_sn"));// 退单关联订单号
		String qdcode = String.valueOf(data.get("qd_code"));// 渠道代码
		String qdname = String.valueOf(data.get("qd_name"));// 渠道名称
		String addtime = String.valueOf(data.get("add_time"));// 退单创建时间

		
		long shippingtimerk = Long.parseLong(String.valueOf(data.get("return_shipping_time_rk")));// 退单入库时间
		Date xdsj_time = new Date(shippingtimerk * 1000);
		String strrq = sdfrqd.format(xdsj_time);
		try {
			bill.set("yd_datefield_fhrq", sdfrqd.parse(strrq));
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		// 设置单据属性
		bill.set("billno", djbh); // 单据编号为平台_店铺_订单号
		Date time = new Date();
		bill.set("createtime", time);
		bill.set("billstatus", "C");
		bill.set("yd_combofield_pt", "1");
		bill.set("yd_textfield_ddbh", order_sn);
		bill.set("yd_datetimefield_xdsj", xdsj_time);
		bill.set("yd_textfield_dpbh", sd_code);
		bill.set("yd_checkboxfield_th", true);// 退货
		bill.set("yd_textfield_ck", fhck);
		bill.set("yd_orderstatus", orderstatus);
		bill.set("yd_shippingstatus", shippingstatus);
		bill.set("yd_paystatus", paystatus);
		bill.set("yd_dealcode", dealcode);
		bill.set("yd_lylx", lylx);
		bill.set("yd_totalamount", totalamount);
		bill.set("yd_orderamount", orderamount);
		bill.set("yd_payment", payment);
		bill.set("yd_otherdiscountfee", otherdiscountfee);
		bill.set("yd_shippingcode", shippingcode);
		bill.set("yd_shippingname", shippingname);
		bill.set("yd_shippingsn", shippingsn);
		bill.set("yd_relatingordersn", relatingordersn);
		bill.set("yd_qdcode", qdcode);
		bill.set("yd_qdname", qdname);
		bill.set("yd_addtime", StringUtils.isEmpty(addtime)?null: Timestamp.valueOf(addtime));

		return bill;
	}
	
	// 退货单明细
	private static void BillSaveThItem(DynamicObject bill,JSONObject data,String djbh) {
		// 获取单据体集合
		DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
		// 获取单据体的Type
		DynamicObjectType type = entrys.getDynamicObjectType();
		// 根据Type创建单据体对象
		JSONArray orderDetailGets = data.getJSONArray("orderDetailGets");
		
		for (int j = 0; j < orderDetailGets.size(); j++) {
			JSONObject dataItem = orderDetailGets.getJSONObject(j);

			// 设置单据体属性
			String share_price = String.valueOf(dataItem.get("share_price"));// 商品单价
			String share_payment = String.valueOf(dataItem.get("share_payment"));// 均摊实付金额
			JSONArray batchs = dataItem.getJSONArray("batchs"); // 批次信息
			
			if(batchs!=null){
				int zs=batchs.size();
				
				if(zs>1){
					BigDecimal ljje=BigDecimal.ZERO;//记录累计金额
					
					for (int k = 0; k < zs; k++) {
						JSONObject itemInfo = batchs.getJSONObject(k);
						String goods_number = String.valueOf(itemInfo.get("goods_number"));// 商品数量
						
						BigDecimal bcje=BigDecimal.ZERO;
						if(k<zs-1){ 
							//不为最后一行 按照单价*数量计算金额
							bcje=new BigDecimal(share_price).multiply(new BigDecimal(goods_number));//当前行批次金额
							ljje=ljje.add(bcje);
						}else{
							//最后一行使用总金额-累计金额计算金额
							bcje=new BigDecimal(share_payment).subtract(ljje);
						}
						DynamicObject entry = setEntry(type,dataItem,djbh);	// 设置明细通用属性

						String ph = String.valueOf(itemInfo.get("batch_no"));
						entry.set("yd_decimalfield_sl", goods_number);
						entry.set("yd_decimalfield_zje", bcje);
						entry.set("yd_ph", ph);
						// 添加到单据体集合
						entrys.add(entry);
					}
				}else{
					DynamicObject entry = setEntry(type,dataItem,djbh);  // 设置明细通用属性
					
					String ph = batchs.getJSONObject(0).get("batch_no").toString();
					entry.set("yd_ph", ph);
					// 添加到单据体集合
					entrys.add(entry);
				}
			}else{
				DynamicObject entry = setEntry(type,dataItem,djbh);	// 设置明细通用属性
				// 添加到单据体集合
				entrys.add(entry);
			}

		}
	}
	
	// 设置单据体信息
	private static DynamicObject setEntry(DynamicObjectType type,JSONObject dataItem,String djbh) {
		DynamicObject entry = new DynamicObject(type);
		
		String sku = String.valueOf(dataItem.get("sku"));// sku
		String share_price = String.valueOf(dataItem.get("share_price"));// 单价
		String share_payment = String.valueOf(dataItem.get("share_payment"));// 均摊实付金额
		String goods_number = String.valueOf(dataItem.get("goods_number"));// 商品数量
		String rowid = String.valueOf(dataItem.get("id"));// id
		String goodsname = String.valueOf(dataItem.get("goods_name"));// 商品名
		String skuid = String.valueOf(dataItem.get("sku_id"));// sku_id
		String goodssn = String.valueOf(dataItem.get("goods_sn"));// 货号
		String goodsid = String.valueOf(dataItem.get("goods_id"));// 货号id
		String numberreturnsj = String.valueOf(dataItem.get("goods_number_return_sj"));// 实际入库数量
		String barcode = String.valueOf(dataItem.get("barcode"));// barcode

		String goods_price = String.valueOf(dataItem.get("goods_price"));
		String shop_price = String.valueOf(dataItem.get("shop_price"));
		BigDecimal goodsprice = "null".equals(goods_price)?null:new BigDecimal(goods_price); // 商品单价
		BigDecimal shopprice = "null".equals(shop_price)?null:new BigDecimal(shop_price);// 商品网店在售价格


		entry.set("yd_textfield_hpbh", sku);
		entry.set("yd_decimalfield_sl", goods_number);
		entry.set("yd_decimalfield_dj", share_price);
		entry.set("yd_decimalfield_zje", share_payment);
		entry.set("yd_rowid", rowid);
		entry.set("yd_goodssn", goodssn);
		entry.set("yd_goodsname", goodsname);
		entry.set("yd_skuid", skuid);
		entry.set("yd_goodsid", goodsid);
		entry.set("yd_numberreturnsj", numberreturnsj);
		entry.set("yd_goodsprice", goodsprice);
		entry.set("yd_shopprice", shopprice);
		entry.set("yd_barcode", barcode);
		
		return entry;
	}
	
	// 退货
	public static void BillSaveTh(JSONArray orderListGets) {
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < orderListGets.size(); i++) {
			String order_sn = orderListGets.getJSONObject(i).get("return_order_sn").toString();// 退单编号
			String sd_code = orderListGets.getJSONObject(i).get("sd_code").toString();// 商店id
			String djbh = "E3_1_" + sd_code + "_" + order_sn;
//			System.out.println(orderListGets.getJSONObject(i).toJSONString());
//			System.out.println(djbh);
			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno",
					new QFilter[] { qFilter });
			if (dObject == null) {
				long add_time = Long
						.parseLong(orderListGets.getJSONObject(i).get("return_shipping_time_rk").toString());// 退单入库时间
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				// String sj=sdf.format(new Date(add_time));
				Date tiem = new Date(add_time * 1000);
				SimpleDateFormat sdfrq = new SimpleDateFormat("yyyy-MM-dd");
				String glrq="2021-11-01 00:00:00";
				Date timerq = new Date();
				Date timeglrq = new Date();
				String strrq = sdfrq.format(tiem);
				try {
					timerq = sdfrq.parse(strrq);
					timeglrq = sdf.parse(glrq);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				//if(tiem.after(timeglrq))//不取11月1日之前的单据
				//{
				String fhck = orderListGets.getJSONObject(i).get("fhck").toString();// 发货仓库

				String orderstatus = String.valueOf(orderListGets.getJSONObject(i).get("return_order_status"));// 退单状态
				String shippingstatus = String.valueOf(orderListGets.getJSONObject(i).get("return_shipping_status"));// 退单物流状态：0未收货、1已收货,未入库、2已入库、3可入库、4已退回给客户
				String paystatus = String.valueOf(orderListGets.getJSONObject(i).get("return_pay_status"));// 财务状态：0未结算、1已结算、2待结算
				String dealcode = String.valueOf(orderListGets.getJSONObject(i).get("refund_deal_code"));// 退单交易号
				String lylx = String.valueOf(orderListGets.getJSONObject(i).get("lylx"));//店铺来源类型
				String shippingcode = String.valueOf(orderListGets.getJSONObject(i).get("return_shipping_code"));// 快递编码
				String shippingname = String.valueOf(orderListGets.getJSONObject(i).get("return_shipping_name"));// 快递名称
				String shippingsn = String.valueOf(orderListGets.getJSONObject(i).get("return_shipping_sn"));// 快递单号
				String relatingordersn = String.valueOf(orderListGets.getJSONObject(i).get("relating_order_sn"));// 退单关联订单号
				String qdcode = String.valueOf(orderListGets.getJSONObject(i).get("qd_code"));// 渠道代码
				String qdname = String.valueOf(orderListGets.getJSONObject(i).get("qd_name"));// 渠道名称
				String addtime = String.valueOf(orderListGets.getJSONObject(i).get("add_time"));// 退单创建时间
				String total_amount = String.valueOf(orderListGets.getJSONObject(i).get("return_total_amount"));
				String order_amount = String.valueOf(orderListGets.getJSONObject(i).get("return_order_amount"));
				String return_payment = String.valueOf(orderListGets.getJSONObject(i).get("return_payment"));
				String other_discount_fee = String.valueOf(orderListGets.getJSONObject(i).get("return_other_discount_fee"));
				BigDecimal totalamount = "null".equals(total_amount)?null:new BigDecimal(total_amount);// 订单总金额
				BigDecimal orderamount = "null".equals(order_amount)?null:new BigDecimal(order_amount);// 买家应付金额
				BigDecimal payment = "null".equals(return_payment)?null:new BigDecimal(return_payment);// 已付金额
				BigDecimal otherdiscountfee = "null".equals(other_discount_fee)?null:new BigDecimal(other_discount_fee);// 订单其他折让(整单折让)
				String provincename = String.valueOf(orderListGets.getJSONObject(i).get("receiver_province_name"));// 省
				String cityname = String.valueOf(orderListGets.getJSONObject(i).get("receiver_city_name"));// 市
				String districtname = String.valueOf(orderListGets.getJSONObject(i).get("receiver_district_name"));// 区
				String address = String.valueOf(orderListGets.getJSONObject(i).get("receiver_address"));// 收货地址

				//String shipping_fee = orderListGets.getJSONObject(i).get("shipping_fee").toString();// 运费
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
				//是否批次异常
				boolean isBatchError = false;
				//是否金额异常
				boolean isAmtError = false;
				//异常状态
				int errorState = 0;
				// 设置单据属性
				bill.set("billno", djbh); // 单据编号为平台_店铺_订单号

				Date time = new Date();
				bill.set("createtime", time);
				bill.set("billstatus", "C");
				bill.set("yd_combofield_pt", "1");
				bill.set("yd_textfield_ddbh", order_sn);
				bill.set("yd_datetimefield_xdsj", tiem);
				bill.set("yd_textfield_dpbh", sd_code);
				//bill.set("yd_decimalfield_yf", shipping_fee);
				// bill.set("yd_checkboxfield_sfkp", "0");//开票
				// bill.set("yd_checkboxfield_sfsg", "0");//手工
				bill.set("yd_checkboxfield_th", true);// 退货
				bill.set("yd_datefield_fhrq", timerq);
				bill.set("yd_textfield_ck", fhck);

				bill.set("yd_ordertype", "1");
				bill.set("yd_orderstatus", orderstatus);
				bill.set("yd_shippingstatus", shippingstatus);
				bill.set("yd_paystatus", paystatus);
				bill.set("yd_dealcode", dealcode);
				bill.set("yd_lylx", lylx);
				bill.set("yd_totalamount", totalamount);
				bill.set("yd_orderamount", orderamount);
				bill.set("yd_payment", payment);
				bill.set("yd_otherdiscountfee", otherdiscountfee);
				bill.set("yd_shippingcode", shippingcode);
				bill.set("yd_shippingname", shippingname);
				bill.set("yd_shippingsn", shippingsn);
				bill.set("yd_relatingordersn", relatingordersn);
				bill.set("yd_qdcode", qdcode);
				bill.set("yd_qdname", qdname);
				bill.set("yd_addtime", StringUtils.isEmpty(addtime)?null: Timestamp.valueOf(addtime));
				bill.set("yd_provincename", provincename);
				bill.set("yd_cityname", cityname);
				bill.set("yd_districtname",districtname);
				bill.set("yd_address", address);

				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray orderDetailGets = orderListGets.getJSONObject(i).getJSONArray("orderDetailGets");
				for (int j = 0; j < orderDetailGets.size(); j++) {
					//DynamicObject entry = new DynamicObject(type);
					// 设置单据体属性
					String sku = orderDetailGets.getJSONObject(j).get("sku").toString();// sku
					//String goods_number = orderDetailGets.getJSONObject(j).get("goods_number_return_sj").toString();// 商品数量
					String share_price = orderDetailGets.getJSONObject(j).get("share_price").toString();// 商品单价
					String share_payment = orderDetailGets.getJSONObject(j).get("share_payment").toString();// 均摊实付金额
					JSONArray batchs = orderDetailGets.getJSONObject(j).getJSONArray("batchs");

					String rowid = String.valueOf(orderDetailGets.getJSONObject(j).get("line_no"));// id
					String goodsname = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_name"));// 商品名
					String skuid = String.valueOf(orderDetailGets.getJSONObject(j).get("sku_id"));// sku_id
					String goodssn = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_sn"));// 货号
					String goodsid = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_id"));// 货号id
					String numberreturnsj = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_number_return_sj"));// 实际入库数量
					String barcode = String.valueOf(orderDetailGets.getJSONObject(j).get("barcode"));// barcode
					String goods_price = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_price"));
					String shop_price = String.valueOf(orderDetailGets.getJSONObject(j).get("shop_price"));
					BigDecimal goodsprice = "null".equals(goods_price)?null:new BigDecimal(goods_price); // 商品单价
					BigDecimal shopprice = "null".equals(shop_price)?null:new BigDecimal(shop_price);// 商品网店在售价

					//判断分录总金额如果少于0，则标记金额异常
					BigDecimal totalAmt = new BigDecimal(share_payment);
					if(totalAmt.compareTo(BigDecimal.ZERO)<0)
					{
						isAmtError = true;
					}
					//循环batchs中的goods_number合计，并对比orderDetailGets中的goods_number是否一致，如不一致，标记批次异常为是
					//orderDetailGets中的数量
					BigDecimal orderDetailQty = new BigDecimal(orderDetailGets.getJSONObject(j).get("goods_number_return_sj").toString());
					//batchs中的数量合计
					BigDecimal batchsTotal = new BigDecimal(0);
					//判断批次是否为空
					if(batchs!=null)
					{
						for(int z=0;z<batchs.size();z++)
						{
							BigDecimal goods_number = new BigDecimal(batchs.getJSONObject(z).get("goods_number").toString());// 商品数量
							batchsTotal = batchsTotal.add(goods_number);
						}
						//如果两个数量不相等，属于批次异常
						if(orderDetailQty.compareTo(batchsTotal)!=0)
						{
							isBatchError = true;
						}
					}
					
					if(batchs!=null)
					{
						int zs=batchs.size();
//						if(zs>1)
//						{
							BigDecimal ljje=BigDecimal.ZERO;//记录累计金额
							for (int k = 0; k < zs; k++) 
							{
								//不为最后一行 按照单价*数量计算金额
								if(k<zs-1)
								{
									String goods_number = batchs.getJSONObject(k).get("goods_number").toString();// 商品数量
									DynamicObject entry = new DynamicObject(type);
									entry.set("yd_textfield_hpbh", sku);
									entry.set("yd_decimalfield_sl", goods_number);
									entry.set("yd_decimalfield_dj", share_price);
									BigDecimal bcje=new BigDecimal(share_price).multiply(new BigDecimal(goods_number));//当前行批次金额
									ljje=ljje.add(bcje);
									entry.set("yd_decimalfield_zje",bcje);
									String ph = batchs.getJSONObject(k).get("batch_no").toString();
									entry.set("yd_ph", ph);

									entry.set("yd_rowid", rowid);
									entry.set("yd_goodssn", goodssn);
									entry.set("yd_goodsname", goodsname);
									entry.set("yd_skuid", skuid);
									entry.set("yd_goodsid", goodsid);
									entry.set("yd_numberreturnsj", numberreturnsj);
									entry.set("yd_goodsprice", goodsprice);
									entry.set("yd_shopprice", shopprice);
									entry.set("yd_barcode", barcode);
									// 添加到单据体集合
									entrys.add(entry);
								}
								//最后一行使用总金额-累计金额计算金额
								else
								{
									String goods_number = batchs.getJSONObject(k).get("goods_number").toString();// 商品数量
									DynamicObject entry = new DynamicObject(type);
									entry.set("yd_textfield_hpbh", sku);
									entry.set("yd_decimalfield_sl", goods_number);
									entry.set("yd_decimalfield_dj", share_price);
									BigDecimal bcje=new BigDecimal(share_payment).subtract(ljje);
									entry.set("yd_decimalfield_zje",bcje);
									String ph = batchs.getJSONObject(k).get("batch_no").toString();
									entry.set("yd_ph", ph);

									entry.set("yd_rowid", rowid);
									entry.set("yd_goodssn", goodssn);
									entry.set("yd_goodsname", goodsname);
									entry.set("yd_skuid", skuid);
									entry.set("yd_goodsid", goodsid);
									entry.set("yd_numberreturnsj", numberreturnsj);
									entry.set("yd_goodsprice", goodsprice);
									entry.set("yd_shopprice", shopprice);
									entry.set("yd_barcode", barcode);
									// 添加到单据体集合
									entrys.add(entry);
								}
							}
//						}
//						else
//						{
//							String goods_number = orderDetailGets.getJSONObject(j).get("goods_number_return_sj").toString();// 商品数量
//							DynamicObject entry = new DynamicObject(type);
//							entry.set("yd_textfield_hpbh", sku);
//							entry.set("yd_decimalfield_sl", goods_number);
//							entry.set("yd_decimalfield_dj", share_price);
//							entry.set("yd_decimalfield_zje", share_payment);
//							String ph = batchs.getJSONObject(0).get("batch_no").toString();
//							entry.set("yd_ph", ph);
//
//							entry.set("yd_rowid", rowid);
//							entry.set("yd_goodssn", goodssn);
//							entry.set("yd_goodsname", goodsname);
//							entry.set("yd_skuid", skuid);
//							entry.set("yd_goodsid", goodsid);
//							entry.set("yd_numberreturnsj", numberreturnsj);
//							entry.set("yd_goodsprice", goodsprice);
//							entry.set("yd_shopprice", shopprice);
//							entry.set("yd_barcode", barcode);
//							// 添加到单据体集合
//							entrys.add(entry);
//						}
					}
					else
					{
						String goods_number = orderDetailGets.getJSONObject(j).get("goods_number_return_sj").toString();// 商品数量
						DynamicObject entry = new DynamicObject(type);
						entry.set("yd_textfield_hpbh", sku);
						entry.set("yd_decimalfield_sl", goods_number);
						entry.set("yd_decimalfield_dj", share_price);
						entry.set("yd_decimalfield_zje", share_payment);

						entry.set("yd_rowid", rowid);
						entry.set("yd_goodssn", goodssn);
						entry.set("yd_goodsname", goodsname);
						entry.set("yd_skuid", skuid);
						entry.set("yd_goodsid", goodsid);
						entry.set("yd_numberreturnsj", numberreturnsj);
						entry.set("yd_goodsprice", goodsprice);
						entry.set("yd_shopprice", shopprice);
						entry.set("yd_barcode", barcode);
						// 添加到单据体集合
						entrys.add(entry);
					}

				}
				
				Boolean iserror = false;
				//根据异常标识标记异常状态
				if(isBatchError && isAmtError)
				{
					errorState = 3;
					iserror = true;
				}
				else if(isBatchError)
				{
					errorState = 2;
					iserror = true;
				}
				else if(isAmtError)
				{
					errorState = 1;
					iserror = true;
				}
				//单据异常
				bill.set("yd_iserror", iserror);
				//异常状态
				bill.set("yd_errorstate", errorState);
				objs.add(bill);
				//}
			}
		}
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

	public static void bcbhzdj(String lx, List<String> pcwlin, List<String> fywl,List<String> ppfd, String rq) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter7 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不为手工
		QFilter filter8 = QFilter.of("yd_checkboxfield_sfkp =?", "0");// 不为开票
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		QFilter filter9 = QFilter.of("yd_checkboxfield_sfsg =?", "1");// 手工
		QFilter filter10 = QFilter.of("yd_checkboxfield_sfkp =?", "1");// 开票
		QFilter filter13 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不手工
		QFilter filter14 = QFilter.of("yd_textfield_sbyy =?", "");// 失败原因为空 此条用于过滤参与了合并汇总但是生成失败的单据
		QFilter filter15 = QFilter.of("yd_checkboxfield_wlwsh =?", "0");// 物料合规
		QFilter filter16 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter17 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 去除按照品牌分单店铺
		QFilter filter18 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter19 = QFilter.of("yd_dygxcf =?", "0");// 物料对应关系重复为否
		QFilter filter20 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
//		QFilter filter17 = QFilter.of("yd_combofield_pt =?", "1");// 平台为佰悦e3
//		QFilter filter18 = QFilter.of("yd_datefield_fhrq <=?", "2021-12-11");// 日期小于等于20号
//		QFilter filter19 = QFilter.of("yd_combofield_pt !=?", "1");// 平台不为佰悦e3
		// QFilter filter12 = QFilter.of("to_char(yd_datetimefield_xdsj,'yyyy-MM-dd') =
		// ?", rq);// 日期
		QFilter filter12 = QFilter.of("yd_datefield_fhrq = ?", rq);// 日期
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter11 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,
				filter14, filter15, filter12, filter11,filter16,filter17,filter18,filter19,filter20 };
		String ZD = "billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf" +
                ",yd_textfield_ck ptck,yd_entryentity.yd_textfield_hpbh ptwl,yd_entryentity.yd_decimalfield_sl sl" +
                ",yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je,yd_entryentity.yd_ph ph" +
                ",yd_entryentity.yd_scrq scrq,yd_entryentity.yd_dqr dqr,yd_bz bz,yd_dealcode dealcode";
		// 查询发货明细 
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		// 查询手工单据
		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter9, filter11,filter14, filter15, filter12,filter16,filter17,filter18,filter19 };
		DataSet yd_fhmxbsg = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		// 查询为开票并不为手工的单据 
		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter10, filter11, filter13,filter14, filter15, filter12,filter16,filter17,filter18,filter19 };
		DataSet yd_fhmxbkp = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		// union发货明细
		yd_fhmxb = yd_fhmxb.union(yd_fhmxbsg).union(yd_fhmxbkp);
		// 查询已审核客户
		QFilter filterbhz = null;
		if ("5".equals(lx)) {
			String[] hz = new String[] { "4", "5" };
			filterbhz = new QFilter("yd_entryentity.yd_combofield_hzcl", QCP.in, hz);
		} else {
			String[] hz = new String[] { "1", "2" };
			filterbhz = new QFilter("yd_entryentity.yd_combofield_hzcl", QCP.in, hz);
		}
		fhmxgl = new QFilter[] { filterbhz, filterSh };
		DataSet dswl = glmx(fywl, yd_fhmxb, fhmxgl);
		// DataSet ds = dswl.copy();
		DataSet dataSetgroupBy = dswl.copy().select(new String[] { "billno",  "ptkh","th", "kh", "ck", "zz", "yf", "pt"
                ,"kcsw","thkcsw","quyu","bz","dealcode" })
				.groupBy(new String[] { "billno", "ptkh","th", "kh", "ck", "zz", "yf", "pt","kcsw","thkcsw","quyu"
                        ,"bz","dealcode" }).finish();
		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String dj = "priceandtax";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型
		if ("5".equals(lx)) {
			formid = "im_otheroutbill";
			djlx = "im_OtherOutBill_STD_BT_S";
			biztypenumber = "355";
			dj = "price";
			je = "amount";
			xyd = "2";
		}
		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz");
			Boolean th = row.getBoolean("th");
			String ckbm = row.getString("ck");
			String dpbm="";
			String dealCode = row.getString("dealCode");
			if(th) {
				// 查询仓库对应组织
				filter1 = new QFilter("number", QCP.equals, ckbm);
				DynamicObject khojb = BusinessDataServiceHelper.loadSingle("bd_warehouse", "createorg.id",
						new QFilter[] { filter1 });
				zzbm = khojb.getString("createorg.id");
			}
			String khbm = row.getString("kh");
			String pt = row.getString("pt");
			// 查询客户对应税率
			filter1 = new QFilter("number", QCP.equals, khbm);
			DynamicObject[] khojb = BusinessDataServiceHelper.load("bd_customer", "taxrate.number",
					new QFilter[] { filter1 });
			String slbm = "";
			for (DynamicObject rowdy : khojb) {
				slbm = rowdy.getString("taxrate.number");
			}
			IFormView view = null;
			view = ABillServiceHelper.createAddView(formid);
			view.getModel().setItemValueByNumber("billtype", djlx);
			view.getModel().setValue("org", zzbm);
			if (lx != "5") {
				view.getModel().setValue("bizorg", zzbm);
				view.getModel().setValue("yd_dealcode", dealCode);
			}
			if (th) {
				if ("5".equals(lx)) {
					view.getModel().setItemValueByNumber("biztype", "3551");
				} else {
					view.getModel().setItemValueByNumber("biztype", "2101");
					view.getModel().setItemValueByNumber("invscheme", "2101");
				}
			} else {
				view.getModel().setItemValueByNumber("biztype", biztypenumber);
				view.getModel().setItemValueByNumber("invscheme", "210");
			}
			if("5".equals(lx))
			{
				String kcsw="0";
				if(th)
				{
					//kcsw = row.getString("thkcsw");
					filter1 = new QFilter("yd_entryentity.yd_orgfield_dyzz", QCP.equals, zzbm);
					filter2 = new QFilter("yd_entryentity.yd_thkcsw", QCP.not_equals, "0");
					DataSet kcswjb = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
							"yd_entryentity.yd_thkcsw thkcsw",
							new QFilter[] { filter1,filter2 }, null).groupBy(new String[] {"thkcsw"}).finish();
					for (Row rowkcsw : kcswjb) {
						kcsw = rowkcsw.getString("thkcsw");
					}
				}
				else
				{
					kcsw = row.getString("kcsw");
				}
				if(kcsw!="0")
				{
					view.getModel().setValue("invscheme", kcsw);
				}
				String quyu = row.getString("quyu");
				view.getModel().setValue("yd_quyu", quyu);
				String bz = row.getString("bz");
				view.getModel().setValue("comment", bz);
				dpbm = row.getString("ptkh");
			}

			view.getModel().setItemValueByNumber("customer", khbm);
			view.getModel().setItemValueByNumber("yd_dzdkh", khbm);
			view.getModel().setValue("biztime", rq);
			view.getModel().setValue("yd_tbly", pt);
			BigDecimal yf = row.getBigDecimal("yf");
			BigDecimal zje = BigDecimal.ZERO;// 整单总金额
			String billno = row.getString("billno");
			DataSet dsxh = dswl.copy();
			//dsxh = dsxh.filter("billno=" + "'" + billno + "'").select(new String[] { "wl", "sl", "je", "dj","ph" });
			dsxh = dsxh.filter("billno=" + "'" + billno + "'").select(new String[] { "wl", "sl", "je", "dj" });
			int i = 0;
			for (Row rowds : dsxh) {
				String wlbm = rowds.getString("wl");// sku
				//String ph = rowds.getString("ph");// sku
				BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
				BigDecimal goods_price = rowds.getBigDecimal("dj");// 商品单价
				BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
				boolean sffy = true;
				for (String fywlbm : fywl) {
					if (wlbm.equals(fywlbm)) {
						yf = yf.add(share_payment);
						sffy = false;
						break;
					}
				}
				if (sffy) {
					if (i > 0) {
						view.getModel().createNewEntryRow("billentry");
					}
					view.getModel().setItemValueByNumber("material", wlbm, i);
					view.getModel().setItemValueByNumber("warehouse", ckbm, i);
					view.getModel().setValue("qty", goods_number, i);
					//view.getModel().setValue("yd_ph", ph, i);
					view.getModel().setValue(dj, goods_price, i);
					view.getModel().setValue(je, share_payment, i);
					if("5".equals(lx))
					{
					view.getModel().setValue("yd_store", dpbm,i);
					}
					if (share_payment.compareTo(BigDecimal.ZERO) == 0 && lx.equals("2")) {
						view.getModel().setValue("ispresent", "1", i);
					}

					if (slbm != null) {
						if ("2".equals(lx)) {
							view.getModel().setItemValueByNumber("taxrateid", slbm, i);
						}
					}
					zje = zje.add(share_payment);
					i++;
				}
			}
			view.getModel().setValue("yd_amountfield_yf", yf);
			view.getModel().setValue("yd_textfield_fhmxdh", billno);
//			if (yf.compareTo(BigDecimal.ZERO) > 0) {
//				// 处理分摊
//				DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
//				BigDecimal ljft = BigDecimal.ZERO;// 记录已分摊的运费金额
//				for (int j = 0; j < entrys.getRowCount(); j++) {
//					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
//					if (j < entrys.getRowCount() - 1) {
//						BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
//						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
//						ljft = ljft.add(yfft);
//						view.getModel().setValue(je, hsjeft, j);
//					} else {
//						BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
//						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
//						view.getModel().setValue(je, hsjeft, j);
//					}
//				}
//			}
			if (yf.compareTo(BigDecimal.ZERO) > 0) {
			// 处理分摊
			DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
			int hs=0;
			for (int j = 0; j < entrys.getRowCount(); j++) {
				BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
				if(hsje.compareTo(BigDecimal.ZERO) > 0)
				{
					hs++;
				}
			}
			BigDecimal ljft = BigDecimal.ZERO;
			int jehs=0;
			for (int j = 0; j < entrys.getRowCount(); j++) {
				BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
				if(hsje.compareTo(BigDecimal.ZERO) > 0)
				{
					jehs++;
				if (jehs < hs) {
					BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
					BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
					ljft = ljft.add(yfft);
					view.getModel().setValue(je, hsjeft, j);
				} else {
					BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
					BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
					view.getModel().setValue(je, hsjeft, j);
				}
				}
			}
		}
			if (i > 0) {
				OperationResult operationResult = ABillServiceHelper.saveOperate(view);
				filter1 = new QFilter("billno", QCP.equals, billno);
				// 判断保存结果
				if (operationResult.isSuccess()) {
					String djbh = "";
					Map<Object, String> map = operationResult.getBillNos();
					for (Map.Entry<Object, String> entry : map.entrySet()) {
						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
								"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							djbh = entry.getValue();
							rowdy.set("yd_textfield_xsckdh", entry.getValue());// 修改数据
							rowdy.set("yd_combofield_xyd", xyd);// 修改数据
						}
						SaveServiceHelper.save(dy);
					}
					// 进行提交操作
//					IFormView modifyView = ABillServiceHelper.createModifyView(view.getEntityId(),
//							operationResult.getSuccessPkIds().get(0).toString());
					operationResult = view.invokeOperation("submit");
					if (!operationResult.isSuccess()) {
						String errMessage = operationResult.getMessage() + ","; // 错误摘要
						// // 演示提取保存详细错误
						for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
							errMessage += errInfo.getMessage() + ",";
						}
						if (errMessage.length() > 1800) {
							errMessage = errMessage.substring(0, 1800);
						}
						filter1 = new QFilter("billno", QCP.equals, djbh);
						DynamicObject[] dy = BusinessDataServiceHelper.load(formid, "yd_sftj",
								new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_sftj", errMessage);// 修改数据
						}
						SaveServiceHelper.save(dy);
					}
					ABillServiceHelper.exitView(view);
				} else {
					String errMessage = operationResult.getMessage() + ","; // 错误摘要
					// // 演示提取保存详细错误
					for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
						errMessage += errInfo.getMessage() + ",";
					}
					if (errMessage.length() > 1800) {
						errMessage = errMessage.substring(0, 1800);
					}
					DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
							new QFilter[] { filter1 });
					for (DynamicObject rowdy : dy) {
						rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
					}
					SaveServiceHelper.save(dy);
					ABillServiceHelper.exitView(view);
				}
			} else {
				ABillServiceHelper.exitView(view);
			}
		}
		dswl.close();
	}
	

	public static DataSet glmx(List<String> fywl, DataSet ds, QFilter[] fhmxgl) {

		QFilter filterSh = QFilter.of("billstatus=?", "C");

		// 查询对应客户
		DataSet khdygx = QueryServiceHelper.queryDataSet("glmx", "yd_khdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number kh,yd_entryentity.yd_orgfield_dyzz zz,yd_entryentity.yd_kcsw kcsw,yd_entryentity.yd_quyu quyu,yd_entryentity.yd_thkcsw thkcsw",
				fhmxgl, null);
		fhmxgl = new QFilter[] { filterSh };
		// 查询对应仓库
		DataSet ckdygx = QueryServiceHelper.queryDataSet("glmx", "yd_ckdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number ck,yd_entryentity.yd_basedatafield.createorg ckzz", fhmxgl,
				null);
		// 查询对应物料
		DataSet wldygx = QueryServiceHelper.queryDataSet("glmx", "yd_wldygx",
				"yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number wl,yd_entryentity.yd_pp pp,yd_combofield_pt pt", fhmxgl,
				null);
		//过滤e3物料对应关系物料
		List<String> wldy01 = new ArrayList<String>();
		for (Row row : wldygx.copy().filter("pt='1'").select("bm")) {
			wldy01.add(row.getString("bm"));
		}
		//过滤旺店通物料对应关系物料
		List<String> wldy02 = new ArrayList<String>();
		for (Row row : wldygx.copy().filter("pt='2'").select("bm")) {
			wldy02.add(row.getString("bm"));
		}
		QFilter filterwldye3 = new QFilter("helpcode", QCP.not_in, wldy01);//不包含对应表商品e3
		QFilter filterwldywdt = new QFilter("number", QCP.not_in, wldy02);//不包含对应表商品旺店通
		filterSh = QFilter.of("status=?", "C");
		// 查询已审核的物料编码
		DataSet wlbm = QueryServiceHelper.queryDataSet("glmx", "bd_material", "number bm,number wl,yd_basedatafield pp", new QFilter[] { filterSh,filterwldywdt }, null);
		wlbm=wlbm.addField("'2'", "pt");
		// 查询已审核的物料助记码
		DataSet wlzjm = QueryServiceHelper.queryDataSet("glmx", "bd_material", "helpcode bm,number wl,yd_basedatafield pp", new QFilter[] { filterSh,filterwldye3 }, null);
		wlzjm=wlzjm.addField("'1'", "pt");
		wldygx=wldygx.union(wlbm).union(wlzjm).groupBy(new String[]{"bm","pt","wl","pp"}).finish();
		//wldygx.copy().print(false);
		String[] fhmxbzd = new String[] { "billno","th", "yf", "ptck", "ptwl", "sl", "dj", "je", "pt","kcsw","thkcsw"
                ,"quyu", "ptkh","ph","scrq","dqr","bz","dealcode"};
		// 关联客户
		ds = ds.join(khdygx).on("pt", "pt").on("ptkh", "bm").select(fhmxbzd, new String[] { "kh", "zz" }).finish();
		//ds.print(true);
		// 关联仓库
		fhmxbzd = new String[] { "billno", "th", "yf", "ptwl", "sl", "dj", "je", "pt", "kh", "zz","kcsw","thkcsw","quyu"
                , "ptkh","ph","scrq","dqr","bz","dealcode"};
		ds = ds.join(ckdygx).on("pt", "pt").on("ptck", "bm").select(fhmxbzd, new String[] { "ck","ckzz" }).finish();
		//ds.print(true);
		// 关联费用物料
		Map<String, Object> mapfywl = new HashMap<>();
		mapfywl.put("fywl", fywl);
		fhmxbzd = new String[] { "billno", "th", "yf", "sl", "dj", "je", "pt", "kh", "zz", "ck","ckzz" ,"kcsw","thkcsw"
                ,"quyu",  "ptkh","ph","scrq","dqr","bz" ,"ptwl","dealcode"};
		DataSet dsfywl = ds.filter("ptwl in fywl", mapfywl).select(fhmxbzd);
		// 关联物料
		fhmxbzd = new String[] { "billno", "th", "yf", "sl", "dj", "je", "pt", "kh", "zz", "ck","ckzz" ,"kcsw","thkcsw"
                ,"quyu", "ptkh","ph","scrq","dqr","bz","dealcode"};
		//ds.print(true);
		ds = ds.join(wldygx).on("pt", "pt").on("ptwl", "bm").select(fhmxbzd, new String[] { "wl","pp" }).finish();
		//ds.copy().print(true);
		// ds.print(true);
		// dsfywl.print(true);
		ds = ds.union(dsfywl.addField("1", "pp"));
		//ds.print(true);
		return ds;
	}

	public static void bchbdj(String lx, List<String> pcwlin, List<String> fywl,List<String> ppfd, String rq) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter7 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不为手工
		QFilter filter8 = QFilter.of("yd_checkboxfield_sfkp =?", "0");// 不为开票
		QFilter filter9 = QFilter.of("yd_checkboxfield_wlwsh =?", "0");// 物料合规
		QFilter filter16 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter17 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 去除按照品牌分单店铺
		QFilter filter18 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter19 = QFilter.of("yd_dygxcf =?", "0");// 物料对应关系重复为否
		QFilter filter20 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
//		QFilter filter17 = QFilter.of("yd_combofield_pt =?", "1");// 平台为佰悦e3
//		QFilter filter18 = QFilter.of("yd_datefield_fhrq <=?", "2021-12-11");// 日期小于等于20号
//		QFilter filter19 = QFilter.of("yd_combofield_pt !=?", "1");// 平台不为佰悦e3
		// QFilter filter12 = QFilter.of("to_char(yd_datetimefield_xdsj,'yyyy-MM-dd') =
		// ?", rq);// 日期
		QFilter filter12 = QFilter.of("yd_datefield_fhrq = ?", rq);// 日期
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter11 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,
				filter9, filter12, filter11,filter16,filter17,filter18,filter19,filter20 };
		// 查询发货明细
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb",
				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf" +
                        ",yd_textfield_ck ptck,yd_entryentity.yd_textfield_hpbh ptwl,yd_entryentity.yd_decimalfield_sl sl" +
                        ",yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je,yd_entryentity.yd_ph ph" +
                        ",yd_entryentity.yd_scrq scrq,yd_entryentity.yd_dqr dqr,yd_bz bz,yd_dealcode dealcode",
				fhmxgl, null);
		// 查询运费汇总 不需要去除排除物料 
		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter7, filter8, filter9, filter12,
				filter11,filter16,filter17,filter18,filter19 };
		DataSet yfhz = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb",
				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf",
				fhmxgl, null).groupBy(new String[] { "billno", "yf", "th", "pt", "ptkh" }).finish().filter("yf>0")
				.groupBy(new String[] { "th", "pt", "ptkh" }).sum("yf").finish();
		filter1 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", lx);// 客户汇总类型
		fhmxgl = new QFilter[] { filter1, filterSh };
		//关联出客户仓库物料组织
		DataSet dswl = glmx(fywl, yd_fhmxb, fhmxgl);
		//dswl.print(true);
		// 查询对应客户
		DataSet khdygx = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number kh,yd_entryentity.yd_orgfield_dyzz zz",
				fhmxgl, null);
		String[] zdhj=new String[] { "pt","th", "kh", "zz" };
		//String[] zdhj=new String[] { "pt","th", "kh", "ckzz" };
		if("1".equals(lx))
		{
		yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
				.select(new String[] { "yf", "th" }, new String[] { "kh", "zz" }).finish()
				.groupBy(new String[] { "th", "kh", "zz" }).sum("yf").finish();
		}
		else
		{
			yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
					.select(new String[] { "yf", "th" }, new String[] { "ptkh", "zz" }).finish()
					.groupBy(new String[] { "th", "ptkh", "zz" }).sum("yf").finish();
			zdhj=new String[] { "pt","th", "kh", "zz","ptkh","thkcsw", "kcsw","quyu"};
			
		}
		// yfhz.print(true);
		DataSet dataSetgroupBy = dswl.copy().filter("th=false").select(zdhj).groupBy(zdhj).finish();
		if("1".equals(lx))
		{
			zdhj=new String[] { "pt","th", "kh", "ckzz" };
		}
		else
		{
			zdhj=new String[] { "pt","th", "kh", "ckzz","ptkh","thkcsw", "kcsw","quyu"};
		}
		 dataSetgroupBy = dataSetgroupBy.union(dswl.copy().filter("th=true").select(zdhj).groupBy(zdhj).finish());
		 
		//dswl.copy().print(true);
		//dataSetgroupBy.addField("", "");
		//dataSetgroupBy.copy().print(true);
		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型
		if ("4".equals(lx)) {
			formid = "im_otheroutbill";
			djlx = "im_OtherOutBill_STD_BT_S";
			biztypenumber = "355";
			je = "amount";
			xyd = "2";
		}

		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz");
			//String zzbm = row.getString("ckzz");	
			String khbm = row.getString("kh");
			String pt = row.getString("pt");
			Boolean th = row.getBoolean("th");
			String dpbm="";
//			if(th) {
//				// 查询仓库对应组织
//				String ckbm = "";
//				
//				for (Row rowdy : dswl.copy()) {
//					ckbm = rowdy.getString("ck");
//					break;
//				}
//				filter1 = new QFilter("number", QCP.equals, ckbm);
//				DynamicObject khojb = BusinessDataServiceHelper.loadSingle("bd_warehouse", "createorg.id",
//						new QFilter[] { filter1 });
//				zzbm = khojb.getString("createorg.id");
//			}
			// 查询客户对应税率
			filter1 = new QFilter("number", QCP.equals, khbm);
			DynamicObject[] khojb = BusinessDataServiceHelper.load("bd_customer", "taxrate.number",
					new QFilter[] { filter1 });
			String slbm = "";
			for (DynamicObject rowdy : khojb) {
				slbm = rowdy.getString("taxrate.number");
			}
			IFormView view = ABillServiceHelper.createAddView(formid);
			view.getModel().setItemValueByNumber("billtype", djlx);
			view.getModel().setValue("org", zzbm);
			if (lx != "4") {
				view.getModel().setValue("bizorg", zzbm);
			}
			if (th) {
				if ("4".equals(lx)) {
					view.getModel().setItemValueByNumber("biztype", "3551");
				} else {
					view.getModel().setItemValueByNumber("biztype", "2101");
					view.getModel().setItemValueByNumber("invscheme", "2101");
				}
			} else {
				view.getModel().setItemValueByNumber("biztype", biztypenumber);
				view.getModel().setItemValueByNumber("invscheme", "210");
			}
			view.getModel().setItemValueByNumber("customer", khbm);
			view.getModel().setItemValueByNumber("yd_dzdkh", khbm);
			view.getModel().setValue("biztime", rq);
			view.getModel().setValue("yd_tbly", pt);
			String khdp="kh=" + "'" + khbm + "'";
			if("4".equals(lx))
			{
				String kcsw="0";
				if(th)
				{
					//kcsw = row.getString("thkcsw");
					filter1 = new QFilter("yd_entryentity.yd_orgfield_dyzz", QCP.equals, zzbm);
					filter2 = new QFilter("yd_entryentity.yd_thkcsw", QCP.not_equals, "0");
					DataSet kcswjb = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
							"yd_entryentity.yd_thkcsw thkcsw",
							new QFilter[] { filter1,filter2 }, null).groupBy(new String[] {"thkcsw"}).finish();
					for (Row rowkcsw : kcswjb) {
						kcsw = rowkcsw.getString("thkcsw");
					}
				}
				else
				{
					kcsw = row.getString("kcsw");
				}
				if(kcsw!="0")
				{
					view.getModel().setValue("invscheme", kcsw);
				}
				String quyu = row.getString("quyu");
				view.getModel().setValue("yd_quyu", quyu);
				dpbm = row.getString("ptkh");
				khdp="ptkh=" + "'" + dpbm + "'";
				//view.getModel().setValue("comment", dpbm);
			}
//			view.getModel().setItemValueByNumber("currency", "CNY");
//			view.getModel().setItemValueByNumber("settlecurrency", "CNY");
			String zzgl="zz="+zzbm;
			if(th)
			{
				zzgl="ckzz="+zzbm;
			}
			// 查询当前客户与退货对应的汇总运费
			DataSet dqyf = yfhz.copy().filter(khdp).filter("th=" + row.getString("th"));
			BigDecimal yf = BigDecimal.ZERO;
			for (Row rowds : dqyf) {
				yf = rowds.getBigDecimal("yf");
			}

			DataSet dsxh = dswl.copy();
			DataSet dsxhW0 = dswl.copy();
			DataSet dsbillno = dswl.copy();
			dsbillno = dsbillno.filter(khdp).filter(zzgl)
					.filter("th=" + row.getString("th")).select(new String[] { "billno" })
					.groupBy(new String[] { "billno" }).finish();
			// dsxh.copy().print(false);
			// 汇总金额大于0的物料
			//dsxh.copy().print(false);
			dsxh = dsxh.filter(khdp).filter(zzgl)
					.filter("th=" + row.getString("th")).filter("je>0").select(new String[] { "wl", "sl", "je", "ck"})
					.groupBy(new String[] { "wl", "ck" }).sum("sl").sum("je").finish();
			// 汇总金额等于0的物料
			dsxhW0 = dsxhW0.filter(khdp).filter(zzgl)
					.filter("th=" + row.getString("th")).filter("je=0").select(new String[] { "wl", "sl", "je", "ck" })
					.groupBy(new String[] { "wl", "ck" }).sum("sl").sum("je").finish();
//			// 汇总金额大于0的物料
//						dsxh = dsxh.filter(khdp)
//								.filter("th=" + row.getString("th")).filter("je>0").select(new String[] { "wl", "sl", "je", "ck","ph" })
//								.groupBy(new String[] { "wl", "ck","ph" }).sum("sl").sum("je").finish();
//						// 汇总金额等于0的物料
//						dsxhW0 = dsxhW0.filter(khdp)
//								.filter("th=" + row.getString("th")).filter("je=0").select(new String[] { "wl", "sl", "je", "ck","ph" })
//								.groupBy(new String[] { "wl", "ck" ,"ph"}).sum("sl").sum("je").finish();
			dsxh = dsxh.union(dsxhW0);
			//dsxh.copy().print(false);
			int i = 0;
			BigDecimal zje = BigDecimal.ZERO;// 整单总金额
			for (Row rowds : dsxh) {
				String ckbm = rowds.getString("ck");
				//String ph = rowds.getString("ph");
				String wlbm = rowds.getString("wl");// sku
				BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
				// BigDecimal goods_price = rowds.getBigDecimal("dj");// 商品单价
				BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
				boolean sffy = true;
				for (String fywlbm : fywl) {
					if (wlbm.equals(fywlbm)) {
						yf = yf.add(share_payment);
						sffy = false;
						break;
					}
				}
				if (sffy) {

					if (i > 0) {
						view.getModel().createNewEntryRow("billentry");
					}
					view.getModel().setItemValueByNumber("material", wlbm, i);
					view.getModel().setItemValueByNumber("warehouse", ckbm, i);
					view.getModel().setValue("qty", goods_number, i);
					//view.getModel().setValue("yd_ph", ph, i);
					view.getModel().setValue(je, share_payment, i);
					if (share_payment.compareTo(BigDecimal.ZERO) == 0 && lx.equals("1")) {
						view.getModel().setValue("ispresent", "1", i);
					}
					if("4".equals(lx))
					{
					view.getModel().setValue("yd_store", dpbm,i);
					}
					if (slbm != null) {
						if ("1".equals(lx)) {
							view.getModel().setItemValueByNumber("taxrateid", slbm, i);
						}
					}
					zje = zje.add(share_payment);
					i++;
				}
			}
			view.getModel().setValue("yd_amountfield_yf", yf);
			// view.getModel().setValue("yd_textfield_fhmxdh", billno);
			List<String> fhmxdh = new ArrayList<String>();// 记录需要会写销售出库单/其他出库的发货明细单号
//			if (yf.compareTo(BigDecimal.ZERO) > 0) {
//				// 处理分摊
//				DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
//				BigDecimal ljft = BigDecimal.ZERO;
//				for (int j = 0; j < entrys.getRowCount(); j++) {
//					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
//					// BigDecimal sl=entrys.get(j).getBigDecimal("qty");//每行数量
//					if (j < entrys.getRowCount() - 1) {
//						BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
//						// BigDecimal yfft=new
//						// BigDecimal(((hsje.doubleValue()/zje.doubleValue())*yf.doubleValue())).setScale(2);//每行分摊运费
//						// BigDecimal hsdj=hsje.add(yfft).divide(sl);//每行分摊后含税单价
//						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
//						ljft = ljft.add(yfft);
//						view.getModel().setValue(je, hsjeft, j);
//					} else {
//						BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
//						// BigDecimal hsdj=hsje.add(yfft).divide(sl);//每行分摊后含税单价
//						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
//						view.getModel().setValue(je, hsjeft, j);
//					}
//				}
//			}
			if (yf.compareTo(BigDecimal.ZERO) > 0) {
				// 处理分摊
				DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
				int hs=0;
				for (int j = 0; j < entrys.getRowCount(); j++) {
					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
					if(hsje.compareTo(BigDecimal.ZERO) > 0)
					{
						hs++;
					}
				}
				BigDecimal ljft = BigDecimal.ZERO;
				int jehs=0;
				for (int j = 0; j < entrys.getRowCount(); j++) {
					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
					if(hsje.compareTo(BigDecimal.ZERO) > 0)
					{
						jehs++;
					if (jehs < hs) {
						BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
						ljft = ljft.add(yfft);
						view.getModel().setValue(je, hsjeft, j);
					} else {
						BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
						view.getModel().setValue(je, hsjeft, j);
					}
					}
				}
			}
			int j = 0;
			for (Row rowds : dsbillno) {
				if (j > 0) {
					view.getModel().createNewEntryRow("yd_entryentity");
				}
				String billno = rowds.getString("billno");
				view.getModel().setValue("yd_textfield_fhmxdh", billno, j);
				fhmxdh.add(billno);
				j++;
			}
			if (i > 0) {
				OperationResult operationResult = ABillServiceHelper.saveOperate(view);
				filter1 = new QFilter("billno", QCP.in, fhmxdh);
				// 判断保存结果
				if (operationResult.isSuccess()) {
					String djbh = "";
					Map<Object, String> map = operationResult.getBillNos();
					for (Map.Entry<Object, String> entry : map.entrySet()) {

						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
								"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							djbh = entry.getValue();
							rowdy.set("yd_textfield_xsckdh", entry.getValue());// 修改数据
							rowdy.set("yd_combofield_xyd", xyd);
						}
						SaveServiceHelper.save(dy);
					}
					// 进行提交操作
//					IFormView modifyView = ABillServiceHelper.createModifyView(view.getEntityId(),
//							operationResult.getSuccessPkIds().get(0).toString());
					operationResult = view.invokeOperation("submit");
					if (!operationResult.isSuccess()) {
						String errMessage = operationResult.getMessage() + ","; // 错误摘要
						// // 演示提取保存详细错误
						for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
							errMessage += errInfo.getMessage() + ",";
						}
						if (errMessage.length() > 1800) {
							errMessage = errMessage.substring(0, 1800);
						}
						filter1 = new QFilter("billno", QCP.equals, djbh);
						DynamicObject[] dy = BusinessDataServiceHelper.load(formid, "yd_sftj",
								new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_sftj", errMessage);// 修改数据
						}
						SaveServiceHelper.save(dy);
					}
					ABillServiceHelper.exitView(view);
				} else {
					String errMessage = operationResult.getMessage() + ","; // 错误摘要
					// // 演示提取保存详细错误
					for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
						errMessage += errInfo.getMessage() + ",";
					}
					if (errMessage.length() > 1800) {
						errMessage = errMessage.substring(0, 1800);
					}
					DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
							new QFilter[] { filter1 });
					for (DynamicObject rowdy : dy) {
						rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
					}
					SaveServiceHelper.save(dy);
					ABillServiceHelper.exitView(view);
				}
			} else {
				ABillServiceHelper.exitView(view);
			}
		}
		dswl.close();
	}

	public static void bchbdjby(String lx, List<String> pcwlin, List<String> fywl, List<String> ppfd,String rq) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
//		QFilter filter7 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不为手工
//		QFilter filter8 = QFilter.of("yd_checkboxfield_sfkp =?", "0");// 不为开票
		QFilter filter9 = QFilter.of("yd_checkboxfield_wlwsh =?", "0");// 物料合规
		QFilter filter16 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter17 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只过滤按照品牌分单店铺
		QFilter filter18 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter19 = QFilter.of("yd_dygxcf =?", "0");// 物料对应关系重复为否
		QFilter filter20 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
//		QFilter filter17 = QFilter.of("yd_combofield_pt =?", "1");// 平台为佰悦e3
//		QFilter filter18 = QFilter.of("yd_datefield_fhrq >?", "2021-12-11");// 日期大于20号
		// QFilter filter12 = QFilter.of("to_char(yd_datetimefield_xdsj,'yyyy-MM-dd') =
		// ?", rq);// 日期
		QFilter filter12 = QFilter.of("yd_datefield_fhrq = ?", rq);// 日期
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter11 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6,
				filter9, filter12, filter11,filter16,filter17,filter18,filter19,filter20 };
		// 查询发货明细
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb",
				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf,yd_textfield_ck ptck,yd_entryentity.yd_textfield_hpbh ptwl,yd_entryentity.yd_decimalfield_sl sl,yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je,yd_entryentity.yd_ph ph,yd_entryentity.yd_scrq scrq,yd_entryentity.yd_dqr dqr,yd_bz bz,yd_dealcode dealcode",
				fhmxgl, null);
		// 查询运费汇总 不需要去除排除物料
		//yd_fhmxb.copy().print(true);
//		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter9, filter12,
//				filter11,filter16,filter17 };
//		DataSet yfhz = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb",
//				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf",
//				fhmxgl, null).groupBy(new String[] { "billno", "yf", "th", "pt", "ptkh" }).finish().filter("yf>0")
//				.groupBy(new String[] { "th", "pt", "ptkh" }).sum("yf").finish();
		filter1 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", lx);// 客户汇总类型
		fhmxgl = new QFilter[] { filter1, filterSh };
		//关联出客户仓库物料组织
		DataSet dswl = glmx(fywl, yd_fhmxb, fhmxgl);
		dswl=dswl.filter("pp !=1");
		//dswl.print(true);
		// 查询对应客户
//		DataSet khdygx = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
//				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number kh,yd_entryentity.yd_orgfield_dyzz zz",
//				fhmxgl, null);
		String[] zdhj=new String[] { "pt","th", "kh", "zz" };
		if("1".equals(lx))
		{
//		yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
//				.select(new String[] { "yf", "th" }, new String[] { "kh", "zz" }).finish()
//				.groupBy(new String[] { "th", "kh", "zz" }).sum("yf").finish();
		}
		else
		{
//			yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
//					.select(new String[] { "yf", "th" }, new String[] { "ptkh", "zz" }).finish()
//					.groupBy(new String[] { "th", "ptkh", "zz" }).sum("yf").finish();
			zdhj=new String[] { "pt","th", "kh", "zz","ptkh","thkcsw", "kcsw","quyu"};
		}
		// yfhz.print(true);
		DataSet dataSetgroupBy = dswl.copy().select(zdhj).groupBy(zdhj).finish();
		//dswl.copy().print(true);
		//dataSetgroupBy.addField("", "");
		//dataSetgroupBy.print(true);
		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型
		if ("4".equals(lx)) {
			formid = "im_otheroutbill";
			djlx = "im_OtherOutBill_STD_BT_S";
			biztypenumber = "355";
			je = "amount";
			xyd = "2";
		}

		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz");
			String khbm = row.getString("kh");
			String pt = row.getString("pt");
			Boolean th = row.getBoolean("th");
			String dpbm="";
//			if(th) {
//				// 查询仓库对应组织
//				String ckbm = "";
//				
//				for (Row rowdy : dswl.copy()) {
//					ckbm = rowdy.getString("ck");
//					break;
//				}
//				filter1 = new QFilter("number", QCP.equals, ckbm);
//				DynamicObject khojb = BusinessDataServiceHelper.loadSingle("bd_warehouse", "createorg.id",
//						new QFilter[] { filter1 });
//				zzbm = khojb.getString("createorg.id");
//			}
			// 查询客户对应税率
			filter1 = new QFilter("number", QCP.equals, khbm);
			DynamicObject[] khojb = BusinessDataServiceHelper.load("bd_customer", "taxrate.number",
					new QFilter[] { filter1 });
			String slbm = "";
			for (DynamicObject rowdy : khojb) {
				slbm = rowdy.getString("taxrate.number");
			}

			String khdp="kh=" + "'" + khbm + "'";
			if("4".equals(lx))
			{
//				String kcsw="0";
//				if(th)
//				{
//					kcsw = row.getString("thkcsw");
//				}
//				else
//				{
//					kcsw = row.getString("kcsw");
//				}
//				if(kcsw!="0")
//				{
//					//view.getModel().setValue("invscheme", kcsw);
//				}
//				String quyu = row.getString("quyu");
				//view.getModel().setValue("yd_quyu", quyu);
				dpbm = row.getString("ptkh");
				khdp="ptkh=" + "'" + dpbm + "'";
				
			}
			// 查询当前客户与退货对应的汇总运费
//			DataSet dqyf = yfhz.copy().filter(khdp).filter("th=" + row.getString("th"));
//			BigDecimal yf = BigDecimal.ZERO;
//			for (Row rowds : dqyf) {
//				yf = rowds.getBigDecimal("yf");
//			}

			DataSet dsxh = dswl.copy();
			DataSet dsxhW0 = dswl.copy();
			DataSet dsbillno = dswl.copy();
			dsbillno = dsbillno.filter(khdp)
					.filter("th=" + row.getString("th")).select(new String[] { "billno","pp" })
					.groupBy(new String[] { "billno","pp" }).finish();
			// dsxh.copy().print(false);
			// 汇总物料
			dsxh = dsxh.filter(khdp)
					.filter("th=" + row.getString("th")).select(new String[] { "wl", "sl", "je", "ck","ph","pp","scrq","dqr" })
					.groupBy(new String[] { "wl", "ck","ph","pp","scrq","dqr" }).sum("sl").sum("je").finish();
			// 汇总金额等于0的物料
//			dsxhW0 = dsxhW0.filter(khdp)
//					.filter("th=" + row.getString("th")).filter("je=0").select(new String[] { "wl", "sl", "je", "ck","ph","pp","scrq","dqr" })
//					.groupBy(new String[] { "wl", "ck","ph","pp","scrq","dqr" }).sum("sl").sum("je").finish();

			//dsxh = dsxh.union(dsxhW0);
			//dsxh.print(true);
//			int zs=0;//总行数
//			BigDecimal zje = BigDecimal.ZERO;// 整单总金额
//			for (Row rowds : dsxh.copy()) {
//				String pp = rowds.getString("pp");// sku
//				BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
//				//zs=rowds.getInteger("zs");
//				if(share_payment.compareTo(BigDecimal.ZERO)==1)
//				{
//					if ("1".equals(pp)) {
//						yf = yf.add(share_payment);
//					}
//					else
//					{
//						zs++;
//						zje = zje.add(share_payment);
//						
//					}
//				}
//			}
//			dsxh=dsxh.filter("pp !=1").addField(yf.toPlainString(), "yf").addField(zje.toPlainString(), "zje");
//			dsxh=dsxh.addField("ROUND((je/zje)*yf,2)", "ftje");

			DataSet pps=dsxh.copy().select(new String[] {"pp"}).groupBy(new String[] {"pp"}).finish();
			//dsxh.copy().print(false);
//			int hs=0;//记录总行数
//			BigDecimal jlyf = BigDecimal.ZERO;//记录已生成运费
			for(Row rowpp:pps)
			{
				IFormView view = ABillServiceHelper.createAddView(formid);
				view.getModel().setItemValueByNumber("billtype", djlx);
				view.getModel().setValue("org", zzbm);
				if (lx != "4") {
					view.getModel().setValue("bizorg", zzbm);
				}
				if (th) {
					if ("4".equals(lx)) {
						view.getModel().setItemValueByNumber("biztype", "3551");
					} else {
						view.getModel().setItemValueByNumber("biztype", "2101");
						view.getModel().setItemValueByNumber("invscheme", "2101");
					}
				} else {
					view.getModel().setItemValueByNumber("biztype", biztypenumber);
					view.getModel().setItemValueByNumber("invscheme", "210");
				}
				view.getModel().setItemValueByNumber("customer", khbm);
				view.getModel().setItemValueByNumber("yd_dzdkh", khbm);
				view.getModel().setValue("biztime", rq);
				view.getModel().setValue("yd_tbly", pt);
				view.getModel().setValue("yd_frome3", 1);
				if("4".equals(lx))
				{
					String kcsw="0";
					if(th)
					{
						filter1 = new QFilter("yd_entryentity.yd_orgfield_dyzz", QCP.equals, zzbm);
						filter2 = new QFilter("yd_entryentity.yd_thkcsw", QCP.not_equals, "0");
						DataSet kcswjb = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
								"yd_entryentity.yd_thkcsw thkcsw",
								new QFilter[] { filter1,filter2 }, null).groupBy(new String[] {"thkcsw"}).finish();
						for (Row rowkcsw : kcswjb) {
							kcsw = rowkcsw.getString("thkcsw");
						}
					}
					else
					{
						kcsw = row.getString("kcsw");
					}
					if(kcsw!="0")
					{
						view.getModel().setValue("invscheme", kcsw);
					}
					String quyu = row.getString("quyu");
					view.getModel().setValue("yd_quyu", quyu);
					dpbm = row.getString("ptkh");
					//view.getModel().setValue("comment", dpbm);
//					khdp="ptkh=" + "'" + dpbm + "'";
					
				}
				String pp=rowpp.getString("pp");
				DataSet dsmx=dsxh.copy().filter("pp="+pp);
				DataSet dsbillnopp=dsbillno.copy().filter("pp="+pp);
				int i = 0;
				//BigDecimal djzyf=BigDecimal.ZERO;//记录单张运费金额
				for (Row rowds : dsmx) {

					String ckbm = rowds.getString("ck");
					String wlbm = rowds.getString("wl");// sku
					BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
					String ph = rowds.getString("ph");//批号
					Date scrq = rowds.getDate("scrq");//生产日期
					Date dqr = rowds.getDate("dqr");//到期日
					BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
//					BigDecimal ftje = rowds.getBigDecimal("ftje");// 分摊运费金额
//					BigDecimal ysje=share_payment.add(ftje);
//					if(share_payment.compareTo(BigDecimal.ZERO)==1)
//					{
//						hs++;
//					}
//					//最后一行使用总运费减去已使用累计运费
//					if(hs==zs)
//					{
//						ysje=share_payment.add(yf.subtract(jlyf));
//						djzyf=djzyf.add(yf.subtract(jlyf));
//						jlyf=jlyf.add(yf.subtract(jlyf));
//					}
//					else {
//						djzyf=djzyf.add(ftje);
//					jlyf=jlyf.add(ftje);
//					}
						if (i > 0) {
							view.getModel().createNewEntryRow("billentry");
						}
						view.getModel().setItemValueByNumber("material", wlbm, i);
						view.getModel().setValue("yd_ph", ph, i);
						if(scrq!=null)
						{
						view.getModel().setValue("yd_scrq", scrq, i);
						}
						if(dqr!=null)
						{
						view.getModel().setValue("yd_dqr", dqr, i);
						}
						if(lx.equals("4"))
						{
						view.getModel().setValue("yd_store", dpbm,i);
						}
						view.getModel().setItemValueByNumber("warehouse", ckbm, i);
						view.getModel().setValue("qty", goods_number, i);
						// view.getModel().setValue(dj, goods_price, i);

						view.getModel().setValue(je, share_payment, i);
//						if (ysje.compareTo(BigDecimal.ZERO) == 0 && lx.equals("1")) {
//							view.getModel().setValue("ispresent", "1", i);
//						}

						if (slbm != null) {
							if ("1".equals(lx)) {
								view.getModel().setItemValueByNumber("taxrateid", slbm, i);
							}
						}
						//zje = zje.add(share_payment);
						i++;
				}
				//view.getModel().setValue("yd_amountfield_yf", djzyf);
				List<String> fhmxdh = new ArrayList<String>();// 记录需要回写销售出库单/其他出库的发货明细单号
				int j = 0;
				for (Row rowds : dsbillnopp) {
					if (j > 0) {
						view.getModel().createNewEntryRow("yd_entryentity");
					}
					String billno = rowds.getString("billno");
					view.getModel().setValue("yd_textfield_fhmxdh", billno, j);
					fhmxdh.add(billno);
					j++;
				}
				if (i > 0) {
					OperationResult operationResult = ABillServiceHelper.saveOperate(view);
					filter1 = new QFilter("billno", QCP.in, fhmxdh);
					// 判断保存结果
					if (operationResult.isSuccess()) {
						String djbh = "";
						Map<Object, String> map = operationResult.getBillNos();
						for (Map.Entry<Object, String> entry : map.entrySet()) {

							DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
									"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
							for (DynamicObject rowdy : dy) {
								String dh=rowdy.getString("yd_textfield_xsckdh");
								if(dh!="")
								{
								djbh =dh+","+entry.getValue();
								}
								else
								{
									djbh=entry.getValue();
								}
								rowdy.set("yd_textfield_xsckdh", djbh);// 修改数据
								rowdy.set("yd_combofield_xyd", xyd);
							}
							SaveServiceHelper.save(dy);
						}
						// 进行提交操作
//						IFormView modifyView = ABillServiceHelper.createModifyView(view.getEntityId(),
//								operationResult.getSuccessPkIds().get(0).toString());
//						operationResult = view.invokeOperation("submit");
//						if (!operationResult.isSuccess()) {
//							String errMessage = operationResult.getMessage() + ","; // 错误摘要
//							// // 演示提取保存详细错误
//							for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
//								errMessage += errInfo.getMessage() + ",";
//							}
//							if (errMessage.length() > 1800) {
//								errMessage = errMessage.substring(0, 1800);
//							}
//							filter1 = new QFilter("billno", QCP.equals, djbh);
//							DynamicObject[] dy = BusinessDataServiceHelper.load(formid, "yd_sftj",
//									new QFilter[] { filter1 });
//							for (DynamicObject rowdy : dy) {
//								rowdy.set("yd_sftj", errMessage);// 修改数据
//							}
//							SaveServiceHelper.save(dy);
//						}
						ABillServiceHelper.exitView(view);
					} else {
						String errMessage = operationResult.getMessage() + ","; // 错误摘要
						// // 演示提取保存详细错误
						for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
							errMessage += errInfo.getMessage() + ",";
						}
						if (errMessage.length() > 1800) {
							errMessage = errMessage.substring(0, 1800);
						}
						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
								new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
						}
						SaveServiceHelper.save(dy);
						ABillServiceHelper.exitView(view);
					}
				} else {
					ABillServiceHelper.exitView(view);
				}
			}


		}
		dswl.close();
	}

	public static void khdyzzjy(QFilter filter) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = QFilter.of("yd_checkboxfield_cyhd =?", "1");// 参与合单
		QFilter filter7 = QFilter.of("yd_textfield_sbyy =?", "");// 失败原因为空
		QFilter filter8 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7,filter8,filter9,filter10,filter };
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_checkboxfield_khwdyzz,yd_textfield_sbyy",
				fhmxgl);
		for (DynamicObject row : dy) {
			row.set("yd_checkboxfield_khwdyzz", 1);
			//row.set("yd_textfield_sbyy", "全部为剔除物料");
		}
		SaveServiceHelper.save(dy);// 保存
	}

	public static void jy(QFilter filter,List<String> pcwl,List<String> pcwlpp,List<String> ppfd) {
		// 清空发货明细不处理,客户,物料,仓库不存在,错误信息
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter2 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter3 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 不处理为否
		QFilter filter4 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter5 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter6 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
				"yd_checkboxfield_bcl,yd_checkboxfield_khbcz,yd_checkboxfield_ckbcz,yd_checkboxfield_wlbcz,yd_textfield_sbyy,yd_checkboxfield_cyhd,yd_checkboxfield_khwdyzz,yd_checkboxfield_wlwsh,yd_entryentity.yd_mxwlbcz,yd_dygxcf,yd_entryentity.yd_mxdygxcf",
				new QFilter[] { filter1, filter2,filter3,filter4,filter5,filter6,filter });
		//没有符合要求的数据，无需处理
		if(dy.length==0)
		{
			return;
		}
		for (DynamicObject row : dy) {
			//row.set("yd_checkboxfield_bcl", 0);
			row.set("yd_checkboxfield_khbcz", 0);
			row.set("yd_checkboxfield_ckbcz", 0);
			row.set("yd_checkboxfield_wlbcz", 0);
			row.set("yd_dygxcf", 0);
			//row.set("yd_checkboxfield_khwdyzz", 0);
			row.set("yd_checkboxfield_wlwsh", 0);
			row.set("yd_textfield_sbyy", "");
			row.set("yd_checkboxfield_cyhd", true);// 标记为参与合单
			DynamicObjectCollection mx=row.getDynamicObjectCollection("yd_entryentity");
			for (DynamicObject rowmx : mx)
			{
				rowmx.set("yd_mxwlbcz", 0);//明细物料不存在为否
				rowmx.set("yd_mxdygxcf", 0);//明细物料对应关系重复
			}
		}
		SaveServiceHelper.save(dy);// 保存
		// 标记出关联客户为不处理的单据
		jymxjoin("yd_khdygx", "yd_textfield_dpbh", "yd_checkboxfield_bcl",filter);// 标记不处理的客户
		jymxjoinpcck("yd_pcck", "yd_textfield_ck", "yd_pcck",filter,ppfd);//排除仓库
		// 查询需要所有排除的物料 包括运费分摊物料
//		QFilter filter5 = QFilter.of("yd_lx =?", "2");//非品牌分单
//		DataSet pcwl = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl", new QFilter[] {filter5},
//				null);
//		List<String> pcwlin = new ArrayList<String>();
//		for (Row row : pcwl) {
//			pcwlin.add(row.getString("pcwl"));
//		}
//		//
//		filter5 = QFilter.of("yd_lx =?", "1");//品牌分单
//		DataSet pcwlpp = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl", new QFilter[] {filter5},
//				null);
//		List<String> pcwlinpp = new ArrayList<String>();
//		for (Row row : pcwlpp) {
//			pcwlinpp.add(row.getString("pcwl"));
//		}
		jymx(pcwl,pcwlpp,"yd_khdygx", "yd_textfield_dpbh", "yd_checkboxfield_khbcz",filter,ppfd);// 标记不存在的客户的单据
		jymx(pcwl,pcwlpp,"yd_ckdygx", "yd_textfield_ck", "yd_checkboxfield_ckbcz",filter,ppfd);// 标记不存在的仓库的单据
		//jymx(pcwl,pcwlpp,"yd_wldygx", "yd_entryentity.yd_textfield_hpbh", "yd_checkboxfield_wlbcz",filter,ppfd);// 标记不存在的物料的单据
		jymxwl(pcwl,pcwlpp, filter,ppfd);// 标记不存在的物料的单据
		jymxhg(pcwl,pcwlpp,"yd_wldygx", "yd_entryentity.yd_textfield_hpbh", "yd_checkboxfield_wlwsh", "bd_material",filter,ppfd);// 标记物料不合规的单据
	}

	public static void jymxjoin(String bzm, String zdm, String xgbz,QFilter filter) {
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		QFilter filter2 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", "3");// 客户为不处理
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter7 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		// 查询销售出库单号为空且不处理为否且不为今天的发货明细表
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymxjoin", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
				new QFilter[] { filter1, filter3, filter4,filter5,filter,filter6,filter7 }, null);
		//yd_fhmxbykh.print(true);
		String[] fhmxbzd = new String[] { "pt", zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		//yd_fhmxbykh.print(true);
		DataSet kh = QueryServiceHelper.queryDataSet("jymxjoin", bzm,
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm",
				new QFilter[] { filterSh,filter2 }, null);
		//kh.print(true);
		//kh = kh.join(khdy).on("kh", "kh").select("pt", "bm").finish();
		//kh.print(true);
		DataSet joinDataset = yd_fhmxbykh.join(kh).on(zdm, "bm").on("pt", "pt").select(fhmxbzd).finish();
		//joinDataset.print(true);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			bczkh.add(row.getString(zdm));
		}
		filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
				new QFilter[] { filter1, filter2, filter3, filter4 });
		for (DynamicObject row : dy) {
			row.set(xgbz, 1);// 修改数据
			// row.set("yd_textfield_sbyy", "不处理");// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
	}
	
	public static void jymxjoinpcck(String bzm, String zdm, String xgbz,QFilter filter,List<String>  ppfd) {
		//jymxjoin("yd_khdygx", "yd_textfield_dpbh", "yd_checkboxfield_bcl",filter);// 标记不处理的客户
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		//QFilter filter2 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", "3");// 排除仓库为已审核
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter8 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter9 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		//非品牌分单
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymxjoin", "yd_fhmxb", zdm,
				new QFilter[] { filter1, filter3, filter4,filter5,filter,filter6,filter8,filter9 }, null);
		//品牌分单
		DataSet yd_fhmxbykhpp = QueryServiceHelper.queryDataSet("jymxjoin", "yd_fhmxb", zdm,
				new QFilter[] { filter1, filter3, filter4,filter5,filter,filter6,filter7,filter9 }, null);
		//yd_fhmxbykh.print(true);
		String[] fhmxbzd = new String[] { zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		yd_fhmxbykhpp = yd_fhmxbykhpp.select(fhmxbzd).groupBy(fhmxbzd).finish();
		//yd_fhmxbykh.print(true);
		QFilter filterfpp = QFilter.of("yd_lx =?", "2");//非品牌
		QFilter filterpp = QFilter.of("yd_lx =?", "1");//品牌
		DataSet kh = QueryServiceHelper.queryDataSet("jymxjoin", bzm,
				"yd_entryentity.yd_textfield_bm bm",
				new QFilter[] { filterSh,filterfpp }, null);
		
		DataSet khpp = QueryServiceHelper.queryDataSet("jymxjoin", bzm,
				"yd_entryentity.yd_textfield_bm bm",
				new QFilter[] { filterSh,filterpp }, null);
		//kh.print(true);
		//khpp.print(true);
		//kh = kh.join(khdy).on("kh", "kh").select("pt", "bm").finish();
		//kh.print(true);
		DataSet joinDataset = yd_fhmxbykh.join(kh).on(zdm, "bm").select(fhmxbzd).finish();
		DataSet joinDatasetpp = yd_fhmxbykhpp.join(khpp).on(zdm, "bm").select(fhmxbzd).finish();
		//joinDataset.print(true);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			bczkh.add(row.getString(zdm));
		}
		QFilter filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
				new QFilter[] { filter1, filter2, filter3, filter4,filter,filter8 });
		for (DynamicObject row : dy) {
			row.set(xgbz, 1);// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
		
		List<String> bczkhpp = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDatasetpp) {
			bczkhpp.add(row.getString(zdm));
		}
		filter2 = new QFilter(zdm, QCP.in, bczkhpp);
		DynamicObject[] dypp = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
				new QFilter[] { filter1, filter2, filter3, filter4,filter,filter7 });
		for (DynamicObject row : dypp) {
			row.set(xgbz, 1);// 修改数据
		}
		SaveServiceHelper.save(dypp);// 保存
	}

	public static void jymx(List<String>  pcwlin,List<String>  pcwlinpp,String bzm, String zdm, String xgbz,QFilter filter,List<String>  ppfd) {
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料非品牌分单
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter8 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlinpp);// 去除排除物料品牌分单
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表
		//非品牌分单单据
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
				new QFilter[] { filter1, filter2,filter3, filter4,filter5,filter,filter7,filter9,filter10 }, null);
		//yd_fhmxbykh.print(true);
		//品牌分单单据
		DataSet yd_fhmxbykhpp = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
				new QFilter[] { filter1,filter3, filter4,filter5,filter,filter6,filter8,filter9,filter10 }, null);
		//yd_fhmxbykhpp.print(true);
		yd_fhmxbykh=yd_fhmxbykh.union(yd_fhmxbykhpp);
		//yd_fhmxbykh.print(true);
		String[] fhmxbzd = new String[] { "pt", zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		DataSet kh = QueryServiceHelper.queryDataSet("jymx", bzm, "yd_combofield_pt pt,yd_entryentity.yd_textfield bm",
				new QFilter[] { filterSh }, null);
		DataSet joinDataset = yd_fhmxbykh.leftJoin(kh).on(zdm, "bm").on("pt", "pt")
				.select(fhmxbzd, new String[] { "bm" }).finish();
		//joinDataset.print(true);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			String number = row.getString("bm");
			if (number == null) {
				bczkh.add(row.getString(zdm));
			}
		}
		filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
				new QFilter[] { filter1, filter2, filter3, filter4,filter5,filter9,filter });
		String bccw = "客户不存在";
		if ("yd_ckdygx".equals(bzm)) {
			bccw = "仓库不存在";
		} else if ("yd_wldygx".equals(bzm)) {
			bccw = "物料不存在";
		}
		for (DynamicObject row : dy) {
			String cwxx = row.getString("yd_textfield_sbyy");
			if (cwxx != "") {
				cwxx += "," + bccw;
			} else {
				cwxx = bccw;
			}
			row.set(xgbz, 1);// 修改数据
			row.set("yd_textfield_sbyy", cwxx);// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
	}
	
	public static void jymxwl(List<String>  pcwlin,List<String>  pcwlinpp,QFilter filter,List<String>  ppfd) {
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料非品牌分单
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter8 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlinpp);// 去除排除物料品牌分单
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("helpcode !=?", "");// 助记码不为空
		QFilter filter11 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表
		QFilter[] fppfdgl={ filter1, filter2,filter3, filter4,filter5,filter,filter7,filter9,filter11 };//非品牌分单过滤
		QFilter[] ppfdgl={ filter1,filter3, filter4,filter5,filter,filter6,filter8,filter9,filter11 };//品牌分单过滤
		String[] fhmxbzd = new String[] { "pt", "yd_entryentity.yd_textfield_hpbh" };
		//非品牌分单单据
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt,yd_entryentity.yd_textfield_hpbh",
				fppfdgl, null).select(fhmxbzd).groupBy(fhmxbzd).finish();
		//品牌分单单据
		DataSet yd_fhmxbykhpp = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt,yd_entryentity.yd_textfield_hpbh",
				ppfdgl, null).select(fhmxbzd).groupBy(fhmxbzd).finish();
//		yd_fhmxbykh=yd_fhmxbykh.union(yd_fhmxbykhpp);
//		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		DataSet wldy = QueryServiceHelper.queryDataSet("jymx", "yd_wldygx", "yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number wl,yd_combofield_pt pt",
				new QFilter[] { filterSh }, null);
		//wldy.copy().print(false);
		//过滤e3物料对应关系物料
		List<String> wldy01 = new ArrayList<String>();
		for (Row row : wldy.copy().filter("pt='1'").select("bm")) {
			wldy01.add(row.getString("bm"));
		}
		//过滤旺店通物料对应关系物料
		List<String> wldy02 = new ArrayList<String>();
		for (Row row : wldy.copy().filter("pt='2'").select("bm")) {
			wldy02.add(row.getString("bm"));
		}
		QFilter filterwldye3 = new QFilter("helpcode", QCP.not_in, wldy01);//不包含对应表商品e3
		QFilter filterwldywdt = new QFilter("number", QCP.not_in, wldy02);//不包含对应表商品旺店通
		filterSh = QFilter.of("status=?", "C");
		// 查询已审核的物料编码
		DataSet wlbm = QueryServiceHelper.queryDataSet("jymx", "bd_material", "number bm,number wl", new QFilter[] { filterSh,filterwldywdt }, null);
		wlbm=wlbm.addField("'2'", "pt");
		// 查询已审核并且不为空的的物料助记码
		DataSet wlzjm = QueryServiceHelper.queryDataSet("jymx", "bd_material", "helpcode bm,number wl", new QFilter[] { filterSh,filter10,filterwldye3 }, null);
		wlzjm=wlzjm.addField("'1'", "pt");
		wldy=wldy.union(wlbm).union(wlzjm).groupBy(new String[]{"bm","pt"}).count("count").finish();
		//wldy.copy().print(false);
		DataSet cfwl=wldy.copy().filter("count>1");
		//查询e3店铺
		DataSet pt01=QueryServiceHelper.queryDataSet("jymx", "yd_khdygx", "yd_entryentity.yd_textfield", new QFilter[] { QFilter.of("yd_combofield_pt=?", "1") }, null);
		//查询旺店通店铺
		DataSet pt02=QueryServiceHelper.queryDataSet("jymx", "yd_khdygx", "yd_entryentity.yd_textfield", new QFilter[] { QFilter.of("yd_combofield_pt=?", "2") }, null);
		//查询吉客云店铺
		DataSet pt03=QueryServiceHelper.queryDataSet("jymx", "yd_khdygx", "yd_entryentity.yd_textfield", new QFilter[] { QFilter.of("yd_combofield_pt=?", "3") }, null);
		//过滤e3对应关系重复物料
		List<String> cfwl01 = new ArrayList<String>();
		for (Row row : cfwl.copy().filter("pt='1'").select("bm")) {
				cfwl01.add(row.getString("bm"));
		}
		//过滤旺店通对应关系重复物料
		List<String> cfwl02 = new ArrayList<String>();
		for (Row row : cfwl.copy().filter("pt='2'").select("bm")) {
				cfwl02.add(row.getString("bm"));
		}
		//过滤吉客云对应关系重复物料
		List<String> cfwl03 = new ArrayList<String>();
		for (Row row : cfwl.copy().filter("pt='3'").select("bm")) {
				cfwl03.add(row.getString("bm"));
		}
		//过滤e3店铺
		List<String> ptdp01 = new ArrayList<String>();
		for (Row row : pt01) {
			ptdp01.add(row.getString("yd_entryentity.yd_textfield"));
		}
		//过滤旺店通店铺
		List<String> ptdp02 = new ArrayList<String>();
		for (Row row : pt02) {
			ptdp02.add(row.getString("yd_entryentity.yd_textfield"));
		}
		//过滤吉客云店铺
		List<String> ptdp03 = new ArrayList<String>();
		for (Row row : pt03) {
			ptdp03.add(row.getString("yd_entryentity.yd_textfield"));
		}
		QFilter[] Filters1={ filter1, filter3, filter4,filter,filter9, new QFilter("yd_textfield_dpbh", QCP.in, ptdp01)};
		gxfhmx(Filters1,"物料对应关系重复","yd_dygxcf","yd_mxdygxcf",cfwl01);
		QFilter[] Filters2={ filter1, filter3, filter4,filter,filter9, new QFilter("yd_textfield_dpbh", QCP.in, ptdp02)};
		gxfhmx(Filters2,"物料对应关系重复","yd_dygxcf","yd_mxdygxcf",cfwl02);
		QFilter[] Filters3={ filter1, filter3, filter4,filter,filter9, new QFilter("yd_textfield_dpbh", QCP.in, ptdp03)};
		gxfhmx(Filters3,"物料对应关系重复","yd_dygxcf","yd_mxdygxcf",cfwl03);
		//非品牌分物料不存在处理
		DataSet dsfpp = yd_fhmxbykh.leftJoin(wldy.copy()).on("yd_entryentity.yd_textfield_hpbh", "bm").on("pt", "pt")
				.select(fhmxbzd, new String[] { "bm" }).finish();
		List<String> bczfpp = new ArrayList<String>();
		for (Row row : dsfpp) {
			String number = row.getString("bm");
			if (number == null) {
				bczfpp.add(row.getString("yd_entryentity.yd_textfield_hpbh"));
			}
		}
		QFilter filterbczfpp = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.in, bczfpp);
		QFilter[] Filters4={ filter1, filter3, filter4,filter,filter7,filter9,filterbczfpp};
		gxfhmx(Filters4,"物料不存在","yd_checkboxfield_wlbcz","yd_mxwlbcz",bczfpp);
		//品牌分单物料不存在处理
		DataSet dspp = yd_fhmxbykhpp.leftJoin(wldy).on("yd_entryentity.yd_textfield_hpbh", "bm").on("pt", "pt")
				.select(fhmxbzd, new String[] { "bm" }).finish();
		List<String> bczpp = new ArrayList<String>();
		for (Row row : dspp) {
			String number = row.getString("bm");
			if (number == null) {
				bczpp.add(row.getString("yd_entryentity.yd_textfield_hpbh"));
			}
		}
		QFilter filterbczpp = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.in, bczpp);
		QFilter[] Filters5={ filter1, filter3, filter4,filter,filter6,filter9,filterbczpp};
		gxfhmx(Filters5,"物料不存在","yd_checkboxfield_wlbcz","yd_mxwlbcz",bczpp);
	}
	//更新发货明细物料相关 过滤条件,错误提示,更新字段,更新字段明细,对比物料集合
	public static void gxfhmx(QFilter[] Filters,String bccw,String gxzd,String gxzdmx,List<String> bczkh)
	{
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_entryentity.yd_mxwlbcz,yd_entryentity.yd_textfield_hpbh,yd_textfield_sbyy,yd_checkboxfield_wlbcz,yd_dygxcf,yd_entryentity.yd_mxdygxcf",
				Filters);
		//String bccw = "对应关系重复";
		for (DynamicObject row : dy) {
			DynamicObjectCollection mx=row.getDynamicObjectCollection("yd_entryentity");
			for (DynamicObject rowmx : mx)
			{
			String wl=rowmx.getString("yd_textfield_hpbh");
			if(bczkh.contains(wl))
			{
			String cwxx = row.getString("yd_textfield_sbyy");
			if (cwxx != "") {
				cwxx += "," + bccw;
			} else {
				cwxx = bccw;
			}
			rowmx.set(gxzdmx, 1);// 修改数据
			row.set(gxzd, 1);// 修改数据
			row.set("yd_textfield_sbyy", cwxx);// 修改数据
			}
			}
		}
		SaveServiceHelper.save(dy);// 保存
	}

	public static void jymxhg(List<String>  pcwlin,List<String>  pcwlinpp, String bzm, String zdm, String xgbz, String jczl,QFilter filter,List<String>  ppfd) {
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
		Date dt = new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter8 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlinpp);// 去除排除物料品牌分单
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表
		//非品牌分单单据
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
				new QFilter[] { filter1, filter2,filter3, filter4,filter5,filter,filter7,filter9,filter10 }, null);
		//品牌分单单据
		DataSet yd_fhmxbykhpp = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
				new QFilter[] { filter1,filter3, filter4,filter5,filter,filter6,filter8,filter9,filter10 }, null);
		yd_fhmxbykh=yd_fhmxbykh.union(yd_fhmxbykhpp);
		String[] fhmxbzd = new String[] { "pt", zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		DataSet kh = QueryServiceHelper.queryDataSet("jymx", bzm,
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield id",
				new QFilter[] { filterSh }, null);
		filterSh = QFilter.of("status=?", "C");
		// 查询已审核的物料编码
		DataSet wl = QueryServiceHelper.queryDataSet("jymx", jczl, "id wlid", new QFilter[] { filterSh }, null);
		// 查询已审核的物料库存信息
		DataSet wlkc = QueryServiceHelper.queryDataSet("jymx", "bd_materialinventoryinfo", "masterid id",
				new QFilter[] { filterSh }, null);
		// 查询已审核的物料销售信息
		DataSet wlxs = QueryServiceHelper.queryDataSet("jymx", "bd_materialsalinfo", "masterid id",
				new QFilter[] { filterSh }, null);
		// join出合规物料
		DataSet hgwl = wl.join(wlkc).on("wlid", "id").select(new String[] { "wlid" }).finish().join(wlxs)
				.on("wlid", "id").select(new String[] { "wlid" }).finish();
		//hgwl.print(true);
		// join出有对应物料编码的平台物料并于合规物料进行判断
		DataSet joinDataset = yd_fhmxbykh.join(kh).on(zdm, "bm").on("pt", "pt")
				.select(fhmxbzd, new String[] { "bm", "id" }).finish().leftJoin(hgwl).on("id", "wlid")
				.select(fhmxbzd, new String[] { "bm", "wlid" }).finish();
		//joinDataset.print(true);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			String number = row.getString("wlid");
			if (number == null) {
				bczkh.add(row.getString(zdm));
			}
		}
		filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
				new QFilter[] { filter1, filter2, filter3, filter4,filter });
		String bccw = "物料未审核或物料库存,销售信息未审核";
		// if ("yd_ckdygx".equals(bzm)) {
		// bccw = "仓库不存在";
		// } else if ("yd_wldygx".equals(bzm)) {
		// bccw = "物料不存在";
		// }
		for (DynamicObject row : dy) {
			String cwxx = row.getString("yd_textfield_sbyy");
			if (cwxx != "") {
				cwxx += "," + bccw;
			} else {
				cwxx = bccw;
			}
			row.set(xgbz, 1);// 修改数据
			row.set("yd_textfield_sbyy", cwxx);// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
	}
}
