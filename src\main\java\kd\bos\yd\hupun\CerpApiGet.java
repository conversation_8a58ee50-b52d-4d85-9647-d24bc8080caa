package kd.bos.yd.hupun;

import kd.bos.algo.DataSet;
import kd.bos.context.RequestContext;
import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.exception.KDException;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CerpApiGet extends AbstractTask {

	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {
		// TODO Auto-generated method stub
		// 1、获取日志微服务接口
		ILogService logService1 = ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取万里牛生成销售出库单启动开始", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
//		//查询e3天数
//		DynamicObject dy = BusinessDataServiceHelper.loadSingle("yd_khdygx","yd_integerfield_ts",new QFilter[] {QFilter.of("yd_combofield_pt =?", "1")});
//		int ts = 7;
//		int e3ts=dy.getInt("yd_integerfield_ts");
////		int e3ts = 1;
//		if(e3ts>0){
//			ts=e3ts;
//		}
//		int sj = 23;
//		Date dt = new Date();
//		Calendar rightNow = Calendar.getInstance();
//		for (int t = ts; t >0; t--) {
//			rightNow.setTime(dt);
//			rightNow.add(Calendar.DAY_OF_MONTH, -t);
//			for (int s = 0; s <= sj; s++) {
//				Date dtks = rightNow.getTime();
//				rightNow.add(Calendar.HOUR, 1);
//				Date dtjs = rightNow.getTime();
//				String ks = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtks);
//				String js = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtjs);
//
////				CerpPost.E3ReturnListGetNew(ks, js);
//			}
//		}

		List<String> platList = new ArrayList<>();
		platList.add("4");  // 万里牛

		// 拉单
		AppLogInfo logInfo2 = OperationLogUtil.buildLogInfo("保存", "获取万里牛出库单生成发货明细启动开始", "sm", "yd_fhmxb");
		logService1.addLog(logInfo2);
		CerpPost.CerpOrderListGet(1);
		logInfo2 = OperationLogUtil.buildLogInfo("保存", "获取万里牛出库单生成发货明细启动结束", "sm", "yd_fhmxb");
		logService1.addLog(logInfo2);

		// 打标签
		AppLogInfo logInfo3 = OperationLogUtil.buildLogInfo("保存", "发货明细打标签启动开始", "sm", "yd_fhmxb");
		logService1.addLog(logInfo3);
		platList.stream().forEach(item->{
			sign(item);
		});
		logInfo3 = OperationLogUtil.buildLogInfo("保存", "发货明细打标签启动结束", "sm", "yd_fhmxb");
		logService1.addLog(logInfo3);

		// 生成销售出库单
		AppLogInfo logInfo4 = OperationLogUtil.buildLogInfo("保存", "发货明细生成销售出库单启动开始", "sm", "im_saloutbill");
		logService1.addLog(logInfo4);
		Map<String,String> ptMap = new HashMap<>();
		ptMap.put("4", "5");
		CerpSaloutPost.saveSaloutbill(platList,ptMap);
		logInfo4 = OperationLogUtil.buildLogInfo("保存", "发货明细生成销售出库单启动结束", "sm", "im_saloutbill");
		logService1.addLog(logInfo4);


		logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取万里牛生成销售出库单启动结束", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
	}

	private void sign(String pt){

		//0.条件：参与合单=0 and 下游单号为空 and 整单剔除，日期
		//参与合单:fk_yd_checkboxfield_cyhd=0
		//整单剔除:fk_yd_checkboxfield_khwdy=0
//        1.1 剔除物料打标
		StringBuilder entryExcludeMaterUpdateSql = new StringBuilder("update tk_yd_fhmxentry set fk_yd_excludematerial=1 " )
				.append(" where exists ( \n")
				.append( "select 1 from tk_yd_pcwl tt,tk_yd_pcwlentry tt2 where tt.FId=tt2.FId ")
				.append("and tk_yd_fhmxentry.fk_yd_textfield_hpbh=tt2.fk_yd_textfield_bm ) ")
				.append(" and exists (select 1 from tk_yd_fhmx t2 where tk_yd_fhmxentry.FId=t2.FId and t2.fbillstatus='C' and t2.fk_yd_checkboxfield_khwdy=0 and t2.fk_yd_checkboxfield_cyhd=0 and t2.fk_yd_combofield_pt=?);");
		int entryExcludeMaterRes = DB.update(DBRoute.of("scm"),entryExcludeMaterUpdateSql.toString(),new Object[]{pt});

		//1.2 整单剔除物料打标
		StringBuilder excludeMaterSql = new StringBuilder("select distinct t.fid excludeId from tk_yd_fhmxentry t \n")
				.append( "where exists\n")
				.append(" (select 1 from tk_yd_fhmx t2 where t.FId=t2.FId and t2.fbillstatus='C' and t2.fk_yd_checkboxfield_khwdy=0 and t2.fk_yd_checkboxfield_cyhd=0 and t2.fk_yd_combofield_pt=?) and  t.fk_yd_excludematerial=? ");
		StringBuilder includeMaterSql = new StringBuilder("select distinct t.fid includeId from tk_yd_fhmxentry t \n")
				.append( "where exists\n")
				.append(" (select 1 from tk_yd_fhmx t2 where t.FId=t2.FId and t2.fbillstatus='C' and t2.fk_yd_checkboxfield_khwdy=0 and t2.fk_yd_checkboxfield_cyhd=0 and t2.fk_yd_combofield_pt=?) and  t.fk_yd_excludematerial=? ");
		//查询剔除物料单据id
		DataSet excludeMaterSet = DB.queryDataSet(this.getClass().getName(), DBRoute.of("scm") ,excludeMaterSql.toString(),new Object[]{pt,1});
		//查询没剔除物料单据id
		DataSet materSet = DB.queryDataSet(this.getClass().getName(), DBRoute.of("scm") ,includeMaterSql.toString(),new Object[]{pt,0});
		//查询整单剔除的单据ID
		DataSet allExcludeIdSet = excludeMaterSet.leftJoin(materSet).on("excludeId","includeId").select("excludeId","includeId").finish().where("includeId is null").select("excludeId");
		List<Object[]> ids = new ArrayList<>();
		allExcludeIdSet.forEach(
				t->ids.add(new Object[]{t.getString(0)})
		);
		allExcludeIdSet.close();
		//更新发货明细"整单剔除"字段为1
		if(ids.size()>0){
			String upExcluedSql = "update tk_yd_fhmx set fk_yd_checkboxfield_khwdy =1 where fid = ?";
			int[] upExcluedRes = DB.executeBatch(DBRoute.of("scm"),upExcluedSql,ids);
		}

		//更新剔除物料的金额放在：fk_yd_excludeamount
		//条件：参与合单:fk_yd_checkboxfield_cyhd=0
		//整单剔除:fk_yd_checkboxfield_khwdy=0
		// 分录剔除物料:fk_yd_excludematerial=1
		StringBuilder updateExcludeamountSql = new StringBuilder("/*dialect*/ update tk_yd_fhmx \n")
				.append("inner join \n" )
				.append("(select t.FId , sum(t.fk_yd_decimalfield_zje) zje from tk_yd_fhmxentry t where t.fk_yd_excludematerial=1 and \n")
				.append(" exists (select 1 from tk_yd_fhmx t2 where t.FId=t2.FId and fk_yd_checkboxfield_cyhd=0 \n")
				.append("and fk_yd_checkboxfield_khwdy=0 and t2.fk_yd_combofield_pt=? ) group by t.FId \n" )
				.append(") tt on tt.fid=tk_yd_fhmx.fid\n")
				.append( "set tk_yd_fhmx.fk_yd_excludeamount = tt.zje\n" )
				.append("where tk_yd_fhmx.fk_yd_checkboxfield_cyhd=0 and tk_yd_fhmx.fk_yd_checkboxfield_khwdy=0 and tk_yd_fhmx.fk_yd_combofield_pt=? ");

		int updateExcludeamountSqlRes = DB.update(DBRoute.of("scm"),updateExcludeamountSql.toString(),new Object[]{pt,pt});

		//2.1不在的客户打标
		StringBuilder updateCustomerSql = new StringBuilder("update tk_yd_fhmx set fk_yd_checkboxfield_khbcz =1 \n")
				.append("where not exists ( \n")
				.append("select 1 from tk_yd_khdygx tt,tk_yd_entryentitydykh tt2 where tt.FId=tt2.FId and tk_yd_fhmx.fk_yd_combofield_pt=tt.fk_yd_combofield_pt and tk_yd_fhmx.fk_yd_textfield_dpbh=tt2.fk_yd_textfield)\n")
				.append("and tk_yd_fhmx.fbillstatus='C' and tk_yd_fhmx.fk_yd_checkboxfield_khwdy=0 and tk_yd_fhmx.fk_yd_checkboxfield_cyhd=0 and tk_yd_fhmx.fk_yd_combofield_pt=?");
		int updateCustomerSqlRes = DB.update(DBRoute.of("scm"),updateCustomerSql.toString(),new Object[]{pt});

		//2.2被打上不在存的客户，检查是否存在，重新打标
		StringBuilder updateExistCustomerSql = new StringBuilder("update tk_yd_fhmx  set fk_yd_checkboxfield_khbcz =0 \n")
				.append("where exists ( \n")
				.append("select 1 from tk_yd_khdygx tt,tk_yd_entryentitydykh tt2 where tt.FId=tt2.FId and tk_yd_fhmx.fk_yd_combofield_pt=tt.fk_yd_combofield_pt and tk_yd_fhmx.fk_yd_textfield_dpbh=tt2.fk_yd_textfield)\n")
				.append("and tk_yd_fhmx.fbillstatus='C' and tk_yd_fhmx.fk_yd_checkboxfield_khwdy=0 and tk_yd_fhmx.fk_yd_checkboxfield_cyhd=0 and tk_yd_fhmx.fk_yd_checkboxfield_khbcz=1 and tk_yd_fhmx.fk_yd_combofield_pt=?");
		int updateExistCustomerSqlRes = DB.update(DBRoute.of("scm"),updateExistCustomerSql.toString(),new Object[]{pt});

		//3.1不存在的仓库打标
		StringBuilder updateWarehouseSql = new StringBuilder("update tk_yd_fhmx  set fk_yd_checkboxfield_ckbcz =1 \n")
				.append("where not exists ( \n")
				.append("select 1 from tk_yd_ckdygx tt,tk_yd_entryentitydyck tt2 where tt.FId=tt2.FId and tk_yd_fhmx.fk_yd_combofield_pt=tt.fk_yd_combofield_pt and tk_yd_fhmx.fk_yd_textfield_ck=tt2.fk_yd_textfield)\n")
				.append("and tk_yd_fhmx.fbillstatus='C' and tk_yd_fhmx.fk_yd_checkboxfield_khwdy=0 and tk_yd_fhmx.fk_yd_checkboxfield_cyhd=0  and tk_yd_fhmx.fk_yd_combofield_pt=?");
		int updateWarehouseSqlRes = DB.update(DBRoute.of("scm"),updateWarehouseSql.toString(),new Object[]{pt});

		//3.2被打上不在存的仓库，检查是否存在，重新打标
		StringBuilder updateExistWarehouseSql = new StringBuilder("update tk_yd_fhmx  set fk_yd_checkboxfield_ckbcz =0 \n")
				.append("where exists ( \n")
				.append("select 1 from tk_yd_ckdygx tt,tk_yd_entryentitydyck tt2 where tt.FId=tt2.FId and tk_yd_fhmx.fk_yd_combofield_pt=tt.fk_yd_combofield_pt and tk_yd_fhmx.fk_yd_textfield_ck=tt2.fk_yd_textfield)\n")
				.append("and tk_yd_fhmx.fbillstatus='C' and tk_yd_fhmx.fk_yd_checkboxfield_khwdy=0 and tk_yd_fhmx.fk_yd_checkboxfield_cyhd=0 and tk_yd_fhmx.fk_yd_checkboxfield_ckbcz=1 and tk_yd_fhmx.fk_yd_combofield_pt=?");
		int updateExistWarehouseSqlRes = DB.update(DBRoute.of("scm"),updateExistWarehouseSql.toString(),new Object[]{pt});

		//4.1不存在的物料打标-发货明细分录
		StringBuilder entryEntryMaterialUpdateSql = new StringBuilder("update tk_yd_fhmxentry set fk_yd_mxwlbcz =1 \n")
				.append("where not exists ( \n")
				.append("select 1 from tk_yd_wldygx tt,tk_yd_entryentitydywl tt2 where tt.FId=tt2.FId and tt.fk_yd_combofield_pt=? and tk_yd_fhmxentry.fk_yd_textfield_hpbh=tt2.fk_yd_textfield)\n")
				.append("and  exists (select 1 from tk_yd_fhmx t2 where tk_yd_fhmxentry.FId=t2.FId and t2.fbillstatus='C' and t2.fk_yd_checkboxfield_khwdy=0 and t2.fk_yd_checkboxfield_cyhd=0 and t2.fk_yd_combofield_pt=?)")
				.append(" and tk_yd_fhmxentry.fk_yd_excludematerial=0");
		int updateEntryMaterialUpdateSqlRes = DB.update(DBRoute.of("scm"),entryEntryMaterialUpdateSql.toString(),new Object[]{pt,pt});
//        //4.2不存在的物料打标-发货明细表头 fk_yd_checkboxfield_wlbcz
		StringBuilder materialUpdateSql = new StringBuilder("update tk_yd_fhmx  set fk_yd_checkboxfield_wlbcz=1 \n")
				.append("where exists (")
				.append("select 1 from tk_yd_fhmxentry t where not exists (\n")
				.append("select 1 from tk_yd_wldygx tt,tk_yd_entryentitydywl tt2 where tt.FId=tt2.FId and tt.fk_yd_combofield_pt =? and t.fk_yd_textfield_hpbh=tt2.fk_yd_textfield) \n")
				.append(" and t.FId=tk_yd_fhmx.FId and tk_yd_fhmx.fbillstatus='C' and tk_yd_fhmx.fk_yd_checkboxfield_khwdy=0 and tk_yd_fhmx.fk_yd_checkboxfield_cyhd=0 and tk_yd_fhmx.fk_yd_combofield_pt=?)");
		int updateMaterialUpdateSqlRes = DB.update(DBRoute.of("scm"),materialUpdateSql.toString(),new Object[]{pt,pt});

		//4.3被打上不在存的物料，检查是否存在，重新打标(发货明细分录)
		StringBuilder entryExistEntryMaterialUpdateSql = new StringBuilder("update tk_yd_fhmxentry set fk_yd_mxwlbcz =0 \n")
				.append("where exists ( \n")
				.append("select 1 from tk_yd_wldygx tt,tk_yd_entryentitydywl tt2 where tt.FId=tt2.FId and tt.fk_yd_combofield_pt=? and tk_yd_fhmxentry.fk_yd_textfield_hpbh=tt2.fk_yd_textfield)\n")
				.append("and  exists (select 1 from tk_yd_fhmx t2 where tk_yd_fhmxentry.FId=t2.FId and t2.fbillstatus='C' and t2.fk_yd_checkboxfield_khwdy=0 and t2.fk_yd_checkboxfield_cyhd=0 and t2.fk_yd_checkboxfield_wlbcz=1 and t2.fk_yd_combofield_pt=?)")
				.append(" and tk_yd_fhmxentry.fk_yd_mxwlbcz=1");
		int updateExistEntryMaterialUpdateSqlRes = DB.update(DBRoute.of("scm"),entryExistEntryMaterialUpdateSql.toString(),new Object[]{pt,pt});
//        //4.2被打上不在存的物料，检查是否存在，重新打标(发货明细表头)- fk_yd_checkboxfield_wlbcz
		StringBuilder materialExistUpdateSql = new StringBuilder("update tk_yd_fhmx  set fk_yd_checkboxfield_wlbcz=0 \n")
				.append("where not exists (")
				.append("select 1 from tk_yd_fhmxentry t where t.FId = tk_yd_fhmx.FId and t.fk_yd_mxwlbcz=1 ) \n")
				.append(" and  tk_yd_fhmx.fbillstatus='C' and tk_yd_fhmx.fk_yd_checkboxfield_khwdy=0 and tk_yd_fhmx.fk_yd_checkboxfield_cyhd=0 and tk_yd_fhmx.fk_yd_checkboxfield_wlbcz=1 and  tk_yd_fhmx.fk_yd_combofield_pt=?");
		int updateExistMaterialUpdateSqlRes = DB.update(DBRoute.of("scm"),materialExistUpdateSql.toString(),new Object[]{pt});

        //更新结算币别
        StringBuilder updateSettleCurrencySql = new StringBuilder("/*dialect*/ update tk_yd_fhmx \n")
                .append("inner join \n" )
                .append(" (select t2.fid,t4.fk_yd_currency_code from pct0001_sys.T_BD_Currency t2,tk_yd_currency_type t3,tk_yd_entryentitycurrency t4 where t3.FId=t4.FId and t4.fk_yd_currency=t2.FID) \n")
                .append(" tt on tt.fk_yd_currency_code=tk_yd_fhmx.fk_yd_currency \n")
                .append(" set tk_yd_fhmx.fk_yd_settlecurrency=tt.fid \n")
                .append("where tk_yd_fhmx.fk_yd_checkboxfield_cyhd=0 and tk_yd_fhmx.fk_yd_checkboxfield_khwdy=0 and tk_yd_fhmx.fk_yd_combofield_pt=? ");
        int updateSettleCurrencySqlRes = DB.update(DBRoute.of("scm"),updateSettleCurrencySql.toString(),new Object[]{pt});

		//5.1查出匹配的汇率
		StringBuilder queryRateSql = new StringBuilder("/*dialect*/ select t.fid,t2.FEXRATE from tk_yd_fhmx t,pct0001_sys.T_BD_EXRATE t2,pct0001_sys.T_BD_ExRateTable t5,tk_yd_currency_type t3,tk_yd_entryentitycurrency t4 \n")
				.append("where t2.FENABLE=1 and t3.fid=t4.FId and t2.FGROUPID=t5.FID and t5.FNUMBER='01' and t4.fk_yd_currency=t2.FORGCURID \n")
				.append("and DATE_FORMAT(t2.FEFFECTDATE,'%Y-%m')=DATE_FORMAT(date_add(t.fk_yd_datetimefield_xdsj,interval-1 month),'%Y-%m') and t.fk_yd_combofield_pt=t3.fk_yd_combofield_pt and t.fk_yd_currency=t4.fk_yd_currency_code\n")
				.append(" and (t.fk_yd_exrate is null or t.fk_yd_exrate<=0) and t.fk_yd_combofield_pt=?");
		List<Object[]> lstRate = new ArrayList<>();
		DataSet queryRateSqlRes = DB.queryDataSet(this.getClass().getName(),DBRoute.of("scm"),queryRateSql.toString(),new Object[]{pt});
		queryRateSqlRes.forEach(
				t->lstRate.add(new Object[]{t.getBigDecimal(1), t.getString(0)})
		);
		queryRateSqlRes.close();
		//5.2更新汇率
		if(lstRate.size()>0){
			int[] updateRateSqlRes = DB.executeBatch(DBRoute.of("scm"),"update tk_yd_fhmx set fk_yd_exrate = ? where fid= ?",lstRate);
		}
	}

}
