package kd.bos.yd.hupun;

import json.JSON;
import json.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.util.StringUtils;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

public class CerpPost {

	/**
	 * 万里牛订单写入
	 */
	public static void CerpOrderListGet(int page) {
		try {
			Map<String,String> param = new HashMap<>();
			param.put("limit", "200");
			param.put("page", page+"");
//			param.put("modify_time", "2023-03-01");
//			param.put("modify_time_end", "2023-03-20");
//			param.put("bill_code", "OD2512304070000136");

			List<Map> data = PostCerp(param,ApiUtil.outbill);
			if(data.size()>0){
				BillSave(data);
				if(data.size()==200){
					CerpOrderListGet(++page);
				}
			}
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	// param
	public static List<Map> PostCerp(Map<String, String> param, String api) {
		// 1、获取日志微服务接口
		ILogService logService1 =  ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("查询", "万里牛出库单拉单开始", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);

		if(StringUtils.isEmpty(param.get("limit"))){
			param.put("limit", "200");
		}
		if(StringUtils.isEmpty(param.get("page"))){
			param.put("page", "1");
		}

		String requestTime = String.valueOf(System.currentTimeMillis()/1000);
		StringBuffer signValue = new StringBuffer();
		signValue.append(ApiUtil.secret);
		signValue.append("_app=");
		signValue.append(ApiUtil.key);
		signValue.append("&_t=");
		signValue.append(requestTime);
		// 排序
		Map<String, String> map = param.entrySet().stream().sorted(Map.Entry.comparingByKey())
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

		try {
			if (map != null) {
				for (String key : map.keySet()) {
					String value = map.get(key);
					signValue.append("&");
					signValue.append(URLEncoder.encode(key, "UTF-8"));
					signValue.append("=");
					signValue.append(URLEncoder.encode(value, "UTF-8"));
				}
			}
		}catch (UnsupportedEncodingException e){
			e.printStackTrace();
		}
		signValue.append(ApiUtil.secret);
		String sign = ApiUtil.getMd5(signValue.toString());

		map.put("_app",ApiUtil.key);
		map.put("_t",requestTime);
		map.put("_sign",sign);

		String result = ApiUtil.doPost(ApiUtil.URI + api, map);
		// 3、记录操作日志
		logInfo1 = OperationLogUtil.buildLogInfo("查询", "万里牛出库单拉单结束", "sm", "yd_fhmxb");

		JSONObject json = JSONObject.parseObject(result);
		List<Map> list = new ArrayList<>();

		String code = json.getString("code");
		if("0".equals(code)){
			list = JSON.parseArray(String.valueOf(json.get("data")),Map.class);
		}else{
			logInfo1 = OperationLogUtil.buildLogInfo("保存", "万里牛出库单拉单结束，查询失败："+code, "sm", "yd_fhmxb");
		}
		logService1.addLog(logInfo1);
		return list;
	}


	public static void BillSave(List<Map> orderListGets) throws Exception {
		try{
			// 查询本地是否已经存在该万里牛订单，单据规则，CERP_0）_（万里牛的订单门店）_（万里牛订单编码-----对应本地订单明细的编码billno
			Object[] Billnos = orderListGets.stream().map(item->"CERP_0_" + "_" + item.get("shop_name") + "_" + item.get("inv_no")).collect(Collectors.toList()).toArray();
			QFilter qFilter = new QFilter("billno", QCP.in, Billnos);
			DynamicObject[] dObject = BusinessDataServiceHelper.load("yd_fhmxb", "billno",new QFilter[] { qFilter });
			// 格式转换
			List<DynamicObject> listQuery = Arrays.asList(dObject);
			Map<String,String> mapQuery = listQuery.stream().collect(Collectors.toMap(item->String.valueOf(item.get("billno")), item->String.valueOf(item.get("billno"))));

			// 排除本地已存在的数据，组装需要新增的数据
			List<DynamicObject> objs = orderListGets.stream().filter(item->mapQuery.get("CERP_0_" + "_" + item.get("shop_name") + "_" + item.get("inv_no"))==null).map(item->{
				// 处理空值
				setEmpData(item);

				DynamicObject bill = setBillItem(item);
				// 设置明细
				setItemInfo(item,bill);

				return bill;
			}).collect(Collectors.toList());

			if (objs.size() > 0) {
				DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
				for (int i = 0; i < objs.size(); i++) {
					objsSAVE[i] = objs.get(i);
				}
				SaveServiceHelper.save(objsSAVE);
				// 1、获取日志微服务接口
				ILogService logService1 = ServiceFactory.getService(ILogService.class);
				// 2、构建日志信息，参考示例如下
				AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
				// 3、记录操作日志
				logService1.addLog(logInfo1);
			}
		}catch (Exception e){
			e.printStackTrace();
			throw new Exception(e.getMessage());
		}
	}

	/**
	 * 设置表头通用数据
	 * @param order
	 * @return
	 */
	private static DynamicObject setBillItem(Map order){

		Date bill_time = null;
		Date bill_date = null;
		if(order.get("bill_date") != null){
			bill_time = Timestamp.valueOf(String.valueOf(order.get("bill_date")));
			LocalDate localDate = bill_time.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			bill_date=java.sql.Date.valueOf(localDate);
		}

		// 创建单据对象
		DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
		// 设置单据属性
		bill.set("billno", "CERP_0_" + "_" + order.get("shop_name") + "_" + order.get("inv_no")); // 单据编号为平台_退货_店铺_订单号
		bill.set("createtime", bill_time);  // 出库日期
		bill.set("yd_textfield_ddbh", order.get("inv_no")); // 订单号
		bill.set("yd_textfield_dpbh", order.get("shop_name"));		// 店铺名称
		bill.set("yd_sdname", order.get("shop_name"));				// 店铺名称
		bill.set("yd_orderstatus", order.get("bill_type"));	// 订单状态
		bill.set("yd_totalamount", order.get("good_total_price"));	// 订单总金额
		bill.set("yd_totalamount_amz", order.get("good_total_price_ex_tax"));	// 订单总金额
		bill.set("yd_dealcode", order.get("tp_tid"));		// 交易号
		bill.set("yd_textfield_ck", order.get("storage_name"));			// 发货仓库
		bill.set("yd_fhckmc", order.get("storage_name")); 				// 发货仓库
		bill.set("yd_datetimefield_xdsj", bill_time);     // 下单时间
		bill.set("yd_good_offer", order.get("good_offers_amount"));	// 卖家优惠（子）
		bill.set("yd_currency", order.get("currency"));		// 货币类别
		bill.set("yd_seller_offer", order.get("seller_offer_amount"));		// 卖家券
		bill.set("yd_tax", order.get("tax"));		// 税
		bill.set("yd_combofield_pt", "4");	// 平台
		bill.set("billstatus", "C");		// 状态
		bill.set("yd_datefield_fhrq", bill_date);	// 出库时间

//		bill.set("yd_addtime", addtime);// 下单时间
//		bill.set("yd_lylx", lylx);		// 来源类型
//		bill.set("yd_sdid", sdid);	// 商店id
//		bill.set("yd_checkboxfield_sfsg", sg);			// 是否手工
//		bill.set("yd_checkboxfield_sfkp", kp);			// 是否开票
//		bill.set("yd_bz", bz);			//备注
//		bill.set("yd_dealcode",deal_code);		// 交易号
//		bill.set("yd_oridealcode", oridealcode);	// 换货原始交易号
//		bill.set("yd_decimalfield_yf", shipping_fee);   // 运费
//		bill.set("yd_shippingname",shippingname);	// 快递名称
//		bill.set("yd_shippingsn", shippingsn);		// 快递单号
//		bill.set("yd_shippingcode", shippingcode);	// 快递编码
//		bill.set("yd_payment", payment);	// 已付金额
//		bill.set("yd_issplit", issplit);	// 是否拆分子单	0：否，1：是
//		bill.set("yd_issplitnew", issplitnew);	// 是否拆分子单	0：否，1：是
//		bill.set("yd_iscombinenew", iscombinenew);	// 是否合并新单	0：否，1：是
//		bill.set("yd_iscombine", iscombine);	// 是否被合并	0：否，1：是
//		bill.set("yd_iscopy", iscopy);	// 是否复制单	0：否，1：是
//		bill.set("yd_ishh", ishh);	// 是否换货单	0：否，1：是
//		bill.set("yd_weigh", weigh);	// 总重量(单位KG)
//		bill.set("yd_qdcode",qdcode);	// 渠道代码
//		bill.set("yd_qdname", qdname);	// 渠道名称
//		bill.set("yd_shippingtimefh", shippingtimefh);	// E3的出库时间
//		bill.set("yd_shippingtimeck", shippingtimeck);	// E3的发库时间
//		bill.set("yd_paytime", StringUtils.isEmpty(paytime)?null:Timestamp.valueOf(paytime)); // 支付时间
//		bill.set("yd_receiveraddress", receiveraddress);	// 收货地址
//		bill.set("yd_username", username);	// 收货人昵称
//		bill.set("yd_receivername", receivername);	// 收货姓名
//		bill.set("yd_receivermobile", receivermobile);	// 收货手机号
//		bill.set("yd_oriordersn", order.get("tp_tid"));		// 换货原始订单号

		return bill;

	}

	/**
	 *
	 * @param order
	 * @param bill
	 * @return
	 */
	private static void setItemInfo(Map order,DynamicObject bill){

		// 获取单据体集合
		DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
		// 获取单据体的Type
		DynamicObjectType type = entrys.getDynamicObjectType();
		// 根据Type创建单据体对象
		List<Map> orderItem = JSON.parseArray(String.valueOf(order.get("details")),Map.class);
		List<DynamicObject> listEntry = orderItem.stream().map(item->{
			DynamicObject entry = new DynamicObject(type);
			setEmpData(item);

			entry.set("yd_decimalfield_sl", item.get("nums"));   // 商品数量
			entry.set("yd_decimalfield_dj", item.get("online_price"));	// 单价
			entry.set("yd_textfield_hpbh", item.get("sku_no"));			// sku

			BigDecimal nums = item.get("nums") == null ? BigDecimal.ZERO: new BigDecimal(String.valueOf(item.get("nums")));
			BigDecimal price = item.get("online_price") == null ? BigDecimal.ZERO: new BigDecimal(String.valueOf(item.get("online_price")));
			entry.set("yd_decimalfield_zje", nums.multiply(price));			// sku

//			entry.set("yd_skuid", item.get("nums"));					// sku_id
//			entry.set("yd_goodsprice", item.get("online_price"));			// 商品单价
//			entry.set("yd_decimalfield_zje", share_payment);   // 均摊实付金额
//			entry.set("yd_goodssn", goodssn);				// 货号
//			entry.set("yd_goodsid", goodsid);				// 货号id
//			entry.set("yd_shopprice", shopprice);			// 商品网店在售价格
//			entry.set("yd_originalordersn", originalordersn);		// 商品原始订单号
//			entry.set("yd_originaldealcode", originaldealcode);		// 商品原始交易编号
//			entry.set("yd_subdealcode", subdealcode);		// 子交易号
//			entry.set("yd_numiid", numiid);						// 平台商品ID
//			entry.set("yd_rowid", rowid);					// id
//			entry.set("yd_goodsname", goodsname);			// 商品名
//			entry.set("yd_isgift", isgift);					// 是否赠品	0:非赠品，1:赠品
//			entry.set("yd_shareshippingfee", shareshippingfee);		// 平均物流成本
//			entry.set("yd_barcode", barcode);			// barcode

			return entry;
		}).collect(Collectors.toList());

		entrys.addAll(listEntry);
	}

	private static void setEmpData(Map item){
		// 处理空值
		item.forEach((k,v)->{
			if ((v instanceof String)){
				String value = String.valueOf(v);
				if(StringUtils.isEmpty(value)){
					v = null;
				}
			}
		});
	}
}
