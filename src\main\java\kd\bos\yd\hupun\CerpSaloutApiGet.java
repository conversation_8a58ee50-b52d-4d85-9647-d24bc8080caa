package kd.bos.yd.hupun;

import kd.bos.algo.DataSet;
import kd.bos.context.RequestContext;
import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.exception.KDException;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CerpSaloutApiGet extends AbstractTask {

	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {
		// TODO Auto-generated method stub
		// 1、获取日志微服务接口
		ILogService logService1 = ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细生成销售出库单启动开始", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
//		//查询e3天数
//		DynamicObject dy = BusinessDataServiceHelper.loadSingle("yd_khdygx","yd_integerfield_ts",new QFilter[] {QFilter.of("yd_combofield_pt =?", "1")});
//		int ts = 7;
//		int e3ts=dy.getInt("yd_integerfield_ts");
////		int e3ts = 1;
//		if(e3ts>0){
//			ts=e3ts;
//		}
//		int sj = 23;
//		Date dt = new Date();
//		Calendar rightNow = Calendar.getInstance();
//		for (int t = ts; t >0; t--) {
//			rightNow.setTime(dt);
//			rightNow.add(Calendar.DAY_OF_MONTH, -t);
//			for (int s = 0; s <= sj; s++) {
//				Date dtks = rightNow.getTime();
//				rightNow.add(Calendar.HOUR, 1);
//				Date dtjs = rightNow.getTime();
//				String ks = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtks);
//				String js = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtjs);
//
////				CerpPost.E3ReturnListGetNew(ks, js);
//			}
//		}
		// 直接查7天数据，按分页模式取值
		List<String> platList = new ArrayList<>();
		platList.add("4");  // 万里牛

//		platList.stream().forEach(item->{
//			hitLabel(item);
//		});
//		CerpSaloutPost.saveSaloutbill(platList);
		logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细生成销售出库单启动结束", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
	}

}
