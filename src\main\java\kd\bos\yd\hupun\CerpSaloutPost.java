package kd.bos.yd.hupun;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.db.DB;
import kd.bos.db.DBRoute;
import kd.bos.entity.operate.result.IOperateInfo;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.IFormView;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.yd.tcyp.ABillServiceHelper;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

public class CerpSaloutPost {

	public static void saveSaloutbill(List<String> platList,Map<String,String> ptMap) {

		List<Map<String,String>> fhmxbList = queryDataList(platList);

		// 合单店铺合单维度，平台+店铺+发货日期+商品编码+批次，数量累加,再以平台+店铺+发货日期分组为一个出库单
		Map<String, List<Map<String, String>>> fhmxb_hd = fhmxbList.stream().filter(item->"1".equals(item.get("hzcl"))).collect(Collectors.groupingBy(d -> {
			// 分组，以设定的key为维度，分割成一个个的list
			return d.get("pt") + "_#_" + d.get("ptkh") + "_#_" + d.get("fhrq") + "_#_" + d.get("ptwl") + "_#_" + d.get("ph");
		}));
		// 不合单店铺,根据查询条件帅选后单据编号分组一对一生成出库单
		Map<String, List<Map<String, String>>> fhmxb_no_hd = fhmxbList.stream().filter(item->"2".equals(item.get("hzcl"))).collect(Collectors.groupingBy(d -> {
			// 分组，以设定的key为维度，分割成一个个的list
			return d.get("billno");
		}));

		// 获取不合单客户
		Map<String, String> mapkh = fhmxbList.stream().filter(item->"2".equals(item.get("hzcl"))).collect(Collectors.toMap(
				item-> item.get("kh"), item-> item.get("kh"), (e1, e2) -> e1, LinkedHashMap::new)
		);
		// 查询客户对应税率
		QFilter filter = new QFilter("number", QCP.in, mapkh.values());
		DynamicObject[] khojb = BusinessDataServiceHelper.load("bd_customer", "taxrate.number,number",
				new QFilter[] { filter });
		Map<String,String> slMap = new HashMap<>();
		for (DynamicObject rowdy : khojb) {
			slMap.put(rowdy.getString("number"),rowdy.getString("taxrate.number"));
		}

		String number1 = "2101";
		String number2 = "210";

//		forEach(0,fhmxb_hd.entrySet(), (index,item)->{});
		List<Object[]> errorList = new ArrayList();
		List<Object[]> resultList = new ArrayList();

		AtomicInteger index2 = new AtomicInteger(0); // 索引
		System.out.println("---数量------"+fhmxb_no_hd.entrySet().size());
		// 处理不合单
		fhmxb_no_hd.entrySet().stream().forEach(item->{
			// 创建模板
			IFormView view = ABillServiceHelper.createAddView("im_saloutbill");

			Map itemData = item.getValue().get(0);
			String billno = String.valueOf(itemData.get("billno"));
			System.out.println(index2.getAndIncrement()+"----单号-----"+billno);

			// 创建子模板,没有合单，只保存当前单
			view.getModel().createNewEntryRow("yd_entryentity");
			view.getModel().setValue("yd_textfield_fhmxdh", billno, 0);

			view.getModel().setValue("org", itemData.get("zz"));	// 组织
			view.getModel().setValue("bizorg", itemData.get("zz")); // 组织
			view.getModel().setValue("biztime", itemData.get("fhrq")); // 发货日期
			view.getModel().setValue("yd_yc", 0); // 流程判断是否重复
			view.getModel().setItemValueByNumber("yd_textfield_fhmxdh", billno); // 单据编号
			view.getModel().setItemValueByNumber("billtype", "im_SalOutBill_STD_BT_S"); // 单据类型
			view.getModel().setItemValueByNumber("customer", String.valueOf(itemData.get("kh"))); // 客户
			view.getModel().setValue("yd_tbly", ptMap.get(itemData.get("pt")+""));
			view.getModel().setItemValueByNumber("exchangerate", String.valueOf(itemData.get("exrate"))); // 汇率
			view.getModel().setValue("currency",  itemData.get("currency")); // 本位币
			view.getModel().setValue("settlecurrency",  itemData.get("settlecurrency")); // 结算币

//			view.getModel().setItemValueByNumber("yd_dzdkh", String.valueOf(itemData.get("kh")));// 单据编号

            BigDecimal sum = getBigDecimal(itemData.get("zjg")); // 总价
			BigDecimal sumamz = getBigDecimal(itemData.get("zjgamz")); // 总价亚马逊
            BigDecimal offer = getBigDecimal(itemData.get("offer")); // 卖家优惠子
            BigDecimal soffer = getBigDecimal(itemData.get("soffer")); // 卖家券
			BigDecimal exrate = getBigDecimal(itemData.get("exrate")); // 汇率
			BigDecimal tax = getBigDecimal(itemData.get("tax")); // 税
            // 剔除的物料单据 总价
            BigDecimal sum_nwl = item.getValue().stream().filter(data->"true".equals(data.get("tc"))).map(data->getBigDecimal(data.get("sl")).multiply(getBigDecimal(data.get("dj")))).reduce(BigDecimal.ZERO,BigDecimal::add).setScale(9,BigDecimal.ROUND_HALF_UP);
            // 剔除物料后单据总价 --- 总价简去剔除价格，减少循环计算的处理
//			BigDecimal sum_wl = item.getValue().stream().filter(data->"false".equals(data.get("tc"))).map(data->new BigDecimal(String.valueOf(data.get("sl"))).setScale(9,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(String.valueOf(data.get("dj"))).setScale(9,BigDecimal.ROUND_HALF_UP))).reduce(BigDecimal.ZERO,BigDecimal::add);
            BigDecimal sum_wl = sum.subtract(sum_nwl);

            // 明细总价（亚马逊不管剔出物料）
			BigDecimal totalMoney = item.getValue().stream().map(data->getBigDecimal(data.get("sl")).multiply(getBigDecimal(data.get("dj")))).reduce(BigDecimal.ZERO,BigDecimal::add).setScale(9,BigDecimal.ROUND_HALF_UP);

			AtomicInteger index = new AtomicInteger(0); // 索引
			item.getValue().stream().filter(data->"false".equals(data.get("tc"))).forEach(data->{
				int i = index.getAndIncrement();
				view.getModel().createNewEntryRow("billentry"); // 创建明细

				view.getModel().setItemValueByNumber("material", data.get("wl"), i); // 物料
				view.getModel().setValue("qty", data.get("sl"),i); // 数量
				view.getModel().setItemValueByNumber("warehouse", data.get("ck"), i); // 仓库
				view.getModel().setItemValueByNumber("taxrateid", slMap.get(data.get("kh")), i); // 税率

//				view.getModel().setValue("yd_ph", ph, i);
//				view.getModel().setValue(je, share_payment, index);
//				view.getModel().setValue("yd_store", dpbm,i);
//				view.getModel().setValue("yd_store", dpbm,i);
//				view.getModel().setValue(je, hsjeft, j);
//				view.getModel().setValue("yd_textfield_fhmxdh", billno, j);

				BigDecimal sumRow = getBigDecimal(data.get("sl")).multiply(getBigDecimal(data.get("dj")).setScale(9,BigDecimal.ROUND_HALF_UP));
				// A重算后总价=A数量*A单价+剔除物料总价*（（A数量*A单价）/非剔除物料总价）
				BigDecimal sumData = sumRow;
				if(sum_wl.compareTo(BigDecimal.ZERO) != 0){
					sumData = sumRow.add(sum_nwl.multiply(sumRow.divide(sum_wl, 9,BigDecimal.ROUND_HALF_UP)));
				}

                BigDecimal easamount= BigDecimal.ZERO;
				BigDecimal expand = new BigDecimal("*********");
				if("1".equals(data.get("count")) && sum.compareTo(BigDecimal.ZERO) != 0){ // laz
					//产品价税合计=（万里牛提供的字段：全部商品总金额-卖家优惠子）*（A重算后总价/万里牛提供的字段：全部商品总金额）
					easamount = sum.subtract(offer).multiply(sumData.multiply(expand).divide(sum, 9,BigDecimal.ROUND_HALF_UP));
				}
				if("2".equals(data.get("count")) && sum.compareTo(BigDecimal.ZERO) != 0){ // 虾皮
					// 产品价税合计=（万里牛提供的字段：全部商品总金额-卖家券）*（A重算后总价/万里牛提供的字段：全部商品总金额）
					easamount = sum.subtract(soffer).multiply(sumData.multiply(expand).divide(sum, 9,BigDecimal.ROUND_HALF_UP));
				}
				if("3".equals(data.get("count")) && totalMoney.compareTo(BigDecimal.ZERO) != 0){ // 亚马孙
					// 产品价税合计=（万里牛提供的字段：全部商品总金额+税-卖家券）*（A重算后总价/万里牛提供的字段：全部商品明细总金额）
					easamount = sumamz.add(tax).subtract(soffer).multiply(sumData.multiply(expand).divide(totalMoney, 9,BigDecimal.ROUND_HALF_UP));
				}
				view.getModel().setValue("amountandtax", easamount.divide(expand).setScale(6,BigDecimal.ROUND_HALF_UP), i); // 产品价税合计
//				view.getModel().setValue("amountandtax", easamount.multiply(exrate).setScale(4,BigDecimal.ROUND_HALF_UP), i); // 产品价税合计

				String th = data.get("th");
				if ("true".equals(th)) {
					view.getModel().setItemValueByNumber("biztype", number1);
					view.getModel().setItemValueByNumber("invscheme", number1);
				} else {
					view.getModel().setItemValueByNumber("biztype", number2);
					view.getModel().setItemValueByNumber("invscheme", number2);
				}
			});

			// 保存
			OperationResult operationResult = ABillServiceHelper.saveOperate(view);
			// 判断保存结果
			if (operationResult.isSuccess()) {
				String djbh = "";
				Map<Object, String> map = operationResult.getBillNos();
				for (Map.Entry<Object, String> entry : map.entrySet()) {
					resultList.add(new Object[]{ entry.getValue(),billno });
				}
				// 进行提交操作
				operationResult = view.invokeOperation("submit");
				if (!operationResult.isSuccess()) {
					String errMessage = operationResult.getMessage() + ","; // 错误摘要
					// // 演示提取保存详细错误
					for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
						errMessage += errInfo.getMessage() + ",";
					}
					if (errMessage.length() > 1800) {
						errMessage = errMessage.substring(0, 1800);
					}
					QFilter filtersalout= new QFilter("billno", QCP.equals, djbh);
					DynamicObject[] saloutbill = BusinessDataServiceHelper.load("yd_im_saloutbill_ext", "yd_sftj",new QFilter[] { filtersalout });
					for (DynamicObject rowdy : saloutbill) {
						rowdy.set("yd_sftj", errMessage);// 修改数据
					}
					SaveServiceHelper.save(saloutbill);
				}
				ABillServiceHelper.exitView(view);
			} else {
				String errMessage = operationResult.getMessage() + ","; // 错误摘要
				// // 演示提取保存详细错误
				for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
					errMessage += errInfo.getMessage() + ",";
				}
				if (errMessage.length() > 1800) {
					errMessage = errMessage.substring(0, 1800);
				}
				errorList.add(new Object[]{ errMessage,billno });

				ABillServiceHelper.exitView(view);
			}
			view.close();

		});
		if (resultList.size() > 0) {
			DB.executeBatch(DBRoute.of("scm"),"update tk_yd_fhmx set fk_yd_textfield_xsckdh = ?,fk_yd_combofield_xyd='1' where fbillno= ?" ,resultList);
		}
		if (errorList.size() > 0){
			DB.executeBatch(DBRoute.of("scm"),"update tk_yd_fhmx set fk_yd_textfield_sbyy = ? where fbillno= ?" ,errorList);
		}

	}

	public static boolean isEmpty(Object obj) {
		if (obj == null)
			return true;
		else if (obj instanceof CharSequence)
			return ((CharSequence) obj).length() == 0;
		else if (obj instanceof Collection)
			return ((Collection) obj).isEmpty();
		else if (obj instanceof Map)
			return ((Map) obj).isEmpty();
		else if (obj.getClass().isArray())
			return Array.getLength(obj) == 0;
		return false;
	}

	private static BigDecimal getBigDecimal(Object obj){
		if(isEmpty(obj) || new BigDecimal(String.valueOf(obj)).compareTo(BigDecimal.ZERO) == 0){
			return BigDecimal.ZERO;
		}
		return new BigDecimal(String.valueOf(obj)).setScale(9,BigDecimal.ROUND_HALF_UP);
	}

	/**
	 * 获取全部需要处理的的发货明细单
	 * @return
	 */
	private static List<Map<String,String>> queryDataList(List<String> platList){
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = new QFilter("yd_combofield_pt", QCP.in, platList);// 平台
		QFilter filter3 = QFilter.of("yd_datetimefield_xdsj <?", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));// 下单时间小于今天
//		QFilter filter3 = QFilter.of(" 1=?", "1");// 下单时间小于今天
		QFilter filter4 = QFilter.of("yd_checkboxfield_khwdyzz=?", "0");// 整单剔除
		QFilter filter5 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter6 = QFilter.of("yd_checkboxfield_ckbcz=?", "0");// 仓库存在
		QFilter filter10 = QFilter.of("yd_checkboxfield_wlbcz=?", "0");// 物料存在
		QFilter filter7 = QFilter.of("yd_exrate >?", "0");// 汇率
		QFilter filter8 = QFilter.of("billstatus=?", "C");// 状态为启用
//		QFilter filter9 = QFilter.of("yd_entryentity.yd_excludematerial=?", "0");// 明细剔除，当前不能排除，否则后续无法计算

		List<String> billList = new ArrayList<>();
		billList.add("CERP_0__AMZ-US_OD2512304130000206");
//		filter10.and(new QFilter("billno", QCP.in, billList));

		QFilter[] khdygxgl = new QFilter[] { filter2, filter8};
		// 查询平台对应客户
		DataSet khdygx = QueryServiceHelper.queryDataSet("yd_khdygx", "yd_khdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_count count,yd_entryentity.yd_textfield khbm,yd_entryentity.yd_basedatafield.number kh,yd_entryentity.yd_orgfield_dyzz zz,yd_entryentity.yd_combofield_hzcl hzcl ",
				khdygxgl, null);

		// 查询业务单元
		DataSet orgExt = QueryServiceHelper.queryDataSet("yd_bos_org_ext", "bos_org","yd_standardcurrency currency,masterid masterid",null, null);
		khdygx = khdygx.leftJoin(orgExt).on("zz","masterid").select(new String[]{"pt","hzcl","zz","kh","count","khbm"},new String[]{"currency"} ).finish();

		// 查询对应物料
		DataSet wldygx = QueryServiceHelper.queryDataSet("yd_wldygx", "yd_wldygx",
				"yd_entryentity.yd_textfield wlbm,yd_entryentity.yd_basedatafield.number wl,yd_entryentity.yd_pp pp,yd_combofield_pt pt", khdygxgl,
				null);

		// 查询对应仓库
		DataSet ckdygx = QueryServiceHelper.queryDataSet("yd_ckdygx", "yd_ckdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_textfield ckbm,yd_entryentity.yd_basedatafield.number ck,yd_entryentity.yd_basedatafield.createorg ckzz", khdygxgl,
				null);

		// 查询发货明细
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2,filter3, filter4,filter5, filter6, filter7, filter8, filter10 };
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("saveSaloutbill_query_fhmxb", "yd_fhmxb",
				"billno,yd_checkboxfield_th th,yd_totalamount zjg,yd_totalamount_amz zjgamz,yd_good_offer offer,yd_tax tax,yd_seller_offer soffer,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf,yd_entryentity.yd_excludematerial tc" +
						",yd_textfield_ck ptck,yd_currencyfield settlecurrency,yd_entryentity.yd_textfield_hpbh ptwl,yd_entryentity.yd_decimalfield_sl sl" +
						",yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je,yd_exrate exrate,yd_entryentity.yd_ph ph" +
						",yd_entryentity.yd_scrq scrq,yd_entryentity.yd_dqr dqr,yd_bz bz,yd_dealcode dealcode,yd_datefield_fhrq fhrq",
				fhmxgl, null);
//		for (Row row : yd_fhmxb) {
//			System.out.println(row);
//		}
		String[] baseStr1 = new String[]{"billno","th","pt","ptkh","yf","settlecurrency","ptck","fhrq","ptwl","ph","sl","dj","zjg","zjgamz","offer","soffer","je","scrq","dqr","bz","dealcode","tc","exrate","tax"};
		String[] baseStr2 = new String[]{"billno","th","pt","ptkh","yf","settlecurrency","ptck","fhrq","ptwl","ph","sl","dj","zjg","zjgamz","offer","soffer","je","scrq","dqr","bz","dealcode","tc","exrate","tax","hzcl","count","currency","zz","kh"};
		String[] baseStr3 = new String[]{"billno","th","pt","ptkh","yf","settlecurrency","ptck","fhrq","ptwl","ph","sl","dj","zjg","zjgamz","offer","soffer","je","scrq","dqr","bz","dealcode","tc","exrate","tax","hzcl","count","currency","zz","kh","wl","pp"};

		// 关联查询,leftjoin
		DataSet fhmxb = yd_fhmxb.join(khdygx).on("pt", "pt").on("ptkh", "khbm").select(baseStr1,new String[]{"hzcl","zz","kh","count","currency"} ).finish()
				.leftJoin(wldygx).on("pt", "pt").on("ptwl", "wlbm").select(baseStr2,new String[]{"wl","pp"}).finish()
				.join(ckdygx).on("pt", "pt").on("ptck", "ckbm").select(baseStr3,new String[]{"ck","ckzz"}).finish(); //关联查询

		// 拼装结果
		List<Map<String,String>> fhmxbList = new ArrayList<>();
		fhmxb.forEach(row->{
			Map<String,String> map = new HashMap();
			map.put("billno",row.getString("billno"));	// 单据编码
			map.put("th",row.getString("th"));		// 是否退货
			map.put("pt",row.getString("pt")); 		// 平台
			map.put("ptkh",row.getString("ptkh"));	// 店铺编号
			map.put("yf",row.getString("yf"));		// 运费
			map.put("ptck",row.getString("ptck"));	// 发货仓库
			map.put("fhrq",row.getString("fhrq"));	// 发货日期
			map.put("dealcode",row.getString("dealcode"));	// 交易号
            map.put("zjg",row.getString("zjg"));	// 总价格
			map.put("zjgamz",row.getString("zjgamz"));	// 总价格
            map.put("offer",row.getString("offer"));	// 卖家优惠子
            map.put("soffer",row.getString("soffer"));	// 卖家券
			map.put("exrate",row.getString("exrate"));	// 汇率
			map.put("tax",row.getString("tax"));	// 汇率
			map.put("settlecurrency",row.getString("settlecurrency"));	// 结算币

			map.put("ptwl",row.getString("ptwl"));	// 货品编码
			map.put("ph",row.getString("ph"));	//	批号
			map.put("sl",row.getString("sl"));	// 数量
			map.put("dj",row.getString("dj"));	// 单据
			map.put("je",row.getString("je"));	// 行总价
			map.put("scrq",row.getString("scrq"));	// 生产日期
			map.put("dqr",row.getString("dqr"));	// 到期日
			map.put("bz",row.getString("bz"));	// 备注
			map.put("tc",row.getString("tc"));	// 物料剔除

			map.put("hzcl",row.getString("hzcl"));	// 客户是否合单1合2不合
			map.put("zz",row.getString("zz"));	// 客户对应组织
			map.put("kh",row.getString("kh"));	// 客户
			map.put("count",row.getString("count"));	// 计算方式 laz=1,虾皮=2
			map.put("currency",row.getString("currency"));	// 本位币

			map.put("wl",row.getString("wl"));	//	物料编码
			map.put("pp",row.getString("pp"));	// 物料品牌

			map.put("ck",row.getString("ck"));	//	仓库
			map.put("ckzz",row.getString("ckzz"));	// 仓库组织

			fhmxbList.add(map);
		});
		return fhmxbList;
	}

	public static <T> void forEach(int startIndex,Iterable<? extends T> elements, BiConsumer<Integer, ? super T> action) {
		Objects.requireNonNull(elements);
		Objects.requireNonNull(action);
		if(startIndex < 0) {
			startIndex = 0;
		}
		int index = 0;
		for (T element : elements) {
			index++;
			if(index <= startIndex) {
				continue;
			}
			action.accept(index-1, element);
		}
	}
}
