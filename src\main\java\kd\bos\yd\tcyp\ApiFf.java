package kd.bos.yd.tcyp;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import kd.bos.algo.JoinType;
import kd.bos.yd.tcyp.utils.BizHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import json.JSONArray;
import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.operate.result.IOperateInfo;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.IFormView;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class ApiFf {
	public static String key="BSAKeqbv9Rd5Q2t1eqtL";
	public static String secret = "BSAK3oeNcZz7AXoQ1Tua";
//	public static String BSDZ = "http://8.142.214.170/e3/webopm/web/?app_act=api/ec&app_mode=func";
	//public static String BSDZ = "http://8.142.214.170/e3/webopm/web/?app_act=api/ec&app_mode=func";
	public static String BSDZ = "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
	public static SimpleDateFormat sdfrqd = new SimpleDateFormat("yyyy-MM-dd");

	public static String getMd5(String plainText) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(plainText.getBytes());
			byte b[] = md.digest();

			int i;

			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			// 32位加密
			return buf.toString();
			// 16位的加密
			// return buf.toString().substring(8, 24);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			return null;
		}

	}

	public static String doPost(String url, Map<String, String> param) {

		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();

		String resultString = "";
		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			if (param != null) {
				for (String key : param.keySet()) {
					builder.addParameter(key, param.get(key));
				}
			}
			URI uri = builder.build();

			// 创建http post请求
			HttpPost httpGet = new HttpPost(uri);

			// 执行请求
			response = httpclient.execute(httpGet);
			// 判断返回状态是否为200
			if (response.getStatusLine().getStatusCode() == 200) {
				resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}
	
	public static void E3ReturnListGetNew(String ks, String js) {
		
		JSONObject postjson = new JSONObject();
		int pageTotal = 1;
		
		for (int i = 1; i <= pageTotal; i++) {

			postjson = new JSONObject();
			postjson.put("time_type", 1);// 时间类型为出库时间
			postjson.put("startModified", ks);
			postjson.put("endModified", js);
			postjson.put("return_order_status", 10);
			postjson.put("pageNo", i);
			postjson.put("pageSize", 100);
			String postdata = postjson.toString();
			String result = ApiFf.PostE3Th(postdata);
			JSONObject json = JSONObject.parseObject(result);
			// 记录调用接口完成之后的时间
			Date kssj = new Date();
			String jg = json.getString("status");
			if ("api-success".equals(jg)) {

				JSONObject data = json.getJSONObject("data");
				JSONObject page = data.getJSONObject("page");
				pageTotal = page.getIntValue("pageTotal");// 页数
				
				JSONArray orderListGets = data.getJSONArray("orderListGets");
				ApiFf.BillSaveThNew(orderListGets);
			}
			// 记录保存单据之后的时间
			Date jssj = new Date();
			long hs = jssj.getTime() - kssj.getTime();
			// 如果时间只差小于10000毫秒 则延时
			if ((hs) < 1000) {
				try {
					Thread.sleep(1000 - hs);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}
	


	public static void E3ReturnListGet(String ks, String js) {
		JSONObject postjson = new JSONObject();
		postjson.put("time_type", 1);// 时间类型为出库时间
		postjson.put("startModified", ks);// 开始时间
		postjson.put("endModified", js);// 结束时间
		postjson.put("return_order_status", 10);// 正式需要启用

		postjson.put("pageNo", 1);// 页码
		postjson.put("pageSize", 100);// 每页数量
		String postdata = postjson.toString();
		String result = ApiFf.PostE3Th(postdata);
		JSONObject json = JSONObject.parseObject(result);
		String jg = json.getString("status");
		if ("api-success".equals(jg)) {
//			try {
//				Thread.sleep(1000);
//			} catch (InterruptedException e1) {
//				// TODO Auto-generated catch block
//				e1.printStackTrace();
//			}
			JSONObject data = json.getJSONObject("data");
			JSONObject page = data.getJSONObject("page");
			int pageTotal = page.getIntValue("pageTotal");// 页数
			// JSONArray orderListGets = data.getJSONArray("orderListGets");
			// BillSave(orderListGets);
			for (int i = 1; i <= pageTotal; i++) {

				postjson = new JSONObject();
				postjson.put("time_type", 1);// 时间类型为出库时间
				postjson.put("startModified", ks);
				postjson.put("endModified", js);
				postjson.put("return_order_status", 10);
				postjson.put("pageNo", i);
				postjson.put("pageSize", 100);
				postdata = postjson.toString();
				result = ApiFf.PostE3Th(postdata);
				json = JSONObject.parseObject(result);
				// 记录调用接口完成之后的时间
				Date kssj = new Date();
				jg = json.getString("status");
				if ("api-success".equals(jg)) {

					data = json.getJSONObject("data");
					page = data.getJSONObject("page");
					JSONArray orderListGets = data.getJSONArray("orderListGets");
					ApiFf.BillSaveTh(orderListGets);
				}else {
					// 1、获取日志微服务接口
					ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
					// 2、构建日志信息，参考示例如下
					AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细退货接口失败报文"+postdata+"错误"+json.toString(), "sm", "yd_rhzfh");
					// 3、记录操作日志
					logService1.addLog(logInfo1);
				}
				// 记录保存单据之后的时间
				Date jssj = new Date();
				long hs = jssj.getTime() - kssj.getTime();
				// 如果时间只差小于10000毫秒 则延时
				if ((hs) < 1000) {
					try {
						Thread.sleep(1000 - hs);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}else {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细退货接口失败报文"+postdata+"错误"+json.toString(), "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

//	public static void E3OrderListGet(String ks, String js, String dp) {
//		JSONObject postjson = new JSONObject();
//		// postjson.put("status_th", 0);//正常单
//		postjson.put("time_type", 7);// 时间类型为发货时间
//		postjson.put("startModified", ks);// 开始时间
//		postjson.put("endModified", js);// 结束时间
//		postjson.put("shipping_status", 7);// 正式需要启用
//		postjson.put("not_decrypt", 1);// 正式需要启用
//		if (dp != "") {
//			postjson.put("sd_code", dp);// 店铺
//		}
//		// postjson.put("tags_code", "order_type_import");//手工导入
//		postjson.put("pageNo", 1);// 页码
//		postjson.put("pageSize", 100);// 每页数量
//		String postdata = postjson.toString();
//		String result = ApiFf.PostE3(postdata);
//		JSONObject json = JSONObject.parseObject(result);
//		String jg = json.getString("status");
//		if ("api-success".equals(jg)) {
////			try {
////				Thread.sleep(1000);
////			} catch (InterruptedException e1) {
////				// TODO Auto-generated catch block
////				e1.printStackTrace();
////			}
//			JSONObject data = json.getJSONObject("data");
//			JSONObject page = data.getJSONObject("page");
//			int pageTotal = page.getIntValue("pageTotal");// 页数
//			// JSONArray orderListGets = data.getJSONArray("orderListGets");
//			// BillSave(orderListGets);
//			for (int i = 1; i <= pageTotal; i++) {
//
//				postjson = new JSONObject();
//				// postjson.put("status_th", 0);//正常单
//				postjson.put("time_type", 7);// 时间类型为发货时间
//				postjson.put("startModified", ks);
//				postjson.put("endModified", js);
//				postjson.put("shipping_status", 7);
//				postjson.put("not_decrypt", 1);// 正式需要启用
//				if (dp != "") {
//					postjson.put("sd_code", dp);// 店铺
//				}
//				// postjson.put("tags_code", "order_type_import");//手工导入
//				postjson.put("pageNo", i);
//				postjson.put("pageSize", 100);
//				postdata = postjson.toString();
//				result = ApiFf.PostE3(postdata);
//				json = JSONObject.parseObject(result);
//				// 记录调用接口完成之后的时间
//				Date kssj = new Date();
//				jg = json.getString("status");
//				if ("api-success".equals(jg)) {
//
//					data = json.getJSONObject("data");
//					page = data.getJSONObject("page");
//					JSONArray orderListGets = data.getJSONArray("orderListGets");
//					ApiFf.BillSave(orderListGets);
//				}
//				// 记录保存单据之后的时间
//				Date jssj = new Date();
//				long hs = jssj.getTime() - kssj.getTime();
//				// 如果时间只差小于10000毫秒 则延时
//				if ((hs) < 1000) {
//					try {
//						Thread.sleep(1000 - hs);
//					} catch (InterruptedException e) {
//						// TODO Auto-generated catch block
//						e.printStackTrace();
//					}
//				}
//			}
//		}
//	}
	public static void MYE3ReturnListGet(String ks, String js) {
		JSONObject postjson = new JSONObject();
		postjson.put("time_type", 1);// 时间类型为出库时间
		postjson.put("startModified", ks);// 开始时间
		postjson.put("endModified", js);// 结束时间
		postjson.put("return_order_status", 10);// 正式需要启用

		postjson.put("pageNo", 1);// 页码
		postjson.put("pageSize", 100);// 每页数量
		String postdata = postjson.toString();
		String result = ApiFf.PostMYE3Th(postdata);
		JSONObject json = JSONObject.parseObject(result);
		String jg = json.getString("status");
		if ("api-success".equals(jg)) {
//			try {
//				Thread.sleep(1000);
//			} catch (InterruptedException e1) {
//				// TODO Auto-generated catch block
//				e1.printStackTrace();
//			}
			JSONObject data = json.getJSONObject("data");
			JSONObject page = data.getJSONObject("page");
			int pageTotal = page.getIntValue("pageTotal");// 页数
			// JSONArray orderListGets = data.getJSONArray("orderListGets");
			// BillSave(orderListGets);
			for (int i = 1; i <= pageTotal; i++) {

				postjson = new JSONObject();
				postjson.put("time_type", 1);// 时间类型为出库时间
				postjson.put("startModified", ks);
				postjson.put("endModified", js);
				postjson.put("return_order_status", 10);
				postjson.put("pageNo", i);
				postjson.put("pageSize", 100);
				postdata = postjson.toString();
				result = ApiFf.PostMYE3Th(postdata);
				json = JSONObject.parseObject(result);
				// 记录调用接口完成之后的时间
				Date kssj = new Date();
				jg = json.getString("status");
				if ("api-success".equals(jg)) {

					data = json.getJSONObject("data");
					page = data.getJSONObject("page");
					JSONArray orderListGets = data.getJSONArray("orderListGets");
					ApiFf.BillSaveTh(orderListGets);
				}
				// 记录保存单据之后的时间
				Date jssj = new Date();
				long hs = jssj.getTime() - kssj.getTime();
				// 如果时间只差小于10000毫秒 则延时
				if ((hs) < 1000) {
					try {
						Thread.sleep(1000 - hs);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
	}
//	public static void MYE3OrderListGet(String ks, String js, String dp) {
//		JSONObject postjson = new JSONObject();
//		// postjson.put("status_th", 0);//正常单
//		postjson.put("time_type", 7);// 时间类型为发货时间
//		postjson.put("startModified", ks);// 开始时间
//		postjson.put("endModified", js);// 结束时间
//		postjson.put("shipping_status", 7);// 正式需要启用
//		if (dp != "") {
//			postjson.put("sd_code", dp);// 店铺
//		}
//		// postjson.put("tags_code", "order_type_import");//手工导入
//		postjson.put("pageNo", 1);// 页码
//		postjson.put("pageSize", 100);// 每页数量
//		String postdata = postjson.toString();
//		String result = ApiFf.PostMYE3(postdata);
//		JSONObject json = JSONObject.parseObject(result);
//		String jg = json.getString("status");
//		if ("api-success".equals(jg)) {
////			try {
////				Thread.sleep(1000);
////			} catch (InterruptedException e1) {
////				// TODO Auto-generated catch block
////				e1.printStackTrace();
////			}
//			JSONObject data = json.getJSONObject("data");
//			JSONObject page = data.getJSONObject("page");
//			int pageTotal = page.getIntValue("pageTotal");// 页数
//			// JSONArray orderListGets = data.getJSONArray("orderListGets");
//			// BillSave(orderListGets);
//			for (int i = 1; i <= pageTotal; i++) {
//
//				postjson = new JSONObject();
//				// postjson.put("status_th", 0);//正常单
//				postjson.put("time_type", 7);// 时间类型为发货时间
//				postjson.put("startModified", ks);
//				postjson.put("endModified", js);
//				postjson.put("shipping_status", 7);
//				if (dp != "") {
//					postjson.put("sd_code", dp);// 店铺
//				}
//				// postjson.put("tags_code", "order_type_import");//手工导入
//				postjson.put("pageNo", i);
//				postjson.put("pageSize", 100);
//				postdata = postjson.toString();
//				result = ApiFf.PostMYE3(postdata);
//				json = JSONObject.parseObject(result);
//				// 记录调用接口完成之后的时间
//				Date kssj = new Date();
//				jg = json.getString("status");
//				if ("api-success".equals(jg)) {
//
//					data = json.getJSONObject("data");
//					page = data.getJSONObject("page");
//					JSONArray orderListGets = data.getJSONArray("orderListGets");
//					ApiFf.BillSave(orderListGets);
//				}
//				// 记录保存单据之后的时间
//				Date jssj = new Date();
//				long hs = jssj.getTime() - kssj.getTime();
//				// 如果时间只差小于10000毫秒 则延时
//				if ((hs) < 1000) {
//					try {
//						Thread.sleep(1000 - hs);
//					} catch (InterruptedException e) {
//						// TODO Auto-generated catch block
//						e.printStackTrace();
//					}
//				}
//			}
//		}
//	}

	public static void E3Rfhhz(String lx, String rq) {
		JSONObject postjsonBB = new JSONObject();
		postjsonBB.put("type", lx);// 开始时间
		postjsonBB.put("date", rq);// 结束时间
		String postdataBB = postjsonBB.toString();
		String resultBB = ApiFf.PostE3BB(postdataBB);
		JSONObject jsonBB = JSONObject.parseObject(resultBB);
		String jgBB = jsonBB.getString("status");
		if ("200".equals(jgBB)) {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数成功", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
			List<DynamicObject> objs = new ArrayList<DynamicObject>();
			JSONArray data = jsonBB.getJSONArray("data");
			for (int i = 0; i < data.size(); i++) {
				// String id = data.getJSONObject(i).get("id").toString();
				String sd_code = data.getJSONObject(i).get("sd_code").toString();
				String date = data.getJSONObject(i).get("date").toString();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date tiem = new Date();
				try {
					tiem = sdf.parse(date);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String type = data.getJSONObject(i).get("type").toString();
				if ("1".equals(type)) {
					type = "0";
				} else {
					type = "1";
				}
				String djbh = "E3_" + type + "_" + sd_code + "_" + date;
				QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
				DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_rhzfh", "billno",
						new QFilter[] { qFilter });
				if (dObject == null) {
					String order_num = data.getJSONObject(i).get("order_num").toString();
					String goods_num = data.getJSONObject(i).get("goods_num").toString();
					String money = data.getJSONObject(i).get("money").toString();
					// String ordersns = data.getJSONObject(i).get("ordersns").toString();
					DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_rhzfh");
					// 设置单据属性
					bill.set("billno", djbh); // 单据编号为平台_退货_店铺_日期
					Date time = new Date();
					bill.set("createtime", time);
					bill.set("billstatus", "C");
					bill.set("yd_combofield_pt", "1");
					bill.set("yd_datefield_fhrq", tiem);
					bill.set("yd_checkboxfield_th", type);
					bill.set("yd_integerfield_zs", order_num);
					bill.set("yd_textfield_dpbh", sd_code);
					bill.set("yd_decimalfield_sl", goods_num);
					bill.set("yd_decimalfield_zje", money);
					objs.add(bill);
				}
			}
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
		} else {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数失败", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
	
	public static void MYE3Rfhhz(String lx, String rq) {
		JSONObject postjsonBB = new JSONObject();
		postjsonBB.put("type", lx);// 开始时间
		postjsonBB.put("date", rq);// 结束时间
		String postdataBB = postjsonBB.toString();
		String resultBB = ApiFf.PostMYE3BB(postdataBB);
		JSONObject jsonBB = JSONObject.parseObject(resultBB);
		String jgBB = jsonBB.getString("status");
		if ("200".equals(jgBB)) {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数成功", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
			List<DynamicObject> objs = new ArrayList<DynamicObject>();
			JSONArray data = jsonBB.getJSONArray("data");
			for (int i = 0; i < data.size(); i++) {
				// String id = data.getJSONObject(i).get("id").toString();
				String sd_code = data.getJSONObject(i).get("sd_code").toString();
				String date = data.getJSONObject(i).get("date").toString();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date tiem = new Date();
				try {
					tiem = sdf.parse(date);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String type = data.getJSONObject(i).get("type").toString();
				if ("1".equals(type)) {
					type = "0";
				} else {
					type = "1";
				}
				String djbh = "E3_" + type + "_" + sd_code + "_" + date;
				QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
				DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_rhzfh", "billno",
						new QFilter[] { qFilter });
				if (dObject == null) {
					String order_num = data.getJSONObject(i).get("order_num").toString();
					String goods_num = data.getJSONObject(i).get("goods_num").toString();
					String money = data.getJSONObject(i).get("money").toString();
					// String ordersns = data.getJSONObject(i).get("ordersns").toString();
					DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_rhzfh");
					// 设置单据属性
					bill.set("billno", djbh); // 单据编号为平台_退货_店铺_日期
					Date time = new Date();
					bill.set("createtime", time);
					bill.set("billstatus", "C");
					bill.set("yd_combofield_pt", "1");
					bill.set("yd_datefield_fhrq", tiem);
					bill.set("yd_checkboxfield_th", type);
					bill.set("yd_integerfield_zs", order_num);
					bill.set("yd_textfield_dpbh", sd_code);
					bill.set("yd_decimalfield_sl", goods_num);
					bill.set("yd_decimalfield_zje", money);
					objs.add(bill);
				}
			}
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
		} else {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数失败", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

//	public static String PostE3(String postdata) {
//		//String key = "TCBJ_E3";
//		//String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";
//		// 1、获取日志微服务接口
//		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
//		// 2、构建日志信息，参考示例如下
//		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3出库单拉单开始", "sm", "yd_fhmxb");
//		// 3、记录操作日志
//		logService1.addLog(logInfo1);
//		String version = "3.0";
//		String serviceType = "order.list.get";
//		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
//		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
//				+ "&serviceType=" + serviceType + "&data=" + postdata;
//		String sign = getMd5(JMZFC);
//		// String BSDZ ="http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
//		//String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
//		String BSDZ = "http://8.142.214.170/e3/webopm/web/?app_act=api/ec&app_mode=func";
//		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
//				+ "&serviceType=" + serviceType + "&sign=" + sign;
//		Map<String, String> map = new HashMap<>();
//		map.put("data", postdata);
//		String result = doPost(url, map);
//		logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3出库单拉单结束", "sm", "yd_fhmxb");
//		// 3、记录操作日志
//		logService1.addLog(logInfo1);
//		return result;
//	}
	// 退货
	public static String PostE3Th(String postdata) {
		//String key = "TCBJ_E3";
		//String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";
		String version = "3.0";
		String serviceType = "return_list_get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		// String BSDZ =
		// "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
		//String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	public static String PostE3BB(String postdata) {
		//String key = "TCBJ_E3";// 正式
		//String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";// 正式
		// String key = "BSAKhmSVtfmkDVGLsRLs";//测试
		// String secret = "BSAKdaVAbhYEjuLPwiOs";//测试
		
		String version = "3.0";
		String serviceType = "ptg.order.list";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api_baison/ec&app_mode=func";//测试
		String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api_baison/ec&app_mode=func";// 正式
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	public static String PostMYE3(String postdata) {
		String key = "jindie";
		String secret = "9a8b7c6d5e4f3g1h5i6j7k11";
		String version = "3.0";
		String serviceType = "order.list.get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		String BSDZ = "http://47.92.84.231/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	// 退货
	public static String PostMYE3Th(String postdata) {
		String key = "jindie";
		String secret = "9a8b7c6d5e4f3g1h5i6j7k11";
		String version = "3.0";
		String serviceType = "return_list_get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		// String BSDZ =
		// "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
		String BSDZ = "http://47.92.84.231/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	public static String PostMYE3BB(String postdata) {
		String key = "jindie";// 正式
		String secret = "9a8b7c6d5e4f3g1h5i6j7k11";// 正式
		// String key = "BSAKhmSVtfmkDVGLsRLs";//测试
		// String secret = "BSAKdaVAbhYEjuLPwiOs";//测试
		String version = "3.0";
		String serviceType = "ptg.order.list";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		// String BSDZ =
		// "http://47.92.195.153/e3test/webopm/web/?app_act=api_baison/ec&app_mode=func";//测试
		String BSDZ = "http://47.92.84.231/e3test/webopm/web/?app_act=api/ec&app_mode=func";// 正式
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}






	// 退货
	public static void BillSaveThNew(JSONArray orderListGets) {
		List<DynamicObject> objs = new ArrayList<DynamicObject>();

		
		for (int i = 0; i < orderListGets.size(); i++) {
			JSONObject data = orderListGets.getJSONObject(i);

			String order_sn = String.valueOf(data.get("return_order_sn"));// 退单编号
			String sd_code = String.valueOf(data.get("sd_code"));// 商店id
			String djbh = "E3_1_" + sd_code + "_" + order_sn;
			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			QFilter filter1 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno", new QFilter[] { qFilter,filter1 });
			
			if (dObject == null) {
				// 设置表头
				DynamicObject bill = BillSaveThData(data,djbh);

				// 退货单明细
				BillSaveThItem(bill,data,djbh);
				
				objs.add(bill);
			}
		}
		
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = (DynamicObject[]) objs.toArray();

			SaveServiceHelper.save(objsSAVE);
			// SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

	// 退货单明细
	private static DynamicObject BillSaveThData(JSONObject data,String djbh) {
		// 创建单据对象
		DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");

		String order_sn = String.valueOf(data.get("return_order_sn"));// 退单编号
		String sd_code = String.valueOf(data.get("sd_code"));// 商店id
		String fhck = String.valueOf(data.get("fhck"));// 发货仓库
		String orderstatus = String.valueOf(data.get("return_order_status"));// 退单状态
		String shippingstatus = String.valueOf(data.get("return_shipping_status"));// 退单物流状态：0未收货、1已收货,未入库、2已入库、3可入库、4已退回给客户
		String paystatus = String.valueOf(data.get("return_pay_status"));// 财务状态：0未结算、1已结算、2待结算
		String dealcode = String.valueOf(data.get("refund_deal_code"));// 退单交易号
		String lylx = String.valueOf(data.get("lylx"));//店铺来源类型

		String total_amount = String.valueOf(data.get("return_total_amount"));
		String order_amount = String.valueOf(data.get("return_order_amount"));
		String return_payment = String.valueOf(data.get("return_payment"));
		String other_discount_fee = String.valueOf(data.get("return_other_discount_fee"));
		BigDecimal totalamount = "null".equals(total_amount)?null:new BigDecimal(total_amount);// 订单总金额
		BigDecimal orderamount = "null".equals(order_amount)?null:new BigDecimal(order_amount);// 买家应付金额
		BigDecimal payment = "null".equals(return_payment)?null:new BigDecimal(return_payment);// 已付金额
		BigDecimal otherdiscountfee = "null".equals(other_discount_fee)?null:new BigDecimal(other_discount_fee);// 订单其他折让(整单折让)

		String shippingcode = String.valueOf(data.get("return_shipping_code"));// 快递编码
		String shippingname = String.valueOf(data.get("return_shipping_name"));// 快递名称
		String shippingsn = String.valueOf(data.get("return_shipping_sn"));// 快递单号
		String relatingordersn = String.valueOf(data.get("relating_order_sn"));// 退单关联订单号
		String qdcode = String.valueOf(data.get("qd_code"));// 渠道代码
		String qdname = String.valueOf(data.get("qd_name"));// 渠道名称
		String addtime = String.valueOf(data.get("add_time"));// 退单创建时间

		
		long shippingtimerk = Long.parseLong(String.valueOf(data.get("return_shipping_time_rk")));// 退单入库时间
		Date xdsj_time = new Date(shippingtimerk * 1000);
		String strrq = sdfrqd.format(xdsj_time);
		try {
			bill.set("yd_datefield_fhrq", sdfrqd.parse(strrq));
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		// 设置单据属性
		bill.set("billno", djbh); // 单据编号为平台_店铺_订单号
		Date time = new Date();
		bill.set("createtime", time);
		bill.set("billstatus", "C");
		bill.set("yd_combofield_pt", "1");
		bill.set("yd_textfield_ddbh", order_sn);
		bill.set("yd_datetimefield_xdsj", xdsj_time);
		bill.set("yd_textfield_dpbh", sd_code);
		bill.set("yd_checkboxfield_th", true);// 退货
		bill.set("yd_textfield_ck", fhck);
		bill.set("yd_orderstatus", orderstatus);
		bill.set("yd_shippingstatus", shippingstatus);
		bill.set("yd_paystatus", paystatus);
		bill.set("yd_dealcode", dealcode);
		bill.set("yd_lylx", lylx);
		bill.set("yd_totalamount", totalamount);
		bill.set("yd_orderamount", orderamount);
		bill.set("yd_payment", payment);
		bill.set("yd_otherdiscountfee", otherdiscountfee);
		bill.set("yd_shippingcode", shippingcode);
		bill.set("yd_shippingname", shippingname);
		bill.set("yd_shippingsn", shippingsn);
		bill.set("yd_relatingordersn", relatingordersn);
		bill.set("yd_qdcode", qdcode);
		bill.set("yd_qdname", qdname);
		bill.set("yd_addtime", StringUtils.isEmpty(addtime)?null: Timestamp.valueOf(addtime));

		return bill;
	}
	
	// 退货单明细
	private static void BillSaveThItem(DynamicObject bill,JSONObject data,String djbh) {
		// 获取单据体集合
		DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
		// 获取单据体的Type
		DynamicObjectType type = entrys.getDynamicObjectType();
		// 根据Type创建单据体对象
		JSONArray orderDetailGets = data.getJSONArray("orderDetailGets");
		
		for (int j = 0; j < orderDetailGets.size(); j++) {
			JSONObject dataItem = orderDetailGets.getJSONObject(j);

			// 设置单据体属性
			String share_price = String.valueOf(dataItem.get("share_price"));// 商品单价
			String share_payment = String.valueOf(dataItem.get("share_payment"));// 均摊实付金额
			JSONArray batchs = dataItem.getJSONArray("batchs"); // 批次信息
			
			if(batchs!=null){
				int zs=batchs.size();
				
				if(zs>1){
					BigDecimal ljje=BigDecimal.ZERO;//记录累计金额
					
					for (int k = 0; k < zs; k++) {
						JSONObject itemInfo = batchs.getJSONObject(k);
						String goods_number = String.valueOf(itemInfo.get("goods_number"));// 商品数量
						
						BigDecimal bcje=BigDecimal.ZERO;
						if(k<zs-1){ 
							//不为最后一行 按照单价*数量计算金额
							bcje=new BigDecimal(share_price).multiply(new BigDecimal(goods_number));//当前行批次金额
							ljje=ljje.add(bcje);
						}else{
							//最后一行使用总金额-累计金额计算金额
							bcje=new BigDecimal(share_payment).subtract(ljje);
						}
						DynamicObject entry = setEntry(type,dataItem,djbh);	// 设置明细通用属性

						String ph = String.valueOf(itemInfo.get("batch_no"));
						entry.set("yd_decimalfield_sl", goods_number);
						entry.set("yd_decimalfield_zje", bcje);
						entry.set("yd_ph", ph);
						// 添加到单据体集合
						entrys.add(entry);
					}
				}else{
					DynamicObject entry = setEntry(type,dataItem,djbh);  // 设置明细通用属性
					
					String ph = batchs.getJSONObject(0).get("batch_no").toString();
					entry.set("yd_ph", ph);
					// 添加到单据体集合
					entrys.add(entry);
				}
			}else{
				DynamicObject entry = setEntry(type,dataItem,djbh);	// 设置明细通用属性
				// 添加到单据体集合
				entrys.add(entry);
			}

		}
	}
	
	// 设置单据体信息
	private static DynamicObject setEntry(DynamicObjectType type,JSONObject dataItem,String djbh) {
		DynamicObject entry = new DynamicObject(type);
		
		String sku = String.valueOf(dataItem.get("sku"));// sku
		String share_price = String.valueOf(dataItem.get("share_price"));// 单价
		String share_payment = String.valueOf(dataItem.get("share_payment"));// 均摊实付金额
		String goods_number = String.valueOf(dataItem.get("goods_number"));// 商品数量
		String rowid = String.valueOf(dataItem.get("id"));// id
		String goodsname = String.valueOf(dataItem.get("goods_name"));// 商品名
		String skuid = String.valueOf(dataItem.get("sku_id"));// sku_id
		String goodssn = String.valueOf(dataItem.get("goods_sn"));// 货号
		String goodsid = String.valueOf(dataItem.get("goods_id"));// 货号id
		String numberreturnsj = String.valueOf(dataItem.get("goods_number_return_sj"));// 实际入库数量
		String barcode = String.valueOf(dataItem.get("barcode"));// barcode

		String goods_price = String.valueOf(dataItem.get("goods_price"));
		String shop_price = String.valueOf(dataItem.get("shop_price"));
		BigDecimal goodsprice = "null".equals(goods_price)?null:new BigDecimal(goods_price); // 商品单价
		BigDecimal shopprice = "null".equals(shop_price)?null:new BigDecimal(shop_price);// 商品网店在售价格


		entry.set("yd_textfield_hpbh", sku);
		entry.set("yd_decimalfield_sl", goods_number);
		entry.set("yd_decimalfield_dj", share_price);
		entry.set("yd_decimalfield_zje", share_payment);
		entry.set("yd_rowid", rowid);
		entry.set("yd_goodssn", goodssn);
		entry.set("yd_goodsname", goodsname);
		entry.set("yd_skuid", skuid);
		entry.set("yd_goodsid", goodsid);
		entry.set("yd_numberreturnsj", numberreturnsj);
		entry.set("yd_goodsprice", goodsprice);
		entry.set("yd_shopprice", shopprice);
		entry.set("yd_barcode", barcode);
		
		return entry;
	}
	
	// 退货
	public static void BillSaveTh(JSONArray orderListGets) {
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < orderListGets.size(); i++) {
			String order_sn = orderListGets.getJSONObject(i).get("return_order_sn").toString();// 退单编号
			String sd_code = orderListGets.getJSONObject(i).get("sd_code").toString();// 商店id
			String djbh = "E3_1_" + sd_code + "_" + order_sn;
//			System.out.println(orderListGets.getJSONObject(i).toJSONString());
//			System.out.println(djbh);
			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno",
					new QFilter[] { qFilter });
			if (dObject == null) {
				long add_time = Long
						.parseLong(orderListGets.getJSONObject(i).get("return_shipping_time_rk").toString());// 退单入库时间
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				// String sj=sdf.format(new Date(add_time));
				Date tiem = new Date(add_time * 1000);
				SimpleDateFormat sdfrq = new SimpleDateFormat("yyyy-MM-dd");
				String glrq="2021-11-01 00:00:00";
				Date timerq = new Date();
				Date timeglrq = new Date();
				String strrq = sdfrq.format(tiem);
				try {
					timerq = sdfrq.parse(strrq);
					timeglrq = sdf.parse(glrq);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				//if(tiem.after(timeglrq))//不取11月1日之前的单据
				//{
				String fhck = orderListGets.getJSONObject(i).get("fhck").toString();// 发货仓库

				String orderstatus = String.valueOf(orderListGets.getJSONObject(i).get("return_order_status"));// 退单状态
				String shippingstatus = String.valueOf(orderListGets.getJSONObject(i).get("return_shipping_status"));// 退单物流状态：0未收货、1已收货,未入库、2已入库、3可入库、4已退回给客户
				String paystatus = String.valueOf(orderListGets.getJSONObject(i).get("return_pay_status"));// 财务状态：0未结算、1已结算、2待结算
				String dealcode = String.valueOf(orderListGets.getJSONObject(i).get("refund_deal_code"));// 退单交易号
				String lylx = String.valueOf(orderListGets.getJSONObject(i).get("lylx"));//店铺来源类型
				String shippingcode = String.valueOf(orderListGets.getJSONObject(i).get("return_shipping_code"));// 快递编码
				String shippingname = String.valueOf(orderListGets.getJSONObject(i).get("return_shipping_name"));// 快递名称
				String shippingsn = String.valueOf(orderListGets.getJSONObject(i).get("return_shipping_sn"));// 快递单号
				String relatingordersn = String.valueOf(orderListGets.getJSONObject(i).get("relating_order_sn"));// 退单关联订单号
				String qdcode = String.valueOf(orderListGets.getJSONObject(i).get("qd_code"));// 渠道代码
				String qdname = String.valueOf(orderListGets.getJSONObject(i).get("qd_name"));// 渠道名称
				String addtime = String.valueOf(orderListGets.getJSONObject(i).get("add_time"));// 退单创建时间
				String total_amount = String.valueOf(orderListGets.getJSONObject(i).get("return_total_amount"));
				String order_amount = String.valueOf(orderListGets.getJSONObject(i).get("return_order_amount"));
				String return_payment = String.valueOf(orderListGets.getJSONObject(i).get("return_payment"));
				String other_discount_fee = String.valueOf(orderListGets.getJSONObject(i).get("return_other_discount_fee"));
				BigDecimal totalamount = "null".equals(total_amount)?null:new BigDecimal(total_amount);// 订单总金额
				BigDecimal orderamount = "null".equals(order_amount)?null:new BigDecimal(order_amount);// 买家应付金额
				BigDecimal payment = "null".equals(return_payment)?null:new BigDecimal(return_payment);// 已付金额
				BigDecimal otherdiscountfee = "null".equals(other_discount_fee)?null:new BigDecimal(other_discount_fee);// 订单其他折让(整单折让)
				String provincename = String.valueOf(orderListGets.getJSONObject(i).get("receiver_province_name"));// 省
				String cityname = String.valueOf(orderListGets.getJSONObject(i).get("receiver_city_name"));// 市
				String districtname = String.valueOf(orderListGets.getJSONObject(i).get("receiver_district_name"));// 区
				String address = String.valueOf(orderListGets.getJSONObject(i).get("receiver_address"));// 收货地址
				String shopOrg = String.valueOf(orderListGets.getJSONObject(i).get("qd_name"));//E3店铺组织

				//String shipping_fee = orderListGets.getJSONObject(i).get("shipping_fee").toString();// 运费
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
				//是否批次异常
				boolean isBatchError = false;
				//是否金额异常
				boolean isAmtError = false;
				//异常状态
				int errorState = 0;
				// 设置单据属性
				bill.set("billno", djbh); // 单据编号为平台_店铺_订单号

				Date time = new Date();
				bill.set("createtime", time);
				bill.set("billstatus", "C");
				bill.set("yd_combofield_pt", "1");
				bill.set("yd_textfield_ddbh", order_sn);
				bill.set("yd_datetimefield_xdsj", tiem);
				bill.set("yd_textfield_dpbh", sd_code);
				//bill.set("yd_decimalfield_yf", shipping_fee);
				// bill.set("yd_checkboxfield_sfkp", "0");//开票
				// bill.set("yd_checkboxfield_sfsg", "0");//手工
				bill.set("yd_checkboxfield_th", true);// 退货
				bill.set("yd_datefield_fhrq", timerq);
				bill.set("yd_textfield_ck", fhck);

				bill.set("yd_ordertype", "1");
				bill.set("yd_orderstatus", orderstatus);
				bill.set("yd_shippingstatus", shippingstatus);
				bill.set("yd_paystatus", paystatus);
				bill.set("yd_dealcode", dealcode);
				bill.set("yd_lylx", lylx);
				bill.set("yd_totalamount", totalamount);
				bill.set("yd_orderamount", orderamount);
				bill.set("yd_payment", payment);
				bill.set("yd_otherdiscountfee", otherdiscountfee);
				bill.set("yd_shippingcode", shippingcode);
				bill.set("yd_shippingname", shippingname);
				bill.set("yd_shippingsn", shippingsn);
				bill.set("yd_relatingordersn", relatingordersn);
				bill.set("yd_qdcode", qdcode);
				bill.set("yd_qdname", qdname);
				bill.set("yd_addtime", StringUtils.isEmpty(addtime)?null: Timestamp.valueOf(addtime));
				bill.set("yd_provincename", provincename);
				bill.set("yd_cityname", cityname);
				bill.set("yd_districtname",districtname);
				bill.set("yd_address", address);
				bill.set("yd_shop_org", shopOrg);

				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray orderDetailGets = orderListGets.getJSONObject(i).getJSONArray("orderDetailGets");
				boolean isAssembly = false;
				for (int j = 0; j < orderDetailGets.size(); j++) {
					//DynamicObject entry = new DynamicObject(type);
					// 设置单据体属性
					String sku = orderDetailGets.getJSONObject(j).get("sku").toString();// sku
					//String goods_number = orderDetailGets.getJSONObject(j).get("goods_number_return_sj").toString();// 商品数量
					String share_price = orderDetailGets.getJSONObject(j).get("share_price").toString();// 商品单价
					String share_payment = orderDetailGets.getJSONObject(j).get("share_payment").toString();// 均摊实付金额
					JSONArray batchs = orderDetailGets.getJSONObject(j).getJSONArray("batchs");

					String rowid = String.valueOf(orderDetailGets.getJSONObject(j).get("line_no"));// id
					String goodsname = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_name"));// 商品名
					String skuid = String.valueOf(orderDetailGets.getJSONObject(j).get("sku_id"));// sku_id
					String goodssn = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_sn"));// 货号
					String goodsid = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_id"));// 货号id
					String numberreturnsj = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_number_return_sj"));// 实际入库数量
					String barcode = String.valueOf(orderDetailGets.getJSONObject(j).get("barcode"));// barcode
					String goods_price = String.valueOf(orderDetailGets.getJSONObject(j).get("goods_price"));
					String shop_price = String.valueOf(orderDetailGets.getJSONObject(j).get("shop_price"));
					BigDecimal goodsprice = "null".equals(goods_price)?null:new BigDecimal(goods_price); // 商品单价
					BigDecimal shopprice = "null".equals(shop_price)?null:new BigDecimal(shop_price);// 商品网店在售价
					String isPackage = String.valueOf(orderDetailGets.getJSONObject(j).get("is_combo")); //是否是组装品 -lzp 2023-02-01组装品标识

					//判断分录总金额如果少于0，则标记金额异常
					BigDecimal totalAmt = new BigDecimal(share_payment);
					if(totalAmt.compareTo(BigDecimal.ZERO)<0)
					{
						isAmtError = true;
					}
					//循环batchs中的goods_number合计，并对比orderDetailGets中的goods_number是否一致，如不一致，标记批次异常为是
					//orderDetailGets中的数量
					BigDecimal orderDetailQty = new BigDecimal(orderDetailGets.getJSONObject(j).get("goods_number_return_sj").toString());
					//batchs中的数量合计
					BigDecimal batchsTotal = new BigDecimal(0);
					//判断批次是否为空
					if(batchs!=null)
					{
						for(int z=0;z<batchs.size();z++)
						{
							BigDecimal goods_number = new BigDecimal(batchs.getJSONObject(z).get("goods_number").toString());// 商品数量
							batchsTotal = batchsTotal.add(goods_number);
						}
						//如果两个数量不相等，属于批次异常
						if(orderDetailQty.compareTo(batchsTotal)!=0)
						{
							isBatchError = true;
						}
					}
					
					if(batchs!=null)
					{
						int zs=batchs.size();
//						if(zs>1)
//						{
							BigDecimal ljje=BigDecimal.ZERO;//记录累计金额
							for (int k = 0; k < zs; k++) 
							{
								//不为最后一行 按照单价*数量计算金额
								if(k<zs-1)
								{
									String goods_number = batchs.getJSONObject(k).get("goods_number").toString();// 商品数量
									DynamicObject entry = new DynamicObject(type);
									entry.set("yd_textfield_hpbh", sku);
									entry.set("yd_decimalfield_sl", goods_number);
									entry.set("yd_decimalfield_dj", share_price);
									BigDecimal bcje=new BigDecimal(share_price).multiply(new BigDecimal(goods_number));//当前行批次金额
									ljje=ljje.add(bcje);
									entry.set("yd_decimalfield_zje",bcje);
									String ph = batchs.getJSONObject(k).get("batch_no").toString();
									entry.set("yd_ph", ph);

									entry.set("yd_rowid", rowid);
									entry.set("yd_goodssn", goodssn);
									entry.set("yd_goodsname", goodsname);
									entry.set("yd_skuid", skuid);
									entry.set("yd_goodsid", goodsid);
									entry.set("yd_numberreturnsj", numberreturnsj);
									entry.set("yd_goodsprice", goodsprice);
									entry.set("yd_shopprice", shopprice);
									entry.set("yd_barcode", barcode);
									// 是否组装品 -lzp 2023-02-01
									if (StringUtils.equals("1", isPackage)) {
										isAssembly = true;
										entry.set("yd_ispropackage", true);
									}
									// 添加到单据体集合
									entrys.add(entry);
								}
								//最后一行使用总金额-累计金额计算金额
								else
								{
									String goods_number = batchs.getJSONObject(k).get("goods_number").toString();// 商品数量
									DynamicObject entry = new DynamicObject(type);
									entry.set("yd_textfield_hpbh", sku);
									entry.set("yd_decimalfield_sl", goods_number);
									entry.set("yd_decimalfield_dj", share_price);
									BigDecimal bcje=new BigDecimal(share_payment).subtract(ljje);
									entry.set("yd_decimalfield_zje",bcje);
									String ph = batchs.getJSONObject(k).get("batch_no").toString();
									entry.set("yd_ph", ph);

									entry.set("yd_rowid", rowid);
									entry.set("yd_goodssn", goodssn);
									entry.set("yd_goodsname", goodsname);
									entry.set("yd_skuid", skuid);
									entry.set("yd_goodsid", goodsid);
									entry.set("yd_numberreturnsj", numberreturnsj);
									entry.set("yd_goodsprice", goodsprice);
									entry.set("yd_shopprice", shopprice);
									entry.set("yd_barcode", barcode);
									// 是否组装品 -lzp 2023-02-01
									if (StringUtils.equals("1", isPackage)) {
										isAssembly = true;
										entry.set("yd_ispropackage", true);
									}
									// 添加到单据体集合
									entrys.add(entry);
								}
							}
//						}
//						else
//						{
//							String goods_number = orderDetailGets.getJSONObject(j).get("goods_number_return_sj").toString();// 商品数量
//							DynamicObject entry = new DynamicObject(type);
//							entry.set("yd_textfield_hpbh", sku);
//							entry.set("yd_decimalfield_sl", goods_number);
//							entry.set("yd_decimalfield_dj", share_price);
//							entry.set("yd_decimalfield_zje", share_payment);
//							String ph = batchs.getJSONObject(0).get("batch_no").toString();
//							entry.set("yd_ph", ph);
//
//							entry.set("yd_rowid", rowid);
//							entry.set("yd_goodssn", goodssn);
//							entry.set("yd_goodsname", goodsname);
//							entry.set("yd_skuid", skuid);
//							entry.set("yd_goodsid", goodsid);
//							entry.set("yd_numberreturnsj", numberreturnsj);
//							entry.set("yd_goodsprice", goodsprice);
//							entry.set("yd_shopprice", shopprice);
//							entry.set("yd_barcode", barcode);
//							// 添加到单据体集合
//							entrys.add(entry);
//						}
					}
					else
					{
						String goods_number = orderDetailGets.getJSONObject(j).get("goods_number_return_sj").toString();// 商品数量
						DynamicObject entry = new DynamicObject(type);
						entry.set("yd_textfield_hpbh", sku);
						entry.set("yd_decimalfield_sl", goods_number);
						entry.set("yd_decimalfield_dj", share_price);
						entry.set("yd_decimalfield_zje", share_payment);

						entry.set("yd_rowid", rowid);
						entry.set("yd_goodssn", goodssn);
						entry.set("yd_goodsname", goodsname);
						entry.set("yd_skuid", skuid);
						entry.set("yd_goodsid", goodsid);
						entry.set("yd_numberreturnsj", numberreturnsj);
						entry.set("yd_goodsprice", goodsprice);
						entry.set("yd_shopprice", shopprice);
						entry.set("yd_barcode", barcode);
						// 是否组装品 -lzp 2023-02-01
						if (StringUtils.equals("1", isPackage)) {
							isAssembly = true;
							entry.set("yd_ispropackage", true);
						}
						// 添加到单据体集合
						entrys.add(entry);
					}

				}

				// 是否组装品
				bill.set("yd_ispackage", isAssembly); // 表头

				Boolean iserror = false;
				//根据异常标识标记异常状态
				if(isBatchError && isAmtError)
				{
					errorState = 3;
					iserror = true;
				}
				else if(isBatchError)
				{
					errorState = 2;
					iserror = true;
				}
				else if(isAmtError)
				{
					errorState = 1;
					iserror = true;
				}
				//单据异常
				bill.set("yd_iserror", iserror);
				//异常状态
				bill.set("yd_errorstate", errorState);
				objs.add(bill);
				//}
			}
		}
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

	public static void bcbhzdj(String lx, List<String> pcwlin, List<String> fywl,List<String> ppfd, String rq,QFilter filterPlat, QFilter dateFilter,
							   List<String> shareClient) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter7 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不为手工
		QFilter filter8 = QFilter.of("yd_checkboxfield_sfkp =?", "0");// 不为开票
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		QFilter filter9 = QFilter.of("yd_checkboxfield_sfsg =?", "1");// 手工
		QFilter filter10 = QFilter.of("yd_checkboxfield_sfkp =?", "1");// 开票
		QFilter filter13 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不手工
		QFilter filter14 = QFilter.of("yd_textfield_sbyy =?", "");// 失败原因为空 此条用于过滤参与了合并汇总但是生成失败的单据
		QFilter filter15 = QFilter.of("yd_checkboxfield_wlwsh =?", "0");// 物料合规
		QFilter filter16 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter17 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 去除按照品牌分单店铺
		QFilter filter18 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter19 = QFilter.of("yd_dygxcf =?", "0");// 物料对应关系重复为否
		QFilter filter20 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
//		QFilter filter17 = QFilter.of("yd_combofield_pt =?", "1");// 平台为佰悦e3
//		QFilter filter18 = QFilter.of("yd_datefield_fhrq <=?", "2021-12-11");// 日期小于等于20号
//		QFilter filter19 = QFilter.of("yd_combofield_pt !=?", "1");// 平台不为佰悦e3
		// QFilter filter12 = QFilter.of("to_char(yd_datetimefield_xdsj,'yyyy-MM-dd') =
		// ?", rq);// 日期
		QFilter filter12 = QFilter.of("yd_datefield_fhrq = ?", rq);// 日期
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter11 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,
				filter14, filter15, filter12, dateFilter,filter16,filter17,filter18,filter19,filter20,filterPlat };
		String ZD = "billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf" +
                ",yd_textfield_ck ptck,yd_entryentity.yd_textfield_hpbh ptwl,yd_entryentity.yd_decimalfield_sl sl" +
                ",yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je,yd_entryentity.yd_ph ph" +
                ",yd_entryentity.yd_scrq scrq,yd_entryentity.yd_dqr dqr,yd_bz bz,yd_dealcode dealcode";
		// 查询发货明细 
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		// 查询手工单据
		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter9, dateFilter,filter14, filter15, filter12,filter16,filter17,filter18,filter19,filter20 };
		DataSet yd_fhmxbsg = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		// 查询为开票并不为手工的单据 
		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter10, dateFilter, filter13,filter14, filter15, filter12,filter16,filter17,filter18,filter19,filter20 };
		DataSet yd_fhmxbkp = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		// union发货明细
		yd_fhmxb = yd_fhmxb.union(yd_fhmxbsg).union(yd_fhmxbkp);

		// 如果是正常发货不汇总，则添加直营店实体店的过滤
		if ("2".equals(lx)) {
			Set<String> platformDirectWarehouse2 = DeliveryHelper.getPlatformDirectWarehouse("2");
			// 过滤掉没有维护的仓库数据
			Map<String, Object> param = new HashMap<>();
			param.put("platformDirectWarehouse", platformDirectWarehouse2);
			yd_fhmxb = yd_fhmxb.filter("ptck in platformDirectWarehouse", param);
		}

		// 查询已审核客户
		QFilter filterbhz = null;
		if ("5".equals(lx)) {
			String[] hz = new String[] { "4", "5" };
			filterbhz = new QFilter("yd_entryentity.yd_combofield_hzcl", QCP.in, hz);
		} else {
			String[] hz = new String[] { "1", "2" };
			filterbhz = new QFilter("yd_entryentity.yd_combofield_hzcl", QCP.in, hz);
		}
		fhmxgl = new QFilter[] { filterbhz, filterSh };
		DataSet dswl = glmx(fywl, yd_fhmxb, fhmxgl);
		// DataSet ds = dswl.copy();
		DataSet dataSetgroupBy = dswl.copy().select(new String[] { "billno",  "ptkh","th", "kh", "ck", "zz", "yf", "pt"
                ,"kcsw","thkcsw","quyu","bz","dealcode" })
				.groupBy(new String[] { "billno", "ptkh","th", "kh", "ck", "zz", "yf", "pt","kcsw","thkcsw","quyu"
                        ,"bz","dealcode" }).finish();
		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String dj = "priceandtax";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型
		if ("5".equals(lx)) {
			formid = "im_otheroutbill";
			djlx = "im_OtherOutBill_STD_BT_S";
			biztypenumber = "355";
			dj = "price";
			je = "amount";
			xyd = "2";
		}
		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz");
			Boolean th = row.getBoolean("th");
			String ckbm = row.getString("ck");
			String dpbm="";
			String dealCode = row.getString("dealCode");
			if(th) {
				// 查询仓库对应组织
				filter1 = new QFilter("number", QCP.equals, ckbm);
				DynamicObject khojb = BusinessDataServiceHelper.loadSingle("bd_warehouse", "createorg.id",
						new QFilter[] { filter1 });
				zzbm = khojb.getString("createorg.id");
			}
			String khbm = row.getString("kh");
			String pt = row.getString("pt");
			// 查询客户对应税率
			filter1 = new QFilter("number", QCP.equals, khbm);
			DynamicObject[] khojb = BusinessDataServiceHelper.load("bd_customer", "taxrate.number",
					new QFilter[] { filter1 });
			String slbm = "";
			for (DynamicObject rowdy : khojb) {
				slbm = rowdy.getString("taxrate.number");
			}
			IFormView view = null;
			view = ABillServiceHelper.createAddView(formid);
			view.getModel().setItemValueByNumber("billtype", djlx);
			view.getModel().setValue("org", zzbm);
			if (lx != "5") {
				view.getModel().setValue("bizorg", zzbm);
				view.getModel().setValue("yd_dealcode", dealCode);
			}
			if (th) {
				if ("5".equals(lx)) {
					view.getModel().setItemValueByNumber("biztype", "3551");
				} else {
					view.getModel().setItemValueByNumber("biztype", "2101");
					view.getModel().setItemValueByNumber("invscheme", "2101");
				}
			} else {
				view.getModel().setItemValueByNumber("biztype", biztypenumber);
				view.getModel().setItemValueByNumber("invscheme", "210");
			}
			if("5".equals(lx))
			{
				String kcsw="0";
				if(th)
				{
					//kcsw = row.getString("thkcsw");
					filter1 = new QFilter("yd_entryentity.yd_orgfield_dyzz", QCP.equals, zzbm);
					filter2 = new QFilter("yd_entryentity.yd_thkcsw", QCP.not_equals, "0");
					DataSet kcswjb = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
							"yd_entryentity.yd_thkcsw thkcsw",
							new QFilter[] { filter1,filter2 }, null).groupBy(new String[] {"thkcsw"}).finish();
					for (Row rowkcsw : kcswjb) {
						kcsw = rowkcsw.getString("thkcsw");
					}
				}
				else
				{
					kcsw = row.getString("kcsw");
				}
				if(kcsw!="0")
				{
					view.getModel().setValue("invscheme", kcsw);
				}
				String quyu = row.getString("quyu");
				view.getModel().setValue("yd_quyu", quyu);
				String bz = row.getString("bz");
				view.getModel().setValue("comment", bz);
				dpbm = row.getString("ptkh");
			}

			view.getModel().setItemValueByNumber("customer", khbm);
			view.getModel().setItemValueByNumber("yd_dzdkh", khbm);
			view.getModel().setValue("biztime", rq);
			view.getModel().setValue("yd_tbly", pt);
			BigDecimal yf = row.getBigDecimal("yf");
			BigDecimal zje = BigDecimal.ZERO;// 整单总金额
			String billno = row.getString("billno");
			DataSet dsxh = dswl.copy();
			//dsxh = dsxh.filter("billno=" + "'" + billno + "'").select(new String[] { "wl", "sl", "je", "dj","ph" });
			dsxh = dsxh.filter("billno=" + "'" + billno + "'").select(new String[] { "wl", "sl", "je", "dj" });
			int i = 0;
			for (Row rowds : dsxh) {
				String wlbm = rowds.getString("wl");// sku
				//String ph = rowds.getString("ph");// sku
				BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
				BigDecimal goods_price = rowds.getBigDecimal("dj");// 商品单价
				BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
				boolean sffy = true;
				for (String fywlbm : fywl) {
					if (wlbm.equals(fywlbm)) {
						yf = yf.add(share_payment);
						sffy = false;
						break;
					}
				}
				if (sffy) {
					if (i > 0) {
						view.getModel().createNewEntryRow("billentry");
					}
					view.getModel().setItemValueByNumber("material", wlbm, i);
					view.getModel().setItemValueByNumber("warehouse", ckbm, i);
					view.getModel().setValue("qty", goods_number, i);
					//view.getModel().setValue("yd_ph", ph, i);
					view.getModel().setValue(dj, goods_price, i);
					view.getModel().setValue(je, share_payment, i);
					if("5".equals(lx))
					{
					view.getModel().setValue("yd_store", dpbm,i);
					}
					if (share_payment.compareTo(BigDecimal.ZERO) == 0 && lx.equals("2")) {
						view.getModel().setValue("ispresent", "1", i);
					}

					if (slbm != null) {
						if ("2".equals(lx)) {
							view.getModel().setItemValueByNumber("taxrateid", slbm, i);
						}
					}
					zje = zje.add(share_payment);
					i++;
				}
			}
			view.getModel().setValue("yd_amountfield_yf", yf);
			view.getModel().setValue("yd_textfield_fhmxdh", billno);
//			if (yf.compareTo(BigDecimal.ZERO) > 0) {
//				// 处理分摊
//				DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
//				BigDecimal ljft = BigDecimal.ZERO;// 记录已分摊的运费金额
//				for (int j = 0; j < entrys.getRowCount(); j++) {
//					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
//					if (j < entrys.getRowCount() - 1) {
//						BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
//						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
//						ljft = ljft.add(yfft);
//						view.getModel().setValue(je, hsjeft, j);
//					} else {
//						BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
//						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
//						view.getModel().setValue(je, hsjeft, j);
//					}
//				}
//			}
			if (yf.compareTo(BigDecimal.ZERO) > 0) {
			// 处理分摊
			DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
			int hs=0;
			for (int j = 0; j < entrys.getRowCount(); j++) {
				BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
				if(hsje.compareTo(BigDecimal.ZERO) > 0)
				{
					hs++;
				}
			}
			BigDecimal ljft = BigDecimal.ZERO;
			int jehs=0;
			for (int j = 0; j < entrys.getRowCount(); j++) {
				BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
				if(hsje.compareTo(BigDecimal.ZERO) > 0)
				{
					jehs++;
				if (jehs < hs) {
					BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
					BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
					ljft = ljft.add(yfft);
					view.getModel().setValue(je, hsjeft, j);
				} else {
					BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
					BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
					view.getModel().setValue(je, hsjeft, j);
					// update by hst 2023/02/01 分摊运费后若金额为0标记为赠品
					if (hsjeft.compareTo(BigDecimal.ZERO) == 0) {
						view.getModel().setValue("ispresent","1",j);
					}
				}
				}
			}
		}
			if (i > 0) {
				OperationResult operationResult = ABillServiceHelper.saveOperate(view);
				filter1 = new QFilter("billno", QCP.equals, billno);
				// 判断保存结果
				if (operationResult.isSuccess()) {
					String djbh = "";
					Map<Object, String> map = operationResult.getBillNos();
					for (Map.Entry<Object, String> entry : map.entrySet()) {
						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
								"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							djbh = entry.getValue();
							rowdy.set("yd_textfield_xsckdh", entry.getValue());// 修改数据
							rowdy.set("yd_combofield_xyd", xyd);// 修改数据
						}
						SaveServiceHelper.save(dy);
					}
					// 进行提交操作
//					IFormView modifyView = ABillServiceHelper.createModifyView(view.getEntityId(),
//							operationResult.getSuccessPkIds().get(0).toString());
					// update by hst 2023/03/28 若当前客户为需分摊运费，则不执行提交操作
					DynamicObject customer = view.getModel().getDataEntity().getDynamicObject("customer");
					if (!("2".equals(lx) && shareClient.indexOf(customer.getString("id")) > -1)) {
						operationResult = view.invokeOperation("submit");
					}
					if (!operationResult.isSuccess()) {
						String errMessage = operationResult.getMessage() + ","; // 错误摘要
						// // 演示提取保存详细错误
						for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
							errMessage += errInfo.getMessage() + ",";
						}
						if (errMessage.length() > 1800) {
							errMessage = errMessage.substring(0, 1800);
						}
						filter1 = new QFilter("billno", QCP.equals, djbh);
						DynamicObject[] dy = BusinessDataServiceHelper.load(formid, "yd_sftj",
								new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_sftj", errMessage);// 修改数据
						}
						SaveServiceHelper.save(dy);
					}
					ABillServiceHelper.exitView(view);
				} else {
					String errMessage = operationResult.getMessage() + ","; // 错误摘要
					// // 演示提取保存详细错误
					for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
						errMessage += errInfo.getMessage() + ",";
					}
					if (errMessage.length() > 1800) {
						errMessage = errMessage.substring(0, 1800);
					}
					DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
							new QFilter[] { filter1 });
					for (DynamicObject rowdy : dy) {
						rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
//						rowdy.set("yd_iserror", true);
					}
					SaveServiceHelper.save(dy);
					ABillServiceHelper.exitView(view);
				}
			} else {
				ABillServiceHelper.exitView(view);
			}
		}
		dswl.close();
	}
	

	public static DataSet glmx(List<String> fywl, DataSet ds, QFilter[] fhmxgl) {

		QFilter filterSh = QFilter.of("billstatus=?", "C");

		// 查询对应客户
		DataSet khdygx = QueryServiceHelper.queryDataSet("glmx", "yd_khdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number kh,yd_entryentity.yd_orgfield_dyzz zz,yd_entryentity.yd_kcsw kcsw,yd_entryentity.yd_quyu quyu,yd_entryentity.yd_thkcsw thkcsw",
				fhmxgl, null);
		fhmxgl = new QFilter[] { filterSh };
		// 查询对应仓库
		DataSet ckdygx = QueryServiceHelper.queryDataSet("glmx", "yd_ckdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number ck,yd_entryentity.yd_basedatafield.createorg ckzz", fhmxgl,
				null);
		// 查询对应物料
//		DataSet wldygx = QueryServiceHelper.queryDataSet("glmx", "yd_wldygx",
//				"yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number wl,yd_entryentity.yd_pp pp,yd_combofield_pt pt", fhmxgl,
//				null);
		// up by laizp, 2022-10-09，调整品牌取值，不取对应关系表中的品牌，取物料中的品牌
		// update by hst 2023/03/30 增加产品类别字段
		DataSet wldygx = QueryServiceHelper.queryDataSet("glmx", "yd_wldygx",
				"yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number wl,yd_entryentity.yd_basedatafield.yd_basedatafield pp," +
						"yd_entryentity.yd_basedatafield.yd_procategory cplb,yd_combofield_pt pt", fhmxgl,
				null);
		//过滤e3物料对应关系物料
		List<String> wldy01 = new ArrayList<String>();
		for (Row row : wldygx.copy().filter("pt='1'").select("bm")) {
			wldy01.add(row.getString("bm"));
		}
		//过滤旺店通物料对应关系物料
		List<String> wldy02 = new ArrayList<String>();
		for (Row row : wldygx.copy().filter("pt='2'").select("bm")) {
			wldy02.add(row.getString("bm"));
		}
		QFilter filterwldye3 = new QFilter("helpcode", QCP.not_in, wldy01);//不包含对应表商品e3
		QFilter filterwldywdt = new QFilter("number", QCP.not_in, wldy02);//不包含对应表商品旺店通
		filterSh = QFilter.of("status=?", "C");
		// 查询已审核的物料编码
		// update by hst 2023/03/30 增加产品类别字段
		DataSet wlbm = QueryServiceHelper.queryDataSet("glmx", "bd_material", "number bm,number wl,yd_basedatafield pp," +
				"yd_procategory cplb", new QFilter[] { filterSh,filterwldywdt }, null);
		wlbm=wlbm.addField("'2'", "pt");
		// 查询已审核的物料助记码
		// update by hst 2023/03/30 增加产品类别字段
		DataSet wlzjm = QueryServiceHelper.queryDataSet("glmx", "bd_material", "helpcode bm,number wl,yd_basedatafield pp," +
				"yd_procategory cplb", new QFilter[] { filterSh,filterwldye3 }, null);
		wlzjm=wlzjm.addField("'1'", "pt");
		wldygx=wldygx.union(wlbm).union(wlzjm).groupBy(new String[]{"bm","pt","wl","pp","cplb"}).finish();
		//wldygx.copy().print(false);
		String[] fhmxbzd = new String[] { "billno","th", "yf", "ptck", "ptwl", "sl", "dj", "je", "pt","kcsw","thkcsw"
                ,"quyu", "ptkh","ph","scrq","dqr","bz","dealcode"};
		// 关联客户
		ds = ds.join(khdygx).on("pt", "pt").on("ptkh", "bm").select(fhmxbzd, new String[] { "kh", "zz" }).finish();
		//ds.print(true);
		// 关联仓库
		fhmxbzd = new String[] { "billno", "th", "yf", "ptwl", "sl", "dj", "je", "pt", "kh", "zz","kcsw","thkcsw","quyu"
                , "ptkh","ph","scrq","dqr","bz","dealcode"};
		ds = ds.join(ckdygx).on("pt", "pt").on("ptck", "bm").select(fhmxbzd, new String[] { "ck","ckzz" }).finish();
		//ds.print(true);
		// 关联费用物料
		Map<String, Object> mapfywl = new HashMap<>();
		mapfywl.put("fywl", fywl);
		fhmxbzd = new String[] { "billno", "th", "yf", "sl", "dj", "je", "pt", "kh", "zz", "ck","ckzz" ,"kcsw","thkcsw"
                ,"quyu",  "ptkh","ph","scrq","dqr","bz" ,"ptwl","dealcode" };
		DataSet dsfywl = ds.filter("ptwl in fywl", mapfywl).select(fhmxbzd);
		// 关联物料
		fhmxbzd = new String[] { "billno", "th", "yf", "sl", "dj", "je", "pt", "kh", "zz", "ck","ckzz" ,"kcsw","thkcsw"
                ,"quyu", "ptkh","ph","scrq","dqr","bz","dealcode" };
		//ds.print(true);
		// update by hst 2023/03/30 增加产品类别字段
		ds = ds.join(wldygx).on("pt", "pt").on("ptwl", "bm").select(fhmxbzd, new String[] { "wl","pp","cplb" }).finish();
		//ds.copy().print(true);
		// ds.print(true);
		// dsfywl.print(true);
		// update by hst 2023/03/30
		ds = ds.union(dsfywl.addField("1", "pp").addField("1","cplb"));
		//ds.print(true);
		return ds;
	}

	/**
	 * 直营实体店（正常汇总、正常不汇总）数据处理
	 * @param lx 汇总类型
	 * @param pcwlin 排除物料集合
	 * @param fywl 运费物料
	 * @param ppfd 品牌汇总集合
	 * @param rq 过滤的日期
	 * @param shareClient 需要重新取价的客户 update by hst 2023/03/28
	 * <AUTHOR>
	 * @date 2022-9-13
	 */
	public static void bchbdj(String lx, List<String> pcwlin, List<String> fywl,List<String> ppfd, String rq,QFilter filterPlat, QFilter dateFilter,
							  List<String> shareClient) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter7 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不为手工
		QFilter filter8 = QFilter.of("yd_checkboxfield_sfkp =?", "0");// 不为开票
		QFilter filter9 = QFilter.of("yd_checkboxfield_sfsg =?", "1");// 手工
		QFilter filter10 = QFilter.of("yd_checkboxfield_sfkp =?", "1");// 开票
		QFilter filter15 = QFilter.of("yd_checkboxfield_wlwsh =?", "0");// 物料合规
		QFilter filter16 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter17 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 去除按照品牌分单店铺
		QFilter filter18 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter19 = QFilter.of("yd_dygxcf =?", "0");// 物料对应关系重复为否
		QFilter filter20 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filter21 = QFilter.of("yd_textfield_sbyy =?", "");// 失败原因为空

//		QFilter filter17 = QFilter.of("yd_combofield_pt =?", "1");// 平台为佰悦e3
//		QFilter filter18 = QFilter.of("yd_datefield_fhrq <=?", "2021-12-11");// 日期小于等于20号
//		QFilter filter19 = QFilter.of("yd_combofield_pt !=?", "1");// 平台不为佰悦e3
		// QFilter filter12 = QFilter.of("to_char(yd_datetimefield_xdsj,'yyyy-MM-dd') =
		// ?", rq);// 日期
		QFilter filter12 = QFilter.of("yd_datefield_fhrq = ?", rq);// 日期

		QFilter filterSh = QFilter.of("billstatus=?", "C");
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter11 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天

		///////////// 添加直营店正常不汇总类型的处理 /////////////
		QFilter[] fhmxgl = null;
		String ZD = "id, billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf" +
				",yd_textfield_ck ptck,yd_entryentity.yd_textfield_hpbh ptwl,yd_entryentity.yd_decimalfield_sl sl" +
				",yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je,yd_entryentity.yd_ph ph" +
				",yd_entryentity.yd_scrq scrq,yd_entryentity.yd_dqr dqr,yd_bz bz,yd_dealcode dealcode";
//		// 查询发货明细
//		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
//		if (org.apache.commons.lang3.StringUtils.equals(lx, "1")) {
//			// 查询手工单据
//			fhmxgl = new QFilter[]{filter1, filter2, filter3, filter4, filter5, filter6, filter9, filter11, filter15, filter12, filter16, filter17, filter18, filter19};
//			DataSet yd_fhmxbsg = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
//			// 查询为开票并不为手工的单据
//			fhmxgl = new QFilter[]{filter1, filter2, filter3, filter4, filter5, filter6, filter10, filter11, filter7, filter15, filter12, filter16, filter17, filter18, filter19};
//			DataSet yd_fhmxbkp = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
//			// union发货明细
//			yd_fhmxb = yd_fhmxb.union(yd_fhmxbsg).union(yd_fhmxbkp);
//		}
		// 实体单的合单逻辑需要排除手工和发票的发货明细数据
//		if (org.apache.commons.lang3.StringUtils.equals(lx, "1")) {
//			// 类型为1的表示为为正常汇总，表示实体店逻辑处理，除了需要考虑“不为手工”和“不为发票”、还需要考虑“手工”和“发票”的发货明细
//			fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6,
//					filter15, filter12, filter11,filter16,filter17,filter18,filter19,filter20,filter21 };
//		}else {
//			fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,
//					filter15, filter12, filter11,filter16,filter17,filter18,filter19,filter20,filter21 };
//		}

		if ("1".equals(lx)) {
			// 获取直营店实体店的平台仓库编码集合
			Set<String> platformDirectWarehouse2 = DeliveryHelper.getPlatformDirectWarehouse("2");
			QFilter filter22 = new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse2); // 平台仓库取直营店仓库

			fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,
					filter15, filter12, dateFilter,filter16,filter17,filter18,filter19,filter20,filter21, filter22,filterPlat};
		}else {
			fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,
					filter15, filter12, dateFilter,filter16,filter17,filter18,filter19,filter20,filter21,filterPlat };
		}

		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		//////////////////////////////////////////

		// 查询运费汇总 不需要去除排除物料
		DataSet yfhz = yd_fhmxb.copy()
			.groupBy(new String[] { "billno", "yf", "th", "pt", "ptkh" }).finish().filter("yf>0")
			.groupBy(new String[] { "th", "pt", "ptkh" }).sum("yf").finish();

//		// 如果是类型为1，正常汇总，需要考虑正常不汇总的情况进行处理，
//		if (org.apache.commons.lang3.StringUtils.equals(lx, "1")) {
//			filter1 = QFilter.of("yd_entryentity.yd_combofield_hzcl in ('1', '2')");// 客户汇总类型
//		}else {
//			filter1 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", lx);// 客户汇总类型
//		}
		// 针对需要处理的汇总类型进行过滤
		filter1 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", lx);// 客户汇总类型

		fhmxgl = new QFilter[] { filter1, filterSh };
		//关联出客户仓库物料组织
		DataSet dswl = glmx(fywl, yd_fhmxb, fhmxgl);
		//dswl.print(true);
		// 查询对应客户
		DataSet khdygx = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number kh,yd_entryentity.yd_orgfield_dyzz zz",
				fhmxgl, null);
		String[] zdhj=new String[] { "pt","th", "kh", "zz" };
		//String[] zdhj=new String[] { "pt","th", "kh", "ckzz" };
		if("1".equals(lx))
		{
			yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
					.select(new String[] { "yf", "th" }, new String[] { "kh", "zz" }).finish()
					.groupBy(new String[] { "th", "kh", "zz" }).sum("yf").finish();
		}
		else
		{
			yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
					.select(new String[] { "yf", "th" }, new String[] { "ptkh", "zz" }).finish()
					.groupBy(new String[] { "th", "ptkh", "zz" }).sum("yf").finish();
			zdhj=new String[] { "pt","th", "kh", "zz","ptkh","thkcsw", "kcsw","quyu"};
			
		}
		// yfhz.print(true);
		DataSet dataSetgroupBy = dswl.copy().filter("th=false").select(zdhj).groupBy(zdhj).finish();
		if("1".equals(lx))
		{
			zdhj=new String[] { "pt","th", "kh", "ckzz" };
		}
		else
		{
			zdhj=new String[] { "pt","th", "kh", "ckzz","ptkh","thkcsw", "kcsw","quyu"};
		}
		 dataSetgroupBy = dataSetgroupBy.union(dswl.copy().filter("th=true").select(zdhj).groupBy(zdhj).finish());
		 
		//dswl.copy().print(true);
		//dataSetgroupBy.addField("", "");
		//dataSetgroupBy.copy().print(true);
		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型
		if ("4".equals(lx)) {
			formid = "im_otheroutbill";
			djlx = "im_OtherOutBill_STD_BT_S";
			biztypenumber = "355";
			je = "amount";
			xyd = "2";
		}
		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz");
			//String zzbm = row.getString("ckzz");	
			String khbm = row.getString("kh");
			String pt = row.getString("pt");
			Boolean th = row.getBoolean("th");
			String dpbm="";
//			if(th) {
//				// 查询仓库对应组织
//				String ckbm = "";
//				
//				for (Row rowdy : dswl.copy()) {
//					ckbm = rowdy.getString("ck");
//					break;
//				}
//				filter1 = new QFilter("number", QCP.equals, ckbm);
//				DynamicObject khojb = BusinessDataServiceHelper.loadSingle("bd_warehouse", "createorg.id",
//						new QFilter[] { filter1 });
//				zzbm = khojb.getString("createorg.id");
//			}
			// 查询客户对应税率
			filter1 = new QFilter("number", QCP.equals, khbm);
			DynamicObject[] khojb = BusinessDataServiceHelper.load("bd_customer", "taxrate.number",
					new QFilter[] { filter1 });
			String slbm = "";
			for (DynamicObject rowdy : khojb) {
				slbm = rowdy.getString("taxrate.number");
			}
			IFormView view = ABillServiceHelper.createAddView(formid);
			view.getModel().setItemValueByNumber("billtype", djlx);
			view.getModel().setValue("org", zzbm);
			if (lx != "4") {
				view.getModel().setValue("bizorg", zzbm);
			}
			if (th) {
				if ("4".equals(lx)) {
					view.getModel().setItemValueByNumber("biztype", "3551");
				} else {
					view.getModel().setItemValueByNumber("biztype", "2101");
					view.getModel().setItemValueByNumber("invscheme", "2101");
				}
			} else {
				view.getModel().setItemValueByNumber("biztype", biztypenumber);
				view.getModel().setItemValueByNumber("invscheme", "210");
			}
			view.getModel().setItemValueByNumber("customer", khbm);
			view.getModel().setItemValueByNumber("yd_dzdkh", khbm);
			view.getModel().setValue("biztime", rq);
			view.getModel().setValue("yd_tbly", pt);
			String khdp="kh=" + "'" + khbm + "'";
			if("4".equals(lx))
			{
				String kcsw="0";
				if(th)
				{
					//kcsw = row.getString("thkcsw");
					filter1 = new QFilter("yd_entryentity.yd_orgfield_dyzz", QCP.equals, zzbm);
					filter2 = new QFilter("yd_entryentity.yd_thkcsw", QCP.not_equals, "0");
					DataSet kcswjb = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
							"yd_entryentity.yd_thkcsw thkcsw",
							new QFilter[] { filter1,filter2 }, null).groupBy(new String[] {"thkcsw"}).finish();
					for (Row rowkcsw : kcswjb) {
						kcsw = rowkcsw.getString("thkcsw");
					}
				}
				else
				{
					kcsw = row.getString("kcsw");
				}
				if(kcsw!="0")
				{
					view.getModel().setValue("invscheme", kcsw);
				}
				String quyu = row.getString("quyu");
				view.getModel().setValue("yd_quyu", quyu);
				dpbm = row.getString("ptkh");
				khdp="ptkh=" + "'" + dpbm + "'";
				//view.getModel().setValue("comment", dpbm);
			}
//			view.getModel().setItemValueByNumber("currency", "CNY");
//			view.getModel().setItemValueByNumber("settlecurrency", "CNY");
			String zzgl="zz="+zzbm;
			if(th)
			{
				zzgl="ckzz="+zzbm;
			}
			// 查询当前客户与退货对应的汇总运费
			DataSet dqyf = yfhz.copy().filter(khdp).filter("th=" + row.getString("th"));
			BigDecimal yf = BigDecimal.ZERO;
			for (Row rowds : dqyf) {
				yf = rowds.getBigDecimal("yf");
			}

			DataSet dsxh = dswl.copy();
			DataSet dsxhW0 = dswl.copy();
			DataSet dsbillno = dswl.copy();
			dsbillno = dsbillno.filter(khdp).filter(zzgl)
					.filter("th=" + row.getString("th")).select(new String[] { "billno" })
					.groupBy(new String[] { "billno" }).finish();
			// dsxh.copy().print(false);
			// 汇总金额大于0的物料
			//dsxh.copy().print(false);
			dsxh = dsxh.filter(khdp).filter(zzgl)
					.filter("th=" + row.getString("th")).filter("je>0").select(new String[] { "wl", "sl", "je", "ck"})
					.groupBy(new String[] { "wl", "ck" }).sum("sl").sum("je").finish();
			// 汇总金额等于0的物料
			dsxhW0 = dsxhW0.filter(khdp).filter(zzgl)
					.filter("th=" + row.getString("th")).filter("je=0").select(new String[] { "wl", "sl", "je", "ck" })
					.groupBy(new String[] { "wl", "ck" }).sum("sl").sum("je").finish();
//			// 汇总金额大于0的物料
//						dsxh = dsxh.filter(khdp)
//								.filter("th=" + row.getString("th")).filter("je>0").select(new String[] { "wl", "sl", "je", "ck","ph" })
//								.groupBy(new String[] { "wl", "ck","ph" }).sum("sl").sum("je").finish();
//						// 汇总金额等于0的物料
//						dsxhW0 = dsxhW0.filter(khdp)
//								.filter("th=" + row.getString("th")).filter("je=0").select(new String[] { "wl", "sl", "je", "ck","ph" })
//								.groupBy(new String[] { "wl", "ck" ,"ph"}).sum("sl").sum("je").finish();
			dsxh = dsxh.union(dsxhW0);
			//dsxh.copy().print(false);
			int i = 0;
			BigDecimal zje = BigDecimal.ZERO;// 整单总金额
			for (Row rowds : dsxh) {
				String ckbm = rowds.getString("ck");
				//String ph = rowds.getString("ph");
				String wlbm = rowds.getString("wl");// sku
				BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
				// BigDecimal goods_price = rowds.getBigDecimal("dj");// 商品单价
				BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
				boolean sffy = true;
				for (String fywlbm : fywl) {
					if (wlbm.equals(fywlbm)) {
						yf = yf.add(share_payment);
						sffy = false;
						break;
					}
				}
				if (sffy) {

					if (i > 0) {
						view.getModel().createNewEntryRow("billentry");
					}
					view.getModel().setItemValueByNumber("material", wlbm, i);
					view.getModel().setItemValueByNumber("warehouse", ckbm, i);
					view.getModel().setValue("qty", goods_number, i);
					//view.getModel().setValue("yd_ph", ph, i);
					view.getModel().setValue(je, share_payment, i);
					if (share_payment.compareTo(BigDecimal.ZERO) == 0 && lx.equals("1")) {
						view.getModel().setValue("ispresent", "1", i);
					}
					if("4".equals(lx))
					{
					view.getModel().setValue("yd_store", dpbm,i);
					}
					if (slbm != null) {
						if ("1".equals(lx)) {
							view.getModel().setItemValueByNumber("taxrateid", slbm, i);
						}
					}
					zje = zje.add(share_payment);
					i++;
				}
			}
			view.getModel().setValue("yd_amountfield_yf", yf);
			// view.getModel().setValue("yd_textfield_fhmxdh", billno);
			List<String> fhmxdh = new ArrayList<String>();// 记录需要会写销售出库单/其他出库的发货明细单号
//			if (yf.compareTo(BigDecimal.ZERO) > 0) {
//				// 处理分摊
//				DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
//				BigDecimal ljft = BigDecimal.ZERO;
//				for (int j = 0; j < entrys.getRowCount(); j++) {
//					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
//					// BigDecimal sl=entrys.get(j).getBigDecimal("qty");//每行数量
//					if (j < entrys.getRowCount() - 1) {
//						BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
//						// BigDecimal yfft=new
//						// BigDecimal(((hsje.doubleValue()/zje.doubleValue())*yf.doubleValue())).setScale(2);//每行分摊运费
//						// BigDecimal hsdj=hsje.add(yfft).divide(sl);//每行分摊后含税单价
//						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
//						ljft = ljft.add(yfft);
//						view.getModel().setValue(je, hsjeft, j);
//					} else {
//						BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
//						// BigDecimal hsdj=hsje.add(yfft).divide(sl);//每行分摊后含税单价
//						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
//						view.getModel().setValue(je, hsjeft, j);
//					}
//				}
//			}
			if (yf.compareTo(BigDecimal.ZERO) > 0) {
				// 处理分摊
				DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
				int hs=0;
				for (int j = 0; j < entrys.getRowCount(); j++) {
					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
					if(hsje.compareTo(BigDecimal.ZERO) > 0)
					{
						hs++;
					}
				}
				BigDecimal ljft = BigDecimal.ZERO;
				int jehs=0;
				for (int j = 0; j < entrys.getRowCount(); j++) {
					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
					if(hsje.compareTo(BigDecimal.ZERO) > 0)
					{
						jehs++;
					if (jehs < hs) {
						BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
						ljft = ljft.add(yfft);
						view.getModel().setValue(je, hsjeft, j);
					} else {
						BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
						view.getModel().setValue(je, hsjeft, j);
						// update by hst 2023/02/01 分摊运费后若金额为0标记为赠品
						if (hsjeft.compareTo(BigDecimal.ZERO) == 0) {
							view.getModel().setValue("ispresent","1",j);
						}
					}
					}
				}
			}
			int j = 0;
			for (Row rowds : dsbillno) {
				if (j > 0) {
					view.getModel().createNewEntryRow("yd_entryentity");
				}
				String billno = rowds.getString("billno");
				view.getModel().setValue("yd_textfield_fhmxdh", billno, j);
				fhmxdh.add(billno);
				j++;
			}
			if (i > 0) {
				OperationResult operationResult = ABillServiceHelper.saveOperate(view);
				filter1 = new QFilter("billno", QCP.in, fhmxdh);
				// 判断保存结果
				if (operationResult.isSuccess()) {
					String djbh = "";
					Map<Object, String> map = operationResult.getBillNos();
					for (Map.Entry<Object, String> entry : map.entrySet()) {

						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
								"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							djbh = entry.getValue();
							rowdy.set("yd_textfield_xsckdh", entry.getValue());// 修改数据
							rowdy.set("yd_combofield_xyd", xyd);
						}
						SaveServiceHelper.save(dy);
					}
					// 进行提交操作
//					IFormView modifyView = ABillServiceHelper.createModifyView(view.getEntityId(),
//							operationResult.getSuccessPkIds().get(0).toString());
					// update by hst 2023/3/28 若当前客户在客户对应关系表中维护为分摊运费且汇总类型为1
					DynamicObject customer = view.getModel().getDataEntity().getDynamicObject("customer");
					if (!("1".equals(lx) && shareClient.indexOf(customer.getString("id")) > -1)) {
						operationResult = view.invokeOperation("submit");
					}
					if (!operationResult.isSuccess()) {
						String errMessage = operationResult.getMessage() + ","; // 错误摘要
						// // 演示提取保存详细错误
						for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
							errMessage += errInfo.getMessage() + ",";
						}
						if (errMessage.length() > 1800) {
							errMessage = errMessage.substring(0, 1800);
						}
						filter1 = new QFilter("billno", QCP.equals, djbh);
						DynamicObject[] dy = BusinessDataServiceHelper.load(formid, "yd_sftj",
								new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_sftj", errMessage);// 修改数据
						}
						SaveServiceHelper.save(dy);
					}
					ABillServiceHelper.exitView(view);
				} else {
					String errMessage = operationResult.getMessage() + ","; // 错误摘要
					// // 演示提取保存详细错误
					for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
						errMessage += errInfo.getMessage() + ",";
					}
					if (errMessage.length() > 1800) {
						errMessage = errMessage.substring(0, 1800);
					}
					DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
							new QFilter[] { filter1 });
					for (DynamicObject rowdy : dy) {
						rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
//						rowdy.set("yd_iserror", true);// 修改单据为异常
					}
					SaveServiceHelper.save(dy);
					ABillServiceHelper.exitView(view);
				}
			} else {
				ABillServiceHelper.exitView(view);
			}
		}
		dswl.close();
	}

	/**
	 * 直营一盘货结算方式处理
	 * @param lx 汇总类型（无作用，直营店结算需要考虑的汇总类型为“正常发货汇总”、“正常发货不汇总”）
	 * @param pcwlin 直营店一盘货-不需要按运费统计的物料
	 * @param fywl 直营店一盘货-需要按运费统计的物料
	 * @param ppfd 品牌分单店铺
	 * @param rq 发货日期
	 * <AUTHOR>
	 * @date 2022-7-14
	 */
	public static void bchbdj_direct(String lx, List<String> pcwlin, List<String> fywl, List<String> ppfd, String rq,QFilter filterPlat, QFilter dateFilter) {

//		// 获取当天的格式化日期
//		String sj = DateTimeUtils.format(new Date(), DateTimeUtils.SDF_DATE);
//		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,
//				filter9, filter12, filter11,filter16,filter17,filter18,filter19,filter20 };

		// 获取直营店一盘货的平台仓库编码集合
		Set<String> platformDirectWarehouse = DeliveryHelper.getPlatformDirectWarehouse("1");

		// 不区分是否包含 “不为手工”和“不为开票” 的发货明细单
		// 需要包含3种情况， “不为手工”和“不为开票”、“为手工”、“不为手工”和“为开票”
		QFilter fhmxgl = QFilter.of("yd_textfield_xsckdh=?", "")	// 下游单据编号为空
				.and(QFilter.of("yd_checkboxfield_khbcz =?", "0"))	// 客户存在
				.and(QFilter.of("yd_checkboxfield_ckbcz =?", "0"))	// 仓库存在
				.and(QFilter.of("yd_checkboxfield_wlbcz =?", "0"))	// 物料存在
				.and(QFilter.of("yd_checkboxfield_bcl =?", "0"))	// 需要处理
				.and(new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin))	// 去除排除物料
//				.and(QFilter.of("yd_checkboxfield_sfsg =?", "0"))	// 不为手工
//				.and(QFilter.of("yd_checkboxfield_sfkp =?", "0"))	// 不为开票
				.and(QFilter.of("yd_checkboxfield_wlwsh =?", "0"))	// 物料合规
				.and(QFilter.of("yd_datefield_fhrq = ?", rq))	// 日期
				.and(dateFilter)	// 下单时间小于今天
				.and(QFilter.of("yd_checkboxfield_khwdyzz =?", "0"))	// 全部为排除物料为否
				.and(new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd))	// 去除按照品牌分单店铺
				.and(QFilter.of("yd_pcck =?", "0"))	// 排除仓库为否
				.and(QFilter.of("yd_dygxcf =?", "0"))	// 物料对应关系重复为否
				.and(QFilter.of("yd_iserror =?", "0"))	//发货明细表单据异常不勾选的才允许处理
				.and(QFilter.of("yd_textfield_sbyy =?", "")) // 失败原因为空
				.and(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse)) // 仓库平台编码过滤，获取直营店一盘货的仓库
				.and(filterPlat);// 平台

		// 查询发货明细
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb",
				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf" +
						",yd_textfield_ck ptck,yd_entryentity.yd_textfield_hpbh ptwl,yd_entryentity.yd_decimalfield_sl sl" +
						",yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je,yd_entryentity.yd_ph ph" +
						",yd_entryentity.yd_scrq scrq,yd_entryentity.yd_dqr dqr,yd_bz bz,yd_dealcode dealcode",
				fhmxgl.toArray(), null);
		// 查询运费汇总 不需要去除排除物料
//		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter7, filter8, filter9, filter12,
//				filter11,filter16,filter17,filter18,filter19 };

//		QFilter fhmxgl2 = QFilter.of("yd_textfield_xsckdh=?", "")	// 下游单据编号为空
//				.and(QFilter.of("yd_checkboxfield_khbcz =?", "0"))	// 客户存在
//				.and(QFilter.of("yd_checkboxfield_ckbcz =?", "0"))	// 仓库存在
//				.and(QFilter.of("yd_checkboxfield_wlbcz =?", "0"))	// 物料存在
//				.and(QFilter.of("yd_checkboxfield_bcl =?", "0"))	// 需要处理
////				.and(QFilter.of("yd_checkboxfield_sfsg =?", "0"))	// 不为手工
////				.and(QFilter.of("yd_checkboxfield_sfkp =?", "0"))	// 不为开票
//				.and(QFilter.of("yd_checkboxfield_wlwsh =?", "0"))	// 物料合规
//				.and(QFilter.of("yd_datefield_fhrq = ?", rq))	// 日期
//				.and(QFilter.of("yd_datetimefield_xdsj <?", sj))	// 下单时间小于今天
//				.and(QFilter.of("yd_checkboxfield_khwdyzz =?", "0"))	// 全部为排除物料为否
//				.and(new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd))	// 去除按照品牌分单店铺
//				.and(QFilter.of("yd_pcck =?", "0"))	// 排除仓库为否
//				.and(QFilter.of("yd_dygxcf =?", "0"))	// 物料对应关系重复为否
//				.and(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse)); // 仓库平台编码过滤，获取直营店一盘货的仓库

		// 根据查询出来的数据统计运费
		// 按 是否退货 + 系统平台 + 客户 的维度统计所有的运费进行相加
		// 有些物料需要单独统计运费的，需要在后面将运费加上
//		DataSet yfhz = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb,
//				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf",
//				fhmxgl2.toArray(), null)
//				.groupBy(new String[] { "billno", "yf", "th", "pt", "ptkh" }).finish().filter("yf>0")
//				.groupBy(new String[] { "th", "pt", "ptkh" }).sum("yf").finish();
		DataSet yfhz = yd_fhmxb.copy()
				.groupBy(new String[] { "billno", "yf", "th", "pt", "ptkh" }).finish().filter("yf>0")
				.groupBy(new String[] { "th", "pt", "ptkh" }).sum("yf").finish();

		// 客户汇总类型为正常汇总且过滤单据为审核的数据
		QFilter fhmxgl3 = QFilter.of("yd_entryentity.yd_combofield_hzcl in ('1','2') and billstatus = 'C'");

		//关联出客户仓库物料组织
		DataSet dswl = glmx(fywl, yd_fhmxb, fhmxgl3.toArray());
		//dswl.print(true);
		// 查询对应客户
		DataSet khdygx = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number kh,yd_entryentity.yd_orgfield_dyzz zz",
				fhmxgl3.toArray(), null);
		String[] zdhj=new String[] { "pt","th", "kh", "zz" };
		// 对查出来的运费数据 拼接上 苍穹的客户（按 平台+客户 的维度关联）
		yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
				.select(new String[] { "yf", "th" }, new String[] { "kh", "zz" }).finish()
				.groupBy(new String[] { "th", "kh", "zz" }).sum("yf").finish();

		/*
		 * 这里跟直营店的区别为：
		 * 1、直营店汇总的方式：按客户+仓库+物料汇总，直营店一盘货的汇总方式：按品牌+批次+仓库+物料进行汇总
		 * update by hst 2023/03/30 直营店一盘货的汇总方式：按品牌+批次+仓库+物料+产品类别进行汇总
		 * 2、运费：直营店是按客户进行汇总，直营店一盘货按品牌的汇总放到其中一张单里面
		 * 3、出库单的状态：直营店为提交状态，而直营店一盘货的状态重置为保存状态即可（或不需要提交即可）
		 * 4、如果是直营店一盘货生成的出库单需要添加直营店结算的标识
		 */
		DataSet dataSetgroupBy = dswl.copy().select(zdhj).groupBy(zdhj).finish();

		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型

		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz"); // 组织
			String khbm = row.getString("kh"); // 客户
			String pt = row.getString("pt"); // 平台
			Boolean th = row.getBoolean("th"); // 是否退货

			String dpbm="";
			// 查询客户对应税率
			String slbm = BizHelper.getQueryOne("bd_customer", "taxrate.number", new QFilter("number", QCP.equals, khbm).toArray());

			String khdp = String.format("kh='%s'", khbm);
			DataSet dsxh = dswl.copy();
			DataSet dsxh0 = dswl.copy();
			DataSet dsbillno = dswl.copy();
			// update by hst 2023/03/31 增加产品类别维度
			dsbillno = dsbillno.filter(khdp)
					.filter("th=" + th).select(new String[] { "billno","pp","cplb" })
					.groupBy(new String[] { "billno","pp","cplb" }).finish();
			// 汇总物料
			// 非赠品的数据统计
			// update by hst 2023/03/30 增加产品类别维度
			dsxh = dsxh.filter(khdp)
					.filter("th=" + row.getString("th")).filter("je>0").select(new String[] { "wl", "sl", "je", "ck","ph","pp","scrq","dqr","cplb" })
					.groupBy(new String[] { "wl", "ck","ph","pp","scrq","dqr","cplb" }).sum("sl").sum("je").finish();
			// 赠品的数量统计
			// update by hst 2023/03/30 增加产品类别维度
			dsxh0 = dsxh0.filter(khdp)
					.filter("th=" + row.getString("th")).filter("je=0").select(new String[] { "wl", "sl", "je", "ck","ph","pp","scrq","dqr","cplb" })
					.groupBy(new String[] { "wl", "ck","ph","pp","scrq","dqr","cplb" }).sum("sl").sum("je").finish();
			dsxh = dsxh.union(dsxh0);

			// 获取所有的品牌，按品牌拆单
			// update by hst 2023/03/30 增加产品类别维度
			DataSet pps=dsxh.copy().select(new String[] {"pp","cplb"}).where("pp!=1 and cplb!=1").groupBy(new String[] {"pp","cplb"}).finish();

			// 按 客户+是否退货 获取运费的值
			DataSet dqyf = yfhz.copy().filter("th="+th+" and kh='"+khbm+"' and zz="+zzbm);
			BigDecimal yf = BigDecimal.ZERO;
			if (dqyf.hasNext()) {
				Row rowds = dqyf.next();
				yf = rowds.getBigDecimal("yf");
			}

			// 获取单次变量品牌的数量
			int ppCount = pps.copy().count("pp", true);
			int ppIndex = 1;

			for(Row rowpp: pps) {
				IFormView view = ABillServiceHelper.createAddView(formid);
				view.getModel().setItemValueByNumber("billtype", djlx);
				view.getModel().setValue("org", zzbm);
				view.getModel().setValue("bizorg", zzbm);

				if (th) {
					view.getModel().setItemValueByNumber("biztype", "2101");
					view.getModel().setItemValueByNumber("invscheme", "2101");
				} else {
					view.getModel().setItemValueByNumber("biztype", biztypenumber);
					view.getModel().setItemValueByNumber("invscheme", "210");
				}
				view.getModel().setItemValueByNumber("customer", khbm);
				view.getModel().setItemValueByNumber("yd_dzdkh", khbm);
				view.getModel().setValue("biztime", rq);
				view.getModel().setValue("yd_tbly", pt);
				// 直营店一盘货的结算，需要设置是否直营店结算为“是”
//				view.getModel().setValue("yd_frome3", 1);
				view.getModel().setValue("yd_isdirectsettle", 1);

				String pp=rowpp.getString("pp");
				// update by hst 2023/03/30 增加产品类别维度
				String cplb = rowpp.getString("cplb");
				DataSet dsmx=dsxh.copy().filter("pp="+pp).filter("cplb="+cplb);
				DataSet dsbillnopp=dsbillno.copy().filter("pp="+pp).filter("cplb="+cplb); // 根据品牌获取汇总的组织编码
				int i = 0;
				BigDecimal zje = BigDecimal.ZERO; // 分录明细的总额
				for (Row rowds : dsmx) {
					String ckbm = rowds.getString("ck");
					String wlbm = rowds.getString("wl");// sku
					BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
					String ph = rowds.getString("ph");//批号
					Date scrq = rowds.getDate("scrq");//生产日期
					Date dqr = rowds.getDate("dqr");//到期日
					BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
					String lb = rowds.getString("cplb");// update by hst 2023/03/30 产品类别

					if (fywl.contains(wlbm)) {
						yf = yf.add(share_payment);
						continue;
					}

					if (i > 0) {
						view.getModel().createNewEntryRow("billentry");
					}
					view.getModel().setItemValueByNumber("material", wlbm, i);
					view.getModel().setValue("yd_ph", ph, i);
					if(scrq!=null)
					{
						view.getModel().setValue("yd_scrq", scrq, i);
					}
					if(dqr!=null)
					{
						view.getModel().setValue("yd_dqr", dqr, i);
					}
					view.getModel().setItemValueByNumber("warehouse", ckbm, i);
					view.getModel().setValue("qty", goods_number, i);
					// view.getModel().setValue(dj, goods_price, i);

					view.getModel().setValue(je, share_payment, i);
					// 是否赠品，如果是赠品，需要把单据标识了
					if (share_payment.compareTo(BigDecimal.ZERO) == 0) {
						view.getModel().setValue("ispresent", "1", i);
					}

					if (slbm != null) {
						view.getModel().setItemValueByNumber("taxrateid", slbm, i);
					}
					// update by hst 2023/03/30
					view.getModel().setValue("yd_procategory", lb, i);
					zje = zje.add(share_payment);
					i++;
				}

				// 判断是否为变量的最后一个品牌，如果是，则反写运费
				if (ppIndex++ == ppCount) {
					// 获取当前客户，需要排除物料统计运费的金额
					DataSet yfwlDataSet = dsxh.copy().filter("pp=1");
					BigDecimal yfwlAmount = BigDecimal.ZERO;
					for (Row yfwlRow : yfwlDataSet) {
						yfwlAmount = yfwlAmount.add(yfwlRow.getBigDecimal("je"));
					}
					yf = yf.add(yfwlAmount);
					view.getModel().setValue("yd_amountfield_yf", yf);

					// 存在运费就需要分摊到每个分录明细
					if (yf.compareTo(BigDecimal.ZERO) > 0) {
						// 处理分摊
						DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
						int hs=0;
						for (int j = 0; j < entrys.getRowCount(); j++) {
							BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
							if(hsje.compareTo(BigDecimal.ZERO) > 0) {
								hs++;
							}
						}
						BigDecimal ljft = BigDecimal.ZERO;
						int jehs=0;
						for (int j = 0; j < entrys.getRowCount(); j++) {
							BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
							if(hsje.compareTo(BigDecimal.ZERO) > 0) {
								jehs++;
								if (jehs < hs) {
									BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
									BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
									ljft = ljft.add(yfft);
									view.getModel().setValue(je, hsjeft, j);
								} else {
									BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
									BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
									view.getModel().setValue(je, hsjeft, j);
									// update by hst 2023/02/01 分摊运费后若金额为0标记为赠品
									if (hsjeft.compareTo(BigDecimal.ZERO) == 0) {
										view.getModel().setValue("ispresent","1",j);
									}
								}
							}
						}
					}
				}

				List<String> fhmxdh = new ArrayList<>();// 记录需要回写销售出库单/其他出库的发货明细单号
				int j = 0;
				for (Row rowds : dsbillnopp) {
					if (j > 0) {
						view.getModel().createNewEntryRow("yd_entryentity");
					}
					String billno = rowds.getString("billno");
					view.getModel().setValue("yd_textfield_fhmxdh", billno, j);
					fhmxdh.add(billno);
					j++;
				}
				if (i > 0) {
					OperationResult operationResult = ABillServiceHelper.saveOperate(view);
					QFilter filter1 = new QFilter("billno", QCP.in, fhmxdh);
					// 判断保存结果
					if (operationResult.isSuccess()) {
						String djbh = "";
						Map<Object, String> map = operationResult.getBillNos();
						for (Map.Entry<Object, String> entry : map.entrySet()) {

							DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
									"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
							for (DynamicObject rowdy : dy) {
								String dh=rowdy.getString("yd_textfield_xsckdh");
								if(dh!="")
								{
									djbh =dh+","+entry.getValue();
								}
								else
								{
									djbh=entry.getValue();
								}
								rowdy.set("yd_textfield_xsckdh", djbh);// 修改数据
								rowdy.set("yd_combofield_xyd", xyd);
							}
							SaveServiceHelper.save(dy);
						}
						ABillServiceHelper.exitView(view);
					} else {
						String errMessage = operationResult.getMessage() + ","; // 错误摘要
						// // 演示提取保存详细错误
						for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
							errMessage += errInfo.getMessage() + ",";
						}
						if (errMessage.length() > 1800) {
							errMessage = errMessage.substring(0, 1800);
						}
						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
								new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
//							rowdy.set("yd_iserror", true);// 修改单据为异常
						}
						SaveServiceHelper.save(dy);
						ABillServiceHelper.exitView(view);
					}
				} else {
					ABillServiceHelper.exitView(view);
				}
			}
		}
		dswl.close();
	}

	public static void bchbdjby(String lx, List<String> pcwlin, List<String> fywl, List<String> ppfd, String rq,QFilter filterPlat, QFilter dateFilter) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
//		QFilter filter7 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不为手工
//		QFilter filter8 = QFilter.of("yd_checkboxfield_sfkp =?", "0");// 不为开票
		QFilter filter9 = QFilter.of("yd_checkboxfield_wlwsh =?", "0");// 物料合规
		QFilter filter16 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter17 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只过滤按照品牌分单店铺
		QFilter filter18 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter19 = QFilter.of("yd_dygxcf =?", "0");// 物料对应关系重复为否
		QFilter filter20 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
//		QFilter filter17 = QFilter.of("yd_combofield_pt =?", "1");// 平台为佰悦e3
//		QFilter filter18 = QFilter.of("yd_datefield_fhrq >?", "2021-12-11");// 日期大于20号
		// QFilter filter12 = QFilter.of("to_char(yd_datetimefield_xdsj,'yyyy-MM-dd') =
		// ?", rq);// 日期
		QFilter filter12 = QFilter.of("yd_datefield_fhrq = ?", rq);// 日期
		QFilter filterSh = QFilter.of("billstatus=?", "C");
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter11 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6,
				filter9, filter12, dateFilter,filter16,filter17,filter18,filter19,filter20,filterPlat };
		// 查询发货明细
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb",
				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf,yd_textfield_ck ptck,yd_entryentity.yd_textfield_hpbh ptwl,yd_entryentity.yd_decimalfield_sl sl,yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je,yd_entryentity.yd_ph ph,yd_entryentity.yd_scrq scrq,yd_entryentity.yd_dqr dqr,yd_bz bz,yd_dealcode dealcode",
				fhmxgl, null);
		// 查询运费汇总 不需要去除排除物料
		//yd_fhmxb.copy().print(true);
//		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter9, filter12,
//				filter11,filter16,filter17 };
//		DataSet yfhz = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb",
//				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh ptkh,yd_decimalfield_yf yf",
//				fhmxgl, null).groupBy(new String[] { "billno", "yf", "th", "pt", "ptkh" }).finish().filter("yf>0")
//				.groupBy(new String[] { "th", "pt", "ptkh" }).sum("yf").finish();
		filter1 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", lx);// 客户汇总类型
		fhmxgl = new QFilter[] { filter1, filterSh };
		//关联出客户仓库物料组织
		DataSet dswl = glmx(fywl, yd_fhmxb, fhmxgl);
		dswl=dswl.filter("pp !=1");
		//dswl.print(true);
		// 查询对应客户
//		DataSet khdygx = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
//				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number kh,yd_entryentity.yd_orgfield_dyzz zz",
//				fhmxgl, null);
		String[] zdhj=new String[] { "pt","th", "kh", "zz" };
		if("1".equals(lx))
		{
//		yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
//				.select(new String[] { "yf", "th" }, new String[] { "kh", "zz" }).finish()
//				.groupBy(new String[] { "th", "kh", "zz" }).sum("yf").finish();
		}
		else
		{
//			yfhz = yfhz.join(khdygx).on("pt", "pt").on("ptkh", "bm")
//					.select(new String[] { "yf", "th" }, new String[] { "ptkh", "zz" }).finish()
//					.groupBy(new String[] { "th", "ptkh", "zz" }).sum("yf").finish();
			zdhj=new String[] { "pt","th", "kh", "zz","ptkh","thkcsw", "kcsw","quyu"};
		}
		// yfhz.print(true);
		DataSet dataSetgroupBy = dswl.copy().select(zdhj).groupBy(zdhj).finish();
		//dswl.copy().print(true);
		//dataSetgroupBy.addField("", "");
		//dataSetgroupBy.print(true);
		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型
		if ("4".equals(lx)) {
			formid = "im_otheroutbill";
			djlx = "im_OtherOutBill_STD_BT_S";
			biztypenumber = "355";
			je = "amount";
			xyd = "2";
		}

		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz");
			String khbm = row.getString("kh");
			String pt = row.getString("pt");
			Boolean th = row.getBoolean("th");
			String dpbm="";
//			if(th) {
//				// 查询仓库对应组织
//				String ckbm = "";
//				
//				for (Row rowdy : dswl.copy()) {
//					ckbm = rowdy.getString("ck");
//					break;
//				}
//				filter1 = new QFilter("number", QCP.equals, ckbm);
//				DynamicObject khojb = BusinessDataServiceHelper.loadSingle("bd_warehouse", "createorg.id",
//						new QFilter[] { filter1 });
//				zzbm = khojb.getString("createorg.id");
//			}
			// 查询客户对应税率
			filter1 = new QFilter("number", QCP.equals, khbm);
			DynamicObject[] khojb = BusinessDataServiceHelper.load("bd_customer", "taxrate.number",
					new QFilter[] { filter1 });
			String slbm = "";
			for (DynamicObject rowdy : khojb) {
				slbm = rowdy.getString("taxrate.number");
			}

			String khdp="kh=" + "'" + khbm + "'";
			if("4".equals(lx))
			{
//				String kcsw="0";
//				if(th)
//				{
//					kcsw = row.getString("thkcsw");
//				}
//				else
//				{
//					kcsw = row.getString("kcsw");
//				}
//				if(kcsw!="0")
//				{
//					//view.getModel().setValue("invscheme", kcsw);
//				}
//				String quyu = row.getString("quyu");
				//view.getModel().setValue("yd_quyu", quyu);
				dpbm = row.getString("ptkh");
				khdp="ptkh=" + "'" + dpbm + "'";
				
			}
			// 查询当前客户与退货对应的汇总运费
//			DataSet dqyf = yfhz.copy().filter(khdp).filter("th=" + row.getString("th"));
//			BigDecimal yf = BigDecimal.ZERO;
//			for (Row rowds : dqyf) {
//				yf = rowds.getBigDecimal("yf");
//			}

			DataSet dsxh = dswl.copy();
			DataSet dsxhW0 = dswl.copy();
			DataSet dsbillno = dswl.copy();
			// update by hst 2023/03/31 经销商一盘货出库单增加产品类别维度
			if ("1".equals(lx)) {
				dsbillno = dsbillno.filter(khdp)
						.filter("th=" + row.getString("th")).select(new String[]{"billno", "pp","cplb"})
						.groupBy(new String[]{"billno", "pp","cplb"}).finish();
			} else {
				dsbillno = dsbillno.filter(khdp)
						.filter("th=" + row.getString("th")).select(new String[]{"billno", "pp"})
						.groupBy(new String[]{"billno", "pp"}).finish();
			}
			// dsxh.copy().print(false);
			// 汇总物料
			// update by hst 2023/03/31 经销商一盘货出库单增加产品类别维度
			if ("1".equals(lx)) {
				dsxh = dsxh.filter(khdp)
						.filter("th=" + row.getString("th")).select(new String[]{"wl", "sl", "je", "ck", "ph", "pp", "scrq", "dqr","cplb"})
						.groupBy(new String[]{"wl", "ck", "ph", "pp", "scrq", "dqr","cplb"}).sum("sl").sum("je").finish();
			} else {
				dsxh = dsxh.filter(khdp)
						.filter("th=" + row.getString("th")).select(new String[]{"wl", "sl", "je", "ck", "ph", "pp", "scrq", "dqr"})
						.groupBy(new String[]{"wl", "ck", "ph", "pp", "scrq", "dqr"}).sum("sl").sum("je").finish();
			}
			// 汇总金额等于0的物料
//			dsxhW0 = dsxhW0.filter(khdp)
//					.filter("th=" + row.getString("th")).filter("je=0").select(new String[] { "wl", "sl", "je", "ck","ph","pp","scrq","dqr" })
//					.groupBy(new String[] { "wl", "ck","ph","pp","scrq","dqr" }).sum("sl").sum("je").finish();

			//dsxh = dsxh.union(dsxhW0);
			//dsxh.print(true);
//			int zs=0;//总行数
//			BigDecimal zje = BigDecimal.ZERO;// 整单总金额
//			for (Row rowds : dsxh.copy()) {
//				String pp = rowds.getString("pp");// sku
//				BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
//				//zs=rowds.getInteger("zs");
//				if(share_payment.compareTo(BigDecimal.ZERO)==1)
//				{
//					if ("1".equals(pp)) {
//						yf = yf.add(share_payment);
//					}
//					else
//					{
//						zs++;
//						zje = zje.add(share_payment);
//						
//					}
//				}
//			}
//			dsxh=dsxh.filter("pp !=1").addField(yf.toPlainString(), "yf").addField(zje.toPlainString(), "zje");
//			dsxh=dsxh.addField("ROUND((je/zje)*yf,2)", "ftje");
			// update by hst 2023/03/31 经销商一盘货出库单增加产品类别维度
			DataSet pps=dsxh.copy();
			if ("1".equals(lx)) {
				pps=pps.select(new String[] {"pp","cplb"}).groupBy(new String[] {"pp","cplb"}).finish();
			} else {
				pps=pps.select(new String[] {"pp"}).groupBy(new String[] {"pp"}).finish();
			}
			//dsxh.copy().print(false);
//			int hs=0;//记录总行数
//			BigDecimal jlyf = BigDecimal.ZERO;//记录已生成运费
			for(Row rowpp:pps)
			{
				IFormView view = ABillServiceHelper.createAddView(formid);
				view.getModel().setItemValueByNumber("billtype", djlx);
				view.getModel().setValue("org", zzbm);
				if (lx != "4") {
					view.getModel().setValue("bizorg", zzbm);
				}
				if (th) {
					if ("4".equals(lx)) {
						view.getModel().setItemValueByNumber("biztype", "3551");
					} else {
						view.getModel().setItemValueByNumber("biztype", "2101");
						view.getModel().setItemValueByNumber("invscheme", "2101");
					}
				} else {
					view.getModel().setItemValueByNumber("biztype", biztypenumber);
					view.getModel().setItemValueByNumber("invscheme", "210");
				}
				view.getModel().setItemValueByNumber("customer", khbm);
				view.getModel().setItemValueByNumber("yd_dzdkh", khbm);
				view.getModel().setValue("biztime", rq);
				view.getModel().setValue("yd_tbly", pt);
				view.getModel().setValue("yd_frome3", 1);
				if("4".equals(lx))
				{
					String kcsw="0";
					if(th)
					{
						filter1 = new QFilter("yd_entryentity.yd_orgfield_dyzz", QCP.equals, zzbm);
						filter2 = new QFilter("yd_entryentity.yd_thkcsw", QCP.not_equals, "0");
						DataSet kcswjb = QueryServiceHelper.queryDataSet("bchbdj", "yd_khdygx",
								"yd_entryentity.yd_thkcsw thkcsw",
								new QFilter[] { filter1,filter2 }, null).groupBy(new String[] {"thkcsw"}).finish();
						for (Row rowkcsw : kcswjb) {
							kcsw = rowkcsw.getString("thkcsw");
						}
					}
					else
					{
						kcsw = row.getString("kcsw");
					}
					if(kcsw!="0")
					{
						view.getModel().setValue("invscheme", kcsw);
					}
					String quyu = row.getString("quyu");
					view.getModel().setValue("yd_quyu", quyu);
					dpbm = row.getString("ptkh");
					//view.getModel().setValue("comment", dpbm);
//					khdp="ptkh=" + "'" + dpbm + "'";
					
				}
				String pp=rowpp.getString("pp");
				// update by hst 2023/03/31 经销商一盘货出库单增加产品类别维度
				String cplb=rowpp.getString("cplb");
				DataSet dsmx = dsxh.copy();
				DataSet dsbillnopp = dsbillno.copy();
				if ("1".equals(lx)) {
					dsmx = dsmx.filter("pp=" + pp).filter("cplb="+cplb);
					dsbillnopp = dsbillnopp.filter("pp=" + pp).filter("cplb="+cplb);
				} else {
					dsmx = dsmx.filter("pp=" + pp);
					dsbillnopp = dsbillnopp.filter("pp=" + pp);
				}
				int i = 0;
				//BigDecimal djzyf=BigDecimal.ZERO;//记录单张运费金额
				for (Row rowds : dsmx) {

					String ckbm = rowds.getString("ck");
					String wlbm = rowds.getString("wl");// sku
					BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
					String ph = rowds.getString("ph");//批号
					Date scrq = rowds.getDate("scrq");//生产日期
					Date dqr = rowds.getDate("dqr");//到期日
					BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
//					BigDecimal ftje = rowds.getBigDecimal("ftje");// 分摊运费金额
//					BigDecimal ysje=share_payment.add(ftje);
//					if(share_payment.compareTo(BigDecimal.ZERO)==1)
//					{
//						hs++;
//					}
//					//最后一行使用总运费减去已使用累计运费
//					if(hs==zs)
//					{
//						ysje=share_payment.add(yf.subtract(jlyf));
//						djzyf=djzyf.add(yf.subtract(jlyf));
//						jlyf=jlyf.add(yf.subtract(jlyf));
//					}
//					else {
//						djzyf=djzyf.add(ftje);
//					jlyf=jlyf.add(ftje);
//					}
						if (i > 0) {
							view.getModel().createNewEntryRow("billentry");
						}
						view.getModel().setItemValueByNumber("material", wlbm, i);
						view.getModel().setValue("yd_ph", ph, i);
						if(scrq!=null)
						{
						view.getModel().setValue("yd_scrq", scrq, i);
						}
						if(dqr!=null)
						{
						view.getModel().setValue("yd_dqr", dqr, i);
						}
						if(lx.equals("4"))
						{
						view.getModel().setValue("yd_store", dpbm,i);
						}
						view.getModel().setItemValueByNumber("warehouse", ckbm, i);
						view.getModel().setValue("qty", goods_number, i);
						// view.getModel().setValue(dj, goods_price, i);

						view.getModel().setValue(je, share_payment, i);
//						if (ysje.compareTo(BigDecimal.ZERO) == 0 && lx.equals("1")) {
//							view.getModel().setValue("ispresent", "1", i);
//						}

						if (slbm != null) {
							if ("1".equals(lx)) {
								view.getModel().setItemValueByNumber("taxrateid", slbm, i);
							}
						}
					// update by hst 2023/03/31 销售出库单携带产品类别字段
						if ("1".equals(lx)) {
							String lb = rowds.getString("cplb");
							view.getModel().setValue("yd_procategory", lb, i);
						}
						//zje = zje.add(share_payment);
						i++;
				}
				//view.getModel().setValue("yd_amountfield_yf", djzyf);
				List<String> fhmxdh = new ArrayList<String>();// 记录需要回写销售出库单/其他出库的发货明细单号
				int j = 0;
				for (Row rowds : dsbillnopp) {
					if (j > 0) {
						view.getModel().createNewEntryRow("yd_entryentity");
					}
					String billno = rowds.getString("billno");
					view.getModel().setValue("yd_textfield_fhmxdh", billno, j);
					fhmxdh.add(billno);
					j++;
				}
				if (i > 0) {
					OperationResult operationResult = ABillServiceHelper.saveOperate(view);
					filter1 = new QFilter("billno", QCP.in, fhmxdh);
					// 判断保存结果
					if (operationResult.isSuccess()) {
						String djbh = "";
						Map<Object, String> map = operationResult.getBillNos();
						for (Map.Entry<Object, String> entry : map.entrySet()) {

							DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
									"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
							for (DynamicObject rowdy : dy) {
								String dh=rowdy.getString("yd_textfield_xsckdh");
								if(dh!="")
								{
								djbh =dh+","+entry.getValue();
								}
								else
								{
									djbh=entry.getValue();
								}
								rowdy.set("yd_textfield_xsckdh", djbh);// 修改数据
								rowdy.set("yd_combofield_xyd", xyd);
							}
							SaveServiceHelper.save(dy);
						}
						// 进行提交操作
//						IFormView modifyView = ABillServiceHelper.createModifyView(view.getEntityId(),
//								operationResult.getSuccessPkIds().get(0).toString());
//						operationResult = view.invokeOperation("submit");
//						if (!operationResult.isSuccess()) {
//							String errMessage = operationResult.getMessage() + ","; // 错误摘要
//							// // 演示提取保存详细错误
//							for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
//								errMessage += errInfo.getMessage() + ",";
//							}
//							if (errMessage.length() > 1800) {
//								errMessage = errMessage.substring(0, 1800);
//							}
//							filter1 = new QFilter("billno", QCP.equals, djbh);
//							DynamicObject[] dy = BusinessDataServiceHelper.load(formid, "yd_sftj",
//									new QFilter[] { filter1 });
//							for (DynamicObject rowdy : dy) {
//								rowdy.set("yd_sftj", errMessage);// 修改数据
//							}
//							SaveServiceHelper.save(dy);
//						}
						ABillServiceHelper.exitView(view);
					} else {
						String errMessage = operationResult.getMessage() + ","; // 错误摘要
						// // 演示提取保存详细错误
						for (IOperateInfo errInfo : operationResult.getAllErrorOrValidateInfo()) {
							errMessage += errInfo.getMessage() + ",";
						}
						if (errMessage.length() > 1800) {
							errMessage = errMessage.substring(0, 1800);
						}
						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
								new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
//							rowdy.set("yd_iserror", true);// 修改单据为异常
						}
						SaveServiceHelper.save(dy);
						ABillServiceHelper.exitView(view);
					}
				} else {
					ABillServiceHelper.exitView(view);
				}
			}


		}
		dswl.close();
	}

	public static void khdyzzjy(QFilter filter) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = QFilter.of("yd_checkboxfield_cyhd =?", "1");// 参与合单
		QFilter filter7 = QFilter.of("yd_textfield_sbyy =?", "");// 失败原因为空
		QFilter filter8 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7,filter8,filter9,filter10,filter };
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_checkboxfield_khwdyzz,yd_textfield_sbyy",
				fhmxgl);
		for (DynamicObject row : dy) {
			row.set("yd_checkboxfield_khwdyzz", 1);
			//row.set("yd_textfield_sbyy", "全部为剔除物料");
		}
		SaveServiceHelper.save(dy);// 保存
	}

	public static void jy(QFilter filter,List<String> pcwl,List<String> pcwldirhz,List<String> pcwlpp,List<String> ppfd, String type, QFilter dateFilter) {
		// 清空发货明细不处理,客户,物料,仓库不存在,错误信息
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter2 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter3 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 不处理为否
		QFilter filter4 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter5 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter6 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理

		// 增加条件
		// 如果是直营店
		QFilter spQFilter = null;
		if (type != null) {
			if (DeliveryHelper.isDirectStore(type)) {
				spQFilter = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd); // 只取品牌分单店铺
			}
			// 如果是代理经销商
			else if (DeliveryHelper.isAgentStore(type)) {
				spQFilter = new QFilter("yd_textfield_dpbh", QCP.in, ppfd); // 只取品牌分单店铺
			}
		}else {
			spQFilter = QFilter.of("1=1");
		}

		String selector = "yd_checkboxfield_bcl,yd_checkboxfield_khbcz,yd_checkboxfield_ckbcz,yd_checkboxfield_wlbcz,yd_textfield_sbyy,yd_checkboxfield_cyhd,yd_checkboxfield_khwdyzz,yd_checkboxfield_wlwsh,yd_entryentity.yd_mxwlbcz,yd_dygxcf,yd_entryentity.yd_mxdygxcf"
				+",yd_entryentity.yd_textfield_hpbh,yd_entryentity.yd_decimalfield_sl,yd_entryentity.yd_decimalfield_dj,yd_entryentity.yd_decimalfield_zje,yd_entryentity.yd_ph,yd_entryentity.yd_scrq,yd_entryentity.yd_dqr,yd_entryentity.yd_ispropackage,yd_entryentity.yd_mainmatnum"
				+",yd_ispackage,yd_ispagesplit,yd_entryentity1.yd_src_material,yd_entryentity1.yd_src_num,yd_entryentity1.yd_src_price,yd_entryentity1.yd_src_amount,yd_entryentity1.yd_src_batchno,yd_entryentity1.yd_src_probegindate,yd_entryentity1.yd_src_proenddate,yd_entryentity1.yd_src_ispropackage";

		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", selector,
				new QFilter[] { filter1, dateFilter, filter3,filter4,filter5,filter6,filter, spQFilter});
		//没有符合要求的数据，无需处理
		if(dy.length==0)
		{
			return;
		}
		for (DynamicObject row : dy) {
			//row.set("yd_checkboxfield_bcl", 0);
			row.set("yd_checkboxfield_khbcz", 0);
			row.set("yd_checkboxfield_ckbcz", 0);
			row.set("yd_checkboxfield_wlbcz", 0);
			row.set("yd_dygxcf", 0);
			//row.set("yd_checkboxfield_khwdyzz", 0);
			row.set("yd_checkboxfield_wlwsh", 0);
			row.set("yd_textfield_sbyy", "");
			row.set("yd_checkboxfield_cyhd", true);// 标记为参与合单
			DynamicObjectCollection mx=row.getDynamicObjectCollection("yd_entryentity");
			for (DynamicObject rowmx : mx)
			{
				rowmx.set("yd_mxwlbcz", 0);//明细物料不存在为否
				rowmx.set("yd_mxdygxcf", 0);//明细物料对应关系重复
			}

			// add by laizp，2022-10-10，对组装品的拆分和标识
			// 如果是组装品
			//	|- 将明细分录的数据备份到组装品明细中
			//  |- 将明细分录的数据货品为组装品的数据进行拆分
			if (row.getBoolean("yd_ispackage") && !row.getBoolean("yd_ispagesplit")) {
				setSplitMatInfo(row);
			}
		}
		SaveServiceHelper.save(dy);// 保存
		// 标记出关联客户为不处理的单据
		jymxjoin("yd_khdygx", "yd_textfield_dpbh", "yd_checkboxfield_bcl",filter,ppfd,type, dateFilter);// 标记不处理的客户
		jymxjoinpcck("yd_pcck", "yd_textfield_ck", "yd_pcck",filter,ppfd,type, dateFilter);//排除仓库
		// 查询需要所有排除的物料 包括运费分摊物料
//		QFilter filter5 = QFilter.of("yd_lx =?", "2");//非品牌分单
//		DataSet pcwl = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl", new QFilter[] {filter5},
//				null);
//		List<String> pcwlin = new ArrayList<String>();
//		for (Row row : pcwl) {
//			pcwlin.add(row.getString("pcwl"));
//		}
//		//
//		filter5 = QFilter.of("yd_lx =?", "1");//品牌分单
//		DataSet pcwlpp = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl", new QFilter[] {filter5},
//				null);
//		List<String> pcwlinpp = new ArrayList<String>();
//		for (Row row : pcwlpp) {
//			pcwlinpp.add(row.getString("pcwl"));
//		}
		jymx(pcwl,pcwldirhz,pcwlpp,"yd_khdygx", "yd_textfield_dpbh", "yd_checkboxfield_khbcz",filter,ppfd, type, dateFilter);// 标记不存在的客户的单据
		jymx(pcwl,pcwldirhz,pcwlpp,"yd_ckdygx", "yd_textfield_ck", "yd_checkboxfield_ckbcz",filter,ppfd, type, dateFilter);// 标记不存在的仓库的单据
		//jymx(pcwl,pcwlpp,"yd_wldygx", "yd_entryentity.yd_textfield_hpbh", "yd_checkboxfield_wlbcz",filter,ppfd);// 标记不存在的物料的单据
		jymxwl(pcwl,pcwldirhz,pcwlpp, filter,ppfd, type, dateFilter);// 标记不存在的物料的单据
		jymxhg(pcwl,pcwldirhz,pcwlpp,"yd_wldygx", "yd_entryentity.yd_textfield_hpbh", "yd_checkboxfield_wlwsh", "bd_material",filter,ppfd, type, dateFilter);// 标记物料不合规的单据
		jymxdir(filter,ppfd, type, dateFilter); // 直营店查找不存在直营店仓库维护的发货明细单
	}

	/**
	 * 校验发货明细，将直营店未维护直营店仓库映射表的进行标识仓库不存在
	 * @param
	 * @return
	 * <AUTHOR>
	 * @date 2022-10-8
	 */
	private static void jymxdir(QFilter filter, List<String> ppfd, String type, QFilter dateFilter) {

		// 直营店店铺才需要处理
		if (!DeliveryHelper.isDirectStore(type)) return;

		// 获取直营店一盘货的平台仓库编码集合
		Set<String> platformDirectWarehouse1 = DeliveryHelper.getPlatformDirectWarehouse("1");
		// 获取直营店实体店的平台仓库编码集合
		Set<String> platformDirectWarehouse2 = DeliveryHelper.getPlatformDirectWarehouse("2");
		platformDirectWarehouse1.addAll(platformDirectWarehouse2);

		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
//		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin).and(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse2))
//				.or(new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwldirhz).and(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse1)));
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表
		//非品牌分单单据
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "id, yd_textfield_dpbh ptkh, yd_textfield_ck ptck",
					new QFilter[]{filter1, filter3, dateFilter, filter5, filter, filter7, filter9, filter10}, null);

		// 取出正常汇总的店铺
		// 正常发货汇总
		Set<Object> storeCol = BizHelper.getQueryCol("yd_khdygx", "yd_entryentity.yd_textfield", QFilter.of("billstatus='C' and yd_entryentity.yd_combofield_hzcl in ('1','2')").toArray());

		DataSet warehouseCheckDataSet = yd_fhmxbykh.distinct();
		for (Row row : warehouseCheckDataSet) {
			long billId = row.getLong("id");
			String customerNumber = row.getString("ptkh");
			String warehouseNumber = row.getString("ptck");
			if (storeCol.contains(customerNumber) && !platformDirectWarehouse1.contains(warehouseNumber)) {
				DynamicObject info = BizHelper.getDynamicObjectById("yd_fhmxb", billId, "yd_checkboxfield_ckbcz");
				info.set("yd_checkboxfield_ckbcz", true);
				SaveServiceHelper.save(new DynamicObject[]{info});
			}
		}
	}

	public static void jymxjoin(String bzm, String zdm, String xgbz,QFilter filter,List<String> ppfd,String type, QFilter dateFilter) {
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		QFilter filter2 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", "3");// 客户为不处理
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter7 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");

		// 增加条件
		// 如果是直营店
		QFilter spQFilter = null;
		if (type != null) {
			if (DeliveryHelper.isDirectStore(type)) {
				spQFilter = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd); // 只取品牌分单店铺
			}
			// 如果是代理经销商
			else if (DeliveryHelper.isAgentStore(type)) {
				spQFilter = new QFilter("yd_textfield_dpbh", QCP.in, ppfd); // 只取品牌分单店铺
			}
		}else {
			spQFilter = QFilter.of("1=1");
		}

		// 查询销售出库单号为空且不处理为否且不为今天的发货明细表
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymxjoin", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
				new QFilter[] { filter1, filter3, dateFilter,filter5,filter,filter6,filter7,spQFilter}, null);
		//yd_fhmxbykh.print(true);
		String[] fhmxbzd = new String[] { "pt", zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		//yd_fhmxbykh.print(true);
		DataSet kh = QueryServiceHelper.queryDataSet("jymxjoin", bzm,
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm",
				new QFilter[] { filterSh,filter2 }, null);
		//kh.print(true);
		//kh = kh.join(khdy).on("kh", "kh").select("pt", "bm").finish();
		//kh.print(true);
		DataSet joinDataset = yd_fhmxbykh.join(kh).on(zdm, "bm").on("pt", "pt").select(fhmxbzd).finish();
		//joinDataset.print(true);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			bczkh.add(row.getString(zdm));
		}
		filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
				new QFilter[] { filter1, filter2, filter3, dateFilter });
		for (DynamicObject row : dy) {
			row.set(xgbz, 1);// 修改数据
			// row.set("yd_textfield_sbyy", "不处理");// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
	}
	
	public static void jymxjoinpcck(String bzm, String zdm, String xgbz, QFilter filter, List<String> ppfd, String type, QFilter dateFilter) {
		//jymxjoin("yd_khdygx", "yd_textfield_dpbh", "yd_checkboxfield_bcl",filter);// 标记不处理的客户
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		//QFilter filter2 = QFilter.of("yd_entryentity.yd_combofield_hzcl=?", "3");// 排除仓库为已审核
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter8 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter9 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");

		String[] fhmxbzd = new String[] { zdm };
		//非品牌分单
		DataSet yd_fhmxbykh = null;
		if (DeliveryHelper.isDirectStore(type)) {
			yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymxjoin", "yd_fhmxb", zdm,
					new QFilter[]{filter1, filter3, dateFilter, filter5, filter, filter6, filter8, filter9}, null);
			yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		}
		//品牌分单
		DataSet yd_fhmxbykhpp = null;
		if (DeliveryHelper.isAgentStore(type)) {
			yd_fhmxbykhpp = QueryServiceHelper.queryDataSet("jymxjoin", "yd_fhmxb", zdm,
					new QFilter[] { filter1, filter3, dateFilter,filter5,filter,filter6,filter7,filter9 }, null);
			yd_fhmxbykhpp = yd_fhmxbykhpp.select(fhmxbzd).groupBy(fhmxbzd).finish();
		}
		//yd_fhmxbykh.print(true);
		//yd_fhmxbykh.print(true);
		QFilter filterfpp = QFilter.of("yd_lx =?", "2");//非品牌
		QFilter filterpp = QFilter.of("yd_lx =?", "1");//品牌
		DataSet kh = QueryServiceHelper.queryDataSet("jymxjoin", bzm,
				"yd_entryentity.yd_textfield_bm bm",
				new QFilter[] { filterSh,filterfpp }, null);
		
		DataSet khpp = QueryServiceHelper.queryDataSet("jymxjoin", bzm,
				"yd_entryentity.yd_textfield_bm bm",
				new QFilter[] { filterSh,filterpp }, null);
		//kh.print(true);
		//khpp.print(true);
		//kh = kh.join(khdy).on("kh", "kh").select("pt", "bm").finish();
		//kh.print(true);
		if (yd_fhmxbykh != null) {
			DataSet joinDataset = yd_fhmxbykh.join(kh).on(zdm, "bm").select(fhmxbzd).finish();
			//joinDataset.print(true);
			List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
			for (Row row : joinDataset) {
				bczkh.add(row.getString(zdm));
			}
			QFilter filter2 = new QFilter(zdm, QCP.in, bczkh);
			DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
					new QFilter[]{filter1, filter2, filter3, dateFilter, filter, filter8});
			for (DynamicObject row : dy) {
				row.set(xgbz, 1);// 修改数据
			}
			SaveServiceHelper.save(dy);// 保存
		}

		if (yd_fhmxbykhpp != null) {
			DataSet joinDatasetpp = yd_fhmxbykhpp.join(khpp).on(zdm, "bm").select(fhmxbzd).finish();
			List<String> bczkhpp = new ArrayList<String>();// 记录不存在的客户
			for (Row row : joinDatasetpp) {
				bczkhpp.add(row.getString(zdm));
			}
			QFilter filter2 = new QFilter(zdm, QCP.in, bczkhpp);
			DynamicObject[] dypp = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
					new QFilter[]{filter1, filter2, filter3, dateFilter, filter, filter7});
			for (DynamicObject row : dypp) {
				row.set(xgbz, 1);// 修改数据
			}
			SaveServiceHelper.save(dypp);// 保存
		}
	}

	public static void jymx(List<String> pcwlin, List<String> pcwldirhz, List<String> pcwlinpp, String bzm, String zdm, String xgbz, QFilter filter, List<String> ppfd, String type, QFilter dateFilter) {

		// 排除物料，直营店+直营店结算的排除物料
		List<String> selfPcwlIn = new ArrayList<>();
		selfPcwlIn.addAll(pcwlin);
		selfPcwlIn.addAll(pcwldirhz);

		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, selfPcwlIn);// 去除排除物料非品牌分单
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter8 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlinpp);// 去除排除物料品牌分单
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表
		//非品牌分单单据
		DataSet yd_fhmxbykh = null;
		DataSet yd_fhmxbykhpp = null;
		if (DeliveryHelper.isDirectStore(type)) {
			yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
					new QFilter[]{filter1, filter2, filter3, dateFilter, filter5, filter, filter7, filter9, filter10}, null);
		}
		//yd_fhmxbykh.print(true);
		//品牌分单单据
		if (DeliveryHelper.isAgentStore(type)) {
			yd_fhmxbykhpp = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
					new QFilter[]{filter1, filter3, dateFilter, filter5, filter, filter6, filter8, filter9, filter10}, null);
		}
		//yd_fhmxbykhpp.print(true);
		// 合并内容
		if (yd_fhmxbykh != null && yd_fhmxbykhpp != null) {
			yd_fhmxbykh=yd_fhmxbykh.union(yd_fhmxbykhpp);
		}else if (yd_fhmxbykh == null && yd_fhmxbykhpp != null) {
			yd_fhmxbykh = yd_fhmxbykhpp;
		}
		//yd_fhmxbykh.print(true);
		String[] fhmxbzd = new String[] { "pt", zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		DataSet kh = QueryServiceHelper.queryDataSet("jymx", bzm, "yd_combofield_pt pt,yd_entryentity.yd_textfield bm",
				new QFilter[] { filterSh }, null);
		DataSet joinDataset = yd_fhmxbykh.leftJoin(kh).on(zdm, "bm").on("pt", "pt")
				.select(fhmxbzd, new String[] { "bm" }).finish();
		//joinDataset.print(true);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			String number = row.getString("bm");
			if (number == null) {
				bczkh.add(row.getString(zdm));
			}
		}
		filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
				new QFilter[] { filter1, filter2, filter3, dateFilter,filter5,filter9,filter });
		String bccw = "客户不存在";
		if ("yd_ckdygx".equals(bzm)) {
			bccw = "仓库不存在";
		} else if ("yd_wldygx".equals(bzm)) {
			bccw = "物料不存在";
		}
		for (DynamicObject row : dy) {
			String cwxx = row.getString("yd_textfield_sbyy");
			if (cwxx != "") {
				cwxx += "," + bccw;
			} else {
				cwxx = bccw;
			}
			row.set(xgbz, 1);// 修改数据
			row.set("yd_textfield_sbyy", cwxx);// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
	}
	
	public static void jymxwl(List<String> pcwlin, List<String> pcwldirhz, List<String> pcwlinpp, QFilter filter, List<String> ppfd, String type, QFilter dateFilter) {

		// 获取直营店一盘货的平台仓库编码集合
		Set<String> platformDirectWarehouse1 = DeliveryHelper.getPlatformDirectWarehouse("1");
		// 获取直营店实体店的平台仓库编码集合
		Set<String> platformDirectWarehouse2 = DeliveryHelper.getPlatformDirectWarehouse("2");

		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
//		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料非品牌分单——直营店实体店
		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin).and(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse2))
				.or(new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwldirhz).and(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse1)));
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter8 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlinpp);// 去除排除物料品牌分单
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("helpcode !=?", "");// 助记码不为空
		QFilter filter11 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表
		QFilter[] fppfdgl={ filter1, filter2,filter3, dateFilter,filter5,filter,filter7,filter9,filter11 };//非品牌分单过滤
		QFilter[] ppfdgl={ filter1,filter3, dateFilter,filter5,filter,filter6,filter8,filter9,filter11 };//品牌分单过滤
		String[] fhmxbzd = new String[] { "pt", "yd_entryentity.yd_textfield_hpbh" };

		DataSet yd_fhmxbykh = null;
		DataSet yd_fhmxbykhpp = null;
		//非品牌分单单据
		if (DeliveryHelper.isDirectStore(type)) {
			yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt,yd_entryentity.yd_textfield_hpbh",
					fppfdgl, null).select(fhmxbzd).groupBy(fhmxbzd).finish();
		}
		//品牌分单单据
		if (DeliveryHelper.isAgentStore(type)) {
			yd_fhmxbykhpp = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt,yd_entryentity.yd_textfield_hpbh",
					ppfdgl, null).select(fhmxbzd).groupBy(fhmxbzd).finish();
		}
//		yd_fhmxbykh=yd_fhmxbykh.union(yd_fhmxbykhpp);
//		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		DataSet wldy = QueryServiceHelper.queryDataSet("jymx", "yd_wldygx", "yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield.number wl,yd_combofield_pt pt",
				new QFilter[] { filterSh }, null);
		//wldy.copy().print(false);
		//过滤e3物料对应关系物料
		List<String> wldy01 = new ArrayList<String>();
		for (Row row : wldy.copy().filter("pt='1'").select("bm")) {
			wldy01.add(row.getString("bm"));
		}
		//过滤旺店通物料对应关系物料
		List<String> wldy02 = new ArrayList<String>();
		for (Row row : wldy.copy().filter("pt='2'").select("bm")) {
			wldy02.add(row.getString("bm"));
		}
		QFilter filterwldye3 = new QFilter("helpcode", QCP.not_in, wldy01);//不包含对应表商品e3
		QFilter filterwldywdt = new QFilter("number", QCP.not_in, wldy02);//不包含对应表商品旺店通
		filterSh = QFilter.of("status=?", "C");
		// 查询已审核的物料编码
		DataSet wlbm = QueryServiceHelper.queryDataSet("jymx", "bd_material", "number bm,number wl", new QFilter[] { filterSh,filterwldywdt }, null);
		wlbm=wlbm.addField("'2'", "pt");
		// 查询已审核并且不为空的的物料助记码
		DataSet wlzjm = QueryServiceHelper.queryDataSet("jymx", "bd_material", "helpcode bm,number wl", new QFilter[] { filterSh,filter10,filterwldye3 }, null);
		wlzjm=wlzjm.addField("'1'", "pt");
		wldy=wldy.union(wlbm).union(wlzjm);
		// 查询物料库存信息为已审核的物料
		DataSet wlkcbm = QueryServiceHelper.queryDataSet("jymx", "bd_materialinventoryinfo", "masterid.number wl", new QFilter[] { filterSh }, null).distinct();
		wldy=wldy.join(wlkcbm, JoinType.INNER).on("wl", "wl").select(new String[] {"bm","wl","pt"}, new String[]{}).finish();
		// 统计重复的物料配置
		wldy = wldy.groupBy(new String[]{"bm","pt"}).count("count").finish();
		//wldy.copy().print(false);
		DataSet cfwl=wldy.copy().filter("count>1");
		//查询e3店铺
		DataSet pt01=QueryServiceHelper.queryDataSet("jymx", "yd_khdygx", "yd_entryentity.yd_textfield", new QFilter[] { QFilter.of("yd_combofield_pt=?", "1") }, null);
		//查询旺店通店铺
		DataSet pt02=QueryServiceHelper.queryDataSet("jymx", "yd_khdygx", "yd_entryentity.yd_textfield", new QFilter[] { QFilter.of("yd_combofield_pt=?", "2") }, null);
		//查询吉客云店铺
		DataSet pt03=QueryServiceHelper.queryDataSet("jymx", "yd_khdygx", "yd_entryentity.yd_textfield", new QFilter[] { QFilter.of("yd_combofield_pt=?", "3") }, null);
		//过滤e3对应关系重复物料
		List<String> cfwl01 = new ArrayList<String>();
		for (Row row : cfwl.copy().filter("pt='1'").select("bm")) {
				cfwl01.add(row.getString("bm"));
		}
		//过滤旺店通对应关系重复物料
		List<String> cfwl02 = new ArrayList<String>();
		for (Row row : cfwl.copy().filter("pt='2'").select("bm")) {
				cfwl02.add(row.getString("bm"));
		}
		//过滤吉客云对应关系重复物料
		List<String> cfwl03 = new ArrayList<String>();
		for (Row row : cfwl.copy().filter("pt='3'").select("bm")) {
				cfwl03.add(row.getString("bm"));
		}
		//过滤e3店铺
		List<String> ptdp01 = new ArrayList<String>();
		for (Row row : pt01) {
			ptdp01.add(row.getString("yd_entryentity.yd_textfield"));
		}
		//过滤旺店通店铺
		List<String> ptdp02 = new ArrayList<String>();
		for (Row row : pt02) {
			ptdp02.add(row.getString("yd_entryentity.yd_textfield"));
		}
		//过滤吉客云店铺
		List<String> ptdp03 = new ArrayList<String>();
		for (Row row : pt03) {
			ptdp03.add(row.getString("yd_entryentity.yd_textfield"));
		}
		QFilter[] Filters1={ filter1, filter3, dateFilter,filter,filter9, new QFilter("yd_textfield_dpbh", QCP.in, ptdp01)};
		gxfhmx(Filters1,"物料对应关系重复","yd_dygxcf","yd_mxdygxcf",cfwl01);
		QFilter[] Filters2={ filter1, filter3, dateFilter,filter,filter9, new QFilter("yd_textfield_dpbh", QCP.in, ptdp02)};
		gxfhmx(Filters2,"物料对应关系重复","yd_dygxcf","yd_mxdygxcf",cfwl02);
		QFilter[] Filters3={ filter1, filter3, dateFilter,filter,filter9, new QFilter("yd_textfield_dpbh", QCP.in, ptdp03)};
		gxfhmx(Filters3,"物料对应关系重复","yd_dygxcf","yd_mxdygxcf",cfwl03);
		//非品牌分物料不存在处理
		if (yd_fhmxbykh != null) {
			DataSet dsfpp = yd_fhmxbykh.leftJoin(wldy.copy()).on("yd_entryentity.yd_textfield_hpbh", "bm").on("pt", "pt")
					.select(fhmxbzd, new String[]{"bm"}).finish();
			List<String> bczfpp = new ArrayList<String>();
			for (Row row : dsfpp) {
				String number = row.getString("bm");
				if (number == null) {
					bczfpp.add(row.getString("yd_entryentity.yd_textfield_hpbh"));
				}
			}
			QFilter filterbczfpp = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.in, bczfpp);
			QFilter[] Filters4 = {filter1, filter2, filter3, dateFilter, filter, filter7, filter9, filterbczfpp};
			gxfhmx(Filters4, "物料不存在", "yd_checkboxfield_wlbcz", "yd_mxwlbcz", bczfpp);
		}
		//品牌分单物料不存在处理
		if (yd_fhmxbykhpp != null) {
			DataSet dspp = yd_fhmxbykhpp.leftJoin(wldy).on("yd_entryentity.yd_textfield_hpbh", "bm").on("pt", "pt")
					.select(fhmxbzd, new String[]{"bm"}).finish();
			List<String> bczpp = new ArrayList<String>();
			for (Row row : dspp) {
				String number = row.getString("bm");
				if (number == null) {
					bczpp.add(row.getString("yd_entryentity.yd_textfield_hpbh"));
				}
			}
			QFilter filterbczpp = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.in, bczpp);
			QFilter[] Filters5 = {filter1, filter3, dateFilter, filter, filter6, filter9, filterbczpp};
			gxfhmx(Filters5, "物料不存在", "yd_checkboxfield_wlbcz", "yd_mxwlbcz", bczpp);
		}
	}
	//更新发货明细物料相关 过滤条件,错误提示,更新字段,更新字段明细,对比物料集合
	public static void gxfhmx(QFilter[] Filters,String bccw,String gxzd,String gxzdmx,List<String> bczkh)
	{
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_entryentity.yd_mxwlbcz,yd_entryentity.yd_textfield_hpbh,yd_textfield_sbyy,yd_checkboxfield_wlbcz,yd_dygxcf,yd_entryentity.yd_mxdygxcf",
				Filters);
		//String bccw = "对应关系重复";
		for (DynamicObject row : dy) {
			DynamicObjectCollection mx=row.getDynamicObjectCollection("yd_entryentity");
			for (DynamicObject rowmx : mx)
			{
			String wl=rowmx.getString("yd_textfield_hpbh");
			if(bczkh.contains(wl))
			{
			String cwxx = row.getString("yd_textfield_sbyy");
			if (cwxx != "") {
				cwxx += "," + bccw;
			} else {
				cwxx = bccw;
			}
			rowmx.set(gxzdmx, 1);// 修改数据
			row.set(gxzd, 1);// 修改数据
			row.set("yd_textfield_sbyy", cwxx);// 修改数据
			}
			}
		}
		SaveServiceHelper.save(dy);// 保存
	}

	public static void jymxhg(List<String> pcwlin, List<String> pcwldirhz, List<String> pcwlinpp, String bzm, String zdm, String xgbz, String jczl, QFilter filter, List<String> ppfd, String type, QFilter dateFilter) {

		// 获取直营店一盘货的平台仓库编码集合
		Set<String> platformDirectWarehouse1 = DeliveryHelper.getPlatformDirectWarehouse("1");
		// 获取直营店实体店的平台仓库编码集合
		Set<String> platformDirectWarehouse2 = DeliveryHelper.getPlatformDirectWarehouse("2");

		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
//		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin).and(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse2))
				.or(new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwldirhz).and(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse1)));
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
//		Date dt = new Date();
//		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
//		QFilter filter4 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter5 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter6 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter7 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter8 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlinpp);// 去除排除物料品牌分单
		QFilter filter9 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter10 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterSh = QFilter.of("billstatus=?", "C");
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表
		//非品牌分单单据
		DataSet yd_fhmxbykh = null;
		if (DeliveryHelper.isDirectStore(type)) {
			yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
					new QFilter[]{filter1, filter2, filter3, dateFilter, filter5, filter, filter7, filter9, filter10}, null);
		}
		//品牌分单单据
		DataSet yd_fhmxbykhpp = null;
		if (DeliveryHelper.isAgentStore(type)) {
			yd_fhmxbykhpp = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
					new QFilter[]{filter1, filter3, dateFilter, filter5, filter, filter6, filter8, filter9, filter10}, null);
		}
		if (yd_fhmxbykh != null && yd_fhmxbykhpp != null) {
			yd_fhmxbykh = yd_fhmxbykh.union(yd_fhmxbykhpp);
		}else if (yd_fhmxbykh == null && yd_fhmxbykhpp != null) {
			yd_fhmxbykh = yd_fhmxbykhpp;
		}
		String[] fhmxbzd = new String[] { "pt", zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		DataSet kh = QueryServiceHelper.queryDataSet("jymx", bzm,
				"yd_combofield_pt pt,yd_entryentity.yd_textfield bm,yd_entryentity.yd_basedatafield id",
				new QFilter[] { filterSh }, null);
		filterSh = QFilter.of("status=?", "C");
		// 查询已审核的物料编码
		DataSet wl = QueryServiceHelper.queryDataSet("jymx", jczl, "id wlid", new QFilter[] { filterSh }, null);
		// 查询已审核的物料库存信息
		DataSet wlkc = QueryServiceHelper.queryDataSet("jymx", "bd_materialinventoryinfo", "masterid id",
				new QFilter[] { filterSh }, null);
		// 查询已审核的物料销售信息
		DataSet wlxs = QueryServiceHelper.queryDataSet("jymx", "bd_materialsalinfo", "masterid id",
				new QFilter[] { filterSh }, null);
		// join出合规物料
		DataSet hgwl = wl.join(wlkc).on("wlid", "id").select(new String[] { "wlid" }).finish().join(wlxs)
				.on("wlid", "id").select(new String[] { "wlid" }).finish();
		//hgwl.print(true);
		// join出有对应物料编码的平台物料并于合规物料进行判断
		DataSet joinDataset = yd_fhmxbykh.join(kh).on(zdm, "bm").on("pt", "pt")
				.select(fhmxbzd, new String[] { "bm", "id" }).finish().leftJoin(hgwl).on("id", "wlid")
				.select(fhmxbzd, new String[] { "bm", "wlid" }).finish();
		//joinDataset.print(true);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			String number = row.getString("wlid");
			if (number == null) {
				bczkh.add(row.getString(zdm));
			}
		}
		filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz + ",yd_textfield_sbyy",
				new QFilter[] { filter1, filter2, filter3, dateFilter,filter });
		String bccw = "物料未审核或物料库存,销售信息未审核";
		// if ("yd_ckdygx".equals(bzm)) {
		// bccw = "仓库不存在";
		// } else if ("yd_wldygx".equals(bzm)) {
		// bccw = "物料不存在";
		// }
		for (DynamicObject row : dy) {
			String cwxx = row.getString("yd_textfield_sbyy");
			if (cwxx != "") {
				cwxx += "," + bccw;
			} else {
				cwxx = bccw;
			}
			row.set(xgbz, 1);// 修改数据
			row.set("yd_textfield_sbyy", cwxx);// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
	}

	/**
	 * 校验和设置拆分物料信息
	 * @param info 发货明细表
	 * <AUTHOR>
	 * @date 2022-10-10
	 */
	private static void setSplitMatInfo(DynamicObject info) {
		DynamicObjectCollection entryCol = info.getDynamicObjectCollection("yd_entryentity"); // 明细分录
		DynamicObjectCollection pageEntryCol = info.getDynamicObjectCollection("yd_entryentity1"); // 拆分明细分录

		// 判断组装分录是否存在数据，如果存在数据，则替换明细的数据，重新拆分
		if (pageEntryCol.size() > 0) {
			entryCol.clear();
			// 重置明细分录数据
			for (DynamicObject pageEntryInfo : pageEntryCol) {
				DynamicObject entryInfo = entryCol.addNew();
				DeliveryHelper.copyInfo(pageEntryInfo, entryInfo, new String[][]{
						{"yd_src_material", "yd_textfield_hpbh"} // 货品编码
						, {"yd_src_num", "yd_decimalfield_sl"} // 数量
						, {"yd_src_price", "yd_decimalfield_dj"} // 单价
						, {"yd_src_amount", "yd_decimalfield_zje"} // 总金额
						, {"yd_src_batchno", "yd_ph"} // 批号
						, {"yd_src_probegindate", "yd_scrq"} // 生产日期
						, {"yd_src_proenddate", "yd_dqr"} // 到期日
						, {"yd_src_ispropackage", "yd_ispropackage"} // 是否组装品
				});
			}
		}

		boolean isStop = false; // 是否终止拆分
		// 校验明细是否可以拆分，如果可以拆分才处理后续
		Map<Integer, List<Object[]>> linkSplitMatInfoMap = new LinkedHashMap<>(); // 如果存在拆分的信息会放入到这里
		for (int index = entryCol.size()-1; index >= 0; index--) {
			DynamicObject entryInfo = entryCol.get(index); // 获取每条明细
			// 遍历查询是否为组装品
			if (entryInfo.getBoolean("yd_ispropackage")) {
				String mainMatNum = entryInfo.getString("yd_textfield_hpbh"); // 获取货品编码
				// 通过货品编码查找主商品拆分表，拆分成对应的明细数据
				List<Object[]> splitMatInfo = DeliveryHelper.getSplitMatInfo(mainMatNum);
				if (splitMatInfo == null) {
					isStop = true;
					break;
				}
				// 将对应行的拆分信息进行缓存
				linkSplitMatInfoMap.put(index, splitMatInfo);
			}
		}
		// 如果是终止拆分，则不需要将明细的数据移到组装明细中、已经标识已拆分的标识信息
		if (!isStop) {
			splitMatToDetailEntry(info, linkSplitMatInfoMap);
		}
	}

	/**
	 * 将组装品的进行进行拆分
	 * @param info 发货明细单
	 * @param linkSplitMatInfoMap 已经处理的待拆分的明细行和对应需拆分的单品信息
	 * <AUTHOR>
	 * @date 2022-10-10
	 */
	private static void splitMatToDetailEntry(DynamicObject info, Map<Integer, List<Object[]>> linkSplitMatInfoMap) {
		DynamicObjectCollection entryCol = info.getDynamicObjectCollection("yd_entryentity"); // 明细分录
		DynamicObjectCollection pageEntryCol = info.getDynamicObjectCollection("yd_entryentity1"); // 拆分明细分录
		// 将明细分录的数据备份到组装品明细
		if (pageEntryCol.size() == 0) {
			for (DynamicObject entryInfo : entryCol) {
				DynamicObject pageEntryInfo = pageEntryCol.addNew();
				DeliveryHelper.copyInfo(entryInfo, pageEntryInfo, new String[][]{
						{"yd_textfield_hpbh", "yd_src_material"} // 货品编码
						, {"yd_decimalfield_sl", "yd_src_num"} // 数量
						, {"yd_decimalfield_dj", "yd_src_price"} // 单价
						, {"yd_decimalfield_zje", "yd_src_amount"} // 总金额
						, {"yd_ph", "yd_src_batchno"} // 批号
						, {"yd_scrq", "yd_src_probegindate"} // 生产日期
						, {"yd_dqr", "yd_src_proenddate"} // 到期日
						, {"yd_ispropackage", "yd_src_ispropackage"} // 是否组装品
				});
			}
		}
		// 将分录明细的数据，货品为组装品进行拆分
		for (Integer mIndex : linkSplitMatInfoMap.keySet()) {
			// 拆分物料编码、数量、拆分比例、组装品物料编码
			List<Object[]> splitMatInfos = linkSplitMatInfoMap.get(mIndex);
			DynamicObject srcEntryInfo = entryCol.get(mIndex); // 需要拆分的明细分录
			// 在待拆分的组装品，第一行直接替换，存在其他拆分的明细，在当前分录下创建新的分录
			BigDecimal matCount = srcEntryInfo.getBigDecimal("yd_decimalfield_sl"); // 明细数量
			BigDecimal matAmount = srcEntryInfo.getBigDecimal("yd_decimalfield_zje"); // 明细总额

			DynamicObject newEntryInfo = srcEntryInfo;

			// 遍历每个待处理的组装品疯了
			BigDecimal totalEntryAmount = BigDecimal.ZERO;
			for (int index = 0; index < splitMatInfos.size(); index++) {
				Object[] splitMatInfo = splitMatInfos.get(index);
				// 计算最终的数量和单品总额
				BigDecimal newMatCount = matCount.multiply((BigDecimal) splitMatInfo[1]); // 明细的数量 * 拆分单品的数量
				BigDecimal newMatAmount;
				if (index == splitMatInfos.size()-1) {
					newMatAmount = matAmount.subtract(totalEntryAmount); // 最后一个金额用差额金额统计
				}else {
					newMatAmount = matAmount.multiply((BigDecimal) splitMatInfo[2]).divide(new BigDecimal("100"), 2, 4); // 总金额 * 比例
					totalEntryAmount = totalEntryAmount.add(newMatAmount);
				}
				if (index != 0) {
					// 新建一个分录明细
					newEntryInfo = (DynamicObject) entryCol.getDynamicObjectType().createInstance();
					// 在需要拆分的组装品后添加拆分的单品
					entryCol.add(mIndex+index, newEntryInfo);
				}
				// 对明细重新赋值吹
				// 拆分后，生产日期和到期日、批次置空
				newEntryInfo.set("yd_textfield_hpbh", splitMatInfo[0]); // 货品明细
				newEntryInfo.set("yd_decimalfield_sl", newMatCount); // 单品数量
				newEntryInfo.set("yd_decimalfield_dj", newMatAmount.divide(newMatCount, 4, 4)); // 单品单价
				newEntryInfo.set("yd_decimalfield_zje", newMatAmount); // 单品总额
				newEntryInfo.set("yd_ph", ""); // 批号
				newEntryInfo.set("yd_scrq", null); // 生产日期
				newEntryInfo.set("yd_dqr", null); // 到期日
				newEntryInfo.set("yd_ispropackage", false); // 是否组装品
				newEntryInfo.set("yd_mainmatnum", splitMatInfo[3]); // 组装品物料编码
			}
		}

		// 更新已经拆分
		info.set("yd_ispagesplit", true);
	}
}
