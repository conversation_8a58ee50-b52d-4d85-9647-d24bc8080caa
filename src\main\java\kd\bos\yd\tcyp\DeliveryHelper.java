package kd.bos.yd.tcyp;

import kd.bos.algo.DataSet;
import kd.bos.algo.JoinType;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.yd.tcyp.utils.BizHelper;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 发货出库辅助类
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-7-15
 */
public class DeliveryHelper {

    /**
     * 获取排除物料的集合
     * @param diffMaterial 是否运费补差物料（1：是运费补差物料，2：不是运费补差物料）
     * @param type 类型（1：一盘货，2：直营店，3：直营店一盘货）
     * @return 排除物料的类型
     * <AUTHOR>
     * @date 2022-7-15
     */
    public static List<String> getExcludeMaterial(int diffMaterial, String type) {
        QFilter qFilter = QFilter.of("yd_entryentity.yd_checkboxfield_bc =? and yd_lx =?", diffMaterial, type);
        DataSet pcwlDataSet = null;
        try {
            pcwlDataSet = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl", qFilter.toArray(), null);
            List<String> pcwlList = new ArrayList<>();
            for (Row row : pcwlDataSet) {
                pcwlList.add(row.getString("pcwl"));
            }
            return pcwlList;
        }finally {
            if (pcwlDataSet != null) pcwlDataSet.close();
        }
    }

    /**
     * 查询按照品牌分单店铺
     * @param
     * @return
     * <AUTHOR>
     * @date 2022-7-15
     */
    public static List<String> getBrandStore() {
        // 查询按照品牌分单店铺
        DataSet ppfdcx = null;
        try {
            ppfdcx = QueryServiceHelper.queryDataSet("jy", "yd_khdygx", "yd_entryentity.yd_textfield ppfd",
                    QFilter.of("yd_entryentity.yd_ppfd =?", 1).toArray(), null);
            List<String> ppfd = new ArrayList<>();
            for (Row row : ppfdcx) {
                ppfd.add(row.getString("ppfd"));
            }
            return ppfd;
        }finally {
            if (ppfdcx != null) ppfdcx.close();
        }
    }

    /**
     * 是否直营店
     * @param type 类型（1为直营店，2为一盘货）
     * @return 是否为直营店（包含直营店结算）
     * <AUTHOR>
     * @date 2022-8-12
     */
    public static boolean isDirectStore(String type) {
        return type == null || "1".equals(type);
    }

    /**
     * 是否经销商一盘货
     * @param type 类型（1为直营店，2为一盘货）
     * @return 是否为直营店（包含直营店结算）
     * <AUTHOR>
     * @date 2022-8-12
     */
    public static boolean isAgentStore(String type) {
        return type == null || "2".equals(type);
    }

    /**
     * 获取平台直营店仓库数据（第三方系统的仓库编码数据）
     * @param type 仓库的属性类型（1：共享仓；2：实体仓）
     * @return 第三方系统（E3）的仓库编码集合
     * <AUTHOR>
     * @date 2022-7-14
     */
    public static Set<String> getPlatformDirectWarehouse(String type) {
        DataSet dataSet = null;
        try {
            Set<String> warehouseSet = new HashSet<>();
            String runLocate = BizHelper.getRunLocate();
            // 获取直营店仓库中，已经审批的仓库信息
            dataSet = BizHelper.getQueryDataSet(runLocate, "yd_directwarehouse", "yd_platform platform, yd_entryentity.yd_warehouse.number as warehouseNumber", QFilter.of("status = 'C' and yd_type=?", type).toArray());
            // 将仓库对应关系和直营店仓库数据进行关联，获取平台仓库的编码
            DataSet wDataSet = BizHelper.getQueryDataSet(runLocate, "yd_ckdygx",
                    "yd_combofield_pt platform,yd_entryentity.yd_textfield otherCkNumber,yd_entryentity.yd_basedatafield.number warehouseNumber,yd_entryentity.yd_basedatafield.createorg warehouseOrgNumber",
                    QFilter.of("billstatus='C'").toArray());
            dataSet = dataSet.join(wDataSet, JoinType.INNER).on("platform", "platform").on("warehouseNumber", "warehouseNumber")
                    .select(new String[]{}, new String[]{"otherCkNumber"}).finish();

            for (Row row : dataSet) {
                warehouseSet.add(row.getString("otherCkNumber"));
            }
            return warehouseSet;
        }finally {
            if (dataSet != null) {
                dataSet.close();
            }
        }
    }

    /**
     * 将一个对象中的属性拷贝到另一个分录中
     * @param srcInfo 源对象
     * @param destInfo 目标对象
     * @param fields 属性对照表
     * <AUTHOR>
     * @date 2022-10-10
     */
    public static void copyInfo(DynamicObject srcInfo, DynamicObject destInfo, String[][] fields) {
        if (fields == null || fields.length == 0) return;
        for (String[] fieldArr : fields) {
            String srcField = fieldArr[0];
            String destField = fieldArr.length < 2 ? srcField : fieldArr[1];
            destInfo.set(destField, srcInfo.get(srcField));
        }
    }

    /**
     * 获取主商品拆分后的物料信息
     * @param mainMatNum 主商品编码
     * @return 拆分商品信息
     * <AUTHOR>
     * @date 2022-10-10
     */
    public static List<Object[]> getSplitMatInfo(String mainMatNum) {
        QFilter qFilter = QFilter.of("billstatus='C' and entryentity.yd_mainmatnum=?", mainMatNum);
        DataSet matCobinationDataSet = null;
        try {
            String runLocate = BizHelper.getRunLocate();
            // 分录ID，物料助记码、数量、比例
            String selector = "id, entryentity.id as entryId, entryentity.yd_subentryentity.yd_material.helpcode as matNum, entryentity.yd_subentryentity.yd_qty as qty, entryentity.yd_subentryentity.yd_rate as rate";
            matCobinationDataSet = BizHelper.getQueryDataSet(runLocate, "yd_matcombination", selector, qFilter.toArray());
            if (!matCobinationDataSet.hasNext()) return null;
            String tempEntryId = null;
            List<Object[]> spMatInfoList = new ArrayList<>();
            for (Row matCobinationRow : matCobinationDataSet) {
                String entryId = matCobinationRow.getString("entryId");
                if (tempEntryId == null) tempEntryId = entryId;
                // 避免存在多个分录/多个单中录入了相同的拆分信息，只读符合条件的一条分录信息
                if (!entryId.equals(tempEntryId)) continue;
                // 将拆分商品的编码/数量/比例返回
                BigDecimal rate = matCobinationRow.getBigDecimal("rate");
//                if (rate.compareTo(BigDecimalUtil.ZERO) != 0) {
//                    spMatInfoList.add(new Object[] {matCobinationRow.getString("matNum"),matCobinationRow.getBigDecimal("qty"),matCobinationRow.getBigDecimal("rate")});
//                }
                spMatInfoList.add(new Object[] {matCobinationRow.getString("matNum"),matCobinationRow.getBigDecimal("qty"),matCobinationRow.getBigDecimal("rate"), mainMatNum});
            }
            return spMatInfoList;
        }finally {
            if (matCobinationDataSet != null) {
                matCobinationDataSet.close();
            }
        }
    }

    /**
     * 获取客户对应关系表中已维护为需要分摊运费的客户
     * @author: hst
     * @createDate: 2023/3/28
     * @return
     */
    public static List<String> getSharedFareClients() {
        Set<String> clients = new HashSet<>();
        String runLocate = BizHelper.getRunLocate();
        // 平台为E3
        QFilter ptFilter = new QFilter("yd_combofield_pt",QFilter.equals,"1");
        // 分摊运费
        QFilter shareFilter = new QFilter("yd_entryentity.yd_isshare",QFilter.equals,"1");
        DataSet client = QueryServiceHelper.queryDataSet(runLocate,"yd_khdygx","yd_entryentity.yd_basedatafield",
                new QFilter[]{ptFilter,shareFilter},null).groupBy(new String[]{"yd_entryentity.yd_basedatafield"}).finish();
        for (Row row : client) {
            String clientId = row.getString("yd_entryentity.yd_basedatafield");
            if (StringUtils.isNotBlank(clientId)) {
                clients.add(clientId);
            }
        }
        return Arrays.asList(clients.toArray(new String[clients.size()]));
    }
}
