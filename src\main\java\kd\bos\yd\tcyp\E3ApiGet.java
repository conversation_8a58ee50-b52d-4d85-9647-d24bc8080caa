package kd.bos.yd.tcyp;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import json.JSONArray;
import json.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDException;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.log.api.AppLogInfo;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class E3ApiGet extends AbstractTask {

	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {
		// TODO Auto-generated method stub
		// 1、获取日志微服务接口
		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取e3出库单查漏启动开始", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
		//查询e3天数
		DynamicObject dy = BusinessDataServiceHelper.loadSingle("yd_khdygx","yd_integerfield_ts",new QFilter[] {QFilter.of("yd_combofield_pt =?", "1")});
		int ts = 7;
		int e3ts=dy.getInt("yd_integerfield_ts");
//		int e3ts = 1;
		if(e3ts>0)
		{
			ts=e3ts;
		}
		int sj = 23;
		Date dt = new Date();
		Calendar rightNow = Calendar.getInstance();
		for (int t = ts; t >0; t--) {
			rightNow.setTime(dt);
			rightNow.add(Calendar.DAY_OF_MONTH, -t);
			for (int s = 0; s <= sj; s++) {
				Date dtks = rightNow.getTime();
				rightNow.add(Calendar.HOUR, 1);
				Date dtjs = rightNow.getTime();
				String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtks);
				String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtjs);
				E3Post.E3OrderListGet(ks,js,"");
				ApiFf.E3ReturnListGet(ks, js);
//				E3Post.E3OrderListGetNew(ks,js,"");
//				ApiFf.E3ReturnListGetNew(ks, js);
			}
		}
		logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取e3出库单查漏启动结束", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
	}

}
