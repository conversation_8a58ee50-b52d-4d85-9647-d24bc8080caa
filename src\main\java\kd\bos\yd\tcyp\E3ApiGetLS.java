package kd.bos.yd.tcyp;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import json.JSONArray;
import json.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDException;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.log.api.AppLogInfo;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class E3ApiGetLS extends AbstractTask {

	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {

		int ts = 24;
		int sj = 23;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String glrq="2022-02-25 00:00:00";
		Date dt = new Date();
		try {
			dt = sdf.parse(glrq);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		Calendar rightNow = Calendar.getInstance();
		for (int t = ts; t >0; t--) {
			rightNow.setTime(dt);
			rightNow.add(Calendar.DAY_OF_MONTH, -t);
			for (int s = 0; s <= sj; s++) {
				Date dtks = rightNow.getTime();
				rightNow.add(Calendar.HOUR, 1);
				Date dtjs = rightNow.getTime();
				String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtks);
				String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtjs);
				E3Post.E3OrderListGet(ks,js,"205");
				E3Post.E3OrderListGet(ks,js,"408");
				 //ApiFf.E3ReturnListGet(ks, js);
			}
		}
	}

}
