package kd.bos.yd.tcyp;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import json.JSONArray;
import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.exception.KDException;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.log.api.AppLogInfo;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class E3FhEas extends AbstractTask {

	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {
		// TODO Auto-generated method stub
		// 1、获取日志微服务接口
		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "修改发货明细eas状态开始", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
		//查询七天内成功同步到eas的出库单
		DynamicObject dyts = BusinessDataServiceHelper.loadSingle("yd_khdygx","yd_easqrts",new QFilter[] {QFilter.of("yd_combofield_pt =?", "1")});
		int ts = 2;
		int e3ts=dyts.getInt("yd_easqrts");
		if(e3ts>0)
		{
			ts=e3ts;
		}
		// 查询对应出库单号的汇总销售出库信息
		Date dt = new Date();
		Calendar rightNow = Calendar.getInstance();
		rightNow.setTime(dt);
		Date dtjs = rightNow.getTime();
		rightNow.add(Calendar.DAY_OF_MONTH, -ts);
		Date dtks = rightNow.getTime();
		String ks = (String) new SimpleDateFormat("yyyy-MM-dd").format(dtks);
		String js = (String) new SimpleDateFormat("yyyy-MM-dd").format(dtjs);
		QFilter filterksrq = QFilter.of("biztime >= ?", ks);// 开始日期
		QFilter filterjsrq = QFilter.of("biztime <= ?", js);// 结束日期
		gxeaszt(ks,js);
		logInfo1 = OperationLogUtil.buildLogInfo("保存", "修改发货明细eas状态结束", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
	}
	public static void gxeaszt(String ks,String js)
	{
		QFilter filterksrq = QFilter.of("biztime >= ?", ks);// 开始日期
		QFilter filterjsrq = QFilter.of("biztime <= ?", js);// 结束日期
		//销售出库
		QFilter filtereasck = QFilter.of("yd_checkboxsf = ?", "1");// 同步成功
		QFilter filtereassbck = QFilter.of("yd_checkboxsf = ?", "0");// 同步失败
		//其他出库
		QFilter filtereas = QFilter.of("yd_checkboxfield03 = ?", "1");// 同步成功
		QFilter filtereassb = QFilter.of("yd_checkboxfield03 = ?", "0");// 同步失败
		QFilter[] Filterck = new QFilter[] { filterksrq,filterjsrq,filtereasck };
		//处理成功
		//查询对应出库单
		DataSet xsck = QueryServiceHelper
				.queryDataSet("E3FhEas", "im_saloutbill",
						"yd_entryentity.yd_textfield_fhmxdh dh", Filterck, null);
		// 查询对应出库单号的汇总其他出库信息
		Filterck = new QFilter[] { filterksrq,filterjsrq,filtereas };
		DataSet qtck = QueryServiceHelper
				.queryDataSet("E3FhEas", "im_otheroutbill",
						"yd_entryentity.yd_textfield_fhmxdh dh", Filterck, null);
		// 合并销售出库与其他出库信息
		DataSet ck = xsck.union(qtck).groupBy(new String [] {"dh"}).finish();
		List<String> cg = new ArrayList<String>();
		for (Row row : ck) {
			cg.add(row.getString("dh"));
		}
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_tbeas",
				new QFilter[] {  new QFilter("billno", QCP.in, cg) });
		for (DynamicObject row : dy) {

			row.set("yd_tbeas", 1);// 修改数据
			}
		SaveServiceHelper.save(dy);// 保存
		//处理失败
		//查询对应出库单
		Filterck = new QFilter[] { filterksrq,filterjsrq,filtereassbck };
		DataSet xscksb = QueryServiceHelper
				.queryDataSet("E3FhEas", "im_saloutbill",
						"yd_entryentity.yd_textfield_fhmxdh dh", Filterck, null);
		// 查询对应出库单号的汇总其他出库信息
		Filterck = new QFilter[] { filterksrq,filterjsrq,filtereassb };
		DataSet qtcksb = QueryServiceHelper
				.queryDataSet("E3FhEas", "im_otheroutbill",
						"yd_entryentity.yd_textfield_fhmxdh dh", Filterck, null);
		// 合并销售出库与其他出库信息
		DataSet cksb = xscksb.union(qtcksb).groupBy(new String [] {"dh"}).finish();
		List<String> sb = new ArrayList<String>();
		for (Row row : cksb) {
			sb.add(row.getString("dh"));
		}
		DynamicObject[] dysb = BusinessDataServiceHelper.load("yd_fhmxb", "yd_tbeas",
				new QFilter[] {  new QFilter("billno", QCP.in, sb) });
		for (DynamicObject row : dysb) {

			row.set("yd_tbeas", 0);// 修改数据
			}
		SaveServiceHelper.save(dysb);// 保存
	}
}
