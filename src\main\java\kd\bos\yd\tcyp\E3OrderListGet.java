package kd.bos.yd.tcyp;


import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;


import json.JSONArray;
import json.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.log.api.ILogService;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.log.api.AppLogInfo;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class E3OrderListGet extends AbstractTask {

	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {
		// TODO Auto-generated method stub
		// 1、获取日志微服务接口
		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取e3销售出库单启动开始", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
		Date dt = new Date();
		Calendar rightNow = Calendar.getInstance();
		rightNow.setTime(dt);
		rightNow.add(Calendar.HOUR, -1);
		Date dtks = rightNow.getTime();
//		 String ks = "2021-09-01 00:00:00";
//		 String js = "2021-09-15 00:16:00";
		String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtks);
		String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dt);
		E3Post.E3OrderListGet(ks,js,"");
		logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取e3销售出库单启动结束", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
	}



}
