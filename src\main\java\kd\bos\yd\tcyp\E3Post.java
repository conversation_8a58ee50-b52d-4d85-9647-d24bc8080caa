package kd.bos.yd.tcyp;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kingdee.bos.ctrl.common.util.StringUtil;
import json.JSONArray;
import json.JSONObject;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.logging.Log;
import kd.bos.logging.LogFactory;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.util.StringUtils;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class E3Post {

	private static final Log logger = LogFactory.getLog("E3Post");

	public static void E3Rfhhz(String lx, String rq) {
		JSONObject postjsonBB = new JSONObject();
		postjsonBB.put("type", lx);// 开始时间
		postjsonBB.put("date", rq);// 结束时间
		String postdataBB = postjsonBB.toString();
		String resultBB = PostE3BB(postdataBB);
		JSONObject jsonBB = JSONObject.parseObject(resultBB);
		String jgBB = jsonBB.getString("status");
		if ("200".equals(jgBB)) {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数成功", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
			List<DynamicObject> objs = new ArrayList<DynamicObject>();
			JSONArray data = jsonBB.getJSONArray("data");
			for (int i = 0; i < data.size(); i++) {
				// String id = data.getJSONObject(i).get("id").toString();
				String sd_code = data.getJSONObject(i).get("sd_code").toString();
				String date = data.getJSONObject(i).get("date").toString();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date tiem = new Date();
				try {
					tiem = sdf.parse(date);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String type = data.getJSONObject(i).get("type").toString();
				if ("1".equals(type)) {
					type = "0";
				} else {
					type = "1";
				}
				String djbh = "E3_" + type + "_" + sd_code + "_" + date;
				QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
				DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_rhzfh", "billno",
						new QFilter[] { qFilter });
				if (dObject == null) {
					String order_num = data.getJSONObject(i).get("order_num").toString();
					String goods_num = data.getJSONObject(i).get("goods_num").toString();
					String money = data.getJSONObject(i).get("money").toString();
					// String ordersns = data.getJSONObject(i).get("ordersns").toString();
					DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_rhzfh");
					// 设置单据属性
					bill.set("billno", djbh); // 单据编号为平台_退货_店铺_日期
					Date time = new Date();
					bill.set("createtime", time);
					bill.set("billstatus", "C");
					bill.set("yd_combofield_pt", "1");
					bill.set("yd_datefield_fhrq", tiem);
					bill.set("yd_checkboxfield_th", type);
					bill.set("yd_integerfield_zs", order_num);
					bill.set("yd_textfield_dpbh", sd_code);
					bill.set("yd_decimalfield_sl", goods_num);
					bill.set("yd_decimalfield_zje", money);
					objs.add(bill);
				}
			}
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
		} else {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3日汇总发货接口取数失败", "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
	public static String PostE3BB(String postdata) {
		//String key = "TCBJ_E3";// 正式
		//String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";// 正式
		// String key = "BSAKhmSVtfmkDVGLsRLs";//测试
		// String secret = "BSAKdaVAbhYEjuLPwiOs";//测试
		
		String version = "3.0";
		String serviceType = "ptg.order.list";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + ApiFf.key + "&requestTime=" + requestTime + "&secret=" + ApiFf.secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = ApiFf.getMd5(JMZFC);
		String BSDZ = "http://47.92.195.153/e3test/webopm/web/?app_act=api_baison/ec&app_mode=func";//测试
//		String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api_baison/ec&app_mode=func";// 正式
		String url = BSDZ + "&key=" + ApiFf.key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result =ApiFf.doPost(url, map);
		return result;
	}
	public static void E3OrderListGet(String ks, String js, String dp) {
		JSONObject postjson = new JSONObject();
		// postjson.put("status_th", 0);//正常单
		postjson.put("time_type", 7);// 时间类型为发货时间
		postjson.put("startModified", ks);// 开始时间
		postjson.put("endModified", js);// 结束时间
		postjson.put("shipping_status", 7);// 正式需要启用
		postjson.put("not_decrypt", 1);// 正式需要启用
		if (dp != "") {
			postjson.put("sd_code", dp);// 店铺
		}
		// postjson.put("tags_code", "order_type_import");//手工导入
		postjson.put("pageNo", 1);// 页码
		postjson.put("pageSize", 100);// 每页数量
		String postdata = postjson.toString();
		String result = PostE3(postdata);
		JSONObject json = JSONObject.parseObject(result);
		String jg = json.getString("status");
		logger.info("PostE3请求参数：" + postjson.toString() + ";" + "E3系统发货明细数据：" + json.toString());
		if ("api-success".equals(jg)) {
//			try {
//				Thread.sleep(1000);
//			} catch (InterruptedException e1) {
//				// TODO Auto-generated catch block
//				e1.printStackTrace();
//			}
			JSONObject data = json.getJSONObject("data");
			JSONObject page = data.getJSONObject("page");
			int pageTotal = page.getIntValue("pageTotal");// 页数
			// JSONArray orderListGets = data.getJSONArray("orderListGets");
			// BillSave(orderListGets);
			for (int i = 1; i <= pageTotal; i++) {

				postjson = new JSONObject();
				// postjson.put("status_th", 0);//正常单
				postjson.put("time_type", 7);// 时间类型为发货时间
				postjson.put("startModified", ks);
				postjson.put("endModified", js);
				postjson.put("shipping_status", 7);
				postjson.put("not_decrypt", 1);// 正式需要启用
				if (dp != "") {
					postjson.put("sd_code", dp);// 店铺
				}
				// postjson.put("tags_code", "order_type_import");//手工导入
				postjson.put("pageNo", i);
				postjson.put("pageSize", 100);
				postdata = postjson.toString();
				result = PostE3(postdata);
				json = JSONObject.parseObject(result);
				// 记录调用接口完成之后的时间
				Date kssj = new Date();
				jg = json.getString("status");
				if ("api-success".equals(jg)) {
					data = json.getJSONObject("data");
					page = data.getJSONObject("page");
					JSONArray orderListGets = data.getJSONArray("orderListGets");
					BillSave(orderListGets);
				}
				else {
					// 1、获取日志微服务接口
					ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
					// 2、构建日志信息，参考示例如下
					AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细接口失败报文"+postdata+"错误"+json.toString(), "sm", "yd_rhzfh");
					// 3、记录操作日志
					logService1.addLog(logInfo1);
				}
				// 记录保存单据之后的时间
				Date jssj = new Date();
				long hs = jssj.getTime() - kssj.getTime();
				// 如果时间只差小于10000毫秒 则延时
				if ((hs) < 1000) {
					try {
						Thread.sleep(1000 - hs);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}else {
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细接口失败报文"+postdata+"错误"+json.toString(), "sm", "yd_rhzfh");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

	public static String PostE3(String postdata) {
		//String key = "TCBJ_E3";
		//String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";
		// 1、获取日志微服务接口
		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3出库单拉单开始", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
		String version = "3.0";
		String serviceType = "order.list.get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + ApiFf.key + "&requestTime=" + requestTime + "&secret=" + ApiFf.secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = ApiFf.getMd5(JMZFC);
		String BSDZ ="http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func"; // 测试地址
		//String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
//		String BSDZ = "http://8.142.214.170/e3/webopm/web/?app_act=api/ec&app_mode=func"; // 正式地址
		String url = BSDZ + "&key=" + ApiFf.key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = ApiFf.doPost(url, map);
		logInfo1 = OperationLogUtil.buildLogInfo("保存", "e3出库单拉单结束", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
		return result;
	}
	public static void BillSave(JSONArray orderListGets) {

		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < orderListGets.size(); i++) {
			JSONObject orderListItem = orderListGets.getJSONObject(i);

			String order_sn = getBillValue(orderListItem,"order_sn");// 订单编号
			String sd_code = getBillValue(orderListItem,"sd_code");// 商店code
			String djbh = "E3_0_" + sd_code + "_" + order_sn;

			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno",new QFilter[] { qFilter });

			if (dObject == null) {
				String add_time = getBillValue(orderListItem,"shipping_time_ck");// 发货时间对应E3的出库时间
				String shipping_time_fh = getBillValue(orderListItem,"shipping_time_ck");// E3的出库时间
				String shipping_time_ck = getBillValue(orderListItem,"shipping_time_ck");// E3的发库时间
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				SimpleDateFormat sdfrq = new SimpleDateFormat("yyyy-MM-dd");
				String glrq="2021-11-01 00:00:00";
				Date tiem = new Date();
				Date timerq = new Date();
				Date timeglrq = new Date();
				Date shippingtimefh = new Date();
				Date shippingtimeck = new Date();
				try {
					tiem = sdf.parse(add_time);
					timerq = sdfrq.parse(add_time);
					shippingtimefh = sdf.parse(shipping_time_fh);
					shippingtimeck = sdf.parse(shipping_time_ck);
					timeglrq = sdf.parse(glrq);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String deal_code = getBillValue(orderListItem,"deal_code");// 交易号
				if(deal_code!=null && deal_code.length()>50){
					deal_code = deal_code.substring(0, 50);
				}
				String lylx = getBillValue(orderListItem,"lylx");// 来源类型
				String oridealcode = getBillValue(orderListItem,"ori_deal_code");// 换货原始交易号
				String oriordersn = getBillValue(orderListItem,"ori_order_sn");// 换货原始订单号
				String sdid = getBillValue(orderListItem,"sd_id");// 商店id
				String orderstatus = getBillValue(orderListItem,"order_status");// 订单状态
				String shippingcode = getBillValue(orderListItem,"shipping_code");// 快递编码
				String shippingname = getBillValue(orderListItem,"shipping_name");// 快递名称
				String shippingsn = getBillValue(orderListItem,"shipping_sn");// 快递单号
				BigDecimal totalamount = getBillValueNum(orderListItem,"total_amount");// 订单总金额
				BigDecimal totalfee = getBillValueNum(orderListItem,"total_fee");// 平台活动价(淘宝订单)
				BigDecimal payment = getBillValueNum(orderListItem,"payment");// 已付金额
				String issplit = getBillValue(orderListItem,"is_split");// 是否拆分子单	0：否，1：是
				String issplitnew = getBillValue(orderListItem,"is_split_new");// 是否拆分子单	0：否，1：是
				String iscombine = getBillValue(orderListItem,"is_combine");// 是否被合并	0：否，1：是
				String iscombinenew = getBillValue(orderListItem,"is_combine_new");// 是否合并新单	0：否，1：是
				String iscopy = getBillValue(orderListItem,"is_copy");// 是否复制单	0：否，1：是
				String ishh = getBillValue(orderListItem,"is_hh");// 是否换货单	0：否，1：是
				BigDecimal weigh = getBillValueNum(orderListItem,"weigh");// 总重量(单位KG)
				String sdname = getBillValue(orderListItem,"sd_name");// 店铺名称
				String fhckmc = getBillValue(orderListItem,"fhckmc");// 仓库名称
				String qdcode = getBillValue(orderListItem,"qd_code");// 渠道代码
				String qdname = getBillValue(orderListItem,"qd_name");// 渠道名称
				String addtime = getBillValue(orderListItem,"add_time");// 下单时间
				String paytime = getBillValue(orderListItem,"pay_time");// 支付时间
				String provincename = getBillValue(orderListItem,"receiver_province_name");// 省
				String cityname = getBillValue(orderListItem,"receiver_city_name");// 市
				String districtname = getBillValue(orderListItem,"receiver_district_name");// 区
				String address = getBillValue(orderListItem,"receiver_address");// 收货地址
				String shopOrg =getBillValue(orderListItem,"qd_name") ;// E3店铺组织

				//if(tiem.after(timeglrq))//不取11月1日之前的单据
				//{
				String shipping_fee = getBillValue(orderListItem,"shipping_fee");// 运费
				String fhck = getBillValue(orderListItem,"fhck");// 发货仓库
				String fptt = getBillValue(orderListItem,"invoice_title");// 开票
				String bz = getBillValue(orderListItem,"order_msg");//备注
				Boolean kp = false;
				if (fptt!="null" && fptt != null) {
					kp = true;
				}
//				Boolean kp = true;
//				if ("".equals(fptt) || fptt == null) {
//					kp = false;
//				}
				String sgtt = getBillValue(orderListItem,"is_shougong");// 手工
				Boolean sg = true;
				if ("0".equals(sgtt)) {
					sg = false;
				}
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
				//是否批次异常
				boolean isBatchError = false;
				//是否金额异常
				boolean isAmtError = false;
				//异常状态
				int errorState = 0;
				// 设置单据属性
				bill.set("billno", djbh); // 单据编号为平台_退货_店铺_订单号
				Date time = new Date();
				bill.set("createtime", time);
				bill.set("billstatus", "C");
				bill.set("yd_ordertype", "0");
				bill.set("yd_combofield_pt", "1");
				bill.set("yd_textfield_ddbh", order_sn);
				bill.set("yd_datetimefield_xdsj", tiem);
				bill.set("yd_textfield_dpbh", sd_code);
				bill.set("yd_checkboxfield_sfkp", kp);
				bill.set("yd_checkboxfield_sfsg", sg);
				bill.set("yd_decimalfield_yf", shipping_fee);
				bill.set("yd_textfield_ck", fhck);
				bill.set("yd_bz", bz);
				bill.set("yd_dealcode",deal_code);
				bill.set("yd_lylx", lylx);
				bill.set("yd_oridealcode", oridealcode);
				bill.set("yd_oriordersn", oriordersn);
				bill.set("yd_sdid", sdid);
				bill.set("yd_orderstatus", orderstatus);
				bill.set("yd_shippingcode", shippingcode);
				bill.set("yd_shippingname",shippingname);
				bill.set("yd_shippingsn", shippingsn);
				bill.set("yd_totalamount", totalamount);
				bill.set("yd_totalfee", totalfee);
				bill.set("yd_payment", payment);
				bill.set("yd_issplit", issplit);
				bill.set("yd_issplitnew", issplitnew);
				bill.set("yd_iscombinenew", iscombinenew);
				bill.set("yd_iscopy", iscopy);
				bill.set("yd_ishh", ishh);
				bill.set("yd_weigh", weigh);
				bill.set("yd_sdname", sdname);
				bill.set("yd_iscombine", iscombine);
				bill.set("yd_fhckmc", fhckmc);
				bill.set("yd_qdcode",qdcode);
				bill.set("yd_qdname", qdname);
				bill.set("yd_datefield_fhrq", timerq);
				bill.set("yd_shippingtimefh", shippingtimefh);
				bill.set("yd_shippingtimeck", shippingtimeck);
				bill.set("yd_addtime", StringUtils.isEmpty(addtime)?null:Timestamp.valueOf(addtime));
				bill.set("yd_paytime", StringUtils.isEmpty(paytime)?null:Timestamp.valueOf(paytime));
				bill.set("yd_provincename", provincename);
				bill.set("yd_cityname", cityname);
				bill.set("yd_districtname",districtname);
				bill.set("yd_address", address);
				bill.set("yd_shop_org", shopOrg);

				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray orderDetailGets = orderListItem.getJSONArray("orderDetailGets");
				//标记分录中是否包含组装品 -hst 2022/10/17
				boolean isAssembly = false;
				for (int j = 0; j < orderDetailGets.size(); j++) {
					// 设置单据体属性
					JSONObject orderItemInfo = orderDetailGets.getJSONObject(j);
					String share_price = getBillValue(orderItemInfo,"share_price");// 商品单价
					String share_payment = getBillValue(orderItemInfo,"share_payment");// 均摊实付金额
					String sku = getBillValue(orderItemInfo,"sku");// sku
					String sku_id = getBillValue(orderItemInfo,"sku_id");// sku_id
					String goodssn = getBillValue(orderItemInfo,"goods_sn");// 货号
					String goodsid = getBillValue(orderItemInfo,"goods_id");// 货号id
					BigDecimal goodsprice = getBillValueNum(orderItemInfo,"goods_price");// 商品单价
					BigDecimal shopprice = getBillValueNum(orderItemInfo,"shop_price");// 商品网店在售价格
					String originalordersn = getBillValue(orderItemInfo,"original_order_sn");// 商品原始订单号
					String originaldealcode = getBillValue(orderItemInfo,"original_deal_code");// 商品原始交易编号
					String subdealcode = getBillValue(orderItemInfo,"sub_deal_code");// 子交易号
					String numiid = getBillValue(orderItemInfo,"num_iid");// 平台商品ID
					String rowid = getBillValue(orderItemInfo,"id");// id
					String goodsname = getBillValue(orderItemInfo,"goods_name");// 商品名
					String isgift = getBillValue(orderItemInfo,"is_gift");// 是否赠品	0:非赠品，1:赠品
					BigDecimal shareshippingfee = getBillValueNum(orderItemInfo,"share_shipping_fee");// 平均物流成本
					String barcode = getBillValue(orderItemInfo,"barcode");// barcode
					JSONArray batchs = orderItemInfo.getJSONArray("batchs");
					String unit = getBillValue(orderItemInfo,"danwei_name"); //是否是组装品 -hst 2022/10/17
					String isPackage = getBillValue(orderItemInfo,"is_combo"); //是否是组装品 -lzp 2023-01-18替换组装品标识

					//判断分录总金额如果少于0，则标记金额异常
					BigDecimal totalAmt = new BigDecimal(share_payment);
					if(totalAmt.compareTo(BigDecimal.ZERO)<0)
					{
						isAmtError = true;
					}
					//循环batchs中的goods_number合计，并对比orderDetailGets中的goods_number是否一致，如不一致，标记批次异常为是
					//orderDetailGets中的数量
					BigDecimal orderDetailQty = new BigDecimal(orderDetailGets.getJSONObject(j).get("goods_number").toString());
					//batchs中的数量合计
					BigDecimal batchsTotal = new BigDecimal(0);
					//判断批次是否为空
					if(batchs!=null)
					{
						for(int z=0;z<batchs.size();z++)
						{
							BigDecimal goods_number = new BigDecimal(batchs.getJSONObject(z).get("goods_number").toString());// 商品数量
							batchsTotal = batchsTotal.add(goods_number);
						}
						//如果两个数量不相等，属于批次异常
						if(orderDetailQty.compareTo(batchsTotal)!=0)
						{
							isBatchError = true;
						}
					}
					
					if(batchs!=null){
						int zs=batchs.size();
						//if(zs>1){
							BigDecimal ljje=BigDecimal.ZERO;//记录累计金额
							for (int k = 0; k < zs; k++) {
								if(k<zs-1){
									//不为最后一行 按照单价*数量计算金额
									String goods_number = batchs.getJSONObject(k).get("goods_number").toString();// 商品数量
									DynamicObject entry = new DynamicObject(type);
									entry.set("yd_textfield_hpbh", sku);
									entry.set("yd_decimalfield_sl", goods_number);
									entry.set("yd_decimalfield_dj", share_price);
									BigDecimal bcje=new BigDecimal(share_price).multiply(new BigDecimal(goods_number));//当前行批次金额
									ljje=ljje.add(bcje);
									entry.set("yd_decimalfield_zje",bcje);
									String ph = batchs.getJSONObject(k).get("batch_no").toString();
									entry.set("yd_ph", ph);

									entry.set("yd_goodssn", goodssn);
									entry.set("yd_goodsid", goodsid);
									entry.set("yd_goodsprice", goodsprice);
									entry.set("yd_shopprice", shopprice);
									entry.set("yd_originalordersn", originalordersn);
									entry.set("yd_originaldealcode", originaldealcode);
									entry.set("yd_subdealcode", subdealcode);
									entry.set("yd_numiid", numiid);
									entry.set("yd_rowid", rowid);
									entry.set("yd_goodsname", goodsname);
									entry.set("yd_isgift", isgift);
									entry.set("yd_shareshippingfee", shareshippingfee);
									entry.set("yd_barcode", barcode);
									//是否是组装品 -hst 2022/10/17
//									if (StringUtil.equals("1",unit)) {
//										isAssembly = true;
//										entry.set("yd_ispropackage",true);
//									}
									// 是否组装品 -lzp 2023-01-18
									if (StringUtil.equals("1", isPackage)) {
										isAssembly = true;
										entry.set("yd_ispropackage", true);
									}
									// 添加到单据体集合
									entrys.add(entry);
								}else{
									//最后一行使用总金额-累计金额计算金额
									String goods_number = batchs.getJSONObject(k).get("goods_number").toString();// 商品数量
									DynamicObject entry = new DynamicObject(type);
									entry.set("yd_textfield_hpbh", sku);
									entry.set("yd_decimalfield_sl", goods_number);
									entry.set("yd_decimalfield_dj", share_price);
									BigDecimal bcje=new BigDecimal(share_payment).subtract(ljje);
									entry.set("yd_decimalfield_zje",bcje);
									String ph = batchs.getJSONObject(k).get("batch_no").toString();
									entry.set("yd_ph", ph);

									entry.set("yd_goodssn", goodssn);
									entry.set("yd_goodsid", goodsid);
									entry.set("yd_goodsprice", goodsprice);
									entry.set("yd_shopprice", shopprice);
									entry.set("yd_originalordersn", originalordersn);
									entry.set("yd_originaldealcode", originaldealcode);
									entry.set("yd_subdealcode", subdealcode);
									entry.set("yd_numiid", numiid);
									entry.set("yd_rowid", rowid);
									entry.set("yd_goodsname", goodsname);
									entry.set("yd_isgift", isgift);
									entry.set("yd_shareshippingfee", shareshippingfee);
									entry.set("yd_barcode", barcode);
									//是否是组装品 -hst 2022/10/17
//									if (StringUtil.equals("1",unit)) {
//										isAssembly = true;
//										entry.set("yd_ispropackage",true);
//									}
									// 是否是组装品 -lzp 2023/01/18
									if (StringUtil.equals("1", isPackage)) {
										isAssembly = true;
										entry.set("yd_ispropackage",true);
									}
									// 添加到单据体集合
									entrys.add(entry);
								}
							}
//						}else{
//							String goods_number = orderDetailGets.getJSONObject(j).get("goods_number").toString();// 商品数量
//							DynamicObject entry = new DynamicObject(type);
//							entry.set("yd_textfield_hpbh", sku);
//							entry.set("yd_decimalfield_sl", goods_number);
//							entry.set("yd_decimalfield_dj", share_price);
//							entry.set("yd_decimalfield_zje", share_payment);
//							String ph = batchs.getJSONObject(0).get("batch_no").toString();
//							entry.set("yd_ph", ph);
//
//							entry.set("yd_goodssn", goodssn);
//							entry.set("yd_goodsid", goodsid);
//							entry.set("yd_goodsprice", goodsprice);
//							entry.set("yd_shopprice", shopprice);
//							entry.set("yd_originalordersn", originalordersn);
//							entry.set("yd_originaldealcode", originaldealcode);
//							entry.set("yd_subdealcode", subdealcode);
//							entry.set("yd_numiid", numiid);
//							entry.set("yd_rowid", rowid);
//							entry.set("yd_goodsname", goodsname);
//							entry.set("yd_isgift", isgift);
//							entry.set("yd_shareshippingfee", shareshippingfee);
//							entry.set("yd_barcode", barcode);
//							// 添加到单据体集合
//							entrys.add(entry);
//						}
					}else{
						String goods_number = orderDetailGets.getJSONObject(j).get("goods_number").toString();// 商品数量
						DynamicObject entry = new DynamicObject(type);
						entry.set("yd_textfield_hpbh", sku);
						entry.set("yd_decimalfield_sl", goods_number);
						entry.set("yd_decimalfield_dj", share_price);
						entry.set("yd_decimalfield_zje", share_payment);

						entry.set("yd_goodssn", goodssn);
						entry.set("yd_goodsid", goodsid);
						entry.set("yd_goodsprice", goodsprice);
						entry.set("yd_shopprice", shopprice);
						entry.set("yd_originalordersn", originalordersn);
						entry.set("yd_originaldealcode", originaldealcode);
						entry.set("yd_subdealcode", subdealcode);
						entry.set("yd_numiid", numiid);
						entry.set("yd_rowid", rowid);
						entry.set("yd_goodsname", goodsname);
						entry.set("yd_isgift", isgift);
						entry.set("yd_shareshippingfee", shareshippingfee);
						entry.set("yd_barcode", barcode);
						// 是否是组装品 -lzp 2023/01/18
						if (StringUtil.equals("1", isPackage)) {
							isAssembly = true;
							entry.set("yd_ispropackage",true);
						}
						// 添加到单据体集合
						entrys.add(entry);
					}
				}
				
				Boolean iserror = false;
				//根据异常标识标记异常状态
				if(isBatchError && isAmtError)
				{
					errorState = 3;
					iserror = true;
				}
				else if(isBatchError)
				{
					errorState = 2;
					iserror = true;
				}
				else if(isAmtError)
				{
					errorState = 1;
					iserror = true;
				}
				//单据异常
				bill.set("yd_iserror", iserror);
				//异常状态
				bill.set("yd_errorstate", errorState);
				//是否含有组装品 -hst 2022/10/17
				if (isAssembly) {
					bill.set("yd_ispackage",true);
				}
				objs.add(bill);
			}
		}
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

	public static void BillSaveNew(JSONArray orderListGets) {

		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < orderListGets.size(); i++) {
			JSONObject orderListItem = orderListGets.getJSONObject(i);

			String order_sn = getBillValue(orderListItem,"order_sn");// 订单编号
			String sd_code = getBillValue(orderListItem,"sd_code");// 商店code
			String djbh = "E3_0_" + sd_code + "_" + order_sn;

			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno",new QFilter[] { qFilter });

			if (dObject == null) {
				// 设置表头
				DynamicObject bill = setBillItem(orderListItem,djbh);
				// 设置明细
				setItemInfo(orderListItem,bill,djbh);

				objs.add(bill);
			}
		}
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

	/**
	 * 设置表头通用数据
	 * @param orderListItem
	 * @param djbh
	 * @return
	 */
	private static DynamicObject setBillItem(JSONObject orderListItem,String djbh){

		String deal_code = getBillValue(orderListItem,"deal_code");// 交易号
		String lylx = getBillValue(orderListItem,"lylx");// 来源类型
		String order_sn = getBillValue(orderListItem,"order_sn");// 订单编号
		String sd_code = getBillValue(orderListItem,"sd_code");// 商店code
		String oridealcode = getBillValue(orderListItem,"ori_deal_code");// 换货原始交易号
		String oriordersn = getBillValue(orderListItem,"ori_order_sn");// 换货原始订单号
		String sdid = getBillValue(orderListItem,"sd_id");// 商店id
		String orderstatus = getBillValue(orderListItem,"order_status");// 订单状态
		String shippingcode = getBillValue(orderListItem,"shipping_code");// 快递编码
		String shippingname = getBillValue(orderListItem,"shipping_name");// 快递名称
		String shippingsn = getBillValue(orderListItem,"shipping_sn");// 快递单号
		BigDecimal totalamount = getBillValueNum(orderListItem,"total_amount");// 订单总金额
		BigDecimal totalfee = getBillValueNum(orderListItem,"total_fee");// 平台活动价(淘宝订单)
		BigDecimal payment = getBillValueNum(orderListItem,"payment");// 已付金额
		String issplit = getBillValue(orderListItem,"is_split");// 是否拆分子单	0：否，1：是
		String issplitnew = getBillValue(orderListItem,"is_split_new");// 是否拆分子单	0：否，1：是
		String iscombine = getBillValue(orderListItem,"is_combine");// 是否被合并	0：否，1：是
		String iscombinenew = getBillValue(orderListItem,"is_combine_new");// 是否合并新单	0：否，1：是
		String iscopy = getBillValue(orderListItem,"is_copy");// 是否复制单	0：否，1：是
		String ishh = getBillValue(orderListItem,"is_hh");// 是否换货单	0：否，1：是
		BigDecimal weigh = getBillValueNum(orderListItem,"weigh");// 总重量(单位KG)
		String sdname = getBillValue(orderListItem,"sd_name");// 店铺名称
		String fhckmc = getBillValue(orderListItem,"fhckmc");// 仓库名称
		String qdcode = getBillValue(orderListItem,"qd_code");// 渠道代码
		String qdname = getBillValue(orderListItem,"qd_name");// 渠道名称
		String shipping_time_fh = getBillValue(orderListItem,"shipping_time_ck");// E3的出库时间
		String shipping_time_ck = getBillValue(orderListItem,"shipping_time_ck");// E3的发库时间
		String addtime = getBillValue(orderListItem,"add_time");// 下单时间
		String paytime = getBillValue(orderListItem,"pay_time");// 支付时间
//		String receiveraddress = getBillValue(orderListItem,"receiver_address");// 收货地址
//		String username = getBillValue(orderListItem,"user_name");// 收货人昵称
//		String receivername = getBillValue(orderListItem,"receiver_name");// 收货姓名
//		String receivermobile = getBillValue(orderListItem,"receiver_mobile");// 收货手机号
		String add_time = getBillValue(orderListItem,"shipping_time_ck");// 发货时间对应e3出库时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat sdfrq = new SimpleDateFormat("yyyy-MM-dd");

		Date shippingtimefh = new Date();
		Date shippingtimeck = new Date();
		Date tiem = new Date();
		Date timerq = new Date();
		try {
			tiem = sdf.parse(add_time);
			timerq = sdfrq.parse(add_time);
			shippingtimefh = sdf.parse(shipping_time_fh);
			shippingtimeck = sdf.parse(shipping_time_ck);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		String shipping_fee = getBillValue(orderListItem,"shipping_fee");// 运费
		String fhck = getBillValue(orderListItem,"fhck");// 发货仓库
		String fptt = getBillValue(orderListItem,"invoice_title");// 开票
		String bz = getBillValue(orderListItem,"order_msg");//备注
		Boolean kp = true;
		if ("".equals(fptt) || fptt == null) {
			kp = false;
		}
		String sgtt = getBillValue(orderListItem,"invoice_title");// 手工
		Boolean sg = false;
		if ("1".equals(sgtt)) {
			sg = true;
		}
		// 创建单据对象
		DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
		// 设置单据属性
		bill.set("billno", djbh); // 单据编号为平台_退货_店铺_订单号
		Date time = new Date();
		bill.set("createtime", time);
		bill.set("billstatus", "C");
		bill.set("yd_combofield_pt", "1");
		bill.set("yd_textfield_ddbh", order_sn);
		bill.set("yd_datetimefield_xdsj", tiem);
		bill.set("yd_textfield_dpbh", sd_code);
		bill.set("yd_checkboxfield_sfkp", kp);
		bill.set("yd_checkboxfield_sfsg", sg);
		bill.set("yd_decimalfield_yf", shipping_fee);
		bill.set("yd_textfield_ck", fhck);
		bill.set("yd_bz", bz);
		bill.set("yd_dealcode",deal_code);
		bill.set("yd_lylx", lylx);
		bill.set("yd_oridealcode", oridealcode);
		bill.set("yd_oriordersn", oriordersn);
		bill.set("yd_sdid", sdid);
		bill.set("yd_orderstatus", orderstatus);
		bill.set("yd_shippingcode", shippingcode);
		bill.set("yd_shippingname",shippingname);
		bill.set("yd_shippingsn", shippingsn);
		bill.set("yd_totalamount", totalamount);
		bill.set("yd_totalfee", totalfee);
		bill.set("yd_payment", payment);
		bill.set("yd_issplit", issplit);
		bill.set("yd_issplitnew", issplitnew);
		bill.set("yd_iscombinenew", iscombinenew);
		bill.set("yd_iscopy", iscopy);
		bill.set("yd_ishh", ishh);
		bill.set("yd_weigh", weigh);
		bill.set("yd_sdname", sdname);
		bill.set("yd_iscombine", iscombine);
		bill.set("yd_fhckmc", fhckmc);
		bill.set("yd_qdcode",qdcode);
		bill.set("yd_qdname", qdname);
		bill.set("yd_datefield_fhrq", timerq);
		bill.set("yd_shippingtimefh", shippingtimefh);
		bill.set("yd_shippingtimeck", shippingtimeck);
		bill.set("yd_addtime", StringUtils.isEmpty(addtime)?null:Timestamp.valueOf(addtime));
		bill.set("yd_paytime", StringUtils.isEmpty(paytime)?null:Timestamp.valueOf(paytime));
//		bill.set("yd_receiveraddress", receiveraddress);
//		bill.set("yd_username", username);
//		bill.set("yd_receivername", receivername);
//		bill.set("yd_receivermobile", receivermobile);

		return bill;

	}

	/**
	 *
	 * @param orderListItem
	 * @param djbh
	 * @return
	 */
	private static void setItemInfo(JSONObject orderListItem,DynamicObject bill,String djbh){

		// 获取单据体集合
		DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
		// 获取单据体的Type
		DynamicObjectType type = entrys.getDynamicObjectType();
		// 根据Type创建单据体对象
		JSONArray orderDetailGets = orderListItem.getJSONArray("orderDetailGets");
		for (int j = 0; j < orderDetailGets.size(); j++) {
			// 设置单据体属性
			JSONObject orderItemInfo = orderDetailGets.getJSONObject(j);
			String share_price = getBillValue(orderItemInfo,"share_price");// 商品单价
			String share_payment = getBillValue(orderItemInfo,"share_payment");// 均摊实付金额
			JSONArray batchs = orderItemInfo.getJSONArray("batchs");

			if(batchs!=null){
				int zs=batchs.size();
				if(zs>1){
					BigDecimal ljje=BigDecimal.ZERO;//记录累计金额
					for (int k = 0; k < zs; k++) {
						String goods_number = getBillValue(batchs.getJSONObject(k),"goods_number");// 商品数量
						String ph = getBillValue(batchs.getJSONObject(k),"batch_no");
						BigDecimal bcje=BigDecimal.ZERO;;
						if(k<zs-1){
							//不为最后一行 按照单价*数量计算金额
							bcje=new BigDecimal(share_price).multiply(new BigDecimal(goods_number));//当前行批次金额
							ljje=ljje.add(bcje);
						}else{
							//最后一行使用总金额-累计金额计算金额
							bcje=new BigDecimal(share_payment).subtract(ljje);
						}
						DynamicObject entry = setItemInfo(orderItemInfo,type,djbh); 	// 设置明细通用属性

						entry.set("yd_decimalfield_sl", goods_number);
						entry.set("yd_ph", ph);
						entry.set("yd_decimalfield_zje",bcje);
						// 添加到单据体集合
						entrys.add(entry);
					}
				}else{
					DynamicObject entry = setItemInfo(orderItemInfo,type,djbh); 	// 设置明细通用属性

					String ph = batchs.getJSONObject(0).get("batch_no").toString();
					entry.set("yd_ph", ph);
					// 添加到单据体集合
					entrys.add(entry);
				}
			}else{
				// 添加到单据体集合
				entrys.add(setItemInfo(orderItemInfo,type,djbh));
			}
		}

	}

	/**
	 * 设置分录通用数据
	 * @param orderItemInfo
	 * @param type
	 * @return
	 */
	private static DynamicObject setItemInfo(JSONObject orderItemInfo,DynamicObjectType type,String djbh){
		DynamicObject entry = new DynamicObject(type);

		String sku = getBillValue(orderItemInfo,"sku");// sku
		String sku_id = getBillValue(orderItemInfo,"sku_id");// sku_id
		String share_price = getBillValue(orderItemInfo,"share_price");// 单价
		String share_payment = getBillValue(orderItemInfo,"share_payment");// 均摊实付金额
		String goods_number = getBillValue(orderItemInfo,"goods_number");// 商品数量
		String goodssn = getBillValue(orderItemInfo,"goods_sn");// 货号
		String goodsid = getBillValue(orderItemInfo,"goods_id");// 货号id
		BigDecimal goodsprice = getBillValueNum(orderItemInfo,"goods_price");// 商品单价
		BigDecimal shopprice = getBillValueNum(orderItemInfo,"shop_price");// 商品网店在售价格
		String originalordersn = getBillValue(orderItemInfo,"original_order_sn");// 商品原始订单号
		String originaldealcode = getBillValue(orderItemInfo,"original_deal_code");// 商品原始交易编号
		String subdealcode = getBillValue(orderItemInfo,"sub_deal_code");// 子交易号
		String numiid = getBillValue(orderItemInfo,"num_iid");// 平台商品ID
		String rowid = getBillValue(orderItemInfo,"id");// id
		String goodsname = getBillValue(orderItemInfo,"goods_name");// 商品名
		String isgift = getBillValue(orderItemInfo,"is_gift");// 是否赠品	0:非赠品，1:赠品
		BigDecimal shareshippingfee = getBillValueNum(orderItemInfo,"share_shipping_fee");// 平均物流成本
		String barcode = getBillValue(orderItemInfo,"barcode");// barcode

		entry.set("yd_decimalfield_sl", goods_number);   // 可能后续逻辑会重写
		entry.set("yd_decimalfield_zje", share_payment);   // 可能后续逻辑会重写
		entry.set("yd_textfield_hpbh", sku);
		entry.set("yd_skuid", sku_id);
		entry.set("yd_decimalfield_dj", share_price);
		entry.set("yd_goodssn", goodssn);
		entry.set("yd_goodsid", goodsid);
		entry.set("yd_goodsprice", goodsprice);
		entry.set("yd_shopprice", shopprice);
		entry.set("yd_originalordersn", originalordersn);
		entry.set("yd_originaldealcode", originaldealcode);
		entry.set("yd_subdealcode", subdealcode);
		entry.set("yd_numiid", numiid);
		entry.set("yd_rowid", rowid);
		entry.set("yd_goodsname", goodsname);
		entry.set("yd_isgift", isgift);
		entry.set("yd_shareshippingfee", shareshippingfee);
		entry.set("yd_barcode", barcode);

		return entry;
	}

	/**
	 * e3订单写入
	 * @param ks
	 * @param js
	 * @param dp
	 */
	public static void E3OrderListGetNew(String ks, String js, String dp) {

		int pageTotal = 1;
		JSONObject postjson = new JSONObject();

		// postjson.put("status_th", 0);//正常单
		// postjson.put("tags_code", "order_type_import");//手工导入
		postjson.put("time_type", 7);// 时间类型为发货时间
		postjson.put("startModified", ks);
		postjson.put("endModified", js);
		postjson.put("shipping_status", 7);
		postjson.put("not_decrypt", 1);// 正式需要启用
		if (dp != "") {
			postjson.put("sd_code", dp);// 店铺
		}
		postjson.put("pageSize", 100);

		for (int i = 1; i <= pageTotal; i++) {

			postjson.put("pageNo", i);

			String postdata = postjson.toString();
			String result = PostE3(postdata);
			JSONObject json = JSONObject.parseObject(result);
			String jg = json.getString("status");

			// 记录调用接口完成之后的时间
			Date kssj = new Date();
			jg = json.getString("status");
			if ("api-success".equals(jg)) {

				JSONObject data = json.getJSONObject("data");
				JSONObject page = data.getJSONObject("page");
				pageTotal = page.getIntValue("pageTotal");// 重置页数
				JSONArray orderListGets = data.getJSONArray("orderListGets");
				BillSaveNew(orderListGets);
			}else{
				// 。。。。。
			}
			// 记录保存单据之后的时间
			Date jssj = new Date();
			long hs = jssj.getTime() - kssj.getTime();
			// 如果时间只差小于10000毫秒 则延时
			if ((hs) < 1000) {
				try {
					Thread.sleep(1000 - hs);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}

	private static String getBillValue(JSONObject orderListItem,String key){
		String value = String.valueOf(orderListItem.get(key));
		if(StringUtils.isEmpty(value)){
			return null;
		}
		return value;
	}
	private static BigDecimal getBillValueNum(JSONObject orderListItem,String key){
		String value = String.valueOf(orderListItem.get(key));
		if(StringUtils.isEmpty(value) || "null".equals(value) || "false".equals(value)){
			return BigDecimal.ZERO;
		}
		return new BigDecimal(value);
	}
}
