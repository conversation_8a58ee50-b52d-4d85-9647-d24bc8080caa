package kd.bos.yd.tcyp;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.OperateOption;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.resource.ResManager;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.exception.KDBizException;
import kd.bos.exception.KDException;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.OperationServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.epm.eb.ebBusiness.serviceHelper.MutexServiceHelper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @package: kd.bos.yd.tcyp.E3RePricingTask
 * @className E3RePricingTask
 * @author: hst
 * @createDate: 2023/03/29
 * @description: E3直营店重新取价调度任务
 * @version: v1.0
 */
public class E3RePricingTask extends AbstractTask {

    private final static String REPRICING_KEY = "E3_REPRICING";
    protected int amountPrecision = 2;
    protected int curAmountPrecision = 2;
    protected int pricePrecision = 10;
    protected BigDecimal ONEHUNDRED = new BigDecimal(100);

    @Override
    public void execute(RequestContext requestContext, Map<String, Object> map) throws KDException {
        String key = map.get("key").toString();
        switch (key) {
            case REPRICING_KEY : {
                // 直营店物料价格重新取价
                this.retrievingMaterialPrices();
                break;
            }
        }
    }

    /**
     * 直营店物料价格重新取价
     * @author: hst
     * @createDate: 2023/03/29
     */
    private void retrievingMaterialPrices() {
        Map<String,Map<String, BigDecimal>> matPriceContext = new HashMap<>();
        // 同步来源系统为E3
        QFilter terFilter = new QFilter("yd_tbly",QFilter.equals,"1");
        // 不是经销商结算也不是直营店结算
        QFilter settleFIlter = QFilter.of("yd_frome3 != 1 and yd_isdirectsettle != 1");
        // 单据状态为暂存
        QFilter statusFilter = new QFilter("billstatus",QFilter.equals,"A");
        DataSet bills = QueryServiceHelper.queryDataSet(this.getClass().getName(),"im_saloutbill","id",
                new QFilter[]{terFilter,settleFIlter,statusFilter},null);
        for (Row row : bills) {
            String id = row.getString("id");
            Map<String, String> lockInfo = MutexServiceHelper.getLockInfo(id, "im_saloutbill", "E3_REPRICING");
            if (lockInfo != null) {
               continue;
            }
            // 对需要处理的单据加锁
            MutexServiceHelper.request(id, "im_saloutbill", "E3_REPRICING");
            try {
                // 按销售组织+客户维护获取经销商价格表物料价格，并对单据分录的物料价格进行重置
                this.retrievingMaterialPrices(id,matPriceContext);
            } catch (Exception e) {
                // 保存异常信息到单据上
                DynamicObject bill = BusinessDataServiceHelper.loadSingle(id,"im_saloutbill");
                String errMessage = e.getMessage();
                if (errMessage.length() > 2000) {
                    errMessage = errMessage.substring(0, 2000);
                }
                bill.set("yd_settleerror", errMessage);
                SaveServiceHelper.save(new DynamicObject[]{bill});
            } finally {
                // 释放锁
                MutexServiceHelper.release(id, "im_saloutbill", "E3_REPRICING");
            }
        }
    }

    /**
     * 按销售组织+客户维护获取经销商价格表物料价格，并对单据分录的物料价格进行重置
     * @param id 单据id
     * @param matPriceContext 物料价格上下文
     * @author: hst
     * @createDate: 2023/03/29
     */
    private void retrievingMaterialPrices(Object id,Map<String,Map<String, BigDecimal>> matPriceContext) {
        DynamicObject bill = BusinessDataServiceHelper.loadSingle(id,"im_saloutbill");
        // 获取经销商价格表物料价格
        Map<String, BigDecimal> matPriceMap = this.getDistributorPrice(bill,matPriceContext);
        if (Objects.nonNull(matPriceMap) && matPriceMap.size() > 0) {
            // 重置物料价格，并分摊运费
            this.resetMaterialPriceAndAllocatedFreight(bill,matPriceMap);
        } else {
            // 提示异常信息
            DynamicObject bizOrg = bill.getDynamicObject("bizorg");
            DynamicObject customer = bill.getDynamicObject("customer");
            throw new KDBizException(String.format(ResManager.loadKDString("销售组织(" + bizOrg.getString("name")
                            + ")与客户(" + customer.getString("name") + ")未维护经销商供货价格",
                    "retrievingMaterialPrices_2", "yd_tcyp")));
        }
    }

    /**
     * 按销售组织+客户维护获取经销商价格表物料价格
     * @param bill
     * @param matPriceContext
     * @author: hst
     * @createDate: 2023/03/29
     * @return
     */
    private Map<String, BigDecimal> getDistributorPrice(DynamicObject bill,Map<String,Map<String, BigDecimal>> matPriceContext) {
        // 销售组织
        DynamicObject bizOrg = bill.getDynamicObject("bizorg");
        // 客户
        DynamicObject customer = bill.getDynamicObject("customer");
        if (Objects.nonNull(bizOrg) && Objects.nonNull(customer)) {
            String orgNumber = bizOrg.getString("number");
            String cusNumber = customer.getString("number");
            if (matPriceContext.containsKey(orgNumber + "_" + cusNumber + "_MatPrice")) {
                return matPriceContext.get(orgNumber + "_" + cusNumber + "_MatPrice");
            } else {
                Map<String,BigDecimal> mapPrice = getOrgtoCusNewPrice(bizOrg.getString("id"),
                        customer.getString("id"),bill.getDate("biztime"));
                matPriceContext.put(orgNumber + "_" + cusNumber + "_MatPrice",mapPrice);
                return mapPrice;
            }
        }
        return null;
    }

    /**
     * 按销售组织+客户维护获取经销商价格表最新的价格
     * @param orgId 销售组织id
     * @param cusId 客户id
     * @param bizDate 出库单业务日期
     * @return
     * <AUTHOR>
     * @createDate: 2023/03/29
     */
    private Map<String,BigDecimal> getOrgtoCusNewPrice(String orgId, String cusId, Date bizDate) {
        SimpleDateFormat DATE_SDF =new SimpleDateFormat("yyyy-MM-dd");
        Map<String,BigDecimal> matNewPriceMap = new HashMap<String,BigDecimal>();
        QFilter priceFilter = new QFilter("yd_org.id", QCP.equals, orgId);
        priceFilter.and(new QFilter("yd_customer.id", QCP.equals, cusId));
        priceFilter.and(new QFilter("billstatus", QCP.equals, "C"));
        String bizDateStr = DATE_SDF.format(bizDate);
        try {
            bizDate = DATE_SDF.parse(bizDateStr);
        } catch (ParseException e) {
            System.out.println(e.getLocalizedMessage());
        }
        priceFilter.and(new QFilter("entryentity.yd_begindate", QCP.less_equals, bizDate));  // 开始日期小于等于业务日期
        priceFilter.and(new QFilter("entryentity.yd_enddate", QCP.large_equals, bizDate));  // 结束日期大于等于业务日期
        DataSet priceSet = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_orgcuspricebill",
                "entryentity.yd_materiel.id matId,entryentity.yd_saleprice newprice", priceFilter.toArray(), null);
        for(Row row : priceSet) {
            matNewPriceMap.put(row.getString("matId"), row.getBigDecimal("newprice"));
        }
        return matNewPriceMap;
    }

    /**
     * 重置物料价格及分摊运费
     * @param bill 单据
     * @param matPriceMap 经销商供货价格
     * @author: hst
     * @createDate: 2023/03/29
     */
    private void resetMaterialPriceAndAllocatedFreight (DynamicObject bill, Map<String, BigDecimal> matPriceMap) {
        StringBuffer errorStr = new StringBuffer();
        // 单据物料总金额
        BigDecimal total = BigDecimal.ZERO;
        DynamicObjectCollection entries = bill.getDynamicObjectCollection("billentry");
        // 重新取价
        for (DynamicObject entry : entries) {
            boolean isPresent = entry.getBoolean("ispresent");
            if (!isPresent) {
                DynamicObject material = entry.getDynamicObject("material");
                if (Objects.nonNull(material)) {
                    String matId = material.getDynamicObject("masterid").getString("id");
                    if (matPriceMap.containsKey(matId)) {
                        // 计算重新取价后价税合计
                        BigDecimal newPrice = matPriceMap.get(matId);
                        int qty = entry.getInt("qty");
                        BigDecimal amount = newPrice.multiply(new BigDecimal(qty));
                        total = total.add(amount);
                        entry.set("amountandtax", amount);
                        this.changePriceAndTax(bill, entry);
                    } else {
                        errorStr.append(material.getDynamicObject("masterid").getString("number") + ",");
                    }
                }
            }
        }
        if (errorStr.length() > 0) {
            DynamicObject bizOrg = bill.getDynamicObject("bizorg");
            DynamicObject customer = bill.getDynamicObject("customer");
            throw new KDBizException(String.format(ResManager.loadKDString("销售组织(" + bizOrg.getString("name")
                    + ")与客户(" + customer.getString("name") + ")未维护以下物料" + "的经销商供货价格：" +
                    errorStr.deleteCharAt(errorStr.length() - 1), "resetMaterialPriceAndAllocatedFreight", "yd_tcyp")));
        }
        // 运费
        BigDecimal freight = bill.getBigDecimal("yd_amountfield_yf");
        // 累计分摊的运费
        BigDecimal accFreight = BigDecimal.ZERO;
        // 分摊运费
        for (int i = 0; i< entries.size(); i++) {
            DynamicObject entry = entries.get(i);
            boolean isPresent = entry.getBoolean("ispresent");
            if (!isPresent) {
                BigDecimal amount = entry.getBigDecimal("amountandtax");
                BigDecimal qty = entry.getBigDecimal("qty");
                if (i != entries.size() - 1) {
                    // 需分摊的运费
                    BigDecimal allAmount = amount.multiply(freight).divide(total, 2, RoundingMode.HALF_UP);
                    accFreight = accFreight.add(allAmount);
                    entry.set("priceandtax",amount.add(allAmount).divide(qty));
                } else {
                    BigDecimal allAmount = freight.subtract(accFreight);
                    entry.set("priceandtax",amount.add(allAmount).divide(qty));
                }
                this.changePriceAndTax(bill, entry);
            }
        }
        // 清空报错信息
        bill.set("yd_settleerror","");
        SaveServiceHelper.save(new DynamicObject[]{bill});
        OperationResult result = OperationServiceHelper.executeOperate("submit","im_saloutbill",new DynamicObject[]{bill},
                OperateOption.create());
        if (!result.isSuccess()) {
            throw new KDBizException(result.getMessage());
        }
    }

    /**
     * 重置分录含税单价后重新金额字段
     * @param bill 单据
     * @param entry 物料分录
     * @author: hst
     * @createDate: 2023/03/29
     */
    private void changePriceAndTax(DynamicObject bill, DynamicObject entry) {
        boolean isTax = bill.getBoolean("istax");
        // 计算单价
        BigDecimal taxPrice = entry.getBigDecimal("priceandtax");
        BigDecimal taxRate = entry.getBigDecimal("taxrate");
        BigDecimal price = BigDecimal.ZERO;
        BigDecimal ONEHUNDRED = new BigDecimal(100);
        if (taxPrice != null) {
            BigDecimal zero = BigDecimal.ZERO;
            BigDecimal one = BigDecimal.ONE;
            BigDecimal oneHundred = ONEHUNDRED;
            taxRate = taxRate == null ? zero : taxRate.divide(oneHundred, taxRate.scale() + 2, 4);
            price = taxPrice.divide(one.add(taxRate), pricePrecision, 4);
        }
        entry.set("price", price);

        if (isTax) {
            this.calAmountAndTax(entry, isTax);
            this.calTaxAmount(entry, isTax);
            this.calAmount(entry, isTax);
        } else {
            this.calAmount(entry, isTax);
            this.calTaxAmount(entry, isTax);
            this.calAmountAndTax(entry, isTax);
        }

        if (isTax) {
            this.calCurAmountAndTax(bill, entry, isTax);
            this.calCurTaxAmount(bill, entry, isTax);
            this.calCurAmount(bill, entry, isTax);
        } else {
            this.calCurAmount(bill, entry, isTax);
            this.calCurTaxAmount(bill, entry, isTax);
            this.calCurAmountAndTax(bill, entry, isTax);
        }

        BigDecimal discountAmount = entry.getBigDecimal("discountamount");
        BigDecimal amount;
        BigDecimal amountAndTax;
        if (BigDecimal.ZERO.compareTo(discountAmount) == 0) {
            amount = entry.getBigDecimal("price");
            entry.set("actualprice", amount);
            amountAndTax = entry.getBigDecimal("priceandtax");
            entry.set("actualtaxprice", amountAndTax);
        } else {
            amount = entry.getBigDecimal("amount");
            BigDecimal qty = entry.getBigDecimal("qty");
            if (BigDecimal.ZERO.compareTo(amount) != 0 && BigDecimal.ZERO.compareTo(qty) != 0) {
                BigDecimal tempPrice = amount.divide(qty, this.pricePrecision, 4);
                entry.set("actualprice", tempPrice);
            } else {
                entry.set("actualprice", BigDecimal.ZERO);
            }
            amountAndTax = entry.getBigDecimal("amountandtax");
            if (BigDecimal.ZERO.compareTo(amountAndTax) != 0 && BigDecimal.ZERO.compareTo(qty) != 0) {
                BigDecimal temptaxPrice = amountAndTax.divide(qty, this.pricePrecision, 4);
                entry.set("actualtaxprice", temptaxPrice);
            } else {
                entry.set("actualtaxprice", BigDecimal.ZERO);
            }
        }

    }

    private void calCurAmount(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
        BigDecimal amount;
        BigDecimal exchangerate;
        if (isTax) {
            amount = entryObj.getBigDecimal("curamountandtax");
            exchangerate = entryObj.getBigDecimal("curtaxamount");
            entryObj.set("curamount", amount.subtract(exchangerate));
        } else {
            amount = entryObj.getBigDecimal("amount");
            exchangerate = tempTargetBillObj.getBigDecimal("exchangerate");
            BigDecimal localTaxAmount = amount.multiply(exchangerate).setScale(this.curAmountPrecision, 4);
            entryObj.set("curamount", localTaxAmount);
        }

    }

    private void calCurTaxAmount(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
        BigDecimal tax = entryObj.getBigDecimal("taxamount");
        BigDecimal exchangerate = tempTargetBillObj.getBigDecimal("exchangerate");
        BigDecimal localTax = tax.multiply(exchangerate).setScale(this.curAmountPrecision, 4);
        entryObj.set("curtaxamount", localTax);
    }

    private void calCurAmountAndTax(DynamicObject tempTargetBillObj, DynamicObject entryObj, boolean isTax) {
        BigDecimal localAmount;
        BigDecimal localTax;
        if (isTax) {
            localAmount = entryObj.getBigDecimal("amountandtax");
            localTax = tempTargetBillObj.getBigDecimal("exchangerate");
            BigDecimal localTaxAmount = localAmount.multiply(localTax).setScale(this.curAmountPrecision, 4);
            entryObj.set("curamountandtax", localTaxAmount);
        } else {
            localAmount = entryObj.getBigDecimal("curamount");
            localTax = entryObj.getBigDecimal("curtaxamount");
            entryObj.set("curamountandtax", localAmount.add(localTax));
        }
    }

    private void calAmount(DynamicObject entryObj, boolean isTax) {
        BigDecimal price;
        BigDecimal qty;
        BigDecimal amount;
        if (isTax) {
            price = entryObj.getBigDecimal("amountandtax");
            qty = entryObj.getBigDecimal("taxamount");
            amount = price.subtract(qty);
            entryObj.set("amount", amount);
        } else {
            price = entryObj.getBigDecimal("price");
            qty = entryObj.getBigDecimal("qty");
            amount = qty.multiply(price).setScale(this.amountPrecision, 4);

            entryObj.set("amount", amount);
        }
    }

    private void calTaxAmount(DynamicObject entryObj, boolean isTax) {
        BigDecimal amount;
        BigDecimal taxRate;
        BigDecimal tax;
        if (isTax) {
            amount = entryObj.getBigDecimal("amountandtax");
            taxRate = entryObj.getBigDecimal("taxrate");
            tax = BigDecimal.ZERO;
            if (amount.compareTo(BigDecimal.ZERO) != 0 && taxRate.compareTo(BigDecimal.ZERO) != 0) {
                taxRate = taxRate.divide(ONEHUNDRED, taxRate.scale() + 2, 4);
                tax = amount.multiply(taxRate).divide(taxRate.add(BigDecimal.ONE), this.amountPrecision, 4);
            }

            entryObj.set("taxamount", tax);
        } else {
            amount = entryObj.getBigDecimal("amount");
            taxRate = entryObj.getBigDecimal("taxrate");
            tax = BigDecimal.ZERO;
            if (amount.compareTo(BigDecimal.ZERO) != 0 && taxRate.compareTo(BigDecimal.ZERO) != 0) {
                taxRate = taxRate.divide(ONEHUNDRED, taxRate.scale() + 2, 4);
                tax = amount.multiply(taxRate).setScale(this.amountPrecision, 4);
            }

            entryObj.set("taxamount", tax);
        }
    }

    private void calAmountAndTax(DynamicObject entryObj, boolean isTax) {
        BigDecimal amount;
        BigDecimal tax;
        BigDecimal taxAmount;
        if (isTax) {
            amount = entryObj.getBigDecimal("priceandtax");
            tax = entryObj.getBigDecimal("qty");
            taxAmount = BigDecimal.ZERO;
            BigDecimal discountAmount = entryObj.getBigDecimal("discountamount");
            taxAmount = tax.multiply(amount).subtract(discountAmount).setScale(this.amountPrecision, 4);
            entryObj.set("amountandtax", taxAmount);
        } else {
            amount = entryObj.getBigDecimal("amount");
            tax = entryObj.getBigDecimal("taxamount");
            taxAmount = amount.add(tax);
            entryObj.set("amountandtax", taxAmount);
        }
    }
}
