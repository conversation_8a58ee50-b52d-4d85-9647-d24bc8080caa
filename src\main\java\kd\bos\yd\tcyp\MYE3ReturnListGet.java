package kd.bos.yd.tcyp;


import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;


import json.JSONArray;
import json.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.log.api.ILogService;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.log.api.AppLogInfo;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class MYE3ReturnListGet extends AbstractTask {

	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {
		// TODO Auto-generated method stub

		// 1、获取日志微服务接口
		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取麦优e3销售销售退货启动开始", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);

		Date dt = new Date();
		Calendar rightNow = Calendar.getInstance();
		rightNow.setTime(dt);
		rightNow.add(Calendar.HOUR, -1);
		Date dtks = rightNow.getTime();
		String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtks);
		String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dt);
		 ApiFf.MYE3ReturnListGet(ks, js);
		logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取麦优e3销售销售退货启动结束", "sm", "yd_fhmxb");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
	}



}
