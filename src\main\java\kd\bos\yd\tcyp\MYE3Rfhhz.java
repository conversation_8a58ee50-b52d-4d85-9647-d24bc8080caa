package kd.bos.yd.tcyp;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;


import json.JSONArray;
import json.JSONObject;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.exception.KDException;
import kd.bos.log.api.ILogService;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.log.api.AppLogInfo;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class MYE3Rfhhz extends AbstractTask {

	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {
		// TODO Auto-generated method stub
		// 1、获取日志微服务接口
		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取麦优e3日汇总发货启动开始", "sm", "yd_rhzfh");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
		Date dt = new Date();
		Calendar rightNow = Calendar.getInstance();
			rightNow.setTime(dt);
			rightNow.add(Calendar.DAY_OF_MONTH, -1);
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(rightNow.getTime());
		ApiFf.MYE3Rfhhz("1",sj);
		ApiFf.MYE3Rfhhz("2",sj);
		logInfo1 = OperationLogUtil.buildLogInfo("保存", "获取麦优e3日汇总发货启动结束", "sm", "yd_rhzfh");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
	}



}
