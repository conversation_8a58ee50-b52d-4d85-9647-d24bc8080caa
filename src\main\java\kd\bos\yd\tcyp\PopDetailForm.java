package kd.bos.yd.tcyp;

import kd.bos.form.control.Control;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.plugin.AbstractFormPlugin;
import org.apache.commons.lang3.StringUtils;

import java.util.EventObject;
import java.util.HashMap;

public class PopDetailForm extends AbstractFormPlugin {
    //页面确认按钮标识
    private final static String KEY_OK = "btnok";
    //页面取消按钮标识
    private final static String KEY_CANCEL = "btncancel";
    //页面日期标识
    private final static String KEY_DATE_WANG = "yd_date_wang";
    //页面天数标识
    private final static String KEY_DAY_WANG = "yd_day_wang";
  //页面天数标识
    private final static String KEY_DP_WANG = "yd_textfield_dp";

    /**
     * 注册监听，监听页面确认按钮和取消按钮
     * @param e
     */
    @Override
    public void registerListener(EventObject e) {
        super.registerListener(e);
        //页面确认按钮和取消按钮添加监听
        this.addClickListeners(KEY_OK, KEY_CANCEL);
    }
//    public void initialize(ClosedCallBackEvent closedCallBackEvent) {
//    	if (StringUtils.equals(closedCallBackEvent.getActionId(), "E3Order")
//				&& null != closedCallBackEvent.getReturnData()) {
//			this.getView().setVisible(false, "yd_textfield_dp");
//    }
//    }

    /**
     * 页面点击事件
     * @param evt
     */
    @Override
    public void click(EventObject evt) {
        super.click(evt);
        //获取被点击的控件对象
        Control source = (Control) evt.getSource();
        if (StringUtils.equals(source.getKey(), KEY_OK)) {
            HashMap<String, String> hashMap = new HashMap<>();
            //如果被点击控件为确认，则获取页面相关控件值，组装数据传入returnData返回给父页面，最后关闭页面
            hashMap.put(KEY_DATE_WANG, String.valueOf(this.getModel().getValue(KEY_DATE_WANG)));
            hashMap.put(KEY_DP_WANG, String.valueOf(this.getModel().getValue(KEY_DP_WANG)));
            hashMap.put(KEY_DAY_WANG, String.valueOf(this.getModel().getValue(KEY_DAY_WANG)));
            this.getView().returnDataToParent(hashMap);
            this.getView().close();
        } else if (StringUtils.equals(source.getKey(), KEY_CANCEL)) {
            //被点击控件为取消则设置返回值为空并关闭页面（在页面关闭回调方法中必须验证返回值不为空，否则会报空指针）
            this.getView().returnDataToParent(null);
            this.getView().close();
        }
    }
}
