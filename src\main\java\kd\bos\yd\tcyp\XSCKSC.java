package kd.bos.yd.tcyp;

import java.util.ArrayList;
import java.util.List;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.plugin.AbstractOperationServicePlugIn;
import kd.bos.entity.plugin.PreparePropertysEventArgs;
import kd.bos.entity.plugin.args.BeginOperationTransactionArgs;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

public class XSCKSC extends AbstractOperationServicePlugIn {
	@Override
	public void onPreparePropertys(PreparePropertysEventArgs e) {
		// TODO 在此添加业务逻辑
		e.getFieldKeys().add("yd_textfield_fhmxdh");
	}

	@Override
	public void beginOperationTransaction(BeginOperationTransactionArgs e) {
		// TODO 在此添加业务逻辑
		List<String> dhs = new ArrayList<>();
		for(DynamicObject obj : e.getDataEntities()){
			DynamicObjectCollection yd_entryentity = obj.getDynamicObjectCollection("yd_entryentity");
			for(DynamicObject dr :yd_entryentity){
			dhs.add(dr.getString("yd_textfield_fhmxdh"));
			}
		}
		QFilter filter = new QFilter("billno", QCP.in, dhs);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_xsckdh,yd_combofield_xyd",
				new QFilter[] {filter});
		for (DynamicObject row : dy) {
			row.set("yd_textfield_xsckdh", "");// 修改数据
			row.set("yd_combofield_xyd", "");// 修改数据
			}
		SaveServiceHelper.save(dy);// 保存
	}

}
