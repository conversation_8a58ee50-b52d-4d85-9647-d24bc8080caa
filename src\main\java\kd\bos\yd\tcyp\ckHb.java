package kd.bos.yd.tcyp;

import java.text.SimpleDateFormat;
import java.util.*;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.exception.KDException;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class ckHb extends AbstractTask {
	// 添加过滤条件 只处理今天之前的数据
	@Override
	public void execute(RequestContext arg0, Map<String, Object> arg1) throws KDException {
		// TODO Auto-generated method stub
		// 1、获取日志微服务接口
		ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
		// 2、构建日志信息，参考示例如下
		AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "出库单合单开始", "sm", "im_saloutbill");
		// 3、记录操作日志
		logService1.addLog(logInfo1);
		// 查询非品牌分单的排除物料
		// 查询排除物料
		// 查询直营店的排除物料
		List<String> pcwlin = DeliveryHelper.getExcludeMaterial(0, "2"); // 不是运费补差且直营店
		List<String> fywl = DeliveryHelper.getExcludeMaterial(1, "2"); // 费用物料且直营店
		// 查询一盘货的排除物料
		List<String> pcwlinpp = DeliveryHelper.getExcludeMaterial(0, "1"); // 不是运费补差且一盘货
		List<String> fywlpp = DeliveryHelper.getExcludeMaterial(1, "1"); // 费用物料且一盘货
		// 查询直营店一盘货的排除物料
		List<String> pcwlindir = DeliveryHelper.getExcludeMaterial(0, "3"); // 不是运费补差且直营店一盘货
		List<String> fywldir = DeliveryHelper.getExcludeMaterial(1, "3"); // 费用物料且直营店一盘货
		// update by hst 2023/03/28 获取客户对应关系表中已维护为需要分摊运费的客户
		List<String> shareClient = DeliveryHelper.getSharedFareClients();

		// 货品品牌店铺
		List<String> ppfd = DeliveryHelper.getBrandStore();

		// 汇总直营店的所有排除物料
		List<String> pcwlhz = new ArrayList<>();
		// 汇总直营店的所有排除物料
		List<String> pcwldirhz = new ArrayList<>();
		// 汇总所有品牌的所有排除物料
		List<String> pcwlhzpp = new ArrayList<>();
		pcwlhz.addAll(pcwlin);
		pcwlhz.addAll(fywl);

		pcwldirhz.addAll(pcwlindir);
		pcwldirhz.addAll(fywldir);

		pcwlhzpp.addAll(pcwlinpp);
		pcwlhzpp.addAll(fywlpp);

		List<String> platList = new ArrayList<>();
		platList.add("1");
		platList.add("2");
		platList.add("3");

		String sj = null;
		if (arg1 != null && arg1.containsKey("filterDate")) {
			sj = (String) arg1.get("filterDate");
		}else {
			sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(new Date());
		}
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天
		QFilter filter3 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 不处理为否
		QFilter filter4 = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 全部为排除物料为否
		QFilter filter7 = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filter8 = QFilter.of("yd_iserror =?", "0");//发货明细表单据异常不勾选的才允许处理
		QFilter filterPlat = new QFilter("yd_combofield_pt", QCP.in, platList);// 平台

		DataSet rqs = QueryServiceHelper
				.queryDataSet("jy", "yd_fhmxb", "yd_datefield_fhrq rq",
						new QFilter[] { filter1, filter2, filter3, filter4,filter7,filter8 }, null)
				.groupBy(new String[] { "rq" }).finish();
		List<String> rq = new ArrayList<String>();
		for (Row rowds : rqs) {
			String ks = rowds.getString("rq");
			rq.add(ks);
		}
		if(rq.size()==0)
		{
			logInfo1 = OperationLogUtil.buildLogInfo("保存", "出库单合单结束", "sm", "im_saloutbill");
			logService1.addLog(logInfo1);
			return;
		}
		QFilter filter5 = new QFilter("yd_datefield_fhrq", QCP.in, rq);// 需要处理的日期的单据合集

		// 从参数中获取参数，根据参数决定是要处理哪些数据
		// add by lzp，将调度计划拆分成按品牌汇总和按非品牌汇总
		String type = null;
		if (arg1 != null) {
			type = (String) arg1.get("type");
		}

		filter5.and(filterPlat);
		ApiFf.jy(filter5,pcwlhz,pcwldirhz,pcwlhzpp,ppfd, type, filter2);
		// DataSet rqs1 = QueryServiceHelper.queryDataSet("jy", "yd_fhmxb",
		// "yd_datefield_fhrq rq",
		// new QFilter[] { filter1,filter2,filter3,filter4 }, null).groupBy(new String[]
		// { "rq"}).finish();

		for (String ks: rq) {
			// 直营店
			if (DeliveryHelper.isDirectStore(type)) {
				// add by lzp, 新增直营店一盘货的结算方式的处理方式
				// update by hst 2023/03/28 增加需分摊运费客户参数
				ApiFf.bchbdj_direct("1", pcwlindir, fywldir, ppfd, ks,filterPlat,filter2); // 包含正常汇总和不汇总的情况
				ApiFf.bchbdj("1", pcwlin, fywl, ppfd, ks,filterPlat,filter2,shareClient);
				ApiFf.bchbdj("4", pcwlin, fywl, ppfd, ks,filterPlat,filter2,shareClient);
				ApiFf.bcbhzdj("2", pcwlin, fywl, ppfd, ks,filterPlat,filter2,shareClient);
				ApiFf.bcbhzdj("5", pcwlin, fywl, ppfd, ks,filterPlat,filter2,shareClient);
			}
			// 一盘货
			if (DeliveryHelper.isAgentStore(type)) {
				ApiFf.bchbdjby("1", pcwlinpp, fywlpp, ppfd, ks,filterPlat,filter2);
				ApiFf.bchbdjby("4", pcwlinpp, fywlpp, ppfd, ks,filterPlat,filter2);
			}
		}
		if (type != null) {
			// 直营店
			if (DeliveryHelper.isDirectStore(type)) {
				// 获取直营店一盘货的平台仓库编码集合
				Set<String> platformDirectWarehouse1 = DeliveryHelper.getPlatformDirectWarehouse("1");
				// 获取直营店实体店的平台仓库编码集合
				Set<String> platformDirectWarehouse2 = DeliveryHelper.getPlatformDirectWarehouse("2");

				QFilter dirQFilter = new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse2)
						.or(new QFilter("yd_textfield_ck", QCP.in, platformDirectWarehouse1));
				QFilter notBrandFilter = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 只取直营店铺
				filter5.and(dirQFilter).and(notBrandFilter);
			}else if (DeliveryHelper.isAgentStore(type)) {
				// 经销商一盘货
				QFilter brandFilter = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
				filter5.and(brandFilter);
			}
		}
		ApiFf.khdyzzjy(filter5);
		// }
		logInfo1 = OperationLogUtil.buildLogInfo("保存", "出库单合单结束", "sm", "im_saloutbill");
		logService1.addLog(logInfo1);
	}

}
