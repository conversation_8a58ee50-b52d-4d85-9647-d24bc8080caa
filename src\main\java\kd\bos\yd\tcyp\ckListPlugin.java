package kd.bos.yd.tcyp;

import java.util.ArrayList;
import java.util.EventObject;
import java.util.List;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.BillList;
import kd.bos.list.IListView;
import kd.bos.list.ListShowParameter;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import java.util.Map;
import java.util.HashMap;

public class ckListPlugin extends AbstractListPlugin {
	String dh=null;
	@Override
	public void beforeBindData(EventObject e) {
		super.beforeBindData(e);
		FormShowParameter showParameter = this.getView().getFormShowParameter();
		dh=showParameter.getCustomParam("billno");
		 //TODO 在此添加业务逻辑
	}
	@Override
	public void setFilter(SetFilterEvent e) {
		//设置过滤，也可使用e.setCustomQFilters()，可设置多条件。    
		if(dh!=null) {
			e.getQFilters().clear();//清除原有过滤条件
		List<QFilter> qfilters = e.getQFilters();
			//List<QFilter> qfilters =new ArrayList<QFilter>();
		QFilter qFilter = new QFilter("billno", QCP.equals, dh);
		qfilters.add(qFilter);//设置排序 Asc升序 Desc降序 ，多条件可用逗号连接，排序顺序按字符串顺序  
		String str="1";
		//e.setOrderBy("billstatus Asc,date Desc");
		//先按单据状态升序，再按日期降序    }
		}
	}
	@Override
	public void afterBindData(EventObject e) {
		super.beforeBindData(e);
		if(dh!=null) {
			String formId = ((IListView) this.getView()).getBillFormId();
			QFilter filter1 = new QFilter("billno", QCP.equals, dh);
			DataSet dy = QueryServiceHelper.queryDataSet("ckListPlugin",formId,"yd_entryentity.yd_textfield_fhmxdh dh",new QFilter[] { filter1 },null);
			List<String> billnos=new ArrayList<String>();
			for (Row row : dy) {
				String dh=row.getString("dh");
				if(dh!=null||dh!="") {
				billnos.add(dh);
				}
			}
			if(billnos.size()>0)
			{
				filter1 = new QFilter("billno", QCP.in, billnos);
				ListShowParameter showParameter = new ListShowParameter();			
				showParameter.setBillFormId("yd_fhmxb");//注意这里是billFormId，指的是列表对应的单据标识		
				showParameter.getOpenStyle().setShowType(ShowType.Modal);
				//showParameter.getListFilterParameter().getQFilters().clear();
				//showParameter.getListFilterParameter().getQFilters().add(filter1);
				showParameter.getListFilterParameter().setFilter(filter1);

				this.getView().showForm(showParameter);

			}
		}
	}
	@Override
	public void itemClick(ItemClickEvent evt) {
		// TODO Auto-generated method stub
		super.itemClick(evt);
		String pname= evt.getItemKey();
		switch (pname) {
		case "yd_baritemap_cx":
			String formId = ((IListView) this.getView()).getBillFormId();
			BillList billList = this.getControl("billlistap");
			// 获取选中行记录,注意:得到的结果是记录的主键ID的集合
			ListSelectedRowCollection listSelectedRowCol = billList.getSelectedRows();
			if (listSelectedRowCol != null && listSelectedRowCol.size() > 0) {
				List<Long> ids=new ArrayList<Long>();
				for(ListSelectedRow dr:listSelectedRowCol) {
					ids.add((Long)dr.getPrimaryKeyValue());
				}
				QFilter filter1 = new QFilter("id", QCP.in, ids);
				DataSet dy = QueryServiceHelper.queryDataSet("ckListPlugin",formId,"yd_entryentity.yd_textfield_fhmxdh dh",new QFilter[] { filter1 },null);
				List<String> billnos=new ArrayList<String>();
				for (Row row : dy) {
					String dh = row.getString("dh");
					if (dh != null && !dh.isEmpty() && !billnos.contains(dh)) {
						billnos.add(dh);
					}
				}
				if(billnos.size()>0)
				{
//					if(billnos.size() > 800) {
//						this.getView().showErrorNotification("发货明细单号数量超过800条，请减少选择的单据数量！");
//						return;
//					}
					
					// filter1 = new QFilter("billno", QCP.in, billnos);
					ListShowParameter showParameter = new ListShowParameter();			
					showParameter.setBillFormId("yd_fhmxb");//注意这里是billFormId，指的是列表对应的单据标识		
					showParameter.getOpenStyle().setShowType(ShowType.Modal);
					//showParameter.getListFilterParameter().getQFilters().clear();
					//showParameter.getListFilterParameter().getQFilters().add(filter1);

					Map<String, Object> value = new HashMap<>();
					value.put("isJoinQuery", true);
					value.put("billno", billnos);
					showParameter.setCustomParams(value);

					// showParameter.getListFilterParameter().setFilter(filter1);

					this.getView().showForm(showParameter);

				}
				else
				{
					this.getView().showMessage("选中单据没有发货明细单号.请重新选择!");
				}
			}
			else
			{
				this.getView().showMessage("请选中行再点击按钮!");
			}
			break;
		}
	}
}
