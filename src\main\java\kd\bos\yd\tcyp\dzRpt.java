package kd.bos.yd.tcyp;

import kd.bos.algo.DataSet;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.permission.PermissionServiceHelper;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class dzRpt extends AbstractReportListDataPlugin {

	@Override
	public DataSet query(ReportQueryParam arg0, Object arg1) throws Throwable {
		// TODO Auto-generated method stub
		List<FilterItemInfo> filters = arg0.getFilter().getFilterItems();
		String dp ="";
		String rq = "";
		String rqjs = "";
		String thgl = "";
		String jegl = "";
		String slgl = "";
		String dsgl = "";
		String pt = "";
		String zz = "";
		for (FilterItemInfo filterItem : filters) {
			switch (filterItem.getPropName()) {
			// 查询条件统计开始年份,标识如不一致,请修改
			case "yd_dpgl":
				dp = (filterItem.getString() == null) ? null :filterItem.getString();
				break;
			case "yd_rqgl":
				rq = (filterItem.getDate() == null) ? null
						: (String) new SimpleDateFormat("yyyy-MM-dd").format(filterItem.getDate());
				break;
			case "yd_rqgljs":
				rqjs = (filterItem.getDate() == null) ? null
						: (String) new SimpleDateFormat("yyyy-MM-dd").format(filterItem.getDate());
				break;
			case "yd_thgl":
				thgl = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			case "yd_jegl":
				jegl = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			case "yd_slgl":
				slgl = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			case "yd_dsgl":
				dsgl = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			case "yd_pt":
				pt = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			case "yd_zzgl":
				zz = (filterItem.getValue() == null) ? null :String.valueOf(((DynamicObject)filterItem.getValue()).getPkValue()) ;
				break;
			default:
				break;
			}
		}
		// ksrq="2020-09-01";
		// jsrq="2020-09-30";
		QFilter filterrq = QFilter.of("yd_datefield_fhrq >= ?", rq);// 日期
		QFilter filterrqjs = QFilter.of("yd_datefield_fhrq <= ?", rqjs);// 日期结束
		QFilter filterdp = new QFilter("yd_textfield_dpbh", QCP.like, "%"+dp+"%");// 日期
		//QFilter filterpcck = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		long userid=Long.valueOf(RequestContext.get().getUserId());
		QFilter filterqx =PermissionServiceHelper.getDataPermission(userid,"im","yd_fhmxb"); //获取当前用户发货明细的数据过滤规则
		//QFilter filterSh = QFilter.of("status=?", "C");
		QFilter[] Filter = new QFilter[] { filterrq,filterrqjs,filterqx };
		if(dp!="")
		{
			Filter = new QFilter[] { filterrq,filterrqjs,filterqx,filterdp };
		}
		DataSet fhmx = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_fhmxb",
				"yd_combofield_pt pt,yd_checkboxfield_th th,yd_textfield_dpbh dp",
				Filter, null).groupBy(new String[] { "dp", "th", "pt" }).finish();
		// 查询当前过滤条件汇总的发货明细信息(不包含组装品） -hst 2022/10/18
		List<QFilter> nopackQFilter = Arrays.stream(Filter).collect(Collectors.toList());
		nopackQFilter.add(new QFilter("yd_ispackage",QFilter.equals,false));
		DataSet nozzfhmx = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_fhmxb",
				"yd_combofield_pt cfpt,yd_checkboxfield_th cfth,yd_textfield_dpbh cfdp,yd_entryentity.yd_decimalfield_sl sl," +
						"yd_entryentity.yd_decimalfield_zje je" , nopackQFilter.toArray(new QFilter[nopackQFilter.size()]), null).
				groupBy(new String[] { "cfdp", "cfth", "cfpt" }).sum("sl").sum("je").finish();;
		// 查询当前过滤条件汇总的发货明细信息(包含组装品） -hst 2022/10/18
		List<QFilter> packQFilter = Arrays.stream(Filter).collect(Collectors.toList());
		packQFilter.add(new QFilter("yd_ispackage",QFilter.equals,true));
		DataSet zzfhmx = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_fhmxb",
				"yd_combofield_pt cfpt,yd_checkboxfield_th cfth,yd_textfield_dpbh cfdp,yd_entryentity1.yd_src_num cfsl," +
						"yd_entryentity1.yd_src_amount cfje", packQFilter.toArray(new QFilter[packQFilter.size()]), null)
				.groupBy(new String[] { "cfdp", "cfth", "cfpt" }).sum("cfsl").sum("cfje").finish();
		//统计商品明细
		fhmx = fhmx.leftJoin(nozzfhmx).on("dp","cfdp").on("th","cfth").on("pt","cfpt").select("dp","th","pt","sl","je je").finish();
		//统计组装品明细
		fhmx = fhmx.leftJoin(zzfhmx).on("dp","cfdp").on("th","cfth").on("pt","cfpt").select("dp","th","pt","cfsl + sl sl","cfje + je je").finish();
		//fhmxpc.print(true);
		// 查询当前过滤条件的发货明细信息一共多少单据并合计运费
		DataSet fhmxzs = QueryServiceHelper
				.queryDataSet(this.getClass().getName(), "yd_fhmxb",
						"yd_combofield_pt pt,yd_checkboxfield_th th,yd_textfield_dpbh dp,yd_decimalfield_yf yf", Filter, null)
				.groupBy(new String[] { "dp", "th", "pt" }).sum("yf").count("count").finish();
		//fhmxzs.print(true);
		fhmx = fhmx.join(fhmxzs).on("dp", "dp").on("th", "th").select("dp", "th", "pt", "sl", "je+yf je", "count").finish();
		//fhmx.print(true);
		//ckdh.print(true);
		// 查询当前过滤条件汇总的日汇总发货信息
		Filter = new QFilter[] { filterrq,filterrqjs };
		if(dp!="")
		{
			Filter = new QFilter[] { filterrq,filterrqjs,filterdp };
		}
		DataSet rhzfh = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_rhzfh",
				"yd_combofield_pt pt,yd_checkboxfield_th th,yd_textfield_dpbh dp,yd_integerfield_zs hzzs,yd_decimalfield_sl hzsl,yd_decimalfield_zje hzje",
				Filter, null).groupBy(new String[] { "dp", "th", "pt" }).sum("hzzs").sum("hzsl").sum("hzje").finish();
		//rhzfh.copy().print(false);
		// filterksrq =new QFilter("biztime", QCP.large_equals,ksrq);
		// filterjsrq =new QFilter("biztime", QCP.less_equals,jsrq);
		// Filter=new QFilter[]{filterksrq,filterjsrq};
		DataSet dykh = QueryServiceHelper
				.queryDataSet(this.getClass().getName(), "yd_khdygx",
						"yd_combofield_pt pt,yd_entryentity.yd_textfield dp,yd_entryentity.yd_textfield_dpmc dpmc,yd_entryentity.yd_orgfield_dyzz zz", null, null);
		fhmx = fhmx.leftJoin(dykh)
				.on("dp", "dp").on("pt", "pt")
				.select("pt", "dp", "th","count", "sl", "je", "dpmc","zz").finish();
		fhmx = fhmx.leftJoin(rhzfh)
				.on("dp", "dp").on("th", "th")
				.select("pt yd_mxpt", "dp yd_dpbh", "th yd_th", "hzzs yd_ptzs", "hzsl yd_ptzsl", "hzje yd_ptzje","dpmc yd_dpmc","zz yd_zz",
						"count yd_cqzs", "sl yd_cqzsl", "je yd_cqzje","case when sl=hzsl then false else true end yd_sjcw",
						"case when je=hzje then false else true end yd_jesjcw","case when count=hzzs then false else true end yd_dssjcw")
				.finish().orderBy(new String[] {"yd_th desc","yd_dpbh"});
		if(zz!=null)
		{
		fhmx=fhmx.filter("yd_zz="+zz);
		}
		if(thgl.equals("1"))
		{
		fhmx=fhmx.filter("yd_th=true");
		}
		if(thgl.equals("0"))
		{
		fhmx=fhmx.filter("yd_th=false");
		}
		if(slgl.equals("1"))
		{
		fhmx=fhmx.filter("yd_sjcw=false");
		}
		if(slgl.equals("0"))
		{
		fhmx=fhmx.filter("yd_sjcw=true");
		}
		if(jegl.equals("1"))
		{
		fhmx=fhmx.filter("yd_jesjcw=false");
		}
		if(jegl.equals("0"))
		{
		fhmx=fhmx.filter("yd_jesjcw=true");
		}
		if(dsgl.equals("1"))
		{
		fhmx=fhmx.filter("yd_dssjcw=false");
		}
		if(dsgl.equals("0"))
		{
		fhmx=fhmx.filter("yd_dssjcw=true");
		}
		if(!pt.equals("0"))
		{
		fhmx=fhmx.filter("yd_mxpt='"+pt+"'");
		}
		return fhmx;
	}

}
