package kd.bos.yd.tcyp;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.permission.PermissionServiceHelper;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class dzRptCk extends AbstractReportListDataPlugin {

	@Override
	public DataSet query(ReportQueryParam arg0, Object arg1) throws Throwable {
		// TODO Auto-generated method stub
		List<FilterItemInfo> filters = arg0.getFilter().getFilterItems();
		String kh ="";
		String rq = "";
		String rqjs = "";
		String thgl = "";
		String jegl = "";
		String slgl = "";
		String pt = "";
		for (FilterItemInfo filterItem : filters) {
			switch (filterItem.getPropName()) {
			// 查询条件统计开始年份,标识如不一致,请修改
			case "yd_khgl":
				kh = (filterItem.getValue() == null) ? null :String.valueOf(((DynamicObject)filterItem.getValue()).getPkValue()) ;
				break;
			case "yd_rqgl":
				rq = (filterItem.getDate() == null) ? null
						: (String) new SimpleDateFormat("yyyy-MM-dd").format(filterItem.getDate());
				break;
			case "yd_rqgljs":
				rqjs = (filterItem.getDate() == null) ? null
						: (String) new SimpleDateFormat("yyyy-MM-dd").format(filterItem.getDate());
				break;
			case "yd_thgl":
				thgl = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			case "yd_jegl":
				jegl = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			case "yd_slgl":
				slgl = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			case "yd_pt":
				pt = (filterItem.getString() == null) ? null
						: filterItem.getString();
				break;
			default:
				break;
			}
		}
		// ksrq="2020-09-01";
		// jsrq="2020-09-30";
		QFilter filterrq = QFilter.of("yd_datefield_fhrq >= ?", rq);// 日期
		QFilter filterrqjs = QFilter.of("yd_datefield_fhrq <= ?", rqjs);// 日期结束
		QFilter filterpcck = QFilter.of("yd_pcck =?", "0");// 排除仓库为否
		QFilter filterpcwl = QFilter.of("yd_checkboxfield_khwdyzz =?", "0");// 排除物料为否
		QFilter filterpbcl = QFilter.of("yd_checkboxfield_bcl =?", "0");// 排除不处理为否
		long userid=Long.valueOf(RequestContext.get().getUserId());
		QFilter filterqx =PermissionServiceHelper.getDataPermission(userid,"im","yd_fhmxb"); //获取当前用户发货明细的数据过滤规则
		//QFilter filterSh = QFilter.of("status=?", "C");
		QFilter[] Filter = new QFilter[] { filterrq,filterrqjs,filterqx,filterpcck,filterpcwl,filterpbcl };
		// 查询非品牌分单的排除物料
		// 查询排除物料
		QFilter filter6 = QFilter.of("yd_lx =?", "2");// 非品牌分单
		DataSet pcwl = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl",
				new QFilter[] { filter6 }, null);
		List<String> pcwlin = new ArrayList<String>();
		for (Row row : pcwl) {
			pcwlin.add(row.getString("pcwl"));
		}
		// 查询品牌分单的排除物料
		// 查询排除物料
		filter6 = QFilter.of("yd_lx =?", "1");// 品牌分单
		DataSet pcwlypp = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl",
				new QFilter[] { filter6 }, null);
		List<String> pcwlinpp = new ArrayList<String>();
		for (Row row : pcwlypp) {
			pcwlinpp.add(row.getString("pcwl"));
		}
		// 查询按照品牌分单店铺
		QFilter filter1 = QFilter.of("yd_entryentity.yd_ppfd =?", 1);
		DataSet ppfdcx = QueryServiceHelper.queryDataSet("jy", "yd_khdygx", "yd_entryentity.yd_textfield ppfd",
				new QFilter[] { filter1 }, null);
		List<String> ppfd = new ArrayList<String>();
		for (Row row : ppfdcx) {
			ppfd.add(row.getString("ppfd"));
		}
		QFilter filter2 = new QFilter("yd_textfield_dpbh", QCP.in, ppfd);// 只取品牌分单店铺
		QFilter filter3 = new QFilter("yd_textfield_dpbh", QCP.not_in, ppfd);// 不取品牌分单店铺
		QFilter filter4 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.in, pcwlin);// 排除物料非品牌分单
		QFilter filter5 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.in, pcwlinpp);// 排除物料品牌分单
		// 查询当前过滤条件汇总的发货明细信息
		DataSet fhmx = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_fhmxb",
				"yd_combofield_pt pt,yd_checkboxfield_th th,yd_textfield_dpbh dp,yd_entryentity.yd_decimalfield_sl sl,yd_entryentity.yd_decimalfield_zje je",
				Filter, null).groupBy(new String[] { "dp", "th", "pt" }).sum("sl").sum("je").finish();
		//查询品牌物料排除数量
		DataSet fhmxpcpp = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_fhmxb",
				"yd_combofield_pt pt,yd_checkboxfield_th th,yd_textfield_dpbh dp,yd_entryentity.yd_decimalfield_sl pcsl",
				new QFilter[] { filterrq,filterrqjs,filterqx,filterpcck,filterpcwl,filterpbcl,filter2,filter5 }, null).groupBy(new String[] { "dp", "th", "pt"}).sum("pcsl").finish();
		//查询非品牌物料排除数量
		DataSet fhmxpc = QueryServiceHelper.queryDataSet(this.getClass().getName(), "yd_fhmxb",
				"yd_combofield_pt pt,yd_checkboxfield_th th,yd_textfield_dpbh dp,yd_entryentity.yd_decimalfield_sl pcsl",
				new QFilter[] { filterrq,filterrqjs,filterqx,filterpcck,filterpcwl,filterpbcl,filter3,filter4 }, null).groupBy(new String[] { "dp", "th", "pt"}).sum("pcsl").finish();
		fhmxpc=fhmxpc.union(fhmxpcpp);
		//fhmxpc.print(true);
		// 查询当前过滤条件的发货明细信息一共多少单据并合计运费
		DataSet fhmxzs = QueryServiceHelper
				.queryDataSet(this.getClass().getName(), "yd_fhmxb",
						"yd_combofield_pt pt,yd_checkboxfield_th th,yd_textfield_dpbh dp,yd_decimalfield_yf yf", Filter, null)
				.groupBy(new String[] { "dp", "th", "pt" }).sum("yf").finish();
		//fhmxzs.print(true);
		fhmx = fhmx.join(fhmxzs).on("dp", "dp").on("th", "th").select("dp", "th", "pt", "sl", "je+yf je").finish()
				.leftJoin(fhmxpc).on("dp", "dp").on("th", "th").select("dp", "th", "pt", "sl", "case pcsl when null then 0 else pcsl end pcsl","je").finish();;
		//fhmx.copy().print(true);
		//查询店铺对应客户
		List<String> xs = new ArrayList<String>();
		xs.add("1");
		xs.add("2");
		List<String> qt = new ArrayList<String>();
		qt.add("4");
		qt.add("5");
		QFilter filterxs = new QFilter("yd_entryentity.yd_combofield_hzcl", QCP.in, xs);//销售出库
		QFilter[] Filterdyxs = new QFilter[] { filterxs};
		QFilter filterqt = new QFilter("yd_entryentity.yd_combofield_hzcl", QCP.in, qt);//其他出库
		QFilter[] Filterdyqt = new QFilter[] { filterqt};
		DataSet dykh = QueryServiceHelper
				.queryDataSet(this.getClass().getName(), "yd_khdygx",
						"yd_combofield_pt pt,yd_entryentity.yd_textfield dp,yd_entryentity.yd_basedatafield kh", Filterdyxs, null);
		DataSet dykhqt = QueryServiceHelper
				.queryDataSet(this.getClass().getName(), "yd_khdygx",
						"yd_combofield_pt pt,yd_entryentity.yd_textfield dp,yd_entryentity.yd_basedatafield kh", Filterdyqt, null);
		//dykh.copy().print(true);
		DataSet fhmxxs=fhmx.copy().join(dykh).on("dp", "dp").on("pt","pt").select("th", "pt", "sl", "je", "kh","pcsl").finish().groupBy(new String[] { "th", "pt","kh" }).sum("sl").sum("je").sum("pcsl").finish();
		DataSet fhmxqt=fhmx.copy().join(dykhqt).on("dp", "dp").on("pt","pt").select("th", "pt", "sl", "je", "dp","pcsl").finish().groupBy(new String[] { "th", "pt","dp" }).sum("sl").sum("je").sum("pcsl").finish();
		//fhmx.print(true);
		// 查询对应出库单号的汇总销售出库信息
		filterrq = QFilter.of("biztime >= ?", rq);// 日期
		filterrqjs = QFilter.of("biztime <= ?", rqjs);// 日期结束
		QFilter[] Filterck = new QFilter[] { filterrq,filterrqjs };
		DataSet xsck = QueryServiceHelper
				.queryDataSet(this.getClass().getName(), "im_saloutbill",
						"yd_dzdkh kh,billtype.name djlx,invscheme.transceivertype.bizdirection fx,abs(billentry.qty) cqsl,abs(billentry.amountandtax) cqje", Filterck, null)
				.groupBy(new String[] { "kh","djlx","fx" }).sum("cqsl").sum("cqje").finish().select("kh","djlx","case  fx when '0' then false else true end fx" ,"cqsl","cqje");
		//xsck.print(true);
		// 查询对应出库单号的汇总其他出库信息
		DataSet qtck = QueryServiceHelper
				.queryDataSet(this.getClass().getName(), "im_otheroutbill",
						"billentry.yd_store dp,billtype.name djlx,invscheme.transceivertype.bizdirection fx,abs(billentry.qty) cqsl,abs(billentry.amount) cqje", Filterck, null)
				.groupBy(new String[] { "dp","djlx","fx" }).sum("cqsl").sum("cqje").finish().select("dp","djlx","case  fx when '0' then false else true end fx" ,"cqsl","cqje");;
		// 合并销售出库与其他出库信息
		//qtck.print(true);
		//DataSet ck = xsck.union(qtck);
		DataSet fhmxglxs=fhmxxs.leftJoin(xsck).on("kh", "kh").on("th", "fx").select( "pt yd_mxpt","djlx yd_djlx","kh yd_kh", "th yd_th", "sl yd_cqzsl",
				"je yd_cqzje", "cqsl yd_cqckzsl", "pcsl yd_tczsl","cqje yd_cqckzje","case when (sl-pcsl)=cqsl then false else true end yd_sjcw",
				"case when je=cqje then false else true end yd_jesjcw").finish().addField("'/'", "yd_dp");
		DataSet fhmxglqt=fhmxqt.leftJoin(qtck).on("dp", "dp").on("th", "fx").select( "pt yd_mxpt","djlx yd_djlx","0 yd_kh", "th yd_th", "sl yd_cqzsl",
				"je yd_cqzje", "cqsl yd_cqckzsl", "pcsl yd_tczsl","cqje yd_cqckzje","case when (sl-pcsl)=cqsl then false else true end yd_sjcw",
				"case when je=cqje then false else true end yd_jesjcw","dp yd_dp").finish();
		fhmx = fhmxglxs.union(fhmxglqt);
		//fhmx.copy().print(true);
		if(kh!=null)
		{
		fhmx=fhmx.filter("yd_kh="+kh);
		}
		if(thgl.equals("1"))
		{
		fhmx=fhmx.filter("yd_th=true");
		}
		if(thgl.equals("0"))
		{
		fhmx=fhmx.filter("yd_th=false");
		}
		if(slgl.equals("1"))
		{
		fhmx=fhmx.filter("yd_sjcw=false");
		}
		if(slgl.equals("0"))
		{
		fhmx=fhmx.filter("yd_sjcw=true");
		}
		if(jegl.equals("1"))
		{
		fhmx=fhmx.filter("yd_jesjcw=false");
		}
		if(jegl.equals("0"))
		{
		fhmx=fhmx.filter("yd_jesjcw=true");
		}
		if(!pt.equals("0"))
		{
		fhmx=fhmx.filter("yd_mxpt='"+pt+"'");
		}
//		fhmx = fhmx.leftJoin(ck).on("dp", "dp").on("th", "th")
//				.select("dp", "pt", "kh", "th", "sl", "pcsl", "je", "count", "cqsl", "cqje", "cqcount").finish().leftJoin(rhzfh)
//				.on("dp", "dp").on("th", "th")
//				.select("pt yd_mxpt", "dp yd_dpbh", "th yd_th", "hzzs yd_ptzs", "hzsl yd_ptzsl", "hzje yd_ptzje", "kh yd_kh",
//						"count yd_cqzs", "sl yd_cqzsl", "pcsl yd_pcsl", "je yd_cqzje", "cqcount yd_cqckzs", "cqsl yd_cqckzsl",
//						"cqje yd_cqckzje","case when hzzs=count and hzsl=sl and hzje= je and hzsl=abs(cqsl)+pcsl and hzje= abs(cqje) and sl=abs(cqsl)+pcsl and je= abs(cqje)  then false else true end yd_sjcw")
//				.finish().orderBy(new String[] {"yd_kh","yd_th desc","yd_dpbh"});
		return fhmx;
	}

}
