package kd.bos.yd.tcyp;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.EventObject;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;

import org.apache.commons.lang3.StringUtils;

import kd.bos.algo.DataSet;
import kd.bos.bill.BillShowParameter;
import kd.bos.bill.OperationStatus;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.events.SetFilterEvent;
import kd.bos.list.BillList;
import kd.bos.list.IListView;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.ORM;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.yd.tcyp.utils.OperationLogUtil;
import kd.bos.dlock.DLock;
import kd.bos.dlock.DLockInfo;
import java.util.Map;

public class fhmxListPlugin extends AbstractListPlugin {
//	String dh = null;

	@Override
	public void beforeBindData(EventObject e) {
		super.beforeBindData(e);
		FormShowParameter showParameter = this.getView().getFormShowParameter();
//		dh = showParameter.getCustomParam("billno");

		// TODO 在此添加业务逻辑
	}

	@Override
	public void setFilter(SetFilterEvent e) {
		super.setFilter(e);
		FormShowParameter showParameter = this.getView().getFormShowParameter();
		Map<String, Object> value = showParameter.getCustomParams();
		if(value != null) {
			Boolean isJoinQuery = (Boolean) value.get("isJoinQuery");
			if(isJoinQuery != null && isJoinQuery) {
				List<String> billnos = (List<String>) value.get("billno");
				if(billnos != null && billnos.size() > 0) {
					// 过滤掉空的billno
					List<String> validBillnos = new ArrayList<>();
					for(String billno : billnos) {
						if(StringUtils.isNotBlank(billno)) {
							validBillnos.add(billno);
						}
					}
					if(validBillnos.size() > 0) {
						e.getQFilters().clear();
						e.getQFilters().add(new QFilter("billno", QCP.in, validBillnos));
					}
				}
			}
		}
	}

	// @Override
	// public void setFilter(SetFilterEvent e) {
	// // 设置过滤，也可使用e.setCustomQFilters()，可设置多条件。   
	// if (dh != null) {
	// List<QFilter> qfilters = e.getQFilters();
	// QFilter qFilter = new QFilter("billno", QCP.equals, dh);
	// qfilters.add(qFilter);// 设置排序 Asc升序 Desc降序 ，多条件可用逗号连接，排序顺序按字符串顺序        
	// // e.setOrderBy("billstatus Asc,date Desc");
	// // 先按单据状态升序，再按日期降序    }
	// }
	// }

	@Override
	public void itemClick(ItemClickEvent evt) {
		// TODO Auto-generated method stub
		super.itemClick(evt);
		String pname = evt.getItemKey();
		switch (pname) {
		case "yd_baritemap_cx":
			String formId = ((IListView) this.getView()).getBillFormId();
			BillList billList = this.getControl("billlistap");
			// 获取选中行记录,注意:得到的结果是记录的主键ID的集合
			ListSelectedRowCollection listSelectedRowCol = billList.getSelectedRows();
			if (listSelectedRowCol != null && listSelectedRowCol.size() > 0) {
				Long tempRowDataId = (Long) listSelectedRowCol.get(0).getPrimaryKeyValue();
				for (int i = listSelectedRowCol.size() - 1; i >= 0; i--) {
					if (listSelectedRowCol.get(i).getPrimaryKeyValue().equals(tempRowDataId)) {
						listSelectedRowCol.remove(i);
					}
				}
				if (listSelectedRowCol.size() > 0) {
					this.getView().showMessage("只能选中一行进行查询!");
					return;
				}
				DynamicObject tempRowData = BusinessDataServiceHelper.loadSingle(tempRowDataId, formId,
						"yd_textfield_xsckdh,yd_combofield_xyd");
				String xydh = tempRowData.getString("yd_textfield_xsckdh");
				String lx = tempRowData.getString("yd_combofield_xyd");
				if (xydh != "" && lx != "") {
					String xylx = "im_saloutbill";
					if (lx.equals("2")) {
						xylx = "im_otheroutbill";
					}
					// 查询下游单据id
					DynamicObject xydj = BusinessDataServiceHelper.loadSingle(xylx, "id",
							new QFilter[] { new QFilter("billno", QCP.equals, xydh) });
					String id = xydj.getString("id");
					BillShowParameter billShowParameter = new BillShowParameter();
					billShowParameter.setFormId(xylx);
					billShowParameter.setPkId(id);
					billShowParameter.getOpenStyle().setShowType(ShowType.Modal);
					billShowParameter.setStatus(OperationStatus.VIEW);
					// billShowParameter.setCloseCallBack(new CloseCallBack(this, "customShow"));
					this.getView().showForm(billShowParameter);
				} else {
					this.getView().showMessage("选中单据没有下游单号或者下游单据类型.请重新选择!");
				}
			} else {
				this.getView().showMessage("请选中行再点击按钮!");
			}
			break;
		case "yd_baritemap_e3Order":
			// // 弹出动态表单页面标识
			// private static final String KEY_POP_FORM = "yd_dates";
			// // 页面天数控件标识
			// private static final String KEY_LEAVE_DAYS = "day";
			// 创建弹出页面对象，FormShowParameter表示弹出页面为动态表单
			FormShowParameter ShowParameter = new FormShowParameter();
			// 设置弹出页面的编码
			ShowParameter.setFormId("yd_dates");
			// 设置弹出页面标题
			ShowParameter.setCloseCallBack(new CloseCallBack(this, "E3Order"));
			// 设置弹出页面打开方式，支持模态，新标签等
			ShowParameter.getOpenStyle().setShowType(ShowType.Modal);
			// 弹出页面对象赋值给父页面
			this.getView().showForm(ShowParameter);
			break;
		case "yd_baritemap_e3Return":
			// // 弹出动态表单页面标识
			// private static final String KEY_POP_FORM = "yd_dates";
			// // 页面天数控件标识
			// private static final String KEY_LEAVE_DAYS = "day";
			// 创建弹出页面对象，FormShowParameter表示弹出页面为动态表单
			FormShowParameter ShowParameterReturn = new FormShowParameter();
			// 设置弹出页面的编码
			ShowParameterReturn.setFormId("yd_rqcs");
			// 设置弹出页面标题
			ShowParameterReturn.setCloseCallBack(new CloseCallBack(this, "E3Return"));
			// 设置弹出页面打开方式，支持模态，新标签等
			ShowParameterReturn.getOpenStyle().setShowType(ShowType.Modal);
			// 弹出页面对象赋值给父页面
			this.getView().showForm(ShowParameterReturn);
			break;
		case "yd_baritemap_hb":
			FormShowParameter ShowParameterhd = new FormShowParameter();
			// 设置弹出页面的编码
			ShowParameterhd.setFormId("yd_rqcs");
			// 设置弹出页面标题
			ShowParameterhd.setCloseCallBack(new CloseCallBack(this, "HD"));
			// 设置弹出页面打开方式，支持模态，新标签等
			ShowParameterhd.getOpenStyle().setShowType(ShowType.Modal);
			// 弹出页面对象赋值给父页面
			this.getView().showForm(ShowParameterhd);
			break;
		case "yd_baritemap_rfhhz":
			FormShowParameter ShowParameterrfhhz = new FormShowParameter();
			// 设置弹出页面的编码
			ShowParameterrfhhz.setFormId("yd_rqcs");
			// 设置弹出页面标题
			ShowParameterrfhhz.setCloseCallBack(new CloseCallBack(this, "RFHHZ"));
			// 设置弹出页面打开方式，支持模态，新标签等
			ShowParameterrfhhz.getOpenStyle().setShowType(ShowType.Modal);
			// 弹出页面对象赋值给父页面
			this.getView().showForm(ShowParameterrfhhz);
			break;
		case "yd_mye3":
			FormShowParameter ShowParameterMY = new FormShowParameter();
			// 设置弹出页面的编码
			ShowParameterMY.setFormId("yd_dates");
			// 设置弹出页面标题
			ShowParameterMY.setCloseCallBack(new CloseCallBack(this, "MYE3Order"));
			// 设置弹出页面打开方式，支持模态，新标签等
			ShowParameterMY.getOpenStyle().setShowType(ShowType.Modal);
			// 弹出页面对象赋值给父页面
			this.getView().showForm(ShowParameterMY);
			break;
		case "yd_easzttb":
			// 创建弹出页面对象，FormShowParameter表示弹出页面为动态表单
			FormShowParameter ShowParameterReturneaszttb = new FormShowParameter();
			// 设置弹出页面的编码
			ShowParameterReturneaszttb.setFormId("yd_rqcs");
			// 设置弹出页面标题
			ShowParameterReturneaszttb.setCloseCallBack(new CloseCallBack(this, "easzttb"));
			// 设置弹出页面打开方式，支持模态，新标签等
			ShowParameterReturneaszttb.getOpenStyle().setShowType(ShowType.Modal);
			// 弹出页面对象赋值给父页面
			this.getView().showForm(ShowParameterReturneaszttb);
			break;
		case "yd_mye3th":
			// 创建弹出页面对象，FormShowParameter表示弹出页面为动态表单
			FormShowParameter ShowParameterReturnMY = new FormShowParameter();
			// 设置弹出页面的编码
			ShowParameterReturnMY.setFormId("yd_rqcs");
			// 设置弹出页面标题
			ShowParameterReturnMY.setCloseCallBack(new CloseCallBack(this, "MYE3Return"));
			// 设置弹出页面打开方式，支持模态，新标签等
			ShowParameterReturnMY.getOpenStyle().setShowType(ShowType.Modal);
			// 弹出页面对象赋值给父页面
			this.getView().showForm(ShowParameterReturnMY);
			break;
		case "yd_myrfhhz":
			ApiFf.MYE3Rfhhz("1", "2021-08-24");
			ApiFf.MYE3Rfhhz("2", "2021-08-24");
			break;
		}
	}

	// public void filterContainerInit(FilterContainerInitArgs args) {
	// // TODO Auto-generated method stub
	// super.filterContainerInit(args);
	// //args.getFastFilterColumns().clear();
	// List<FilterColumn> fastFilter = args.getFastFilterColumns();
	// args.getCommonFilterColumns();
	// List<FilterColumn> sColumns = args.getSchemeFilterColumns();
	// SchemeFilterColumn sFilter = (SchemeFilterColumn) sColumns.get(1);
	// List<ComboItem> cItems = sFilter.getComboItems();
	// cItems.remove(0);
	// sFilter.setComboItems(cItems);
	//// ListShowParameter listShowParameter = (ListShowParameter)
	// this.getView().getFormShowParameter();
	//// if (listShowParameter.isLookUp()) return;
	//// long orgid = RequestContext.get().getOrgId();
	//
	// }
	@Override
	public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
		super.closedCallBack(closedCallBackEvent);
		if (StringUtils.equals(closedCallBackEvent.getActionId(), "E3Order")
				&& null != closedCallBackEvent.getReturnData()) {
			// 这里返回对象为Object，可强转成相应的其他类型，
			// 单条数据可用String类型传输，返回多条数据可放入map中，也可使用json等方式传输
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			Calendar rightNow = Calendar.getInstance();
			rightNow.setTime(dt);
			String dp = returnData.get("yd_textfield_dp");
			for (int s = 0; s <= 23; s++) {
				String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				rightNow.add(Calendar.HOUR, 1);
				String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				// 1、获取日志微服务接口
				ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
				// 2、构建日志信息，参考示例如下
				AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "手动拉单启动开始", "sm", "yd_fhmxb");
				// 3、记录操作日志
				logService1.addLog(logInfo1);
				E3Post.E3OrderListGet(ks, js, dp);
				logInfo1 = OperationLogUtil.buildLogInfo("保存", "手动拉单启动结束", "sm", "yd_fhmxb");
				// 3、记录操作日志
				logService1.addLog(logInfo1);
				// System.out.println(ks);
				// System.out.println(js);
			}

		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "E3Return")
				&& null != closedCallBackEvent.getReturnData()) {
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			Calendar rightNow = Calendar.getInstance();
			rightNow.setTime(dt);
			for (int s = 0; s <= 23; s++) {
				String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				rightNow.add(Calendar.HOUR, 1);

				String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				ApiFf.E3ReturnListGet(ks, js);
				// System.out.println(ks);
				// System.out.println(js);
			}
		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "HD")
				&& null != closedCallBackEvent.getReturnData()) {
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			String ks = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
			// 查数据是否已经被锁，如果被锁则报错返回
			// 如果没有被锁，则对数据进行加锁并继续向后执行 modify by mxp
        	DLock lock;
			BillList list = this.getControl("billlistap");
			ListSelectedRowCollection selectedRows = list.getSelectedRows();
			// 如果不选择数据，只填入日期，则需以该日期为条件查询是否数据被锁（或锁住该日期所有的数据）
			if (selectedRows.size()==0) {
				List<QFilter> Filters = new LinkedList<>();
				Filters.add(new QFilter("yd_datefield_fhrq", QCP.equals,dt));
		        DataSet selectBillDataSet = QueryServiceHelper.queryDataSet(fhmxListPlugin.class.getName(), "yd_fhmxb",
		                "id", Filters.toArray(new QFilter[Filters.size()]), "id");
		        ORM selectOrm = ORM.create();
		        DynamicObjectCollection selectBills = selectOrm.toPlainDynamicObjectCollection(selectBillDataSet);
		        for (DynamicObject selectBill : selectBills) {
		        	Long id = selectBill.getLong("id");
		        	String rowid = id.toString();
					DLockInfo info = DLock.getLockInfo(rowid);
					if(null!=info) {
						this.getView().showMessage("所选的数据已被其他人锁定，请确认后重试！");
						return;
					}else {
			        	lock = DLock.create(rowid);
						lock.fastMode();
						lock.lock();
					}
		        }
			}else {
				Object[] pks = selectedRows.getPrimaryKeyValues();
				for (Object pk: pks) {
					Long rowid = (Long) pk;
					DLockInfo info = DLock.getLockInfo(rowid.toString());
					if(null!=info) {
						//this.getView().close();
						this.getView().showMessage("所选的数据已被其他人锁定，请确认后重试！");
						return;
						//DLock.forceUnlock(rowid.toString());
					}else {
						lock = DLock.create(rowid.toString());
						lock.fastMode();
						lock.lock();
					}
				}
			}
			// 查询直营店的排除物料
			List<String> pcwlin = DeliveryHelper.getExcludeMaterial(0, "2"); // 不是运费补差且直营店
			List<String> fywl = DeliveryHelper.getExcludeMaterial(1, "2"); // 费用物料且直营店
			// 查询一盘货的排除物料
			List<String> pcwlinpp = DeliveryHelper.getExcludeMaterial(0, "1"); // 不是运费补差且一盘货
			List<String> fywlpp = DeliveryHelper.getExcludeMaterial(1, "1"); // 费用物料且一盘货
			// 查询直营店一盘货的排除物料
			List<String> pcwlindir = DeliveryHelper.getExcludeMaterial(0, "3"); // 不是运费补差且直营店一盘货
			List<String> fywldir = DeliveryHelper.getExcludeMaterial(1, "3"); // 费用物料且直营店一盘货
			// update by hst 2023/03/28 获取客户对应关系表中已维护为需要分摊运费的客户
			List<String> shareClient = DeliveryHelper.getSharedFareClients();

			// 货品品牌店铺
			List<String> ppfd = DeliveryHelper.getBrandStore();

			// 汇总直营店的所有排除物料
			List<String> pcwlhz = new ArrayList<>();
			// 汇总直营店的所有排除物料
			List<String> pcwldirhz = new ArrayList<>();
			// 汇总所有品牌的所有排除物料
			List<String> pcwlhzpp = new ArrayList<>();
			pcwlhz.addAll(pcwlin);
			pcwlhz.addAll(fywl);

			pcwldirhz.addAll(pcwlindir);
			pcwldirhz.addAll(fywldir);

			pcwlhzpp.addAll(pcwlinpp);
			pcwlhzpp.addAll(fywlpp);

			List<String> platList = new ArrayList<>();
			platList.add("1");
			platList.add("2");
			platList.add("3");

			QFilter filterPlat = new QFilter("yd_combofield_pt", QCP.in, platList);// 平台

			QFilter filter1 = QFilter.of("yd_datefield_fhrq =?", ks);
			filter1.and(filterPlat);

			String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(new Date());
			QFilter dateFilter = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间小于今天

			ApiFf.jy(filter1, pcwlhz, pcwldirhz, pcwlhzpp, ppfd, null, dateFilter);

			// add by lzp, 新增直营店一盘货的结算方式的处理方式
			long start = System.currentTimeMillis();
			ApiFf.bchbdj_direct("1", pcwlindir, fywldir, ppfd, ks,filterPlat,dateFilter); // 包含正常汇总和不汇总的情况
			long finish = System.currentTimeMillis();
			long timeElapsed = finish - start;
			System.out.println("直营店一盘货耗时：" + timeElapsed);

			// update by hst 2023/03/28 增加需要分摊运费的客户
			start = System.currentTimeMillis();
			ApiFf.bchbdj("1", pcwlin, fywl, ppfd, ks,filterPlat,dateFilter,shareClient);
			finish = System.currentTimeMillis();
			timeElapsed = finish - start;
			System.out.println("直营店正常发货汇总耗时：" + timeElapsed);

			start = System.currentTimeMillis();
			ApiFf.bchbdj("4", pcwlin, fywl, ppfd, ks,filterPlat,dateFilter,shareClient);
			finish = System.currentTimeMillis();
			timeElapsed = finish - start;
			System.out.println("直营店转其他出库汇总耗时：" + timeElapsed);

			start = System.currentTimeMillis();
			ApiFf.bcbhzdj("2", pcwlin, fywl, ppfd, ks,filterPlat,dateFilter,shareClient);
			finish = System.currentTimeMillis();
			timeElapsed = finish - start;
			System.out.println("直营店正常发货不汇总耗时：" + timeElapsed);

			start = System.currentTimeMillis();
			ApiFf.bcbhzdj("5", pcwlin, fywl, ppfd, ks,filterPlat,dateFilter,shareClient);
			finish = System.currentTimeMillis();
			timeElapsed = finish - start;
			System.out.println("直营店转其他出库不汇总耗时：" + timeElapsed);

			start = System.currentTimeMillis();
			ApiFf.bchbdjby("1", pcwlinpp, fywlpp, ppfd, ks,filterPlat,dateFilter);
			finish = System.currentTimeMillis();
			timeElapsed = finish - start;
			System.out.println("经销商正常发货汇总耗时：" + timeElapsed);

			start = System.currentTimeMillis();
			ApiFf.bchbdjby("4", pcwlinpp, fywlpp, ppfd, ks,filterPlat,dateFilter);
			finish = System.currentTimeMillis();
			timeElapsed = finish - start;
			System.out.println("经销商转其他出库汇总耗时：" + timeElapsed);

			ApiFf.khdyzzjy(filter1);
			// 逻辑处理后需要对锁进行解锁 modify by mxp
			if(selectedRows.size()==0) {
				List<QFilter> Filters = new LinkedList<>();
				Filters.add(new QFilter("yd_datefield_fhrq", QCP.equals,dt));
		        DataSet selectBillDataSet = QueryServiceHelper.queryDataSet(fhmxListPlugin.class.getName(), "yd_fhmxb",
		                "id", Filters.toArray(new QFilter[Filters.size()]), "id");
		        ORM selectOrm = ORM.create();
		        DynamicObjectCollection selectBills = selectOrm.toPlainDynamicObjectCollection(selectBillDataSet);
		        for (DynamicObject selectBill : selectBills) {
		        	Long id = selectBill.getLong("id");
		        	String rowid = id.toString();
					DLockInfo info = DLock.getLockInfo(rowid);
					if(null!=info) {
						DLock.forceUnlock(rowid);
					}
		        }			
			}else {
				Object[] pks = selectedRows.getPrimaryKeyValues();
				for (Object pk: pks) {
					Long rowid = (Long) pk;
					DLockInfo info = DLock.getLockInfo(rowid.toString());
					if(null!=info) {
						DLock.forceUnlock(rowid.toString());
					}		
				}				
			}
			
		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "RFHHZ")
				&& null != closedCallBackEvent.getReturnData()) {
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			String ks = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
			E3Post.E3Rfhhz("1", ks);
			E3Post.E3Rfhhz("2", ks);
		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "MYE3Order")
				&& null != closedCallBackEvent.getReturnData()) {
			// 这里返回对象为Object，可强转成相应的其他类型，
			// 单条数据可用String类型传输，返回多条数据可放入map中，也可使用json等方式传输
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			Calendar rightNow = Calendar.getInstance();
			rightNow.setTime(dt);
			String dp = returnData.get("yd_textfield_dp");
			for (int s = 0; s <= 23; s++) {
				String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				rightNow.add(Calendar.HOUR, 1);
				String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				//ApiFf.MYE3OrderListGet(ks, js, dp);
			}
		} else if (StringUtils.equals(closedCallBackEvent.getActionId(), "MYE3Return")
				&& null != closedCallBackEvent.getReturnData()) {
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			Calendar rightNow = Calendar.getInstance();
			rightNow.setTime(dt);
			for (int s = 0; s <= 23; s++) {
				String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				rightNow.add(Calendar.HOUR, 1);

				String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				ApiFf.MYE3ReturnListGet(ks, js);
				// System.out.println(ks);
				// System.out.println(js);
			}
		}else if (StringUtils.equals(closedCallBackEvent.getActionId(), "easzttb")
				&& null != closedCallBackEvent.getReturnData()) {
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			String ks = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
			E3FhEas.gxeaszt(ks,ks);
		}else if (StringUtils.equals(closedCallBackEvent.getActionId(), "HD") 
				&& null != closedCallBackEvent.getReturnData()) {
			// 点击退出也需要判断并解锁 modify by mxp
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			BillList list = this.getControl("billlistap");
			ListSelectedRowCollection selectedRows = list.getSelectedRows();
			if (selectedRows.size()==0) {
				List<QFilter> Filters = new LinkedList<>();
				Filters.add(new QFilter("yd_datefield_fhrq", QCP.equals,dt));
		        DataSet selectBillDataSet = QueryServiceHelper.queryDataSet(fhmxListPlugin.class.getName(), "yd_fhmxb",
		                "id", Filters.toArray(new QFilter[Filters.size()]), "id");
		        ORM selectOrm = ORM.create();
		        DynamicObjectCollection selectBills = selectOrm.toPlainDynamicObjectCollection(selectBillDataSet);
		        for (DynamicObject selectBill : selectBills) {
		        	Long id = selectBill.getLong("id");
		        	String rowid = id.toString();
					DLockInfo info = DLock.getLockInfo(rowid);
					if(null!=info) {
						DLock.forceUnlock(rowid);
					}
		        }
			}else {
				Object[] pks = selectedRows.getPrimaryKeyValues();
				for (Object pk: pks) {
					Long rowid = (Long) pk;
					DLockInfo info = DLock.getLockInfo(rowid.toString());
					if(null!=info) {
						DLock.forceUnlock(rowid.toString());
					}		
				}		
			}				
		}
	}
}
