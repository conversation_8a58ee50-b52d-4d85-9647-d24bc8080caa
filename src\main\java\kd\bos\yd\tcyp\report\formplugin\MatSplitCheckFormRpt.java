package kd.bos.yd.tcyp.report.formplugin;

import kd.bos.entity.report.FilterItemInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.exception.KDBizException;
import kd.bos.report.events.SortAndFilterEvent;
import kd.bos.report.plugin.AbstractReportFormPlugin;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 主商品拆分核对
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-11-18
 */
public class MatSplitCheckFormRpt extends AbstractReportFormPlugin {

    @Override
    public void setSortAndFilter(List<SortAndFilterEvent> list) {
        super.setSortAndFilter(list);
        for (SortAndFilterEvent event : list) {
            //if ("name".equals(event.getColumnName()) || "number".equals(event.getColumnName())) {
            event.setSort(true);
            event.setFilter(true);
            //}
        }
    }

    /**
     * 查询增加时间校验（不能超过31天）
     * @author: hst
     * @createDate: 2022/11/08
     * @param queryParam
     */
    @Override
    public void beforeQuery(ReportQueryParam queryParam) {
        super.beforeQuery(queryParam);
        Date beginDate = null;
        Date endDate = null;
        List<FilterItemInfo> filters = queryParam.getFilter().getFilterItems();
        // 获取筛选条件中的日期
        for (FilterItemInfo filterItem : filters) {
            if ("yd_begindate".equals(filterItem.getPropName())) {
                beginDate = filterItem.getDate();
            } else if ("yd_enddate".equals(filterItem.getPropName())) {
                endDate = filterItem.getDate();
            }
        }
        if (Objects.isNull(beginDate)) {
            throw new KDBizException("请选择开始日期！");
        }
        if (Objects.isNull(endDate)) {
            throw new KDBizException("请选择结束日期！");
        }
        //校验结束时间是否小于开始时间
        if (endDate.compareTo(beginDate) < 0) {
            throw new KDBizException("结束日期不能小于开始日期！");
        }
        //计算时间差
        long beginTime = beginDate.getTime();
        long endTime = endDate.getTime();
        int days = (int) ((endTime - beginTime) / (1000 * 60 * 60 * 24));
        if (days >= 31) {
            throw new KDBizException("时间间隔不能超过31天，请缩小范围！");
        }
    }
}
