package kd.bos.yd.tcyp.report.queryplugin;

import kd.bos.algo.DataSet;
import kd.bos.entity.report.AbstractReportColumn;
import kd.bos.entity.report.AbstractReportListDataPlugin;
import kd.bos.entity.report.FilterInfo;
import kd.bos.entity.report.ReportQueryParam;
import kd.bos.orm.query.QFilter;
import kd.bos.yd.tcyp.utils.BizHelper;

import java.util.Date;
import java.util.List;

/**
 * 主商品拆分数据查询插件
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-11-18
 */
public class MatSplitCheckQueryRpt extends AbstractReportListDataPlugin {

    @Override
    public DataSet query(ReportQueryParam queryParam, Object o) throws Throwable {
        FilterInfo filter = queryParam.getFilter();
        Date beginDate = (Date) filter.getValue("yd_begindate");
        Date endDate = (Date) filter.getValue("yd_enddate");
        // 按发货日期根据开始日期和结束日期进行过滤，然后在过滤是组装品、审核的单据和分录为组装品的明细
        QFilter qFilter = QFilter.of("yd_datefield_fhrq >= ? and yd_datefield_fhrq <= ? and yd_ispackage = 1 and billstatus='C' and yd_entryentity.yd_mainmatnum != ''", beginDate, endDate);
        String orderBy = "yd_datefield_fhrq desc";
        // 查询发货明细
        DataSet dataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), "yd_fhmxb", "billno as billNumber, yd_combofield_pt as platform, yd_textfield_dpbh as storeNumber, yd_datefield_fhrq as sendDate, yd_entryentity.yd_mainmatnum as mainMatNum, yd_entryentity.yd_textfield_hpbh as spMatNum", qFilter.toArray(), orderBy);
        // 查询店铺对应关系
        QFilter storeQfilter = QFilter.of("billstatus = 'C'");
        DataSet storeDataSet = BizHelper.getQueryDataSet(BizHelper.getRunLocate(), "yd_khdygx", "yd_combofield_pt as platform,yd_entryentity.yd_textfield as storeNumber, yd_entryentity.yd_orgfield_dyzz.name as orgName", storeQfilter.toArray(), null);
        // 通过平台+物料编码进行关联
        return dataSet.leftJoin(storeDataSet).on("platform", "platform").on("storeNumber", "storeNumber").select(new String[] {"storeNumber as yd_storenumber", "billNumber as yd_billnumber", "mainMatNum as yd_mainmatnum", "spMatNum as yd_spmatnum"}, new String[]{"orgName as yd_orgname"}).finish();
    }

    @Override
    public List<AbstractReportColumn> getColumns(List<AbstractReportColumn> columns) throws Throwable {
        return super.getColumns(columns);
    }
}
