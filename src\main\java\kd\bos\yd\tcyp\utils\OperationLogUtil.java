package kd.bos.yd.tcyp.utils;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.utils.StringUtils;
import kd.bos.entity.AppInfo;
import kd.bos.entity.AppMetadataCache;
import kd.bos.log.api.AppLogInfo;
import kd.bos.servicehelper.TimeServiceHelper;

public class OperationLogUtil {
    public OperationLogUtil() {
    }

    public static AppLogInfo buildLogInfo(String opName, String opDescription, String bizAppId, String bizEntityNumber) {
        AppLogInfo logInfo = new AppLogInfo();
        logInfo.setUserID(Long.valueOf(RequestContext.get().getUserId()));
        String appId = "";
        if (StringUtils.isNotBlank(bizAppId) && !StringUtils.equalsIgnoreCase("bos", bizAppId)) {
            AppInfo appInfo = AppMetadataCache.getAppInfo(bizAppId);
            if (null != appInfo) {
                appId = appInfo.getId();
            }
        }

        logInfo.setBizAppID(appId);
        logInfo.setBizObjID(bizEntityNumber);
        logInfo.setOrgID(RequestContext.get().getOrgId());
        logInfo.setOpTime(TimeServiceHelper.now());
        logInfo.setClientType(RequestContext.get().getClient());
        logInfo.setClientIP(RequestContext.get().getLoginIP());
        logInfo.setClientName(RequestContext.get().getClient());
        logInfo.setOpName(opName);
        logInfo.setOpDescription(opDescription);
        return logInfo;
    }
}
