//package qimen.api;
//
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.PropertySource;
//import org.springframework.stereotype.Component;
//
//import com.qimencloud.api.QimenCloudClient;
//
//import com.taobao.api.ApiException;
//
//public class QimenStockoutUtil {
//	public WdtTradeQueryResponse getOrderLst(WdtTradeQuery query) {
//		WdtTradeQueryRequest req = new WdtTradeQueryRequest();
//		BeanUtils.copyProperties(query, req);
//		req.setTargetAppKey(targetAppKey);//旺店通的奇门appkey
//		req.setSid(sid);//旺店通erp的卖家账号
////		req.setSrcTid("959853920063383126");
////		req.setPageSize(100L);
////		req.setPageNo(0L);
////		req.setStartTime("2020-04-22 10:00:00");
////		req.setEndTime("2020-04-22 11:00:00");
//		try {
//			WdtTradeQueryResponse res = jmClient.execute(req);
////			System.out.println("code:"+ res.getErrorCode());
////			System.out.println("message:"+res.getMessage());
////			System.out.println("totalCount:"+res.getTotalCount());
////			System.out.println("body:"+res.getBody());
////			tranToByWmsOrders(res.getBody());
//
//
//			return res;
//		} catch (ApiException e) {
//			e.printStackTrace();
//		}
//		return null;
//	}
//}