package qimen.api;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qimencloud.api.DefaultQimenCloudClient;
import com.taobao.api.ApiException;

import kd.bos.context.RequestContext;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.exception.KDException;
import kd.bos.form.CloseCallBack;
import kd.bos.form.FormShowParameter;
import kd.bos.form.ShowType;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.schedule.executor.AbstractTask;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class TradQueryButtun extends AbstractListPlugin {
	private static String serverUrl = "https://hu3cgwt0tc.api.taobao.com/router/qm";// 奇门API地址
	private static String appKey = "29144852";// 奇门appkey 21363512 29144852
	private static String appSecret = "317447863c8eb56ad7aef8e891ff6018";// 奇门appkey对应的secret
	private static String format = "json";
	// 弹出动态表单页面标识
	private static final String KEY_POP_FORM = "yd_dates";
	// 页面天数控件标识
	private static final String KEY_LEAVE_DAYS = "day";

	private static String TEXT = "";

	@Override
	public void itemClick(ItemClickEvent evt) {
		String key = evt.getItemKey();
		if ("yd_qum".equals(key)) {
			// 创建弹出页面对象，FormShowParameter表示弹出页面为动态表单
			FormShowParameter ShowParameter = new FormShowParameter();
			// 设置弹出页面的编码
			ShowParameter.setFormId(KEY_POP_FORM);
			// 设置弹出页面标题
			ShowParameter.setCloseCallBack(new CloseCallBack(this, KEY_LEAVE_DAYS));
			// 设置弹出页面打开方式，支持模态，新标签等
			ShowParameter.getOpenStyle().setShowType(ShowType.Modal);
			// 弹出页面对象赋值给父页面
			this.getView().showForm(ShowParameter);
		}
	}

	@Override
	public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
		super.closedCallBack(closedCallBackEvent);
		if (StringUtils.equals(closedCallBackEvent.getActionId(), KEY_LEAVE_DAYS)
				&& null != closedCallBackEvent.getReturnData()) {
			// 这里返回对象为Object，可强转成相应的其他类型，
			// 单条数据可用String类型传输，返回多条数据可放入map中，也可使用json等方式传输
			HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
			SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date dt = null;
			String dp = returnData.get("yd_textfield_dp");
			TEXT = dp;
			try {
				dt = sdf.parse(returnData.get("yd_date_wang"));
			} catch (ParseException e) {
				e.printStackTrace();
			}
			Calendar rightNow = Calendar.getInstance();
			rightNow.setTime(dt);
			for (int s = 0; s <= 23; s++) {
				String ks = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				rightNow.add(Calendar.HOUR, 1);
				String js = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rightNow.getTime());
				DefaultQimenCloudClient client = new DefaultQimenCloudClient(serverUrl, appKey, appSecret, format);
				WdtTradeQueryRequest req = new WdtTradeQueryRequest();
				req.setTargetAppKey("21363512");// 旺店通的奇门appkey
				req.setSid("tcbj2");// 旺店通erp的卖家账号
				req.setStartTime(ks);
				req.setEndTime(js);
				req.setPageSize(100L);
				req.setPageNo(0L);

				try {
					WdtTradeQueryResponse res = client.execute(req);
					String response = res.getBody();
					Thread.sleep(1000);
					JSONObject jsonObject = JSONObject.parseObject(response);
					JSONObject one = jsonObject.getJSONObject("response");
					JSONArray TCBJ = one.getJSONArray("trades");
					Long msg = one.getLong("total_count");
					Long page_s = req.getPageSize();
					double page_size = page_s.intValue();
					double total_count = msg.intValue();
					double pageTotal = Math.ceil(total_count / page_size);
					// BillSave(TCBJ);
					Date kssj = new Date();
					for (int n = 1; n < pageTotal;) {
						req.setTargetAppKey("21363512");// 旺店通的奇门appkey
						req.setSid("tcbj2");// 旺店通erp的卖家账号
						req.setStartTime(ks);
						req.setEndTime(js);
						req.setPageSize(100L);
						Long dd = Long.valueOf(n);
						req.setPageNo(dd);
						try {
							res = client.execute(req);
							Thread.sleep(1000);
						} catch (InterruptedException e) {
							e.printStackTrace();
						}
						response = res.getBody();
						jsonObject = JSONObject.parseObject(response);
						one = jsonObject.getJSONObject("response");
						TCBJ = one.getJSONArray("trades");
						BillSave(TCBJ);
						n++;
					}
					// 记录保存单据之后的时间
					// Date jssj = new Date();
					// long hs = jssj.getTime() - kssj.getTime();
					// // 如果时间只差小于10000毫秒 则延时
					// if ((hs) < 1000) {
					// try {
					// Thread.sleep(1000 - hs);
					// } catch (InterruptedException e) {
					// e.printStackTrace();
					// }
					// }
				} catch (ApiException e) {
					e.printStackTrace();
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}
	}

	public void BillSave(JSONArray TCBJ) {
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < TCBJ.size(); i++) {
			JSONObject onecar = TCBJ.getJSONObject(i);
			String trade_no = onecar.getString("trade_no");// 订单编号
			String platform_id = onecar.getString("platform_id");// 平台ID
			String shop_no = onecar.getString("shop_no");// 店铺编号
			String warehouse_no = onecar.getString("warehouse_no");// 仓库编号
			String src_tids = onecar.getString("src_tids");// 交易号
			String warehouse = shop_no + "&" + warehouse_no;
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date time = new Date();// 下单时间
			String trade_time = onecar.getString("created");
			try {
				time = sdf.parse(trade_time);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			String post_amount = onecar.getString("post_amount");// 运费
			String trade_from = onecar.getString("trade_from");// 是否手工
			String invoice_id = onecar.getString("invoice_id");// 是否开票
			String refund_status = onecar.getString("refund_status");// 是否开票
			String djbh = "WDT_" + refund_status + "_" + trade_no + "_" + shop_no;
			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);// 查询单据编号是否存在
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno",
					new QFilter[] { qFilter });
			if (dObject == null) {// 如果单据编号存在，不进行保存
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
				bill.set("billno", djbh);
				bill.set("billstatus", "A");
				bill.set("yd_combofield_pt", "2");
				bill.set("yd_textfield_ddbh", trade_no);
				bill.set("yd_datetimefield_xdsj", time);
				bill.set("yd_textfield_dpbh", shop_no);
				bill.set("yd_textfield_ck", warehouse);
				bill.set("yd_dealcode", src_tids);
				if (invoice_id.equals(0)) {// 0表示未开发票，＞0表示已开发票
					bill.set("yd_checkboxfield_sfkp", false);
				} else {
					bill.set("yd_checkboxfield_sfkp", true);
				} // 订单来源 1API抓单，2手工建单 3excel导入 4现款销售
				if (trade_from.equals(2)) { // 如果等于2为手工建单
					bill.set("yd_checkboxfield_sfsg", true);
				} else {
					bill.set("yd_checkboxfield_sfsg", false);
				}
				bill.set("yd_decimalfield_yf", post_amount);
				Date times = new Date();
				bill.set("createtime", times);
				JSONArray jsonArray2 = onecar.getJSONArray("goods_list");
				for (int r = 0; r < jsonArray2.size(); r++) {
					JSONObject onecar2 = jsonArray2.getJSONObject(r);
					String goods_no = onecar2.getString("goods_no");// 货品编号
					String num = onecar2.getString("num");// 数量
					String price = onecar2.getString("price");// 单价
					String paid = onecar2.getString("paid");// 总金额
					DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
					DynamicObjectType type = entrys.getDynamicObjectType();
					DynamicObject entry = new DynamicObject(type);
					entry.set("yd_textfield_hpbh", goods_no);
					entry.set("yd_decimalfield_sl", num);
					entry.set("yd_decimalfield_dj", price);
					entry.set("yd_decimalfield_zje", paid);
					entrys.add(entry);
				}
				if (TEXT.equals(shop_no)) {
					objs.add(bill);
					if (objs.size() > 0) {
						DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
						for (int e = 0; e < objs.size(); e++) {
							objsSAVE[e] = objs.get(e);
						}
						SaveServiceHelper.save(objsSAVE);
					}
					// 1、获取日志微服务接口
					ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
					// 2、构建日志信息，参考示例如下
					AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "发货明细表进入保存", "yd_sm_ext", "yd_fhmxb");
					// 3、记录操作日志
					logService1.addLog(logInfo1);
				}
			}
		}
	}
}
