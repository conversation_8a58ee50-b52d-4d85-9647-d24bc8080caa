package qimen.api;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qimencloud.api.DefaultQimenCloudClient;
import com.taobao.api.ApiException;

public class TradeQueryTest {
	private static String serverUrl = "https://hu3cgwt0tc.api.taobao.com/router/qm";//奇门API地址
	private static String appKey = "29144852";//奇门appkey 21363512 29144852
	private static String appSecret = "317447863c8eb56ad7aef8e891ff6018";//奇门appkey对应的secret
	private static String format = "json";
	public static void main(String[] args) {

		DefaultQimenCloudClient client = new DefaultQimenCloudClient(serverUrl, appKey, appSecret, format);
		WdtTradeQueryRequest req = new WdtTradeQueryRequest();
		req.setTargetAppKey("21363512");//旺店通的奇门appkey
        req.setSid("tcbj2");//旺店通erp的卖家账号
		req.setStartTime("2021-6-18 11:00:00");
		req.setEndTime("2021-6-18 12:00:00");
		req.setPageSize(100L);
		req.setPageNo(0L);
		
		try {
			WdtTradeQueryResponse res = client.execute(req);
			System.out.println(res);
			System.out.println("code:"+ res.getCode());
			System.out.println("message:"+res.getMessage());
			System.out.println("body:"+res.getBody());
			String response = res.getBody();
			JSONObject jsonObject = JSONObject.parseObject(response);
			JSONObject one = jsonObject.getJSONObject("response");
			JSONArray TCBJ = one.getJSONArray("trades");
			System.out.println(jsonObject);
//			System.out.println(jsonArray);
			Long reqs = req.getPageSize();
			System.out.println(reqs);
			String startDate = null;

			String endDate = null;

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			JSONObject obj = new JSONObject();
			JSONArray arr = new JSONArray();
			int o = 0;
			int r = 0;
			for(int i=0;i<24;i++){
				r = i;
				o = r+1;
				Calendar calendar = Calendar.getInstance();
				Calendar calendar2 = Calendar.getInstance();
				calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY)-i);
				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				System.out.println(i+"i小时前时间为："+df.format(calendar.getTime()));
				startDate = sdf.format(calendar.getTime());
				calendar2.set(Calendar.HOUR_OF_DAY, calendar2.get(Calendar.HOUR_OF_DAY)-o);
				System.out.println(o+"o小时前时间为："+df.format(calendar2.getTime()));
				endDate = sdf.format(calendar.getTime());
				
			}
			 //获取当前小时数(0-23)

//			Calendar calendar = Calendar.getInstance();
//			Calendar calendar2 = Calendar.getInstance();
//
//			calendar.setTime(new Date());
//			calendar2.setTime(new Date());
//			int o = 0;
//			for (int i = -1; i > -24; i--) {
//				o=i-24;
//				calendar.add(calendar.HOUR_OF_DAY, -1);
//				startDate = sdf.format(calendar.getTime());
//				System.out.println(startDate);
//			}	
//			for (int j = -1; j > -24; j--) {
//				calendar.add(calendar.HOUR_OF_DAY, -j);
//				endDate = sdf.format(calendar.getTime());
//				System.out.println(endDate);
//			}
			
			
		} catch (ApiException e) {
			e.printStackTrace();
		}
	}
}
