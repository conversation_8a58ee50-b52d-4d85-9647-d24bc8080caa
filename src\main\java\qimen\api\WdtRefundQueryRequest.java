package qimen.api;

import com.taobao.api.internal.util.RequestCheckUtils;
import java.util.Map;

import com.taobao.api.ApiRuleException;
import com.taobao.api.BaseTaobaoRequest;
import com.taobao.api.internal.util.TaobaoHashMap;
import com.taobao.api.Constants;

/**
 * TOP API(QimenCloud): wdt.refund.query request
 * 
 * <AUTHOR> auto create
 * @since 1.0, 2018.10.23
 */
public class WdtRefundQueryRequest extends BaseTaobaoRequest<WdtRefundQueryResponse> {
     
     

	/** 
	* 秘钥
	 */
	private String appkey;

	/** 
	* 结束时间
	 */
	private String endTime;

	/** 
	* 页码
	 */
	private Long pageNo;

	/** 
	* 分页大小
	 */
	private Long pageSize;

	/** 
	* 退换单处理状态 5 补款 10已取消 20待审核 30已同意 40已拒绝 50待财审 60待收货 70部分到货 80待结算 90已完成
	 */
	private Long processStatus;

	/** 
	* Erp内退换单编号
	 */
	private String refundNo;

	/** 
	* 卖家账号
	 */
	private String sid;

	/** 
	* 平台原始退换单号
	 */
	private String srcRefundNo;

	/** 
	* 开始时间
	 */
	private String startTime;

	/** 
	* 请求时间类型
	 */
	private Long timeType;
	
	/**
	 * 系统订单编号
	 */
	private String tradeNo;
	
	/**
	 * 原始单号
	 */
	private String tid;

	public void setAppkey(String appkey) {
		this.appkey = appkey;
	}

	public String getAppkey() {
		return this.appkey;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getEndTime() {
		return this.endTime;
	}

	public void setPageNo(Long pageNo) {
		this.pageNo = pageNo;
	}

	public Long getPageNo() {
		return this.pageNo;
	}

	public void setPageSize(Long pageSize) {
		this.pageSize = pageSize;
	}

	public Long getPageSize() {
		return this.pageSize;
	}

	public void setProcessStatus(Long processStatus) {
		this.processStatus = processStatus;
	}

	public Long getProcessStatus() {
		return this.processStatus;
	}

	public void setRefundNo(String refundNo) {
		this.refundNo = refundNo;
	}

	public String getRefundNo() {
		return this.refundNo;
	}

	public void setSid(String sid) {
		this.sid = sid;
	}

	public String getSid() {
		return this.sid;
	}

	public void setSrcRefundNo(String srcRefundNo) {
		this.srcRefundNo = srcRefundNo;
	}

	public String getSrcRefundNo() {
		return this.srcRefundNo;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getStartTime() {
		return this.startTime;
	}

	public void setTimeType(Long timeType) {
		this.timeType = timeType;
	}

	public Long getTimeType() {
		return this.timeType;
	}

     public String getApiMethodName() {
        return "wdt.refund.query";
     }

     private String topContentType ;

     public String getTopContentType() {
        return topContentType;
     }

     public void setTopContentType(String topContentType) {
         this.topContentType = topContentType;
     }

     private String topResponseType = Constants.RESPONSE_TYPE_QIMEN;

     public String getTopResponseType() {
        return topResponseType;
     }

     public void setTopResponseType(String topResponseType) {
        this.topResponseType = topResponseType;
     }

    private String topApiVersion = "2.0";

     public String getTopApiVersion() {
        return this.topApiVersion;
     }

     public void setTopApiVersion(String topApiVersion) {
        this.topApiVersion = topApiVersion;
     }

     private String topApiFormat ;

     public String getTopApiFormat() {
     	return this.topApiFormat;
     }

     public void setTopApiFormat(String topApiFormat) {
     	this.topApiFormat = topApiFormat;
     }
     
     public Map<String, String> getTextParams() {		
		TaobaoHashMap txtParams = new TaobaoHashMap();
		txtParams.put("appkey", this.appkey);
		txtParams.put("end_time", this.endTime);
		txtParams.put("page_no", this.pageNo);
		txtParams.put("page_size", this.pageSize);
		txtParams.put("process_status", this.processStatus);
		txtParams.put("refund_no", this.refundNo);
		txtParams.put("sid", this.sid);
		txtParams.put("src_refund_no", this.srcRefundNo);
		txtParams.put("start_time", this.startTime);
		txtParams.put("time_type", this.timeType);
		txtParams.put("trade_no", this.tradeNo);
		txtParams.put("tid", this.tid);
		if(this.udfParams != null) {
			txtParams.putAll(this.udfParams);
		}
		return txtParams;
	}
     
     public Class<WdtRefundQueryResponse> getResponseClass() {
		return WdtRefundQueryResponse.class;
	}

     public void check() throws ApiRuleException {
		//RequestCheckUtils.checkNotEmpty(endTime, "endTime");
		//RequestCheckUtils.checkNotEmpty(pageNo, "pageNo");
		//RequestCheckUtils.checkNotEmpty(pageSize, "pageSize");
		RequestCheckUtils.checkNotEmpty(sid, "sid");
		//RequestCheckUtils.checkNotEmpty(startTime, "startTime");
     }

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}
     


}