package qimen.api;

import java.util.List;
import com.taobao.api.internal.mapping.ApiField;
import com.taobao.api.internal.mapping.ApiListField;
import com.taobao.api.TaobaoObject;

import com.taobao.api.TaobaoResponse;

/**
 * TOP API(QimenCloud): wdt.refund.query response.
 * 
 * <AUTHOR> auto create
 * @since 1.0, null
 */
public class WdtRefundQueryResponse extends TaobaoResponse {

	private static final long serialVersionUID = 5439371572989171131L;

	/** 
	 * 错误码
	 */
	@ApiField("errorcode")
	private Long errorcode;

	/** 
	 * 错误信息
	 */
	@ApiField("message")
	private String message;

	/** 
	 * 退换单信息
	 */
	@ApiListField("refunds")
	@ApiField("array")
	private List<Array> refunds;

	/** 
	 * 数据总条数
	 */
	@ApiField("total_count")
	private Long totalCount;


	public void setErrorcode(Long errorcode) {
		this.errorcode = errorcode;
	}
	public Long getErrorcode( ) {
		return this.errorcode;
	}

	public void setMessage(String message) {
		this.message = message;
	}
	public String getMessage( ) {
		return this.message;
	}

	public void setRefunds(List<Array> refunds) {
		this.refunds = refunds;
	}
	public List<Array> getRefunds( ) {
		return this.refunds;
	}

	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}
	public Long getTotalCount( ) {
		return this.totalCount;
	}
	
	/**
 * 退款子订单信息
 *
 * <AUTHOR> auto create
 * @since 1.0, null
 */
 
public static class Array {

	/**
		 * 优惠
		 */
		@ApiField("discount")
		private String discount;
		/**
		 * 货品名称
		 */
		@ApiField("goods_name")
		private String goodsName;
		/**
		 * 货品编号
		 */
		@ApiField("goods_no")
		private String goodsNo;
		/**
		 * 原始子订单id
		 */
		@ApiField("oid")
		private String oid;
		/**
		 * erp子订单主键
		 */
		@ApiField("order_id")
		private Long orderId;
		/**
		 * 实际数量
		 */
		@ApiField("order_num")
		private String orderNum;
		/**
		 * 原价
		 */
		@ApiField("original_price")
		private String originalPrice;
		/**
		 * 已付金额
		 */
		@ApiField("paid")
		private String paid;
		/**
		 * 价格
		 */
		@ApiField("price")
		private String price;
		/**
		 * 处理状态
		 */
		@ApiField("process_status")
		private Long processStatus;
		/**
		 * 退换单主键
		 */
		@ApiField("refund_id")
		private Long refundId;
		/**
		 * 退款数量
		 */
		@ApiField("refund_num")
		private String refundNum;
		/**
		 * 明细实际退款金额
		 */
		@ApiField("refund_order_amount")
		private String refundOrderAmount;
		/**
		 * 退款说明
		 */
		@ApiField("remark")
		private String remark;
		/**
		 * 销售订单编号
		 */
		@ApiField("sales_tid")
		private String salesTid;
		/**
		 * erp商品主键
		 */
		@ApiField("spec_id")
		private Long specId;
		/**
		 * 规格名称
		 */
		@ApiField("spec_name")
		private String specName;
		/**
		 * 商家编码
		 */
		@ApiField("spec_no")
		private String specNo;
		/**
		 * 退货入库数量
		 */
		@ApiField("stockin_num")
		private String stockinNum;
		/**
		 * 组合装名称
		 */
		@ApiField("suite_name")
		private String suiteName;
		/**
		 * 组合装编号
		 */
		@ApiField("suite_no")
		private String suiteNo;
		/**
		 * 组合装数量
		 */
		@ApiField("suite_num")
		private String suiteNum;
		/**
		 * 订单子订单的原始单号
		 */
		@ApiField("tid")
		private String tid;
		/**
		 * 退款总金额
		 */
		@ApiField("total_amount")
		private String totalAmount;
	

	public String getDiscount() {
			return this.discount;
		}
		public void setDiscount(String discount) {
			this.discount = discount;
		}
		public String getGoodsName() {
			return this.goodsName;
		}
		public void setGoodsName(String goodsName) {
			this.goodsName = goodsName;
		}
		public String getGoodsNo() {
			return this.goodsNo;
		}
		public void setGoodsNo(String goodsNo) {
			this.goodsNo = goodsNo;
		}
		public String getOid() {
			return this.oid;
		}
		public void setOid(String oid) {
			this.oid = oid;
		}
		public Long getOrderId() {
			return this.orderId;
		}
		public void setOrderId(Long orderId) {
			this.orderId = orderId;
		}
		public String getOrderNum() {
			return this.orderNum;
		}
		public void setOrderNum(String orderNum) {
			this.orderNum = orderNum;
		}
		public String getOriginalPrice() {
			return this.originalPrice;
		}
		public void setOriginalPrice(String originalPrice) {
			this.originalPrice = originalPrice;
		}
		public String getPaid() {
			return this.paid;
		}
		public void setPaid(String paid) {
			this.paid = paid;
		}
		public String getPrice() {
			return this.price;
		}
		public void setPrice(String price) {
			this.price = price;
		}
		public Long getProcessStatus() {
			return this.processStatus;
		}
		public void setProcessStatus(Long processStatus) {
			this.processStatus = processStatus;
		}
		public Long getRefundId() {
			return this.refundId;
		}
		public void setRefundId(Long refundId) {
			this.refundId = refundId;
		}
		public String getRefundNum() {
			return this.refundNum;
		}
		public void setRefundNum(String refundNum) {
			this.refundNum = refundNum;
		}
		public String getRefundOrderAmount() {
			return this.refundOrderAmount;
		}
		public void setRefundOrderAmount(String refundOrderAmount) {
			this.refundOrderAmount = refundOrderAmount;
		}
		public String getRemark() {
			return this.remark;
		}
		public void setRemark(String remark) {
			this.remark = remark;
		}
		public String getSalesTid() {
			return this.salesTid;
		}
		public void setSalesTid(String salesTid) {
			this.salesTid = salesTid;
		}
		public Long getSpecId() {
			return this.specId;
		}
		public void setSpecId(Long specId) {
			this.specId = specId;
		}
		public String getSpecName() {
			return this.specName;
		}
		public void setSpecName(String specName) {
			this.specName = specName;
		}
		public String getSpecNo() {
			return this.specNo;
		}
		public void setSpecNo(String specNo) {
			this.specNo = specNo;
		}
		public String getStockinNum() {
			return this.stockinNum;
		}
		public void setStockinNum(String stockinNum) {
			this.stockinNum = stockinNum;
		}
		public String getSuiteName() {
			return this.suiteName;
		}
		public void setSuiteName(String suiteName) {
			this.suiteName = suiteName;
		}
		public String getSuiteNo() {
			return this.suiteNo;
		}
		public void setSuiteNo(String suiteNo) {
			this.suiteNo = suiteNo;
		}
		public String getSuiteNum() {
			return this.suiteNum;
		}
		public void setSuiteNum(String suiteNum) {
			this.suiteNum = suiteNum;
		}
		public String getTid() {
			return this.tid;
		}
		public void setTid(String tid) {
			this.tid = tid;
		}
		public String getTotalAmount() {
			return this.totalAmount;
		}
		public void setTotalAmount(String totalAmount) {
			this.totalAmount = totalAmount;
		}

}



}
