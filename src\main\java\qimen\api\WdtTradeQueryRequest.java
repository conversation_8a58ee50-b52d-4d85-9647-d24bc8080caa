package qimen.api;

import com.taobao.api.internal.util.RequestCheckUtils;
import java.util.Map;

import com.taobao.api.ApiRuleException;
import com.taobao.api.BaseTaobaoRequest;
import com.taobao.api.internal.util.TaobaoHashMap;
import com.taobao.api.Constants;


/**
 * TOP API(QimenCloud): wdt.trade.query request
 * 
 * <AUTHOR> auto create
 * @since 1.0, 2018.03.15
 */
public class WdtTradeQueryRequest extends BaseTaobaoRequest<WdtTradeQueryResponse> {
     
     

	/** 
	* 卖家appkey
	 */
	private String appkey;

	/** 
	* 结束时间
	 */
	private String endTime;

	/** 
	* 使用税率
	 */
	private Long goodstax;

	/** 
	* 物流单号限制
	 */
	private Long hasLogisticsNo;

	/** 
	* 货品信息是否返回图片
	 */
	private Long imgUrl;

	/** 
	* 物流单号
	 */
	private String logisticsNo;

	/** 
	* 页码
	 */
	private Long pageNo;

	/** 
	* 分页大小
	 */
	private Long pageSize;

	/** 
	* 店铺编号
	 */
	private String shopNo;

	/** 
	* 卖家账号
	 */
	private String sid;

	/** 
	* 是否返回交易流水号
	 */
	private String src;

	/** 
	* 开始时间
	 */
	private String startTime;

	/** 
	* 状态
	 */
	private Long status;

	/** 
	* 订单编号
	 */
	private String tradeNo;
	
	/**
	 * 原始单号
	 */
	private String srcTid;
	
	
	

	/** 
	* 仓库编号
	 */
	private String warehouseNo;

	public void setAppkey(String appkey) {
		this.appkey = appkey;
	}

	public String getAppkey() {
		return this.appkey;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getEndTime() {
		return this.endTime;
	}

	public void setGoodstax(Long goodstax) {
		this.goodstax = goodstax;
	}

	public Long getGoodstax() {
		return this.goodstax;
	}

	public void setHasLogisticsNo(Long hasLogisticsNo) {
		this.hasLogisticsNo = hasLogisticsNo;
	}

	public Long getHasLogisticsNo() {
		return this.hasLogisticsNo;
	}

	public void setImgUrl(Long imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Long getImgUrl() {
		return this.imgUrl;
	}

	public void setLogisticsNo(String logisticsNo) {
		this.logisticsNo = logisticsNo;
	}

	public String getLogisticsNo() {
		return this.logisticsNo;
	}

	public void setPageNo(Long pageNo) {
		this.pageNo = pageNo;
	}

	public Long getPageNo() {
		return this.pageNo;
	}

	public void setPageSize(Long pageSize) {
		this.pageSize = pageSize;
	}

	public Long getPageSize() {
		return this.pageSize;
	}

	public void setShopNo(String shopNo) {
		this.shopNo = shopNo;
	}

	public String getShopNo() {
		return this.shopNo;
	}

	public void setSid(String sid) {
		this.sid = sid;
	}

	public String getSid() {
		return this.sid;
	}

	public void setSrc(String src) {
		this.src = src;
	}

	public String getSrc() {
		return this.src;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getStartTime() {
		return this.startTime;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public Long getStatus() {
		return this.status;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getTradeNo() {
		return this.tradeNo;
	}

	public void setWarehouseNo(String warehouseNo) {
		this.warehouseNo = warehouseNo;
	}

	public String getWarehouseNo() {
		return this.warehouseNo;
	}

     public String getApiMethodName() {
        return "wdt.trade.query";
     }

     private String topContentType ;

     public String getTopContentType() {
        return topContentType;
     }

     public void setTopContentType(String topContentType) {
         this.topContentType = topContentType;
     }

     private String topResponseType = Constants.RESPONSE_TYPE_QIMEN;

     public String getTopResponseType() {
        return topResponseType;
     }

     public void setTopResponseType(String topResponseType) {
        this.topResponseType = topResponseType;
     }

    private String topApiVersion = "2.0";

     public String getTopApiVersion() {
        return this.topApiVersion;
     }

     public void setTopApiVersion(String topApiVersion) {
        this.topApiVersion = topApiVersion;
     }

     private String topApiFormat ;

     public String getTopApiFormat() {
     	return this.topApiFormat;
     }

     public void setTopApiFormat(String topApiFormat) {
     	this.topApiFormat = topApiFormat;
     }
     
     public Map<String, String> getTextParams() {		
		TaobaoHashMap txtParams = new TaobaoHashMap();
		txtParams.put("appkey", this.appkey);
		txtParams.put("end_time", this.endTime);
		txtParams.put("goodstax", this.goodstax);
		txtParams.put("has_logistics_no", this.hasLogisticsNo);
		txtParams.put("img_url", this.imgUrl);
		txtParams.put("logistics_no", this.logisticsNo);
		txtParams.put("page_no", this.pageNo);
		txtParams.put("page_size", this.pageSize);
		txtParams.put("shop_no", this.shopNo);
		txtParams.put("sid", this.sid);
		txtParams.put("src", this.src);
		txtParams.put("start_time", this.startTime);
		txtParams.put("status", this.status);
		txtParams.put("trade_no", this.tradeNo);
		txtParams.put("warehouse_no", this.warehouseNo);
		txtParams.put("src_tid", this.srcTid);
		if(this.udfParams != null) {
			txtParams.putAll(this.udfParams);
		}
		return txtParams;
	}
     
     public Class<WdtTradeQueryResponse> getResponseClass() {
		return WdtTradeQueryResponse.class;
	}

     public void check() throws ApiRuleException {
		//RequestCheckUtils.checkNotEmpty(endTime, "endTime");
		//RequestCheckUtils.checkNotEmpty(pageNo, "pageNo");
		//RequestCheckUtils.checkNotEmpty(pageSize, "pageSize");
		RequestCheckUtils.checkNotEmpty(sid, "sid");
		//RequestCheckUtils.checkNotEmpty(startTime, "startTime");
		//RequestCheckUtils.checkNotEmpty(status, "status");
     }

	public String getSrcTid() {
		return srcTid;
	}

	public void setSrcTid(String srcTid) {
		this.srcTid = srcTid;
	}
     


}