package qimen.api;

import java.util.List;
import com.taobao.api.internal.mapping.ApiField;
import com.taobao.api.internal.mapping.ApiListField;
import com.taobao.api.TaobaoObject;

import com.taobao.api.TaobaoResponse;

/**
 * TOP API(QimenCloud): wdt.trade.query response.
 * 
 * <AUTHOR> auto create
 * @since 1.0, null
 */
public class WdtTradeQueryResponse extends TaobaoResponse {

	private static final long serialVersionUID = 8341398259466687977L;

	/** 
	 * 错误码
	 */
	@ApiField("errorcode")
	private Long errorcode;

	/** 
	 * 信息
	 */
	@ApiField("message")
	private String message;

	/** 
	 * 总条数
	 */
	@ApiField("total_count")
	private Long totalCount;

	/** 
	 * 当前页的订单数据
	 */
	@ApiListField("trades")
	@ApiField("array")
	private List<Array> trades;


	public void setErrorcode(Long errorcode) {
		this.errorcode = errorcode;
	}
	public Long getErrorcode( ) {
		return this.errorcode;
	}

	public void setMessage(String message) {
		this.message = message;
	}
	public String getMessage( ) {
		return this.message;
	}

	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}
	public Long getTotalCount( ) {
		return this.totalCount;
	}

	public void setTrades(List<Array> trades) {
		this.trades = trades;
	}
	public List<Array> getTrades( ) {
		return this.trades;
	}
	
	/**
 * 商品详情
 *
 * <AUTHOR> auto create
 * @since 1.0, null
 */
 
public static class Array {

	/**
		 * 实发数量
		 */
		@ApiField("actual_num")
		private String actualNum;
		/**
		 * 手工调整价
		 */
		@ApiField("adjust")
		private String adjust;
		/**
		 * 平台货品名称
		 */
		@ApiField("api_goods_name")
		private String apiGoodsName;
		/**
		 * 平台规格名称
		 */
		@ApiField("api_spec_name")
		private String apiSpecName;
		/**
		 * 基本单位
		 */
		@ApiField("base_unit_id")
		private Long baseUnitId;
		/**
		 * 关联发货
		 */
		@ApiField("bind_oid")
		private String bindOid;
		/**
		 * 类目id
		 */
		@ApiField("cid")
		private Long cid;
		/**
		 * 佣金
		 */
		@ApiField("commission")
		private String commission;
		/**
		 * 创建时间
		 */
		@ApiField("created")
		private String created;
		/**
		 * 发货条件
		 */
		@ApiField("delivery_term")
		private Long deliveryTerm;
		/**
		 * 总折扣金额
		 */
		@ApiField("discount")
		private String discount;
		/**
		 * flag
		 */
		@ApiField("flag")
		private Long flag;
		/**
		 * 原始单标记
		 */
		@ApiField("from_mask")
		private Long fromMask;
		/**
		 * 是否是赠品 0非赠品 1自动赠送 2手工赠送 3 回购自动送赠品
		 */
		@ApiField("gift_type")
		private Long giftType;
		/**
		 * 赠品方式
		 */
		@ApiField("gift_typeint")
		private Long giftTypeint;
		/**
		 * 货品id
		 */
		@ApiField("goods_id")
		private Long goodsId;
		/**
		 * 货品名称
		 */
		@ApiField("goods_name")
		private String goodsName;
		/**
		 * 货品编号
		 */
		@ApiField("goods_no")
		private String goodsNo;
		/**
		 * 货品类别
		 */
		@ApiField("goods_type")
		private Long goodsType;
		/**
		 * 担保方式
		 */
		@ApiField("guarantee_mode")
		private String guaranteeMode;
		/**
		 * 货品图片url
		 */
		@ApiField("img_url")
		private String imgUrl;
		/**
		 * 发票
		 */
		@ApiField("invoice_content")
		private String invoiceContent;
		/**
		 * 发票类别
		 */
		@ApiField("invoice_type")
		private Long invoiceType;
		/**
		 * 大件类型
		 */
		@ApiField("large_type")
		private Long largeType;
		/**
		 * 最后修改时间
		 */
		@ApiField("modified")
		private String modified;
		/**
		 * 下单数量
		 */
		@ApiField("num")
		private String num;
		/**
		 * 成交价
		 */
		@ApiField("order_price")
		private String orderPrice;
		/**
		 * 已付
		 */
		@ApiField("paid")
		private String paid;
		/**
		 * 交易流水单号
		 */
		@ApiField("pay_id")
		private String payId;
		/**
		 * 平台货品id
		 */
		@ApiField("platform_goods_id")
		private String platformGoodsId;
		/**
		 * 平台
		 */
		@ApiField("platform_id")
		private Long platformId;
		/**
		 * 平台商品id
		 */
		@ApiField("platform_spec_id")
		private String platformSpecId;
		/**
		 * 标价
		 */
		@ApiField("price")
		private String price;
		/**
		 * 自定义属性2
		 */
		@ApiField("prop2")
		private String prop2;
		/**
		 * erp子订单主键
		 */
		@ApiField("rec_id")
		private Long recId;
		/**
		 * 售后退款数量
		 */
		@ApiField("refund_num")
		private String refundNum;
		/**
		 * 退款状态
		 */
		@ApiField("refund_status")
		private Long refundStatus;
		/**
		 * 备注
		 */
		@ApiField("remark")
		private String remark;
		/**
		 * 分摊后总价
		 */
		@ApiField("share_amount")
		private String shareAmount;
		/**
		 * 分摊邮费
		 */
		@ApiField("share_post")
		private String sharePost;
		/**
		 * 分摊后价格
		 */
		@ApiField("share_price")
		private String sharePrice;
		/**
		 * 规格码
		 */
		@ApiField("spec_code")
		private String specCode;
		/**
		 * erp内商品主键
		 */
		@ApiField("spec_id")
		private Long specId;
		/**
		 * 规格名
		 */
		@ApiField("spec_name")
		private String specName;
		/**
		 * 商家编码
		 */
		@ApiField("spec_no")
		private String specNo;
		/**
		 * 线上包裹拆分数
		 */
		@ApiField("split_package_num")
		private Long splitPackageNum;
		/**
		 * 子订单编号
		 */
		@ApiField("src_oid")
		private String srcOid;
		/**
		 * 原始订单编号
		 */
		@ApiField("src_tid")
		private String srcTid;
		/**
		 * 组合装分摊后总价
		 */
		@ApiField("suite_amount")
		private String suiteAmount;
		/**
		 * 组合装优惠
		 */
		@ApiField("suite_discount")
		private String suiteDiscount;
		/**
		 * 组合装ID
		 */
		@ApiField("suite_id")
		private Long suiteId;
		/**
		 * 拆自组合装
		 */
		@ApiField("suite_name")
		private String suiteName;
		/**
		 * 组合装编码
		 */
		@ApiField("suite_no")
		private String suiteNo;
		/**
		 * 组合装数量
		 */
		@ApiField("suite_num")
		private String suiteNum;
		/**
		 * 税率
		 */
		@ApiField("tax_rate")
		private String taxRate;
		/**
		 * erp订单主键
		 */
		@ApiField("trade_id")
		private Long tradeId;
		/**
		 * 基本单位名称
		 */
		@ApiField("unit_name")
		private String unitName;
		/**
		 * 估重
		 */
		@ApiField("weight")
		private String weight;
	

	public String getActualNum() {
			return this.actualNum;
		}
		public void setActualNum(String actualNum) {
			this.actualNum = actualNum;
		}
		public String getAdjust() {
			return this.adjust;
		}
		public void setAdjust(String adjust) {
			this.adjust = adjust;
		}
		public String getApiGoodsName() {
			return this.apiGoodsName;
		}
		public void setApiGoodsName(String apiGoodsName) {
			this.apiGoodsName = apiGoodsName;
		}
		public String getApiSpecName() {
			return this.apiSpecName;
		}
		public void setApiSpecName(String apiSpecName) {
			this.apiSpecName = apiSpecName;
		}
		public Long getBaseUnitId() {
			return this.baseUnitId;
		}
		public void setBaseUnitId(Long baseUnitId) {
			this.baseUnitId = baseUnitId;
		}
		public String getBindOid() {
			return this.bindOid;
		}
		public void setBindOid(String bindOid) {
			this.bindOid = bindOid;
		}
		public Long getCid() {
			return this.cid;
		}
		public void setCid(Long cid) {
			this.cid = cid;
		}
		public String getCommission() {
			return this.commission;
		}
		public void setCommission(String commission) {
			this.commission = commission;
		}
		public String getCreated() {
			return this.created;
		}
		public void setCreated(String created) {
			this.created = created;
		}
		public Long getDeliveryTerm() {
			return this.deliveryTerm;
		}
		public void setDeliveryTerm(Long deliveryTerm) {
			this.deliveryTerm = deliveryTerm;
		}
		public String getDiscount() {
			return this.discount;
		}
		public void setDiscount(String discount) {
			this.discount = discount;
		}
		public Long getFlag() {
			return this.flag;
		}
		public void setFlag(Long flag) {
			this.flag = flag;
		}
		public Long getFromMask() {
			return this.fromMask;
		}
		public void setFromMask(Long fromMask) {
			this.fromMask = fromMask;
		}
		public Long getGiftType() {
			return this.giftType;
		}
		public void setGiftType(Long giftType) {
			this.giftType = giftType;
		}
		public Long getGiftTypeint() {
			return this.giftTypeint;
		}
		public void setGiftTypeint(Long giftTypeint) {
			this.giftTypeint = giftTypeint;
		}
		public Long getGoodsId() {
			return this.goodsId;
		}
		public void setGoodsId(Long goodsId) {
			this.goodsId = goodsId;
		}
		public String getGoodsName() {
			return this.goodsName;
		}
		public void setGoodsName(String goodsName) {
			this.goodsName = goodsName;
		}
		public String getGoodsNo() {
			return this.goodsNo;
		}
		public void setGoodsNo(String goodsNo) {
			this.goodsNo = goodsNo;
		}
		public Long getGoodsType() {
			return this.goodsType;
		}
		public void setGoodsType(Long goodsType) {
			this.goodsType = goodsType;
		}
		public String getGuaranteeMode() {
			return this.guaranteeMode;
		}
		public void setGuaranteeMode(String guaranteeMode) {
			this.guaranteeMode = guaranteeMode;
		}
		public String getImgUrl() {
			return this.imgUrl;
		}
		public void setImgUrl(String imgUrl) {
			this.imgUrl = imgUrl;
		}
		public String getInvoiceContent() {
			return this.invoiceContent;
		}
		public void setInvoiceContent(String invoiceContent) {
			this.invoiceContent = invoiceContent;
		}
		public Long getInvoiceType() {
			return this.invoiceType;
		}
		public void setInvoiceType(Long invoiceType) {
			this.invoiceType = invoiceType;
		}
		public Long getLargeType() {
			return this.largeType;
		}
		public void setLargeType(Long largeType) {
			this.largeType = largeType;
		}
		public String getModified() {
			return this.modified;
		}
		public void setModified(String modified) {
			this.modified = modified;
		}
		public String getNum() {
			return this.num;
		}
		public void setNum(String num) {
			this.num = num;
		}
		public String getOrderPrice() {
			return this.orderPrice;
		}
		public void setOrderPrice(String orderPrice) {
			this.orderPrice = orderPrice;
		}
		public String getPaid() {
			return this.paid;
		}
		public void setPaid(String paid) {
			this.paid = paid;
		}
		public String getPayId() {
			return this.payId;
		}
		public void setPayId(String payId) {
			this.payId = payId;
		}
		public String getPlatformGoodsId() {
			return this.platformGoodsId;
		}
		public void setPlatformGoodsId(String platformGoodsId) {
			this.platformGoodsId = platformGoodsId;
		}
		public Long getPlatformId() {
			return this.platformId;
		}
		public void setPlatformId(Long platformId) {
			this.platformId = platformId;
		}
		public String getPlatformSpecId() {
			return this.platformSpecId;
		}
		public void setPlatformSpecId(String platformSpecId) {
			this.platformSpecId = platformSpecId;
		}
		public String getPrice() {
			return this.price;
		}
		public void setPrice(String price) {
			this.price = price;
		}
		public String getProp2() {
			return this.prop2;
		}
		public void setProp2(String prop2) {
			this.prop2 = prop2;
		}
		public Long getRecId() {
			return this.recId;
		}
		public void setRecId(Long recId) {
			this.recId = recId;
		}
		public String getRefundNum() {
			return this.refundNum;
		}
		public void setRefundNum(String refundNum) {
			this.refundNum = refundNum;
		}
		public Long getRefundStatus() {
			return this.refundStatus;
		}
		public void setRefundStatus(Long refundStatus) {
			this.refundStatus = refundStatus;
		}
		public String getRemark() {
			return this.remark;
		}
		public void setRemark(String remark) {
			this.remark = remark;
		}
		public String getShareAmount() {
			return this.shareAmount;
		}
		public void setShareAmount(String shareAmount) {
			this.shareAmount = shareAmount;
		}
		public String getSharePost() {
			return this.sharePost;
		}
		public void setSharePost(String sharePost) {
			this.sharePost = sharePost;
		}
		public String getSharePrice() {
			return this.sharePrice;
		}
		public void setSharePrice(String sharePrice) {
			this.sharePrice = sharePrice;
		}
		public String getSpecCode() {
			return this.specCode;
		}
		public void setSpecCode(String specCode) {
			this.specCode = specCode;
		}
		public Long getSpecId() {
			return this.specId;
		}
		public void setSpecId(Long specId) {
			this.specId = specId;
		}
		public String getSpecName() {
			return this.specName;
		}
		public void setSpecName(String specName) {
			this.specName = specName;
		}
		public String getSpecNo() {
			return this.specNo;
		}
		public void setSpecNo(String specNo) {
			this.specNo = specNo;
		}
		public Long getSplitPackageNum() {
			return this.splitPackageNum;
		}
		public void setSplitPackageNum(Long splitPackageNum) {
			this.splitPackageNum = splitPackageNum;
		}
		public String getSrcOid() {
			return this.srcOid;
		}
		public void setSrcOid(String srcOid) {
			this.srcOid = srcOid;
		}
		public String getSrcTid() {
			return this.srcTid;
		}
		public void setSrcTid(String srcTid) {
			this.srcTid = srcTid;
		}
		public String getSuiteAmount() {
			return this.suiteAmount;
		}
		public void setSuiteAmount(String suiteAmount) {
			this.suiteAmount = suiteAmount;
		}
		public String getSuiteDiscount() {
			return this.suiteDiscount;
		}
		public void setSuiteDiscount(String suiteDiscount) {
			this.suiteDiscount = suiteDiscount;
		}
		public Long getSuiteId() {
			return this.suiteId;
		}
		public void setSuiteId(Long suiteId) {
			this.suiteId = suiteId;
		}
		public String getSuiteName() {
			return this.suiteName;
		}
		public void setSuiteName(String suiteName) {
			this.suiteName = suiteName;
		}
		public String getSuiteNo() {
			return this.suiteNo;
		}
		public void setSuiteNo(String suiteNo) {
			this.suiteNo = suiteNo;
		}
		public String getSuiteNum() {
			return this.suiteNum;
		}
		public void setSuiteNum(String suiteNum) {
			this.suiteNum = suiteNum;
		}
		public String getTaxRate() {
			return this.taxRate;
		}
		public void setTaxRate(String taxRate) {
			this.taxRate = taxRate;
		}
		public Long getTradeId() {
			return this.tradeId;
		}
		public void setTradeId(Long tradeId) {
			this.tradeId = tradeId;
		}
		public String getUnitName() {
			return this.unitName;
		}
		public void setUnitName(String unitName) {
			this.unitName = unitName;
		}
		public String getWeight() {
			return this.weight;
		}
		public void setWeight(String weight) {
			this.weight = weight;
		}

}



}
