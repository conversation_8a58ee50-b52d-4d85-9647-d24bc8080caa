package test;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import json.JSONArray;
import json.JSONObject;
import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.IFormView;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.yd.tcyp.ABillServiceHelper;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

public class ApiFf {
	public static String getMd5(String plainText) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(plainText.getBytes());
			byte b[] = md.digest();

			int i;

			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			// 32位加密
			return buf.toString();
			// 16位的加密
			// return buf.toString().substring(8, 24);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			return null;
		}

	}

	public static String doPost(String url, Map<String, String> param) {

		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();

		String resultString = "";
		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			if (param != null) {
				for (String key : param.keySet()) {
					builder.addParameter(key, param.get(key));
				}
			}
			URI uri = builder.build();

			// 创建http post请求
			HttpPost httpGet = new HttpPost(uri);

			// 执行请求
			response = httpclient.execute(httpGet);
			// 判断返回状态是否为200
			if (response.getStatusLine().getStatusCode() == 200) {
				resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}
	public static void E3ReturnListGet(String ks,String js) {
		JSONObject postjson = new JSONObject();
		postjson.put("startModified", ks);// 开始时间
		postjson.put("endModified", js);// 结束时间
		postjson.put("return_order_status", 10);// 正式需要启用

		postjson.put("pageNo", 1);// 页码
		postjson.put("pageSize", 100);// 每页数量
		String postdata = postjson.toString();
		String result = ApiFf.PostE3Th(postdata);
		JSONObject json = JSONObject.parseObject(result);
		String jg = json.getString("status");
		if (jg.equals("api-success")) {

			JSONObject data = json.getJSONObject("data");
			JSONObject page = data.getJSONObject("page");
			int pageTotal = page.getIntValue("pageTotal");// 页数
			// JSONArray orderListGets = data.getJSONArray("orderListGets");
			// BillSave(orderListGets);
			for (int i = 1; i <= pageTotal; i++) {

				postjson = new JSONObject();
				postjson.put("startModified", ks);
				postjson.put("endModified", js);
				postjson.put("return_order_status", 10);
				postjson.put("pageNo", i);
				postjson.put("pageSize", 100);
				postdata = postjson.toString();
				result = ApiFf.PostE3Th(postdata);
				json = JSONObject.parseObject(result);
				// 记录调用接口完成之后的时间
				Date kssj = new Date();
				jg = json.getString("status");
				if (jg.equals("api-success")) {

					data = json.getJSONObject("data");
					page = data.getJSONObject("page");
					JSONArray orderListGets = data.getJSONArray("orderListGets");
					ApiFf.BillSaveTh(orderListGets);
				}
				// 记录保存单据之后的时间
				Date jssj = new Date();
				long hs = jssj.getTime() - kssj.getTime();
				// 如果时间只差小于10000毫秒 则延时
				if ((hs) < 1000) {
					try {
						Thread.sleep(1000 - hs);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
	}
	public static void E3OrderListGet(String ks,String js,String dp) {
		JSONObject postjson = new JSONObject();
		postjson.put("startModified", ks);// 开始时间
		postjson.put("endModified", js);// 结束时间
		postjson.put("shipping_status", 7);// 正式需要启用
		if(dp!="")
		{
		postjson.put("sd_code", dp);//店铺
		}
		// postjson.put("tags_code", "order_type_import");//手工导入
		postjson.put("pageNo", 1);// 页码
		postjson.put("pageSize", 100);// 每页数量
		String postdata = postjson.toString();
		String result = ApiFf.PostE3(postdata);
		JSONObject json = JSONObject.parseObject(result);
		String jg = json.getString("status");
		if (jg.equals("api-success")) {

			JSONObject data = json.getJSONObject("data");
			JSONObject page = data.getJSONObject("page");
			int pageTotal = page.getIntValue("pageTotal");// 页数
			// JSONArray orderListGets = data.getJSONArray("orderListGets");
			// BillSave(orderListGets);
			for (int i = 1; i <= pageTotal; i++) {

				postjson = new JSONObject();
				postjson.put("startModified", ks);
				postjson.put("endModified", js);
				postjson.put("shipping_status", 7);
				if(dp!="")
				{
				postjson.put("sd_code", dp);//店铺
				}
				// postjson.put("tags_code", "order_type_import");//手工导入
				postjson.put("pageNo", i);
				postjson.put("pageSize", 100);
				postdata = postjson.toString();
				result = ApiFf.PostE3(postdata);
				json = JSONObject.parseObject(result);
				// 记录调用接口完成之后的时间
				Date kssj = new Date();
				jg = json.getString("status");
				if (jg.equals("api-success")) {

					data = json.getJSONObject("data");
					page = data.getJSONObject("page");
					JSONArray orderListGets = data.getJSONArray("orderListGets");
					ApiFf.BillSave(orderListGets);
				}
				// 记录保存单据之后的时间
				Date jssj = new Date();
				long hs = jssj.getTime() - kssj.getTime();
				// 如果时间只差小于10000毫秒 则延时
				if ((hs) < 1000) {
					try {
						Thread.sleep(1000 - hs);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
	}
	public static void E3Rfhhz(String lx,String rq) {
		JSONObject postjsonBB = new JSONObject();
		 postjsonBB.put("type", lx);// 开始时间
		 postjsonBB.put("date", rq);// 结束时间
		String postdataBB = postjsonBB.toString();
		String resultBB = ApiFf.PostE3BB(postdataBB);
		JSONObject jsonBB = JSONObject.parseObject(resultBB);
		String jgBB = jsonBB.getString("status");
		if (jgBB.equals("200")) {
			List<DynamicObject> objs = new ArrayList<DynamicObject>();
			JSONArray data = jsonBB.getJSONArray("data");
			for (int i = 0; i < data.size(); i++) {
			//String id = data.getJSONObject(i).get("id").toString();
			String sd_code = data.getJSONObject(i).get("sd_code").toString();
			String date = data.getJSONObject(i).get("date").toString();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date tiem = new Date();
			try {
				tiem = sdf.parse(date);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			String num = data.getJSONObject(i).get("num").toString();
			String money = data.getJSONObject(i).get("money").toString();
			String type = data.getJSONObject(i).get("type").toString();
			if(type.equals("1"))
			{
				type="0";
			}
			else
			{
				type="1";
			}
			//String ordersns = data.getJSONObject(i).get("ordersns").toString();
			DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_rhzfh");
			// 设置单据属性
			bill.set("billno", "1"+"_"+type+"_"+sd_code+"_"+date); // 单据编号为平台_退货_店铺_日期
			Date time = new Date();
			bill.set("createtime", time);
			bill.set("billstatus", "A");
			bill.set("yd_combofield_pt", "1");
			bill.set("yd_datefield_fhrq", tiem);
			bill.set("yd_checkboxfield_th", type);
			bill.set("yd_integerfield_zs", 1);
			bill.set("yd_textfield_dpbh", sd_code);
			bill.set("yd_decimalfield_sl", num);
			bill.set("yd_decimalfield_zje", money);
			objs.add(bill);
			}
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
		}
	}
	public static String PostE3(String postdata) {
		String key = "TCBJ_E3";
		String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";
		String version = "3.0";
		String serviceType = "order.list.get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		//String BSDZ = "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	
	public static String PostE3BB(String postdata) {
		String key = "BSAKhmSVtfmkDVGLsRLs";
		String secret = "BSAKdaVAbhYEjuLPwiOs";
		String version = "3.0";
		String serviceType = "ptg.order.list";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		String BSDZ = "http://47.92.195.153/e3test/webopm/web/?app_act=api_baison/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	public static void BillSave(JSONArray orderListGets) {
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < orderListGets.size(); i++) {
			String order_sn = orderListGets.getJSONObject(i).get("order_sn").toString();// 订单编号
			String sd_code = orderListGets.getJSONObject(i).get("sd_code").toString();// 商店id
			String djbh = "1_0_" + sd_code + "_" + order_sn;
			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno",
					new QFilter[] { qFilter });
			if (dObject == null) {
				String add_time = orderListGets.getJSONObject(i).get("add_time").toString();// 下单时间
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date tiem = new Date();
				try {
					tiem = sdf.parse(add_time);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String shipping_fee = orderListGets.getJSONObject(i).get("shipping_fee").toString();// 运费
				String fhck = orderListGets.getJSONObject(i).get("fhck").toString();// 发货仓库
				String fptt = orderListGets.getJSONObject(i).get("invoice_title").toString();// 开票
				Boolean kp = true;
				if (fptt.equals("") || fptt == null) {
					kp = false;
				}
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
				// 设置单据属性
				bill.set("billno", djbh); // 单据编号为平台_退货_店铺_订单号
				Date time = new Date();
				bill.set("createtime", time);
				bill.set("billstatus", "A");
				bill.set("yd_combofield_pt", "1");
				bill.set("yd_textfield_ddbh", order_sn);
				bill.set("yd_datetimefield_xdsj", tiem);
				bill.set("yd_textfield_dpbh", sd_code);
				bill.set("yd_checkboxfield_sfkp", kp);
				// bill.set("yd_checkboxfield_sfsg", "0");
				bill.set("yd_decimalfield_yf", shipping_fee);
				bill.set("yd_textfield_ck", fhck);
				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray orderDetailGets = orderListGets.getJSONObject(i).getJSONArray("orderDetailGets");
				for (int j = 0; j < orderDetailGets.size(); j++) {
					DynamicObject entry = new DynamicObject(type);
					// 设置单据体属性
					String sku = orderDetailGets.getJSONObject(j).get("sku").toString();// sku
					String goods_number = orderDetailGets.getJSONObject(j).get("goods_number").toString();// 商品数量
					String goods_price = orderDetailGets.getJSONObject(j).get("goods_price").toString();// 商品单价
					String share_payment = orderDetailGets.getJSONObject(j).get("share_payment").toString();// 均摊实付金额
					entry.set("yd_textfield_hpbh", sku);
					entry.set("yd_decimalfield_sl", goods_number);
					entry.set("yd_decimalfield_dj", goods_price);
					entry.set("yd_decimalfield_zje", share_payment);
					// 添加到单据体集合
					entrys.add(entry);

				}
				objs.add(bill);
			}
		}
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
	//退货
	public static String PostE3Th(String postdata) {
		String key = "TCBJ_E3";
		String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";
		String version = "3.0";
		String serviceType = "return_list_get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		String BSDZ = "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		//String BSDZ = "http://47.92.195.153/e3/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	//退货
	public static void BillSaveTh(JSONArray orderListGets) {
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < orderListGets.size(); i++) {
			String order_sn = orderListGets.getJSONObject(i).get("return_order_sn").toString();// 退单编号
			String sd_code = orderListGets.getJSONObject(i).get("sd_code").toString();// 商店id
			String djbh = "1_1_" + sd_code + "_" + order_sn;
			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno",
					new QFilter[] { qFilter });
			if (dObject == null) {
				String add_time = orderListGets.getJSONObject(i).get("add_time").toString();// 退单时间
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date tiem = new Date();
				try {
					tiem = sdf.parse(add_time);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				// String shipping_fee =
				// orderListGets.getJSONObject(i).get("shipping_fee").toString();// 运费?
				String fhck = orderListGets.getJSONObject(i).get("fhck").toString();// 发货仓库
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
				// 设置单据属性
				bill.set("billno", djbh); // 单据编号为平台_店铺_订单号
				
				Date time = new Date();
				bill.set("createtime", time);
				bill.set("billstatus", "A");
				bill.set("yd_combofield_pt", "1");
				bill.set("yd_textfield_ddbh", order_sn);
				bill.set("yd_datetimefield_xdsj", tiem);
				bill.set("yd_textfield_dpbh", sd_code);
				// bill.set("yd_checkboxfield_sfkp", "0");//开票
				// bill.set("yd_checkboxfield_sfsg", "0");//手工
				bill.set("yd_checkboxfield_th", true);// 退货
				// bill.set("yd_decimalfield_yf", shipping_fee);
				bill.set("yd_textfield_ck", fhck);
				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray orderDetailGets = orderListGets.getJSONObject(i).getJSONArray("orderDetailGets");
				for (int j = 0; j < orderDetailGets.size(); j++) {
					DynamicObject entry = new DynamicObject(type);
					// 设置单据体属性
					String sku = orderDetailGets.getJSONObject(j).get("sku").toString();// sku
					String goods_number = orderDetailGets.getJSONObject(j).get("goods_number").toString();// 商品数量
					String goods_price = orderDetailGets.getJSONObject(j).get("goods_price").toString();// 商品单价
					String share_payment = orderDetailGets.getJSONObject(j).get("share_payment").toString();// 均摊实付金额
					entry.set("yd_textfield_hpbh", sku);
					entry.set("yd_decimalfield_sl", goods_number);
					entry.set("yd_decimalfield_dj", goods_price);
					entry.set("yd_decimalfield_zje", share_payment);
					// 添加到单据体集合
					entrys.add(entry);

				}
				objs.add(bill);
			}
		}
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			//SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}
	public static void bcbhzdj(String lx, List<String> pcwlin, List<String> fywl) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter7 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不为手工
		QFilter filter8 = QFilter.of("yd_checkboxfield_sfkp =?", "0");// 不为开票
		QFilter filterSh = QFilter.of("status=?", "C");
		QFilter filter9 = QFilter.of("yd_checkboxfield_sfsg =?", "1");// 手工
		QFilter filter10 = QFilter.of("yd_checkboxfield_sfkp =?", "1");// 开票
		Date dt =new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter11 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间大于今天
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,filter11 };
		String ZD = "billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh kh,yd_decimalfield_yf yf,yd_textfield_ck ck,yd_entryentity.yd_textfield_hpbh wlbm,yd_entryentity.yd_decimalfield_sl sl,yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je";
		// 查询发货明细
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		String[] fhmxbzd = new String[] { "billno", "th","yf", "ck", "wlbm", "sl", "dj", "je", "pt","zz" };
		// 查询手工单据
		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter9,filter11 };
		DataSet yd_fhmxbsg = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		// 查询开票单据
		fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter10,filter11 };
		DataSet yd_fhmxbkp = QueryServiceHelper.queryDataSet("bcbhzdj", "yd_fhmxb", ZD, fhmxgl, null);
		//union发货明细
		yd_fhmxb=yd_fhmxb.union(yd_fhmxbsg).union(yd_fhmxbkp);
		// 查询已审核客户
				QFilter filterbhz = null;
				if (lx.equals("5")) {
					String[] hz = new String[] { "4", "5" };
					filterbhz = new QFilter("yd_combofield_hzcl", QCP.in, hz);
				} else {
					String[] hz = new String[] { "1", "2" };
					filterbhz = new QFilter("yd_combofield_hzcl", QCP.in, hz);
				}
				QFilter[] filter=new QFilter[] { filterbhz, filterSh };
		//关联客户
		DataSet dskh = glmx(fhmxbzd, yd_fhmxb, filter, "bd_customer", "kh",",yd_orgfield_dyzz zz");
		filter=new QFilter[] {filterSh};
		// 关联仓库
		fhmxbzd = new String[] { "billno", "th", "kh", "yf", "wlbm", "sl", "dj", "je", "pt", "zz" };
		DataSet dsck = glmx(fhmxbzd, dskh, filter, "bd_warehouse", "ck","");
		// 关联费用物料
		Map<String, Object> mapfywl = new HashMap<>();
		mapfywl.put("fywl", fywl);
		fhmxbzd = new String[] { "billno", "th", "kh", "ck", "yf", "sl", "dj", "je", "pt", "zz", "wlbm" };
		DataSet dsfywl = dsck.filter("wlbm in fywl", mapfywl).select(fhmxbzd);
		// 关联物料
		fhmxbzd = new String[] { "billno", "th", "kh", "ck", "yf", "sl", "dj", "je", "pt", "zz" };
		DataSet dswl = glmx(fhmxbzd, dsck, filter, "bd_material", "wlbm","").union(dsfywl);
		// DataSet ds = dswl.copy();
		DataSet dataSetgroupBy = dswl.copy().select(new String[] { "billno", "th", "kh", "ck", "zz", "yf" })
				.groupBy(new String[] { "billno", "th", "kh", "ck", "zz", "yf" }).finish();
		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String dj = "priceandtax";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型
		if (lx.equals("5")) {
			formid = "im_otheroutbill";
			djlx = "im_OtherOutBill_STD_BT_S";
			biztypenumber = "355";
			dj = "price";
			je = "amount";
			xyd = "2";
		}
		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz");
			String ckbm = row.getString("ck");
			String khbm = row.getString("kh");
			Boolean th = row.getBoolean("th");
			IFormView view=null;
			 view = ABillServiceHelper.createAddView(formid);

			view.getModel().setItemValueByNumber("billtype", djlx);
			view.getModel().setValue("org", zzbm);
			if (lx != "5") {
				view.getModel().setValue("bizorg", zzbm);
			}
			if (th) {
				if (lx.equals("5")) {
					view.getModel().setItemValueByNumber("biztype", "3551");
				} else {
					view.getModel().setItemValueByNumber("biztype", "2101");
				}
			} else {
				view.getModel().setItemValueByNumber("biztype", biztypenumber);
			}
			view.getModel().setItemValueByNumber("customer", khbm);
			BigDecimal yf = row.getBigDecimal("yf");
			BigDecimal zje = BigDecimal.ZERO;// 整单总金额
			String billno = row.getString("billno");
			DataSet dsxh = dswl.copy();
			dsxh = dsxh.filter("billno=" + "'" + billno + "'").select(new String[] { "wlbm", "sl", "je", "dj" });
			int i = 0;
			for (Row rowds : dsxh) {
				String wlbm = rowds.getString("wlbm");// sku
				BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
				BigDecimal goods_price = rowds.getBigDecimal("dj");// 商品单价
				BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
				boolean sffy = true;
				for (String fywlbm : fywl) {
					if (wlbm.equals(fywlbm)) {
						yf = yf.add(share_payment);
						sffy = false;
						break;
					}
				}
				if (sffy) {
					if (i > 0) {
						view.getModel().createNewEntryRow("billentry");
					}
					view.getModel().setItemValueByNumber("material", wlbm, i);
					view.getModel().setItemValueByNumber("warehouse", ckbm, i);
					view.getModel().setValue("qty", goods_number, i);
					view.getModel().setValue(dj, goods_price, i);
					view.getModel().setValue(je, share_payment, i);
					zje = zje.add(share_payment);
					i++;
				}
			}
			view.getModel().setValue("yd_amountfield_yf", yf);
			view.getModel().setValue("yd_textfield_fhmxdh", billno);
			if (yf.compareTo(BigDecimal.ZERO) > 0) {
				// 处理分摊
				DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
				BigDecimal ljft = BigDecimal.ZERO;// 记录已分摊的运费金额
				for (int j = 0; j < entrys.getRowCount(); j++) {
					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
					if (j < entrys.getRowCount() - 1) {
						BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
						ljft = ljft.add(yfft);
						view.getModel().setValue(je, hsjeft, j);
					} else {
						BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
						view.getModel().setValue(je, hsjeft, j);
					}
				}
			}
			if (i > 0) {
				OperationResult operationResult = ABillServiceHelper.saveOperate(view);
				filter1 = new QFilter("billno", QCP.equals, billno);
				// 判断保存结果
				if (operationResult.isSuccess()) {
					Map<Object, String> map = operationResult.getBillNos();
					for (Map.Entry<Object, String> entry : map.entrySet()) {
						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
								"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_textfield_xsckdh", entry.getValue());// 修改数据
							rowdy.set("yd_combofield_xyd", xyd);// 修改数据
						}
						SaveServiceHelper.save(dy);
					}
				} else {
					String errMessage = operationResult.getMessage(); // 错误摘要
					// // 演示提取保存详细错误
					// for(IOperateInfo errInfo: operationResult.getAllErrorOrValidateInfo()) {
					// String detailMessage = errInfo.getMessage();
					// }
					DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
							new QFilter[] { filter1 });
					for (DynamicObject rowdy : dy) {
						rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
					}
					SaveServiceHelper.save(dy);
				}
			} else {
				view.close();
			}
		}
		dswl.close();
	}

	public static DataSet glmx(String[] fhmxbzd, DataSet joinDataset, QFilter[] filterSh, String bz, String bm,String ewzd) {
		DataSet wl = QueryServiceHelper.queryDataSet("glmx", bz,
				"number,yd_entryentity_e3.yd_textfield_e3 e3bm,yd_entryentity_wdt.yd_textfield_wdt wdtbm,yd_entryentity_jky.yd_textfield_jky jkybm"+ewzd,
				 filterSh , null);
		DataSet joinDatasete3 = joinDataset.filter("pt='1'").join(wl).on(bm, "e3bm")
				.select(fhmxbzd, new String[] { "number " + bm }).finish();
		//joinDatasete3.print(true);
		DataSet joinDatasetwdt = joinDataset.filter("pt='2'").join(wl).on(bm, "wdtbm")
				.select(fhmxbzd, new String[] { "number " + bm }).finish();
		//joinDatasetwdt.print(true);
		DataSet joinDatasetjky = joinDataset.filter("pt='3'").join(wl).on(bm, "jkybm")
				.select(fhmxbzd, new String[] { "number " + bm }).finish();
		joinDataset = joinDatasete3.union(joinDatasetwdt).union(joinDatasetjky);
		//joinDataset.print(true);
		return joinDataset;
	}

	public static void bchbdj(String lx, List<String> pcwlin, List<String> fywl) {
		QFilter filter1 = QFilter.of("yd_textfield_xsckdh=?", "");// 下游单据编号为空
		QFilter filter2 = QFilter.of("yd_checkboxfield_khbcz =?", "0");// 客户存在
		QFilter filter3 = QFilter.of("yd_checkboxfield_ckbcz =?", "0");// 仓库存在
		QFilter filter4 = QFilter.of("yd_checkboxfield_wlbcz =?", "0");// 物料存在
		QFilter filter5 = QFilter.of("yd_checkboxfield_bcl =?", "0");// 需要处理
		QFilter filter6 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter7 = QFilter.of("yd_checkboxfield_sfsg =?", "0");// 不为手工
		QFilter filter8 = QFilter.of("yd_checkboxfield_sfkp =?", "0");// 不为开票
		QFilter filterSh = QFilter.of("status=?", "C");
		Date dt =new Date();
		String sj = (String) new SimpleDateFormat("yyyy-MM-dd").format(dt);
		QFilter filter11 = QFilter.of("yd_datetimefield_xdsj <?", sj);// 下单时间大于今天
		QFilter[] fhmxgl = new QFilter[] { filter1, filter2, filter3, filter4, filter5, filter6, filter7, filter8,filter11 };
		// 查询发货明细
		DataSet yd_fhmxb = QueryServiceHelper.queryDataSet("bchbdj", "yd_fhmxb",
				"billno,yd_checkboxfield_th th,yd_combofield_pt pt,yd_textfield_dpbh kh,yd_decimalfield_yf yf,yd_textfield_ck ck,yd_entryentity.yd_textfield_hpbh wlbm,yd_entryentity.yd_decimalfield_sl sl,yd_entryentity.yd_decimalfield_dj dj,yd_entryentity.yd_decimalfield_zje je",
				fhmxgl, null);
		// 查询汇总处理为汇总的客户
		filter1 = QFilter.of("yd_combofield_hzcl=?", lx);
		String[] fhmxbzd = new String[] { "billno", "th","yf", "ck", "wlbm", "sl", "dj", "je", "pt","zz" };
		QFilter[] filter=new QFilter[] { filter1, filterSh };
		//关联客户
		DataSet dskh = glmx(fhmxbzd, yd_fhmxb, filter, "bd_customer", "kh",",yd_orgfield_dyzz zz");
		filter=new QFilter[] {filterSh};
		// 关联仓库
		fhmxbzd = new String[] { "billno", "th", "kh", "yf", "wlbm", "sl", "dj", "je", "pt", "zz" };
		DataSet dsck = glmx(fhmxbzd, dskh, filter, "bd_warehouse", "ck","");
		// 关联费用物料
		Map<String, Object> mapfywl = new HashMap<>();
		mapfywl.put("fywl", fywl);
		fhmxbzd = new String[] { "billno", "th", "kh", "ck", "yf", "sl", "dj", "je", "pt", "zz", "wlbm" };
		DataSet dsfywl = dsck.filter("wlbm in fywl", mapfywl).select(fhmxbzd);
		// 关联物料
		fhmxbzd = new String[] { "billno", "th", "kh", "ck", "yf", "sl", "dj", "je", "pt", "zz" };
		DataSet dswl = glmx(fhmxbzd, dsck, filter, "bd_material", "wlbm","").union(dsfywl);
		DataSet dataSetgroupBy = dswl.copy().select(new String[] { "th", "kh", "ck", "zz", "yf" })
				.groupBy(new String[] { "th", "kh", "ck", "zz" }).sum("yf").finish();
		String formid = "im_saloutbill";
		String djlx = "im_SalOutBill_STD_BT_S";
		String biztypenumber = "210";
		String dj = "priceandtax";
		String je = "amountandtax";
		String xyd = "1";// 需要写入发货明细的下游单类型
		if (lx.equals("4")) {
			formid = "im_otheroutbill";
			djlx = "im_OtherOutBill_STD_BT_S";
			biztypenumber = "355";
			dj = "price";
			je = "amount";
			xyd = "2";
		}

		for (Row row : dataSetgroupBy) {
			String zzbm = row.getString("zz");
			String ckbm = row.getString("ck");
			String khbm = row.getString("kh");
			Boolean th = row.getBoolean("th");
			IFormView view = ABillServiceHelper.createAddView(formid);
			view.getModel().setItemValueByNumber("billtype", djlx);
			view.getModel().setValue("org", zzbm);
			if (lx != "4") {
				view.getModel().setValue("bizorg", zzbm);
			}
			if (th) {
				if (lx.equals("4")) {
					view.getModel().setItemValueByNumber("biztype", "3551");
				} else {
					view.getModel().setItemValueByNumber("biztype", "2101");
				}
			} else {
				view.getModel().setItemValueByNumber("biztype", biztypenumber);
			}
			view.getModel().setItemValueByNumber("customer", khbm);

			BigDecimal yf = row.getBigDecimal("yf");

			DataSet dsxh = dswl.copy();
			DataSet dsbillno = dswl.copy();
			dsbillno = dsbillno.filter("kh=" + "'" + khbm + "'").filter("ck=" + "'" + ckbm + "'")
					.filter("th=" + row.getString("th")).select(new String[] { "billno" })
					.groupBy(new String[] { "billno" }).finish();
			dsxh.copy().print(false);
			dsxh = dsxh.filter("kh=" + "'" + khbm + "'").filter("ck=" + "'" + ckbm + "'")
					.filter("th=" + row.getString("th")).select(new String[] { "wlbm", "sl", "je", "dj" })
					.groupBy(new String[] { "wlbm", "dj" }).sum("sl").sum("je").finish();
			dsxh.copy().print(false);
			int i = 0;
			BigDecimal zje = BigDecimal.ZERO;// 整单总金额
			for (Row rowds : dsxh) {
				String wlbm = rowds.getString("wlbm");// sku
				BigDecimal goods_number = rowds.getBigDecimal("sl");// 商品数量
				BigDecimal goods_price = rowds.getBigDecimal("dj");// 商品单价
				BigDecimal share_payment = rowds.getBigDecimal("je");// 均摊实付金额
				boolean sffy = true;
				for (String fywlbm : fywl) {
					if (wlbm.equals(fywlbm)) {
						yf = yf.add(share_payment);
						sffy = false;
						break;
					}
				}
				if (sffy) {

					if (i > 0) {
						view.getModel().createNewEntryRow("billentry");
					}
					view.getModel().setItemValueByNumber("material", wlbm, i);
					view.getModel().setItemValueByNumber("warehouse", ckbm, i);
					view.getModel().setValue("qty", goods_number, i);
					view.getModel().setValue(dj, goods_price, i);
					view.getModel().setValue(je, share_payment, i);
					zje = zje.add(share_payment);
					i++;
				}
			}
			view.getModel().setValue("yd_amountfield_yf", yf);
			// view.getModel().setValue("yd_textfield_fhmxdh", billno);
			List<String> fhmxdh = new ArrayList<String>();// 记录需要会写销售出库单/其他出库的发货明细单号
			if (yf.compareTo(BigDecimal.ZERO) > 0) {
				// 处理分摊
				DynamicObjectCollection entrys = view.getModel().getEntryEntity("billentry");
				BigDecimal ljft = BigDecimal.ZERO;
				for (int j = 0; j < entrys.getRowCount(); j++) {
					BigDecimal hsje = entrys.get(j).getBigDecimal(je);// 每行总金额
					// BigDecimal sl=entrys.get(j).getBigDecimal("qty");//每行数量
					if (j < entrys.getRowCount() - 1) {
						BigDecimal yfft = hsje.multiply(yf).divide(zje, 2, RoundingMode.HALF_UP);// 每行分摊运费
						// BigDecimal yfft=new
						// BigDecimal(((hsje.doubleValue()/zje.doubleValue())*yf.doubleValue())).setScale(2);//每行分摊运费
						// BigDecimal hsdj=hsje.add(yfft).divide(sl);//每行分摊后含税单价
						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
						ljft = ljft.add(yfft);
						view.getModel().setValue(je, hsjeft, j);
					} else {
						BigDecimal yfft = yf.subtract(ljft);// 每行分摊运费
						// BigDecimal hsdj=hsje.add(yfft).divide(sl);//每行分摊后含税单价
						BigDecimal hsjeft = hsje.add(yfft);// 每行分摊后含税金额
						view.getModel().setValue(je, hsjeft, j);
					}
				}
			}
			int j = 0;
			for (Row rowds : dsbillno) {
				if (j > 0) {
					view.getModel().createNewEntryRow("yd_entryentity");
				}
				String billno = rowds.getString("billno");
				view.getModel().setValue("yd_textfield_fhmxdh", billno, j);
				fhmxdh.add(billno);
				j++;
			}
			if (i > 0) {
				OperationResult operationResult = ABillServiceHelper.saveOperate(view);
				filter1 = new QFilter("billno", QCP.in, fhmxdh);
				// 判断保存结果
				if (operationResult.isSuccess()) {
					Map<Object, String> map = operationResult.getBillNos();
					for (Map.Entry<Object, String> entry : map.entrySet()) {

						DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb",
								"yd_textfield_xsckdh,yd_combofield_xyd", new QFilter[] { filter1 });
						for (DynamicObject rowdy : dy) {
							rowdy.set("yd_textfield_xsckdh", entry.getValue());// 修改数据
							rowdy.set("yd_combofield_xyd", xyd);
						}
						SaveServiceHelper.save(dy);
					}
				} else {
					String errMessage = operationResult.getMessage(); // 错误摘要
					// // 演示提取保存详细错误
					// for(IOperateInfo errInfo: operationResult.getAllErrorOrValidateInfo()) {
					// String detailMessage = errInfo.getMessage();
					// }
					DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", "yd_textfield_sbyy",
							new QFilter[] { filter1 });
					for (DynamicObject rowdy : dy) {
						rowdy.set("yd_textfield_sbyy", errMessage);// 修改数据
					}
					SaveServiceHelper.save(dy);
				}
			} else {
				view.close();
			}
		}
		dswl.close();
	}

	public static void jy() {
		// 标记出关联客户为不处理的单据
		jymxjoin("bd_customer", "yd_textfield_dpbh", "yd_checkboxfield_bcl");//标记不处理的客户
		// 查询需要所有排除的物料 包括运费分摊物料
		DataSet pcwl = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl", null,
				null);
		List<String> pcwlin = new ArrayList<String>();

		for (Row row : pcwl) {
			pcwlin.add(row.getString("pcwl"));
		}
		jymx(pcwlin, "bd_customer", "yd_textfield_dpbh", "yd_checkboxfield_khbcz");// 标记不存在的客户的单据
		jymx(pcwlin, "bd_warehouse", "yd_textfield_ck", "yd_checkboxfield_ckbcz");// 标记不存在的仓库的单据
		jymx(pcwlin, "bd_material", "yd_entryentity.yd_textfield_hpbh", "yd_checkboxfield_wlbcz");// 标记不存在的物料的单据
	}
	public static void jymxjoin(String bzm, String zdm, String xgbz) {
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		QFilter filter2 = QFilter.of("yd_combofield_hzcl =?", "3");// 不处理
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
		QFilter filterSh = QFilter.of("status=?", "C");
		
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表客户
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymxjoin", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
				new QFilter[] { filter1, filter3 }, null);
		yd_fhmxbykh.print(true);
		String[] fhmxbzd = new String[] { "pt", zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		DataSet kh = null;
		DataSet joinDataset = null;
			kh = QueryServiceHelper.queryDataSet("jymxjoin", bzm,
					"number,yd_entryentity_e3.yd_textfield_e3 e3bm,yd_entryentity_wdt.yd_textfield_wdt wdtbm,yd_entryentity_jky.yd_textfield_jky jkybm",
					new QFilter[] { filter2,filterSh }, null);
			DataSet joinDatasete3 = yd_fhmxbykh.filter("pt='1'").join(kh).on(zdm, "e3bm")
					.select(fhmxbzd, new String[] { "number" }).finish();
			DataSet joinDatasetwdt = yd_fhmxbykh.filter("pt='2'").join(kh).on(zdm, "wdtbm")
					.select(fhmxbzd, new String[] { "number" }).finish();
			DataSet joinDatasetjky = yd_fhmxbykh.filter("pt='3'").join(kh).on(zdm, "jkybm")
					.select(fhmxbzd, new String[] { "number" }).finish();
			joinDataset = joinDatasete3.union(joinDatasetwdt).union(joinDatasetjky);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			String number = row.getString("number");
			if (number == null) {
				bczkh.add(row.getString(zdm));
			}
		}
		 filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz,
				new QFilter[] { filter1, filter2, filter3 });
		for (DynamicObject row : dy) {
			row.set(xgbz, 1);// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
	}
	public static void jymx(List<String> pcwlin, String bzm, String zdm, String xgbz) {
		QFilter filter1 = QFilter.of("yd_checkboxfield_bcl =?", 0);// 不处理为否
		QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.not_in, pcwlin);// 去除排除物料
		QFilter filter3 = QFilter.of("yd_textfield_xsckdh =?", "");// 销售出库单为空
		QFilter filterSh = QFilter.of("status=?", "C");
		// 查询销售出库单号为空且不处理为否且去除了排除物料的发货明细表客户
		DataSet yd_fhmxbykh = QueryServiceHelper.queryDataSet("jymx", "yd_fhmxb", "yd_combofield_pt pt," + zdm,
				new QFilter[] { filter1, filter2, filter3 }, null);
		yd_fhmxbykh.print(true);
		String[] fhmxbzd = new String[] { "pt", zdm };
		yd_fhmxbykh = yd_fhmxbykh.select(fhmxbzd).groupBy(fhmxbzd).finish();
		DataSet kh = null;
		DataSet joinDataset = null;
			kh = QueryServiceHelper.queryDataSet("jymx", bzm,
					"number,yd_entryentity_e3.yd_textfield_e3 e3bm,yd_entryentity_wdt.yd_textfield_wdt wdtbm,yd_entryentity_jky.yd_textfield_jky jkybm",
					new QFilter[] { filterSh }, null);
			DataSet joinDatasete3 = yd_fhmxbykh.filter("pt='1'").leftJoin(kh).on(zdm, "e3bm")
					.select(fhmxbzd, new String[] { "number" }).finish();
			DataSet joinDatasetwdt = yd_fhmxbykh.filter("pt='2'").leftJoin(kh).on(zdm, "wdtbm")
					.select(fhmxbzd, new String[] { "number" }).finish();
			DataSet joinDatasetjky = yd_fhmxbykh.filter("pt='3'").leftJoin(kh).on(zdm, "jkybm")
					.select(fhmxbzd, new String[] { "number" }).finish();
			joinDataset = joinDatasete3.union(joinDatasetwdt).union(joinDatasetjky);
		List<String> bczkh = new ArrayList<String>();// 记录不存在的客户
		for (Row row : joinDataset) {
			String number = row.getString("number");
			if (number == null) {
				bczkh.add(row.getString(zdm));
			}
		}
		filter2 = new QFilter(zdm, QCP.in, bczkh);
		DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz,
				new QFilter[] { filter1, filter2, filter3 });
		for (DynamicObject row : dy) {
			row.set(xgbz, 1);// 修改数据
		}
		SaveServiceHelper.save(dy);// 保存
	}
}
