package test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import json.JSON;
import json.JSONArray;
import json.JSONObject;
import kd.bos.tcbj.im.transbill.service.ApiHelper;

/**
* @auditor yanzuwei
* @date 2021年12月29日
* 
*/
public class PersonalTest {

	public static void main(String[] args) {
		// 测试调用营销云接口
		JSONObject req=new JSONObject();
//		 s.getPurNoticeData(req);
		 // s.getOrderRetNoticeData();
		Map<String,Object> requestMap = new HashMap<String,Object>();
//		requestMap.put("tenantId", "");
		requestMap.put("seq", "ZT");
		List<String> partnersList = new ArrayList<String>();
		partnersList.add("0258efff95c84432a9f47e98346c7d61");partnersList.add("000000007baaf09b017bc44340ba6837");
		requestMap.put("partners", partnersList);
		List<String> productNosList = new ArrayList<String>();
		productNosList.add("AA010120AA");productNosList.add("AA010210C");
		requestMap.put("productNos", productNosList);
		
		Map<String,Object> sendMap = new HashMap<String,Object>();
		sendMap.put("request", requestMap);
		
		String request = JSON.toJSONString(sendMap);
		System.out.println(request);
		
		String result = ApiHelper.post("https://ssc.by-health.com/openapi-basesubject/productPrice/queryPriceTcbj", request);
		JSONObject returnJson = JSONObject.parseObject(result);
		System.out.println("返回结果:"+returnJson.toString());
	}

}
