package test;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import kd.bos.algo.DataSet;
import kd.bos.algo.Row;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.dataentity.entity.DynamicObjectCollection;
import kd.bos.dataentity.metadata.dynamicobject.DynamicObjectType;
import kd.bos.entity.operate.result.OperationResult;
import kd.bos.form.IFormView;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.log.api.AppLogInfo;
import kd.bos.log.api.ILogService;
import kd.bos.orm.query.QCP;
import kd.bos.orm.query.QFilter;
import kd.bos.service.ServiceFactory;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.QueryServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;
import kd.bos.yd.tcyp.ABillServiceHelper;
import kd.bos.yd.tcyp.ApiFf;
import kd.bos.yd.tcyp.utils.OperationLogUtil;

import java.util.Map;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import json.JSONArray;
import json.JSONObject;

public class testBd extends AbstractBillPlugIn {
	@Override
	public void itemClick(ItemClickEvent evt) {
		// TODO Auto-generated method stub
		super.itemClick(evt);
		String pname = evt.getItemKey();
		switch (pname) {
		case "yd_baritemap_hd":
//			String sj1="2021-09-14";
//			ApiFf.E3Rfhhz("1",sj1);
//			ApiFf.E3Rfhhz("2",sj1);
			String xgbz="yd_entryentity.yd_checkboxfield_bczmx,yd_entryentity.yd_textfield_hpbh";
			QFilter filter2 = new QFilter("yd_entryentity.yd_textfield_hpbh", QCP.equals, "AA140103A");
			DynamicObject[] dy = BusinessDataServiceHelper.load("yd_fhmxb", xgbz ,
					new QFilter[] {  filter2 });
			for (DynamicObject row : dy) {
				DynamicObjectCollection mx=row.getDynamicObjectCollection("yd_entryentity");
				for (DynamicObject rowmx : mx)
				{
					rowmx.set("yd_checkboxfield_bczmx", 1);// 修改数据
				}
			}
			SaveServiceHelper.save(dy);// 保存
			break;
		case "yd_baritemap_e3dj":
			QFilter filter1 = new QFilter("number", QCP.equals, "Cus-000001");
			DynamicObject[] khojb = BusinessDataServiceHelper.load("bd_customer", "taxrate.number",
					new QFilter[] { filter1 });
			String a="";
			for (DynamicObject rowdy : khojb) {
				 a=rowdy.getString("taxrate.number");
			}
			break;
		case "yd_baritemap_count":
			// 查询需要所有排除的物料 包括运费分摊物料
			DataSet pcwl = QueryServiceHelper.queryDataSet("jy", "yd_pcwl", "yd_entryentity.yd_textfield_bm pcwl", null,
					null);
			List<String> pcwlin = new ArrayList<String>();

			for (Row row : pcwl) {
				pcwlin.add(row.getString("pcwl"));
			}
			//ApiFf.jymxhg(pcwlin, "yd_wldygx", "yd_entryentity.yd_textfield_hpbh", "yd_checkboxfield_wlwsh","bd_material");
			break;
		case "yd_baritemap_sj":
			int ts = 7;
			int sj = 24;
			Date dt = new Date();
			Calendar rightNow = Calendar.getInstance();
			for (int t = ts; t >0; t--) {
				rightNow.setTime(dt);
				System.out.print(t+"t\n");
				rightNow.add(Calendar.DAY_OF_MONTH, -t);
				for (int s = 1; s <= sj; s++) {
					System.out.print(s+"s\n");
					//Calendar rightNow = Calendar.getInstance();
					//rightNow.setTime(dt);
					Date dtjs = rightNow.getTime();
					rightNow.add(Calendar.HOUR, 1);
					Date dtks = rightNow.getTime();
					String ks1 = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtks);
					String js1 = (String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dtjs);
					System.out.print(ks1+"\n");
					System.out.print(js1+"\n");
				}
			}
			break;
		}
	}

	public static String PostE3(String postdata) {
		String key = "TCBJ_E3";
		String secret = "1a2b3c4d5e6f7g8h9i10j11k12l";
		String version = "3.0";
		String serviceType = "order.list.get";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		String BSDZ = "http://47.92.195.153/e3test/webopm/web/?app_act=api/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}
	
	public static String PostE3BB(String postdata) {
		String key = "BSAKhmSVtfmkDVGLsRLs";
		String secret = "BSAKdaVAbhYEjuLPwiOs";
		String version = "3.0";
		String serviceType = "ptg.order.list";
		String requestTime = (String) new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String JMZFC = "key=" + key + "&requestTime=" + requestTime + "&secret=" + secret + "&version=" + version
				+ "&serviceType=" + serviceType + "&data=" + postdata;
		String sign = getMd5(JMZFC);
		String BSDZ = "http://47.92.195.153/e3test/webopm/web/?app_act=api_baison/ec&app_mode=func";
		String url = BSDZ + "&key=" + key + "&requestTime=" + requestTime + "&format=json&version=" + version
				+ "&serviceType=" + serviceType + "&sign=" + sign;
		Map<String, String> map = new HashMap<>();
		map.put("data", postdata);
		String result = doPost(url, map);
		return result;
	}

	public static void BillSave(JSONArray orderListGets) {
		List<DynamicObject> objs = new ArrayList<DynamicObject>();
		for (int i = 0; i < orderListGets.size(); i++) {
			String order_sn = orderListGets.getJSONObject(i).get("order_sn").toString();// 订单编号
			String sd_id = orderListGets.getJSONObject(i).get("sd_id").toString();// 商店id
			String djbh = "1_0_" + sd_id + "_" + order_sn;
			QFilter qFilter = new QFilter("billno", QCP.equals, djbh);
			DynamicObject dObject = BusinessDataServiceHelper.loadSingle("yd_fhmxb", "billno",
					new QFilter[] { qFilter });
			if (dObject == null) {
				String add_time = orderListGets.getJSONObject(i).get("add_time").toString();// 下单时间
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date tiem = new Date();
				try {
					tiem = sdf.parse(add_time);
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String shipping_fee = orderListGets.getJSONObject(i).get("shipping_fee").toString();// 运费
				String fhck = orderListGets.getJSONObject(i).get("fhck").toString();// 发货仓库
				String fptt = orderListGets.getJSONObject(i).get("invoice_title").toString();// 开票
				Boolean kp = true;
				if (fptt.equals("") || fptt == null) {
					kp = false;
				}
				// 创建单据对象
				DynamicObject bill = BusinessDataServiceHelper.newDynamicObject("yd_fhmxb");
				// 设置单据属性
				bill.set("billno", djbh); // 单据编号为平台_退货_店铺_订单号
				bill.set("billstatus", "A");
				bill.set("yd_combofield_pt", "1");
				bill.set("yd_textfield_ddbh", order_sn);
				bill.set("yd_datetimefield_xdsj", tiem);
				bill.set("yd_textfield_dpbh", sd_id);
				bill.set("yd_checkboxfield_sfkp", kp);
				// bill.set("yd_checkboxfield_sfsg", "0");
				bill.set("yd_decimalfield_yf", shipping_fee);
				bill.set("yd_textfield_ck", fhck);
				// 获取单据体集合
				DynamicObjectCollection entrys = bill.getDynamicObjectCollection("yd_entryentity");
				// 获取单据体的Type
				DynamicObjectType type = entrys.getDynamicObjectType();
				// 根据Type创建单据体对象
				JSONArray orderDetailGets = orderListGets.getJSONObject(i).getJSONArray("orderDetailGets");
				for (int j = 0; j < orderDetailGets.size(); j++) {
					DynamicObject entry = new DynamicObject(type);
					// 设置单据体属性
					String sku = orderDetailGets.getJSONObject(j).get("sku").toString();// sku
					String goods_number = orderDetailGets.getJSONObject(j).get("goods_number").toString();// 商品数量
					String goods_price = orderDetailGets.getJSONObject(j).get("goods_price").toString();// 商品单价
					String share_payment = orderDetailGets.getJSONObject(j).get("share_payment").toString();// 均摊实付金额
					entry.set("yd_textfield_hpbh", sku);
					entry.set("yd_decimalfield_sl", goods_number);
					entry.set("yd_decimalfield_dj", goods_price);
					entry.set("yd_decimalfield_zje", share_payment);
					// 添加到单据体集合
					entrys.add(entry);

				}
				objs.add(bill);
			}
		}
		if (objs.size() > 0) {
			DynamicObject[] objsSAVE = new DynamicObject[objs.size()];
			for (int i = 0; i < objs.size(); i++) {
				objsSAVE[i] = objs.get(i);
			}
			SaveServiceHelper.save(objsSAVE);
			// 1、获取日志微服务接口
			ILogService logService1 = (ILogService) ServiceFactory.getService(ILogService.class);
			// 2、构建日志信息，参考示例如下
			AppLogInfo logInfo1 = OperationLogUtil.buildLogInfo("保存", "保存发货明细", "sm", "yd_fhmxb");
			// 3、记录操作日志
			logService1.addLog(logInfo1);
		}
	}

	public static String getMd5(String plainText) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(plainText.getBytes());
			byte b[] = md.digest();

			int i;

			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			// 32位加密
			return buf.toString();
			// 16位的加密
			// return buf.toString().substring(8, 24);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			return null;
		}

	}

	public static String doPost(String url, Map<String, String> param) {

		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();

		String resultString = "";
		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			if (param != null) {
				for (String key : param.keySet()) {
					builder.addParameter(key, param.get(key));
				}
			}
			URI uri = builder.build();

			// 创建http post请求
			HttpPost httpGet = new HttpPost(uri);

			// 执行请求
			response = httpclient.execute(httpGet);
			// 判断返回状态是否为200
			if (response.getStatusLine().getStatusCode() == 200) {
				resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}

}
