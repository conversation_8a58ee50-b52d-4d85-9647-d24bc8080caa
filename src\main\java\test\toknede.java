package test;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import json.JSON;
import json.JSONObject;
import kd.bos.api.ApiRequestContext;
import kd.bos.bill.AbstractBillPlugIn;
import kd.bos.dc.api.model.Account;
import kd.bos.dc.utils.AccountUtils;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.lang.Lang;
import kd.bos.login.LoginClientEnum;
import kd.bos.login.user.LoginUserService;
import kd.bos.login.utils.SessionUtils;
import kd.bos.login.utils.ThirdAPIAPPUtils;
import kd.bos.util.NetAddressUtils;

public class toknede extends AbstractBillPlugIn{
	public void itemClick(ItemClickEvent evt) {
		super.itemClick(evt);
		if("yd_baritemap_sj".equals(evt.getItemKey()) )
		{
			JSONObject requestData = new JSONObject();
//			requestData.put("appId","demo");//第三方应用id
//			requestData.put("appSecuret","Qwertyasdfgh123456.");//第三方应用密码
//			requestData.put("tenantid","cosmic-simple");//租户id通过登录管理中心看到
//			requestData.put("accountId","679008189806542848");//数据中心id通过登录管理中心看到
			requestData.put("appId", "CASHINV");
			  requestData.put("appSecuret", "Qwertyasdfgh123456.");
			  requestData.put("tenantid", "ierp");
			  requestData.put("accountId", "1263897402012599296");
			requestData.put("language","zh_CN");
			//String json = post(requestData,"http://localhost:8080/ierp/api/getAppToken.do");
			String json = post(requestData,"http://***************:8088/ierp/api/getAppToken.do");
			System.out.println(json);
			JSONObject resultObj = (JSONObject) JSON.parse(json);
			JSONObject data = resultObj.getJSONObject("data");
			String apptoken = data.getString("app_token");
//			String tenantid = "cosmic-simple";
//			String user = "***********";
//			String accountId = "679008189806542848";
			String tenantid = "ierp";
			String user = "***********";
			String accountId = "1263897402012599296";
			String usertype = "Mobile";
			JSONObject loginParam = new JSONObject();
			loginParam.put("apptoken", apptoken);
			loginParam.put("tenantid", tenantid);
			loginParam.put("user", user);
			loginParam.put("accountId", accountId);
			loginParam.put("usertype", usertype);
			//String result = post(loginParam,"http://localhost:8080/ierp/api/login.do");
			String result = post(loginParam,"http://***************:8088/ierp//api/login.do");
			JSONObject result1 = (JSONObject) JSON.parse(result);
			JSONObject data1 = result1.getJSONObject("data");
			String access_token = data1.getString("access_token");
			System.out.println(result);
			//this.getView().showMessage(result);
			
			String url="http://***************:8088/ierp/accessTokenLogin.do?access_token="+access_token+
					"&redirect=http://***************:8088/ierp/index.html?formId=bos_list&billFormId=im_saloutbill&type=list&billno=XSCK-211015-000005";
//			String url="http://localhost:8080/ierp/accessTokenLogin.do?access_token="+access_token+
//					"&redirect=http://localhost:8080/ierp/index.html?formId=bos_list&billFormId=im_saloutbill&type=list&billno=XSCK-211017-000012";
			
			this.getView().showMessage(url);
		}
}

public static String post(JSONObject json, String url){
	String result = "";
	HttpPost post = new HttpPost(url);
	try{
		CloseableHttpClient httpClient = HttpClients.createDefault();
		post.setHeader("Content-Type","application/json;charset=utf-8");
		post.addHeader("Authorization", "Basic YWRtaW46");
		StringEntity postingString = new StringEntity(json.toString(),"utf-8");
		post.setEntity(postingString);
		HttpResponse response = httpClient.execute(post);
		InputStream in = response.getEntity().getContent();
		BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
		StringBuilder strber= new StringBuilder();
		String line = null;
		while((line = br.readLine())!=null){
		strber.append(line+'\n');
		}
		br.close();
		in.close();
		result = strber.toString();
		if(response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
		result = "服务器异常";
		}
	}
	catch (Exception e){
	System.out.println("请求异常");
	throw new RuntimeException(e);
	} finally{
	post.abort();
	}
	return result;
}
}
