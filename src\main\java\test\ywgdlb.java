package test;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.EventObject;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import kd.bos.dataentity.entity.DynamicObject;
import kd.bos.entity.datamodel.ListSelectedRow;
import kd.bos.entity.datamodel.ListSelectedRowCollection;
import kd.bos.entity.datamodel.events.BizDataEventArgs;
import kd.bos.filter.FilterColumn;
import kd.bos.form.ConfirmCallBackListener;
import kd.bos.form.FormShowParameter;
import kd.bos.form.MessageBoxOptions;
import kd.bos.form.control.Toolbar;
import kd.bos.form.control.events.ItemClickEvent;
import kd.bos.form.events.ClosedCallBackEvent;
import kd.bos.form.events.FilterContainerInitArgs;
import kd.bos.list.BillList;
import kd.bos.list.IListView;
import kd.bos.list.plugin.AbstractListPlugin;
import kd.bos.servicehelper.BusinessDataServiceHelper;
import kd.bos.servicehelper.operation.SaveServiceHelper;

public class ywgdlb extends AbstractListPlugin {
	String a="";
	@Override
	public void registerListener(EventObject e) {
		// TODO 侦听控件的插件事件
		this.addClickListeners("tbmain");

	}

	@Override
	public void beforeBindData(EventObject e) {
		super.beforeBindData(e);
		FormShowParameter showParameter = this.getView().getFormShowParameter();
		a=showParameter.getCustomParam("abc");
		// TODO 在此添加业务逻辑
	}
	@Override
	public void itemClick(ItemClickEvent evt) {
		// TODO Auto-generated method stub
		super.itemClick(evt);
		String pname= evt.getItemKey();
		switch (pname) {
		case "yd_baritemap_jg":
			String formId = ((IListView) this.getView()).getBillFormId();
			BillList billList = this.getControl("billlistap");
			// 获取选中行记录,注意:得到的结果是记录的主键ID的集合
			ListSelectedRowCollection listSelectedRowCol = billList.getSelectedRows();
			if (listSelectedRowCol != null && listSelectedRowCol.size() > 0) {
				List<DynamicObject> listSelectedRowCol2 = new ArrayList<DynamicObject>();
				DynamicObject tempRowData = null;
				for (ListSelectedRow tempRowDataId : listSelectedRowCol) {
					// 查询数据库得到当前行记录的完整结果
					tempRowData = BusinessDataServiceHelper.loadSingle(tempRowDataId.getPrimaryKeyValue(), formId);
					DynamicObject gdzt= tempRowData.getDynamicObject("yd_basedatafield_gdzt");
					if(gdzt!=null) {
						String gdztbm=gdzt.getString("number");
						if(gdztbm.equals("D"))
						{
							//作废操作前让用户选择是否继续作废操作，在用户点击确认框上的按钮后，系统会调用confirmCallBack方法
				            ConfirmCallBackListener confirmCallBackListener = new ConfirmCallBackListener("yd_baritemap_jg", this);
				            //设置页面确认框，参数为：标题，选项框类型，回调监听
				            this.getView().showConfirm("确认解挂单据吗？", MessageBoxOptions.YesNo, confirmCallBackListener);
						}
						else
						{
							this.getView().showTipNotification("只有挂起中状态的单据才可以解挂");
						}
					}
				}
			}
			break;
		case "yd_baritemap2":
			this.getView().showMessage(a);
			break;
		}
	}
	@Override
    public void closedCallBack(ClosedCallBackEvent closedCallBackEvent) {
        super.closedCallBack(closedCallBackEvent);
        //判断标识是否匹配，并验证返回值不为空，不验证返回值可能会报空指针
        String BZ=closedCallBackEvent.getActionId();
        if (BZ.equals("customShow") && null != closedCallBackEvent.getReturnData()) {
            //这里返回对象为Object，可强转成相应的其他类型，
            // 单条数据可用String类型传输，返回多条数据可放入map中，也可使用json等方式传输
            HashMap<String, String> returnData = (HashMap<String, String>) closedCallBackEvent.getReturnData();
            SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			Date date = new Date();
			Date dd=new Date();
			SimpleDateFormat sim=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String time = sim.format(dd); 
			try {
				date = sdf.parse(returnData.get("yd_datetimefield")); 
			} catch (Exception e) {
				e.printStackTrace();
			}
            String formatStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
            Date dqsj=new Date();
            String dqsjc=sim.format(dqsj); 
            this.getModel().setValue("yd_datetimefield_gqsj",dqsjc);
            this.getModel().setValue("yd_datetimefield_yjgqjs",formatStr);
            //this.getModel().setValue("yd_textfield_xqrwdh", returnData.get("yd_combofield_gqyy"));
            this.getModel().setItemValueByNumber("yd_basedatafield_gdzt", "D");
            DynamicObject obj=this.getModel().getDataEntity();
            SaveServiceHelper.save(new DynamicObject[] { obj});
            //this.getModel().setValue(KEY_REMARK, returnData.get(""));
        }
    }
}
